{"name": "@kit/prettier-config", "private": true, "version": "0.1.0", "main": "index.mjs", "scripts": {"clean": "rm -rf .turbo node_modules", "format": "prettier --check \"**/*.{mjs,json}\"", "typecheck": "tsc --noEmit"}, "dependencies": {"@trivago/prettier-plugin-sort-imports": "5.2.2", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11"}, "devDependencies": {"@kit/tsconfig": "workspace:*", "typescript": "^5.8.3"}, "prettier": "./index.mjs"}