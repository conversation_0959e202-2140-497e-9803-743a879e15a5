#!/bin/bash

# Định nghĩa các biến
REMOTE_HOST="*************"          # Domain hoặc IP của server
REMOTE_USER="mcp"                         # User SSH (theo CloudPanel)
PROJECT_DIR="htdocs/mcp.s2-dev.mkit.vn/ecom-zns"  # Đường dẫn trên server
SUPABASE_DIR="htdocs/mcp.s2-dev.mkit.vn/supabase"  # Đường dẫn Supabase trên server
SERVICE_ROLE_KEY="eyJhbGc..36XVt4Q"  # SERVICE_ROLE_KEY của Supabase

# Kiểm tra biến
if [ -z "$REMOTE_HOST" ] || [ -z "$REMOTE_USER" ] || [ -z "$SERVICE_ROLE_KEY" ]; then
  echo "Error: Cập nhật REMOTE_HOST, REMOTE_USER, và SERVICE_ROLE_KEY trong script."
  exit 1
fi

# Thực hiện tất cả các bước trong một lệnh SSH duy nhất
echo "Đang triển khai..."
ssh -T "$REMOTE_USER@$REMOTE_HOST" << EOF
  set -e  # Exit on any error

  # Bước 1: Pull code mới
  echo "Đang pull code..."
  cd "$PROJECT_DIR"
  git pull

  # Bước 2: Cài đặt phụ thuộc
  echo "Đang cài đặt phụ thuộc..."
  pnpm install

  # Bước 3: Build Next.js
  echo "Đang build Next.js..."
  pnpm build

  # Bước 4: Restart Next.js (dùng Supervisor)
  echo "Đang restart Next.js..."
  supervisorctl restart mcp-nextjs

  # Bước 5: Restart Supabase và chạy migrations
  echo "Đang restart Supabase và chạy migrations..."
  cd ~
  cd "$SUPABASE_DIR"
  docker compose restart
  cd ~
  cd "$PROJECT_DIR/apps/web/supabase"
  supabase db push --url http://localhost:8052 --key "$SERVICE_ROLE_KEY"

  # Bước 6: Kiểm tra trạng thái
  echo "Kiểm tra trạng thái..."
  supervisorctl status mcp-nextjs
  docker ps

  echo "Triển khai thành công!"
EOF

# Kiểm tra mã lỗi của SSH
if [ $? -eq 0 ]; then
  echo "Triển khai hoàn tất thành công!"
else
  echo "Triển khai thất bại! Kiểm tra log phía trên để xem chi tiết."
  exit 1
fi
