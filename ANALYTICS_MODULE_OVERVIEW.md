# Module Analytics - Tổng quan chi tiết

## 🎯 Mục đích và vai trò

Module Analytics trong dự án này được thiết kế để:
- **Thu thập dữ liệu hành vi người dùng** từ nhiều nguồn khác nhau
- **Phân tích hiệu suất** của các tính năng và tích hợp
- **Cung cấp insights** cho việc ra quyết định kinh doanh
- **Theo dõi ROI** của các chiến dịch marketing

## 🏗️ Kiến trúc tổng thể

### 1. **Cấu trúc Package** (`packages/analytics/`)
```
packages/analytics/
├── src/
│   ├── index.ts                    # Entry point chính
│   ├── analytics-manager.ts        # Quản lý các provider
│   ├── types.ts                    # Type definitions
│   ├── null-analytics-service.ts   # Fallback service
│   └── providers/
│       └── zalo-miniapp-provider.ts # Provider cho Zalo Mini App
└── README.md
```

### 2. **<PERSON><PERSON><PERSON> thành phần chính**

#### **Analytics Manager** 
- **Vai trò**: Điều phối trung tâm cho tất cả analytics providers
- **Chức năng**: 
  - Quản lý lifecycle của providers
  - Phân phối events đến tất cả active providers
  - Fallback mechanism khi không có provider nào active

#### **Analytics Service Interface**
```typescript
interface AnalyticsService {
  initialize(): Promise<unknown>;
  trackPageView(path: string): Promise<unknown>;
  trackEvent(eventName: string, eventProperties?: Record<string, any>): Promise<unknown>;
  identify(userId: string, traits?: Record<string, string>): Promise<unknown>;
}
```

#### **Provider System**
- **Pluggable architecture**: Dễ dàng thêm/xóa providers
- **Multi-provider support**: Có thể gửi data đến nhiều service cùng lúc
- **Provider hiện tại**:
  - `NullAnalyticsService`: Fallback provider (không làm gì)
  - `ZaloMiniAppAnalyticsService`: Provider cho Zalo Mini App

## 📊 Luồng dữ liệu (Data Flow)

### 1. **Event Collection**
```
User Action → App Events → Analytics Provider → Database/External Service
```

### 2. **Các nguồn dữ liệu**
- **Direct API calls**: `/api/analytics`, `/api/analytics/zalo`
- **App Events System**: Tự động track các sự kiện hệ thống
- **Manual tracking**: Gọi trực tiếp analytics service

### 3. **Event Types được hỗ trợ**
- `pageview`: Lượt xem trang
- `product_view`: Xem sản phẩm
- `add_to_cart`: Thêm vào giỏ hàng
- `purchase`: Mua hàng
- `identify`: Nhận diện người dùng
- `user.signedIn`: Đăng nhập
- `user.signedUp`: Đăng ký
- `checkout.started`: Bắt đầu thanh toán

## 🗄️ Database Schema

### **Bảng chính: `analytics_events`**
```sql
CREATE TABLE public.analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL,           -- Liên kết với account
  theme_id UUID,                      -- ID của theme/store
  event_type TEXT NOT NULL,           -- Loại sự kiện
  event_data JSONB NOT NULL DEFAULT '{}', -- Dữ liệu chi tiết
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID,                       -- ID người dùng (nếu có)
  visitor_id TEXT,                    -- ID visitor (anonymous)
  device_type TEXT,                   -- mobile/desktop/tablet
  source TEXT DEFAULT 'zalo_miniapp'  -- Nguồn dữ liệu
);
```

### **Indexes để tối ưu performance**
- `account_id`: Truy vấn theo account
- `theme_id`: Truy vấn theo store
- `event_type`: Lọc theo loại sự kiện
- `created_at`: Truy vấn theo thời gian
- `source`: Lọc theo nguồn

### **Views tổng hợp**
- `daily_pageviews`: Lượt xem theo ngày
- `daily_orders`: Đơn hàng theo ngày
- `product_analytics`: Phân tích sản phẩm

## 🔧 Cách sử dụng

### 1. **Khởi tạo Analytics Manager**
```typescript
import { analytics } from '@kit/analytics';

// Analytics manager đã được khởi tạo sẵn với providers
```

### 2. **Tracking Events**
```typescript
// Track page view
await analytics.trackPageView('/products');

// Track custom event
await analytics.trackEvent('add_to_cart', {
  productId: 'prod-123',
  quantity: 2,
  price: 100000
});

// Identify user
await analytics.identify('user-456', {
  name: 'John Doe',
  email: '<EMAIL>'
});
```

### 3. **Sử dụng với App Events**
```typescript
// Trong analytics-provider.tsx
const analyticsMapping = {
  'user.signedIn': (event) => {
    analytics.identify(event.payload.userId);
  },
  'checkout.started': (event) => {
    analytics.trackEvent('checkout_started', event.payload);
  }
};
```

## 🌐 API Endpoints

### 1. **Main Analytics API** (`/api/analytics`)
- **Authentication**: Supabase JWT token
- **Use case**: Ứng dụng đã tích hợp Supabase Auth
- **Auto-extract**: `account_id` từ token

### 2. **Zalo Analytics API** (`/api/analytics/zalo`)
- **Authentication**: Zalo token
- **Use case**: Zalo Mini App hoặc external apps
- **Manual**: Phải truyền `accountId` trong request

## 📈 Analytics Dashboard

### **ZNS Analytics** (`/home/<USER>/integrations/zns/analytics`)
- **Metrics**: Success rate, error analysis, template performance
- **Visualizations**: Charts, trends, distributions
- **Filters**: Date range, template, event type

### **Integration Analytics** (`/home/<USER>/integrations/analytics`)
- **Sync history**: Theo dõi quá trình đồng bộ
- **Resource analytics**: Phân tích theo resource type
- **Performance metrics**: Success/failure rates

## 🔒 Security & Privacy

### **Row Level Security (RLS)**
```sql
-- Chỉ cho phép truy cập data của account mình
CREATE POLICY "analytics_events_read" ON public.analytics_events FOR SELECT
  TO authenticated USING (
    account_id = (select auth.uid()) OR
    public.has_role_on_account(account_id)
  );
```

### **Data Protection**
- **Account isolation**: Mỗi account chỉ thấy data của mình
- **User consent**: Tuân thủ GDPR/privacy laws
- **Data retention**: Có thể config retention policies

## 🚀 Extensibility

### **Thêm Provider mới**
```typescript
// 1. Implement AnalyticsService interface
class GoogleAnalyticsService implements AnalyticsService {
  // ... implementation
}

// 2. Register provider
analytics.addProvider('googleAnalytics', new GoogleAnalyticsService(config));
```

### **Thêm Event Type mới**
```typescript
// 1. Extend event types
interface CustomEvents {
  'product.favorited': { productId: string };
  'cart.abandoned': { cartId: string };
}

// 2. Use in app events
emitAppEvent({
  type: 'product.favorited',
  payload: { productId: 'prod-123' }
});
```

## 📊 Use Cases thực tế

### **E-commerce Analytics**
- Track product views, cart additions, purchases
- Analyze conversion funnels
- Monitor abandoned carts

### **Marketing Analytics**
- UTM tracking cho campaigns
- Attribution modeling
- ROI measurement

### **User Behavior Analytics**
- Page flow analysis
- Feature usage tracking
- User journey mapping

### **Performance Analytics**
- API response times
- Error rates
- System health metrics
