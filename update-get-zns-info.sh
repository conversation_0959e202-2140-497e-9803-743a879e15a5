#!/bin/bash

# Find all occurrences of the pattern and replace them
sed -i '' 's/  \/\/ Lấy thông tin OA configuration\n  const { data: oaConfig, error } = await supabase\n    .from('\''oa_configurations'\'')\n    .select('\''*'\'')\n    .eq('\''id'\'', oaConfigId)\n    .single();\n\n  if (error || !oaConfig) {\n    throw new Error(`Failed to fetch OA config: ${error?.message}`);\n  }\n\n  \/\/ Kiểm tra và refresh token nếu cần\n  let accessToken = oaConfig.access_token;\n  if (new Date(oaConfig.token_expires_at) < new Date()) {\n    accessToken = await refreshZnsToken(supabase, oaConfig);\n  }/  \/\/ Lấy token hợp lệ và thông tin OA configuration\n  const { accessToken, oaConfig } = await getValidZnsToken(supabase, oaConfigId);/g' packages/zns/src/lib/get-zns-info.ts

echo "Updated get-zns-info.ts"
