name: Workflow
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
jobs:
  typescript:
    name: ʦ TypeScript
    timeout-minutes: 10
    runs-on: ubuntu-latest
    env:
      SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      SUPABASE_DB_WEBHOOK_SECRET: ${{ secrets.SUPABASE_DB_WEBHOOK_SECRET }}
      STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY }}
      STRIPE_WEBHOOK_SECRET: ${{ secrets.STRIPE_WEBHOOK_SECRET }}
      DO_NOT_TRACK: 1
      TURBO_SCM_BASE: ${{ github.event_name == 'pull_request' && github.event.pull_request.base.sha || github.event.before }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - uses: pnpm/action-setup@v4

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Typecheck
        run: pnpm run typecheck

      - name: Lint
        run: pnpm run lint

  test:
    name: ⚫️ Test
    timeout-minutes: 15
    runs-on: ubuntu-latest
    if: ${{ vars.ENABLE_E2E_JOB == 'true' }}
    env:
      SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
      SUPABASE_DB_WEBHOOK_SECRET: ${{ secrets.SUPABASE_DB_WEBHOOK_SECRET }}
      STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY }}
      STRIPE_WEBHOOK_SECRET: ${{ secrets.STRIPE_WEBHOOK_SECRET }}
      ENABLE_BILLING_TESTS: ${{ vars.ENABLE_BILLING_TESTS }}
      DO_NOT_TRACK: 1
      TURBO_SCM_BASE: ${{ github.event_name == 'pull_request' && github.event.pull_request.base.sha || github.event.before }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - uses: pnpm/action-setup@v4

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Store Playwright's Version
        run: |
          PLAYWRIGHT_VERSION=$(pnpm ls @playwright/test --filter web-e2e | grep @playwright | sed 's/.*@//')
          echo "Playwright's Version: $PLAYWRIGHT_VERSION"
          echo "PLAYWRIGHT_VERSION=$PLAYWRIGHT_VERSION" >> $GITHUB_ENV

      - name: Cache Playwright Browsers for Playwright's Version
        id: cache-playwright-browsers
        uses: actions/cache@v4
        with:
          path: ~/.cache/ms-playwright
          key: playwright-browsers-${{ env.PLAYWRIGHT_VERSION }}

      - name: Install Playwright Browsers
        if: steps.cache-playwright-browsers.outputs.cache-hit != 'true'
        run: pnpm exec ./apps/e2e/node_modules/.bin/playwright install --with-deps

      - name: Supabase CLI
        uses: supabase/setup-cli@v1

      - name: Supabase Server
        run: pnpm run supabase:web:start -- -x studio,migra,deno-relay,pgadmin-schema-diff,imgproxy,logflare

      - name: Stripe CLI
        run: |
          docker run --add-host=host.docker.internal:host-gateway --rm -it --name=stripe -d stripe/stripe-cli:latest listen --forward-to http://host.docker.internal:3000/api/billing/webhook --skip-verify --api-key "$STRIPE_SECRET_KEY" --log-level debug &

      - name: Run Next.js Production Build (test env)
        run: pnpm --filter web build:test

      - name: Run Next.js Server
        run: pnpm --filter web start:test &

      - name: Run Playwright tests
        run: |
          echo "Running Playwright tests. Billing tests enabled: $ENABLE_BILLING_TESTS"
          pnpm run test

      - name: Run Supabase tests
        run: pnpm --filter web supabase:test

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: apps/e2e/playwright-report/
          retention-days: 7