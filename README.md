# Makerkit - Supabase SaaS Starter Kit - Turbo Edition

This is a Starter Kit for building SaaS applications using Supabase, Next.js, and Tailwind CSS.

A demo version of this project can be found at [makerkit/next-supabase-saas-kit-turbo-demo](https://github.com/makerkit/next-supabase-saas-kit-turbo-demo). This version contains a tasks functionality that is not present in the original version, multiple languages, and other various modifications.

[Please follow the documentation to get started](https://makerkit.dev/docs/next-supabase-turbo/introduction).

**Please remember to update the repository daily**.


# Test
pnpm --filter web-e2e install

cd apps/e2e
pnpm exec playwright install


#
```angular2html
pnpm --filter web-e2e test:debug tests/branch/branch.spec.ts
```


#production
Dựa vào cấu hình <PERSON>, có 3 domain được setup:

1. `mcp.s2-dev.mkit.vn`:
- <PERSON><PERSON><PERSON> l<PERSON> domain chính cho Next.js application
- Traffic sẽ được proxy đến `http://127.0.0.1:3000` (Next.js server)
- <PERSON><PERSON><PERSON> nê<PERSON> là `SITE_URL` trong cấu hình Supabase

2. `mcp-supabase.s2-dev.mkit.vn`:
- Domain cho Supabase Studio (dashboard)
- Khi truy cập `/studio/` sẽ proxy đến `http://127.0.0.1:3052` (Supabase Studio)
- Phần root `/` sẽ proxy đến `http://127.0.0.1:8052` (Kong API gateway)

3. `mcp-supabase-api.s2-dev.mkit.vn`:
- Domain cho Supabase API
- Traffic sẽ được proxy đến `http://127.0.0.1:8052` (Kong API gateway)
- Đây nên là `API_EXTERNAL_URL` trong cấu hình Supabase

Cấu hình tương ứng trong `docker/.env` nên là:
```bash
SITE_URL=https://mcp.s2-dev.mkit.vn
API_EXTERNAL_URL=https://mcp-supabase-api.s2-dev.mkit.vn
SUPABASE_PUBLIC_URL=https://mcp-supabase.s2-dev.mkit.vn
```

Lưu ý:
- Tất cả các domain đều được cấu hình để redirect từ HTTP (80) sang HTTPS (443)
- SSL certificates được yêu cầu cho cả 3 domain
- Kong API gateway chạy trên port 8052 (KONG_HTTP_PORT)
- Supabase Studio chạy trên port 3052 (STUDIO_PORT)
- Next.js chạy trên port 3000
```angular2html
pnpm run clean:workspaces
pnpm run clean
pnpm i

```
