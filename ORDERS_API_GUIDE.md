# Hướng dẫn sử dụng API Orders

Tài liệu này mô tả cách sử dụng API Orders để tạo và quản lý đơn hàng trong Zalo Mini App.

## Tổng quan

API Orders cho phép bạn tạo đơn hàng mới, l<PERSON>y danh sách đơn hàng, xem chi tiết đơn hàng và cập nhật trạng thái đơn hàng. API này tích hợp với các tính năng flash sale và voucher để đảm bảo tính đúng đắn của giá trị đơn hàng.

## Endpoints

### Tạo đơn hàng mới

```
POST /api/orders
```

### Lấy danh sách đơn hàng

```
GET /api/orders
```

### Lấy chi tiết đơn hàng

```
GET /api/orders/:id
```

### Cập nhật trạng thái đơn hàng

```
PATCH /api/orders/:id/status
```

## X<PERSON>c thực

API yêu cầu xác thực bằng JWT token. Token này cần được gửi trong header `Authorization`.

```
Authorization: Bearer <your_jwt_token>
```

## Tham số

### Tạo đơn hàng mới

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| items | array | Có | Danh sách các sản phẩm trong đơn hàng |
| subtotal | number | Có | Tổng giá trị của các sản phẩm |
| discount | number | Có | Tổng giảm giá (không bao gồm voucher) |
| total_amount | number | Có | Tổng giá trị đơn hàng sau khi giảm giá |
| payment_method | string | Có | Phương thức thanh toán (`cash`, `bank_transfer`, `momo`, `zalopay`) |
| status | string | Có | Trạng thái đơn hàng (`pending`, `confirmed`, `processing`, `shipped`, `delivered`, `cancelled`) |
| branch_id | string | Không | ID của chi nhánh (nếu có) |
| voucher_code | string | Không | Mã voucher (nếu có) |
| voucher_id | string | Không | ID của voucher (nếu có) |
| voucher_discount | number | Không | Giá trị giảm giá từ voucher (nếu có) |
| customer_name | string | Không | Tên khách hàng |
| customer_phone | string | Không | Số điện thoại khách hàng |
| customer_address | string | Không | Địa chỉ giao hàng |
| customer_note | string | Không | Ghi chú từ khách hàng |

#### Cấu trúc của mỗi item trong mảng items

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| product_id | string | Có | ID của sản phẩm |
| quantity | number | Có | Số lượng sản phẩm |
| price | number | Có | Giá của sản phẩm (sau khi áp dụng flash sale) |
| original_price | number | Có | Giá gốc của sản phẩm |
| flash_sale_id | string | Không | ID của flash sale (nếu có) |
| discount_percentage | number | Không | Phần trăm giảm giá từ flash sale |
| attribute_id | string | Không | ID của thuộc tính sản phẩm (nếu có) |

### Lấy danh sách đơn hàng

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| page | number | Không | Số trang (mặc định: 1) |
| limit | number | Không | Số lượng kết quả trên mỗi trang (mặc định: 10) |
| status | string | Không | Lọc theo trạng thái đơn hàng |
| start_date | string | Không | Lọc từ ngày (định dạng: YYYY-MM-DD) |
| end_date | string | Không | Lọc đến ngày (định dạng: YYYY-MM-DD) |
| search | string | Không | Tìm kiếm theo mã đơn hàng hoặc tên khách hàng |

### Cập nhật trạng thái đơn hàng

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| status | string | Có | Trạng thái mới của đơn hàng |

## Phản hồi

### Thành công - Tạo đơn hàng mới

```json
{
  "success": true,
  "message": "Order created successfully",
  "order_id": "order-123"
}
```

### Thành công - Lấy danh sách đơn hàng

```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "id": "order-123",
        "order_number": "ORD-********-001",
        "subtotal": 300000,
        "discount": 60000,
        "voucher_discount": 20000,
        "total_amount": 220000,
        "status": "pending",
        "payment_method": "cash",
        "customer_name": "Nguyễn Văn A",
        "created_at": "2023-06-01T10:30:00Z",
        "updated_at": "2023-06-01T10:30:00Z"
      },
      {
        "id": "order-456",
        "order_number": "ORD-********-002",
        "subtotal": 500000,
        "discount": 0,
        "voucher_discount": 0,
        "total_amount": 500000,
        "status": "delivered",
        "payment_method": "bank_transfer",
        "customer_name": "Trần Thị B",
        "created_at": "2023-05-31T14:45:00Z",
        "updated_at": "2023-06-01T09:15:00Z"
      }
    ],
    "pagination": {
      "total": 25,
      "page": 1,
      "limit": 10,
      "total_pages": 3
    }
  }
}
```

### Thành công - Lấy chi tiết đơn hàng

```json
{
  "success": true,
  "data": {
    "order": {
      "id": "order-123",
      "order_number": "ORD-********-001",
      "subtotal": 300000,
      "discount": 60000,
      "voucher_discount": 20000,
      "total_amount": 220000,
      "status": "pending",
      "payment_method": "cash",
      "customer_name": "Nguyễn Văn A",
      "customer_phone": "**********",
      "customer_address": "123 Đường ABC, Quận XYZ, TP.HCM",
      "customer_note": "Giao hàng ngoài giờ hành chính",
      "branch_id": "branch-789",
      "branch": {
        "name": "Chi nhánh Quận 1",
        "address": "456 Đường DEF, Quận 1, TP.HCM"
      },
      "voucher_code": "SUMMER10",
      "voucher_id": "voucher-012",
      "created_at": "2023-06-01T10:30:00Z",
      "updated_at": "2023-06-01T10:30:00Z",
      "items": [
        {
          "id": "order-item-123",
          "product_id": "product-789",
          "product_name": "Áo thun mùa hè",
          "quantity": 2,
          "price": 120000,
          "original_price": 150000,
          "flash_sale_id": "flash-sale-345",
          "discount_percentage": 20,
          "total_price": 240000,
          "attribute_id": null,
          "product_image": "/images/products/product-789.jpg"
        },
        {
          "id": "order-item-456",
          "product_id": "product-012",
          "product_name": "Quần short",
          "quantity": 1,
          "price": 60000,
          "original_price": 60000,
          "flash_sale_id": null,
          "discount_percentage": 0,
          "total_price": 60000,
          "attribute_id": "attr-345",
          "attribute_name": "Size L, Màu xanh",
          "product_image": "/images/products/product-012.jpg"
        }
      ]
    }
  }
}
```

### Thành công - Cập nhật trạng thái đơn hàng

```json
{
  "success": true,
  "message": "Order status updated successfully",
  "order_id": "order-123",
  "status": "confirmed"
}
```

### Lỗi

```json
{
  "success": false,
  "error": "Mô tả lỗi",
  "details": "Chi tiết lỗi (nếu có)"
}
```

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| 400 | Yêu cầu không hợp lệ hoặc thiếu tham số bắt buộc |
| 401 | Không được phép truy cập |
| 404 | Không tìm thấy đơn hàng |
| 422 | Dữ liệu không hợp lệ (ví dụ: subtotal không bằng tổng giá trị các sản phẩm) |
| 500 | Lỗi server |

## Ví dụ

### Tạo đơn hàng mới

```bash
curl -X POST https://your-domain.com/api/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "items": [
      {
        "product_id": "product-789",
        "quantity": 2,
        "price": 120000,
        "original_price": 150000,
        "flash_sale_id": "flash-sale-345",
        "discount_percentage": 20
      },
      {
        "product_id": "product-012",
        "quantity": 1,
        "price": 60000,
        "original_price": 60000,
        "attribute_id": "attr-345"
      }
    ],
    "subtotal": 300000,
    "discount": 60000,
    "total_amount": 240000,
    "payment_method": "cash",
    "status": "pending",
    "customer_name": "Nguyễn Văn A",
    "customer_phone": "**********",
    "customer_address": "123 Đường ABC, Quận XYZ, TP.HCM"
  }'
```

### Lấy danh sách đơn hàng

```bash
curl -X GET "https://your-domain.com/api/orders?page=1&limit=10&status=pending" \
  -H "Authorization: Bearer your_jwt_token"
```

### Lấy chi tiết đơn hàng

```bash
curl -X GET "https://your-domain.com/api/orders/order-123" \
  -H "Authorization: Bearer your_jwt_token"
```

### Cập nhật trạng thái đơn hàng

```bash
curl -X PATCH "https://your-domain.com/api/orders/order-123/status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "status": "confirmed"
  }'
```

## Lưu ý

1. Khi tạo đơn hàng mới, API sẽ kiểm tra tính nhất quán của dữ liệu:
   - Subtotal phải bằng tổng giá trị của các sản phẩm (quantity * price)
   - Total_amount phải bằng subtotal - discount - voucher_discount
2. Nếu cung cấp voucher_code, API sẽ kiểm tra tính hợp lệ của voucher và áp dụng giảm giá nếu hợp lệ.
3. Các trạng thái đơn hàng hợp lệ: `pending`, `confirmed`, `processing`, `shipped`, `delivered`, `cancelled`.
4. Các phương thức thanh toán hợp lệ: `cash`, `bank_transfer`, `momo`, `zalopay`.

## Tích hợp với Zalo Mini App

Để tích hợp API Orders vào Zalo Mini App, bạn cần:

1. Lấy JWT token từ quá trình xác thực
2. Sử dụng API Checkout để tính toán giá trị đơn hàng trước khi tạo đơn hàng
3. Tạo đơn hàng mới với dữ liệu đã được tính toán
4. Hiển thị thông tin đơn hàng cho người dùng

### Ví dụ tích hợp (JavaScript)

```javascript
// Hàm tạo đơn hàng mới
async function createOrder(orderData) {
  try {
    const response = await fetch('https://your-domain.com/api/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      },
      body: JSON.stringify(orderData)
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Lỗi khi tạo đơn hàng');
    }
    
    return data;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
}

// Hàm lấy danh sách đơn hàng
async function getOrders(page = 1, limit = 10, status = null) {
  try {
    let url = `https://your-domain.com/api/orders?page=${page}&limit=${limit}`;
    
    if (status) {
      url += `&status=${status}`;
    }
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      }
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Lỗi khi lấy danh sách đơn hàng');
    }
    
    return data.data;
  } catch (error) {
    console.error('Error fetching orders:', error);
    return { orders: [], pagination: { total: 0, page, limit, total_pages: 0 } };
  }
}

// Hàm lấy chi tiết đơn hàng
async function getOrderDetails(orderId) {
  try {
    const response = await fetch(`https://your-domain.com/api/orders/${orderId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      }
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Lỗi khi lấy chi tiết đơn hàng');
    }
    
    return data.data.order;
  } catch (error) {
    console.error(`Error fetching order details for ID ${orderId}:`, error);
    return null;
  }
}

// Ví dụ sử dụng
// Tạo đơn hàng mới
const orderData = {
  items: [
    {
      product_id: "product-789",
      quantity: 2,
      price: 120000,
      original_price: 150000,
      flash_sale_id: "flash-sale-345",
      discount_percentage: 20
    },
    {
      product_id: "product-012",
      quantity: 1,
      price: 60000,
      original_price: 60000,
      attribute_id: "attr-345"
    }
  ],
  subtotal: 300000,
  discount: 60000,
  total_amount: 240000,
  payment_method: "cash",
  status: "pending",
  customer_name: "Nguyễn Văn A",
  customer_phone: "**********",
  customer_address: "123 Đường ABC, Quận XYZ, TP.HCM"
};

createOrder(orderData)
  .then(result => {
    console.log('Đơn hàng đã được tạo thành công!');
    console.log('Order ID:', result.order_id);
    
    // Lấy chi tiết đơn hàng vừa tạo
    return getOrderDetails(result.order_id);
  })
  .then(orderDetails => {
    if (!orderDetails) return;
    
    console.log('Chi tiết đơn hàng:');
    console.log(`Mã đơn hàng: ${orderDetails.order_number}`);
    console.log(`Tổng tiền: ${orderDetails.total_amount.toLocaleString()} VND`);
    console.log(`Trạng thái: ${orderDetails.status}`);
    console.log('Sản phẩm:');
    
    orderDetails.items.forEach(item => {
      console.log(`- ${item.product_name} x${item.quantity}: ${item.total_price.toLocaleString()} VND`);
    });
  })
  .catch(error => {
    console.error('Lỗi:', error);
  });

// Lấy danh sách đơn hàng
getOrders(1, 10, 'pending')
  .then(result => {
    console.log(`Tìm thấy ${result.pagination.total} đơn hàng đang chờ xử lý`);
    
    result.orders.forEach(order => {
      console.log(`- ${order.order_number}: ${order.total_amount.toLocaleString()} VND (${order.created_at})`);
    });
  })
  .catch(error => {
    console.error('Lỗi:', error);
  });
```
