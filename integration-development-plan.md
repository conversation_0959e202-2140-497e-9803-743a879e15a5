# Kế hoạch phát triển tính năng đồng bộ dữ liệu từ bên thứ 3

## 1. Tổng quan

Tính năng này cho phép người dùng kết nối và đồng bộ dữ liệu từ các nền tảng thương mại điện tử bên thứ 3 nh<PERSON>, i<PERSON>od, KiotViet vào hệ thống. Người dùng có thể chủ động chọn mapping để tự động đồng bộ các resource về, tương tự như cách n8n hoạt động.

## 2. <PERSON><PERSON><PERSON> trúc hệ thống

### 2.1. Kiến trúc tổng thể

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  UI (Next.js)   │────▶│  API (Next.js)  │────▶│  Background     │
│                 │     │                 │     │  Jobs (Worker)  │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                      │                       │
         │                      │                       │
         ▼                      ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                        Supabase Database                        │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
         ▲                      ▲                       ▲
         │                      │                       │
         │                      │                       │
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Sapo API       │     │  iPod API       │     │  KiotViet API   │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### 2.2. <PERSON><PERSON> lý Background Jobs

Sử dụng một trong các phương pháp sau:

1. **Supabase Edge Functions**: Tạo các edge functions để xử lý các công việc đồng bộ dữ liệu.
2. **Cron Jobs API**: Tạo API endpoints được gọi bởi cron jobs từ dịch vụ bên ngoài (như Upstash, Vercel Cron).
3. **Worker Service**: Triển khai một worker service riêng biệt để xử lý các công việc nặng.

Đề xuất: Sử dụng kết hợp Supabase Edge Functions và Cron Jobs API để tối ưu chi phí và dễ triển khai.

## 3. Cấu trúc dữ liệu

### 3.1. Mở rộng bảng `integrations`

Bảng `integrations` hiện tại đã có cấu trúc tốt, cần bổ sung thêm các loại integration mới:

```sql
-- Thêm các loại integration mới
ALTER TYPE public.integration_type ADD VALUE 'sapo' AFTER 'custom';
ALTER TYPE public.integration_type ADD VALUE 'kiotviet' AFTER 'sapo';
ALTER TYPE public.integration_type ADD VALUE 'ipod' AFTER 'kiotviet';
```

### 3.2. Tạo bảng `integration_mappings`

```sql
-- Bảng lưu trữ mapping giữa các trường dữ liệu
CREATE TABLE public.integration_mappings (
  id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  integration_id uuid REFERENCES public.integrations(id) ON DELETE CASCADE,
  resource_type text NOT NULL, -- 'products', 'orders', 'customers', etc.
  source_field text NOT NULL,
  target_field text NOT NULL,
  transform_function text, -- Optional transformation function
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Index
CREATE INDEX idx_integration_mappings_integration_id ON public.integration_mappings(integration_id);
CREATE INDEX idx_integration_mappings_resource_type ON public.integration_mappings(resource_type);
```

### 3.3. Tạo bảng `integration_sync_logs`

```sql
-- Bảng lưu trữ lịch sử đồng bộ
CREATE TABLE public.integration_sync_logs (
  id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  integration_id uuid REFERENCES public.integrations(id) ON DELETE CASCADE,
  resource_type text NOT NULL,
  status text NOT NULL, -- 'success', 'error', 'in_progress'
  items_processed integer DEFAULT 0,
  items_created integer DEFAULT 0,
  items_updated integer DEFAULT 0,
  items_failed integer DEFAULT 0,
  error_message text,
  started_at timestamptz DEFAULT now(),
  completed_at timestamptz,
  created_by uuid REFERENCES auth.users(id),
  metadata jsonb DEFAULT '{}'::jsonb
);

-- Index
CREATE INDEX idx_integration_sync_logs_integration_id ON public.integration_sync_logs(integration_id);
CREATE INDEX idx_integration_sync_logs_status ON public.integration_sync_logs(status);
```

### 3.4. Tạo bảng `integration_sync_items`

```sql
-- Bảng lưu trữ chi tiết các item được đồng bộ
CREATE TABLE public.integration_sync_items (
  id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  sync_log_id uuid REFERENCES public.integration_sync_logs(id) ON DELETE CASCADE,
  external_id text NOT NULL, -- ID của item trên hệ thống bên thứ 3
  internal_id text, -- ID của item trên hệ thống của chúng ta
  resource_type text NOT NULL,
  status text NOT NULL, -- 'success', 'error'
  error_message text,
  raw_data jsonb, -- Dữ liệu gốc từ API bên thứ 3
  processed_data jsonb, -- Dữ liệu sau khi xử lý mapping
  created_at timestamptz DEFAULT now()
);

-- Index
CREATE INDEX idx_integration_sync_items_sync_log_id ON public.integration_sync_items(sync_log_id);
CREATE INDEX idx_integration_sync_items_external_id ON public.integration_sync_items(external_id);
CREATE INDEX idx_integration_sync_items_internal_id ON public.integration_sync_items(internal_id);
```

## 4. Các API Endpoints

### 4.1. API cho UI

```
# Quản lý Integration
GET    /api/integrations                  - Lấy danh sách integrations
POST   /api/integrations                  - Tạo integration mới
GET    /api/integrations/:id              - Lấy chi tiết integration
PUT    /api/integrations/:id              - Cập nhật integration
DELETE /api/integrations/:id              - Xóa integration
POST   /api/integrations/:id/connect      - Kết nối với dịch vụ bên thứ 3
POST   /api/integrations/:id/disconnect   - Ngắt kết nối

# Quản lý Mapping
GET    /api/integrations/:id/mappings              - Lấy danh sách mappings
POST   /api/integrations/:id/mappings              - Tạo mapping mới
PUT    /api/integrations/:id/mappings/:mappingId   - Cập nhật mapping
DELETE /api/integrations/:id/mappings/:mappingId   - Xóa mapping

# Đồng bộ dữ liệu
POST   /api/integrations/:id/sync                  - Bắt đầu đồng bộ thủ công
GET    /api/integrations/:id/sync/logs             - Lấy lịch sử đồng bộ
GET    /api/integrations/:id/sync/logs/:logId      - Lấy chi tiết lịch sử đồng bộ
GET    /api/integrations/:id/sync/logs/:logId/items - Lấy danh sách items đã đồng bộ
```

### 4.2. API cho Background Jobs

```
# API cho Cron Jobs
POST   /api/cron/sync-integrations                 - Đồng bộ tất cả integrations đang active
POST   /api/cron/refresh-integration-tokens        - Refresh tokens cho các integrations

# Webhook Handlers
POST   /api/webhooks/sapo                          - Webhook handler cho Sapo
POST   /api/webhooks/kiotviet                      - Webhook handler cho KiotViet
POST   /api/webhooks/ipod                          - Webhook handler cho iPod
```

## 5. Giao diện người dùng (UI/UX)

### 5.1. Trang quản lý Integrations

- Danh sách các integrations có sẵn
- Trạng thái kết nối
- Nút kết nối/ngắt kết nối
- Nút cấu hình

### 5.2. Trang cấu hình Integration

- Thông tin cơ bản (tên, mô tả)
- Cấu hình API (API key, secret, URL)
- Cấu hình đồng bộ (tần suất, loại dữ liệu)

### 5.3. Trang Mapping

- Giao diện kéo thả để mapping các trường
- Hỗ trợ preview dữ liệu
- Lưu template mapping

### 5.4. Trang lịch sử đồng bộ

- Danh sách các lần đồng bộ
- Thống kê (số lượng items, thành công/thất bại)
- Chi tiết lỗi

## 6. Quy trình đồng bộ dữ liệu

### 6.1. Đồng bộ thủ công

1. Người dùng chọn loại dữ liệu cần đồng bộ
2. Hệ thống tạo một sync log với trạng thái "in_progress"
3. Hệ thống gọi API bên thứ 3 để lấy dữ liệu
4. Áp dụng mapping để chuyển đổi dữ liệu
5. Lưu dữ liệu vào hệ thống
6. Cập nhật sync log với kết quả

### 6.2. Đồng bộ tự động

1. Cron job gọi API `/api/cron/sync-integrations`
2. Hệ thống lấy danh sách các integrations đang active
3. Với mỗi integration:
   - Tạo một sync log với trạng thái "in_progress"
   - Gọi API bên thứ 3 để lấy dữ liệu mới/cập nhật
   - Áp dụng mapping để chuyển đổi dữ liệu
   - Lưu dữ liệu vào hệ thống
   - Cập nhật sync log với kết quả

### 6.3. Đồng bộ qua Webhook

1. Bên thứ 3 gửi webhook khi có dữ liệu mới/cập nhật
2. Hệ thống xác thực webhook
3. Tạo một sync log với trạng thái "in_progress"
4. Xử lý dữ liệu từ webhook
5. Áp dụng mapping để chuyển đổi dữ liệu
6. Lưu dữ liệu vào hệ thống
7. Cập nhật sync log với kết quả

## 7. Xử lý lỗi và retry

### 7.1. Cơ chế retry

- Tự động retry khi gặp lỗi tạm thời (network, timeout)
- Giới hạn số lần retry
- Tăng thời gian chờ giữa các lần retry (exponential backoff)

### 7.2. Thông báo lỗi

- Gửi email thông báo khi đồng bộ thất bại
- Hiển thị lỗi chi tiết trong UI
- Lưu log lỗi để debug

## 8. Bảo mật

### 8.1. Lưu trữ credentials

- Mã hóa credentials trước khi lưu vào database
- Sử dụng Supabase Vault hoặc giải pháp tương tự

### 8.2. Xác thực webhook

- Xác thực webhook bằng signature
- Kiểm tra IP nguồn

### 8.3. Rate limiting

- Giới hạn số lượng requests đến API bên thứ 3
- Tuân thủ các giới hạn của API bên thứ 3

## 9. Kế hoạch triển khai

### 9.1. Giai đoạn 1: Cơ sở hạ tầng

- Tạo các bảng database mới
- Xây dựng các API endpoints cơ bản
- Thiết lập cron jobs

### 9.2. Giai đoạn 2: Tích hợp Sapo

- Xây dựng connector cho Sapo API
- Phát triển UI cho mapping Sapo
- Triển khai đồng bộ dữ liệu Sapo

### 9.3. Giai đoạn 3: Tích hợp KiotViet

- Xây dựng connector cho KiotViet API
- Phát triển UI cho mapping KiotViet
- Triển khai đồng bộ dữ liệu KiotViet

### 9.4. Giai đoạn 4: Tích hợp iPod

- Xây dựng connector cho iPod API
- Phát triển UI cho mapping iPod
- Triển khai đồng bộ dữ liệu iPod

### 9.5. Giai đoạn 5: Tối ưu và mở rộng

- Tối ưu hiệu suất
- Thêm các tính năng nâng cao
- Hỗ trợ thêm các nền tảng khác

## 10. Công nghệ sử dụng

### 10.1. Frontend

- Next.js (đã có sẵn)
- React DnD (cho giao diện kéo thả mapping)
- React Query (quản lý state và cache)
- TailwindCSS (đã có sẵn)

### 10.2. Backend

- Next.js API Routes (đã có sẵn)
- Supabase (đã có sẵn)
- Supabase Edge Functions (cho background jobs)

### 10.3. Background Jobs

- Vercel Cron (hoặc tương tự) cho scheduled jobs
- Bull/BullMQ (nếu cần queue system riêng)

### 10.4. Monitoring

- Sentry hoặc tương tự cho error tracking
- Prometheus + Grafana cho metrics (nếu cần)

## 11. Ước tính thời gian và nguồn lực

### 11.1. Thời gian

- Giai đoạn 1: 2-3 tuần
- Giai đoạn 2: 2-3 tuần
- Giai đoạn 3: 2 tuần
- Giai đoạn 4: 2 tuần
- Giai đoạn 5: 2-4 tuần
- Tổng cộng: 10-14 tuần

### 11.2. Nguồn lực

- 1 Frontend Developer
- 1 Backend Developer
- 1 QA Engineer (part-time)
- 1 Project Manager (part-time)

## 12. Kết luận

Tính năng đồng bộ dữ liệu từ bên thứ 3 sẽ mang lại giá trị lớn cho người dùng, giúp họ tự động hóa quy trình và tiết kiệm thời gian. Với kiến trúc được đề xuất, hệ thống sẽ có khả năng mở rộng để hỗ trợ nhiều nền tảng khác trong tương lai.
