# Hướng dẫn sử dụng API Analytics

Tài liệu này mô tả cách sử dụng API Analytics để ghi nhận các sự kiện từ Zalo Mini App và các ứng dụng khác.

## Tổng quan

API Analytics cho phép bạn ghi nhận các sự kiện người dùng như xem trang, xem sản phẩm, thêm vào giỏ hàng và mua hàng. Dữ liệu này được lưu trữ trong bảng `analytics_events` và có thể được sử dụng để phân tích hành vi người dùng.

## Endpoints

### 1. API Analytics chính (yêu cầu xác thực)
```
POST /api/analytics
```

### 2. API Analytics cho Zalo Mini App (xác thực riêng)
```
POST /api/analytics/zalo
```

## X<PERSON><PERSON> thực

### Đối với `/api/analytics`
API yêu cầu xác thực bằng JWT token từ Supabase Auth. Token này cần được gửi trong header `Authorization` và phải chứa `account_id` trong `user_metadata`.

```
Authorization: Bearer <supabase_jwt_token>
```

### Đối với `/api/analytics/zalo`
API sử dụng token xác thực riêng cho Zalo Mini App. Token này cần được gửi trong header `Authorization`.

```
Authorization: Bearer <zalo_token>
```

## Tham số

### Tham số chung cho cả hai endpoints

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| eventType | string | Có | Loại sự kiện (`pageview`, `product_view`, `add_to_cart`, `purchase`, `identify`) |
| themeId | string | Có | ID của theme (cửa hàng) |
| visitorId | string | Có | ID của người truy cập |
| deviceType | string | Có | Loại thiết bị (`mobile`, `desktop`, `tablet`) |

### Tham số riêng cho `/api/analytics/zalo`

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| accountId | string | Có | ID của account (chỉ cho Zalo endpoint) |
| customerId | string | Không | ID của khách hàng (tùy chọn) |

### Tham số bổ sung cho từng loại sự kiện

#### Sự kiện `pageview`

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| pagePath | string | Không | Đường dẫn trang (mặc định: `/`) |
| pageTitle | string | Không | Tiêu đề trang |
| duration | number | Không | Thời gian xem trang (giây) |
| utmSource | string | Không | Nguồn UTM |
| utmMedium | string | Không | Kênh UTM |
| utmCampaign | string | Không | Chiến dịch UTM |
| browser | string | Không | Trình duyệt |
| os | string | Không | Hệ điều hành |
| ipAddress | string | Không | Địa chỉ IP |
| referrer | string | Không | Trang giới thiệu |

#### Sự kiện `product_view` và `add_to_cart`

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| productId | string | Có | ID của sản phẩm |
| quantity | number | Không | Số lượng (mặc định: 1) |

#### Sự kiện `purchase`

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| productId | string | Có | ID của sản phẩm |
| quantity | number | Không | Số lượng (mặc định: 1) |
| orderId | string | Có | ID của đơn hàng |
| amount | number | Không | Tổng giá trị đơn hàng (mặc định: 0) |

## Phản hồi

### Thành công

```json
{
  "success": true
}
```

### Lỗi

```json
{
  "error": "Mô tả lỗi",
  "details": "Chi tiết lỗi (nếu có)"
}
```

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| 400 | Thiếu tham số bắt buộc hoặc loại sự kiện không được hỗ trợ |
| 401 | Không tìm thấy account_id trong token |
| 500 | Lỗi server khi lưu sự kiện |

## Ví dụ

### Ghi nhận sự kiện xem trang (API chính)

```bash
curl -X POST https://your-domain.com/api/analytics \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_supabase_jwt_token" \
  -d '{
    "eventType": "pageview",
    "themeId": "123e4567-e89b-12d3-a456-************",
    "visitorId": "visitor-123",
    "deviceType": "mobile",
    "pagePath": "/products",
    "pageTitle": "Danh sách sản phẩm",
    "utmSource": "zalo",
    "utmMedium": "social",
    "utmCampaign": "summer_sale"
  }'
```

### Ghi nhận sự kiện xem trang (Zalo endpoint)

```bash
curl -X POST https://your-domain.com/api/analytics/zalo \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_zalo_token" \
  -d '{
    "eventType": "pageview",
    "accountId": "123e4567-e89b-12d3-a456-************",
    "themeId": "123e4567-e89b-12d3-a456-************",
    "visitorId": "visitor-123",
    "deviceType": "mobile",
    "pagePath": "/products",
    "pageTitle": "Danh sách sản phẩm",
    "utmSource": "zalo",
    "utmMedium": "social",
    "utmCampaign": "summer_sale"
  }'
```

### Ghi nhận sự kiện xem sản phẩm

```bash
curl -X POST https://your-domain.com/api/analytics \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "eventType": "product_view",
    "themeId": "123e4567-e89b-12d3-a456-************",
    "visitorId": "visitor-123",
    "deviceType": "mobile",
    "productId": "product-456"
  }'
```

### Ghi nhận sự kiện thêm vào giỏ hàng

```bash
curl -X POST https://your-domain.com/api/analytics \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "eventType": "add_to_cart",
    "themeId": "123e4567-e89b-12d3-a456-************",
    "visitorId": "visitor-123",
    "deviceType": "mobile",
    "productId": "product-456",
    "quantity": 2
  }'
```

### Ghi nhận sự kiện mua hàng

```bash
curl -X POST https://your-domain.com/api/analytics \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "eventType": "purchase",
    "themeId": "123e4567-e89b-12d3-a456-************",
    "visitorId": "visitor-123",
    "deviceType": "mobile",
    "productId": "product-456",
    "quantity": 2,
    "orderId": "order-789",
    "amount": 1000000
  }'
```

## Lưu ý quan trọng

### Đối với `/api/analytics`
1. API này yêu cầu xác thực JWT token từ Supabase Auth có chứa thông tin `account_id` trong `user_metadata`.
2. `account_id` được tự động lấy từ token, không cần truyền trong request body.
3. `user_id` được tự động lấy từ token xác thực.

### Đối với `/api/analytics/zalo`
1. API này sử dụng token xác thực riêng cho Zalo Mini App.
2. `accountId` phải được truyền trong request body.
3. `customerId` có thể được truyền tùy chọn, nếu không có sẽ sử dụng `userId` từ token.

### Chung
1. Tất cả các sự kiện đều được ghi nhận với nguồn là `zalo_miniapp`.
2. Dữ liệu sự kiện được lưu trữ trong bảng `analytics_events` trong cơ sở dữ liệu.
3. Các loại sự kiện không được hỗ trợ sẽ bị từ chối với mã lỗi 400.
4. API hỗ trợ App Events system để xử lý sự kiện theo thời gian thực.

## Tích hợp với Zalo Mini App

Để tích hợp API Analytics vào Zalo Mini App, bạn cần:

1. Lấy JWT token từ quá trình xác thực
2. Tạo một ID duy nhất cho mỗi người truy cập (visitorId)
3. Gửi sự kiện đến API mỗi khi người dùng thực hiện một hành động cần theo dõi

### Ví dụ tích hợp (JavaScript)

```javascript
// Hàm gửi sự kiện analytics
async function sendAnalyticsEvent(eventData) {
  try {
    const response = await fetch('https://your-domain.com/api/analytics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      },
      body: JSON.stringify({
        ...eventData,
        visitorId: localStorage.getItem('visitor_id') || generateVisitorId(),
        deviceType: detectDeviceType()
      })
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error sending analytics event:', error);
    return { error: error.message };
  }
}

// Tạo ID người truy cập
function generateVisitorId() {
  const visitorId = 'visitor-' + Math.random().toString(36).substring(2, 15);
  localStorage.setItem('visitor_id', visitorId);
  return visitorId;
}

// Phát hiện loại thiết bị
function detectDeviceType() {
  const userAgent = navigator.userAgent;
  if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
    return 'mobile';
  } else if (/iPad|Tablet|PlayBook/i.test(userAgent)) {
    return 'tablet';
  }
  return 'desktop';
}

// Ví dụ sử dụng
// Ghi nhận sự kiện xem trang
sendAnalyticsEvent({
  eventType: 'pageview',
  themeId: 'your-theme-id',
  pagePath: window.location.pathname,
  pageTitle: document.title
});

// Ghi nhận sự kiện xem sản phẩm
function trackProductView(productId) {
  sendAnalyticsEvent({
    eventType: 'product_view',
    themeId: 'your-theme-id',
    productId
  });
}

// Ghi nhận sự kiện thêm vào giỏ hàng
function trackAddToCart(productId, quantity) {
  sendAnalyticsEvent({
    eventType: 'add_to_cart',
    themeId: 'your-theme-id',
    productId,
    quantity
  });
}

// Ghi nhận sự kiện mua hàng
function trackPurchase(productId, quantity, orderId, amount) {
  sendAnalyticsEvent({
    eventType: 'purchase',
    themeId: 'your-theme-id',
    productId,
    quantity,
    orderId,
    amount
  });
}
```

## Cấu trúc Database

### Bảng `analytics_events`

```sql
CREATE TABLE public.analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL,
  theme_id UUID,
  event_type TEXT NOT NULL,
  event_data JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID,
  visitor_id TEXT,
  device_type TEXT,
  source TEXT DEFAULT 'zalo_miniapp'
);
```

### Các trường dữ liệu

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| id | UUID | ID duy nhất của sự kiện |
| account_id | UUID | ID của account (bắt buộc) |
| theme_id | UUID | ID của theme/cửa hàng |
| event_type | TEXT | Loại sự kiện (pageview, product_view, etc.) |
| event_data | JSONB | Dữ liệu chi tiết của sự kiện |
| created_at | TIMESTAMPTZ | Thời gian tạo sự kiện |
| user_id | UUID | ID của user (nếu có) |
| visitor_id | TEXT | ID của visitor |
| device_type | TEXT | Loại thiết bị |
| source | TEXT | Nguồn sự kiện (mặc định: zalo_miniapp) |

## Tích hợp với Analytics Service

Hệ thống cũng cung cấp Analytics Service để tích hợp dễ dàng hơn:

### Sử dụng ZaloMiniAppAnalyticsService

```javascript
import { ZaloMiniAppAnalyticsService } from '@kit/analytics';

// Khởi tạo service
const analyticsService = new ZaloMiniAppAnalyticsService({
  accountId: 'your-account-id',
  themeId: 'your-theme-id'
});

// Theo dõi pageview
await analyticsService.trackPageView('/products', {
  visitorId: 'visitor-123',
  userId: 'user-456',
  deviceType: 'mobile'
});

// Theo dõi sự kiện tùy chỉnh
await analyticsService.trackEvent('add_to_cart', {
  productId: 'product-789',
  quantity: 2,
  visitorId: 'visitor-123',
  userId: 'user-456',
  deviceType: 'mobile'
});

// Identify user
await analyticsService.identify('user-456', {
  name: 'John Doe',
  email: '<EMAIL>'
});
```

## Best Practices

### 1. Chọn endpoint phù hợp
- Sử dụng `/api/analytics` cho các ứng dụng đã tích hợp Supabase Auth
- Sử dụng `/api/analytics/zalo` cho Zalo Mini App hoặc các ứng dụng có hệ thống xác thực riêng

### 2. Quản lý visitor ID
- Tạo visitor ID duy nhất cho mỗi session
- Lưu trữ visitor ID trong localStorage hoặc sessionStorage
- Đảm bảo visitor ID được truyền trong tất cả các sự kiện

### 3. Xử lý lỗi
- Luôn kiểm tra response status và xử lý lỗi phù hợp
- Implement retry logic cho các lỗi tạm thời (5xx)
- Log lỗi để debug và monitoring

### 4. Performance
- Batch multiple events nếu có thể
- Sử dụng async/await để không block UI
- Consider queuing events khi offline

## Troubleshooting

### Lỗi 401 - Unauthorized
- **Nguyên nhân**: Token không hợp lệ hoặc thiếu account_id
- **Giải pháp**: Kiểm tra token và đảm bảo user_metadata chứa account_id

### Lỗi 400 - Bad Request
- **Nguyên nhân**: Thiếu tham số bắt buộc hoặc eventType không hỗ trợ
- **Giải pháp**: Kiểm tra tất cả tham số bắt buộc và eventType

### Lỗi 500 - Internal Server Error
- **Nguyên nhân**: Lỗi database hoặc server
- **Giải pháp**: Kiểm tra logs server và thử lại sau

### Events không xuất hiện trong database
- Kiểm tra RLS policies cho bảng analytics_events
- Đảm bảo account_id tồn tại trong bảng accounts
- Kiểm tra network tab để xem request có thành công không
