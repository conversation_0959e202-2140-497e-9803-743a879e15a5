#!/bin/bash

# Script để chạy toàn bộ quy trình test từ đầu
# Bao gồm: tạo dữ liệu mẫu, làm mới token, và chạy test API

# Màu sắc cho output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Đường dẫn
PROJECT_ROOT=$(cd "$(dirname "$0")/../../.." && pwd)
E2E_DIR="$PROJECT_ROOT/apps/e2e"
WEB_DIR="$PROJECT_ROOT/apps/web"

# Hàm để hiển thị thông báo
function log() {
  echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

function success() {
  echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

function error() {
  echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# Kiểm tra xem server có đang chạy không
function check_server() {
  log "Kiểm tra server..."
  if curl -s http://localhost:3000 > /dev/null; then
    success "Server đang chạy trên cổng 3000"
    return 0
  elif curl -s http://localhost:3001 > /dev/null; then
    success "Server đang chạy trên cổng 3001"
    return 0
  else
    error "Server không chạy trên cổng 3000 hoặc 3001"
    return 1
  fi
}

# Khởi động server nếu cần
function start_server() {
  log "Khởi động server..."
  cd "$WEB_DIR" && pnpm dev &
  SERVER_PID=$!

  # Đợi server khởi động
  log "Đợi server khởi động..."
  for i in {1..30}; do
    if curl -s http://localhost:3000 > /dev/null; then
      success "Server đã khởi động thành công trên cổng 3000"
      return 0
    elif curl -s http://localhost:3001 > /dev/null; then
      success "Server đã khởi động thành công trên cổng 3001"
      return 0
    fi
    sleep 1
  done

  error "Không thể khởi động server"
  return 1
}

# Tạo dữ liệu mẫu
function create_sample_data() {
  log "Tạo dữ liệu mẫu..."
  cd "$PROJECT_ROOT" && pnpm --filter web-e2e test:sample-data
  if [ $? -eq 0 ]; then
    success "Đã tạo dữ liệu mẫu thành công"
    return 0
  else
    error "Không thể tạo dữ liệu mẫu"
    return 1
  fi
}

# Làm mới token NextJS bằng cách gọi API auth/zalo
function refresh_token() {
  log "Làm mới token NextJS..."

  # Kiểm tra xem đã có token NextJS chưa
  if [ -f "$WEB_DIR/.env.test.local" ] && grep -q "NEXT_AUTH_TOKEN" "$WEB_DIR/.env.test.local"; then
    log "Đã có token NextJS, kiểm tra xem có cần làm mới không..."

    # Nếu muốn bỏ qua việc làm mới token, hãy bỏ comment dòng dưới đây
    # return 0
  fi

  # Làm mới token bằng cách gọi script refresh-token.js
  cd "$PROJECT_ROOT" && pnpm --filter web-e2e api:refresh-token
  if [ $? -eq 0 ]; then
    success "Đã làm mới token NextJS thành công"
    return 0
  else
    error "Không thể làm mới token NextJS"
    return 1
  fi
}

# Lấy account theme ID
function get_account_theme() {
  log "Lấy account theme ID..."

  # Kiểm tra xem đã có theme ID chưa
  if [ -f "$WEB_DIR/.env.test.local" ] && grep -q "THEME_ID" "$WEB_DIR/.env.test.local"; then
    log "Đã có theme ID, kiểm tra xem có cần làm mới không..."

    # Nếu muốn bỏ qua việc làm mới theme ID, hãy bỏ comment dòng dưới đây
    # return 0
  fi

  # Lấy team ID và theme ID từ database
  log "Lấy team ID và theme ID từ database..."
  cd "$PROJECT_ROOT" && pnpm --filter web-e2e api:get-team-info
  if [ $? -eq 0 ]; then
    success "Đã lấy team ID và theme ID thành công"
    return 0
  else
    error "Không thể lấy team ID và theme ID"
    return 1
  fi
}

# Chạy test API
function run_api_tests() {
  log "Chạy test API..."

  # Chạy tất cả các test API (test:api đã được cấu hình để chạy auth/zalo.spec.ts trước)
  cd "$PROJECT_ROOT" && pnpm --filter web-e2e test:api
  if [ $? -eq 0 ]; then
    success "Tất cả test API đã chạy thành công"
    return 0
  else
    error "Một số test API đã thất bại"
    return 1
  fi
}

# Xóa file .env.test.local để đảm bảo môi trường sạch
function clean_env() {
  log "Xóa file .env.test.local..."
  if [ -f "$WEB_DIR/.env.test.local" ]; then
    rm -f "$WEB_DIR/.env.test.local"
    success "Đã xóa file .env.test.local"
  else
    log "Không tìm thấy file .env.test.local"
  fi
}

# Dọn dẹp
function cleanup() {
  log "Dọn dẹp..."
  if [ ! -z "$SERVER_PID" ]; then
    kill $SERVER_PID 2>/dev/null
    success "Đã dừng server"
  fi
}

# Bắt sự kiện khi script bị dừng
trap cleanup EXIT

# Chạy các bước
log "=== BẮT ĐẦU QUY TRÌNH TEST ĐẦY ĐỦ ==="

# Xóa file .env.test.local để đảm bảo môi trường sạch
clean_env

# Kiểm tra và khởi động server nếu cần
if ! check_server; then
  if ! start_server; then
    exit 1
  fi
fi

# Tạo dữ liệu mẫu
if ! create_sample_data; then
  exit 1
fi

# Lấy account theme ID
if ! get_account_theme; then
  exit 1
fi

# Làm mới token
if ! refresh_token; then
  exit 1
fi

# Chạy test API
if ! run_api_tests; then
  exit 1
fi

success "=== QUY TRÌNH TEST ĐÃ HOÀN THÀNH THÀNH CÔNG ==="
exit 0
