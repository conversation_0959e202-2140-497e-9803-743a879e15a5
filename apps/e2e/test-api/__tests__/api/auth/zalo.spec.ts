/**
 * Test cho API /api/auth/zalo
 */
import { readToken } from '../../../utils/auth';
import { createApiClient } from '../../../utils/api-client';

describe('Auth API - Zalo Authentication', () => {
  // Lấy token đã được lưu từ quá trình xác thực
  const { token } = readToken();
  const api = createApiClient(undefined, token);

  it('should verify the current token is valid', async () => {
    // Kiểm tra token hiện tại
    expect(token).toBeDefined();
    console.log('Token verified successfully');
  });

  it('should be able to access protected API with the token', async () => {
    // Gọi một API được bảo vệ để kiểm tra token
    // Sử dụng /api/products thay vì /api/orders vì nó có thể ổn định hơn
    const response = await api.get('/api/products');

    // Log kết quả để debug
    console.log('API response status:', response.status);
    console.log('API response has success property:', response.data?.success);

    expect(response.status).toBe(200);
    expect(response.data).toHaveProperty('success', true);
  });
});
