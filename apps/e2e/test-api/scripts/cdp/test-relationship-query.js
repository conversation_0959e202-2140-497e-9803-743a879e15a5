#!/usr/bin/env node

/**
 * Test relationship query performance for customer profiles
 */

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = 'http://127.0.0.1:54321';
const SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

async function testRelationshipQuery() {
  console.log('🧪 Testing Relationship Query Performance...\n');

  try {
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

    // Get account id for makerkit team
    const { data: account, error: accountError } = await supabase
      .from('accounts')
      .select('*')
      .eq('slug', 'makerkit')
      .single();

    if (accountError) {
      console.log('❌ Account lookup failed:', accountError.message);
      return;
    }

    console.log('✅ Account found:', account.name);
    console.log('📊 Account ID:', account.id);
    console.log('');

    // Test 1: Old approach - separate queries
    console.log('1️⃣ Testing OLD approach (separate queries)...');
    const startTime1 = Date.now();

    // Step 1: Get customer memberships
    const { data: memberships, error: membershipsError } = await supabase
      .from('accounts_memberships')
      .select('user_id')
      .eq('account_id', account.id)
      .eq('account_role', 'customer');

    if (membershipsError) {
      console.log('❌ Memberships query failed:', membershipsError.message);
      return;
    }

    console.log(`📊 Found ${memberships.length} customer memberships`);

    if (memberships.length > 0) {
      const userIds = memberships.map(m => m.user_id);

      // Step 2: Get personal accounts
      const { data: accounts, error: accountsError } = await supabase
        .from('accounts')
        .select('*')
        .eq('is_personal_account', true)
        .in('primary_owner_user_id', userIds);

      if (accountsError) {
        console.log('❌ Accounts query failed:', accountsError.message);
        return;
      }

      console.log(`📊 Found ${accounts.length} personal accounts`);
    }

    const endTime1 = Date.now();
    const duration1 = endTime1 - startTime1;
    console.log(`⏱️ OLD approach took: ${duration1}ms`);
    console.log('');

    // Test 2: New approach - optimized 2-query approach
    console.log('2️⃣ Testing NEW approach (optimized 2-query)...');
    const startTime2 = Date.now();

    // Step 1: Get customer memberships (same as old approach)
    const { data: newMemberships, error: newMembershipsError } = await supabase
      .from('accounts_memberships')
      .select('user_id')
      .eq('account_id', account.id)
      .eq('account_role', 'customer');

    if (newMembershipsError) {
      console.log('❌ New memberships query failed:', newMembershipsError.message);
      return;
    }

    let newAccountsData = [];
    if (newMemberships.length > 0) {
      const newUserIds = newMemberships.map(m => m.user_id);

      // Step 2: Get personal accounts with optimized query (single query with all filters)
      const { data: newAccounts, error: newAccountsError } = await supabase
        .from('accounts')
        .select('*')
        .eq('is_personal_account', true)
        .in('primary_owner_user_id', newUserIds)
        .order('created_at', { ascending: false })
        .limit(50);

      if (newAccountsError) {
        console.log('❌ New accounts query failed:', newAccountsError.message);
        return;
      }

      newAccountsData = newAccounts;
    }

    const endTime2 = Date.now();
    const duration2 = endTime2 - startTime2;

    console.log(`📊 Found ${newAccountsData.length} customer profiles via optimized approach`);
    console.log(`⏱️ NEW approach took: ${duration2}ms`);
    console.log('');

    // Show sample data
    if (newAccountsData.length > 0) {
      const sample = newAccountsData[0];
      console.log('👤 Sample optimized data:');
      console.log({
        account_id: sample.id,
        account_name: sample.name,
        account_email: sample.email,
        is_personal: sample.is_personal_account,
        public_data_keys: Object.keys(sample.public_data || {})
      });
      console.log('');
    }

    // Performance comparison
    console.log('📊 Performance Comparison:');
    console.log(`OLD approach: ${duration1}ms (${memberships.length > 0 ? '2 queries' : '1 query'})`);
    console.log(`NEW approach: ${duration2}ms (1 query)`);

    if (duration1 > duration2) {
      const improvement = ((duration1 - duration2) / duration1 * 100).toFixed(1);
      console.log(`🚀 Performance improvement: ${improvement}% faster`);
    } else if (duration2 > duration1) {
      const slower = ((duration2 - duration1) / duration1 * 100).toFixed(1);
      console.log(`⚠️ Performance regression: ${slower}% slower`);
    } else {
      console.log('🤝 Similar performance');
    }

    console.log('');

    // Test 3: Test with search and filters
    console.log('3️⃣ Testing relationship query with search...');
    const startTime3 = Date.now();

    const { data: searchData, error: searchError } = await supabase
      .from('accounts_memberships')
      .select(`
        user_id,
        created_at,
        accounts!accounts_memberships_user_id_fkey (
          id,
          name,
          email,
          phone,
          picture_url,
          created_at,
          updated_at,
          public_data,
          is_personal_account
        )
      `)
      .eq('account_id', account.id)
      .eq('account_role', 'customer')
      .eq('accounts.is_personal_account', true)
      .or('name.ilike.%Đặng%,email.ilike.%hai%')
      .limit(10);

    const endTime3 = Date.now();
    const duration3 = endTime3 - startTime3;

    if (searchError) {
      console.log('❌ Search query failed:', searchError.message);
    } else {
      console.log(`📊 Found ${searchData.length} results with search`);
      console.log(`⏱️ Search query took: ${duration3}ms`);
    }

    console.log('\n🎉 Relationship query testing completed!');

  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
  }
}

// Run the test
testRelationshipQuery();
