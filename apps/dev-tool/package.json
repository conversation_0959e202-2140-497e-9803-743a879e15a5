{"name": "dev-tool", "version": "0.1.0", "private": true, "scripts": {"clean": "git clean -xdf .next .turbo node_modules", "dev": "next dev --turbo --port=3010 | pino-pretty -c", "format": "prettier --check --write \"**/*.{js,cjs,mjs,ts,tsx,md,json}\""}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@tanstack/react-query": "5.72.2", "lucide-react": "^0.487.0", "next": "15.3.0", "nodemailer": "^6.10.0", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@kit/email-templates": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@tailwindcss/postcss": "^4.1.3", "@types/node": "^22.14.0", "@types/nodemailer": "6.4.17", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "babel-plugin-react-compiler": "19.0.0-beta-e993439-20250405", "pino-pretty": "^13.0.0", "react-hook-form": "^7.55.0", "tailwindcss": "4.1.3", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "zod": "^3.24.2"}, "prettier": "@kit/prettier-config", "browserslist": ["last 1 versions", "> 0.7%", "not dead"]}