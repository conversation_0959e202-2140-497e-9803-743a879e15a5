# Customer Data Query Optimization - Migration Guide

## Overview
This guide explains the database optimizations made to improve both customer profiles and customers query performance using relationship-based queries and optimized indexes.

## Changes Made

### 1. Database Schema Analysis
- Analyzed existing relationships between `accounts` and `accounts_memberships` tables
- Confirmed proper foreign key relationships through `auth.users` table
- Identified optimization opportunities for JOIN operations

### 2. New Migrations Created

#### Migration 1: `20241220000001_optimize_customer_profiles_queries.sql`
**Purpose**: Adds optimized indexes for better query performance

**Indexes Added**:
- `ix_accounts_memberships_customer_lookup`: Composite index for efficient customer lookups
- `ix_accounts_personal_owner_lookup`: Index for personal account queries with sorting
- `ix_accounts_personal_search`: GIN index for full-text search across name, email, phone
- `ix_accounts_public_data_total_spent`: Index for value-based filtering by total_spent
- `ix_accounts_public_data_last_active`: Index for churn analysis by last_active_at

#### Migration 2: `20241220000004_create_customer_profiles_view.sql`
**Purpose**: Creates optimized view and database function for customer profiles

**Components**:
- `customer_profiles_view`: Pre-joined view of accounts and memberships
- `load_customer_profiles_optimized()`: Database function with pagination and filtering
- Proper RLS and permissions setup

#### Migration 3: `20241220000005_optimize_customers_loader.sql`
**Purpose**: Creates optimized database function for customers loading

**Components**:
- `load_customers_optimized()`: Database function leveraging customer_profiles_view
- Returns data in Customer interface format
- Includes pagination, search, and proper permissions

### 3. Code Optimization

#### File 1: `apps/web/app/home/<USER>/cdp/_lib/server/load-customer-profiles.ts`
**Improvements**:
- Replaced multiple separate queries with single optimized database function call
- Leverages database-level JOINs instead of application-level data manipulation
- Uses optimized indexes for better performance
- Maintains all existing functionality (search, filtering, pagination)

#### File 2: `apps/web/app/home/<USER>/customers/_lib/server/customers.loader.ts`
**Improvements**:
- Replaced 2 separate queries with single optimized database function call
- Uses the same customer_profiles_view for consistency
- Maintains Customer interface compatibility
- Improved error handling and performance

## Performance Benefits

### Before Optimization
- **2 separate queries**: First get user IDs, then get accounts (both functions)
- **Application-level JOIN**: Data joining done in TypeScript
- **Multiple database round-trips**: Inefficient for large datasets
- **Limited index utilization**: Basic indexes only

### After Optimization
- **Single database function call**: All logic handled at database level (both functions)
- **Database-level JOIN**: Optimized with proper indexes
- **Pre-computed view**: Eliminates repeated JOIN calculations
- **Specialized indexes**: Optimized for specific query patterns
- **Shared infrastructure**: Both functions use the same customer_profiles_view

## How to Apply

### Step 1: Run Database Migrations
```bash
# Reset and regenerate types to apply new migrations
pnpm run supabase:web:reset && pnpm run supabase:web:typegen
```

### Step 2: Verify Migration Success
Check that the following objects exist in your database:
- Indexes: `ix_accounts_memberships_customer_lookup`, `ix_accounts_personal_owner_lookup`, etc.
- View: `customer_profiles_view`
- Functions: `load_customer_profiles_optimized`, `load_customers_optimized`

### Step 3: Test the Optimized Queries
The optimized code will automatically use the new database functions and indexes.

## Query Performance Comparison

### Original Query Pattern (Both Functions)
```sql
-- Step 1: Get customer user IDs
SELECT user_id FROM accounts_memberships
WHERE account_id = ? AND account_role = 'customer';

-- Step 2: Get personal accounts
SELECT * FROM accounts
WHERE is_personal_account = true
  AND primary_owner_user_id IN (?);
```

### Optimized Query Pattern
```sql
-- Customer Profiles (CDP)
SELECT * FROM load_customer_profiles_optimized(
  target_account_id := ?,
  search_query := ?,
  filter_type := ?,
  page_number := ?,
  page_limit := ?
);

-- Customers (General)
SELECT * FROM load_customers_optimized(
  target_account_id := ?,
  search_query := ?,
  page_number := ?,
  page_limit := ?
);
```

## Rollback Instructions
If you need to rollback these changes:

1. **Revert code changes**: Restore the original files:
   - `load-customer-profiles.ts`
   - `customers.loader.ts`
2. **Remove migrations**: Delete the migration files and reset database
3. **Regenerate types**: Run `pnpm run supabase:web:typegen`

## Monitoring and Maintenance

### Performance Monitoring
- Monitor query execution times using `EXPLAIN ANALYZE`
- Check index usage with `pg_stat_user_indexes`
- Monitor view performance with `pg_stat_user_tables`

### Index Maintenance
- Indexes are automatically maintained by PostgreSQL
- Consider `REINDEX` if performance degrades over time
- Monitor index bloat with `pg_stat_user_indexes`

## Notes
- All existing functionality is preserved
- RLS policies are properly inherited
- Backward compatibility is maintained
- No breaking changes to the API interface
