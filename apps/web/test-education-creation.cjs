// Test script to create education organization
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testEducationCreation() {
  try {
    console.log('Testing education organization creation via service...');

    // Test creating a new education organization
    const teamData = {
      name: 'Trường Mầm non Test',
      industry: 'education'
    };

    console.log('Creating team with data:', teamData);

    // Call the service action directly
    const response = await fetch('http://localhost:3001/api/test-create-team', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(teamData)
    });

    if (!response.ok) {
      console.error('HTTP Error:', response.status, response.statusText);
      return;
    }

    const result = await response.json();
    console.log('Service response:', result);

    if (result.success) {
      console.log('✅ Education organization created successfully!');
      console.log('Team ID:', result.teamId);
      console.log('Team Slug:', result.teamSlug);

      // Check created data
      const { data: programs } = await supabase
        .from('programs')
        .select('*')
        .eq('account_id', result.teamId);

      const { data: instructors } = await supabase
        .from('instructors')
        .select('*')
        .eq('account_id', result.teamId);

      const { data: learners } = await supabase
        .from('learners')
        .select('*')
        .eq('account_id', result.teamId);

      console.log('Created education data:');
      console.log('- Programs:', programs?.length || 0);
      console.log('- Instructors:', instructors?.length || 0);
      console.log('- Learners:', learners?.length || 0);
    } else {
      console.error('❌ Failed to create education organization');
      console.error('Error:', result.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testEducationCreation();
