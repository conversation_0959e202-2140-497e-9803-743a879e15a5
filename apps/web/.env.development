# This file is used to define environment variables for the development environment.
# These values are only used when running the app in development mode.

# SUPABASE
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321

#NEXT_PUBLIC_SUPABASE_URL=https://local2-haidv.s2-dev.mkit.vn
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
SUPABASE_JWT_SECRET=super-secret-jwt-token-with-at-least-32-characters-long
NEXT_PUBLIC_DEFAULT_LOCALE=vi
## THIS IS FOR DEVELOPMENT ONLY - DO NOT USE IN PRODUCTION
SUPABASE_DB_WEBHOOK_SECRET=WEBHOOKSECRET


# BILLING
NEXT_PUBLIC_BILLING_PROVIDER=lemon-squeezy
#BILLING_MODE=one-time #order_items
BILLING_MODE=subscription

# CURRENCY
NEXT_PUBLIC_DEFAULT_CURRENCY=VND
NEXT_PUBLIC_ENABLE_MULTIPLE_CURRENCIES=false
NEXT_PUBLIC_ENABLE_CURRENCY_SELECTION=false

# EMAILS
EMAIL_SENDER="Makerkit <<EMAIL>>"
EMAIL_PORT=54325
EMAIL_HOST=localhost
EMAIL_TLS=false
EMAIL_USER=user
EMAIL_PASSWORD=password

# CONTACT FORM
CONTACT_EMAIL=<EMAIL>

# STRIPE
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51R2QSE009TeS9Nj4NROIiLJ1dTGFRh4MOKCtibWBpop2etKujXdgX1IR5RfmPQ3Aj54qOr9FGcUZsjCBiToAvRSz00OAIZhU4V

# MAILER
MAILER_PROVIDER=nodemailer

ENABLE_BILLING_TESTS=true
