/*
* brand-colors.css
*
* Custom brand colors for the website
*/

:root {
  /* Brand colors */
  --brand-red: #FF0000;
  --brand-blue: #007BFF;
  --brand-yellow: #FFDE59;

  /* Lighter and darker variants */
  --brand-red-light: #FF3333;
  --brand-red-dark: #CC0000;

  --brand-blue-light: #3399FF;
  --brand-blue-dark: #0062CC;

  --brand-yellow-light: #FFE580;
  --brand-yellow-dark: #FFCD00;

  /* Override primary colors */
  --primary: var(--brand-blue);
  --primary-foreground: white;

  /* Override accent colors */
  --accent: var(--brand-yellow);
  --accent-foreground: black;

  /* Override destructive colors */
  --destructive: var(--brand-red);
  --destructive-foreground: white;
}

/* Custom gradient classes */
.bg-brand-gradient-primary {
  background: linear-gradient(to right, var(--brand-blue), var(--brand-blue-light));
}

.bg-brand-gradient-secondary {
  background: linear-gradient(to right, var(--brand-red), var(--brand-yellow));
}

.bg-brand-gradient-accent {
  background: linear-gradient(to right, var(--brand-yellow), var(--brand-yellow-light));
}

.text-brand-gradient {
  background: linear-gradient(to right, var(--brand-red), var(--brand-blue));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-brand-gradient-primary {
  background: linear-gradient(to right, var(--brand-blue), var(--brand-blue-light));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-brand-gradient-secondary {
  background: linear-gradient(to right, var(--brand-red), var(--brand-yellow));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Button gradients */
.btn-brand-gradient {
  background: linear-gradient(to right, var(--brand-blue), var(--brand-red));
  color: white;
}

.btn-brand-gradient:hover {
  background: linear-gradient(to right, var(--brand-blue-dark), var(--brand-red-dark));
}

/* Glow effects */
.glow-brand-blue {
  box-shadow: 0 0 15px 5px rgba(0, 123, 255, 0.3);
}

.glow-brand-red {
  box-shadow: 0 0 15px 5px rgba(255, 0, 0, 0.3);
}

.glow-brand-yellow {
  box-shadow: 0 0 15px 5px rgba(255, 222, 89, 0.3);
}

/* Tab colors */
.bg-brand-blue {
  background-color: var(--brand-blue) !important;
}

.bg-brand-red {
  background-color: var(--brand-red) !important;
}

.bg-brand-yellow {
  background-color: var(--brand-yellow) !important;
}

.text-brand-blue {
  color: var(--brand-blue) !important;
}

.text-brand-red {
  color: var(--brand-red) !important;
}

.text-brand-yellow {
  color: var(--brand-yellow) !important;
}

.text-brand-yellow-dark {
  color: var(--brand-yellow-dark) !important;
}

/* Data attribute selectors for tabs */
[data-state="active"] {
  background-color: transparent;
}

/* Default tab styles */
.tabs-store, .tabs-marketing, .tabs-loyalty {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* Active tab styles */
[data-state="active"].tabs-store {
  background-color: var(--brand-blue) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
}

[data-state="active"].tabs-marketing {
  background-color: var(--brand-red) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(255, 0, 0, 0.2);
}

[data-state="active"].tabs-loyalty {
  background-color: var(--brand-yellow) !important;
  color: black !important;
  box-shadow: 0 4px 12px rgba(255, 222, 89, 0.3);
}

/* Hover effects for inactive tabs */
.tabs-store:not([data-state="active"]):hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.tabs-marketing:not([data-state="active"]):hover {
  background-color: rgba(255, 0, 0, 0.1);
}

.tabs-loyalty:not([data-state="active"]):hover {
  background-color: rgba(255, 222, 89, 0.2);
}
