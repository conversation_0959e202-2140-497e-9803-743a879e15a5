{"title": "Import/Export Data", "description": "Import and export your data using CSV or Excel files", "export": "Export", "import": "Import", "export_title": "Export Data", "export_description": "Select a resource and drag fields to export", "import_title": "Import Data", "import_description": "Upload a CSV or Excel file and map fields to import", "select_resource": "Select Resource", "products": "Products", "categories": "Categories", "orders": "Orders", "customers": "Customers", "vouchers": "Vouchers", "analytics": "Analytics", "available_fields": "Available Fields", "selected_fields": "Selected Fields", "source_fields": "Source Fields (from file)", "destination_fields": "Destination Fields (database)", "preview": "Preview", "upload_file": "Upload File", "drag_fields_here": "Drag fields here to export", "no_fields": "No fields available", "select_fields": "Please select at least one field to export", "select_mapping": "Please map at least one field to import", "export_success": "Data exported successfully", "export_error": "Failed to export data", "import_success": "Data imported successfully", "import_success_count": "Successfully imported {{count}} items", "import_error": "Failed to import data", "validation_error": "Validation failed. Please check your data", "error_loading_fields": "Error loading fields", "invalid_template": "Invalid template", "template_name_required": "Template name is required", "template_saved": "Template saved successfully", "template_save_error": "Failed to save template", "template_deleted": "Template deleted successfully", "template_delete_error": "Failed to delete template", "template_name": "Template Name", "template_name_placeholder": "Enter template name", "save": "Save", "cancel": "Cancel", "save_as_template": "Save as Template", "saved_templates": "Saved Export Templates", "use_template": "Use", "delete": "Delete", "import_partial_success": "Imported {{success}} items, failed {{failed}} items", "processing_file": "Processing file: {{progress}}%", "select_sheet": "Select Sheet", "file_rows_count": "File contains {{count}} rows", "batch_settings": "Batch Import Settings", "batch_size": "<PERSON><PERSON> Si<PERSON>", "batch_info": "Will process in {{batches}} batches", "import_progress": "Import Progress", "processed": "Processed: {{processed}}/{{total}}", "success_failed": "Success: {{success}}, Failed: {{failed}}", "export_format": "Export Format", "select_format": "Select Format", "filters": "Filters", "field": "field", "operator": "Operator", "value": "Value", "add_filter": "Add Filter", "remove": "Remove", "show_filters": "Show Filters", "hide_filters": "Hide Filters", "mapping_required": "Mapping is required", "field_selection": "Field Selection", "drag_fields_instruction": "Drag fields from the left to the right to select them for export. You can also reorder the selected fields by dragging them up or down.", "drop_file": "Drop your file here or click to browse", "supported_formats": "Supported formats: CSV, Excel (.xlsx, .xls)", "import_guide_title": "How to import data", "import_guide_step1": "Select the resource type you want to import (Products, Categories, etc.)", "import_guide_step2": "Upload a CSV or Excel file containing your data", "import_guide_step3": "Map the fields from your file to the database fields by dragging them", "import_guide_step4": "Preview the data and click Import to complete the process", "field_mapping_title": "Field Mapping", "field_mapping_description": "Drag fields from the left (your file) to the right (database) to create mappings. Required fields are marked with an asterisk (*).", "preview_description": "Showing first {count} rows", "large_file_warning": "Large File Detected", "large_file_description": "Your file contains {totalRows} rows and will be processed in batches for better performance.", "batch_size_description": "Number of rows to process at once", "batch_summary": "Processing Summary", "total_rows": "Total Rows", "batch_count": "Number of Batches", "estimated_time": "Estimated Time", "success_count": "Successful", "failed_count": "Failed", "reset": "Reset", "field_mapping_no_file": "Upload a file first to map fields. You'll be able to drag fields from your file to these database fields.", "upload_file_first": "Upload a file first to see source fields", "loading_fields": "Loading database fields...", "field_mapped_success": "Field '{{source}}' mapped to '{{destination}}'", "main_title": "Import/Export Data", "main_description": "Import and export your data using CSV or Excel files", "export_card_title": "Export Data", "export_card_description": "Export your data to CSV or Excel files. You can select specific fields and apply filters.", "import_card_title": "Import Data", "import_card_description": "Import your data from CSV or Excel files. Map fields from your file to database fields.", "go_to_export": "Go to Export", "go_to_import": "Go to Import", "more_warnings": "{{count}} more warnings. Numeric fields with invalid values will be set to 0.", "import_success_title": "Import Successful", "import_success_description": "Your data has been successfully imported. What would you like to do next?", "imported_items": "Successfully imported {{count}} items", "failed_items": "Failed to import {{count}} items", "import_more": "Import More Data", "go_to_resource": "Go to {{resource}}", "export_success_title": "Export Successful", "export_success_description": "Your data has been successfully exported. What would you like to do next?", "exported_items": "Successfully exported {{count}} items", "export_more": "Export More Data", "main_options": "Main Options", "quick_export": "Quick Export", "no_templates": "No templates found", "saved_templates_description": "Click on a template to export data immediately as CSV", "fields": "fields", "customize": "Customize", "create_template": "Create Template", "create_template_description": "Create a template by going to the export page and saving your export configuration.", "remove_mapping": "Remove mapping", "mapping_removed": "Mapping for '{{field}}' removed", "required_field_instruction": "Required - Drag a source field here", "optional_field_instruction": "Optional - Drag a source field here", "fields_mapped": "{{count}} of {{total}} fields mapped", "fields_selected": "{{count}} fields selected", "fields_count": "{{count}} fields"}