{"empty": "No products found", "createNew": "Add Product", "createNewDescription": "Create a new product for your store", "products": "Products", "pageTitle": "Products", "search": "Search products...", "addProduct": "Add Product", "editProduct": "Edit Product", "deleteProduct": "Delete Product", "productDetails": "Product Details", "productName": "Product Name", "productDescription": "Product Description", "productPrice": "Price", "productImage": "Image", "productCategory": "Category", "productStock": "Stock", "productSKU": "SKU", "productStatus": "Status", "productVariants": "Variants", "addVariant": "<PERSON><PERSON>", "status": {"active": "Active", "draft": "Draft", "outOfStock": "Out of Stock", "discontinued": "Discontinued"}, "filters": {"all": "All", "active": "Active", "outOfStock": "Out of Stock", "draft": "Draft"}, "sort": {"newest": "Newest", "oldest": "Oldest", "priceHighToLow": "Price: High to Low", "priceLowToHigh": "Price: Low to High"}, "validation": {"nameRequired": "Please enter product name", "priceRequired": "Please enter product price", "categoryRequired": "Please select a category", "invalidPrice": "Invalid price", "name_required": "Please enter product name", "category_invalid": "Invalid category", "price_positive": "Price must be greater than 0", "attribute_name_required": "Please enter attribute name", "attribute_value_required": "Please enter attribute value"}, "new": {"title": "Create New Product"}, "edit": {"title": "Edit Product"}, "create": {"success": "Product created successfully", "error": "Failed to create product"}, "update": {"success": "Product updated successfully", "error": "Failed to update product"}, "actions": {"save": "Save product", "create": "Create product", "edit": "Edit product", "delete": "Delete product"}, "delete": {"title": "Delete Product", "description": "Are you sure you want to delete product \"{{name}}\"? This action cannot be undone.", "success": "Product deleted successfully", "error": "Failed to delete product"}, "preview": {"title": "Preview"}, "gallery": {"label": "Gallery Images", "help": "Additional product images (up to 5)"}, "thumbnail": {"label": "<PERSON><PERSON><PERSON><PERSON>", "help": "Main product image (recommended: 400x400px)"}, "name": {"label": "Product Name", "description": "Choose a clear and descriptive name", "placeholder": "Enter product name"}, "type": {"label": "Product Type", "description": "Select the type that best describes your product", "placeholder": "Select a product type", "physical": "Physical Product", "physical_description": "Tangible items that require shipping", "digital": "Digital Product", "digital_description": "Downloadable items or content", "service": "Service", "service_description": "Time or expertise-based offerings"}, "category": {"label": "Category", "description": "Select a category for your product", "placeholder": "Select a category"}, "description": {"label": "Product Description", "help": "Describe your product's features and benefits in detail"}, "table": {"image": "Image", "name": "Name", "type": "Type", "price": "Price", "category": "Category", "stock": "Stock", "attributes": "Attributes", "createdAt": "Created At", "actions": "Actions", "notTracked": "Not tracked", "managedPerAttribute": "Managed per attribute", "yes": "Yes", "no": "No"}, "pagination": {"showing": "Showing {{from}} to {{to}} of {{total}} products", "previous": "Previous", "next": "Next", "first": "First", "last": "Last"}, "limitReached": "Limit Reached ({{current}}/{{limit}})", "subscriptionRequired": "Subscribe to Add Products", "upgradeRequired": "Upgrade Required", "basic_info": {"title": "Basic Information", "tooltip": "Essential details about your product"}, "pricing": {"title": "Pricing", "price": {"label": "Price", "placeholder": "Enter product price"}, "compare_price": {"label": "Compare at Price", "placeholder": "Enter original price"}, "cost": {"label": "Cost per Item", "placeholder": "Enter cost per item"}, "tax": {"label": "Tax Rate (%)", "placeholder": "Enter tax rate"}}, "attributes": {"title": "Attributes", "description": "Add variations like size, color, etc.", "add": "Add Attribute", "name": "Attribute Name", "values": "Attribute Values", "price_modifier": "Price Modifier", "add_value": "Add Value", "suggestions": {"size": "Size", "color": "Color", "topping": "Topping"}}, "branch": {"title": "Branch Availability", "description": "Select branches where this product is available", "select_all": "Select All", "deselect_all": "Deselect All", "available_everywhere": "Available Everywhere", "available_everywhere_description": "This product is available at all branches", "select_branches": "Select Branches", "select_branches_description": "Choose specific branches where this product is available", "loading": "Loading branches..."}, "inventory": {"title": "Inventory", "track": "Track Inventory", "track_description": "Enable inventory tracking across all branches", "quantity": "Total Quantity", "select_branches": "Please select branches in the Branch Availability section first", "sku": {"label": "SKU (Stock Keeping Unit)", "help": "Enter manually or click generate for automatic SKU", "placeholder": "Enter SKU or generate"}, "barcode": {"label": "Barcode (ISBN, UPC, GTIN, etc.)", "help": "Product barcode for scanning"}, "type": "Inventory Type", "global": {"title": "Global Stock", "description": "Single inventory across all branches"}, "detailed": {"title": "Branch Details", "description": "Separate inventory per branch"}, "no_tracking": "This product does not require inventory management."}}