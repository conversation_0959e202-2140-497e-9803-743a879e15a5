{"title_edu": "Education Platform", "description": "Comprehensive education management system", "dashboard": {"title": "Education Dashboard", "welcome": "Welcome to {{organizationName}}", "welcomeDescription": "Comprehensive education management system for {{organizationType}}", "metrics": {"totalLearners": "Total Learners", "activeLearners": "Active Learners", "attendanceRate": "Attendance Rate", "monthlyRevenue": "Monthly Revenue", "upcomingEvents": "Upcoming Events", "newEnrollments": "New Enrollments", "pendingFees": "Pending Fees", "thisMonth": "This month", "newRegistrations": "New"}, "navigation": {"learners": {"title": "Learners", "description": "Manage learners"}, "programs": {"title": "Programs", "description": "Manage programs"}, "instructors": {"title": "Instructors", "description": "Manage instructors"}, "attendance": {"title": "Attendance", "description": "Attendance tracking"}, "fees": {"title": "Fees", "description": "Fee management"}, "events": {"title": "Events", "description": "Event management"}, "messages": {"title": "Messages", "description": "Messages and notifications"}, "reports": {"title": "Reports", "description": "Reports and analytics"}}, "quickActions": {"title": "Quick Actions", "addLearner": "Add <PERSON> Learner", "qrAttendance": "QR Attendance", "createEvent": "Create Event"}, "recentActivity": {"title": "Recent Activity", "learnerCheckedIn": "{{learner<PERSON><PERSON>}} checked in", "feePayment": "{{guardianName}} paid tuition fee", "eventRegistrations": "Event \"{{eventName}}\" has {{count}} new registrations", "minutesAgo": "{{minutes}} minutes ago", "hourAgo": "{{hours}} hour ago", "hoursAgo": "{{hours}} hours ago"}}, "learners": {"title": "Learner Management", "description": "List and manage learners", "addLearner": "<PERSON><PERSON>", "addSuccess": "<PERSON><PERSON> added successfully", "searchPlaceholder": "Search learners...", "filters": {"allStatuses": "All Statuses", "active": "Active", "inactive": "Inactive", "graduated": "Graduated", "transferred": "Transferred"}, "stats": {"total": "Total Learners", "active": "Active", "inactive": "Inactive", "graduated": "Graduated"}, "table": {"learner": "<PERSON><PERSON>", "code": "Code", "age": "Age", "gender": "Gender", "program": "Program", "status": "Status", "enrollmentDate": "Enrollment Date", "actions": "Actions", "yearsOld": "years old", "noProgram": "No program"}, "status": {"active": "Active", "inactive": "Inactive", "graduated": "Graduated", "transferred": "Transferred"}, "gender": {"male": "Male", "female": "Female", "other": "Other"}, "emptyState": {"noResults": "No learners found matching the filters.", "noLearners": "No learners yet. Add your first learner!"}, "form": {"basicInfo": "Basic Information", "learnerCode": "Learner Code", "fullName": "Full Name", "nickname": "Nickname", "dateOfBirth": "Date of Birth", "gender": "Gender", "address": "Address", "guardianInfo": "Guardian Information", "guardianName": "Guardian Name", "guardianPhone": "Guardian Phone", "guardianEmail": "Guardian Email", "relationship": "Relationship", "healthInfo": "Health Information", "medicalConditions": "Medical Conditions", "allergies": "Allergies", "emergencyContactName": "Emergency Contact Name", "emergencyContactPhone": "Emergency Contact Phone"}, "relationship": {"father": "Father", "mother": "Mother", "guardian": "Guardian", "other": "Other"}}, "programs": {"title": "Program Management", "description": "Manage educational programs", "addProgram": "Add Program", "searchPlaceholder": "Search programs...", "filters": {"allStatuses": "All Statuses", "active": "Active", "inactive": "Inactive", "archived": "Archived"}, "stats": {"total": "Total Programs", "active": "Active", "totalEnrollments": "Total Enrollments", "avgCapacity": "Avg Capacity"}, "status": {"active": "Active", "inactive": "Inactive", "archived": "Archived"}, "enrolled": "enrolled", "emptyState": {"noResults": "No programs found matching the filters.", "noPrograms": "No programs yet. Add your first program!"}, "form": {"basicInfo": "Basic Information", "name": "Program Name", "type": "Program Type", "description": "Description", "capacity": "Capacity", "location": "Location", "tuitionFee": "Tuition Fee (VND)", "ageGroup": "Age Group", "schedule": "Schedule", "startTime": "Start Time", "endTime": "End Time", "days": "Days of Week"}, "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "addSuccess": "Program added successfully"}, "instructors": {"title": "Instructor Management", "description": "Manage instructors and staff", "addInstructor": "Add Instructor", "searchPlaceholder": "Search instructors...", "filters": {"allStatuses": "All Statuses", "active": "Active", "inactive": "Inactive", "onLeave": "On Leave", "terminated": "Terminated"}, "stats": {"total": "Total Instructors", "active": "Active", "totalClasses": "Total Classes", "totalLearners": "Total Learners"}, "table": {"instructor": "<PERSON><PERSON><PERSON><PERSON>", "code": "Code", "position": "Position", "contact": "Contact", "classes": "Classes", "status": "Status", "hireDate": "Hire Date", "actions": "Actions"}, "status": {"active": "Active", "inactive": "Inactive", "onLeave": "On Leave", "terminated": "Terminated"}, "position": {"teacher": "Teacher", "assistant": "Assistant", "coordinator": "Coordinator", "principal": "Principal", "specialist": "Specialist"}, "learners": "learners", "emptyState": {"noResults": "No instructors found matching the filters.", "noInstructors": "No instructors yet. Add your first instructor!"}, "form": {"basicInfo": "Basic Information", "employeeCode": "Employee Code", "fullName": "Full Name", "email": "Email", "phone": "Phone", "position": "Position", "department": "Department", "hireDate": "Hire Date", "salary": "Salary (VND)", "address": "Address", "professionalInfo": "Professional Information", "qualifications": "Qualifications", "specializations": "Specializations", "emergencyContact": "Emergency Contact", "emergencyContactName": "Emergency Contact Name", "emergencyContactPhone": "Emergency Contact Phone", "notes": "Notes"}, "addSuccess": "Instructor added successfully"}, "attendance": {"title": "Attendance Management", "description": "Track and manage attendance", "qrTitle": "QR Code Attendance", "qrScanner": "QR Code Scanner", "qrPlaceholder": "QR Scanner will be implemented here", "qrNote": "Use manual entry below for now", "manualEntry": "Manual Entry - Learner Code", "markPresent": "<PERSON>", "quickActions": "Quick Actions", "present": "Present", "late": "Late", "absent": "Absent", "todayAttendance": "Today's Attendance", "noRecords": "No attendance records for this date", "statistics": "Attendance Statistics", "rate": "Attendance Rate", "date": "Date", "learnerNotFound": "<PERSON><PERSON> not found", "alreadyMarked": "Attendance already marked for this learner today", "marked": "Attendance marked for {{name}}", "enterCode": "Please enter a learner code", "status": {"present": "Present", "late": "Late", "absent": "Absent"}, "searchPlaceholder": "Search learners...", "filters": {"allStatuses": "All Statuses"}, "stats": {"total": "Total Records"}, "records": "Attendance Records", "table": {"learner": "<PERSON><PERSON>", "code": "Code", "status": "Status", "checkIn": "Check In", "checkOut": "Check Out", "session": "Session", "notes": "Notes"}, "sessionTime": {"morning": "Morning", "afternoon": "Afternoon", "full_day": "Full Day"}, "export": "Export", "emptyState": {"noResults": "No attendance records found matching the filters.", "noRecords": "No attendance records for this date."}}, "fees": {"title": "Fee Management", "description": "Manage tuition and fees", "addFee": "Add Fee", "searchPlaceholder": "Search fees...", "filters": {"allStatuses": "All Statuses", "pending": "Pending", "paid": "Paid", "overdue": "Overdue", "cancelled": "Cancelled"}, "stats": {"total": "Total Fees", "pending": "Pending", "paid": "Paid", "overdue": "Overdue", "totalAmount": "Total Amount", "paidAmount": "<PERSON><PERSON>", "pendingAmount": "Pending Amount"}, "table": {"learner": "<PERSON><PERSON>", "feeType": "Fee Type", "amount": "Amount", "dueDate": "Due Date", "status": "Status", "paymentDate": "Payment Date", "actions": "Actions"}, "status": {"pending": "Pending", "paid": "Paid", "overdue": "Overdue", "cancelled": "Cancelled"}, "feeType": {"tuition": "Tuition", "registration": "Registration", "activity": "Activity", "meal": "<PERSON><PERSON>", "transport": "Transport", "material": "Material", "other": "Other"}, "emptyState": {"noResults": "No fees found matching the filters.", "noFees": "No fees yet. Add your first fee!"}, "form": {"feeInfo": "Fee Information", "learner": "<PERSON><PERSON>", "feeType": "Fee Type", "amount": "Amount (VND)", "dueDate": "Due Date", "description": "Description", "notes": "Notes"}, "addSuccess": "<PERSON><PERSON> added successfully"}, "events": {"title": "Event Management", "description": "Manage school events", "addEvent": "Add Event", "form": {"eventInfo": "Event Information", "title": "Event Title", "eventType": "Event Type", "location": "Location", "maxParticipants": "Max Participants", "startDateTime": "Start Date & Time", "endDateTime": "End Date & Time", "description": "Description", "registrationSettings": "Registration Settings", "registrationRequired": "Registration Required", "registrationRequiredDesc": "Participants must register for this event", "registrationDeadline": "Registration Deadline", "notes": "Notes"}, "eventType": {"academic": "Academic", "extracurricular": "Extracurricular", "parent_meeting": "Parent Meeting", "celebration": "Celebration", "field_trip": "Field Trip", "other": "Other"}, "addSuccess": "Event added successfully", "searchPlaceholder": "Search events...", "filters": {"allStatuses": "All Statuses", "allTypes": "All Types", "scheduled": "Scheduled", "ongoing": "Ongoing", "completed": "Completed", "cancelled": "Cancelled"}, "stats": {"total": "Total Events", "upcoming": "Upcoming", "completed": "Completed", "totalRegistrations": "Total Registrations"}, "table": {"event": "Event", "type": "Type", "datetime": "Date & Time", "location": "Location", "participants": "Participants", "status": "Status", "actions": "Actions"}, "status": {"scheduled": "Scheduled", "ongoing": "Ongoing", "completed": "Completed", "cancelled": "Cancelled"}, "export": "Export", "emptyState": {"noResults": "No events found matching the filters.", "noEvents": "No events yet. Add your first event!"}}, "messages": {"title": "Messages", "description": "Communication system", "newMessage": "New Message", "searchPlaceholder": "Search messages...", "filters": {"allStatuses": "All Statuses", "allTypes": "All Types", "sent": "<PERSON><PERSON>", "scheduled": "Scheduled", "draft": "Draft", "failed": "Failed"}, "stats": {"totalRecipients": "Total Recipients", "delivered": "Delivered", "read": "Read", "failed": "Failed"}, "table": {"message": "Message", "type": "Type", "recipients": "Recipients", "status": "Status", "delivery": "Delivery", "datetime": "Date & Time", "actions": "Actions", "recipient": "Recipient", "channel": "Channel", "deliveredAt": "Delivered At", "readAt": "Read At"}, "status": {"sent": "<PERSON><PERSON>", "scheduled": "Scheduled", "draft": "Draft", "failed": "Failed"}, "messageType": {"announcement": "Announcement", "invitation": "Invitation", "fee_reminder": "<PERSON><PERSON>", "emergency": "Emergency"}, "recipientType": {"all_parents": "All Parents", "class_parents": "Class Parents", "specific_parents": "Specific Parents", "instructors": "Instructors"}, "from": "From", "delivered": "delivered", "read": "read", "scheduledFor": "Scheduled for", "export": "Export", "emptyState": {"noResults": "No messages found matching the filters.", "noMessages": "No messages yet. Send your first message!"}, "form": {"messageContent": "Message Content", "title": "Title", "messageType": "Message Type", "priority": "Priority", "content": "Content", "recipients": "Recipients", "recipientType": "Recipient Type", "recipientCount": "This message will be sent to {{count}} recipients", "deliveryOptions": "Delivery Options", "sendSMS": "Send SMS", "sendSMSDesc": "Send via text message", "sendEmail": "Send Email", "sendEmailDesc": "Send via email", "sendPush": "Push Notification", "sendPushDesc": "Send push notification", "scheduling": "Scheduling", "scheduleSend": "Schedule Send", "scheduleSendDesc": "Send this message at a specific time", "scheduledAt": "Scheduled Date & Time", "saveDraft": "Save Draft", "schedule": "Schedule", "sendNow": "Send Now"}, "priority": {"low": "Low", "normal": "Normal", "high": "High", "urgent": "<PERSON><PERSON>"}, "viewMessage": "View Message", "edit": "Edit", "send": "Send", "delete": "Delete", "content": "Content", "recipients": "Recipients", "deliveryChannels": "Delivery Channels", "deliveryRecords": "Delivery Records", "deliveryStatus": {"delivered": "Delivered", "read": "Read"}, "savedSuccess": "Message saved as draft", "scheduledSuccess": "Message scheduled successfully", "sentSuccess": "Message sent successfully"}, "reports": {"title": "Reports & Analytics", "description": "Reports and statistics", "dateRange": "Date Range", "refresh": "Refresh", "exportAll": "Export All", "learnerReport": "Learner Report", "learnerReportDesc": "Detailed learner statistics and demographics", "attendanceReport": "Attendance Report", "attendanceReportDesc": "Attendance rates and trends", "feeReport": "Fee Report", "feeReportDesc": "Financial overview and payment status", "programReport": "Program Report", "programReportDesc": "Program performance and enrollment", "overview": {"totalLearners": "Total Learners", "active": "active", "attendanceRate": "Attendance Rate", "totalRevenue": "Total Revenue", "pending": "pending", "enrollmentRate": "Enrollment Rate", "programs": "programs"}, "total": "Total", "active": "Active", "rate": "Rate", "trend": "Trend", "paid": "Paid", "pending": "Pending", "enrollment": "Enrollment", "viewChart": "View Chart", "export": "Export", "quickActions": "Quick Actions", "monthlyReport": "Monthly Report", "customReport": "Custom Report", "scheduleReport": "Schedule Report", "monthlyReportTitle": "Monthly Report", "generatedOn": "Generated on", "newThisMonth": "new this month", "sessions": "sessions", "collected": "collected", "learnerDetails": "Learner Details", "financialSummary": "Financial Summary", "attendanceSummary": "Attendance Summary", "eventsSummary": "Events Summary", "totalFees": "Total Fees", "paidAmount": "<PERSON><PERSON>", "pendingAmount": "Pending Amount", "overdueAmount": "Overdue Amount", "totalSessions": "Total Sessions", "presentCount": "Present Count", "absentCount": "Absent Count", "totalEvents": "Total Events", "completedEvents": "Completed Events", "participationRate": "Participation Rate", "upcomingEvents": "Upcoming Events", "activeLearners": "Active Learners", "newEnrollments": "New Enrollments", "graduations": "Graduations", "print": "Print", "exportStarted": "Export started", "exportCompleted": "Export completed", "exportFailed": "Export failed", "basicSettings": "Basic Settings", "reportName": "Report Name", "reportType": "Report Type", "groupBy": "Group By", "startDate": "Start Date", "endDate": "End Date", "dataSelection": "Data Selection", "selectModules": "Select Modules", "selectMetrics": "Select Metrics", "displayOptions": "Display Options", "format": "Format", "chartType": "Chart Type", "generating": "Generating...", "generateReport": "Generate Report", "preview": "Preview", "totalRecords": "Total Records", "dataPoints": "Data Points", "coverage": "Coverage", "selectedModules": "Selected Modules", "selectedMetrics": "Selected Metrics", "exportPDF": "Export PDF", "exportExcel": "Export Excel", "generateToPreview": "Generate a report to see preview", "saveTemplate": "Save Template", "templateSaved": "Report template saved", "templateSaveFailed": "Failed to save template", "reportGenerated": "Report generated successfully", "generateFirstToPrint": "Please generate a report first", "types": {"summary": "Summary", "detailed": "Detailed", "comparison": "Comparison"}, "grouping": {"day": "Day", "week": "Week", "month": "Month", "quarter": "Quarter"}, "formats": {"table": "Table Only", "chart": "Chart Only", "both": "Table & Chart"}, "charts": {"bar": "Bar Chart", "line": "Line Chart", "pie": "Pie Chart", "area": "Area Chart"}, "modules": {"learners": "Learners", "attendance": "Attendance", "fees": "Fees", "programs": "Programs", "events": "Events", "instructors": "Instructors"}, "metrics": {"total_count": "Total Count", "new_enrollments": "New Enrollments", "active_count": "Active Count", "graduation_rate": "Graduation Rate", "attendance_rate": "Attendance Rate", "present_count": "Present Count", "absent_count": "Absent Count", "late_count": "Late Count", "total_amount": "Total Amount", "paid_amount": "<PERSON><PERSON>", "pending_amount": "Pending Amount", "overdue_amount": "Overdue Amount", "program_count": "Program Count", "enrollment_rate": "Enrollment Rate", "capacity_utilization": "Capacity Utilization", "event_count": "Event Count", "participation_rate": "Participation Rate", "completion_rate": "Completion Rate", "instructor_count": "Instructor Count", "class_load": "Class Load", "performance_rating": "Performance Rating"}, "schedule": {"active": "Active", "paused": "Paused", "error": "Error", "newSchedule": "New Schedule", "totalSchedules": "Total Schedules", "dueToday": "Due Today", "scheduledReports": "Scheduled Reports", "frequency": {"daily": "Daily", "weekly": "Weekly", "biWeekly": "Bi-weekly", "monthly": "Monthly", "quarterly": "Quarterly"}, "table": {"name": "Name", "type": "Type", "frequency": "Frequency", "nextRun": "Next Run", "lastRun": "Last Run", "recipients": "Recipients", "status": "Status", "actions": "Actions"}, "reportType": {"monthly": "Monthly", "custom": "Custom", "attendance": "Attendance", "financial": "Financial"}, "emptyState": "No scheduled reports yet. Create your first schedule!", "quickSetup": "Quick Setup", "dailyAttendance": "Daily Attendance", "dailyAttendanceDesc": "Automatic daily attendance reports", "weeklyProgress": "Weekly Progress", "weeklyProgressDesc": "Weekly learning progress reports", "monthlyFinancial": "Monthly Financial", "monthlyFinancialDesc": "Monthly financial summary reports"}}, "common": {"loading": "Loading...", "error": "Unable to load data", "viewDetails": "View Details", "edit": "Edit", "delete": "Delete", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "confirm": "Confirm", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close"}, "breadcrumbs": {"home": "Home", "education": "Education", "learners": "Learners", "programs": "Programs", "instructors": "Instructors", "attendance": "Attendance", "fees": "Fees", "events": "Events", "messages": "Messages", "reports": "Reports", "guardian": "Guardian"}, "guardian": {"title": "Guardian Dashboard", "description": "Track your children's progress", "welcome": "Welcome to Guardian Portal", "welcomeDescription": "Stay connected with your children's education journey", "stats": {"children": "My Children", "childrenDescription": "Children enrolled", "pendingFees": "Pending Fees", "pendingFeesDescription": "Outstanding payments", "upcomingEvents": "Upcoming Events", "upcomingEventsDescription": "Events to attend", "unreadMessages": "Unread Messages", "unreadMessagesDescription": "New notifications"}, "child": {"program": "Program", "recentAttendance": "Recent Attendance", "pendingFees": "Pending Fees", "dueDate": "Due", "viewDetails": "View Details", "payFees": "Pay Fees", "details": "Child Details"}, "attendance": {"title": "Attendance History"}, "quickActions": {"title": "Quick Actions", "viewAttendance": "View Attendance", "payFees": "Pay Fees", "viewEvents": "View Events", "viewMessages": "View Messages"}, "notifications": {"title": "Recent Notifications", "viewAll": "View All Notifications"}, "fees": {"title": "Fee Management"}, "events": {"title": "Events & Activities"}, "messages": {"title": "Messages & Communications"}}, "organizationType": {"kindergarten": "kindergarten", "talent_center": "talent center", "preschool": "preschool", "daycare": "daycare"}, "programType": {"early_childhood": "Early Childhood", "preschool": "Preschool", "kindergarten": "Kindergarten", "extracurricular": "Extracurricular", "special_needs": "Special Needs"}}