{"title": "Customer Data Platform", "description": "Unified customer data management and analytics", "dashboard": {"title": "CDP Dashboard", "description": "Overview of customer performance and insights", "welcome": "Welcome to CDP Dashboard", "subtitle": "Unified customer data platform with AI-powered insights"}, "metrics": {"totalCustomers": "Total Customers", "activeCustomers": "Active Customers", "monthlyRevenue": "Monthly Revenue", "conversionRate": "Conversion Rate", "churnRate": "Churn Rate", "avgOrderValue": "Avg Order Value", "customerLifetimeValue": "Customer Lifetime Value", "engagementScore": "Engagement Score"}, "tabs": {"overview": "Overview", "realtime": "Real-time", "analytics": "Interactive Analytics", "insights": "AI Insights"}, "quickActions": {"title": "Quick Actions", "viewAll": "View All", "createSegment": "Create Segment", "runCampaign": "Run Campaign", "analyzeJourney": "Analyze Journey", "exportData": "Export Data", "manageIntegrations": "Manage Integrations", "viewReports": "View Reports", "customerGrowth": "Customer Growth", "revenueGrowth": "Revenue Growth", "engagementTrend": "Engagement Trend", "conversionRate": "Conversion Rate"}, "recentActivity": {"title": "Recent Activity", "newCustomer": "New customer registered", "segmentUpdated": "Segment updated", "campaignLaunched": "Campaign launched", "integrationConnected": "Integration connected", "reportGenerated": "Report generated", "workflowExecuted": "Workflow executed"}, "systemHealth": {"title": "System Health", "allSystemsOperational": "All Systems Operational", "dataProcessing": "Data Processing", "apiPerformance": "API Performance", "integrationStatus": "Integration Status", "mlModels": "ML Models", "healthy": "Healthy", "warning": "Warning", "critical": "Critical", "operational": "Operational", "maintenance": "Maintenance"}, "cards": {"customerGrowth": "Customer Growth", "revenueGrowth": "Revenue Growth", "engagementTrend": "Engagement Trend", "conversionRate": "Conversion Rate", "churnAnalysis": "Churn Analysis", "segmentPerformance": "Segment Performance", "viewChart": "View Chart", "viewDetails": "View Details", "configure": "Configure", "refresh": "Refresh", "export": "Export Data", "fullscreen": "Fullscreen"}, "profiles": {"valueTiers": {"high": "High Value", "medium": "Medium Value", "low": "Low Value"}}, "segments": {"title": "Smart Segments", "description": "Description", "totalSegments": "Total Segments", "activeSegments": "Active Segments", "totalCustomers": "Total Customers", "avgGrowth": "Avg Growth", "autoUpdating": "Auto-updating", "autoUpdate": "Auto-update", "searchPlaceholder": "Search segments by name or description...", "loadError": "Failed to load segments", "createError": "Failed to create segment", "updateError": "Failed to update segment", "deleteError": "Failed to delete segment", "confirmDelete": "Delete Segment", "confirmDeleteDescription": "Are you sure you want to delete this segment? This action cannot be undone and will remove all associated data.", "refreshData": "Refresh Data", "activeCount": "{{count}} active", "segmentedCustomers": "Segmented Customers", "monthlyGrowth": "Monthly Growth", "aiPowered": "AI Powered", "auto": "Auto", "customers": "Customers", "viewDetails": "View Details", "editSegment": "Edit Segment", "emptyState": {"title": "No segments found", "description": "Create your first customer segment to get started", "createSegment": "Create Segment"}, "performance": {"title": "Segment Performance Overview", "description": "Customer distribution and engagement across segments"}, "filters": {"all": "All", "behavioral": "Behavioral", "demographic": "Demographic", "valueBased": "Value-based", "predictive": "Predictive", "advanced": "Advanced"}, "types": {"behavioral": "Behavioral", "demographic": "Demographic", "value_based": "Value-based", "predictive": "Predictive"}, "createSegment": {"title": "Create New Segment", "description": "Create smart customer segment with AI", "basicInfo": "Basic Information", "segmentType": "Segment Type", "criteria": "Segment Criteria", "criteriaNote": "These criteria will be used to automatically classify customers into this segment.", "submit": "Create Segment"}, "analyze": "Analyze", "campaign": "Campaign", "customerCount": "Customer Count", "name": "Segment Name", "namePlaceholder": "Enter segment name", "descriptionPlaceholder": "Describe this segment...", "autoUpdatingDescription": "Automatically update segment when new matching customers are found", "typeDescriptions": {"behavioral": "Based on customer behavior and interactions", "demographic": "Based on demographic information", "value_based": "Based on customer value and spending", "predictive": "Based on AI predictions and machine learning"}, "criteria": {"totalSpent": "Total Spent", "totalOrders": "Total Orders", "engagementScore": "Engagement Score", "churnRisk": "Churn Risk", "recentActivity": "Recent Activity", "location": "Location", "valueTier": "Value Tier", "selectLocation": "Select location", "selectValueTier": "Select value tier"}}, "analysis": {"title": "Segment Analysis", "overview": "Overview", "demographics": "Demographics", "behavior": "Behavior", "customers": "Customers", "metrics": {"totalCustomers": "Total Customers", "totalRevenue": "Total Revenue", "totalOrders": "Total Orders", "growthRate": "Growth Rate", "avgSpent": "per customer", "avgOrders": "orders per customer", "newThisMonth": "new in last 30 days"}, "distribution": {"valueTitle": "Customer Value Distribution", "valueDescription": "Breakdown of customers by value tier", "statusTitle": "Customer Status Distribution", "statusDescription": "Breakdown of customers by status"}, "insights": {"title": "Insights", "description": "Deep analysis of this customer segment"}, "valueTiers": {"high": "High Value", "medium": "Medium Value", "low": "Low Value"}, "customerStatus": {"active": "Active", "lead": "Leads", "inactive": "Inactive"}, "tabs": {"demographic": {"title": "Demographic Analysis", "description": "Customer demographic breakdown and insights", "placeholder": "Demographic analysis will be available when more customer data is collected."}, "behavior": {"title": "Behavioral Analysis", "description": "Customer behavior patterns and trends", "placeholder": "Behavioral analysis will be available when customer interaction data is collected."}, "customerList": {"title": "Customer List", "description": "Customers in this segment ({count} total)", "noCustomers": "No customers found in this segment.", "showing": "Showing {showing} of {total} customers", "orders": "orders"}}, "monthlyGrowthRate": "Monthly growth rate"}, "campaigns": {"title": "Marketing Campaigns", "description": "Create and manage automated marketing campaigns", "types": {"email": "Email", "sms": "SMS", "push": "Push Notification", "inApp": "In-App"}, "createCampaign": "Create Campaign", "editCampaign": "Edit Campaign", "viewDetails": "View Details", "totalCampaigns": "Total Campaigns", "activeCampaigns": "Active Campaigns", "totalSent": "Total Sent", "avgOpenRate": "Avg Open Rate"}, "journeys": {"title": "Journey Orchestration", "description": "Create and manage automated customer journeys", "createJourney": {"title": "Create Journey"}, "viewDetails": "View Details", "editJourney": "Edit Journey", "duplicateJourney": "Duplicate Journey", "confirmDelete": "Are you sure you want to delete this journey?", "deleteError": "Error occurred while deleting journey", "startError": "Error occurred while starting journey", "createError": "Error occurred while creating journey", "detailModal": {"description": "Customer journey details and analytics"}, "tabs": {"overview": "Overview", "steps": "Steps", "analytics": "Analytics", "settings": "Settings"}, "loadError": "Failed to load journeys", "startSuccess": "Journey started successfully", "pauseSuccess": "Journey paused successfully", "pauseError": "Failed to pause journey", "confirmDeleteDescription": "This action cannot be undone. This will permanently delete the journey and all associated data.", "status": {"active": "Active", "paused": "Paused", "draft": "Draft", "completed": "Completed"}, "participants": "Participants", "completion": "Completion", "trigger": "<PERSON><PERSON>", "steps": "Steps", "pause": "Pause", "start": "Start", "analyze": "Analyze", "emptyState": {"title": "No journeys found", "description": "Create your first customer journey to start automating customer experiences", "searchEmpty": "No journeys match your search criteria"}, "filters": {"advanced": "Advanced Filters"}, "updateError": "Failed to update journey", "editJourneyStepsPreview": "Journey Steps", "editDescription": "Update your customer journey details", "nameRequired": "Journey name is required"}, "analytics": {"title": "Advanced Analytics", "description": "Interactive charts and deep customer insights", "timeRanges": {"24h": "24H", "7d": "7D", "30d": "30D", "90d": "90D"}, "charts": {"customerGrowth": "Customer Growth", "revenueGrowth": "Revenue Growth", "engagementTrend": "Engagement Trend", "conversionRate": "Conversion Rate", "churnRate": "Churn Rate", "segmentPerformance": "Segment Performance", "monthlyRevenue": "Monthly Revenue", "customerAcquisition": "Customer Acquisition", "retentionRate": "Retention Rate", "averageOrderValue": "Average Order Value"}, "tabs": {"journey": "Customer Journey", "cohort": "Cohort Analysis", "attribution": "Attribution", "funnel": "Conversion Funnel", "revenue": "Revenue Analytics"}, "journey": {"title": "Customer Journey Flow", "description": "Step-by-step customer journey analysis", "conversionRates": "Journey Conversion Rates", "insights": "Journey Insights", "biggestDropoff": "Biggest Drop-off", "bestConverter": "Best Converter", "avgJourneyTime": "Avg Journey Time"}, "cohort": {"title": "Cohort Retention Analysis", "description": "Customer retention over time", "performance": "Cohort Performance", "retention30": "30-day Retention", "retention90": "90-day Retention", "retention1year": "1-year Retention", "churnRisk": "Churn Risk"}, "attribution": {"title": "Marketing Channel Attribution", "description": "Revenue attribution by marketing channel", "comparison": "Attribution Model Comparison", "insights": "Attribution Insights", "topChannel": "Top Channel", "bestROI": "Best ROI", "avgTouchpoints": "Avg Touchpoints"}, "funnel": {"title": "Conversion Funnel", "description": "Step-by-step conversion analysis", "rates": "Funnel Conversion Rates", "optimization": "Funnel Optimization", "overallConversion": "Overall Conversion", "potentialImprovement": "Potential Improvement"}, "revenue": {"title": "Revenue Trend Analysis", "description": "Monthly revenue growth and trends", "metrics": "Revenue Metrics", "insights": "Revenue Insights", "monthlyRecurring": "Monthly Recurring Revenue", "avgOrderValue": "Average Order Value", "customerLifetimeValue": "Customer Lifetime Value", "growthRate": "Revenue Growth Rate", "bestMonth": "Best Month", "growthTrend": "Growth Trend", "forecast": "Forecast"}}, "integrations": {"title": "Integration Hub", "description": "Connect and manage third-party integrations for your CDP", "activeIntegrations": "Active Integrations", "connected": "Connected", "recordsSynced": "Records Synced", "healthScore": "Health Score", "exportConfig": "Export Config", "addIntegration": "Add Integration", "configure": "Configure", "viewDetails": "View Details", "syncNow": "Sync Now", "status": {"connected": "Connected", "disconnected": "Disconnected", "error": "Error", "syncing": "Syncing"}, "categories": {"email": "Email Marketing", "analytics": "Analytics", "ecommerce": "E-commerce", "social": "Social Media", "advertising": "Advertising", "crm": "CRM"}}, "realtime": {"title": "Real-time Dashboard", "description": "Live customer activity and system metrics", "activeUsers": "Active Users", "pageViews": "Page Views", "conversions": "Conversions", "bounceRate": "Bounce Rate", "emailOpens": "Email Opens", "responseTime": "Avg Response Time", "connected": "Connected", "disconnected": "Disconnected", "pause": "Pause", "resume": "Resume", "lastUpdate": "Last update", "activityFeed": {"title": "Live Activity Feed", "description": "Real-time user actions and system events", "waiting": "Waiting for real-time events..."}, "systemPerformance": {"title": "System Performance", "description": "Real-time system health and performance metrics", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "databaseLoad": "Database Load", "apiResponseTime": "API Response Time", "activeConnections": "Active Connections", "systemStatus": "System Status", "allSystemsOperational": "All Systems Operational"}}, "aiInsights": {"title": "AI-Powered Insights", "description": "Machine learning insights and predictive analytics", "activeInsights": "Active Insights", "criticalIssues": "Critical Issues", "highImpact": "High Impact", "mlModels": "ML Models", "newThisWeek": "new this week", "requiresAttention": "Requires immediate attention", "revenueOpportunities": "Revenue opportunities", "allModelsActive": "All models active", "tabs": {"insights": "AI Insights", "predictions": "Predictions", "models": "ML Models", "recommendations": "Recommendations"}, "confidence": "Confidence Score", "impact": {"critical": "Critical", "high": "High", "medium": "Medium", "low": "Low"}, "categories": {"revenue": "Revenue", "churn": "Churn", "engagement": "Engagement", "conversion": "Conversion", "performance": "Performance"}, "actions": {"implement": "Implement", "dismiss": "<PERSON><PERSON><PERSON>", "configure": "Configure"}, "recommendations": {"title": "Today's AI Recommendations", "description": "Prioritized actions based on AI analysis", "highPriority": "High Priority", "mediumPriority": "Medium Priority", "lowPriority": "Low Priority"}, "models": {"accuracy": "Accuracy", "precision": "Precision", "recall": "Recall", "f1Score": "F1 Score", "aucRoc": "AUC-ROC", "lastTrained": "Last trained", "retrain": "Retrain"}, "predictions": {"title": "Customer Predictions", "description": "AI-powered predictions for individual customers", "selectCustomer": "Select a customer to view AI-powered predictions and risk assessments", "browseCustomers": "Browse Customers"}}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "create": "Create", "update": "Update", "refresh": "Refresh", "export": "Export", "import": "Import", "search": "Search", "filter": "Filter", "sort": "Sort", "actions": "Actions", "settings": "Settings", "help": "Help", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "confirm": "Confirm", "yes": "Yes", "no": "No", "all": "All", "none": "None", "select": "Select", "clear": "Clear", "reset": "Reset", "apply": "Apply", "download": "Download", "upload": "Upload", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo"}, "errors": {"generic": "An error occurred. Please try again.", "network": "Network connection error. Please check your internet connection.", "unauthorized": "You don't have permission to access this resource.", "notFound": "The requested resource was not found.", "validation": "Invalid input data. Please check and try again.", "server": "Internal server error. Please try again later.", "timeout": "Request timed out. Please try again."}, "success": {"saved": "Successfully saved", "updated": "Successfully updated", "deleted": "Successfully deleted", "created": "Successfully created", "imported": "Successfully imported", "exported": "Successfully exported", "synced": "Successfully synced"}}