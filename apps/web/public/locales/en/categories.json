{"pageTitle": "Categories", "createNew": "Add Category", "fields": {"name": "Name", "description": "Description", "parent": "Parent Category", "products": "Products", "createdAt": "Created At"}, "actions": {"edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "create": "Create Category"}, "messages": {"createSuccess": "Category created successfully", "updateSuccess": "Category updated successfully", "deleteSuccess": "Category deleted successfully", "deleteConfirm": "Are you sure you want to delete this category?", "errors": {"createFailed": "Failed to create category", "updateFailed": "Failed to update category", "deleteFailed": "Failed to delete category"}}, "form": {"name": "Category Name", "description": "Description", "parent": "Parent Category", "noParent": "No parent category", "image": "Category Image (Optional)", "image:help": "Upload an image for this category (recommended: 400x400px)"}, "create": {"title": "Create New Category"}, "edit": {"title": "Edit Category"}, "emptyState": {"title": "No categories yet", "description": "Start by creating your first category. Categories will appear here after they are created."}, "table": {"name": "Name", "parent": "Parent Category", "description": "Description", "products": "Products", "createdAt": "Created At", "actions": "Actions", "image": "Image"}, "search": {"placeholder": "Search categories..."}}