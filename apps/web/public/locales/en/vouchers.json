{"create_new": "Create New", "create_first": "Create Your First Voucher", "no_vouchers": "No Vouchers", "no_vouchers_description": "You haven't created any vouchers yet. Create your first voucher to offer discounts to your customers.", "code": "Code", "name": "Name", "description": "Description", "discount_type": "Discount Type", "discount_value": "Discount Value", "min_order_value": "Minimum Order Value", "max_discount_value": "Maximum Discount Value", "max_uses": "Maximum Uses", "start_date": "Start Date", "end_date": "End Date", "percentage": "Percentage", "fixed": "Fixed Amount", "activate": "Activate", "deactivate": "Deactivate", "delete_voucher": "Delete Voucher", "confirm_delete": "Are you sure you want to delete this voucher?", "confirm_activate": "Are you sure you want to activate this voucher?", "confirm_deactivate": "Are you sure you want to deactivate this voucher?", "create_voucher": "Create Voucher", "edit_voucher": "<PERSON>", "save_voucher": "Save Voucher", "voucher_created": "Voucher created successfully", "voucher_updated": "Voucher updated successfully", "voucher_deleted": "Voucher deleted successfully", "voucher_activated": "Voucher activated successfully", "voucher_deactivated": "Voucher deactivated successfully", "error_creating": "Error creating voucher", "error_updating": "Error updating voucher", "error_deleting": "Error deleting voucher", "error_activating": "Error activating voucher", "error_deactivating": "Error deactivating voucher", "discount": "discount", "min_order": "for orders over {{value}}", "valid_until": "Valid until", "redemptions": "Redemptions", "no_redemptions": "No redemptions yet", "order_id": "Order ID", "customer": "Customer", "discount_amount": "Discount Amount", "redeemed_at": "Redeemed At", "apply_voucher": "Apply Voucher", "voucher_code": "Voucher Code", "apply": "Apply", "applying": "Applying...", "search": {"placeholder": "Search vouchers..."}, "code_description": "A unique code that customers will enter to apply the discount.", "percentage_description": "Percentage discount (1-100)", "fixed_description": "Fixed amount discount", "min_order_value_description": "Minimum order value required to use this voucher (optional)", "max_uses_description": "Maximum number of times this voucher can be used (optional)", "max_discount_value_description": "Maximum discount amount when using percentage discount (optional)", "code_placeholder": "SUMMER2025", "name_placeholder": "Summer Discount", "description_placeholder": "Special discount for summer products...", "percentage_placeholder": "20", "fixed_placeholder": "50000", "min_order_value_placeholder": "100000", "max_uses_placeholder": "100", "max_discount_value_placeholder": "50000", "pick_a_date": "Pick a date", "voucher_details": "Voucher Details", "update_voucher": "Update Voucher", "active": "Active", "expired": "Expired", "disabled": "Disabled", "status": "Status", "uses_count": "Uses Count", "generate_code": "Generate Code", "create_voucher_description": "Create a new voucher to offer discounts to your customers.", "valid_period": "Valid from {{from}} to {{to}}", "customer_restrictions": "Customer Restrictions", "is_customer_specific": "Customer-specific voucher", "is_customer_specific_description": "If enabled, this voucher can only be used by specific customers", "customer_phones": "Customer Phone Numbers", "phone_placeholder": "+84123456789, +84987654321", "customer_phones_description": "Enter phone numbers of customers who can use this voucher. You can enter multiple numbers separated by commas. Only applies if customer-specific is enabled.", "added_phones": "Added Phone Numbers", "usage_limit_per_customer": "Usage Limit Per Customer", "usage_limit_per_customer_placeholder": "1", "usage_limit_per_customer_description": "Maximum number of times a single customer can use this voucher (optional)", "first_time_customers_only": "First-time customers only", "first_time_customers_only_description": "If enabled, this voucher can only be used by customers with no previous orders", "min_previous_orders": "Minimum Previous Orders", "min_previous_orders_placeholder": "1", "min_previous_orders_description": "Minimum number of previous orders required to use this voucher (optional)", "product_restrictions": "Product Restrictions", "included_products": "Included Products", "included_products_description": "Only these products can be discounted by this voucher (leave empty to include all products)", "excluded_products": "Excluded Products", "excluded_products_description": "These products cannot be discounted by this voucher", "included_categories": "Included Categories", "included_categories_description": "Only these categories can be discounted by this voucher (leave empty to include all categories)", "excluded_categories": "Excluded Categories", "excluded_categories_description": "These categories cannot be discounted by this voucher", "select_products": "Select products", "select_categories": "Select categories", "no_products": "No products found", "no_categories": "No categories found", "confirm_delete_description": "This action cannot be undone. This will permanently delete the voucher."}