{"welcome": {"title": "Welcome to MinApp!", "titleWithName": "Welcome to <PERSON><PERSON><PERSON>, {{name}}!", "description": "Get started by creating your first business to manage your products, orders, and customers", "teamBenefits": "Creating a business allows you to manage products, orders, and invite team members"}, "features": {"manage": "Manage Your Business", "manageDescription": "Create products, manage orders, and track your business performance", "collaborate": "Collaborate with Team", "collaborateDescription": "Invite team members and assign roles to work together efficiently", "grow": "Grow Your Business", "growDescription": "Use analytics and insights to make data-driven decisions"}, "dashboard": {"title": "Your Dashboard", "subtitle": "Manage your businesses and personal settings"}, "teams": {"title": "Your Businesses", "description": "Businesses you own or are a member of", "memberDescription": "Businesses you are a member of", "owner": "Owner", "member": "Member", "role": "{{role}}", "manage": "Manage", "access": "Access", "upgrade": "Upgrade", "upgradeTip": "Upgrade to access premium features", "freePlan": "Free Plan", "noTeams": "You are not a member of any business yet", "createToJoin": "Create a business or wait for an invitation to join one", "memberCount": "{{count}} member", "memberCount_plural": "{{count}} members", "today": "Today", "lastActive": "{{time}}", "recently": "Recently", "errorAccessingTeam": "Error accessing business", "unnamed": "Unnamed Business", "members": "Members", "active": "Active", "subscription": {"active": "Active", "trialing": "Trial", "past_due": "Past Due", "canceled": "Canceled", "unpaid": "Unpaid", "incomplete": "Incomplete", "incomplete_expired": "Expired", "paused": "Paused"}}, "quickActions": {"title": "Quick Actions", "settings": "Account <PERSON><PERSON>", "createTeam": "Create New Business", "billing": "Billing"}, "recentActivity": {"title": "Recent Activity", "empty": "No recent activity to display", "memberAdded": "{{user}} joined {{team}}", "productCreated": "New product {{product}} created in {{team}}", "settingsUpdated": "Settings updated for {{team}}"}, "pendingInvitations": {"title": "Pending Invitations", "empty": "No pending invitations", "invitedBy": "Invited by {{name}}", "accept": "Accept", "decline": "Decline"}, "whyCreate": {"title": "Why Create a Business?", "efficiency": "Increased Efficiency", "efficiencyDesc": "Streamline operations and automate repetitive tasks", "insights": "Real-time Insights", "insightsDesc": "Access analytics and reports to make informed decisions", "collaboration": "Team Collaboration", "collaborationDesc": "Work together seamlessly with role-based permissions"}, "stats": {"totalBusinesses": "Total Businesses", "ownedBusinesses": "Owned Businesses", "memberBusinesses": "Member Businesses", "lastActive": "Last Active", "today": "Today"}, "learnMore": "Learn More"}