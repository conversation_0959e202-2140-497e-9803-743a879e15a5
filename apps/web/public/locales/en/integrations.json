{"title": "Integrations", "description": "Connect your account with external services", "connect": "Connect", "configure": "Configure", "connected": "Connected", "connecting": "Connecting...", "disconnected": "Disconnected", "not_connected": "Not Connected", "lastUpdated": "Last updated", "configureDescription": "Configure integration settings", "comingSoon": "Coming Soon", "dashboard": "Dashboard", "manageDashboard": "Manage", "setupIntegration": "Setup", "zns": {"title": "Zalo Notification Service", "description": "Send notifications to your customers via Zalo OA", "name": "Notification Templates", "welcome": "Welcome to Zalo Notification Service", "welcomeDescription": "Connect with Zalo Notification Service to send notification messages to Zalo users.", "getStarted": "Get Started", "notConfigured": "Not Configured", "notConnected": "Not Connected", "connected": "Connected", "tokenExpired": "Token Expired", "tokenExpiredDescription": "Your token has expired. Please reconnect to continue using Zalo Notification Service.", "reconnect": "Reconnect", "documentation": "Documentation", "documentationDescription": "Learn more about Zalo Notification Service", "documentationLinks": {"overview": "ZNS Overview", "templates": "Template API", "sendZns": "Send ZNS API"}, "support": "Support", "supportDescription": "Contact Zalo support team", "supportLinks": {"developerPortal": "Developer Portal", "zaloOA": "Zalo Official Account", "email": "Send Support Email"}, "pageTitle": "ZNS Configuration", "configure": {"title": "Configure ZNS Integration", "oa_id": "Zalo OA ID", "oa_token": "Zalo OA Token", "template_id": "Template ID", "success": "ZNS configuration successful", "error": "Could not configure <PERSON><PERSON>. Please try again."}, "setup": {"title": "Zalo OA Configuration", "description": "Enter your Zalo OA information to connect with Zalo notification service", "howToGet": "How to get Zalo OA information", "steps": {"step1": "Visit ", "step1Part1": "Visit", "step1Link": "Zalo Developer Portal", "step1Part2": "to get started", "step2": "Log in and select your OA", "step3": "Go to Management > App Information", "step4": "Copy the App ID and Secret Key"}, "form": {"appId": "App ID", "appIdDescription": "Your Zalo OA application ID", "secretKey": "Secret Key", "secretKeyDescription": "Your Zalo OA application secret key", "oaId": "OA ID", "oaIdDescription": "Your Zalo OA ID (starts with a number)", "manualTokenSection": "Manual Token Entry (Optional)", "manualTokenDescription": "You can manually enter Access token and Refresh token from", "getTokenLink": "<PERSON><PERSON>", "selectOaTokenType": "(select \"OA Access Token\" type and click \"Get Access Token\")", "enterAppIdFirst": "enter App ID first", "accessToken": "Access Token", "accessTokenPlaceholder": "Enter your Zalo OA Access Token", "accessTokenDescription": "Access token for calling Zalo APIs", "refreshToken": "Refresh <PERSON>", "refreshTokenPlaceholder": "Enter your Zalo OA Refresh Token", "refreshTokenDescription": "Token to refresh your Access Token when it expires", "submit": "Save Configuration", "cancel": "Cancel"}, "permissionDenied": "You don't have permission to update this OA configuration. This configuration is managed by {{manager}}.", "cannotModifySystem": "You cannot modify the system default OA configuration. Please create a new configuration for your account.", "systemAuthor": "system", "themeAuthor": "theme author", "anotherUser": "another user", "success": "OA configuration saved successfully", "error": "Failed to save OA configuration"}, "connect": {"title": "Connect Zalo Notification Service", "description": "Connect your Zalo Official Account to send notifications", "status": "Connection Status", "connected": "Connected", "notConnected": "Not Connected", "connectedDescription": "Your Zalo OA is connected and ready to send notifications", "notConnectedDescription": "Your Zalo OA is not connected. Please click \"Connect Zalo OA\" to authenticate.", "notConfiguredDescription": "Connect your Zalo OA to start sending notifications", "readyToSendNotifications": "Ready to send notifications", "zaloUser": "<PERSON><PERSON>r", "zaloUserId": "Zalo User ID", "enableZns": "Enable <PERSON>", "enabledDescription": "ZNS is enabled and will send notifications", "disabledDescription": "<PERSON><PERSON> is disabled and will not send notifications", "configureOa": "Configure OA", "connectOa": "Connect Zalo OA", "reconnectOa": "Reconnect Zalo OA", "loginOa": "Login to OA", "connecting": "Connecting...", "connectionFailed": "Connection Failed", "authUrlError": "Could not create authentication URL", "checkOaConfig": "Please check your OA configuration", "oaConfigError": "OA Configuration Error", "checkSettings": "There was an error loading the OA configuration. Please check your settings.", "setupOaFirst": "Please configure OA first", "enabledSuccess": "ZNS has been enabled successfully", "disabledSuccess": "<PERSON><PERSON> has been disabled successfully", "updateStatusError": "Could not update ZNS status", "oaInformation": "Zalo OA Information", "oaInformationDescription": "Details about your connected Zalo Official Account", "oaConfigType": "OA Configuration Type", "connectionDetails": "Connection Details", "tokenExpiresAt": "Token Expires At", "configuredAt": "Configured At", "tokenValidatedAt": "Token Validated At", "tokenLastRefreshed": "Token Last Refreshed", "authTimestamp": "Authentication Timestamp", "basicInfo": "Basic Information", "advancedInfo": "Advanced Information", "connect": {"templates": {"title": "Notification Templates", "description": "Manage your ZNS notification templates", "configureTemplates": "Configure Templates", "events": {"order_created": "Order Created", "order_confirmed": "Order Confirmed", "order_shipped": "Order Shipped", "order_delivered": "Order Delivered", "order_cancelled": "Order Cancelled"}}}, "systemDefault": "System Default", "theme": "Theme", "account": "Account", "templates": {"title": "ZNS Templates", "list": "Template List", "listDescription": "Manage your ZNS templates", "description": "Manage ZNS templates", "createTemplate": "Create New Template", "createFirstTemplate": "Create your first template", "editTemplate": "Edit Template", "viewTemplate": "View Template", "search": "Search templates...", "showing": "Showing {{start}}-{{end}} of {{total}} templates", "templateId": "Template ID", "templateName": "Template Name", "quality": "Quality", "price": "Price", "timeout": "Timeout", "preview": "Preview", "openPreview": "Open Preview", "details": "Details", "parameters": "Parameters", "sampleData": "Sample Data", "paramName": "Parameter Name", "paramType": "Parameter Type", "paramRequired": "Required", "paramLength": "Length", "paramAcceptNull": "Accept <PERSON>", "sampleValue": "Sample Value", "noParameters": "No parameters available", "noSampleData": "No sample data available", "sampleDataNotAvailable": "Sample data is only available for approved templates", "useTemplate": "Use This Template", "notConnected": "ZNS Not Connected", "notConnectedDescription": "You need to connect to Zalo Notification Service before managing templates.", "connectNow": "Connect Now", "noTemplates": "No templates available", "noTemplatesDescription": "You don't have any ZNS templates yet. Create your first template.", "noTemplatesFound": "No templates found matching your search", "createTemplateDescription": "Create a new template for Zalo Notification Service", "configureParameters": "Configure Parameters", "configureParametersDescription": "Configure parameters and add review notes to help with approval", "submitForReview": "Submit for Review", "submitForReviewDescription": "Configure parameters and add review notes to help with accurate approval", "parameterName": "Parameter Name", "parameterType": "Technical Setting", "parameterValue": "Parameter Value", "parameterTypesInfo": "Parameter types (max characters)", "example": "Example", "reviewNotes": "Review Notes", "reviewNotesPlaceholder": "Add notes to help with the approval process", "reviewNotesDescription": "Provide additional context to help reviewers understand your template", "agreeTerms": "I have read and agree to the", "termsAndPolicies": "Terms and Policies", "agreeTermsRequired": "Please agree to the terms and conditions", "validationErrors": "Please correct the errors before proceeding", "nameRequired": "Template name is required", "titleRequired": "Template title is required", "contentRequired": "Template content is required", "typeRequired": "Template type is required", "tagRequired": "Template tag is required", "lightImageRequired": "Light mode header image is required", "darkImageRequired": "Dark mode header image is required", "paramValueRequired": "Parameter value is required", "paramTypeRequired": "Parameter type is required", "useSample": "Use Sample", "sampleTemplates": "Sample Templates", "selectSampleTemplate": "Select a sample template to get started quickly", "use": "Use", "tableData": "Table Data", "addTableRow": "Add Row", "tableRowTitle": "Title", "tableRowValue": "Value", "tableRowType": "Row Type", "buttonSettings": "Action Buttons", "buttonSettingsDescription": "Maximum 2 action buttons allowed", "addButton": "Add Action Button", "templateContent": "Template Content", "templateContentHelp": "Use <n> to insert customer name parameter", "templateNotePlaceholder": "Enter a note for the reviewer", "templateNoteDescription": "This note will be sent to the reviewer", "paramTypes": {"customerName": "Customer Name (30)", "code": "Code (30)", "amount": "Amount (20)", "time": "Time (20)", "contact": "Contact (50)", "url": "URL (100)"}, "fetchError": "Error fetching templates", "fetchDetailError": "Error fetching template details", "fetchSampleDataError": "Error fetching sample data", "tokenInvalid": "<PERSON><PERSON> has expired or is invalid. Please reconnect your Zalo account.", "refreshSuccess": "Templates refreshed successfully", "templateNotFound": "Template Not Found", "templateNotFoundDescription": "The template you are looking for does not exist or you don't have access to it.", "rejectionReason": "Rejection Reason", "statusReason": "Status Reason", "cannotEditTemplate": "Only templates with REJECT status can be edited", "editTemplateDescription": "Edit your template details below", "editFormPlaceholder": "Template edit form would be implemented here", "createFormPlaceholder": "Template creation form would be implemented here", "templateType": "Template Type", "templateTag": "Template Tag", "templateNote": "Review Note", "templateLayout": "Template Layout", "templateParams": "Template Parameters", "saveSuccess": "Template saved successfully", "createSuccess": "Template created successfully", "editTab": "Edit", "previewTab": "Preview", "generatePreview": "Generate Preview", "previewGenerated": "Preview generated successfully", "previewError": "Error generating preview", "noPreview": "No preview available", "generatePreviewHelp": "Fill in the template details and click \"Generate Preview\" to see how your template will look.", "previewDisclaimer": "This is a preview of how your template might look. The actual appearance may vary.", "templateApplied": "Template sample applied successfully", "fetchSamplesError": "Error fetching template samples", "templatePreview": "Template Preview", "noPreviewAvailable": "No preview available for this template.", "applyError": "Error applying template", "templateTitle": "Template Title", "templateNameDescription": "Enter a name for your template", "templateTypeDescription": "Select the type of template", "templateTagDescription": "Select the tag for your template", "selectType": "Select type", "selectTag": "Select tag", "layoutDescription": "This is where you would configure the template layout, components, and parameters", "types": {"custom": "Custom", "authentication": "Authentication", "paymentRequest": "Payment Request", "voucher": "Voucher", "serviceRating": "Service Rating"}, "statusTitle": "Status", "tagTitle": "Tag", "status": {"all": "All Statuses", "enabled": "Enabled", "pendingReview": "Pending Review", "rejected": "Rejected", "disabled": "Disabled"}, "filterByStatus": "Filter by status", "tag": {"transaction": "Transaction", "customerCare": "Customer Care", "promotion": "Promotion", "unknown": "Unknown"}}, "send": {"title": "Send ZNS", "formTitle": "Send ZNS Message", "formDescription": "Fill in the details to send a ZNS message", "templateId": "Template ID", "templateIdDescription": "ID of the ZNS template you want to use", "phone": "Phone Number", "phoneDescription": "Recipient's phone number (normalized format, e.g.: 84987654321)", "sendingMode": "Sending Mode", "sendingModeDescription": "Choose how to send the ZNS message", "selectSendingMode": "Select sending mode", "sendingModes": {"normal": "Normal", "exceedQuota": "Exceed <PERSON><PERSON><PERSON>", "development": "Development Mode"}, "useHashPhone": "Use Hash Phone", "useHashPhoneDescription": "Encrypt phone number before sending", "trackingId": "Tracking ID", "trackingIdDescription": "Custom tracking ID defined by you", "parameters": "Parameters", "paramTypeDescription": "Type: {{type}}, Length: {{min}}-{{max}}", "useSampleData": "Use Sample Data", "sendButton": "Send Message", "result": "Send Result", "resultDescription": "Information about the sent ZNS message", "success": "Message sent successfully", "error": "Error sending message", "messageId": "Message ID", "sentTime": "Sent Time", "remainingQuota": "<PERSON><PERSON><PERSON>", "notConnected": "ZNS Not Connected", "notConnectedDescription": "You need to connect to Zalo Notification Service before sending messages.", "connectNow": "Connect Now", "fetchTemplateError": "Error fetching template information", "fetchSampleDataError": "Error fetching sample data", "sampleDataApplied": "Sample data applied"}, "dashboard": {"title": "ZNS Dashboard", "backToIntegrations": "Back to Integrations", "manageTemplates": "Manage Templates", "connectionStatus": "Connection Status", "connected": "Connected", "error": "Error", "disconnected": "Disconnected", "connectedTooltip": "ZNS is connected and operational", "errorTooltip": "There is an error with the ZNS connection", "disconnectedTooltip": "ZNS is not connected", "notConnected": "ZNS is not connected. Please configure your connection.", "connectNow": "Connect Now", "oaName": "OA Name", "oaId": "OA ID", "appId": "App ID", "lastConnected": "Last Connected", "tokenExpires": "Token Expires", "testConnection": "Test Connection", "templateStats": "Template Stats", "templateStatsTooltip": "Statistics about your ZNS templates", "totalTemplates": "Total Templates", "enabledTemplates": "Enabled Templates", "pendingTemplates": "Pending Review", "rejectedTemplates": "Rejected Templates", "connectToViewTemplates": "Connect to ZNS to view template statistics.", "quotaUsage": "<PERSON><PERSON><PERSON>", "quotaUsageTooltip": "Your daily ZNS message quota usage", "dailyQuota": "Daily Quota", "remainingToday": "Remaining Today", "usedToday": "Used Today", "usagePercentage": "Usage Percentage", "connectToViewQuota": "Connect to ZNS to view quota usage.", "viewAnalytics": "View Analytics", "healthStatus": "Health Status", "healthStatusTooltip": "Current health status of your ZNS connection", "apiLatency": "API Latency", "uptime": "Uptime", "errorCount": "Error Count", "lastError": "Last Error", "connectToViewHealth": "Connect to ZNS to view health status.", "manageConnection": "Manage Connection", "connectionSuccess": "Connection Successful", "connectionError": "Connection Error", "noConnectionDetails": "No Connection Details", "pleaseConfigureFirst": "Please configure your connection first", "accountNotFound": "Account not found", "readyToSendNotifications": "Ready to send notifications"}, "templatesDescription": "Configure templates for different notification types", "noTemplates": "No templates found", "addTemplate": "Add Template", "editTemplate": "Edit Template", "deleteTemplate": "Delete Template", "templateAdded": "Template added successfully", "templateUpdated": "Template updated successfully", "templateDeleted": "Template deleted successfully", "templateError": "Error managing template", "enabled": "Enabled", "disabled": "Disabled", "form": {"enabled": "Enabled", "templateId": "Template ID", "templateIdDescription": "ZNS template ID", "content": "Content", "contentDescription": "Template content", "params": "Parameters", "paramsDescription": "Parameters used in the template", "submit": "Save Configuration", "cancel": "Cancel"}}}, "ipos": {"connect": {"title": "Connect iPOS", "description": "Connect your iPOS account to sync products, orders, and customers.", "credentials": "Credentials", "webhook": "Webhook", "accessToken": "Access Token", "accessTokenPlaceholder": "Enter your iPOS Access Token", "accessTokenDescription": "Your iPOS Access Token from the developer portal.", "posParent": "POS Parent", "posParentPlaceholder": "Enter your iPOS Parent ID", "posParentDescription": "Your iPOS Parent ID (Brand ID).", "posId": "POS ID", "posIdPlaceholder": "Enter your iPOS Store ID", "posIdDescription": "Your iPOS Store ID.", "baseUrl": "API Base URL", "baseUrlPlaceholder": "https://api.foodbook.vn", "baseUrlDescription": "The base URL for the iPOS API.", "webhookSetup": "Webhook Setup", "webhookDescription": "Set up webhooks to receive real-time updates from iPOS.", "webhookUrl": "Webhook URL", "webhookUrlDescription": "Add this URL to your iPOS developer portal to receive webhooks.", "webhookEvents": "Webhook Events", "webhookEventsDescription": "Register the following events in your iPOS developer portal:", "testConnection": "Test Connection", "testing": "Testing...", "testSuccess": "Connection successful", "testSuccessDescription": "Successfully connected to the iPOS API.", "testError": "Connection failed", "testErrorDescription": "Could not connect to the iPOS API. Please check your credentials.", "connect": "Connect", "success": "Connected successfully", "successDescription": "Your iPOS account has been connected successfully.", "error": "Connection failed", "checkOaConfig": "Please check your iPOS configuration.", "footer": "Need help? See <1>iPOS API documentation</1>.", "benefitsTitle": "Integration Benefits:", "benefit1": "Automatically sync products from iPOS to your system", "benefit2": "Keep orders synchronized between platforms", "benefit3": "Maintain consistent customer data", "benefit4": "Receive real-time updates via webhooks", "setupInstructions": "Setup Instructions", "setupSteps": "Complete these steps to set up your iPOS integration:", "step1": "Enter your iPOS API credentials (required)", "step2": "Test the connection to verify credentials", "step3": "Set up webhooks for real-time updates (optional)", "step4": "Click \"Complete Setup\" to finish", "exampleNote": "<0>Note:</0> Example values are pre-filled for quick testing.", "accessTokenExample": "<0>Example:</0> JHTHWPCE6OCZBW0PBH9XRRBC6JTR1UWQ", "posParentExample": "<0>Example:</0> SAOBANG", "posIdExample": "<0>Example:</0> 3160", "baseUrlDefault": "<0>Default:</0> https://api.foodbook.vn"}, "dashboard": {"title": "iPOS Dashboard", "connectionStatus": "Connection Status", "connected": "Connected", "connectionIssue": "Connection Issue", "notConnected": "Not Connected", "connectionHealthy": "Connection is healthy", "connectionUnhealthy": "Connection is having issues", "connectionNotEstablished": "Connection has not been established", "configure": "Configure", "testConnection": "Test", "connectionSuccess": "Connection successful", "connectionError": "Connection error", "apiLatency": "API Latency", "mappingStatus": "Field Mapping Status", "products": "Products", "orders": "Orders", "customers": "Customers", "branches": "Branches", "categories": "Categories", "vouchers": "Vouchers", "configured": "Configured", "notConfigured": "Not Configured", "syncedItems": "Synced", "noSyncYet": "No data synced yet", "editMapping": "Edit Mapping", "setupMapping": "Setup Mapping", "syncHistory": "Sync History", "viewAll": "View All", "syncNow": "Sync Now", "quickActions": "Quick Actions", "syncData": "Sync Data", "syncDataDescription": "Import products, orders, and customers from iPOS", "configureMapping": "Configure Mapping", "configureMappingDescription": "Set up field mappings between iPOS and your system", "viewSyncHistory": "View Sync History", "viewSyncHistoryDescription": "Check past synchronization results and logs", "configureConnection": "Configure Connection", "configureConnectionDescription": "Update your iPOS connection settings and credentials", "backToIntegrations": "Back to Integrations", "viewAllMappings": "View All"}, "mapping": {"title": "iPOS Data Mapping", "description": "Map fields from iPOS to your system for accurate data synchronization.", "resourceType": "Resource Type", "selectResource": "Select resource type", "products": "Products", "orders": "Orders", "customers": "Customers", "loading": "Loading fields...", "sourceFields": "iPOS Fields", "sourceFieldsDescription": "Drag fields from here to target fields.", "targetFields": "System Fields", "targetFieldsDescription": "Drop iPOS fields here to create mappings.", "mapped": "Mapped", "drag": "Drag", "dropHere": "Drop here", "dropToRemove": "Drop here to remove mapping", "testMapping": "Test Mapping", "testing": "Testing...", "testSuccess": "Test successful: {count} items processed", "testFailed": "Test failed", "testError": "Could not test mapping. Please try again.", "previewData": "Preview Data", "showingPreview": "Showing {shown} of {total} items", "syncNow": "Sync Now", "syncing": "Syncing...", "saveSuccess": "Mappings saved", "saveSuccessDescription": "Your field mappings have been saved successfully.", "saveError": "Could not save mappings", "loadError": "Could not load fields", "integrationNotFound": "iPOS integration not found", "connectFirst": "Please connect to iPOS first."}, "sync": {"title": "Sync iPOS Data", "description": "Synchronize data from iPOS to your system.", "loading": "Loading...", "selectResource": "Select Resource Type", "products": "Products", "productsDescription": "Sync product data from iPOS", "orders": "Orders", "ordersDescription": "Sync order data from iPOS", "customers": "Customers", "customersDescription": "Sync customer data from iPOS", "setupMapping": "Setup Mapping", "syncing": "Syncing...", "startSync": "Start Sync", "viewHistory": "View Sync History", "editMapping": "Edit Mapping", "syncComplete": "Sync Complete", "syncResultDescription": "Processed {total} items: {created} created/updated, {failed} failed.", "viewDetails": "View Sync Details", "syncSuccess": "Sync Complete", "syncSuccessDescription": "Processed {total} items: {created} created/updated, {failed} failed.", "syncError": "Sync Failed", "integrationNotFound": "iPOS integration not found", "connectFirst": "Please connect to iPOS first."}, "syncHistory": {"title": "iPOS Sync History", "description": "View history of data synchronization with iPOS.", "backToMapping": "Back to Mapping", "newSync": "New Sync", "searchPlaceholder": "Search by resource type or error message", "resourceFilter": "Filter by resource", "statusFilter": "Filter by status", "allResources": "All resources", "allStatuses": "All statuses", "products": "Products", "orders": "Orders", "customers": "Customers", "success": "Success", "error": "Error", "partial": "Partial", "inProgress": "In Progress", "loading": "Loading sync history...", "noLogs": "No sync logs found", "startSync": "Start Sync", "noIntegration": "No active iPOS integration found for this account", "setupIntegration": "Set Up Integration", "resourceType": "Resource", "status": "Status", "items": "Items", "startedAt": "Started", "duration": "Duration", "actions": "Actions", "itemsProcessed": "{count} processed", "itemsCreated": "{count} created", "itemsFailed": "{count} failed", "viewDetails": "View Details"}, "syncHistoryDetail": {"title": "Sync Details", "loading": "Loading sync details...", "notFound": "Sync log not found", "backToHistory": "Back to Sync History", "errorFetchingDetails": "Error fetching sync details", "syncDetails": "Sync Details", "startedAt": "Started {0}", "processed": "Processed", "created": "Created", "updated": "Updated", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "inProgress": "In Progress", "initiatedBy": "Initiated By", "system": "System", "error": "Error", "items": "Sync Items", "itemsDescription": "Details of individual items processed in this sync.", "overview": "Overview", "success": "Success", "failed": "Failed", "noItems": "No items found", "noSuccessItems": "No successful items found", "noFailedItems": "No failed items found", "externalId": "External ID", "internalId": "Internal ID", "status": "Status", "createdAt": "Created At", "showingItems": "Showing {shown} of {total} items", "showingSuccessItems": "Showing {shown} of {total} successful items", "showingFailedItems": "Showing {shown} of {total} failed items", "itemId": "Item ID: {id}", "rawData": "Raw Data", "processedData": "Processed Data", "products": "Products", "orders": "Orders", "customers": "Customers"}}, "serviceUnavailable": {"title": "{{serviceName}} is in development", "description": "This feature is currently in development and will be available soon.", "serviceInfo": "Service Information", "inDevelopment": "{{serviceName}} is currently being developed by our team.", "estimatedAvailability": "Estimated availability: {{date}}", "interestedQuestion": "Are you interested in this feature?", "priorityMessage": "Let us know if you'd like us to prioritize developing this feature sooner.", "learnMore": "Learn More", "contactUs": "Contact Us", "emailSubject": "Integration Request"}}