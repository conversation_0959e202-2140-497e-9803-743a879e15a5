{"pageTitle": "<PERSON><PERSON><PERSON><PERSON>", "pageDescription": "<PERSON><PERSON><PERSON><PERSON> lý khách hàng và thông tin của họ", "addCustomer": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "deleteCustomer": "<PERSON><PERSON><PERSON> h<PERSON>ng", "editCustomer": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hàng", "confirmDelete": "Bạn có chắc chắn muốn xóa khách hàng này?", "deleteSuccess": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa khách hàng", "editSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t khách hàng thành công", "editError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật kh<PERSON>ch hàng", "table": {"name": "<PERSON><PERSON><PERSON>", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "orders": "<PERSON><PERSON><PERSON> hàng", "totalSpent": "T<PERSON>ng chi tiêu", "joinedDate": "<PERSON><PERSON><PERSON> tham gia", "actions": "<PERSON><PERSON>"}, "vipBadge": "VIP", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu khách hàng", "deleteDialog": {"title": "<PERSON><PERSON><PERSON> h<PERSON>ng", "description": "Bạn có chắc chắn muốn xóa khách hàng này? Hành động này không thể hoàn tác."}, "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm kh<PERSON>ch hàng...", "export": "<PERSON><PERSON><PERSON>", "loadingChart": "<PERSON><PERSON> tải biểu đồ...", "title": "<PERSON><PERSON><PERSON><PERSON>", "detail": {"title": "<PERSON> tiết kh<PERSON>ch hàng", "generalInfo": "THÔNG TIN CHUNG", "loyaltyCards": "THẺ THÀNH VIÊN", "connectedApps": "ỨNG DỤNG KẾT NỐI", "labels": "NHÃN", "attributes": "THUỘC TÍNH", "allAttributes": "TẤT CẢ THUỘC TÍNH", "recentOrders": "ĐƠN HÀNG GẦN ĐÂY", "recentActivity": "HOẠT ĐỘNG GẦN ĐÂY", "lastOrder": "<PERSON><PERSON><PERSON> hàng gần nh<PERSON>t", "signedUp": "<PERSON><PERSON><PERSON> ký {{timeAgo}}", "idNotAvailable": "Không có ID", "seeAllDeals": "XEM TẤT CẢ GIAO DỊCH", "allTime": "TẤT CẢ", "autoRefresh": "Tự động làm mới:"}, "status": {"vip": "VIP", "regular": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fields": {"name": "<PERSON><PERSON><PERSON>", "email": "Email", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "status": "<PERSON><PERSON><PERSON><PERSON> thái"}}