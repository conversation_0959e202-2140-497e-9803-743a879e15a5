{"signIn": "<PERSON><PERSON><PERSON>", "signUp": "<PERSON><PERSON><PERSON> ký", "signOut": "<PERSON><PERSON><PERSON> xu<PERSON>", "forgotPassword": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "passwordRecovery": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> mật kh<PERSON>u", "resetPassword": "Đặt lại mật khẩu", "createAccount": "<PERSON><PERSON><PERSON> t<PERSON>", "alreadyHaveAccount": "Đã có tài k<PERSON>n?", "dontHaveAccountYet": "Chưa có tài k<PERSON>n?", "forgotPasswordQuestion": "<PERSON>uên mật khẩu?", "passwordUpdated": "<PERSON><PERSON><PERSON> khẩu đã đư<PERSON><PERSON> cập nhật", "passwordResetRequested": "<PERSON><PERSON><PERSON> cầu đặt lại mật khẩu đã đượ<PERSON> gửi", "signUpHeading": "<PERSON><PERSON><PERSON> t<PERSON>", "signUpSubheading": "<PERSON><PERSON><PERSON><PERSON> thông tin bên dưới để tạo tài k<PERSON>n.", "signInHeading": "<PERSON><PERSON><PERSON>h<PERSON>p v<PERSON><PERSON> tà<PERSON>", "signInSubheading": "Chào mừng trở lại! Vui lòng nhập thông tin của bạn", "getStarted": "<PERSON><PERSON><PERSON> đ<PERSON>u", "updatePassword": "<PERSON><PERSON><PERSON> nh<PERSON>t mật kh<PERSON>u", "signingIn": "<PERSON><PERSON> đăng nhập...", "signingUp": "<PERSON><PERSON> đăng ký...", "doNotHaveAccountYet": "Chưa có tài k<PERSON>n?", "alreadyHaveAnAccount": "Đã có tài k<PERSON>n?", "signUpToAcceptInvite": "<PERSON>ui lòng đăng nhập/đăng ký để chấp nhận lời mời", "clickToAcceptAs": "<PERSON><PERSON><PERSON><PERSON> vào nút bên dưới để chấp nhận lời mời với tư cách <b>{{email}}</b>", "acceptInvite": "<PERSON><PERSON><PERSON> nh<PERSON>n lời mời", "acceptingInvite": "<PERSON><PERSON> chấp nhận lời mời...", "acceptInviteSuccess": "<PERSON><PERSON> chấp nhận lời mời thành công", "acceptInviteError": "<PERSON><PERSON> xảy ra lỗi khi chấp nhận lời mời", "acceptInviteWithDifferentAccount": "<PERSON><PERSON><PERSON> chấp nhận lời mời với tài khoản kh<PERSON>c?", "alreadyHaveAccountStatement": "Tôi đã có tài <PERSON>, tôi muốn đăng nh<PERSON>p", "doNotHaveAccountStatement": "Tôi chưa có tài <PERSON>, tôi muốn đăng ký", "signInWithProvider": "<PERSON><PERSON><PERSON> nh<PERSON>p với {{provider}}", "signInWithPhoneNumber": "<PERSON><PERSON><PERSON> nhập bằng số điện thoại", "signInWithEmail": "<PERSON><PERSON><PERSON> nhập bằng email", "signUpWithEmail": "<PERSON><PERSON><PERSON> ký bằng email", "passwordHint": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> có <PERSON>t nhất 8 ký tự", "repeatPasswordHint": "<PERSON><PERSON><PERSON><PERSON> lại mật khẩu của bạn", "repeatPassword": "<PERSON><PERSON><PERSON><PERSON> lại mật kh<PERSON>u", "passwordForgottenQuestion": "<PERSON>uên mật khẩu?", "passwordResetLabel": "Đặt lại mật khẩu", "passwordResetSubheading": "<PERSON>hậ<PERSON> địa chỉ email của bạn bên dưới. Bạn sẽ nhận được liên kết để đặt lại mật khẩu.", "passwordResetSuccessMessage": "Kiểm tra hộp thư đến của bạn! Chúng tôi đã gửi cho bạn một liên kết để đặt lại mật khẩu.", "passwordRecoveredQuestion": "<PERSON><PERSON> khôi phục mật khẩu?", "passwordLengthError": "<PERSON><PERSON> lòng cung cấp mật khẩu có ít nhất 6 ký tự", "sendEmailLink": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON> email", "sendingEmailLink": "<PERSON><PERSON> g<PERSON>i liên kết email...", "sendLinkSuccessDescription": "<PERSON><PERSON><PERSON> tra email c<PERSON><PERSON> bạn, chúng tôi vừa gửi cho bạn một liên kết. Nhấp vào liên kết để đăng nhập.", "sendLinkSuccess": "<PERSON><PERSON>g tôi đã gửi cho bạn một liên kết qua email", "sendLinkSuccessToast": "<PERSON><PERSON> g<PERSON>i liên kết thành công", "getNewLink": "<PERSON><PERSON><PERSON><PERSON> liên kết mới", "verifyCodeHeading": "<PERSON><PERSON><PERSON>h tài k<PERSON>n của bạn", "verificationCode": "<PERSON><PERSON> x<PERSON>c <PERSON>h", "verificationCodeHint": "Nhập mã chúng tôi đã gửi cho bạn qua SMS", "verificationCodeSubmitButtonLabel": "<PERSON><PERSON><PERSON> mã x<PERSON>c minh", "sendingMfaCode": "<PERSON><PERSON> gửi mã xác minh...", "verifyingMfaCode": "<PERSON><PERSON> x<PERSON>c minh mã...", "sendMfaCodeError": "<PERSON><PERSON> lỗi, chúng tôi không thể gửi cho bạn mã xác minh", "verifyMfaCodeSuccess": "Đã xác minh mã! <PERSON><PERSON> đăng nhập cho bạn...", "verifyMfaCodeError": "Rất tiếc! Có vẻ như mã không chính xác", "reauthenticate": "<PERSON><PERSON><PERSON> th<PERSON>c l<PERSON>i", "reauthenticateDescription": "<PERSON><PERSON> lý do b<PERSON><PERSON> mậ<PERSON>, chúng tôi cần bạn xác thực lại", "errorAlertHeading": "<PERSON><PERSON> lỗi, chúng tôi không thể xác thực bạn", "emailConfirmationAlertHeading": "<PERSON><PERSON><PERSON> tôi đã gửi cho bạn một email xác nhận.", "emailConfirmationAlertBody": "Chào mừng! Vui lòng kiểm tra email của bạn và nhấp vào liên kết để xác minh tài khoản của bạn.", "resendLink": "<PERSON><PERSON><PERSON> lại liên kết", "resendLinkSuccessDescription": "<PERSON>úng tôi đã gửi cho bạn một liên kết mới đến email của bạn! Nhấp vào liên kết để đăng nhập.", "resendLinkSuccess": "<PERSON><PERSON><PERSON> tra email c<PERSON>a bạn!", "authenticationErrorAlertHeading": "Lỗi xác thực", "authenticationErrorAlertBody": "<PERSON><PERSON> lỗi, chúng tôi không thể xác thực bạn. <PERSON><PERSON> lòng thử lại.", "sendEmailCode": "<PERSON><PERSON><PERSON><PERSON> mã qua email", "sendingEmailCode": "<PERSON><PERSON> gửi mã...", "resetPasswordError": "<PERSON><PERSON> lỗi, chúng tôi không thể đặt lại mật khẩu của bạn. <PERSON><PERSON> lòng thử lại.", "emailPlaceholder": "<EMAIL>", "inviteAlertHeading": "Bạn đã đư<PERSON><PERSON> mời tham gia một nhóm", "inviteAlertBody": "<PERSON>ui lòng đăng nhập hoặc đăng ký để chấp nhận lời mời và tham gia nhóm.", "acceptTermsAndConditions": "<PERSON><PERSON><PERSON> chấp nh<PERSON> <TermsOfServiceLink /> và <PrivacyPolicyLink />", "termsOfService": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> vụ", "privacyPolicy": "<PERSON><PERSON><PERSON> s<PERSON><PERSON> b<PERSON><PERSON> mật", "orContinueWith": "Hoặc tiếp tục với", "redirecting": "Bạn đã vào! Vui lòng đợi...", "errors": {"invalidCredentials": "Email hoặc mật khẩu không đúng", "emailAlreadyInUse": "<PERSON>ail này đã đư<PERSON>c sử dụng", "weakPassword": "<PERSON><PERSON><PERSON> kh<PERSON>u quá yếu", "invalidResetCode": "Mã đặt lại không hợp lệ hoặc đã hết hạn", "Invalid login credentials": "Thông tin đăng nhập không hợp lệ", "User already registered": "Thông tin này đã được sử dụng. <PERSON><PERSON> lòng thử với thông tin khác.", "Email not confirmed": "<PERSON><PERSON> lòng xác nhận địa chỉ email của bạn trước khi đăng nhập", "default": "<PERSON><PERSON>g tôi đã gặp lỗi. <PERSON><PERSON> lòng đảm bảo bạn có kết nối internet ổn định và thử lại", "generic": "<PERSON><PERSON> lỗi, chúng tôi không thể xác thực bạn. <PERSON><PERSON> lòng thử lại.", "same_password": "<PERSON><PERSON>t khẩu không thể giống với mật khẩu hiện tại", "link": "<PERSON><PERSON> lỗi, chúng tôi đã gặp lỗi khi gửi liên kết của bạn. <PERSON><PERSON> lòng thử lại.", "codeVerifierMismatch": "<PERSON><PERSON> vẻ như bạn đang cố gắng đăng nhập bằng trình duyệt khác với trình duyệt bạn đã sử dụng để yêu cầu liên kết đăng nhập. <PERSON><PERSON> lòng thử lại bằng cùng một trình duyệt.", "minPasswordLength": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự", "passwordsDoNotMatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "minPasswordNumbers": "<PERSON><PERSON><PERSON> kh<PERSON>u phải chứa ít nhất một số", "minPasswordSpecialChars": "<PERSON><PERSON>t khẩu phải chứa ít nhất một ký tự đặc biệt", "uppercasePassword": "<PERSON><PERSON><PERSON> khẩu phải chứa ít nhất một chữ cái viết hoa", "insufficient_aal": "<PERSON>ui lòng đăng nhập với xác thực đa yếu tố hiện tại của bạn để thực hiện hành động này", "otp_expired": "<PERSON><PERSON><PERSON> kết email đã hết hạn. <PERSON><PERSON> lòng thử lại."}}