/*
This file dynamically loads the appropriate billing configuration based on the NEXT_PUBLIC_BILLING_PROVIDER environment variable.
This file will never be overwritten by git updates
 */
import { BillingProviderSchema } from '@kit/billing';



import lemonSqueezyConfig from './billing.lemonsqueezy.config';
// Import all available configurations
import stripeConfig from './billing.sample.config';





// Determine which config to use based on the environment variable
let provider;

try {
  provider = BillingProviderSchema.parse(process.env.NEXT_PUBLIC_BILLING_PROVIDER);
} catch (error) {
  console.error('Invalid billing provider specified:', error);
  // Default to stripe if invalid or not specified
  provider = 'stripe';
}

// Select the appropriate configuration based on the provider
let config;
switch (provider) {
  case 'lemon-squeezy':
    config = lemonSqueezyConfig;
    break;
  case 'paddle':
    // For now, use stripe config as fallback for paddle
    // Replace with actual paddle config when available
    config = stripeConfig;
    break;
  case 'stripe':
  default:
    config = stripeConfig;
    break;
}
export default config;
