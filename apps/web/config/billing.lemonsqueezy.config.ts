import { createBillingSchema } from '@kit/billing';





const provider = 'lemon-squeezy';
export default createBillingSchema({
  provider,
  products: [
    {
      id: '481769',
      name: 'Starter',
      description: '<PERSON><PERSON>i khởi đầu hoàn hảo',
      currency: 'VND',
      badge: '<PERSON><PERSON> biến',
      plans: [
        {
          name: '<PERSON><PERSON> Tháng',
          id: 'starter-monthly',
          paymentType: 'recurring',
          interval: 'month',
          trialDays: 14,
          lineItems: [
            {
              id: '747402', // Replace with your actual Lemon Squeezy variant ID
              name: '<PERSON>er Tháng',
              cost: 199000,
              type: 'flat',
              metadata: { teams_limit: -1, zns_limit: 50, products_limit: 20, branches_limit: 3, miniapps_limit: 1, integrations_limit: 1 },
            },
          ],
        },
        {
          name: 'Starter Năm',
          id: 'starter-yearly',
          paymentType: 'recurring',
          interval: 'year',
          trialDays: 14,
          lineItems: [
            {
              id: '747405',
              name: '<PERSON><PERSON>ă<PERSON>',
              cost: 1990000,
              type: 'flat',
              metadata: { teams_limit: -1, zns_limit: 600, products_limit: 20, branches_limit: 3, miniapps_limit: 1, integrations_limit: 1 },
            },
          ],
        },
      ],
      features: [
        'billing:features.zns_messages_50',
        'billing:features.products_20',
        'billing:features.branches_3',
        'billing:features.private_theme_1',
        'billing:features.integrations_1',
        'billing:features.private_oa_support',
        'billing:features.unlimited_teams',
        'Sử dụng 1 mini app',

      ],
    },
    {
      id: '481793',
      name: 'Pro',
      badge: 'Tiết kiệm',
      highlighted: true,
      description: 'Dành cho người dùng chuyên nghiệp',
      currency: 'VND',
      plans: [
        {
          name: 'Pro Tháng',
          id: 'pro-monthly',
          paymentType: 'recurring',
          interval: 'month',
          trialDays: 14,
          lineItems: [
            {
              id: '747450', // Replace with your actual Lemon Squeezy variant ID
              name: 'Pro Tháng',
              cost: 399000,
              type: 'flat',
              metadata: { teams_limit: -1, zns_limit: 150, products_limit: 100, branches_limit: 10, miniapps_limit: 3, integrations_limit: 5 },
            },
          ],
        },
        {
          name: 'Pro Năm',
          id: 'pro-yearly',
          paymentType: 'recurring',
          interval: 'year',
          trialDays: 14,
          lineItems: [
            {
              id: '747451', // Replace with your actual Lemon Squeezy variant ID
              name: 'Pro Năm',
              cost: 3990000,
              type: 'flat',
              metadata: { teams_limit: -1, zns_limit: 1800, products_limit: 100, branches_limit: 10, miniapps_limit: 3, integrations_limit: 5 },
            },
          ],
        },
      ],
      features: [
        'billing:features.zns_messages_150',
        'billing:features.products_100',
        'billing:features.branches_10',
        'billing:features.private_theme_5',
        'billing:features.integrations_5',
        'billing:features.private_oa_support',
        'billing:features.unlimited_teams',
      ],
    },
    {
      id: '481769',
      name: 'Enterprise',
      description: 'Dành cho doanh nghiệp lớn',
      currency: 'VND',
      plans: [
        {
          name: 'Enterprise Tháng',
          id: 'enterprise-monthly',
          paymentType: 'recurring',
          interval: 'month',
          trialDays: 14,
          lineItems: [
            {
              id: '747447', // Replace with your actual Lemon Squeezy variant ID
              name: 'Enterprise Tháng',
              cost: 599000,
              type: 'flat',
              metadata: { teams_limit: -1, zns_limit: 500, products_limit: -1, branches_limit: -1, miniapps_limit: 10, integrations_limit: -1 }, // -1 indicates unlimited
            },
          ],
        },
        {
          name: 'Enterprise Năm',
          id: 'enterprise-yearly',
          paymentType: 'recurring',
          interval: 'year',
          trialDays: 14,
          lineItems: [
            {
              id: '747448', // Replace with your actual Lemon Squeezy variant ID
              name: 'Enterprise Năm',
              cost: 5990000,
              type: 'flat',
              metadata: { teams_limit: -1, zns_limit: 6000, products_limit: -1, branches_limit: -1, miniapps_limit: 10, integrations_limit: -1 }, // -1 indicates unlimited
            },
          ],
        },
      ],
      features: [
        'billing:features.zns_messages_500',
        'billing:features.unlimited_products',
        'billing:features.unlimited_branches',
        'billing:features.unlimited_private_themes',
        'billing:features.unlimited_integrations',
        'billing:features.priority_support',
        'billing:features.private_oa_support',
        'billing:features.unlimited_teams',
      ],
    },
  ],
});
