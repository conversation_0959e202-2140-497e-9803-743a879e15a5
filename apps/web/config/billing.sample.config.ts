import { createBillingSchema } from '@kit/billing';





const provider = 'stripe';

export default createBillingSchema({
  provider,
  products: [
    {
      id: 'free',
      name: 'Free',
      description: 'Get started for free',
      currency: 'USD',
      plans: [
        {
          name: 'Free Plan',
          id: 'free-plan',
          paymentType: 'one-time',
          lineItems: [
            {
              id: 'price_1R91Wi009TeS9Nj4i6cbWn8W',
              // id: 'free_plan_item', // ID nội bộ, không cần Price từ Stripe
              name: 'Free Plan',
              cost: 0,
              type: 'flat',
              metadata: { teams_limit: 2, zns_limit: 10, miniapp_limit: 0 },
            },
          ],
        },
      ],
      features: ['5 products', '1 branch', 'Default theme only', 'No Private OA Support', '2 teams max', 'No mini apps'],
    },
    {
      id: 'starter',
      name: 'Starter',
      description: 'The perfect plan to get started',
      currency: 'USD',
      badge: 'Value',
      plans: [
        {
          name: 'Starter Monthly',
          id: 'starter-monthly',
          paymentType: 'recurring',
          interval: 'month',
          lineItems: [
            {
              id: 'price_1R2TcG009TeS9Nj4FjdTJtNg',
              name: 'Starter',
              cost: 9.99,
              type: 'per_seat',
              metadata: { teams_limit: 2, miniapp_limit: 1 },
              tiers: [
                { cost: 0, upTo: 2 },
              ],
            },
            {
              id: 'price_1R9O3E009TeS9Nj4LKhlXLUe',
              name: 'ZNS Messages',
              cost: 0,
              type: 'metered',
              unit: 'message',
              metadata: { zns_limit: 50 },
              tiers: [
                { cost: 0, upTo: 50 },
                { cost: 0.01, upTo: 'unlimited' },
              ],
            },
          ],
        },
        {
          name: 'Starter Yearly',
          id: 'starter-yearly',
          paymentType: 'recurring',
          interval: 'year',
          lineItems: [
            {
              id: 'price_1R2Td0009TeS9Nj4fU0n3SUJ',
              name: 'Base',
              cost: 99.99,
              type: 'flat',
              metadata: { teams_limit: 5, miniapp_limit: 1 },
            },
            {
              id: 'zns_usage_starter_yearly',
              name: 'ZNS Messages',
              cost: 0,
              type: 'metered',
              unit: 'message',
              metadata: { zns_limit: 600 },
              tiers: [
                { cost: 0, upTo: 600 },
                { cost: 0.01, upTo: 'unlimited' },
              ],
            },
          ],
        },
      ],
      features: [
        '50 ZNS messages/month (600/year)',
        '20 products',
        '3 branches',
        '1 private theme',
        '1 mini app',
        'Basic integrations',
        'Private OA Support',
        '5 teams max',
      ],
    },
    {
      id: 'pro',
      name: 'Pro',
      badge: 'Popular',
      highlighted: true,
      description: 'The perfect plan for professionals',
      currency: 'USD',
      plans: [
        {
          name: 'Pro Monthly',
          id: 'pro-monthly',
          paymentType: 'recurring',
          interval: 'month',
          lineItems: [
            {
              id: 'price_1R2TeH009TeS9Nj49YpdFjSD',
              name: 'Base',
              cost: 19.99,
              type: 'flat',
              metadata: { teams_limit: 10, miniapp_limit: 3 },
            },
            {
              id: 'zns_usage_pro_monthly',
              name: 'ZNS Messages',
              cost: 0,
              type: 'metered',
              unit: 'message',
              metadata: { zns_limit: 150 },
              tiers: [
                { cost: 0, upTo: 150 },
                { cost: 0.01, upTo: 'unlimited' },
              ],
            },
          ],
        },
        {
          name: 'Pro Yearly',
          id: 'pro-yearly',
          paymentType: 'recurring',
          interval: 'year',
          lineItems: [
            {
              id: 'price_1R2Tev009TeS9Nj4ZpFDYkTj',
              name: 'Base',
              cost: 199.99,
              type: 'flat',
              metadata: { teams_limit: 10, miniapp_limit: 3 },
            },
            {
              id: 'zns_usage_pro_yearly',
              name: 'ZNS Messages',
              cost: 0,
              type: 'metered',
              unit: 'message',
              metadata: { zns_limit: 1800 },
              tiers: [
                { cost: 0, upTo: 1800 },
                { cost: 0.01, upTo: 'unlimited' },
              ],
            },
          ],
        },
      ],
      features: [
        '150 ZNS messages/month (1800/year)',
        '100 products',
        '10 branches',
        '5 private themes',
        '3 mini apps',
        'Advanced integrations',
        'Private OA Support',
        '10 teams max',
      ],
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      description: 'The perfect plan for enterprises',
      currency: 'USD',
      plans: [
        {
          name: 'Enterprise Monthly',
          id: 'enterprise-monthly',
          paymentType: 'recurring',
          interval: 'month',
          lineItems: [
            {
              id: 'price_1R2Tgg009TeS9Nj4vHGfRSuk',
              name: 'Base',
              cost: 29.99,
              type: 'flat',
              metadata: { teams_limit: Infinity, miniapp_limit: -1 },
            },
            {
              id: 'zns_usage_enterprise_monthly',
              name: 'ZNS Messages',
              cost: 0,
              type: 'metered',
              unit: 'message',
              metadata: { zns_limit: 500 },
              tiers: [
                { cost: 0, upTo: 500 },
                { cost: 0.01, upTo: 'unlimited' },
              ],
            },
          ],
        },
        {
          name: 'Enterprise Yearly',
          id: 'enterprise-yearly',
          paymentType: 'recurring',
          interval: 'year',
          lineItems: [
            {
              id: 'price_1R2ThK009TeS9Nj4bZxJZdDv',
              name: 'Base',
              cost: 299.9,
              type: 'flat',
              metadata: { teams_limit: Infinity, miniapp_limit: -1 },
            },
            {
              id: 'zns_usage_enterprise_yearly',
              name: 'ZNS Messages',
              cost: 0,
              type: 'metered',
              unit: 'message',
              metadata: { zns_limit: 6000 },
              tiers: [
                { cost: 0, upTo: 6000 },
                { cost: 0.01, upTo: 'unlimited' },
              ],
            },
          ],
        },
      ],
      features: [
        '500 ZNS messages/month (6000/year)',
        'Unlimited products',
        'Unlimited branches',
        'Unlimited private themes',
        'Unlimited mini apps',
        'All integrations',
        'Priority support',
        'Private OA Support',
        'Unlimited teams',
      ],
    },
  ],
});
