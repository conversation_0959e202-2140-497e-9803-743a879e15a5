export interface EducationMockupConfig {
  organizationType: string;
  organizationName: string;
  directorName: string;
  address: string;
  phone: string;
  email: string;
  programs: Array<{
    name: string;
    type: string;
    ageRange: string;
    capacity: number;
    description: string;
    location: string;
    schedule: Record<string, any>;
    feeStructure: Record<string, any>;
  }>;
  sampleLearners: Array<{
    fullName: string;
    nickname: string;
    dateOfBirth: string;
    gender: string;
    healthInfo: Record<string, any>;
    emergencyContact: Record<string, any>;
  }>;
  sampleGuardians: Array<{
    fullName: string;
    phone: string;
    email: string;
    relationship: string;
    occupation: string;
    isPrimary: boolean;
  }>;
  feeStructure: Array<{
    category: string;
    amount: number;
    billingPeriod: string;
    description: string;
  }>;
  sampleEvents: Array<{
    title: string;
    description: string;
    eventType: string;
    targetAudience: string;
    registrationRequired: boolean;
  }>;
}

// Kindergarten Template
export const KINDERGARTEN_MOCKUP: EducationMockupConfig = {
  organizationType: 'kindergarten',
  organizationName: '<PERSON>ầ<PERSON>',
  directorName: '<PERSON><PERSON>',
  address: '123 Đường Hoa Mai, Quận 1, TP.HCM',
  phone: '**********',
  email: '<EMAIL>',
  programs: [
    {
      name: 'Lớp Chồi (3-4 tuổi)',
      type: 'regular_class',
      ageRange: '3-4 tuổi',
      capacity: 15,
      description: 'Lớp học dành cho trẻ 3-4 tuổi, tập trung phát triển kỹ năng cơ bản',
      location: 'Phòng A1',
      schedule: {
        monday: { start: '07:30', end: '16:30' },
        tuesday: { start: '07:30', end: '16:30' },
        wednesday: { start: '07:30', end: '16:30' },
        thursday: { start: '07:30', end: '16:30' },
        friday: { start: '07:30', end: '16:30' }
      },
      feeStructure: {
        tuition: 2500000,
        meal: 800000,
        activities: 200000
      }
    },
    {
      name: 'Lớp Lá (4-5 tuổi)',
      type: 'regular_class',
      ageRange: '4-5 tuổi',
      capacity: 18,
      description: 'Lớp học dành cho trẻ 4-5 tuổi, chuẩn bị cho bậc tiểu học',
      location: 'Phòng B1',
      schedule: {
        monday: { start: '07:30', end: '16:30' },
        tuesday: { start: '07:30', end: '16:30' },
        wednesday: { start: '07:30', end: '16:30' },
        thursday: { start: '07:30', end: '16:30' },
        friday: { start: '07:30', end: '16:30' }
      },
      feeStructure: {
        tuition: 2800000,
        meal: 800000,
        activities: 300000
      }
    },
    {
      name: 'Lớp Hoa (5-6 tuổi)',
      type: 'regular_class',
      ageRange: '5-6 tuổi',
      capacity: 20,
      description: 'Lớp học dành cho trẻ 5-6 tuổi, chuẩn bị vào lớp 1',
      location: 'Phòng C1',
      schedule: {
        monday: { start: '07:30', end: '16:30' },
        tuesday: { start: '07:30', end: '16:30' },
        wednesday: { start: '07:30', end: '16:30' },
        thursday: { start: '07:30', end: '16:30' },
        friday: { start: '07:30', end: '16:30' }
      },
      feeStructure: {
        tuition: 3000000,
        meal: 800000,
        activities: 400000
      }
    }
  ],
  sampleLearners: [
    {
      fullName: 'Nguyễn Minh An',
      nickname: 'Bé An',
      dateOfBirth: '2020-03-15',
      gender: 'male',
      healthInfo: {
        allergies: [],
        medications: [],
        specialNeeds: '',
        bloodType: 'O'
      },
      emergencyContact: {
        name: 'Nguyễn Văn Hùng',
        phone: '**********',
        relationship: 'father'
      }
    },
    {
      fullName: 'Trần Thị Bảo',
      nickname: 'Bé Bảo',
      dateOfBirth: '2019-08-22',
      gender: 'female',
      healthInfo: {
        allergies: ['đậu phộng'],
        medications: [],
        specialNeeds: '',
        bloodType: 'A'
      },
      emergencyContact: {
        name: 'Trần Thị Mai',
        phone: '**********',
        relationship: 'mother'
      }
    },
    {
      fullName: 'Lê Văn Cường',
      nickname: 'Bé Cường',
      dateOfBirth: '2020-11-10',
      gender: 'male',
      healthInfo: {
        allergies: [],
        medications: [],
        specialNeeds: 'Cần hỗ trợ thêm trong giao tiếp',
        bloodType: 'B'
      },
      emergencyContact: {
        name: 'Lê Thị Hoa',
        phone: '**********',
        relationship: 'mother'
      }
    }
  ],
  sampleGuardians: [
    {
      fullName: 'Nguyễn Văn Hùng',
      phone: '**********',
      email: '<EMAIL>',
      relationship: 'father',
      occupation: 'Kỹ sư phần mềm',
      isPrimary: true
    },
    {
      fullName: 'Nguyễn Thị Linh',
      phone: '**********',
      email: '<EMAIL>',
      relationship: 'mother',
      occupation: 'Giáo viên',
      isPrimary: false
    },
    {
      fullName: 'Trần Thị Mai',
      phone: '**********',
      email: '<EMAIL>',
      relationship: 'mother',
      occupation: 'Bác sĩ',
      isPrimary: true
    },
    {
      fullName: 'Lê Thị Hoa',
      phone: '**********',
      email: '<EMAIL>',
      relationship: 'mother',
      occupation: 'Kế toán',
      isPrimary: true
    }
  ],
  feeStructure: [
    {
      category: 'tuition',
      amount: 2500000,
      billingPeriod: 'monthly',
      description: 'Học phí hàng tháng'
    },
    {
      category: 'meal',
      amount: 800000,
      billingPeriod: 'monthly',
      description: 'Tiền ăn hàng tháng'
    },
    {
      category: 'material',
      amount: 200000,
      billingPeriod: 'semester',
      description: 'Tiền đồ dùng học tập'
    },
    {
      category: 'activity',
      amount: 300000,
      billingPeriod: 'monthly',
      description: 'Hoạt động ngoại khóa'
    },
    {
      category: 'transport',
      amount: 500000,
      billingPeriod: 'monthly',
      description: 'Xe đưa đón (tùy chọn)'
    }
  ],
  sampleEvents: [
    {
      title: 'Ngày hội gia đình',
      description: 'Sự kiện giao lưu giữa gia đình và nhà trường',
      eventType: 'family_day',
      targetAudience: 'all',
      registrationRequired: true
    },
    {
      title: 'Biểu diễn văn nghệ cuối năm',
      description: 'Chương trình biểu diễn của các bé',
      eventType: 'performance',
      targetAudience: 'all',
      registrationRequired: false
    },
    {
      title: 'Họp phụ huynh đầu năm',
      description: 'Họp thông tin chương trình học năm mới',
      eventType: 'meeting',
      targetAudience: 'guardians',
      registrationRequired: true
    }
  ]
};

// Talent Center Template
export const TALENT_CENTER_MOCKUP: EducationMockupConfig = {
  organizationType: 'talent_center',
  organizationName: 'Trung Tâm Năng Khiếu Sao Mai',
  directorName: 'Thầy Phạm Văn Đức',
  address: '456 Đường Sao Mai, Quận 3, TP.HCM',
  phone: '0283456790',
  email: '<EMAIL>',
  programs: [
    {
      name: 'Piano Cơ Bản',
      type: 'music_program',
      ageRange: '6-12 tuổi',
      capacity: 8,
      description: 'Khóa học piano cơ bản cho trẻ em',
      location: 'Phòng nhạc 1',
      schedule: {
        saturday: { start: '08:00', end: '10:00' },
        sunday: { start: '08:00', end: '10:00' }
      },
      feeStructure: {
        tuition: 1200000,
        material: 200000
      }
    },
    {
      name: 'Vẽ Tranh Thiếu Nhi',
      type: 'art_program',
      ageRange: '5-15 tuổi',
      capacity: 12,
      description: 'Khóa học vẽ tranh cho trẻ em và thiếu niên',
      location: 'Phòng mỹ thuật',
      schedule: {
        saturday: { start: '14:00', end: '16:00' },
        sunday: { start: '14:00', end: '16:00' }
      },
      feeStructure: {
        tuition: 800000,
        material: 300000
      }
    }
  ],
  sampleLearners: [
    {
      fullName: 'Hoàng Minh Tuấn',
      nickname: 'Tuấn',
      dateOfBirth: '2015-05-20',
      gender: 'male',
      healthInfo: {
        allergies: [],
        medications: [],
        specialNeeds: '',
        bloodType: 'AB'
      },
      emergencyContact: {
        name: 'Hoàng Văn Nam',
        phone: '**********',
        relationship: 'father'
      }
    }
  ],
  sampleGuardians: [
    {
      fullName: 'Hoàng Văn Nam',
      phone: '**********',
      email: '<EMAIL>',
      relationship: 'father',
      occupation: 'Kiến trúc sư',
      isPrimary: true
    }
  ],
  feeStructure: [
    {
      category: 'tuition',
      amount: 1000000,
      billingPeriod: 'monthly',
      description: 'Học phí khóa học'
    },
    {
      category: 'material',
      amount: 250000,
      billingPeriod: 'semester',
      description: 'Đồ dùng học tập'
    }
  ],
  sampleEvents: [
    {
      title: 'Triển lãm tranh học viên',
      description: 'Trưng bày tác phẩm của các học viên',
      eventType: 'exhibition',
      targetAudience: 'all',
      registrationRequired: false
    }
  ]
};

// Export default config based on environment or selection
export const getEducationMockupConfig = (type: 'kindergarten' | 'talent_center' = 'kindergarten'): EducationMockupConfig => {
  switch (type) {
    case 'talent_center':
      return TALENT_CENTER_MOCKUP;
    case 'kindergarten':
    default:
      return KINDERGARTEN_MOCKUP;
  }
};
