export const sampleDataTemplates = {
    'food-beverage': {
        account: {
            name: '<PERSON><PERSON><PERSON> hàng Ẩm thực Việt',
            slug: 'am-thuc-viet',
            email: '<EMAIL>',
            is_personal_account: false,
            picture_url: '/images/mockup/food-beverage/am-thuc-viet-logo.webp',
        },
        memberships: [
            {user_id: 'customer-uuid-1', account_role: 'customer'},
            {user_id: 'customer-uuid-2', account_role: 'customer'},
        ],
        categories: [
            {
                name: 'Món khai vị',
                description: 'Appetizers',
                parent_id: null,
                image_url: '/images/mockup/food-beverage/mon-khai-vi.webp'
            },
            {
                name: '<PERSON><PERSON> chính',
                description: 'Main Courses',
                parent_id: null,
                image_url: '/images/mockup/food-beverage/mon-chinh.webp'
            },
            {
                name: 'Tráng miệng',
                description: 'Desserts',
                parent_id: null,
                image_url: '/images/mockup/food-beverage/trang-mieng.webp'
            },
            {
                name: '<PERSON><PERSON> uống',
                description: 'Beverages',
                parent_id: null,
                image_url: '/images/mockup/food-beverage/do-uong.webp'
            },
        ],
        products: [
            {
                name: 'Gỏi cuốn',
                category_name: 'Món khai vị',
                type: 'physical',
                price: 50000,
                compare_at_price: 60000,
                image_url: '/images/mockup/food-beverage/goi-cuon.webp',
                status: 'active',
                sku: 'GOICUON01'
            },
            {
                name: 'Chả giò',
                category_name: 'Món khai vị',
                type: 'physical',
                price: 45000,
                compare_at_price: 50000,
                image_url: '/images/mockup/food-beverage/cha-gio.webp',
                status: 'active',
                sku: 'CHAGIO01'
            },
            {
                name: 'Súp cua',
                category_name: 'Món khai vị',
                type: 'physical',
                price: 60000,
                compare_at_price: 70000,
                image_url: '/images/mockup/food-beverage/sup-cua.webp',
                status: 'active',
                sku: 'SUPCUA01'
            },
            {
                name: 'Phở bò',
                category_name: 'Món chính',
                type: 'physical',
                price: 80000,
                compare_at_price: 90000,
                image_url: '/images/mockup/food-beverage/pho-bo.webp',
                status: 'active',
                sku: 'PHOBO01'
            },
            {
                name: 'Cơm tấm',
                category_name: 'Món chính',
                type: 'physical',
                price: 65000,
                compare_at_price: 75000,
                image_url: '/images/mockup/food-beverage/com-tam.webp',
                status: 'active',
                sku: 'COMTAM01'
            },
            {
                name: 'Bún bò Huế',
                category_name: 'Món chính',
                type: 'physical',
                price: 85000,
                compare_at_price: 95000,
                image_url: '/images/mockup/food-beverage/bun-bo-hue.webp',
                status: 'active',
                sku: 'BUNBO01'
            },
            {
                name: 'Gà nướng',
                category_name: 'Món chính',
                type: 'physical',
                price: 120000,
                compare_at_price: 140000,
                image_url: '/images/mockup/food-beverage/ga-nuong.webp',
                status: 'active',
                sku: 'GANUONG01'
            },
            {
                name: 'Cá kho tộ',
                category_name: 'Món chính',
                type: 'physical',
                price: 90000,
                compare_at_price: 100000,
                image_url: '/images/mockup/food-beverage/ca-kho-to.webp',
                status: 'active',
                sku: 'CAKHO01'
            },
            {
                name: 'Chè ba màu',
                category_name: 'Tráng miệng',
                type: 'physical',
                price: 30000,
                compare_at_price: 35000,
                image_url: '/images/mockup/food-beverage/che-ba-mau.webp',
                status: 'active',
                sku: 'CHE01'
            },
            {
                name: 'Bánh flan',
                category_name: 'Tráng miệng',
                type: 'physical',
                price: 25000,
                compare_at_price: 30000,
                image_url: '/images/mockup/food-beverage/banh-flan.webp',
                status: 'active',
                sku: 'FLAN01'
            },
            {
                name: 'Kem dâu',
                category_name: 'Tráng miệng',
                type: 'physical',
                price: 35000,
                compare_at_price: 40000,
                image_url: '/images/mockup/food-beverage/kem-dau.webp',
                status: 'active',
                sku: 'KEMDAU01'
            },
            {
                name: 'Nước mía',
                category_name: 'Đồ uống',
                type: 'physical',
                price: 20000,
                compare_at_price: 25000,
                image_url: '/images/mockup/food-beverage/nuoc-mia.webp',
                status: 'active',
                sku: 'NUOCMIA01'
            },
            {
                name: 'Trà sữa',
                category_name: 'Đồ uống',
                type: 'physical',
                price: 35000,
                compare_at_price: 40000,
                image_url: '/images/mockup/food-beverage/tra-sua.webp',
                status: 'active',
                sku: 'TRASUA01'
            },
            {
                name: 'Cà phê sữa',
                category_name: 'Đồ uống',
                type: 'physical',
                price: 30000,
                compare_at_price: 35000,
                image_url: '/images/mockup/food-beverage/ca-phe-sua.webp',
                status: 'active',
                sku: 'CAPHESUA01'
            },
            {
                name: 'Nước cam',
                category_name: 'Đồ uống',
                type: 'physical',
                price: 40000,
                compare_at_price: 45000,
                image_url: '/images/mockup/food-beverage/nuoc-cam.webp',
                status: 'active',
                sku: 'NUOCCAM01'
            },
        ],
        branches: [
            {name: 'Chi nhánh Hà Nội', address: '123 Đường Láng, Hà Nội', phone: '0909123456', is_active: true},
        ],
        branch_products: [
            {product_name: 'Gỏi cuốn', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Chả giò', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Súp cua', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Phở bò', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Cơm tấm', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Bún bò Huế', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Gà nướng', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Cá kho tộ', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Chè ba màu', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Bánh flan', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Kem dâu', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Nước mía', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Trà sữa', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Cà phê sữa', branch_name: 'Chi nhánh Hà Nội', is_active: true},
            {product_name: 'Nước cam', branch_name: 'Chi nhánh Hà Nội', is_active: true},
        ],
        customer_orders: [
            {
                customer_id: 'customer-uuid-1',
                branch_name: 'Chi nhánh Hà Nội',
                total_amount: 165000,
                payment_method: 'zalopay',
                status: 'completed',
                items: [
                    {product_name: 'Gỏi cuốn', quantity: 1, price: 50000},
                    {product_name: 'Phở bò', quantity: 1, price: 80000},
                    {product_name: 'Nước mía', quantity: 1, price: 25000},
                ],
            },
            {
                customer_id: 'customer-uuid-2',
                branch_name: 'Chi nhánh Hà Nội',
                total_amount: 145000,
                payment_method: 'cash',
                status: 'pending',
                items: [
                    {product_name: 'Cơm tấm', quantity: 1, price: 65000},
                    {product_name: 'Trà sữa', quantity: 2, price: 35000},
                ],
            },
        ],
        account_themes: [
            {
                theme_id: '********-0000-4000-a000-********0001',
                theme_name: 'F&B Theme',
                mini_app_id: '3423472366583461276',
                primary_color: '#FF5733',
                secondary_color: '#FFC107',
                logo_url: '/images/mockup/food-beverage/am-thuc-viet-logo.webp'
            },
        ],
        flash_sales: [
            {
                name: 'Khuyến mãi mùa hè',
                description: 'Giảm giá đặc biệt cho mùa hè',
                start_time: new Date().toISOString(),
                end_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
                status: 'active',
                products: [
                    { product_name: 'Gỏi cuốn', discount_percentage: 30, quantity_limit: 50 },
                    { product_name: 'Phở bò', discount_percentage: 20, quantity_limit: 30 },
                    { product_name: 'Nước mía', discount_percentage: 15, quantity_limit: 100 }
                ]
            }
        ],
        vouchers: [
            {
                code: 'SUMMER2025',
                name: 'Khuyến mãi hè 2025',
                description: 'Giảm 20% cho đơn hàng từ 100.000đ',
                discount_type: 'percentage',
                discount_value: 20,
                min_order_value: 100000,
                max_discount_value: 50000,
                max_uses: 100,
                start_date: new Date().toISOString(),
                end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
                status: 'active',
                // Advanced restrictions
                usage_limit_per_customer: 2,
                first_time_customers_only: false
            },
            {
                code: 'WELCOME50K',
                name: 'Chào mừng khách hàng mới',
                description: 'Giảm 50.000đ cho đơn hàng đầu tiên',
                discount_type: 'fixed',
                discount_value: 50000,
                min_order_value: 200000,
                max_uses: 1,
                start_date: new Date().toISOString(),
                end_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days from now
                status: 'active',
                // Advanced restrictions
                first_time_customers_only: true,
                usage_limit_per_customer: 1
            }
        ],
    },
    'retail-fashion': {
        account: {
            name: 'Thời trang XYZ',
            slug: 'thoi-trang-xyz',
            email: '<EMAIL>',
            is_personal_account: false,
            picture_url: '/images/mockup/retail-fashion/retail-fashion-logo.webp',
        },
        memberships: [
            {user_id: 'customer-uuid-3', account_role: 'customer'},
            {user_id: 'customer-uuid-4', account_role: 'customer'},
        ],
        categories: [
            {name: 'Áo', description: 'Tops', parent_id: null, image_url: '/images/mockup/retail-fashion/ao.webp'},
            {
                name: 'Quần',
                description: 'Bottoms',
                parent_id: null,
                image_url: '/images/mockup/retail-fashion/quan.webp'
            },
            {
                name: 'Phụ kiện',
                description: 'Accessories',
                parent_id: null,
                image_url: '/images/mockup/retail-fashion/phu-kien.webp'
            },
        ],
        products: [
            {
                name: 'Áo thun trắng',
                category_name: 'Áo',
                type: 'physical',
                price: 150000,
                compare_at_price: 180000,
                image_url: '/images/mockup/retail-fashion/ao-thun-trang.webp',
                status: 'active',
                sku: 'AOTHUN01'
            },
            {
                name: 'Áo sơ mi',
                category_name: 'Áo',
                type: 'physical',
                price: 250000,
                compare_at_price: 300000,
                image_url: '/images/mockup/retail-fashion/ao-so-mi.webp',
                status: 'active',
                sku: 'AOSOMI01'
            },
            {
                name: 'Áo khoác denim',
                category_name: 'Áo',
                type: 'physical',
                price: 450000,
                compare_at_price: 500000,
                image_url: '/images/mockup/retail-fashion/ao-khoac-denim.webp',
                status: 'active',
                sku: 'DENIM01'
            },
            {
                name: 'Áo len',
                category_name: 'Áo',
                type: 'physical',
                price: 300000,
                compare_at_price: 350000,
                image_url: '/images/mockup/retail-fashion/ao-len.webp',
                status: 'active',
                sku: 'AOLEN01'
            },
            {
                name: 'Áo hoodie',
                category_name: 'Áo',
                type: 'physical',
                price: 400000,
                compare_at_price: 450000,
                image_url: '/images/mockup/retail-fashion/ao-hoodie.webp',
                status: 'active',
                sku: 'HOODIE01'
            },
            {
                name: 'Quần jeans',
                category_name: 'Quần',
                type: 'physical',
                price: 350000,
                compare_at_price: 400000,
                image_url: '/images/mockup/retail-fashion/quan-jeans.webp',
                status: 'active',
                sku: 'JEANS01'
            },
            {
                name: 'Quần short',
                category_name: 'Quần',
                type: 'physical',
                price: 200000,
                compare_at_price: 250000,
                image_url: '/images/mockup/retail-fashion/quan-short.webp',
                status: 'active',
                sku: 'SHORT01'
            },
            {
                name: 'Quần jogger',
                category_name: 'Quần',
                type: 'physical',
                price: 300000,
                compare_at_price: 350000,
                image_url: '/images/mockup/retail-fashion/quan-jogger.webp',
                status: 'active',
                sku: 'JOGGER01'
            },
            {
                name: 'Quần kaki',
                category_name: 'Quần',
                type: 'physical',
                price: 280000,
                compare_at_price: 320000,
                image_url: '/images/mockup/retail-fashion/quan-kaki.webp',
                status: 'active',
                sku: 'KAKI01'
            },
            {
                name: 'Quần ống suông',
                category_name: 'Quần',
                type: 'physical',
                price: 250000,
                compare_at_price: 300000,
                image_url: '/images/mockup/retail-fashion/quan-ong-suong.webp',
                status: 'active',
                sku: 'SUONG01'
            },
            {
                name: 'Mũ lưỡi trai',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 100000,
                compare_at_price: 120000,
                image_url: '/images/mockup/retail-fashion/mu-luoi-trai.webp',
                status: 'active',
                sku: 'MU01'
            },
            {
                name: 'Túi xách',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 500000,
                compare_at_price: 600000,
                image_url: '/images/mockup/retail-fashion/tui-xach.webp',
                status: 'active',
                sku: 'TUI01'
            },
            {
                name: 'Dây nịt',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 150000,
                compare_at_price: 180000,
                image_url: '/images/mockup/retail-fashion/day-nit.webp',
                status: 'active',
                sku: 'DAYNIT01'
            },
            {
                name: 'Kính râm',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 200000,
                compare_at_price: 250000,
                image_url: '/images/mockup/retail-fashion/kinh-ram.webp',
                status: 'active',
                sku: 'KINH01'
            },
            {
                name: 'Khăn choàng',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 120000,
                compare_at_price: 150000,
                image_url: '/images/mockup/retail-fashion/khan-choang.webp',
                status: 'active',
                sku: 'KHAN01'
            },
        ],
        branches: [
            {name: 'Cửa hàng TP.HCM', address: '456 Lê Lợi, TP.HCM', phone: '0909876543', is_active: true},
        ],
        branch_products: [
            {product_name: 'Áo thun trắng', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Áo sơ mi', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Áo khoác denim', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Áo len', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Áo hoodie', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Quần jeans', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Quần short', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Quần jogger', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Quần kaki', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Quần ống suông', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Mũ lưỡi trai', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Túi xách', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Dây nịt', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Kính râm', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Khăn choàng', branch_name: 'Cửa hàng TP.HCM', is_active: true},
        ],
        customer_orders: [
            {
                customer_id: 'customer-uuid-3',
                branch_name: 'Cửa hàng TP.HCM',
                total_amount: 600000,
                payment_method: 'zalopay',
                status: 'completed',
                items: [
                    {product_name: 'Áo thun trắng', quantity: 1, price: 150000},
                    {product_name: 'Quần jeans', quantity: 1, price: 350000},
                    {product_name: 'Mũ lưỡi trai', quantity: 1, price: 100000},
                ],
            },
        ],
        account_themes: [
            {
                theme_id: '********-0000-4000-a000-********0001',
                theme_name: 'Fashion Theme',
                mini_app_id: '3423472366583461276',
                primary_color: '#28A745',
                secondary_color: '#17A2B8',
                logo_url: '/images/mockup/retail-fashion/xyz-logo.webp'
            },
        ],
        flash_sales: [
            {
                name: 'Flash Sale Thời Trang',
                description: 'Giảm giá sốc trong 3 ngày',
                start_time: new Date().toISOString(),
                end_time: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
                status: 'active',
                products: [
                    { product_name: 'Áo thun trắng', discount_percentage: 40, quantity_limit: 20 },
                    { product_name: 'Quần jeans', discount_percentage: 35, quantity_limit: 15 },
                    { product_name: 'Mũ lưỡi trai', discount_percentage: 50, quantity_limit: 30 }
                ]
            }
        ],
        vouchers: [
            {
                code: 'FASHION25',
                name: 'Mã giảm giá thời trang',
                description: 'Giảm 25% cho đơn hàng từ 300.000đ',
                discount_type: 'percentage',
                discount_value: 25,
                min_order_value: 300000,
                max_discount_value: 100000,
                max_uses: 50,
                start_date: new Date().toISOString(),
                end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
                status: 'active',
                // Advanced restrictions
                min_previous_orders: 1,
                usage_limit_per_customer: 3
            },
            {
                code: 'VIP2025',
                name: 'Voucher VIP',
                description: 'Giảm 30% cho khách hàng VIP',
                discount_type: 'percentage',
                discount_value: 30,
                min_order_value: 500000,
                max_discount_value: 200000,
                max_uses: 20,
                start_date: new Date().toISOString(),
                end_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(), // 60 days from now
                status: 'active',
                // Advanced restrictions
                is_customer_specific: true,
                usage_limit_per_customer: 2
            }
        ],
    },
    'services-repairs': {
        account: {
            name: 'Sửa chữa ABC',
            slug: 'sua-chua-abc',
            email: '<EMAIL>',
            is_personal_account: false,
            picture_url: '/images/mockup/services-repairs/services-repairs-logo.webp',
        },
        memberships: [
            {user_id: 'customer-uuid-5', account_role: 'customer'},
            {user_id: 'customer-uuid-6', account_role: 'customer'},
        ],
        categories: [
            {
                name: 'Sửa chữa điện tử',
                description: 'Electronics Repairs',
                parent_id: null,
                image_url: '/images/mockup/services-repairs/sua-chua-dien-tu.webp'
            },
            {
                name: 'Sửa chữa đồ gia dụng',
                description: 'Appliance Repairs',
                parent_id: null,
                image_url: '/images/mockup/services-repairs/sua-chua-do-gia-dung.webp'
            },
        ],
        products: [
            {
                name: 'Sửa điện thoại',
                category_name: 'Sửa chữa điện tử',
                type: 'service',
                price: 200000,
                compare_at_price: 250000,
                image_url: '/images/mockup/services-repairs/sua-dien-thoai.webp',
                status: 'active',
                sku: 'SUADT01'
            },
            {
                name: 'Sửa laptop',
                category_name: 'Sửa chữa điện tử',
                type: 'service',
                price: 500000,
                compare_at_price: 600000,
                image_url: '/images/mockup/services-repairs/sua-laptop.webp',
                status: 'active',
                sku: 'SUALAP01'
            },
            {
                name: 'Sửa tivi',
                category_name: 'Sửa chữa điện tử',
                type: 'service',
                price: 300000,
                compare_at_price: 350000,
                image_url: '/images/mockup/services-repairs/sua-tivi.webp',
                status: 'active',
                sku: 'SUATV01'
            },
            {
                name: 'Sửa máy ảnh',
                category_name: 'Sửa chữa điện tử',
                type: 'service',
                price: 400000,
                compare_at_price: 450000,
                image_url: '/images/mockup/services-repairs/sua-may-anh.webp',
                status: 'active',
                sku: 'SUAMA01'
            },
            {
                name: 'Sửa loa',
                category_name: 'Sửa chữa điện tử',
                type: 'service',
                price: 150000,
                compare_at_price: 180000,
                image_url: '/images/mockup/services-repairs/sua-loa.webp',
                status: 'active',
                sku: 'SUALOA01'
            },
            {
                name: 'Sửa tủ lạnh',
                category_name: 'Sửa chữa đồ gia dụng',
                type: 'service',
                price: 400000,
                compare_at_price: 450000,
                image_url: '/images/mockup/services-repairs/sua-tu-lanh.webp',
                status: 'active',
                sku: 'SUATL01'
            },
            {
                name: 'Sửa máy giặt',
                category_name: 'Sửa chữa đồ gia dụng',
                type: 'service',
                price: 350000,
                compare_at_price: 400000,
                image_url: '/images/mockup/services-repairs/sua-may-giat.webp',
                status: 'active',
                sku: 'SUAMG01'
            },
            {
                name: 'Sửa lò vi sóng',
                category_name: 'Sửa chữa đồ gia dụng',
                type: 'service',
                price: 200000,
                compare_at_price: 250000,
                image_url: '/images/mockup/services-repairs/sua-lo-vi-song.webp',
                status: 'active',
                sku: 'SUALVS01'
            },
            {
                name: 'Sửa máy hút bụi',
                category_name: 'Sửa chữa đồ gia dụng',
                type: 'service',
                price: 250000,
                compare_at_price: 300000,
                image_url: '/images/mockup/services-repairs/sua-may-hut-bui.webp',
                status: 'active',
                sku: 'SUAMHB01'
            },
            {
                name: 'Sửa quạt điện',
                category_name: 'Sửa chữa đồ gia dụng',
                type: 'service',
                price: 100000,
                compare_at_price: 120000,
                image_url: '/images/mockup/services-repairs/sua-quat-dien.webp',
                status: 'active',
                sku: 'SUAQUAT01'
            },
            {
                name: 'Sửa máy in',
                category_name: 'Sửa chữa điện tử',
                type: 'service',
                price: 300000,
                compare_at_price: 350000,
                image_url: '/images/mockup/services-repairs/sua-may-in.webp',
                status: 'active',
                sku: 'SUAMAYIN01'
            },
            {
                name: 'Sửa bàn ủi',
                category_name: 'Sửa chữa đồ gia dụng',
                type: 'service',
                price: 150000,
                compare_at_price: 180000,
                image_url: '/images/mockup/services-repairs/sua-ban-ui.webp',
                status: 'active',
                sku: 'SUABANUI01'
            },
            {
                name: 'Sửa máy xay',
                category_name: 'Sửa chữa đồ gia dụng',
                type: 'service',
                price: 180000,
                compare_at_price: 200000,
                image_url: '/images/mockup/services-repairs/sua-may-xay.webp',
                status: 'active',
                sku: 'SUAMAYXAY01'
            },
            {
                name: 'Sửa tai nghe',
                category_name: 'Sửa chữa điện tử',
                type: 'service',
                price: 120000,
                compare_at_price: 150000,
                image_url: '/images/mockup/services-repairs/sua-tai-nghe.webp',
                status: 'active',
                sku: 'SUATAI01'
            },
            {
                name: 'Sửa đồng hồ',
                category_name: 'Sửa chữa điện tử',
                type: 'service',
                price: 250000,
                compare_at_price: 300000,
                image_url: '/images/mockup/services-repairs/sua-dong-ho.webp',
                status: 'active',
                sku: 'SUADH01'
            },
        ],
        branches: [
            {name: 'Trung tâm Đà Nẵng', address: '789 Nguyễn Trãi, Đà Nẵng', phone: '0909765432', is_active: true},
        ],
        branch_products: [
            {product_name: 'Sửa điện thoại', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa laptop', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa tivi', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa máy ảnh', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa loa', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa tủ lạnh', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa máy giặt', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa lò vi sóng', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa máy hút bụi', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa quạt điện', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa máy in', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa bàn ủi', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa máy xay', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa tai nghe', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
            {product_name: 'Sửa đồng hồ', branch_name: 'Trung tâm Đà Nẵng', is_active: true},
        ],
        customer_orders: [
            {
                customer_id: 'customer-uuid-5',
                branch_name: 'Trung tâm Đà Nẵng',
                total_amount: 700000,
                payment_method: 'zalopay',
                status: 'completed',
                items: [
                    {product_name: 'Sửa điện thoại', quantity: 1, price: 200000},
                    {product_name: 'Sửa laptop', quantity: 1, price: 500000},
                ],
            },
        ],
        account_themes: [
            {
                theme_id: '********-0000-4000-a000-********0001',
                theme_name: 'Service Theme',
                mini_app_id: '3423472366583461276',
                primary_color: '#007BFF',
                secondary_color: '#6C757D',
                logo_url: '/images/mockup/services-repairs/abc-logo.webp'
            },
        ],
    },
    'electronics': {
        account: {
            name: 'Điện tử TechZone',
            slug: 'techzone',
            email: '<EMAIL>',
            is_personal_account: false,
            picture_url: '/images/mockup/electronics/techzone-logo.webp',
        },
        memberships: [
            {user_id: 'customer-uuid-7', account_role: 'customer'},
            {user_id: 'customer-uuid-8', account_role: 'customer'},
        ],
        categories: [
            {
                name: 'Điện thoại',
                description: 'Smartphones',
                parent_id: null,
                image_url: '/images/mockup/electronics/dien-thoai.webp'
            },
            {
                name: 'Máy tính',
                description: 'Computers',
                parent_id: null,
                image_url: '/images/mockup/electronics/may-tinh.webp'
            },
            {
                name: 'Phụ kiện',
                description: 'Accessories',
                parent_id: null,
                image_url: '/images/mockup/electronics/phu-kien.webp'
            },
        ],
        products: [
            {
                name: 'iPhone 14',
                category_name: 'Điện thoại',
                type: 'physical',
                price: ********,
                compare_at_price: ********,
                image_url: '/images/mockup/electronics/iphone-14.webp',
                status: 'active',
                sku: 'IPHONE14'
            },
            {
                name: 'Samsung Galaxy S23',
                category_name: 'Điện thoại',
                type: 'physical',
                price: 18000000,
                compare_at_price: ********,
                image_url: '/images/mockup/electronics/galaxy-s23.webp',
                status: 'active',
                sku: 'S23'
            },
            {
                name: 'Oppo Reno 8',
                category_name: 'Điện thoại',
                type: 'physical',
                price: 12000000,
                compare_at_price: 13000000,
                image_url: '/images/mockup/electronics/oppo-reno-8.webp',
                status: 'active',
                sku: 'RENO8'
            },
            {
                name: 'Xiaomi 13',
                category_name: 'Điện thoại',
                type: 'physical',
                price: 15000000,
                compare_at_price: 17000000,
                image_url: '/images/mockup/electronics/xiaomi-13.webp',
                status: 'active',
                sku: 'XIAOMI13'
            },
            {
                name: 'Laptop Dell XPS',
                category_name: 'Máy tính',
                type: 'physical',
                price: 30000000,
                compare_at_price: 32000000,
                image_url: '/images/mockup/electronics/dell-xps.webp',
                status: 'active',
                sku: 'DELLXPS'
            },
            {
                name: 'MacBook Air',
                category_name: 'Máy tính',
                type: 'physical',
                price: ********,
                compare_at_price: 28000000,
                image_url: '/images/mockup/electronics/macbook-air.webp',
                status: 'active',
                sku: 'MACAIR'
            },
            {
                name: 'Laptop HP Spectre',
                category_name: 'Máy tính',
                type: 'physical',
                price: 28000000,
                compare_at_price: 30000000,
                image_url: '/images/mockup/electronics/hp-spectre.webp',
                status: 'active',
                sku: 'HPSPECTRE'
            },
            {
                name: 'PC Gaming',
                category_name: 'Máy tính',
                type: 'physical',
                price: 35000000,
                compare_at_price: 38000000,
                image_url: '/images/mockup/electronics/pc-gaming.webp',
                status: 'active',
                sku: 'PCGAMING'
            },
            {
                name: 'Tai nghe AirPods',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 5000000,
                compare_at_price: 5500000,
                image_url: '/images/mockup/electronics/airpods.webp',
                status: 'active',
                sku: 'AIRPODS'
            },
            {
                name: 'Sạc dự phòng',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 300000,
                compare_at_price: 350000,
                image_url: '/images/mockup/electronics/sac-du-phong.webp',
                status: 'active',
                sku: 'SAC01'
            },
            {
                name: 'Chuột Logitech',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 400000,
                compare_at_price: 450000,
                image_url: '/images/mockup/electronics/chuot-logitech.webp',
                status: 'active',
                sku: 'CHUOT01'
            },
            {
                name: 'Bàn phím cơ',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 1000000,
                compare_at_price: 1200000,
                image_url: '/images/mockup/electronics/ban-phim-co.webp',
                status: 'active',
                sku: 'BANPHIM01'
            },
            {
                name: 'Loa Bluetooth',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 800000,
                compare_at_price: 900000,
                image_url: '/images/mockup/electronics/loa-bluetooth.webp',
                status: 'active',
                sku: 'LOA01'
            },
            {
                name: 'Ổ cứng SSD',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 1500000,
                compare_at_price: 1700000,
                image_url: '/images/mockup/electronics/o-cung-ssd.webp',
                status: 'active',
                sku: 'SSD01'
            },
            {
                name: 'Cáp sạc USB',
                category_name: 'Phụ kiện',
                type: 'physical',
                price: 50000,
                compare_at_price: 60000,
                image_url: '/images/mockup/electronics/cap-sac-usb.webp',
                status: 'active',
                sku: 'CAPUSB01'
            },
        ],
        branches: [
            {name: 'Cửa hàng Hà Nội', address: '321 Hoàn Kiếm, Hà Nội', phone: '0909123876', is_active: true},
        ],
        branch_products: [
            {product_name: 'iPhone 14', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Samsung Galaxy S23', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Oppo Reno 8', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Xiaomi 13', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Laptop Dell XPS', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'MacBook Air', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Laptop HP Spectre', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'PC Gaming', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Tai nghe AirPods', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Sạc dự phòng', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Chuột Logitech', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Bàn phím cơ', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Loa Bluetooth', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Ổ cứng SSD', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Cáp sạc USB', branch_name: 'Cửa hàng Hà Nội', is_active: true},
        ],
        customer_orders: [
            {
                customer_id: 'customer-uuid-7',
                branch_name: 'Cửa hàng Hà Nội',
                total_amount: ********,
                payment_method: 'zalopay',
                status: 'completed',
                items: [
                    {product_name: 'MacBook Air', quantity: 1, price: ********},
                    {product_name: 'Sạc dự phòng', quantity: 1, price: 300000},
                ],
            },
        ],
        account_themes: [
            {
                theme_id: '********-0000-4000-a000-********0001',
                theme_name: 'Electronics Theme',
                mini_app_id: '3423472366583461276',
                primary_color: '#343A40',
                secondary_color: '#F8F9FA',
                logo_url: '/images/mockup/electronics/techzone-logo.webp'
            },
        ],
    },
    'beauty-cosmetics': {
        account: {
            name: 'Mỹ phẩm Hoa Anh Đào',
            slug: 'hoa-anh-dao',
            email: '<EMAIL>',
            is_personal_account: false,
            picture_url: '/images/mockup/beauty-cosmetics/hoa-anh-dao-logo.webp',
        },
        memberships: [
            {user_id: 'customer-uuid-9', account_role: 'customer'},
            {user_id: 'customer-uuid-10', account_role: 'customer'},
        ],
        categories: [
            {
                name: 'Chăm sóc da',
                description: 'Skincare',
                parent_id: null,
                image_url: '/images/mockup/beauty-cosmetics/cham-soc-da.webp'
            },
            {
                name: 'Trang điểm',
                description: 'Makeup',
                parent_id: null,
                image_url: '/images/mockup/beauty-cosmetics/trang-diem.webp'
            },
            {
                name: 'Chăm sóc tóc',
                description: 'Haircare',
                parent_id: null,
                image_url: '/images/mockup/beauty-cosmetics/cham-soc-toc.webp'
            },
        ],
        products: [
            {
                name: 'Sữa rửa mặt',
                category_name: 'Chăm sóc da',
                type: 'physical',
                price: 150000,
                compare_at_price: 180000,
                image_url: '/images/mockup/beauty-cosmetics/sua-rua-mat.webp',
                status: 'active',
                sku: 'SRM01'
            },
            {
                name: 'Kem dưỡng ẩm',
                category_name: 'Chăm sóc da',
                type: 'physical',
                price: 250000,
                compare_at_price: 300000,
                image_url: '/images/mockup/beauty-cosmetics/kem-duong-am.webp',
                status: 'active',
                sku: 'KDA01'
            },
            {
                name: 'Mặt nạ giấy',
                category_name: 'Chăm sóc da',
                type: 'physical',
                price: 30000,
                compare_at_price: 35000,
                image_url: '/images/mockup/beauty-cosmetics/mat-na-giay.webp',
                status: 'active',
                sku: 'MNG01'
            },
            {
                name: 'Toner',
                category_name: 'Chăm sóc da',
                type: 'physical',
                price: 200000,
                compare_at_price: 250000,
                image_url: '/images/mockup/beauty-cosmetics/toner.webp',
                status: 'active',
                sku: 'TONER01'
            },
            {
                name: 'Kem chống nắng',
                category_name: 'Chăm sóc da',
                type: 'physical',
                price: 300000,
                compare_at_price: 350000,
                image_url: '/images/mockup/beauty-cosmetics/kem-chong-nang.webp',
                status: 'active',
                sku: 'KCN01'
            },
            {
                name: 'Son môi',
                category_name: 'Trang điểm',
                type: 'physical',
                price: 200000,
                compare_at_price: 250000,
                image_url: '/images/mockup/beauty-cosmetics/son-moi.webp',
                status: 'active',
                sku: 'SON01'
            },
            {
                name: 'Phấn má hồng',
                category_name: 'Trang điểm',
                type: 'physical',
                price: 180000,
                compare_at_price: 220000,
                image_url: '/images/mockup/beauty-cosmetics/phan-ma-hong.webp',
                status: 'active',
                sku: 'PMH01'
            },
            {
                name: 'Mascara',
                category_name: 'Trang điểm',
                type: 'physical',
                price: 150000,
                compare_at_price: 180000,
                image_url: '/images/mockup/beauty-cosmetics/mascara.webp',
                status: 'active',
                sku: 'MAS01'
            },
            {
                name: 'Phấn nền',
                category_name: 'Trang điểm',
                type: 'physical',
                price: 250000,
                compare_at_price: 300000,
                image_url: '/images/mockup/beauty-cosmetics/phan-nen.webp',
                status: 'active',
                sku: 'PHANNEN01'
            },
            {
                name: 'Chì kẻ mày',
                category_name: 'Trang điểm',
                type: 'physical',
                price: 100000,
                compare_at_price: 120000,
                image_url: '/images/mockup/beauty-cosmetics/chi-ke-may.webp',
                status: 'active',
                sku: 'CHIMAY01'
            },
            {
                name: 'Dầu gội',
                category_name: 'Chăm sóc tóc',
                type: 'physical',
                price: 120000,
                compare_at_price: 150000,
                image_url: '/images/mockup/beauty-cosmetics/dau-goi.webp',
                status: 'active',
                sku: 'DG01'
            },
            {
                name: 'Dầu xả',
                category_name: 'Chăm sóc tóc',
                type: 'physical',
                price: 130000,
                compare_at_price: 160000,
                image_url: '/images/mockup/beauty-cosmetics/dau-xa.webp',
                status: 'active',
                sku: 'DX01'
            },
            {
                name: 'Mặt nạ tóc',
                category_name: 'Chăm sóc tóc',
                type: 'physical',
                price: 150000,
                compare_at_price: 180000,
                image_url: '/images/mockup/beauty-cosmetics/mat-na-toc.webp',
                status: 'active',
                sku: 'MNT01'
            },
            {
                name: 'Dầu dưỡng tóc',
                category_name: 'Chăm sóc tóc',
                type: 'physical',
                price: 200000,
                compare_at_price: 250000,
                image_url: '/images/mockup/beauty-cosmetics/dau-duong-toc.webp',
                status: 'active',
                sku: 'DDT01'
            },
            {
                name: 'Keo xịt tóc',
                category_name: 'Chăm sóc tóc',
                type: 'physical',
                price: 100000,
                compare_at_price: 120000,
                image_url: '/images/mockup/beauty-cosmetics/keo-xit-toc.webp',
                status: 'active',
                sku: 'KEOXIT01'
            },
        ],
        branches: [
            {name: 'Cửa hàng Hà Nội', address: '654 Ba Đình, Hà Nội', phone: '0909123765', is_active: true},
        ],
        branch_products: [
            {product_name: 'Sữa rửa mặt', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Kem dưỡng ẩm', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Mặt nạ giấy', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Toner', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Kem chống nắng', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Son môi', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Phấn má hồng', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Mascara', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Phấn nền', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Chì kẻ mày', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Dầu gội', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Dầu xả', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Mặt nạ tóc', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Dầu dưỡng tóc', branch_name: 'Cửa hàng Hà Nội', is_active: true},
            {product_name: 'Keo xịt tóc', branch_name: 'Cửa hàng Hà Nội', is_active: true},
        ],
        customer_orders: [
            {
                customer_id: 'customer-uuid-9',
                branch_name: 'Cửa hàng Hà Nội',
                total_amount: 380000,
                payment_method: 'zalopay',
                status: 'completed',
                items: [
                    {product_name: 'Sữa rửa mặt', quantity: 1, price: 150000},
                    {product_name: 'Son môi', quantity: 1, price: 200000},
                    {product_name: 'Mặt nạ giấy', quantity: 1, price: 30000},
                ],
            },
        ],
        account_themes: [
            {
                theme_id: '********-0000-4000-a000-********0001',
                theme_name: 'Cosmetics Theme',
                mini_app_id: '3423472366583461276',
                primary_color: '#FF69B4',
                secondary_color: '#FFD700',
                logo_url: '/images/mockup/beauty-cosmetics/hoa-anh-dao-logo.webp'
            },
        ],
    },
    'grocery': {
        account: {
            name: 'Siêu thị Mini 365',
            slug: 'sieu-thi-365',
            email: '<EMAIL>',
            is_personal_account: false,
            picture_url: '/images/mockup/grocery/sieu-thi-365-logo.webp',
        },
        memberships: [
            {user_id: 'customer-uuid-11', account_role: 'customer'},
            {user_id: 'customer-uuid-12', account_role: 'customer'},
        ],
        categories: [
            {
                name: 'Thực phẩm tươi',
                description: 'Fresh Food',
                parent_id: null,
                image_url: '/images/mockup/grocery/thuc-pham-tuoi.webp'
            },
            {
                name: 'Đồ khô',
                description: 'Dry Goods',
                parent_id: null,
                image_url: '/images/mockup/grocery/do-kho.webp'
            },
            {
                name: 'Đồ uống',
                description: 'Beverages',
                parent_id: null,
                image_url: '/images/mockup/grocery/do-uong.webp'
            },
            {
                name: 'Đồ gia dụng',
                description: 'Household',
                parent_id: null,
                image_url: '/images/mockup/grocery/do-gia-dung.webp'
            },
        ],
        products: [
            {
                name: 'Thịt bò tươi',
                category_name: 'Thực phẩm tươi',
                type: 'physical',
                price: 200000,
                compare_at_price: 220000,
                image_url: '/images/mockup/grocery/thit-bo-tuoi.webp',
                status: 'active',
                sku: 'THITBO01'
            },
            {
                name: 'Cá hồi',
                category_name: 'Thực phẩm tươi',
                type: 'physical',
                price: 300000,
                compare_at_price: 350000,
                image_url: '/images/mockup/grocery/ca-hoi.webp',
                status: 'active',
                sku: 'CAHOI01'
            },
            {
                name: 'Rau muống',
                category_name: 'Thực phẩm tươi',
                type: 'physical',
                price: 15000,
                compare_at_price: 20000,
                image_url: '/images/mockup/grocery/rau-muong.webp',
                status: 'active',
                sku: 'RAU01'
            },
            {
                name: 'Cà chua',
                category_name: 'Thực phẩm tươi',
                type: 'physical',
                price: 20000,
                compare_at_price: 25000,
                image_url: '/images/mockup/grocery/ca-chua.webp',
                status: 'active',
                sku: 'CACHUA01'
            },
            {
                name: 'Thịt gà',
                category_name: 'Thực phẩm tươi',
                type: 'physical',
                price: 120000,
                compare_at_price: 140000,
                image_url: '/images/mockup/grocery/thit-ga.webp',
                status: 'active',
                sku: 'THITGA01'
            },
            {
                name: 'Gạo ST25',
                category_name: 'Đồ khô',
                type: 'physical',
                price: 150000,
                compare_at_price: 180000,
                image_url: '/images/mockup/grocery/gao-st25.webp',
                status: 'active',
                sku: 'GAO01'
            },
            {
                name: 'Mì gói',
                category_name: 'Đồ khô',
                type: 'physical',
                price: 5000,
                compare_at_price: 6000,
                image_url: '/images/mockup/grocery/mi-goi.webp',
                status: 'active',
                sku: 'MIGOI01'
            },
            {
                name: 'Đậu phộng',
                category_name: 'Đồ khô',
                type: 'physical',
                price: 30000,
                compare_at_price: 35000,
                image_url: '/images/mockup/grocery/dau-phong.webp',
                status: 'active',
                sku: 'DAUPHONG01'
            },
            {
                name: 'Bánh quy',
                category_name: 'Đồ khô',
                type: 'physical',
                price: 40000,
                compare_at_price: 45000,
                image_url: '/images/mockup/grocery/banh-quy.webp',
                status: 'active',
                sku: 'BANHQUY01'
            },
            {
                name: 'Nước ngọt Pepsi',
                category_name: 'Đồ uống',
                type: 'physical',
                price: 10000,
                compare_at_price: 12000,
                image_url: '/images/mockup/grocery/pepsi.webp',
                status: 'active',
                sku: 'PEPSI01'
            },
            {
                name: 'Sữa tươi',
                category_name: 'Đồ uống',
                type: 'physical',
                price: 25000,
                compare_at_price: 30000,
                image_url: '/images/mockup/grocery/sua-tuoi.webp',
                status: 'active',
                sku: 'SUA01'
            },
            {
                name: 'Nước lọc',
                category_name: 'Đồ uống',
                type: 'physical',
                price: 5000,
                compare_at_price: 6000,
                image_url: '/images/mockup/grocery/nuoc-loc.webp',
                status: 'active',
                sku: 'NUOCLOC01'
            },
            {
                name: 'Nước rửa chén',
                category_name: 'Đồ gia dụng',
                type: 'physical',
                price: 40000,
                compare_at_price: 45000,
                image_url: '/images/mockup/grocery/nuoc-rua-chen.webp',
                status: 'active',
                sku: 'NRC01'
            },
            {
                name: 'Khăn giấy',
                category_name: 'Đồ gia dụng',
                type: 'physical',
                price: 20000,
                compare_at_price: 25000,
                image_url: '/images/mockup/grocery/khan-giay.webp',
                status: 'active',
                sku: 'KHAN01'
            },
            {
                name: 'Xà phòng',
                category_name: 'Đồ gia dụng',
                type: 'physical',
                price: 15000,
                compare_at_price: 20000,
                image_url: '/images/mockup/grocery/xa-phong.webp',
                status: 'active',
                sku: 'XAPHONG01'
            },
        ],
        branches: [
            {name: 'Cửa hàng TP.HCM', address: '987 Gò Vấp, TP.HCM', phone: '0909123987', is_active: true},
        ],
        branch_products: [
            {product_name: 'Thịt bò tươi', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Cá hồi', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Rau muống', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Cà chua', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Thịt gà', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Gạo ST25', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Mì gói', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Đậu phộng', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Bánh quy', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Nước ngọt Pepsi', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Sữa tươi', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Nước lọc', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Nước rửa chén', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Khăn giấy', branch_name: 'Cửa hàng TP.HCM', is_active: true},
            {product_name: 'Xà phòng', branch_name: 'Cửa hàng TP.HCM', is_active: true},
        ],
        customer_orders: [
            {
                customer_id: 'customer-uuid-11',
                branch_name: 'Cửa hàng TP.HCM',
                total_amount: 245000,
                payment_method: 'zalopay',
                status: 'completed',
                items: [
                    {product_name: 'Thịt bò tươi', quantity: 1, price: 200000},
                    {product_name: 'Nước ngọt Pepsi', quantity: 2, price: 10000},
                    {product_name: 'Khăn giấy', quantity: 1, price: 25000},
                ],
            },
        ],
        account_themes: [
            {
                theme_id: '********-0000-4000-a000-********0001',
                theme_name: 'Grocery Theme',
                mini_app_id: '3423472366583461276',
                primary_color: '#6B7280',
                secondary_color: '#10B981',
                logo_url: '/images/mockup/grocery/sieu-thi-365-logo.webp'
            },
        ],
    },
    'gym-membership': {
        account: {
            name: 'Phòng Gym Sức Mạnh',
            slug: 'suc-manh-gym',
            email: '<EMAIL>',
            is_personal_account: false,
            picture_url: '/images/mockup/gym-membership/suc-manh-gym-logo.webp',
        },
        memberships: [
            {user_id: 'customer-uuid-13', account_role: 'customer'},
            {user_id: 'customer-uuid-14', account_role: 'customer'},
        ],
        categories: [
            {
                name: 'Thẻ tập gym',
                description: 'Gym Memberships',
                parent_id: null,
                image_url: '/images/mockup/gym-membership/the-tap-gym.webp'
            },
            {
                name: 'Dịch vụ PT',
                description: 'Personal Training',
                parent_id: null,
                image_url: '/images/mockup/gym-membership/dich-vu-pt.webp'
            },
            {
                name: 'Lớp nhóm',
                description: 'Group Classes',
                parent_id: null,
                image_url: '/images/mockup/gym-membership/lop-nhom.webp'
            },
        ],
        products: [
            {
                name: 'Thẻ 1 tháng',
                category_name: 'Thẻ tập gym',
                type: 'service',
                price: 500000,
                compare_at_price: 600000,
                image_url: '/images/mockup/gym-membership/the-1-thang.webp',
                status: 'active',
                sku: 'GYM1M'
            },
            {
                name: 'Thẻ 3 tháng',
                category_name: 'Thẻ tập gym',
                type: 'service',
                price: 1400000,
                compare_at_price: 1600000,
                image_url: '/images/mockup/gym-membership/the-3-thang.webp',
                status: 'active',
                sku: 'GYM3M'
            },
            {
                name: 'Thẻ 6 tháng',
                category_name: 'Thẻ tập gym',
                type: 'service',
                price: 2600000,
                compare_at_price: 3000000,
                image_url: '/images/mockup/gym-membership/the-6-thang.webp',
                status: 'active',
                sku: 'GYM6M'
            },
            {
                name: 'Thẻ 12 tháng',
                category_name: 'Thẻ tập gym',
                type: 'service',
                price: 4800000,
                compare_at_price: 5500000,
                image_url: '/images/mockup/gym-membership/the-12-thang.webp',
                status: 'active',
                sku: 'GYM12M'
            },
            {
                name: 'PT 10 buổi',
                category_name: 'Dịch vụ PT',
                type: 'service',
                price: 3000000,
                compare_at_price: 3500000,
                image_url: '/images/mockup/gym-membership/pt-10-buoi.webp',
                status: 'active',
                sku: 'PT10'
            },
            {
                name: 'PT 20 buổi',
                category_name: 'Dịch vụ PT',
                type: 'service',
                price: 5500000,
                compare_at_price: 6000000,
                image_url: '/images/mockup/gym-membership/pt-20-buoi.webp',
                status: 'active',
                sku: 'PT20'
            },
            {
                name: 'PT 30 buổi',
                category_name: 'Dịch vụ PT',
                type: 'service',
                price: 7800000,
                compare_at_price: 8500000,
                image_url: '/images/mockup/gym-membership/pt-30-buoi.webp',
                status: 'active',
                sku: 'PT30'
            },
            {
                name: 'Yoga 10 buổi',
                category_name: 'Lớp nhóm',
                type: 'service',
                price: 800000,
                compare_at_price: 900000,
                image_url: '/images/mockup/gym-membership/yoga-10-buoi.webp',
                status: 'active',
                sku: 'YOGA10'
            },
            {
                name: 'Zumba 10 buổi',
                category_name: 'Lớp nhóm',
                type: 'service',
                price: 850000,
                compare_at_price: 950000,
                image_url: '/images/mockup/gym-membership/zumba-10-buoi.webp',
                status: 'active',
                sku: 'ZUMBA10'
            },
            {
                name: 'Kickboxing 10 buổi',
                category_name: 'Lớp nhóm',
                type: 'service',
                price: 900000,
                compare_at_price: 1000000,
                image_url: '/images/mockup/gym-membership/kickboxing-10-buoi.webp',
                status: 'active',
                sku: 'KICK10'
            },
        ],
        branches: [
            {
                name: 'Chi nhánh TP.HCM',
                address: '123 Nguyễn Thị Minh Khai, TP.HCM',
                phone: '0909123456',
                is_active: true
            },
        ],
        branch_products: [
            {product_name: 'Thẻ 1 tháng', branch_name: 'Chi nhánh TP.HCM', is_active: true},
            {product_name: 'Thẻ 3 tháng', branch_name: 'Chi nhánh TP.HCM', is_active: true},
            {product_name: 'Thẻ 6 tháng', branch_name: 'Chi nhánh TP.HCM', is_active: true},
            {product_name: 'Thẻ 12 tháng', branch_name: 'Chi nhánh TP.HCM', is_active: true},
            {product_name: 'PT 10 buổi', branch_name: 'Chi nhánh TP.HCM', is_active: true},
            {product_name: 'PT 20 buổi', branch_name: 'Chi nhánh TP.HCM', is_active: true},
            {product_name: 'PT 30 buổi', branch_name: 'Chi nhánh TP.HCM', is_active: true},
            {product_name: 'Yoga 10 buổi', branch_name: 'Chi nhánh TP.HCM', is_active: true},
            {product_name: 'Zumba 10 buổi', branch_name: 'Chi nhánh TP.HCM', is_active: true},
            {product_name: 'Kickboxing 10 buổi', branch_name: 'Chi nhánh TP.HCM', is_active: true},
        ],
        customer_orders: [
            {
                customer_id: 'customer-uuid-13',
                branch_name: 'Chi nhánh TP.HCM',
                total_amount: 1900000,
                payment_method: 'zalopay',
                status: 'completed',
                items: [
                    {product_name: 'Thẻ 3 tháng', quantity: 1, price: 1400000},
                    {product_name: 'Yoga 10 buổi', quantity: 1, price: 800000},
                ],
            },
            {
                customer_id: 'customer-uuid-14',
                branch_name: 'Chi nhánh TP.HCM',
                total_amount: 500000,
                payment_method: 'cash',
                status: 'pending',
                items: [
                    {product_name: 'Thẻ 1 tháng', quantity: 1, price: 500000},
                ],
            },
        ],
        account_themes: [
            {
                theme_id: '********-0000-4000-a000-********0001',
                theme_name: 'Gym Theme',
                mini_app_id: '3423472366583461276',
                primary_color: '#FF4500',
                secondary_color: '#1E90FF',
                logo_url: '/images/mockup/gym-membership/suc-manh-gym-logo.webp'
            },
        ],
    },
    'education': {
        account: {
            name: 'Trường Mầm non Hoa Mai',
            slug: 'truong-hoa-mai',
            email: '<EMAIL>',
            is_personal_account: false,
            picture_url: '/images/mockup/education/hoa-mai-logo.svg',
        },
        organization: {
            name: 'Trường Mầm non Hoa Mai',
            organization_type: 'kindergarten',
            address: '123 Đường Nguyễn Văn Cừ, Quận 5, TP.HCM',
            phone: '028-3838-1234',
            email: '<EMAIL>',
            website: 'https://truonghoamai.edu.vn',
            established_year: 2010,
            license_number: 'GD-ĐKMN-001/2010',
            capacity: 300,
            description: 'Trường mầm non uy tín với phương pháp giáo dục hiện đại, tập trung phát triển toàn diện cho trẻ em từ 18 tháng đến 6 tuổi.',
        },
        programs: [
            {
                name: 'Lớp Nhà trẻ (18-36 tháng)',
                description: 'Chương trình chăm sóc và phát triển cho trẻ từ 18-36 tháng',
                program_type: 'nursery',
                age_group: '18-36 months',
                capacity: 15,
                duration_weeks: 78, // 18 months * 4.33 weeks
                schedule: { days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'], time: '07:30-16:30' },
                fees: { monthly: 3500000, meal: 800000 },
                status: 'active',
            },
            {
                name: 'Lớp Chồi (3-4 tuổi)',
                description: 'Chương trình giáo dục mầm non cho trẻ 3-4 tuổi',
                program_type: 'preschool',
                age_group: '3-4 years',
                capacity: 20,
                duration_weeks: 52,
                schedule: { days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'], time: '07:30-16:30' },
                fees: { monthly: 4000000, meal: 800000 },
                status: 'active',
            },
            {
                name: 'Lớp Lá (4-5 tuổi)',
                description: 'Chương trình giáo dục mầm non cho trẻ 4-5 tuổi',
                program_type: 'preschool',
                age_group: '4-5 years',
                capacity: 25,
                duration_weeks: 52,
                schedule: { days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'], time: '07:30-16:30' },
                fees: { monthly: 4500000, meal: 800000 },
                status: 'active',
            },
            {
                name: 'Lớp Hoa (5-6 tuổi)',
                description: 'Chương trình chuẩn bị vào lớp 1 cho trẻ 5-6 tuổi',
                program_type: 'kindergarten',
                age_group: '5-6 years',
                capacity: 25,
                duration_weeks: 52,
                schedule: { days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'], time: '07:30-16:30' },
                fees: { monthly: 5000000, meal: 800000 },
                status: 'active',
            },
        ],
        instructors: [
            {
                full_name: 'Nguyễn Thị Hoa',
                employee_code: 'INS001',
                email: '<EMAIL>',
                phone: '0909123456',
                specialization: 'Giáo dục mầm non',
                qualifications: {
                    degree: 'Cử nhân Sư phạm Mầm non',
                    experience: '8 years',
                    certifications: ['Chứng chỉ Montessori', 'Chứng chỉ Anh ngữ trẻ em']
                },
                status: 'active',
            },
            {
                full_name: 'Trần Thị Mai',
                employee_code: 'INS002',
                email: '<EMAIL>',
                phone: '0909123457',
                specialization: 'Âm nhạc trẻ em',
                qualifications: {
                    degree: 'Cử nhân Âm nhạc',
                    experience: '5 years',
                    certifications: ['Chứng chỉ Orff', 'Chứng chỉ Piano']
                },
                status: 'active',
            },
            {
                full_name: 'Lê Văn Nam',
                employee_code: 'INS003',
                email: '<EMAIL>',
                phone: '0909123458',
                specialization: 'Thể dục trẻ em',
                qualifications: {
                    degree: 'Cử nhân Thể dục thể thao',
                    experience: '6 years',
                    certifications: ['Chứng chỉ Yoga trẻ em', 'Chứng chỉ Bơi lội']
                },
                status: 'active',
            },
            {
                full_name: 'Phạm Thị Linh',
                employee_code: 'INS004',
                email: '<EMAIL>',
                phone: '0909123459',
                specialization: 'Tiếng Anh trẻ em',
                qualifications: {
                    degree: 'Cử nhân Ngôn ngữ Anh',
                    experience: '4 years',
                    certifications: ['TESOL Certificate', 'Cambridge Teaching Certificate']
                },
                status: 'active',
            },
            {
                full_name: 'Võ Thị Hương',
                employee_code: 'INS005',
                email: '<EMAIL>',
                phone: '0909123460',
                specialization: 'Mỹ thuật trẻ em',
                qualifications: {
                    degree: 'Cử nhân Mỹ thuật',
                    experience: '7 years',
                    certifications: ['Chứng chỉ Giáo dục Nghệ thuật', 'Chứng chỉ Vẽ tranh']
                },
                status: 'active',
            },
            {
                full_name: 'Đặng Văn Tuấn',
                employee_code: 'INS006',
                email: '<EMAIL>',
                phone: '0909123461',
                specialization: 'Khoa học tự nhiên',
                qualifications: {
                    degree: 'Cử nhân Khoa học Tự nhiên',
                    experience: '5 years',
                    certifications: ['Chứng chỉ STEM Education', 'Chứng chỉ Thí nghiệm An toàn']
                },
                status: 'active',
            },
        ],
        learners: [
            {
                full_name: 'Nguyễn Minh An',
                nickname: 'Bé An',
                learner_code: 'HM2024001',
                date_of_birth: '2021-03-15',
                gender: 'male',
                address: '456 Đường Lê Lợi, Quận 1, TP.HCM',
                emergency_contact: {
                    name: 'Nguyễn Thị Lan',
                    relationship: 'mother',
                    phone: '**********',
                },
                medical_info: {
                    allergies: 'None',
                    medications: 'None'
                },
                status: 'active',
            },
            {
                full_name: 'Trần Thị Bảo Ngọc',
                nickname: 'Bé Ngọc',
                learner_code: 'HM2024002',
                date_of_birth: '2020-07-20',
                gender: 'female',
                address: '789 Đường Nguyễn Huệ, Quận 1, TP.HCM',
                emergency_contact: {
                    name: 'Trần Thị Hương',
                    relationship: 'mother',
                    phone: '**********',
                },
                medical_info: {
                    allergies: 'Peanuts',
                    medications: 'None'
                },
                status: 'active',
            },
            {
                full_name: 'Lê Hoàng Minh',
                nickname: 'Bé Minh',
                learner_code: 'HM2024003',
                date_of_birth: '2019-11-10',
                gender: 'male',
                address: '321 Đường Võ Văn Tần, Quận 3, TP.HCM',
                emergency_contact: {
                    name: 'Lê Văn Hùng',
                    relationship: 'father',
                    phone: '**********',
                },
                medical_info: {
                    allergies: 'None',
                    medications: 'Asthma inhaler'
                },
                status: 'active',
            },
            {
                full_name: 'Nguyễn Thị Kim Anh',
                nickname: 'Bé Kim Anh',
                learner_code: 'HM2024004',
                date_of_birth: '2021-05-20',
                gender: 'female',
                address: '123 Đường Pasteur, Quận 1, TP.HCM',
                emergency_contact: {
                    name: 'Nguyễn Văn Đức',
                    relationship: 'father',
                    phone: '**********',
                },
                medical_info: {
                    allergies: 'Milk',
                    medications: 'None'
                },
                status: 'active',
            },
            {
                full_name: 'Trần Văn Bảo',
                nickname: 'Bé Bảo',
                learner_code: 'HM2024005',
                date_of_birth: '2021-08-12',
                gender: 'male',
                address: '567 Đường Điện Biên Phủ, Quận 3, TP.HCM',
                emergency_contact: {
                    name: 'Trần Thị Hoa',
                    relationship: 'mother',
                    phone: '**********',
                },
                medical_info: {
                    allergies: 'None',
                    medications: 'Vitamin D'
                },
                status: 'active',
            },
            {
                full_name: 'Phạm Thị Lan Anh',
                nickname: 'Bé Lan Anh',
                learner_code: 'HM2024006',
                date_of_birth: '2020-12-03',
                gender: 'female',
                address: '890 Đường Cách Mạng Tháng 8, Quận 10, TP.HCM',
                emergency_contact: {
                    name: 'Phạm Văn Nam',
                    relationship: 'father',
                    phone: '**********',
                },
                medical_info: {
                    allergies: 'Seafood',
                    medications: 'None'
                },
                status: 'active',
            },
        ],
        guardians: [
            {
                full_name: 'Nguyễn Văn Bình',
                relationship: 'father',
                phone: '**********',
                email: '<EMAIL>',
                address: '456 Đường Lê Lợi, Quận 1, TP.HCM',
                is_primary: true,
                status: 'active',
            },
            {
                full_name: 'Nguyễn Thị Lan',
                relationship: 'mother',
                phone: '**********',
                email: '<EMAIL>',
                address: '456 Đường Lê Lợi, Quận 1, TP.HCM',
                is_primary: false,
                status: 'active',
            },
            {
                full_name: 'Trần Văn Đức',
                relationship: 'father',
                phone: '**********',
                email: '<EMAIL>',
                address: '789 Đường Nguyễn Huệ, Quận 1, TP.HCM',
                is_primary: true,
                status: 'active',
            },
            {
                full_name: 'Lê Thị Thu',
                relationship: 'mother',
                phone: '0912345682',
                email: '<EMAIL>',
                address: '321 Đường Võ Văn Tần, Quận 3, TP.HCM',
                is_primary: true,
                status: 'active',
            },
            {
                full_name: 'Lê Văn Hùng',
                relationship: 'father',
                phone: '**********',
                email: '<EMAIL>',
                address: '321 Đường Võ Văn Tần, Quận 3, TP.HCM',
                is_primary: false,
                status: 'active',
            },
            {
                full_name: 'Nguyễn Văn Đức',
                relationship: 'father',
                phone: '**********',
                email: '<EMAIL>',
                address: '123 Đường Pasteur, Quận 1, TP.HCM',
                is_primary: true,
                status: 'active',
            },
            {
                full_name: 'Trần Thị Hoa',
                relationship: 'mother',
                phone: '**********',
                email: '<EMAIL>',
                address: '567 Đường Điện Biên Phủ, Quận 3, TP.HCM',
                is_primary: true,
                status: 'active',
            },
            {
                full_name: 'Phạm Văn Nam',
                relationship: 'father',
                phone: '**********',
                email: '<EMAIL>',
                address: '890 Đường Cách Mạng Tháng 8, Quận 10, TP.HCM',
                is_primary: true,
                status: 'active',
            },
        ],
        enrollments: [
            {
                learner_name: 'Nguyễn Minh An',
                program_name: 'Lớp Chồi (3-4 tuổi)',
                enrollment_date: '2024-09-01',
                status: 'active',
            },
            {
                learner_name: 'Nguyễn Thị Kim Anh',
                program_name: 'Lớp Chồi (3-4 tuổi)',
                enrollment_date: '2024-09-01',
                status: 'active',
            },
            {
                learner_name: 'Trần Văn Bảo',
                program_name: 'Lớp Chồi (3-4 tuổi)',
                enrollment_date: '2024-09-01',
                status: 'active',
            },
            {
                learner_name: 'Trần Thị Bảo Ngọc',
                program_name: 'Lớp Lá (4-5 tuổi)',
                enrollment_date: '2024-09-01',
                status: 'active',
            },
            {
                learner_name: 'Phạm Thị Lan Anh',
                program_name: 'Lớp Lá (4-5 tuổi)',
                enrollment_date: '2024-09-01',
                status: 'active',
            },
            {
                learner_name: 'Lê Hoàng Minh',
                program_name: 'Lớp Hoa (5-6 tuổi)',
                enrollment_date: '2024-09-01',
                status: 'active',
            },
        ],
        learner_photos: [
            {
                learner_name: 'Nguyễn Minh An',
                photo_url: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face',
                photo_type: 'profile',
                is_primary: true,
                quality_score: 0.95,
                face_encoding: { encoding: [0.1, 0.2, 0.3], landmarks: { left_eye: [120, 80], right_eye: [160, 80] } }
            },
            {
                learner_name: 'Trần Thị Bảo Ngọc',
                photo_url: 'https://images.unsplash.com/photo-1503919005314-30d93d07d823?w=400&h=400&fit=crop&crop=face',
                photo_type: 'profile',
                is_primary: true,
                quality_score: 0.92,
                face_encoding: { encoding: [0.2, 0.3, 0.4], landmarks: { left_eye: [115, 85], right_eye: [155, 85] } }
            },
            {
                learner_name: 'Lê Hoàng Minh',
                photo_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
                photo_type: 'profile',
                is_primary: true,
                quality_score: 0.88,
                face_encoding: { encoding: [0.3, 0.4, 0.5], landmarks: { left_eye: [118, 82], right_eye: [158, 82] } }
            },
            {
                learner_name: 'Nguyễn Thị Kim Anh',
                photo_url: 'https://images.unsplash.com/photo-1494790108755-2616c9c9b8e7?w=400&h=400&fit=crop&crop=face',
                photo_type: 'profile',
                is_primary: true,
                quality_score: 0.94,
                face_encoding: { encoding: [0.4, 0.5, 0.6], landmarks: { left_eye: [122, 78], right_eye: [162, 78] } }
            },
            {
                learner_name: 'Trần Văn Bảo',
                photo_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
                photo_type: 'profile',
                is_primary: true,
                quality_score: 0.90,
                face_encoding: { encoding: [0.5, 0.6, 0.7], landmarks: { left_eye: [116, 84], right_eye: [156, 84] } }
            },
            {
                learner_name: 'Phạm Thị Lan Anh',
                photo_url: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
                photo_type: 'profile',
                is_primary: true,
                quality_score: 0.93,
                face_encoding: { encoding: [0.6, 0.7, 0.8], landmarks: { left_eye: [119, 81], right_eye: [159, 81] } }
            },
        ],
        attendance: [
            {
                learner_code: 'HM2024001',
                session_date: '2024-12-02',
                status: 'present',
                check_in_time: '2024-12-02T07:45:00+07:00',
                check_out_time: '2024-12-02T16:20:00+07:00',
                notes: 'Bé vui vẻ và tham gia tích cực',
            },
            {
                learner_code: 'HM2024001',
                session_date: '2024-12-01',
                status: 'present',
                check_in_time: '2024-12-01T07:50:00+07:00',
                check_out_time: '2024-12-01T16:15:00+07:00',
            },
            {
                learner_code: 'HM2024001',
                session_date: '2024-11-30',
                status: 'late',
                check_in_time: '2024-11-30T08:15:00+07:00',
                check_out_time: '2024-11-30T16:30:00+07:00',
                notes: 'Đến muộn do kẹt xe',
            },
            {
                learner_code: 'HM2024002',
                session_date: '2024-12-02',
                status: 'present',
                check_in_time: '2024-12-02T07:30:00+07:00',
                check_out_time: '2024-12-02T16:00:00+07:00',
            },
            {
                learner_code: 'HM2024003',
                session_date: '2024-12-02',
                status: 'present',
                check_in_time: '2024-12-02T07:55:00+07:00',
                check_out_time: '2024-12-02T16:30:00+07:00',
            },
            {
                learner_code: 'HM2024004',
                session_date: '2024-12-02',
                status: 'present',
                check_in_time: '2024-12-02T07:40:00+07:00',
                check_out_time: '2024-12-02T16:25:00+07:00',
                notes: 'Bé vui vẻ và năng động',
            },
            {
                learner_code: 'HM2024005',
                session_date: '2024-12-02',
                status: 'late',
                check_in_time: '2024-12-02T08:10:00+07:00',
                check_out_time: '2024-12-02T16:30:00+07:00',
                notes: 'Đến muộn do gia đình có việc',
            },
            {
                learner_code: 'HM2024006',
                session_date: '2024-12-02',
                status: 'present',
                check_in_time: '2024-12-02T07:35:00+07:00',
                check_out_time: '2024-12-02T16:20:00+07:00',
            },
            {
                learner_code: 'HM2024001',
                session_date: '2024-12-03',
                status: 'present',
                check_in_time: '2024-12-03T07:45:00+07:00',
                check_out_time: '2024-12-03T16:15:00+07:00',
            },
            {
                learner_code: 'HM2024004',
                session_date: '2024-12-03',
                status: 'absent',
                notes: 'Bé bị ốm',
            },
            {
                learner_code: 'HM2024005',
                session_date: '2024-12-03',
                status: 'present',
                check_in_time: '2024-12-03T07:50:00+07:00',
                check_out_time: '2024-12-03T16:25:00+07:00',
            },
        ],
        fees: [
            {
                learner_code: 'HM2024001',
                fee_type: 'tuition',
                amount: 4000000,
                due_date: '2024-12-05',
                description: 'Học phí tháng 12/2024',
                status: 'pending',
            },
            {
                learner_code: 'HM2024001',
                fee_type: 'meal',
                amount: 800000,
                due_date: '2024-12-05',
                description: 'Tiền ăn tháng 12/2024',
                status: 'pending',
            },
            {
                learner_code: 'HM2024001',
                fee_type: 'tuition',
                amount: 4000000,
                due_date: '2024-11-05',
                description: 'Học phí tháng 11/2024',
                status: 'paid',
                payment_date: '2024-11-03',
                payment_method: 'bank_transfer',
            },
            {
                learner_code: 'HM2024002',
                fee_type: 'tuition',
                amount: 4500000,
                due_date: '2024-12-05',
                description: 'Học phí tháng 12/2024',
                status: 'pending',
            },
            {
                learner_code: 'HM2024003',
                fee_type: 'tuition',
                amount: 5000000,
                due_date: '2024-12-05',
                description: 'Học phí tháng 12/2024',
                status: 'paid',
                payment_date: '2024-12-01',
                payment_method: 'zalopay',
            },
            {
                learner_code: 'HM2024004',
                fee_type: 'tuition',
                amount: 4000000,
                due_date: '2024-12-05',
                description: 'Học phí tháng 12/2024',
                status: 'pending',
            },
            {
                learner_code: 'HM2024004',
                fee_type: 'meal',
                amount: 800000,
                due_date: '2024-12-05',
                description: 'Tiền ăn tháng 12/2024',
                status: 'pending',
            },
            {
                learner_code: 'HM2024005',
                fee_type: 'tuition',
                amount: 4000000,
                due_date: '2024-12-05',
                description: 'Học phí tháng 12/2024',
                status: 'paid',
                payment_date: '2024-11-30',
                payment_method: 'bank_transfer',
            },
            {
                learner_code: 'HM2024006',
                fee_type: 'tuition',
                amount: 4500000,
                due_date: '2024-12-05',
                description: 'Học phí tháng 12/2024',
                status: 'overdue',
            },
        ],
        events: [
            {
                title: 'Họp phụ huynh đầu năm học',
                description: 'Họp phụ huynh để thông báo kế hoạch năm học mới và trao đổi về chương trình giáo dục',
                start_datetime: '2024-12-20T19:00:00+07:00',
                end_datetime: '2024-12-20T21:00:00+07:00',
                location: 'Hội trường trường',
                event_type: 'meeting',
                max_participants: 100,
                registration_deadline: '2024-12-18T23:59:59+07:00',
                status: 'active',
            },
            {
                title: 'Biểu diễn Giáng sinh',
                description: 'Chương trình biểu diễn Giáng sinh của các bé với sự tham gia của phụ huynh',
                start_datetime: '2024-12-24T09:00:00+07:00',
                end_datetime: '2024-12-24T11:00:00+07:00',
                location: 'Sân trường',
                event_type: 'performance',
                max_participants: 200,
                registration_deadline: '2024-12-22T23:59:59+07:00',
                status: 'active',
            },
            {
                title: 'Dã ngoại cuối năm',
                description: 'Chuyến dã ngoại cuối năm học tại công viên Tao Đàn',
                start_datetime: '2024-12-28T08:00:00+07:00',
                end_datetime: '2024-12-28T16:00:00+07:00',
                location: 'Công viên Tao Đàn',
                event_type: 'field_trip',
                max_participants: 50,
                registration_deadline: '2024-12-25T23:59:59+07:00',
                status: 'active',
            },
        ],
        account_themes: [
            {
                theme_id: '********-0000-4000-a000-********0001',
                theme_name: 'Education Theme',
                mini_app_id: '3423472366583461276',
                primary_color: '#4CAF50',
                secondary_color: '#FFC107',
                logo_url: '/images/mockup/education/hoa-mai-logo.svg'
            },
        ],
    },
};
