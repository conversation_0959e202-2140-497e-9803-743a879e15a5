import {
  Box,
  Building2,
  CreditCard,
  Database,
  FileDown,
  FolderTree,
  GraduationCap,
  LayoutDashboard,
  Plug,
  Settings,
  ShoppingCart,
  Smartphone,
  Ticket,
  UserCircle,
  Users,
  Zap,
} from 'lucide-react';

import { NavigationConfigSchema } from '@kit/ui/navigation-schema';

import featureFlagsConfig from '~/config/feature-flags.config';
import pathsConfig from '~/config/paths.config';

const iconClasses = 'w-4';

// Hàm lấy danh sách menu
const getRoutes = (account: string) => [
  {
    label: 'common:routes.application',
    children: [
      {
        label: 'common:routes.dashboard',
        path: pathsConfig.app.accountHome.replace('[account]', account),
        Icon: <LayoutDashboard className={iconClasses} />,
        end: true,
        // Dashboard luôn hiển thị cho tất cả người dùng
      },
      {
        label: 'common:routes.orders',
        path: pathsConfig.app.accountOrders.replace('[account]', account),
        Icon: <ShoppingCart className={iconClasses} />,
        accessCheck: {
          permission: 'orders.manage',
        },
      },

      {
        label: 'common:routes.customers', // Khách hàng
        path: pathsConfig.app.accountCustomers.replace('[account]', account),
        Icon: <UserCircle className={iconClasses} />,
        accessCheck: {
          permission: 'customers.manage',
        },
      },
      {
        label: 'cdp:title', // Customer Data Platform
        path: `/home/<USER>/cdp`,
        Icon: <Database className={iconClasses} />,
        accessCheck: {
          permission: 'customers.manage',
        },
      },
      {
        label: 'common:routes.education', // Education Platform
        path: `/home/<USER>/education`,
        Icon: <GraduationCap className={iconClasses} />,
        accessCheck: {
          permission: 'education.manage',
        },
      },
    ],
  },
  {
    label: 'common:routes.settings',
    collapsible: false,
    children: [
      {
        label: 'common:routes.categories',
        path: createPath(pathsConfig.app.accountCategories, account),
        Icon: <FolderTree className={iconClasses} />,
        accessCheck: {
          permission: 'categories.manage',
        },
      },
      {
        label: 'common:routes.products',
        path: createPath(pathsConfig.app.accountProducts, account),
        Icon: <Box className={iconClasses} />,
        accessCheck: {
          permission: 'products.manage',
        },
      },
      {
        label: 'common:routes.flash_sales',
        path: createPath(pathsConfig.app.accountFlashSales, account),
        Icon: <Zap className={iconClasses} />,
        accessCheck: {
          permission: 'products.manage',
        },
      },
      {
        label: 'common:routes.vouchers',
        path: createPath(pathsConfig.app.accountVouchers, account),
        Icon: <Ticket className={iconClasses} />,
        accessCheck: {
          permission: 'products.manage',
        },
      },
      {
        label: 'common:routes.branch', // Chi nhánh
        path: pathsConfig.app.accountBranch.replace('[account]', account),
        Icon: <Building2 className={iconClasses} />,
        accessCheck: {
          permission: 'branches.manage',
        },
      },
      {
        label: 'common:routes.integrations', // Tích hợp
        path: pathsConfig.app.accountIntegrations.replace('[account]', account),
        Icon: <Plug className={iconClasses} />,
        accessCheck: {
          permission: 'integrations.manage',
        },
      },
      {
        label: 'common:routes.miniapp',
        path: pathsConfig.app.accountMiniApp.replace('[account]', account),
        Icon: <Smartphone className={iconClasses} />,
        accessCheck: {
          permission: 'miniapps.manage',
        },
      },
      {
        label: 'common:routes.import_export',
        path: createPath(pathsConfig.app.accountImportExport, account),
        Icon: <FileDown className={iconClasses} />,
        accessCheck: {
          permission: 'data.manage',
        },
      },
      {
        label: 'common:routes.settings',
        path: createPath(pathsConfig.app.accountSettings, account),
        Icon: <Settings className={iconClasses} />,
        accessCheck: {
          permission: 'settings.manage',
        },
      },
      {
        label: 'common:routes.members',
        path: createPath(pathsConfig.app.accountMembers, account),
        Icon: <Users className={iconClasses} />,
        accessCheck: {
          permission: 'members.manage',
        },
      },
      featureFlagsConfig.enableTeamAccountBilling
        ? {
            label: 'common:routes.billing',
            path: createPath(pathsConfig.app.accountBilling, account),
            Icon: <CreditCard className={iconClasses} />,
            accessCheck: {
              permission: 'billing.manage',
            },
          }
        : undefined,
    ].filter(Boolean),
  },
];

export function getTeamAccountSidebarConfig(account: string) {
  return NavigationConfigSchema.parse({
    routes: getRoutes(account),
    style: process.env.NEXT_PUBLIC_TEAM_NAVIGATION_STYLE,
    sidebarCollapsed: process.env.NEXT_PUBLIC_TEAM_SIDEBAR_COLLAPSED,
    sidebarCollapsedStyle: process.env.NEXT_PUBLIC_SIDEBAR_COLLAPSED_STYLE,
  });
}

function createPath(path: string, account: string) {
  return path.replace('[account]', account);
}
