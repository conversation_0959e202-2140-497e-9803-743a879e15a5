import { z } from 'zod';





// Define currency format types
export type CurrencyPosition = 'prefix' | 'suffix';
export type CurrencyCode = 'VND' | 'USD' | 'EUR';

// Define currency schema
const CurrencyConfigSchema = z.object({
  defaultCurrency: z.enum(['VND', 'USD', 'EUR'], {
    description: 'The default currency used throughout the application',
    required_error: 'Provide the variable NEXT_PUBLIC_DEFAULT_CURRENCY',
  }),

  // Currency display settings
  currencyFormats: z.record(z.object({
    code: z.enum(['VND', 'USD', 'EUR']),
    symbol: z.string(),
    position: z.enum(['prefix', 'suffix']),
    decimalPlaces: z.number().int().min(0),
    thousandsSeparator: z.string(),
    decimalSeparator: z.string(),
  })),

  // Feature flags for currency display
  enableMultipleCurrencies: z.boolean({
    description: 'Enable support for multiple currencies',
    required_error: 'Provide the variable NEXT_PUBLIC_ENABLE_MULTIPLE_CURRENCIES',
  }),

  enableCurrencySelection: z.boolean({
    description: 'Enable currency selection by users',
    required_error: 'Provide the variable NEXT_PUBLIC_ENABLE_CURRENCY_SELECTION',
  }),
});

// Parse configuration from environment variables
const currencyConfig = CurrencyConfigSchema.parse({
  defaultCurrency: (process.env.NEXT_PUBLIC_DEFAULT_CURRENCY as CurrencyCode) || 'VND',

  currencyFormats: {
    VND: {
      code: 'VND',
      symbol: '₫',
      position: 'suffix',
      decimalPlaces: 0,
      thousandsSeparator: ',',
      decimalSeparator: '.',
    },
    USD: {
      code: 'USD',
      symbol: '$',
      position: 'prefix',
      decimalPlaces: 2,
      thousandsSeparator: ',',
      decimalSeparator: '.',
    },
    EUR: {
      code: 'EUR',
      symbol: '€',
      position: 'prefix',
      decimalPlaces: 2,
      thousandsSeparator: '.',
      decimalSeparator: ',',
    },
  },

  enableMultipleCurrencies: getBoolean(
    process.env.NEXT_PUBLIC_ENABLE_MULTIPLE_CURRENCIES,
    false,
  ),

  enableCurrencySelection: getBoolean(
    process.env.NEXT_PUBLIC_ENABLE_CURRENCY_SELECTION,
    false,
  ),
} satisfies z.infer<typeof CurrencyConfigSchema>);

export default currencyConfig;

// Helper function to parse boolean values
function getBoolean(value: unknown, defaultValue: boolean) {
  if (typeof value === 'string') {
    return value === 'true';
  }

  return defaultValue;
}
