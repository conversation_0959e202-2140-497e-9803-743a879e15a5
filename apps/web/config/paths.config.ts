import { z } from 'zod';

const PathsSchema = z.object({
  auth: z.object({
    signIn: z.string().min(1),
    signUp: z.string().min(1),
    verifyMfa: z.string().min(1),
    callback: z.string().min(1),
    passwordReset: z.string().min(1),
    passwordUpdate: z.string().min(1),
  }),
  app: z.object({
    home: z.string().min(1),
    personalAccountSettings: z.string().min(1),
    personalAccountBilling: z.string().min(1),
    personalAccountBillingReturn: z.string().min(1),
    accountHome: z.string().min(1),
    accountSettings: z.string().min(1),
    accountBilling: z.string().min(1),
    accountMembers: z.string().min(1),
    accountBillingReturn: z.string().min(1),
    joinTeam: z.string().min(1),
    accountCategories: z.string().min(1),
    accountProducts: z.string().min(1),
    accountOrders: z.string().min(1),
    accountIntegrations: z.string().min(1), // Tích hợp
    accountMiniApp: z.string().min(1), // Ứng dụng mini
    accountCustomers: z.string().min(1), // Khách hàng
    accountBranch: z.string().min(1), // Chi nhánh
    accountFlashSales: z.string().min(1), // Flash Sales
    accountVouchers: z.string().min(1), // Vouchers
    accountImportExport: z.string().min(1), // Import/Export
    accountImportExportImport: z.string().min(1), // Nhập dữ liệu
    accountImportExportExport: z.string().min(1), // Xuất dữ liệu
    documentation: z.string().min(1), // Tài liệu hướng dẫn
    notifications: z.string().optional(), // Thông báo
  }),
});

const pathsConfig = PathsSchema.parse({
  auth: {
    signIn: '/auth/sign-in',
    signUp: '/auth/sign-up',
    verifyMfa: '/auth/verify',
    callback: '/auth/callback',
    passwordReset: '/auth/password-reset',
    passwordUpdate: '/update-password',
  },
  app: {
    home: '/home',
    personalAccountSettings: '/home/<USER>',
    personalAccountBilling: '/home/<USER>',
    personalAccountBillingReturn: '/home/<USER>/return',
    accountHome: '/home/<USER>',
    accountSettings: `/home/<USER>/settings`,
    accountBilling: `/home/<USER>/billing`,
    accountMembers: `/home/<USER>/members`,
    accountBillingReturn: `/home/<USER>/billing/return`,
    joinTeam: '/join',
    accountCategories: '/home/<USER>/categories',
    accountProducts: '/home/<USER>/products',
    accountOrders: '/home/<USER>/orders',
    accountIntegrations: '/home/<USER>/integrations', // Quản lý các tích hợp bên thứ 3
    accountMiniApp: '/home/<USER>/miniapp', // Quản lý ứng dụng mini
    accountCustomers: '/home/<USER>/customers', // Quản lý khách hàng
    accountBranch: '/home/<USER>/branch', // Quản lý chi nhánh
    accountFlashSales: '/home/<USER>/flash-sales', // Quản lý flash sales
    accountVouchers: '/home/<USER>/vouchers', // Quản lý vouchers
    accountImportExport: '/home/<USER>/import-export', // Nhập/Xuất dữ liệu
    accountImportExportImport: '/home/<USER>/import-export/import', // Nhập dữ liệu
    accountImportExportExport: '/home/<USER>/import-export/export', // Xuất dữ liệu
    documentation: '/docs', // Tài liệu hướng dẫn
    notifications: '/notifications', // Thông báo
  },
} satisfies z.infer<typeof PathsSchema>);

export default pathsConfig;
