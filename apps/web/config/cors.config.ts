import { z } from 'zod';

import appConfig from './app.config';

const CorsConfigSchema = z.object({
  allowedOrigins: z.array(z.string()),
  allowedMethods: z.string(),
  allowedHeaders: z.string(),
  requireCorsPaths: z.array(z.string()),
});

// Function to generate localhost origins for common development ports
const generateLocalhostOrigins = () => {
  // Get custom ports from environment variable
  const customPorts = process.env.CORS_LOCALHOST_PORTS?.split(',').map(p => parseInt(p.trim())) || [];

  // Default common development ports
  const defaultPorts = [3000, 3001, 3002, 3999, 2999, 4000, 5000, 8000, 8080, 9000];

  // Combine custom and default ports
  const allPorts = [...new Set([...customPorts, ...defaultPorts])];

  return allPorts.map(port => `http://localhost:${port}`);
};

const corsConfig = CorsConfigSchema.parse({
  allowedOrigins: [
    'https://h5.zdn.vn',
    'https://h5.zadn.vn',
    'https://app.minapp.vn',
    'zbrowser://h5.zdn.vn',
    ...generateLocalhostOrigins(), // Support multiple localhost ports
    appConfig.production ? appConfig.url : 'http://localhost:2999',
  ],
  allowedMethods: 'GET, POST, OPTIONS',
  allowedHeaders: 'Content-Type, Authorization',
  requireCorsPaths: ['/api/auth/zalo', '/api/auth/refresh', '/api/auth/orders'],
});

export default corsConfig;
