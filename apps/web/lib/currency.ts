import currencyConfig, { CurrencyCode } from '../config/currency.config';

/**
 * Format a number as currency according to the specified currency code
 * @param amount - The amount to format
 * @param currencyCode - The currency code (defaults to the application's default currency)
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  currencyCode: CurrencyCode = currencyConfig.defaultCurrency
): string {
  // Get currency format configuration
  const format = currencyConfig.currencyFormats[currencyCode];
  
  if (!format) {
    console.warn(`Currency format not found for code: ${currencyCode}`);
    return amount.toString();
  }
  
  // Format the number with proper decimal places
  const formattedNumber = amount.toLocaleString('en-US', {
    minimumFractionDigits: format.decimalPlaces,
    maximumFractionDigits: format.decimalPlaces,
    useGrouping: true,
  });
  
  // Replace default separators with configured ones
  const formattedAmount = formattedNumber
    .replace(/,/g, '~') // Temporarily replace commas
    .replace(/\./g, format.decimalSeparator)
    .replace(/~/g, format.thousandsSeparator);
  
  // Apply prefix or suffix based on configuration
  if (format.position === 'prefix') {
    return `${format.symbol}${formattedAmount}`;
  } else {
    return `${formattedAmount}${format.symbol}`;
  }
}

/**
 * Parse a currency string into a number
 * @param currencyString - The currency string to parse
 * @param currencyCode - The currency code (defaults to the application's default currency)
 * @returns Parsed number
 */
export function parseCurrency(
  currencyString: string,
  currencyCode: CurrencyCode = currencyConfig.defaultCurrency
): number {
  const format = currencyConfig.currencyFormats[currencyCode];
  
  if (!format) {
    console.warn(`Currency format not found for code: ${currencyCode}`);
    return parseFloat(currencyString) || 0;
  }
  
  // Remove currency symbol
  let cleanString = currencyString.replace(format.symbol, '').trim();
  
  // Replace configured separators with standard ones for parsing
  cleanString = cleanString
    .replace(new RegExp(`\\${format.thousandsSeparator}`, 'g'), '')
    .replace(new RegExp(`\\${format.decimalSeparator}`, 'g'), '.');
  
  // Parse the string to a number
  return parseFloat(cleanString) || 0;
}
