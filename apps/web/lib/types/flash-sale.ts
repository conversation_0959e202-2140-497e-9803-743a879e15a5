import { Database } from '../database.types';

export type FlashSale = Database['public']['Tables']['flash_sales']['Row'];
export type FlashSaleProduct = Database['public']['Tables']['flash_sale_products']['Row'];

export type FlashSaleWithProducts = FlashSale & {
  products: (FlashSaleProduct & {
    product: Database['public']['Tables']['products']['Row'];
  })[];
};

export type FlashSaleProductWithDetails = FlashSaleProduct & {
  product: Database['public']['Tables']['products']['Row'];
  flash_sale: FlashSale;
};

export type CreateFlashSaleParams = {
  name: string;
  description?: string;
  start_time: Date;
  end_time: Date;
  status?: FlashSaleStatus;
  products: {
    product_id: string;
    discount_percentage: number;
    quantity_limit?: number;
  }[];
};

export type FlashSaleStatus = 'draft' | 'active' | 'ended' | 'cancelled';
