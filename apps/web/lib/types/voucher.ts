import { Database } from '../database.types';

export type Voucher = Database['public']['Tables']['vouchers']['Row'];
export type VoucherRedemption = Database['public']['Tables']['voucher_redemptions']['Row'];
export type VoucherCustomerPhone = Database['public']['Tables']['voucher_customer_phones']['Row'];

export type VoucherWithRedemptions = Voucher & {
  redemptions: VoucherRedemption[];
};

export type VoucherWithCustomerPhones = Voucher & {
  customer_phones: VoucherCustomerPhone[];
};

export type VoucherRedemptionWithDetails = VoucherRedemption & {
  voucher: Voucher;
  order: Database['public']['Tables']['customer_orders']['Row'];
};

export type CreateVoucherParams = {
  code: string;
  name: string;
  description?: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: number;
  min_order_value?: number;
  max_discount_value?: number;
  max_uses?: number;
  start_date: Date;
  end_date: Date;
  // Advanced restrictions
  is_customer_specific?: boolean;
  usage_limit_per_customer?: number;
  first_time_customers_only?: boolean;
  min_previous_orders?: number;
  excluded_product_ids?: string[];
  included_product_ids?: string[];
  excluded_category_ids?: string[];
  included_category_ids?: string[];
};

export type VoucherStatus = 'active' | 'expired' | 'disabled';

export type VoucherCustomerPhoneOperation = {
  voucher_id: string;
  phone_numbers: string[];
};
