/*
    Mock modules for development.

    This file is used to mock the modules that are not needed during development (unless they are used).
    It allows the development server to load faster by not loading the modules that are not needed.
 */

// Turnstile
export const Turnstile = undefined;
export const TurnstileProps = {};

// Baselime
export const useBaselimeRum = undefined;
export const BaselimeRum = undefined;

// Sentry
export const captureException =() => ({});
export const captureEvent = () => ({});
export const setUser = () => ({});
export const init = () => ({});

// Stripe
export const loadStripe = () => Promise.resolve({
  elements: () => ({
    create: () => ({}),
    getElement: () => ({}),
    update: () => ({}),
  }),
  createToken: () => Promise.resolve({ token: null, error: null }),
  createSource: () => Promise.resolve({ source: null, error: null }),
  createPaymentMethod: () => Promise.resolve({ paymentMethod: null, error: null }),
  confirmCardPayment: () => Promise.resolve({ paymentIntent: null, error: null }),
  confirmCardSetup: () => Promise.resolve({ setupIntent: null, error: null }),
  retrievePaymentIntent: () => Promise.resolve({ paymentIntent: null, error: null }),
  retrieveSetupIntent: () => Promise.resolve({ setupIntent: null, error: null }),
});

// Stripe React Components
export const EmbeddedCheckout = () => null;
export const EmbeddedCheckoutProvider = ({ children }) => children;
