import { NextResponse } from 'next/server';

import corsConfig from '../config/cors.config';

// Hàm helper để lấy path từ URL
const getPathFromUrl = (url: string): string => {
  const urlObj = new URL(url);
  return urlObj.pathname;
};

export const createCorsResponse = (
  request: Request,
  data: any,
  status: number,
): NextResponse => {
  const path = getPathFromUrl(request.url); // Lấy path từ request.url
  const origin = request.headers.get('Origin'); // Lấy origin từ headers

  // Kiểm tra xem path có bắt đầu bằng /api/ hoặc nằm trong danh sách requireCorsPaths không
  const isApiPath = path.startsWith('/api/');
  const isRequiredCorsPath = corsConfig.requireCorsPaths.includes(path);
  // Nếu không phải API path và không yêu cầu CORS, trả về response bình thường
  if (!isApiPath && !isRequiredCorsPath) {
    return NextResponse.json(data, { status });
  }

  const allowedOrigin =
    origin && corsConfig.allowedOrigins.includes(origin)
      ? origin
      : corsConfig.allowedOrigins[0];

  return new NextResponse(JSON.stringify(data), {
    status,
    headers: {
      'Access-Control-Allow-Origin': allowedOrigin,
      'Access-Control-Allow-Methods': corsConfig.allowedMethods,
      'Access-Control-Allow-Headers': corsConfig.allowedHeaders,
    },
  });
};

export const handleCorsOptions = (request: Request) => {
  const path = getPathFromUrl(request.url);

  if (!corsConfig.requireCorsPaths.includes(path)) {
    return new Response(null, { status: 204 });
  }
  return createCorsResponse(request, {}, 200);
};
