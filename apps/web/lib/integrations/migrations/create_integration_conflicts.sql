-- Create integration_conflicts table
CREATE TABLE IF NOT EXISTS public.integration_conflicts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  account_id UUID NOT NULL REFERENCES public.accounts(id),
  platform TEXT NOT NULL,
  resource TEXT NOT NULL,
  local JSONB NOT NULL,
  remote JSONB NOT NULL,
  resolution TEXT,
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolved_by <PERSON><PERSON><PERSON>,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add index for faster queries
CREATE INDEX IF NOT EXISTS integration_conflicts_account_id_idx ON public.integration_conflicts(account_id);
CREATE INDEX IF NOT EXISTS integration_conflicts_platform_idx ON public.integration_conflicts(platform);
CREATE INDEX IF NOT EXISTS integration_conflicts_resource_idx ON public.integration_conflicts(resource);

-- Add exclusive_resources column to integrations table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'integrations' AND column_name = 'exclusive_resources'
  ) THEN
    ALTER TABLE public.integrations ADD COLUMN exclusive_resources JSONB;
  END IF;
END
$$;

-- Add comment
COMMENT ON TABLE public.integration_conflicts IS 'Stores conflicts that occur during bi-directional sync between the system and third-party platforms';
