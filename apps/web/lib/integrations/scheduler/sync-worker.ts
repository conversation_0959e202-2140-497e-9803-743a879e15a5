/**
 * Sync Worker
 * Thực thi các lịch trình đồng bộ tự động
 */

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';
import { createConnectorFromIntegrationId } from '~/lib/integrations/connectors/connector-factory';

/**
 * Lớp SyncWorker
 * Xử lý các tác vụ đồng bộ tự động
 */
export class SyncWorker {
  private logger: any;
  private supabase: any;

  constructor() {
    this.initialize();
  }

  /**
   * Khởi tạo worker
   */
  private async initialize() {
    this.logger = await getLogger();
    this.supabase = getSupabaseServerClient<Database>();
  }

  /**
   * Chạy worker
   */
  async run() {
    try {
      this.logger.info('Starting sync worker');

      // L<PERSON>y danh sách lịch tr<PERSON>nh cần thực thi
      const schedules = await this.getSchedulesToRun();
      this.logger.info({ count: schedules.length }, 'Found schedules to run');

      // Thực thi từng lịch trình
      for (const schedule of schedules) {
        await this.processSchedule(schedule);
      }

      this.logger.info('Sync worker completed');
    } catch (error) {
      this.logger.error({ error }, 'Error running sync worker');
    }
  }

  /**
   * Lấy danh sách lịch trình cần thực thi
   */
  private async getSchedulesToRun(): Promise<any[]> {
    try {
      // Lấy các lịch trình đã đến thời gian thực thi
      const { data: schedules, error } = await this.supabase
        .from('integration_schedules')
        .select('*, integration:integrations(*)')
        .eq('enabled', true)
        .lte('next_run_at', new Date().toISOString())
        .order('next_run_at', { ascending: true });

      if (error) {
        this.logger.error({ error }, 'Error fetching schedules');
        return [];
      }

      return schedules || [];
    } catch (error) {
      this.logger.error({ error }, 'Error getting schedules to run');
      return [];
    }
  }

  /**
   * Xử lý một lịch trình
   * @param schedule Lịch trình cần xử lý
   */
  private async processSchedule(schedule: any): Promise<void> {
    try {
      this.logger.info(
        { scheduleId: schedule.id, integrationId: schedule.integration_id },
        'Processing schedule'
      );

      // Cập nhật trạng thái lịch trình
      await this.updateScheduleStatus(schedule.id, 'running');

      // Kiểm tra thời gian thực thi
      const now = new Date();
      const startTime = schedule.start_time ? this.parseTimeString(schedule.start_time) : null;
      const endTime = schedule.end_time ? this.parseTimeString(schedule.end_time) : null;

      // Kiểm tra nếu nằm ngoài thời gian cho phép
      if (startTime && endTime) {
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        const currentTimeInMinutes = currentHour * 60 + currentMinute;
        const startTimeInMinutes = startTime.hour * 60 + startTime.minute;
        const endTimeInMinutes = endTime.hour * 60 + endTime.minute;

        if (
          currentTimeInMinutes < startTimeInMinutes ||
          currentTimeInMinutes > endTimeInMinutes
        ) {
          this.logger.info(
            { scheduleId: schedule.id, currentTime: `${currentHour}:${currentMinute}`, startTime: schedule.start_time, endTime: schedule.end_time },
            'Schedule outside of allowed time window'
          );
          
          // Cập nhật next_run_at
          await this.updateNextRunTime(schedule);
          return;
        }
      }

      // Kiểm tra trạng thái integration
      if (
        !schedule.integration ||
        schedule.integration.status !== 'connected' ||
        !schedule.integration.enabled
      ) {
        this.logger.warn(
          { scheduleId: schedule.id, integrationStatus: schedule.integration?.status },
          'Integration not available'
        );
        
        // Cập nhật next_run_at
        await this.updateNextRunTime(schedule);
        return;
      }

      // Tạo sync log
      const { data: syncLog, error: syncLogError } = await this.supabase
        .from('integration_sync_logs')
        .insert({
          integration_id: schedule.integration_id,
          resource_type: schedule.resource_type,
          status: 'in_progress',
          started_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (syncLogError || !syncLog) {
        this.logger.error({ error: syncLogError }, 'Error creating sync log');
        await this.updateScheduleStatus(schedule.id, 'error', 'Failed to create sync log');
        return;
      }

      // Tạo connector
      const connector = await createConnectorFromIntegrationId(schedule.integration_id);
      if (!connector) {
        this.logger.error({ integrationId: schedule.integration_id }, 'Failed to create connector');
        
        // Cập nhật sync log
        await this.updateSyncLogStatus(syncLog.id, 'error', 'Failed to create connector');
        
        // Cập nhật lịch trình
        await this.updateScheduleStatus(schedule.id, 'error', 'Failed to create connector');
        return;
      }

      try {
        // Lấy dữ liệu từ bên thứ 3
        const data = await connector.getData({
          resourceType: schedule.resource_type,
          strategy: 'incremental',
          limit: 100, // Giới hạn số lượng để tránh quá tải
        });

        // Xác định bảng dữ liệu
        const table = this.getTableForResource(schedule.resource_type);
        
        // Đồng bộ dữ liệu
        let successCount = 0;
        let failedCount = 0;
        
        for (const item of data) {
          try {
            // Lấy mappings
            const { data: mappings } = await this.supabase
              .from('integration_mappings')
              .select('*')
              .eq('integration_id', schedule.integration_id)
              .eq('resource_type', schedule.resource_type)
              .eq('is_active', true);
            
            // Áp dụng mapping
            const mappedData = this.applyMapping(item, mappings || []);
            
            // Thêm account_id vào dữ liệu
            mappedData.account_id = schedule.integration.account_id;
            
            // Kiểm tra xem đã có dữ liệu chưa
            const { data: existingData, error: fetchError } = await this.supabase
              .from(table)
              .select('id')
              .eq('id', mappedData.id)
              .eq('account_id', schedule.integration.account_id)
              .maybeSingle();
            
            if (fetchError && fetchError.code !== 'PGRST116') {
              throw fetchError;
            }
            
            let saveResult;
            if (existingData) {
              // Cập nhật dữ liệu hiện có
              saveResult = await this.supabase
                .from(table)
                .update(mappedData)
                .eq('id', mappedData.id)
                .eq('account_id', schedule.integration.account_id);
            } else {
              // Tạo dữ liệu mới
              saveResult = await this.supabase.from(table).insert(mappedData);
            }
            
            if (saveResult.error) {
              // Lưu thông tin lỗi
              await this.supabase.from('integration_sync_items').insert({
                sync_log_id: syncLog.id,
                external_id: item.id,
                resource_type: schedule.resource_type,
                status: 'error',
                error_message: saveResult.error.message,
                raw_data: item,
                processed_data: mappedData,
              });
              
              failedCount++;
            } else {
              // Lưu thông tin thành công
              await this.supabase.from('integration_sync_items').insert({
                sync_log_id: syncLog.id,
                external_id: item.id,
                internal_id: mappedData.id,
                resource_type: schedule.resource_type,
                status: 'success',
                raw_data: item,
                processed_data: mappedData,
              });
              
              successCount++;
            }
          } catch (itemError: any) {
            // Lưu thông tin lỗi
            await this.supabase.from('integration_sync_items').insert({
              sync_log_id: syncLog.id,
              external_id: item.id || 'unknown',
              resource_type: schedule.resource_type,
              status: 'error',
              error_message: itemError.message,
              raw_data: item,
            });
            
            failedCount++;
          }
        }
        
        // Cập nhật sync log
        const status = failedCount > 0 
          ? (successCount > 0 ? 'partial' : 'error') 
          : 'success';
        
        await this.updateSyncLogStatus(
          syncLog.id, 
          status, 
          failedCount > 0 ? `Failed to sync ${failedCount} items` : null,
          {
            items_processed: data.length,
            items_created: successCount,
            items_failed: failedCount,
          }
        );
        
        // Cập nhật integration
        await this.supabase
          .from('integrations')
          .update({
            last_sync_at: new Date().toISOString(),
            error_message: failedCount > 0 ? `Failed to sync ${failedCount} items` : null,
          })
          .eq('id', schedule.integration_id);
        
        // Cập nhật lịch trình
        await this.updateScheduleStatus(
          schedule.id, 
          'success', 
          null, 
          {
            items_processed: data.length,
            items_created: successCount,
            items_failed: failedCount,
          }
        );
      } catch (error: any) {
        this.logger.error({ error, scheduleId: schedule.id }, 'Error syncing data');
        
        // Cập nhật sync log
        await this.updateSyncLogStatus(syncLog.id, 'error', error.message);
        
        // Cập nhật integration
        await this.supabase
          .from('integrations')
          .update({
            error_message: `Sync failed: ${error.message}`,
          })
          .eq('id', schedule.integration_id);
        
        // Cập nhật lịch trình
        await this.updateScheduleStatus(schedule.id, 'error', error.message);
      }

      // Cập nhật next_run_at
      await this.updateNextRunTime(schedule);
    } catch (error) {
      this.logger.error({ error, scheduleId: schedule.id }, 'Error processing schedule');
      await this.updateScheduleStatus(schedule.id, 'error', error.message);
    }
  }

  /**
   * Cập nhật trạng thái lịch trình
   * @param scheduleId ID của lịch trình
   * @param status Trạng thái mới
   * @param errorMessage Thông báo lỗi (nếu có)
   * @param stats Thống kê đồng bộ
   */
  private async updateScheduleStatus(
    scheduleId: string,
    status: 'running' | 'success' | 'error',
    errorMessage: string | null = null,
    stats: {
      items_processed?: number;
      items_created?: number;
      items_failed?: number;
    } = {}
  ): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('integration_schedules')
        .update({
          last_run_status: status,
          last_run_error: errorMessage,
          last_run_at: new Date().toISOString(),
          ...stats,
        })
        .eq('id', scheduleId);

      if (error) {
        this.logger.error({ error, scheduleId }, 'Error updating schedule status');
      }
    } catch (error) {
      this.logger.error({ error, scheduleId }, 'Error updating schedule status');
    }
  }

  /**
   * Cập nhật thời gian chạy tiếp theo của lịch trình
   * @param schedule Lịch trình
   */
  private async updateNextRunTime(schedule: any): Promise<void> {
    try {
      // Tính toán thời gian chạy tiếp theo dựa trên cron expression
      const nextRunAt = this.calculateNextRunTime(schedule.cron_expression);
      
      const { error } = await this.supabase
        .from('integration_schedules')
        .update({
          next_run_at: nextRunAt.toISOString(),
        })
        .eq('id', schedule.id);

      if (error) {
        this.logger.error({ error, scheduleId: schedule.id }, 'Error updating next run time');
      }
    } catch (error) {
      this.logger.error({ error, scheduleId: schedule.id }, 'Error updating next run time');
    }
  }

  /**
   * Tính toán thời gian chạy tiếp theo dựa trên cron expression
   * @param cronExpression Cron expression
   * @returns Thời gian chạy tiếp theo
   */
  private calculateNextRunTime(cronExpression: string): Date {
    try {
      // Đây là một triển khai đơn giản
      // Trong thực tế, bạn nên sử dụng thư viện như cron-parser
      
      // Phân tích cron expression
      const parts = cronExpression.split(' ');
      
      // Mặc định là 1 giờ sau
      const nextRun = new Date();
      nextRun.setHours(nextRun.getHours() + 1);
      
      return nextRun;
    } catch (error) {
      this.logger.error({ error, cronExpression }, 'Error calculating next run time');
      
      // Mặc định là 1 giờ sau nếu có lỗi
      const nextRun = new Date();
      nextRun.setHours(nextRun.getHours() + 1);
      
      return nextRun;
    }
  }

  /**
   * Cập nhật trạng thái sync log
   * @param syncLogId ID của sync log
   * @param status Trạng thái mới
   * @param errorMessage Thông báo lỗi (nếu có)
   * @param stats Thống kê đồng bộ
   */
  private async updateSyncLogStatus(
    syncLogId: string,
    status: string,
    errorMessage: string | null = null,
    stats: {
      items_processed?: number;
      items_created?: number;
      items_updated?: number;
      items_failed?: number;
    } = {}
  ): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('integration_sync_logs')
        .update({
          status,
          error_message: errorMessage,
          completed_at: new Date().toISOString(),
          ...stats,
        })
        .eq('id', syncLogId);

      if (error) {
        this.logger.error({ error, syncLogId }, 'Error updating sync log status');
      }
    } catch (error) {
      this.logger.error({ error, syncLogId }, 'Error updating sync log status');
    }
  }

  /**
   * Áp dụng mapping cho dữ liệu
   * @param data Dữ liệu gốc
   * @param mappings Mapping
   */
  private applyMapping(data: any, mappings: any[]): any {
    const result: Record<string, any> = {};

    // Nếu không có mapping, trả về dữ liệu gốc
    if (!mappings || mappings.length === 0) {
      return data;
    }

    // Áp dụng từng mapping
    for (const mapping of mappings) {
      const { source_field, target_field, transform_function } = mapping;

      // Lấy giá trị từ source field
      let value = data[source_field];

      // Áp dụng hàm biến đổi nếu có
      if (transform_function && value !== undefined) {
        try {
          // Thực thi hàm biến đổi (cẩn thận với eval!)
          // Trong môi trường production, nên sử dụng cách an toàn hơn
          const transformFn = new Function('value', transform_function);
          value = transformFn(value);
        } catch (error) {
          console.error(
            `Error applying transform function for ${source_field}:`,
            error
          );
        }
      }

      // Gán giá trị cho target field
      if (value !== undefined) {
        result[target_field] = value;
      }
    }

    return result;
  }

  /**
   * Lấy bảng tương ứng với loại resource
   * @param resourceType Loại resource
   */
  private getTableForResource(resourceType: string): string {
    switch (resourceType) {
      case 'products':
        return 'products';
      case 'orders':
        return 'customer_orders';
      case 'customers':
        return 'customers';
      case 'categories':
        return 'categories';
      case 'branches':
        return 'branches';
      case 'vouchers':
        return 'vouchers';
      default:
        throw new Error(`Unsupported resource type: ${resourceType}`);
    }
  }

  /**
   * Parse chuỗi thời gian (HH:MM)
   * @param timeString Chuỗi thời gian
   */
  private parseTimeString(timeString: string): { hour: number; minute: number } | null {
    try {
      const [hourStr, minuteStr] = timeString.split(':');
      const hour = parseInt(hourStr, 10);
      const minute = parseInt(minuteStr, 10);
      
      if (isNaN(hour) || isNaN(minute) || hour < 0 || hour > 23 || minute < 0 || minute > 59) {
        return null;
      }
      
      return { hour, minute };
    } catch (error) {
      return null;
    }
  }
}
