import crypto from 'crypto';

// Lấy secret key từ biến môi trường hoặc sử dụng giá trị mặc định
const SECRET_KEY = process.env.INTEGRATION_CREDENTIALS_SECRET || 'integration-credentials-secret-key';
const IV_LENGTH = 16; // AES block size

/**
 * Mã hóa dữ liệu
 * @param data Dữ liệu cần mã hóa
 * @returns Dữ liệu đã mã hóa
 */
export function encrypt(data: any): string {
  try {
    // Tạo IV ngẫu nhiên
    const iv = crypto.randomBytes(IV_LENGTH);
    
    // Tạo key từ secret key
    const key = crypto.createHash('sha256').update(String(SECRET_KEY)).digest('base64').substring(0, 32);
    
    // Tạo cipher
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
    
    // Mã hóa dữ liệu
    const jsonData = JSON.stringify(data);
    let encrypted = cipher.update(jsonData, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Kết hợp IV và dữ liệu đã mã hóa
    return iv.toString('hex') + ':' + encrypted;
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Giải mã dữ liệu
 * @param encryptedData Dữ liệu đã mã hóa
 * @returns Dữ liệu đã giải mã
 */
export function decrypt(encryptedData: string): any {
  try {
    // Tách IV và dữ liệu đã mã hóa
    const parts = encryptedData.split(':');
    if (parts.length !== 2) {
      throw new Error('Invalid encrypted data format');
    }
    
    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];
    
    // Tạo key từ secret key
    const key = crypto.createHash('sha256').update(String(SECRET_KEY)).digest('base64').substring(0, 32);
    
    // Tạo decipher
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
    
    // Giải mã dữ liệu
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    // Parse JSON
    return JSON.parse(decrypted);
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
}
