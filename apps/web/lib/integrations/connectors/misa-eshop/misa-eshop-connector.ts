import { AbstractConnector } from '../abstract-connector';
import { ResourceType, SyncAction } from '../connector.interface';

/**
 * Interface cho thông tin xác thực Misa eShop
 */
export interface MisaEshopCredentials {
  api_key: string;
  tenant_id: string;
  company_id: string;
  baseUrl?: string;
}

/**
 * Interface cho cấu hình Misa eShop
 */
export interface MisaEshopConfig {
  webhook_url?: string;
  webhook_secret?: string;
}

/**
 * Connector cho Misa eShop
 */
export class MisaEshopConnector extends AbstractConnector<MisaEshopCredentials, MisaEshopConfig> {
  /**
   * Xác thực với Misa eShop API
   * @param credentials Thông tin xác thực
   */
  async authenticate(credentials: MisaEshopCredentials): Promise<boolean> {
    try {
      // Kiểm tra access_token bằng cách gọi API lấy thông tin cửa hàng
      const testResponse = await this.callMisaEshopAPI('/api/v1/companies', 'GET');

      // Log kết quả để debug
      this.logger.info({ testResponse }, 'Authentication response');

      return !!testResponse.data;
    } catch (error) {
      this.logger.error({ error, credentials }, 'Error authenticating with Misa eShop API');
      throw error;
    }
  }

  /**
   * Lấy schema của dữ liệu từ Misa eShop
   * @param resourceType Loại resource
   */
  async getSchema(resourceType: string): Promise<string[]> {
    try {
      switch (resourceType) {
        case ResourceType.PRODUCTS:
          return await this.getProductFields();
        case ResourceType.ORDERS:
          return await this.getOrderFields();
        case ResourceType.CUSTOMERS:
          return await this.getCustomerFields();
        default:
          throw new Error(`Unsupported resource type: ${resourceType}`);
      }
    } catch (error) {
      this.logger.error({ error, resourceType }, 'Error getting schema from Misa eShop');
      throw error;
    }
  }

  /**
   * Lấy dữ liệu từ Misa eShop
   * @param params Tham số cho việc lấy dữ liệu
   */
  async getData(params: {
    resourceType: string;
    strategy?: 'full' | 'incremental';
    filters?: Record<string, any>;
    limit?: number;
    offset?: number;
  }): Promise<any[]> {
    const { resourceType, strategy = 'full', filters = {}, limit, offset } = params;

    try {
      // Chuẩn bị tham số API
      const apiParams: Record<string, any> = {
        ...filters,
      };

      if (limit) {
        apiParams.pageSize = limit;
      }

      if (offset) {
        apiParams.pageIndex = Math.floor(offset / (limit || 20)) + 1;
      }

      // Nếu là incremental, thêm tham số updated_after
      if (strategy === 'incremental' && !filters.updatedAfter) {
        const oneDayAgo = new Date();
        oneDayAgo.setDate(oneDayAgo.getDate() - 1);
        apiParams.updatedAfter = oneDayAgo.toISOString();
      }

      // Gọi API tương ứng
      switch (resourceType) {
        case ResourceType.PRODUCTS:
          return await this.getProducts(apiParams);
        case ResourceType.ORDERS:
          return await this.getOrders(apiParams);
        case ResourceType.CUSTOMERS:
          return await this.getCustomers(apiParams);
        default:
          throw new Error(`Unsupported resource type: ${resourceType}`);
      }
    } catch (error) {
      this.logger.error({ error, resourceType }, 'Error getting data from Misa eShop');
      throw error;
    }
  }

  /**
   * Gửi dữ liệu đến Misa eShop
   * @param params Tham số cho việc gửi dữ liệu
   */
  async sendData(params: {
    resourceType: string;
    action: 'create' | 'update' | 'delete';
    data: any;
  }): Promise<any> {
    const { resourceType, action, data } = params;

    try {
      // Xác định endpoint và method dựa trên resource type và action
      let endpoint: string;
      let method: string;

      switch (resourceType) {
        case ResourceType.ORDERS:
          if (action === SyncAction.CREATE) {
            endpoint = '/api/v1/orders';
            method = 'POST';
          } else if (action === SyncAction.UPDATE) {
            endpoint = `/api/v1/orders/${data.id}`;
            method = 'PUT';
          } else if (action === SyncAction.DELETE) {
            endpoint = `/api/v1/orders/${data.id}`;
            method = 'DELETE';
          } else {
            throw new Error(`Unsupported action: ${action}`);
          }
          break;
        case ResourceType.PRODUCTS:
          if (action === SyncAction.CREATE) {
            endpoint = '/api/v1/products';
            method = 'POST';
          } else if (action === SyncAction.UPDATE) {
            endpoint = `/api/v1/products/${data.id}`;
            method = 'PUT';
          } else if (action === SyncAction.DELETE) {
            endpoint = `/api/v1/products/${data.id}`;
            method = 'DELETE';
          } else {
            throw new Error(`Unsupported action: ${action}`);
          }
          break;
        case ResourceType.CUSTOMERS:
          if (action === SyncAction.CREATE) {
            endpoint = '/api/v1/customers';
            method = 'POST';
          } else if (action === SyncAction.UPDATE) {
            endpoint = `/api/v1/customers/${data.id}`;
            method = 'PUT';
          } else if (action === SyncAction.DELETE) {
            endpoint = `/api/v1/customers/${data.id}`;
            method = 'DELETE';
          } else {
            throw new Error(`Unsupported action: ${action}`);
          }
          break;
        default:
          throw new Error(`Sending data not supported for resource type: ${resourceType}`);
      }

      // Gọi API
      return await this.callMisaEshopAPI(endpoint, method, data);
    } catch (error) {
      this.logger.error({ error, resourceType, action }, 'Error sending data to Misa eShop');
      throw error;
    }
  }

  /**
   * Chuyển tiếp yêu cầu API đến Misa eShop (bắc cầu API)
   * @param resourceType Loại resource
   * @param action Hành động
   * @param payload Dữ liệu gửi đi
   */
  async proxyRequest(
    resourceType: string,
    action: string,
    payload: any
  ): Promise<any> {
    try {
      // Xác định endpoint và method dựa trên resource type và action
      let endpoint: string;
      let method: string;

      switch (resourceType) {
        case 'loyalty':
          if (action === 'getPoints') {
            endpoint = '/api/v1/loyalty/points';
            method = 'GET';
          } else if (action === 'addPoints') {
            endpoint = '/api/v1/loyalty/points';
            method = 'POST';
          } else {
            throw new Error(`Unsupported action: ${action}`);
          }
          break;
        case 'voucher':
          if (action === 'validate') {
            endpoint = '/api/v1/vouchers/validate';
            method = 'POST';
          } else if (action === 'redeem') {
            endpoint = '/api/v1/vouchers/redeem';
            method = 'POST';
          } else {
            throw new Error(`Unsupported action: ${action}`);
          }
          break;
        default:
          throw new Error(`Unsupported resource type: ${resourceType}`);
      }

      // Gọi API
      return await this.callMisaEshopAPI(endpoint, method, payload);
    } catch (error) {
      this.logger.error(
        { error, resourceType, action },
        'Error proxying request to Misa eShop'
      );
      throw error;
    }
  }

  /**
   * Xử lý webhook từ Misa eShop
   * @param payload Dữ liệu webhook
   */
  async handleWebhook(payload: any): Promise<void> {
    try {
      const { event, data } = payload;
      this.logger.info({ event }, 'Received webhook from Misa eShop');

      // Xử lý các sự kiện khác nhau
      switch (event) {
        case 'product.created':
        case 'product.updated':
          await this.handleProductWebhook(data);
          break;

        case 'order.created':
        case 'order.updated':
        case 'order.status_changed':
          await this.handleOrderWebhook(data);
          break;

        case 'customer.created':
        case 'customer.updated':
          await this.handleCustomerWebhook(data);
          break;

        default:
          this.logger.warn({ event }, 'Unhandled webhook event from Misa eShop');
      }
    } catch (error) {
      this.logger.error({ error, payload }, 'Error handling Misa eShop webhook');
      throw error;
    }
  }

  /**
   * Lấy tên platform
   */
  protected getPlatformName(): string {
    return 'misa-eshop';
  }

  /**
   * Gọi API Misa eShop
   * @param endpoint Endpoint
   * @param method Method
   * @param body Body
   */
  private async callMisaEshopAPI(
    endpoint: string,
    method: string = 'GET',
    body?: any
  ): Promise<any> {
    try {
      const { api_key, tenant_id, company_id } = this.credentials;
      const baseUrl = this.credentials.baseUrl || 'https://api.eshop.misa.vn';

      // Xây dựng URL
      const url = `${baseUrl}${endpoint}`;

      // Log URL để debug
      this.logger.info({ url, method }, 'Calling Misa eShop API');

      // Chuẩn bị headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': api_key,
        'X-Tenant-ID': tenant_id,
        'X-Company-ID': company_id
      };

      // Chuẩn bị options
      const options: RequestInit = {
        method,
        headers,
        cache: 'no-store',
      };

      // Thêm body cho các request không phải GET
      if (body && method !== 'GET') {
        options.body = JSON.stringify(body);
      }

      // Thêm query params cho GET request
      let finalUrl = url;
      if (method === 'GET' && body) {
        const queryParams = new URLSearchParams();
        Object.entries(body).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString());
          }
        });

        const separator = url.includes('?') ? '&' : '?';
        finalUrl = `${url}${separator}${queryParams.toString()}`;
      }

      // Log URL cuối cùng
      this.logger.info({ finalUrl }, 'Final URL');

      // Gọi API
      const response = await fetch(finalUrl, options);

      // Log response status để debug
      this.logger.info(
        {
          status: response.status,
          statusText: response.statusText,
        },
        'Misa eShop API response'
      );

      // Đọc response text trước
      const responseText = await response.text();

      // Kiểm tra nếu response rỗng hoặc không phải JSON
      if (!responseText || responseText.trim() === '') {
        this.logger.error({ url }, 'Misa eShop API returned empty response');

        // Nếu đây là môi trường development, trả về dữ liệu giả lập
        if (process.env.NODE_ENV === 'development') {
          this.logger.info('Using mock data for development');
          return { data: [{ id: '1', name: 'Test Product' }] };
        }

        throw new Error('Misa eShop API returned empty response');
      }

      // Parse JSON
      let jsonResponse;
      try {
        jsonResponse = JSON.parse(responseText);
      } catch (parseError) {
        this.logger.error({ parseError, responseText }, 'Failed to parse JSON response');

        // Nếu đây là môi trường development, trả về dữ liệu giả lập
        if (process.env.NODE_ENV === 'development') {
          this.logger.info('Using mock data for development');
          return { data: [{ id: '1', name: 'Test Product' }] };
        }

        throw new Error(`Failed to parse JSON response: ${responseText.substring(0, 100)}...`);
      }

      // Kiểm tra lỗi từ API
      if (!response.ok) {
        this.logger.error({ error: jsonResponse, endpoint }, 'Misa eShop API request failed');
        throw new Error(`Misa eShop API request failed: ${jsonResponse.message || response.statusText}`);
      }

      return jsonResponse;
    } catch (error) {
      this.logger.error({ error, endpoint }, 'Error calling Misa eShop API');

      // Nếu đây là môi trường development, trả về dữ liệu giả lập
      if (process.env.NODE_ENV === 'development') {
        this.logger.info('Using mock data for development');
        return { data: [{ id: '1', name: 'Test Product' }] };
      }

      throw error;
    }
  }

  /**
   * Lấy danh sách sản phẩm từ Misa eShop
   * @param params Tham số
   */
  private async getProducts(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Gọi API với params được truyền trực tiếp
      const response = await this.callMisaEshopAPI('/api/v1/products', 'GET', params);

      // Log kết quả để debug
      this.logger.info({ dataLength: response.data?.length || 0 }, 'Products fetched');

      // Trả về danh sách sản phẩm từ response
      return response.data || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching products from Misa eShop');
      throw error;
    }
  }

  /**
   * Lấy danh sách đơn hàng từ Misa eShop
   * @param params Tham số
   */
  private async getOrders(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Gọi API với params được truyền trực tiếp
      const response = await this.callMisaEshopAPI('/api/v1/orders', 'GET', params);

      // Log kết quả để debug
      this.logger.info({ dataLength: response.data?.length || 0 }, 'Orders fetched');

      return response.data || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching orders from Misa eShop');
      throw error;
    }
  }

  /**
   * Lấy danh sách khách hàng từ Misa eShop
   * @param params Tham số
   */
  private async getCustomers(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Gọi API với params được truyền trực tiếp
      const response = await this.callMisaEshopAPI('/api/v1/customers', 'GET', params);

      // Log kết quả để debug
      this.logger.info({ dataLength: response.data?.length || 0 }, 'Customers fetched');

      return response.data || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching customers from Misa eShop');
      throw error;
    }
  }

  /**
   * Lấy danh sách trường dữ liệu của sản phẩm
   */
  private async getProductFields(): Promise<string[]> {
    try {
      // Nếu đây là môi trường development, trả về danh sách trường mặc định
      if (process.env.NODE_ENV === 'development') {
        return [
          'id',
          'code',
          'name',
          'description',
          'price',
          'cost_price',
          'sku',
          'barcode',
          'image_url',
          'category_id',
          'category_name',
          'status',
          'stock_quantity',
          'weight',
          'unit',
          'created_at',
          'updated_at',
        ];
      }

      // Lấy một sản phẩm mẫu để xác định các trường
      const products = await this.getProducts({ pageSize: 1 });

      if (products.length === 0) {
        // Nếu không có sản phẩm nào, trả về danh sách trường mặc định
        return [
          'id',
          'code',
          'name',
          'description',
          'price',
          'cost_price',
          'sku',
          'barcode',
          'image_url',
          'category_id',
          'category_name',
          'status',
          'stock_quantity',
          'weight',
          'unit',
          'created_at',
          'updated_at',
        ];
      }

      // Lấy tất cả các key từ sản phẩm đầu tiên
      return Object.keys(products[0]);
    } catch (error) {
      this.logger.error({ error }, 'Error getting product fields from Misa eShop');

      // Nếu có lỗi, trả về danh sách trường mặc định
      return [
        'id',
        'code',
        'name',
        'description',
        'price',
        'cost_price',
        'sku',
        'barcode',
        'image_url',
        'category_id',
        'category_name',
        'status',
        'stock_quantity',
        'weight',
        'unit',
        'created_at',
        'updated_at',
      ];
    }
  }

  /**
   * Lấy danh sách trường dữ liệu của đơn hàng
   */
  private async getOrderFields(): Promise<string[]> {
    try {
      // Nếu đây là môi trường development, trả về danh sách trường mặc định
      if (process.env.NODE_ENV === 'development') {
        return [
          'id',
          'code',
          'customer_id',
          'customer_name',
          'customer_phone',
          'customer_email',
          'total_amount',
          'discount_amount',
          'tax_amount',
          'status',
          'payment_status',
          'payment_method',
          'shipping_address',
          'billing_address',
          'note',
          'created_at',
          'updated_at',
        ];
      }

      // Lấy một đơn hàng mẫu để xác định các trường
      const orders = await this.getOrders({ pageSize: 1 });

      if (orders.length === 0) {
        // Nếu không có đơn hàng nào, trả về danh sách trường mặc định
        return [
          'id',
          'code',
          'customer_id',
          'customer_name',
          'customer_phone',
          'customer_email',
          'total_amount',
          'discount_amount',
          'tax_amount',
          'status',
          'payment_status',
          'payment_method',
          'shipping_address',
          'billing_address',
          'note',
          'created_at',
          'updated_at',
        ];
      }

      // Lấy tất cả các key từ đơn hàng đầu tiên (trừ order_items vì đó là một mảng)
      return Object.keys(orders[0]).filter(key => key !== 'order_items');
    } catch (error) {
      this.logger.error({ error }, 'Error getting order fields from Misa eShop');

      // Nếu có lỗi, trả về danh sách trường mặc định
      return [
        'id',
        'code',
        'customer_id',
        'customer_name',
        'customer_phone',
        'customer_email',
        'total_amount',
        'discount_amount',
        'tax_amount',
        'status',
        'payment_status',
        'payment_method',
        'shipping_address',
        'billing_address',
        'note',
        'created_at',
        'updated_at',
      ];
    }
  }

  /**
   * Lấy danh sách trường dữ liệu của khách hàng
   */
  private async getCustomerFields(): Promise<string[]> {
    try {
      // Nếu đây là môi trường development, trả về danh sách trường mặc định
      if (process.env.NODE_ENV === 'development') {
        return [
          'id',
          'code',
          'name',
          'phone',
          'email',
          'address',
          'city',
          'district',
          'ward',
          'country',
          'postal_code',
          'group_id',
          'group_name',
          'created_at',
          'updated_at',
        ];
      }

      // Lấy một khách hàng mẫu để xác định các trường
      const customers = await this.getCustomers({ pageSize: 1 });

      if (customers.length === 0) {
        // Nếu không có khách hàng nào, trả về danh sách trường mặc định
        return [
          'id',
          'code',
          'name',
          'phone',
          'email',
          'address',
          'city',
          'district',
          'ward',
          'country',
          'postal_code',
          'group_id',
          'group_name',
          'created_at',
          'updated_at',
        ];
      }

      // Lấy tất cả các key từ khách hàng đầu tiên
      return Object.keys(customers[0]);
    } catch (error) {
      this.logger.error({ error }, 'Error getting customer fields from Misa eShop');

      // Nếu có lỗi, trả về danh sách trường mặc định
      return [
        'id',
        'code',
        'name',
        'phone',
        'email',
        'address',
        'city',
        'district',
        'ward',
        'country',
        'postal_code',
        'group_id',
        'group_name',
        'created_at',
        'updated_at',
      ];
    }
  }

  /**
   * Xử lý webhook sản phẩm
   * @param data Dữ liệu webhook
   */
  private async handleProductWebhook(data: any): Promise<void> {
    // Implement xử lý webhook sản phẩm
    this.logger.info({ data }, 'Processing product webhook');
  }

  /**
   * Xử lý webhook đơn hàng
   * @param data Dữ liệu webhook
   */
  private async handleOrderWebhook(data: any): Promise<void> {
    // Implement xử lý webhook đơn hàng
    this.logger.info({ data }, 'Processing order webhook');
  }

  /**
   * Xử lý webhook khách hàng
   * @param data Dữ liệu webhook
   */
  private async handleCustomerWebhook(data: any): Promise<void> {
    // Implement xử lý webhook khách hàng
    this.logger.info({ data }, 'Processing customer webhook');
  }
}
