import { AbstractConnector } from '../abstract-connector';
import { ResourceType, SyncAction } from '../connector.interface';
import { SapoConnectorServer, SapoCredentials as SapoCredentialsServer } from './sapo-connector-server';

/**
 * Interface cho thông tin xác thực <PERSON>po
 */
export interface SapoCredentials {
  api_key: string;
  app_id: string;
  app_secret: string;
  baseUrl?: string;
}

/**
 * Interface cho cấu hình <PERSON>
 */
export interface SapoConfig {
  webhook_url?: string;
  webhook_secret?: string;
}

/**
 * Connector cho Sapo
 */
export class SapoConnector extends AbstractConnector<SapoCredentials, SapoConfig> {
  private sapoServer: SapoConnectorServer;

  constructor(credentials: SapoCredentials, config?: SapoConfig) {
    super(credentials, config);
    this.sapoServer = new SapoConnectorServer(credentials as SapoCredentialsServer);
  }

  /**
   * <PERSON><PERSON><PERSON> thực với <PERSON> API
   * @param credentials Thông tin xác thực
   */
  async authenticate(credentials: SapoCredentials): Promise<boolean> {
    try {
      this.logger.info({ credentials }, 'Authenticating with Sapo API');

      // Sử dụng SapoConnectorServer để kiểm tra kết nối
      const isAuthenticated = await this.sapoServer.testConnection();

      // Placeholder for development
      if (process.env.NODE_ENV === 'development' && !isAuthenticated) {
        this.logger.info('Using mock authentication for development');
        return true;
      }

      return isAuthenticated;
    } catch (error) {
      this.logger.error({ error, credentials }, 'Error authenticating with Sapo API');
      throw error;
    }
  }

  /**
   * Lấy schema của dữ liệu từ Sapo
   * @param resourceType Loại resource
   */
  async getSchema(resourceType: string): Promise<string[]> {
    try {
      this.logger.info({ resourceType }, 'Getting schema from Sapo');

      // Sử dụng SapoConnectorServer để lấy schema
      switch (resourceType) {
        case ResourceType.PRODUCTS:
          return await this.sapoServer.getProductSchema();
        case ResourceType.ORDERS:
          return await this.sapoServer.getOrderSchema();
        case ResourceType.CUSTOMERS:
          return await this.sapoServer.getCustomerSchema();
        default:
          throw new Error(`Unsupported resource type: ${resourceType}`);
      }
    } catch (error) {
      this.logger.error({ error, resourceType }, 'Error getting schema from Sapo');

      // Placeholder for development
      if (process.env.NODE_ENV === 'development') {
        this.logger.info('Using mock schema for development');
        switch (resourceType) {
          case ResourceType.PRODUCTS:
            return [
              'id', 'name', 'description', 'price', 'compare_at_price',
              'sku', 'barcode', 'image_url', 'category_id', 'status',
              'stock_quantity', 'weight', 'dimensions', 'metadata'
            ];
          case ResourceType.ORDERS:
            return [
              'id', 'customer_id', 'order_number', 'total_amount',
              'discount_amount', 'tax_amount', 'status', 'payment_method',
              'payment_status', 'shipping_address', 'billing_address',
              'notes', 'metadata'
            ];
          case ResourceType.CUSTOMERS:
            return [
              'id', 'name', 'email', 'phone', 'address',
              'city', 'province', 'country', 'postal_code',
              'metadata'
            ];
          default:
            throw new Error(`Unsupported resource type: ${resourceType}`);
        }
      }

      throw error;
    }
  }

  /**
   * Lấy dữ liệu từ Sapo
   * @param params Tham số cho việc lấy dữ liệu
   */
  async getData(params: {
    resourceType: string;
    strategy?: 'full' | 'incremental';
    filters?: Record<string, any>;
    limit?: number;
    offset?: number;
  }): Promise<any[]> {
    const { resourceType, strategy = 'full', filters = {}, limit, offset } = params;

    try {
      this.logger.info({ resourceType, strategy, filters }, 'Getting data from Sapo');

      // Chuẩn bị tham số API
      const apiParams: Record<string, any> = {
        ...filters,
      };

      if (limit) {
        apiParams.limit = limit;
      }

      if (offset) {
        apiParams.page = Math.floor(offset / (limit || 50)) + 1;
      }

      // Nếu là incremental, thêm tham số updated_after
      if (strategy === 'incremental' && !filters.updated_after) {
        const oneDayAgo = new Date();
        oneDayAgo.setDate(oneDayAgo.getDate() - 1);
        apiParams.updated_at_min = oneDayAgo.toISOString();
      }

      // Sử dụng SapoConnectorServer để lấy dữ liệu
      switch (resourceType) {
        case ResourceType.PRODUCTS:
          return await this.sapoServer.getProducts(apiParams);
        case ResourceType.ORDERS:
          return await this.sapoServer.getOrders(apiParams);
        case ResourceType.CUSTOMERS:
          return await this.sapoServer.getCustomers(apiParams);
        default:
          throw new Error(`Unsupported resource type: ${resourceType}`);
      }
    } catch (error) {
      this.logger.error({ error, resourceType }, 'Error getting data from Sapo');

      // Placeholder for development
      if (process.env.NODE_ENV === 'development') {
        this.logger.info('Using mock data for development');
        switch (resourceType) {
          case ResourceType.PRODUCTS:
            return [
              { id: '1', name: 'Test Product 1', price: 100000, sku: 'SKU001', stock_quantity: 10 },
              { id: '2', name: 'Test Product 2', price: 200000, sku: 'SKU002', stock_quantity: 20 },
            ];
          case ResourceType.ORDERS:
            return [
              { id: '1', order_number: 'ORD001', customer_id: 'CUST001', total_amount: 300000 },
              { id: '2', order_number: 'ORD002', customer_id: 'CUST002', total_amount: 450000 },
            ];
          case ResourceType.CUSTOMERS:
            return [
              { id: '1', name: 'Customer 1', email: '<EMAIL>', phone: '0123456789' },
              { id: '2', name: 'Customer 2', email: '<EMAIL>', phone: '0987654321' },
            ];
          default:
            return [];
        }
      }

      throw error;
    }
  }

  /**
   * Gửi dữ liệu đến Sapo
   * @param params Tham số cho việc gửi dữ liệu
   */
  async sendData(params: {
    resourceType: string;
    action: 'create' | 'update' | 'delete';
    data: any;
  }): Promise<any> {
    const { resourceType, action, data } = params;

    try {
      this.logger.info({ resourceType, action, data }, 'Sending data to Sapo');

      // Chỉ hỗ trợ orders hiện tại
      if (resourceType === ResourceType.ORDERS) {
        if (action === SyncAction.CREATE) {
          return await this.sapoServer.createOrder(data);
        } else if (action === SyncAction.UPDATE) {
          return await this.sapoServer.updateOrder(data.id, data);
        } else {
          throw new Error(`Action ${action} not supported for orders`);
        }
      } else {
        throw new Error(`Sending data not supported for resource type: ${resourceType}`);
      }
    } catch (error) {
      this.logger.error({ error, resourceType, action }, 'Error sending data to Sapo');

      // Placeholder for development
      if (process.env.NODE_ENV === 'development') {
        this.logger.info('Using mock response for development');
        return { success: true, id: data.id || '123' };
      }

      throw error;
    }
  }

  /**
   * Chuyển tiếp yêu cầu API đến Sapo (bắc cầu API)
   * @param resourceType Loại resource
   * @param action Hành động
   * @param payload Dữ liệu gửi đi
   */
  async proxyRequest(
    resourceType: string,
    action: string,
    payload: any
  ): Promise<any> {
    try {
      // TODO: Implement proxying request to Sapo API
      this.logger.info({ resourceType, action, payload }, 'Proxying request to Sapo');

      // Placeholder for development
      if (process.env.NODE_ENV === 'development') {
        return { success: true, data: { id: '123', name: 'Test' } };
      }

      return { success: true };
    } catch (error) {
      this.logger.error(
        { error, resourceType, action },
        'Error proxying request to Sapo'
      );
      throw error;
    }
  }

  /**
   * Xử lý webhook từ Sapo
   * @param payload Dữ liệu webhook
   */
  async handleWebhook(payload: any): Promise<void> {
    try {
      // TODO: Implement handling webhook from Sapo
      this.logger.info({ payload }, 'Handling webhook from Sapo');
    } catch (error) {
      this.logger.error({ error, payload }, 'Error handling Sapo webhook');
      throw error;
    }
  }

  /**
   * Lấy tên platform
   */
  protected getPlatformName(): string {
    return 'sapo';
  }
}
