/**
 * Sapo API Connector Server
 * Triển khai các hàm gọi API đến Sapo
 */

import { getLogger } from '@kit/shared/logger';

/**
 * Interface cho thông tin xác thực <PERSON>
 */
export interface SapoCredentials {
  api_key: string;
  app_id: string;
  app_secret: string;
  baseUrl?: string;
}

/**
 * Lớp SapoConnectorServer
 * Xử lý các tác vụ gọi API đến Sapo
 */
export class SapoConnectorServer {
  private credentials: SapoCredentials;
  private logger: any;
  private baseUrl: string;

  constructor(credentials: SapoCredentials) {
    this.credentials = credentials;
    this.baseUrl = credentials.baseUrl || 'https://api.sapo.vn';
    this.initializeLogger();
  }

  /**
   * Khởi tạo logger
   */
  private async initializeLogger() {
    this.logger = await getLogger();
  }

  /**
   * Kiểm tra kết nối với <PERSON>po API
   * @returns Promise<boolean> Kết quả kiểm tra
   */
  async testConnection(): Promise<boolean> {
    try {
      // Gọi API để kiểm tra kết nối
      const response = await this.callSapoAPI('/admin/products/count.json', 'GET');
      return !!response;
    } catch (error) {
      this.logger.error({ error }, 'Error testing connection to Sapo API');
      return false;
    }
  }

  /**
   * Lấy danh sách sản phẩm từ Sapo
   * @param params Tham số truy vấn
   * @returns Promise<any[]> Danh sách sản phẩm
   */
  async getProducts(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Xây dựng query params
      const queryParams = new URLSearchParams();
      for (const [key, value] of Object.entries(params)) {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      }

      // Gọi API với params
      const response = await this.callSapoAPI(
        `/admin/products.json?${queryParams.toString()}`,
        'GET'
      );

      // Trả về danh sách sản phẩm
      return response.products || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching products from Sapo');
      throw error;
    }
  }

  /**
   * Lấy danh sách đơn hàng từ Sapo
   * @param params Tham số truy vấn
   * @returns Promise<any[]> Danh sách đơn hàng
   */
  async getOrders(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Xây dựng query params
      const queryParams = new URLSearchParams();
      for (const [key, value] of Object.entries(params)) {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      }

      // Gọi API với params
      const response = await this.callSapoAPI(
        `/admin/orders.json?${queryParams.toString()}`,
        'GET'
      );

      // Trả về danh sách đơn hàng
      return response.orders || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching orders from Sapo');
      throw error;
    }
  }

  /**
   * Lấy danh sách khách hàng từ Sapo
   * @param params Tham số truy vấn
   * @returns Promise<any[]> Danh sách khách hàng
   */
  async getCustomers(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Xây dựng query params
      const queryParams = new URLSearchParams();
      for (const [key, value] of Object.entries(params)) {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      }

      // Gọi API với params
      const response = await this.callSapoAPI(
        `/admin/customers.json?${queryParams.toString()}`,
        'GET'
      );

      // Trả về danh sách khách hàng
      return response.customers || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching customers from Sapo');
      throw error;
    }
  }

  /**
   * Tạo đơn hàng mới trong Sapo
   * @param orderData Dữ liệu đơn hàng
   * @returns Promise<any> Đơn hàng đã tạo
   */
  async createOrder(orderData: any): Promise<any> {
    try {
      // Gọi API để tạo đơn hàng
      const response = await this.callSapoAPI('/admin/orders.json', 'POST', {
        order: orderData,
      });

      // Trả về đơn hàng đã tạo
      return response.order;
    } catch (error) {
      this.logger.error({ error }, 'Error creating order in Sapo');
      throw error;
    }
  }

  /**
   * Cập nhật đơn hàng trong Sapo
   * @param orderId ID đơn hàng
   * @param orderData Dữ liệu đơn hàng
   * @returns Promise<any> Đơn hàng đã cập nhật
   */
  async updateOrder(orderId: string, orderData: any): Promise<any> {
    try {
      // Gọi API để cập nhật đơn hàng
      const response = await this.callSapoAPI(
        `/admin/orders/${orderId}.json`,
        'PUT',
        {
          order: orderData,
        }
      );

      // Trả về đơn hàng đã cập nhật
      return response.order;
    } catch (error) {
      this.logger.error({ error }, 'Error updating order in Sapo');
      throw error;
    }
  }

  /**
   * Lấy schema của sản phẩm từ Sapo
   * @returns Promise<string[]> Danh sách trường
   */
  async getProductSchema(): Promise<string[]> {
    try {
      // Lấy một sản phẩm mẫu để xác định schema
      const products = await this.getProducts({ limit: 1 });
      if (products.length === 0) {
        // Nếu không có sản phẩm nào, trả về schema mặc định
        return [
          'id',
          'name',
          'description',
          'price',
          'compare_at_price',
          'sku',
          'barcode',
          'image_url',
          'category_id',
          'status',
          'stock_quantity',
          'weight',
          'dimensions',
          'metadata',
        ];
      }

      // Lấy tất cả các key từ sản phẩm đầu tiên
      return Object.keys(products[0]);
    } catch (error) {
      this.logger.error({ error }, 'Error getting product schema from Sapo');
      // Trả về schema mặc định nếu có lỗi
      return [
        'id',
        'name',
        'description',
        'price',
        'compare_at_price',
        'sku',
        'barcode',
        'image_url',
        'category_id',
        'status',
        'stock_quantity',
        'weight',
        'dimensions',
        'metadata',
      ];
    }
  }

  /**
   * Lấy schema của đơn hàng từ Sapo
   * @returns Promise<string[]> Danh sách trường
   */
  async getOrderSchema(): Promise<string[]> {
    try {
      // Lấy một đơn hàng mẫu để xác định schema
      const orders = await this.getOrders({ limit: 1 });
      if (orders.length === 0) {
        // Nếu không có đơn hàng nào, trả về schema mặc định
        return [
          'id',
          'order_number',
          'customer_id',
          'total_amount',
          'discount_amount',
          'tax_amount',
          'status',
          'payment_method',
          'payment_status',
          'shipping_address',
          'billing_address',
          'notes',
          'metadata',
        ];
      }

      // Lấy tất cả các key từ đơn hàng đầu tiên
      return Object.keys(orders[0]);
    } catch (error) {
      this.logger.error({ error }, 'Error getting order schema from Sapo');
      // Trả về schema mặc định nếu có lỗi
      return [
        'id',
        'order_number',
        'customer_id',
        'total_amount',
        'discount_amount',
        'tax_amount',
        'status',
        'payment_method',
        'payment_status',
        'shipping_address',
        'billing_address',
        'notes',
        'metadata',
      ];
    }
  }

  /**
   * Lấy schema của khách hàng từ Sapo
   * @returns Promise<string[]> Danh sách trường
   */
  async getCustomerSchema(): Promise<string[]> {
    try {
      // Lấy một khách hàng mẫu để xác định schema
      const customers = await this.getCustomers({ limit: 1 });
      if (customers.length === 0) {
        // Nếu không có khách hàng nào, trả về schema mặc định
        return [
          'id',
          'name',
          'email',
          'phone',
          'address',
          'city',
          'province',
          'country',
          'postal_code',
          'metadata',
        ];
      }

      // Lấy tất cả các key từ khách hàng đầu tiên
      return Object.keys(customers[0]);
    } catch (error) {
      this.logger.error({ error }, 'Error getting customer schema from Sapo');
      // Trả về schema mặc định nếu có lỗi
      return [
        'id',
        'name',
        'email',
        'phone',
        'address',
        'city',
        'province',
        'country',
        'postal_code',
        'metadata',
      ];
    }
  }

  /**
   * Gọi API đến Sapo
   * @param endpoint Endpoint
   * @param method Method
   * @param body Body
   * @returns Promise<any> Kết quả từ API
   */
  private async callSapoAPI(
    endpoint: string,
    method: string = 'GET',
    body?: any
  ): Promise<any> {
    try {
      // Xây dựng URL
      const url = `${this.baseUrl}${endpoint}`;

      // Chuẩn bị headers
      const headers: Record<string, string> = {
        'X-Sapo-Access-Token': this.credentials.api_key,
        'X-Sapo-Application-Id': this.credentials.app_id,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      };

      // Chuẩn bị options
      const options: RequestInit = {
        method,
        headers,
        cache: 'no-store',
      };

      // Thêm body nếu cần
      if (body && method !== 'GET') {
        options.body = JSON.stringify(body);
      }

      // Gọi API
      const response = await fetch(url, options);

      // Kiểm tra response
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Sapo API request failed: ${response.status} ${response.statusText} - ${errorText}`
        );
      }

      // Parse JSON
      const data = await response.json();
      return data;
    } catch (error) {
      this.logger.error({ error, endpoint }, 'Error calling Sapo API');
      throw error;
    }
  }
}
