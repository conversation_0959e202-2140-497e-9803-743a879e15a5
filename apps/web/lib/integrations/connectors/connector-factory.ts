import { Connector, ConnectorFactory } from './connector.interface';
import { getLogger } from '@kit/shared/logger';

/**
 * Factory tạo connector dựa trên loại tích hợp
 */
export class IntegrationConnectorFactory implements ConnectorFactory {
  private logger: any;

  constructor() {
    this.initializeLogger();
  }

  /**
   * Khởi tạo logger
   */
  private async initializeLogger() {
    this.logger = await getLogger();
  }

  /**
   * Tạo connector dựa trên loại tích hợp
   * @param type Loại tích hợp
   * @param credentials Thông tin xác thực
   * @param config Cấu hình
   * @returns Connector
   */
  async createConnector(
    type: string,
    credentials: any,
    config?: any
  ): Promise<Connector | null> {
    try {
      // Tạo connector dựa trên loại tích hợp
      switch (type) {
        case 'ipos':
          const { IPOSConnector } = await import('./ipos/ipos-connector');
          return new IPOSConnector(credentials, config);
        case 'sapo':
          const { SapoConnector } = await import('./sapo/sapo-connector');
          return new SapoConnector(credentials, config);
        case 'kiotviet':
          const { KiotVietConnector } = await import('./kiotviet/kiotviet-connector');
          return new KiotVietConnector(credentials, config);
        case 'misa-eshop':
          const { MisaEshopConnector } = await import('./misa-eshop/misa-eshop-connector');
          return new MisaEshopConnector(credentials, config);
        default:
          this.logger.warn({ type }, 'Unsupported integration type');
          return null;
      }
    } catch (error) {
      this.logger.error({ error, type }, 'Error creating connector');
      return null;
    }
  }
}

/**
 * Singleton instance của connector factory
 */
let connectorFactoryInstance: IntegrationConnectorFactory | null = null;

/**
 * Lấy instance của connector factory
 * @returns ConnectorFactory
 */
export function getConnectorFactory(): ConnectorFactory {
  if (!connectorFactoryInstance) {
    connectorFactoryInstance = new IntegrationConnectorFactory();
  }
  return connectorFactoryInstance;
}

/**
 * Tạo connector từ integration ID
 * @param integrationId ID của integration
 * @returns Promise<Connector>
 */
export async function createConnectorFromIntegrationId(
  integrationId: string
): Promise<Connector | null> {
  try {
    const { getSupabaseServerClient } = await import('@kit/supabase/server-client');
    const supabase = getSupabaseServerClient();
    const logger = await getLogger();

    // Lấy thông tin integration
    const { data: integration, error } = await supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .single();

    if (error || !integration) {
      logger.error({ error, integrationId }, 'Integration not found');
      return null;
    }

    // Tạo connector
    const factory = getConnectorFactory();
    return await factory.createConnector(
      integration.type,
      integration.credentials,
      integration.config
    );
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error, integrationId }, 'Error creating connector from integration ID');
    return null;
  }
}
