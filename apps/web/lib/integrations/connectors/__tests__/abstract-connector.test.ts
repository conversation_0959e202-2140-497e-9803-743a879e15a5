import { AbstractConnector } from '../abstract-connector';
import { ResourceType, SyncAction, SyncDirection, SyncStrategy } from '../connector.interface';

// Mock implementation của AbstractConnector
class MockConnector extends AbstractConnector<any, any> {
  async authenticate(credentials: any): Promise<boolean> {
    return true;
  }

  async getSchema(resourceType: string): Promise<string[]> {
    return ['id', 'name', 'description'];
  }

  async getData(params: {
    resourceType: string;
    strategy?: SyncStrategy;
    filters?: Record<string, any>;
    limit?: number;
    offset?: number;
  }): Promise<any[]> {
    return [{ id: '1', name: 'Test' }];
  }

  async sendData(params: {
    resourceType: string;
    action: SyncAction;
    data: any;
  }): Promise<any> {
    return { success: true };
  }

  async proxyRequest(
    resourceType: string,
    action: string,
    payload: any
  ): Promise<any> {
    return { success: true };
  }

  async handleWebhook(payload: any): Promise<void> {
    // Do nothing
  }

  protected getPlatformName(): string {
    return 'mock';
  }
}

describe('AbstractConnector', () => {
  let connector: MockConnector;

  beforeEach(() => {
    connector = new MockConnector({});
  });

  test('should initialize with credentials', () => {
    expect(connector).toBeDefined();
  });

  test('should return platform name', () => {
    expect(connector.getPlatform()).toBe('mock');
  });

  test('should authenticate successfully', async () => {
    const result = await connector.authenticate({});
    expect(result).toBe(true);
  });

  test('should get schema', async () => {
    const schema = await connector.getSchema(ResourceType.PRODUCTS);
    expect(schema).toEqual(['id', 'name', 'description']);
  });

  test('should get data', async () => {
    const data = await connector.getData({
      resourceType: ResourceType.PRODUCTS,
    });
    expect(data).toEqual([{ id: '1', name: 'Test' }]);
  });

  test('should send data', async () => {
    const result = await connector.sendData({
      resourceType: ResourceType.PRODUCTS,
      action: SyncAction.CREATE,
      data: { name: 'New Product' },
    });
    expect(result).toEqual({ success: true });
  });

  test('should proxy request', async () => {
    const result = await connector.proxyRequest(
      ResourceType.PRODUCTS,
      'get',
      { id: '1' }
    );
    expect(result).toEqual({ success: true });
  });

  test('should handle webhook', async () => {
    await expect(connector.handleWebhook({ event: 'test' })).resolves.not.toThrow();
  });

  test('should determine sync direction', () => {
    expect(connector.getSyncDirection(ResourceType.PRODUCTS, SyncAction.CREATE)).toBe(SyncDirection.OUTBOUND);
    expect(connector.getSyncDirection(ResourceType.PRODUCTS)).toBe(SyncDirection.INBOUND);
  });
});
