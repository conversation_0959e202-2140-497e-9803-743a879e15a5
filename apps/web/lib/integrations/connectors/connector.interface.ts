/**
 * Interface chung cho tất cả các connector tích hợp bên thứ 3
 */
export interface Connector<TCredentials = any, TConfig = any> {
  /**
   * Xác thực với API của bên thứ 3
   * @param credentials Thông tin xác thực
   * @returns Promise<boolean> Kết quả xác thực
   */
  authenticate(credentials: TCredentials): Promise<boolean>;

  /**
   * Lấy schema của dữ liệu từ bên thứ 3
   * @param resourceType Loại resource (products, orders, customers, etc.)
   * @returns Promise<string[]> Danh sách các trường
   */
  getSchema(resourceType: string): Promise<string[]>;

  /**
   * Lấy dữ liệu từ bên thứ 3
   * @param params Tham số cho việc lấy dữ liệu
   * @returns Promise<any[]> Dữ liệu lấy được
   */
  getData(params: {
    resourceType: string;
    strategy?: 'full' | 'incremental';
    filters?: Record<string, any>;
    limit?: number;
    offset?: number;
  }): Promise<any[]>;

  /**
   * Gửi dữ liệu đến bên thứ 3
   * @param params Tham số cho việc gửi dữ liệu
   * @returns Promise<any> Kết quả gửi dữ liệu
   */
  sendData(params: {
    resourceType: string;
    action: 'create' | 'update' | 'delete';
    data: any;
  }): Promise<any>;

  /**
   * Chuyển tiếp yêu cầu API đến bên thứ 3 (bắc cầu API)
   * @param resourceType Loại resource
   * @param action Hành động
   * @param payload Dữ liệu gửi đi
   * @returns Promise<any> Kết quả từ API bên thứ 3
   */
  proxyRequest(resourceType: string, action: string, payload: any): Promise<any>;

  /**
   * Xử lý webhook từ bên thứ 3
   * @param payload Dữ liệu webhook
   * @returns Promise<void>
   */
  handleWebhook(payload: any): Promise<void>;

  /**
   * Kiểm tra xung đột giữa dữ liệu local và remote
   * @param local Dữ liệu local
   * @param remote Dữ liệu remote
   * @param strategy Chiến lược xử lý xung đột
   * @returns Promise<any> Dữ liệu sau khi xử lý xung đột
   */
  resolveConflict(local: any, remote: any, strategy: string): Promise<any>;
}

/**
 * Interface cho factory tạo connector
 */
export interface ConnectorFactory {
  /**
   * Tạo connector dựa trên loại tích hợp
   * @param type Loại tích hợp
   * @param credentials Thông tin xác thực
   * @param config Cấu hình
   * @returns Connector
   */
  createConnector(
    type: string,
    credentials: any,
    config?: any
  ): Connector | null;
}

/**
 * Enum cho các loại resource
 */
export enum ResourceType {
  PRODUCTS = 'products',
  ORDERS = 'orders',
  CUSTOMERS = 'customers',
  CATEGORIES = 'categories',
  BRANCHES = 'branches',
  VOUCHERS = 'vouchers',
  LOYALTY_POINTS = 'loyalty_points',
}

/**
 * Enum cho các hành động đồng bộ
 */
export enum SyncAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
}

/**
 * Enum cho chiến lược xử lý xung đột
 */
export enum ConflictStrategy {
  TIMESTAMP = 'timestamp', // Ưu tiên dữ liệu mới nhất
  LOCAL = 'local', // Ưu tiên dữ liệu local
  REMOTE = 'remote', // Ưu tiên dữ liệu remote
  MANUAL = 'manual', // Người dùng quyết định
}

/**
 * Enum cho chiến lược đồng bộ
 */
export enum SyncStrategy {
  FULL = 'full', // Đồng bộ toàn bộ dữ liệu
  INCREMENTAL = 'incremental', // Đồng bộ dữ liệu mới/cập nhật
}

/**
 * Enum cho hướng đồng bộ
 */
export enum SyncDirection {
  INBOUND = 'inbound', // Từ bên thứ 3 vào hệ thống
  OUTBOUND = 'outbound', // Từ hệ thống ra bên thứ 3
  BIDIRECTIONAL = 'bidirectional', // Hai chiều
}

/**
 * Interface cho cấu hình đồng bộ
 */
export interface SyncConfig {
  direction: SyncDirection;
  permissions: {
    create: boolean;
    update: boolean;
    delete: boolean;
  };
  resources: {
    [key in ResourceType]?: {
      enabled: boolean;
      strategy: SyncStrategy;
      frequency?: string; // Cron expression
    };
  };
  conflict_strategy: ConflictStrategy;
}

/**
 * Interface cho kết quả đồng bộ
 */
export interface SyncResult {
  success: boolean;
  syncLogId: string;
  total: number;
  created: number;
  updated: number;
  failed: number;
  errors?: any[];
}
