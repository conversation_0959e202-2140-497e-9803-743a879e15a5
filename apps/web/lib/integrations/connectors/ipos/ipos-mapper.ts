import { Logger } from '@kit/logger';

/**
 * Mapper cho iPOS
 * Chuyển đổi dữ liệu giữa iPOS và hệ thống
 */
export class IPOSMapper {
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger.child({ service: 'IPOSMapper' });
  }

  /**
   * Map dữ liệu chi nhánh từ iPOS sang hệ thống
   * @param branch Dữ liệu chi nhánh từ iPOS
   * @param accountId ID tài khoản
   */
  mapBranch(branch: any, accountId: string) {
    try {
      return {
        account_id: accountId,
        name: branch.Pos_Name,
        address: branch.Pos_Address,
        phone: branch.Phone_Number,
        latitude: branch.Pos_Latitude,
        longitude: branch.Pos_Longitude,
        external_id: branch.Id.toString(),
        source: 'ipos',
        last_synced_at: new Date(),
        integration_data: JSON.stringify({
          open_time: branch.Open_Time,
          estimate_price: branch.Estimate_Price,
          wifi_password: branch.Wifi_Password,
          is_car_parking: branch.Is_Car_Parking,
          is_visa: branch.Is_Visa,
          delivery_services: branch.Delivery_Services,
          city_name: branch.City_Name
        })
      };
    } catch (error) {
      this.logger.error({ error, branch }, 'Error mapping branch from iPOS');
      throw error;
    }
  }

  /**
   * Map dữ liệu danh mục từ iPOS sang hệ thống
   * @param category Dữ liệu danh mục từ iPOS
   * @param accountId ID tài khoản
   */
  mapCategory(category: any, accountId: string) {
    try {
      return {
        account_id: accountId,
        name: category.name,
        external_id: category.text_id,
        source: 'ipos',
        last_synced_at: new Date(),
        integration_data: JSON.stringify({
          id: category.id,
          sort: category.sort,
          active: category.active,
          store_id: category.store_id
        })
      };
    } catch (error) {
      this.logger.error({ error, category }, 'Error mapping category from iPOS');
      throw error;
    }
  }

  /**
   * Map dữ liệu sản phẩm từ iPOS sang hệ thống
   * @param product Dữ liệu sản phẩm từ iPOS
   * @param accountId ID tài khoản
   * @param categoryId ID danh mục (nếu có)
   */
  mapProduct(product: any, accountId: string, categoryId?: string) {
    try {
      return {
        account_id: accountId,
        name: product.name,
        price: product.ta_price,
        description: product.description || '',
        category_id: categoryId,
        external_id: product.store_item_id,
        source: 'ipos',
        last_synced_at: new Date(product.update_at) || new Date(),
        integration_data: JSON.stringify({
          ots_price: product.ots_price,
          is_featured: product.is_featured,
          allow_take_away: product.allow_take_away,
          allow_self_order: product.allow_self_order,
          time_sale_date_week: product.time_sale_date_week,
          time_sale_hour_day: product.time_sale_hour_day,
          image_url: product.image_url,
          type_id: product.type_id
        })
      };
    } catch (error) {
      this.logger.error({ error, product }, 'Error mapping product from iPOS');
      throw error;
    }
  }

  /**
   * Map dữ liệu khách hàng từ iPOS sang hệ thống
   * @param customer Dữ liệu khách hàng từ iPOS
   * @param accountId ID tài khoản
   */
  mapCustomer(customer: any, accountId: string) {
    try {
      // Xử lý địa chỉ
      let address = '';
      if (customer.address && typeof customer.address === 'object') {
        address = customer.address.full_address || '';
      } else if (typeof customer.address === 'string') {
        address = customer.address;
      }

      return {
        account_id: accountId,
        name: customer.name,
        email: customer.email || '',
        phone: customer.phone_number,
        address: address,
        external_id: customer.membership_id_new || customer.id,
        source: 'ipos',
        last_synced_at: new Date(customer.update_at) || new Date(),
        integration_data: JSON.stringify({
          birthday: customer.birthday,
          gender: customer.gender,
          points: customer.point,
          membership_type_id: customer.membership_type_id,
          membership_type_name: customer.membership_type_name,
          point_amount: customer.point_amount,
          eat_times: customer.eat_times,
          first_eat_date: customer.first_eat_date,
          last_eat_date: customer.last_eat_date
        })
      };
    } catch (error) {
      this.logger.error({ error, customer }, 'Error mapping customer from iPOS');
      throw error;
    }
  }

  /**
   * Map dữ liệu đơn hàng từ iPOS sang hệ thống
   * @param order Dữ liệu đơn hàng từ iPOS
   * @param accountId ID tài khoản
   * @param userId ID người dùng (nếu có)
   * @param branchId ID chi nhánh (nếu có)
   */
  mapOrder(order: any, accountId: string, userId?: string, branchId?: string) {
    try {
      return {
        account_id: accountId,
        user_id: userId,
        branch_id: branchId,
        items: JSON.stringify(order.order_data_item || []),
        total_amount: order.total_amount,
        status: order.status,
        order_type: order.order_type,
        external_id: order.foodbook_code,
        source: 'ipos',
        last_synced_at: new Date(order.updated_at) || new Date(),
        integration_data: JSON.stringify({
          service_charge_amount: order.service_charge_amount,
          vat_tax_amount: order.vat_tax_amount,
          ship_price_real: order.ship_price_real,
          booking_info: order.booking_info,
          note: order.note
        })
      };
    } catch (error) {
      this.logger.error({ error, order }, 'Error mapping order from iPOS');
      throw error;
    }
  }

  /**
   * Map dữ liệu voucher từ iPOS sang hệ thống
   * @param voucher Dữ liệu voucher từ iPOS
   * @param accountId ID tài khoản
   * @param userId ID người dùng (nếu có)
   */
  mapVoucher(voucher: any, accountId: string, userId?: string) {
    try {
      return {
        account_id: accountId,
        user_id: userId,
        name: voucher.voucher_campaign_name,
        description: voucher.voucher_description || '',
        discount_type: voucher.discount_type,
        discount_amount: voucher.discount_amount,
        discount_extra: voucher.discount_extra,
        start_date: new Date(voucher.date_start),
        end_date: new Date(voucher.date_end),
        status: voucher.status,
        external_id: voucher.voucher_code,
        source: 'ipos',
        last_synced_at: new Date(voucher.date_created) || new Date(),
        integration_data: JSON.stringify({
          voucher_campaign_id: voucher.voucher_campaign_id,
          is_all_item: voucher.is_all_item,
          item_type_id_list: voucher.item_type_id_list,
          discount_max: voucher.discount_max,
          is_coupon: voucher.is_coupon,
          only_coupon: voucher.only_coupon
        })
      };
    } catch (error) {
      this.logger.error({ error, voucher }, 'Error mapping voucher from iPOS');
      throw error;
    }
  }

  /**
   * Map dữ liệu khách hàng từ hệ thống sang iPOS
   * @param customer Dữ liệu khách hàng từ hệ thống
   */
  mapCustomerToIPOS(customer: any) {
    try {
      // Parse integration_data nếu có
      let integrationData = {};
      if (customer.integration_data) {
        try {
          integrationData = JSON.parse(customer.integration_data);
        } catch (e) {
          this.logger.warn({ error: e }, 'Failed to parse integration_data');
        }
      }

      return {
        name: customer.name,
        phone_number: customer.phone,
        email: customer.email || '',
        birthday: integrationData.birthday || null,
        gender: integrationData.gender || -1, // -1: không xác định, 0: nữ, 1: nam
        address: {
          full_address: customer.address || ''
        }
      };
    } catch (error) {
      this.logger.error({ error, customer }, 'Error mapping customer to iPOS');
      throw error;
    }
  }

  /**
   * Map dữ liệu đơn hàng từ hệ thống sang iPOS
   * @param order Dữ liệu đơn hàng từ hệ thống
   * @param posId ID chi nhánh iPOS
   * @param membershipId ID thành viên iPOS (nếu có)
   */
  mapOrderToIPOS(order: any, posId: string, membershipId?: string) {
    try {
      // Parse items nếu có
      let items = [];
      if (order.items) {
        try {
          items = JSON.parse(order.items);
        } catch (e) {
          this.logger.warn({ error: e }, 'Failed to parse items');
        }
      }

      // Parse integration_data nếu có
      let integrationData = {};
      if (order.integration_data) {
        try {
          integrationData = JSON.parse(order.integration_data);
        } catch (e) {
          this.logger.warn({ error: e }, 'Failed to parse integration_data');
        }
      }

      return {
        pos_id: posId,
        user_id: membershipId || '',
        order_type: order.order_type || 'DELI', // DELI, STORE, PICK, DELIAT
        order_data_item: items,
        total_amount: order.total_amount,
        service_charge_amount: integrationData.service_charge_amount || 0,
        vat_tax_amount: integrationData.vat_tax_amount || 0,
        ship_price_real: integrationData.ship_price_real || 0,
        note: integrationData.note || '',
        booking_info: integrationData.booking_info || null
      };
    } catch (error) {
      this.logger.error({ error, order }, 'Error mapping order to iPOS');
      throw error;
    }
  }
}
