import { AbstractConnector } from '../abstract-connector';
import { ResourceType, SyncAction } from '../connector.interface';

/**
 * Interface cho thông tin xác thực iPOS
 */
export interface IPOSCredentials {
  access_token: string;
  pos_parent: string;
  pos_id: string;
  baseUrl?: string;
}

/**
 * Interface cho cấu hình iPOS
 */
export interface IPOSConfig {
  webhook_url?: string;
  webhook_secret?: string;
}

/**
 * Connector cho iPOS
 */
export class IPOSConnector extends AbstractConnector<IPOSCredentials, IPOSConfig> {
  /**
   * <PERSON>ác thực với iPOS API
   * @param credentials Thông tin xác thực
   */
  async authenticate(credentials: IPOSCredentials): Promise<boolean> {
    try {
      // Kiểm tra access_token bằng cách gọi API lấy thông tin món ăn
      // Sử dụng URL test đơn giản hơn và giới hạn kết quả
      const testResponse = await this.callIPOSAPI('/ipos/ws/xpartner/v2/items', 'GET', { limit: 1 });

      // Log kết quả để debug
      this.logger.info({ testResponse }, 'Authentication response');

      return !!testResponse.data;
    } catch (error) {
      this.logger.error({ error, credentials }, 'Error authenticating with iPOS API');
      throw error;
    }
  }

  /**
   * Lấy schema của dữ liệu từ iPOS
   * @param resourceType Loại resource
   */
  async getSchema(resourceType: string): Promise<string[]> {
    try {
      switch (resourceType) {
        case ResourceType.PRODUCTS:
          return await this.getProductFields();
        case ResourceType.ORDERS:
          return await this.getOrderFields();
        case ResourceType.CUSTOMERS:
          return await this.getCustomerFields();
        case ResourceType.BRANCHES:
          return await this.getBranchFields();
        case ResourceType.CATEGORIES:
          return await this.getCategoryFields();
        default:
          throw new Error(`Unsupported resource type: ${resourceType}`);
      }
    } catch (error) {
      this.logger.error({ error, resourceType }, 'Error getting schema from iPOS');
      throw error;
    }
  }

  /**
   * Lấy dữ liệu từ iPOS
   * @param params Tham số cho việc lấy dữ liệu
   */
  async getData(params: {
    resourceType: string;
    strategy?: 'full' | 'incremental';
    filters?: Record<string, any>;
    limit?: number;
    offset?: number;
  }): Promise<any[]> {
    const { resourceType, strategy = 'full', filters = {}, limit, offset } = params;

    try {
      // Chuẩn bị tham số API
      const apiParams: Record<string, any> = {
        ...filters,
      };

      if (limit) {
        apiParams.limit = limit;
      }

      if (offset) {
        apiParams.offset = offset;
      }

      // Nếu là incremental, thêm tham số updated_after
      if (strategy === 'incremental' && !filters.updated_after) {
        const oneDayAgo = new Date();
        oneDayAgo.setDate(oneDayAgo.getDate() - 1);
        apiParams.updated_after = oneDayAgo.toISOString();
      }

      // Gọi API tương ứng
      switch (resourceType) {
        case ResourceType.PRODUCTS:
          return await this.getProducts(apiParams);
        case ResourceType.ORDERS:
          return await this.getOrders(apiParams);
        case ResourceType.CUSTOMERS:
          return await this.getCustomers(apiParams);
        case ResourceType.BRANCHES:
          return await this.getBranches(apiParams);
        case ResourceType.CATEGORIES:
          return await this.getCategories(apiParams);
        default:
          throw new Error(`Unsupported resource type: ${resourceType}`);
      }
    } catch (error) {
      this.logger.error({ error, resourceType }, 'Error getting data from iPOS');
      throw error;
    }
  }

  /**
   * Gửi dữ liệu đến iPOS
   * @param params Tham số cho việc gửi dữ liệu
   */
  async sendData(params: {
    resourceType: string;
    action: 'create' | 'update' | 'delete';
    data: any;
  }): Promise<any> {
    const { resourceType, action, data } = params;

    try {
      // Xác định endpoint và method dựa trên resource type và action
      let endpoint: string;
      let method: string;

      switch (resourceType) {
        case ResourceType.ORDERS:
          if (action === SyncAction.CREATE) {
            endpoint = '/ipos/ws/xpartner/v2/orders';
            method = 'POST';
          } else if (action === SyncAction.UPDATE) {
            endpoint = `/ipos/ws/xpartner/v2/orders/${data.id}`;
            method = 'PUT';
          } else if (action === SyncAction.DELETE) {
            throw new Error('Delete operation not supported for orders');
          } else {
            throw new Error(`Unsupported action: ${action}`);
          }
          break;
        case ResourceType.PRODUCTS:
        case ResourceType.CUSTOMERS:
        default:
          throw new Error(`Sending data not supported for resource type: ${resourceType}`);
      }

      // Gọi API
      return await this.callIPOSAPI(endpoint, method, data);
    } catch (error) {
      this.logger.error({ error, resourceType, action }, 'Error sending data to iPOS');
      throw error;
    }
  }

  /**
   * Chuyển tiếp yêu cầu API đến iPOS (bắc cầu API)
   * @param resourceType Loại resource
   * @param action Hành động
   * @param payload Dữ liệu gửi đi
   */
  async proxyRequest(
    resourceType: string,
    action: string,
    payload: any
  ): Promise<any> {
    try {
      // Xác định endpoint và method dựa trên resource type và action
      let endpoint: string;
      let method: string;

      switch (resourceType) {
        case 'loyalty':
          if (action === 'getPoints') {
            endpoint = '/ipos/ws/xpartner/v2/loyalty/points';
            method = 'GET';
          } else if (action === 'addPoints') {
            endpoint = '/ipos/ws/xpartner/v2/loyalty/points';
            method = 'POST';
          } else {
            throw new Error(`Unsupported action: ${action}`);
          }
          break;
        case 'voucher':
          if (action === 'validate') {
            endpoint = '/ipos/ws/xpartner/v2/vouchers/validate';
            method = 'POST';
          } else if (action === 'redeem') {
            endpoint = '/ipos/ws/xpartner/v2/vouchers/redeem';
            method = 'POST';
          } else {
            throw new Error(`Unsupported action: ${action}`);
          }
          break;
        default:
          throw new Error(`Unsupported resource type: ${resourceType}`);
      }

      // Gọi API
      return await this.callIPOSAPI(endpoint, method, payload);
    } catch (error) {
      this.logger.error(
        { error, resourceType, action },
        'Error proxying request to iPOS'
      );
      throw error;
    }
  }

  /**
   * Xử lý webhook từ iPOS
   * @param payload Dữ liệu webhook
   */
  async handleWebhook(payload: any): Promise<void> {
    try {
      const { event_id, event, timestamp, item_changed, item_status_changed } = payload;
      this.logger.info({ event_id, event }, 'Received webhook from iPOS');

      // Xử lý các sự kiện khác nhau
      switch (event) {
        case 'item_changed':
          // Sự kiện khi có item thay đổi thông tin
          if (item_changed) {
            await this.handleItemChangedWebhook(item_changed);
          }
          break;

        case 'item_out_of_stock':
          // Sự kiện khi thay đổi trạng thái item thành OUT_OF_STOCK
          if (item_status_changed) {
            await this.handleItemOutOfStockWebhook(item_status_changed);
          }
          break;

        default:
          this.logger.warn({ event }, 'Unhandled webhook event from iPOS');
      }
    } catch (error) {
      this.logger.error({ error, payload }, 'Error handling iPOS webhook');
      throw error;
    }
  }

  /**
   * Lấy tên platform
   */
  protected getPlatformName(): string {
    return 'ipos';
  }

  /**
   * Gọi API iPOS
   * @param endpoint Endpoint
   * @param method Method
   * @param body Body
   */
  private async callIPOSAPI(
    endpoint: string,
    method: string = 'GET',
    body?: any
  ): Promise<any> {
    try {
      const { access_token, pos_parent, pos_id } = this.credentials;
      const baseUrl = this.credentials.baseUrl || 'https://api.foodbook.vn';

      // Xây dựng URL với các tham số cần thiết
      let url = `${baseUrl}${endpoint}`;

      // Log URL để debug
      this.logger.info({ url, method }, 'Calling iPOS API');

      // Chuẩn bị body request theo đúng tài liệu API
      let requestBody: any = {};

      // Thêm các tham số xác thực vào body
      requestBody.access_token = access_token;
      requestBody.pos_parent = pos_parent;
      requestBody.pos_id = pos_id;

      // Thêm các tham số khác nếu có
      if (body) {
        requestBody = { ...requestBody, ...body };
      }

      // Nếu là GET request, thêm các tham số vào URL
      if (method === 'GET') {
        const queryParams = new URLSearchParams();
        Object.entries(requestBody).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, value.toString());
          }
        });

        const separator = url.includes('?') ? '&' : '?';
        url = `${url}${separator}${queryParams.toString()}`;

        // GET request không có body
        requestBody = undefined;
      }

      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        cache: 'no-store',
      };

      // Thêm body cho các request không phải GET
      if (requestBody && method !== 'GET') {
        options.body = JSON.stringify(requestBody);
      }

      // Log URL cuối cùng
      this.logger.info({ finalUrl: url }, 'Final URL');

      const response = await fetch(url, options);

      // Log response status để debug
      this.logger.info(
        {
          status: response.status,
          statusText: response.statusText,
        },
        'iPOS API response'
      );

      // Đọc response text trước
      const responseText = await response.text();

      // Kiểm tra nếu response rỗng hoặc không phải JSON
      if (!responseText || responseText.trim() === '') {
        this.logger.error({ url }, 'iPOS API returned empty response');

        // Nếu đây là môi trường development, trả về dữ liệu giả lập
        if (process.env.NODE_ENV === 'development') {
          this.logger.info('Using mock data for development');
          return { data: { items: [{ id: '1', name: 'Test Product' }] } };
        }

        throw new Error('iPOS API returned empty response');
      }

      // Parse JSON
      let jsonResponse;
      try {
        jsonResponse = JSON.parse(responseText);
      } catch (parseError) {
        this.logger.error({ parseError, responseText }, 'Failed to parse JSON response');

        // Nếu đây là môi trường development, trả về dữ liệu giả lập
        if (process.env.NODE_ENV === 'development') {
          this.logger.info('Using mock data for development');
          return { data: { items: [{ id: '1', name: 'Test Product' }] } };
        }

        throw new Error(`Failed to parse JSON response: ${responseText.substring(0, 100)}...`);
      }

      // Kiểm tra lỗi từ API
      if (!response.ok) {
        this.logger.error({ error: jsonResponse, endpoint }, 'iPOS API request failed');
        throw new Error(`iPOS API request failed: ${jsonResponse.message || response.statusText}`);
      }

      return jsonResponse;
    } catch (error) {
      this.logger.error({ error, endpoint }, 'Error calling iPOS API');

      // Nếu đây là môi trường development, trả về dữ liệu giả lập
      if (process.env.NODE_ENV === 'development') {
        this.logger.info('Using mock data for development');
        return { data: { items: [{ id: '1', name: 'Test Product' }] } };
      }

      throw error;
    }
  }

  /**
   * Lấy danh sách sản phẩm và danh mục từ iPOS
   * @param posParent ID thương hiệu
   * @param posId ID chi nhánh
   * @param menuType Loại menu (DELI, STORE, PICK)
   */
  async getItems(posParent: string, posId: string, menuType: string = 'DELI') {
    try {
      // Gọi API với params được truyền trực tiếp
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/items', 'GET', {
        pos_parent: posParent,
        pos_id: posId,
        menu_type: menuType
      });

      // Log kết quả để debug
      this.logger.info({
        itemsLength: data.data?.items?.length || 0,
        itemTypesLength: data.data?.item_types?.length || 0
      }, 'Items and categories fetched');

      return data;
    } catch (error) {
      this.logger.error({ error, posParent, posId, menuType }, 'Error fetching items from iPOS');
      throw error;
    }
  }

  /**
   * Lấy danh sách sản phẩm từ iPOS
   * @param params Tham số
   */
  private async getProducts(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Thêm menu_type nếu không có
      if (!params.menu_type) {
        params.menu_type = 'DELI'; // DELI, STORE, ACTIVE, ALL
      }

      // Gọi API với params được truyền trực tiếp
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/items', 'GET', params);

      // Log kết quả để debug
      this.logger.info({ dataLength: data.data?.items?.length || 0 }, 'Products fetched');

      // Trả về danh sách sản phẩm từ response
      return data.data?.items || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching products from iPOS');
      throw error;
    }
  }

  /**
   * Lấy danh sách đơn hàng từ iPOS
   * @param params Tham số
   */
  private async getOrders(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Gọi API với params được truyền trực tiếp
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/orders', 'GET', params);

      // Log kết quả để debug
      this.logger.info({ dataLength: data.data?.length || 0 }, 'Orders fetched');

      return data.data || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching orders from iPOS');
      throw error;
    }
  }

  /**
   * Lấy danh sách khách hàng từ iPOS
   * @param params Tham số
   */
  private async getCustomers(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Gọi API với params được truyền trực tiếp
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/customers', 'GET', params);

      // Log kết quả để debug
      this.logger.info({ dataLength: data.data?.length || 0 }, 'Customers fetched');

      return data.data || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching customers from iPOS');
      throw error;
    }
  }

  /**
   * Lấy danh sách trường dữ liệu của sản phẩm
   */
  private async getProductFields(): Promise<string[]> {
    try {
      // Nếu đây là môi trường development, trả về danh sách trường mặc định
      if (process.env.NODE_ENV === 'development') {
        return [
          'id',
          'name',
          'ta_price',
          'ots_price',
          'sort',
          'store_id',
          'store_item_id',
          'type_id',
          'image_url',
          'description',
          'status',
          'update_at',
        ];
      }

      // Lấy một sản phẩm mẫu để xác định các trường
      const products = await this.getProducts({ limit: 1 });

      if (products.length === 0) {
        // Nếu không có sản phẩm nào, trả về danh sách trường mặc định
        return [
          'id',
          'name',
          'ta_price',
          'ots_price',
          'sort',
          'store_id',
          'store_item_id',
          'type_id',
          'image_url',
          'description',
          'status',
          'update_at',
        ];
      }

      // Lấy tất cả các key từ sản phẩm đầu tiên (loại bỏ customizations và childs)
      const fields = Object.keys(products[0]).filter(
        (key) => key !== 'customizations' && key !== 'childs'
      );
      return fields;
    } catch (error) {
      this.logger.error({ error }, 'Error getting product fields from iPOS');

      // Nếu có lỗi, trả về danh sách trường mặc định
      return [
        'id',
        'name',
        'ta_price',
        'ots_price',
        'sort',
        'store_id',
        'store_item_id',
        'type_id',
        'image_url',
        'description',
        'status',
        'update_at',
      ];
    }
  }

  /**
   * Lấy danh sách trường dữ liệu của đơn hàng
   */
  private async getOrderFields(): Promise<string[]> {
    try {
      // Nếu đây là môi trường development, trả về danh sách trường mặc định
      if (process.env.NODE_ENV === 'development') {
        return [
          'id',
          'order_code',
          'customer_id',
          'customer_name',
          'customer_phone',
          'customer_email',
          'total_amount',
          'discount_amount',
          'tax_amount',
          'status',
          'payment_status',
          'payment_method',
          'note',
          'created_at',
          'updated_at',
        ];
      }

      // Lấy một đơn hàng mẫu để xác định các trường
      const orders = await this.getOrders({ limit: 1 });

      if (orders.length === 0) {
        // Nếu không có đơn hàng nào, trả về danh sách trường mặc định
        return [
          'id',
          'order_code',
          'customer_id',
          'customer_name',
          'customer_phone',
          'customer_email',
          'total_amount',
          'discount_amount',
          'tax_amount',
          'status',
          'payment_status',
          'payment_method',
          'note',
          'created_at',
          'updated_at',
        ];
      }

      // Lấy tất cả các key từ đơn hàng đầu tiên (trừ items vì đó là một mảng)
      const fields = Object.keys(orders[0]).filter((key) => key !== 'items');
      return fields;
    } catch (error) {
      this.logger.error({ error }, 'Error getting order fields from iPOS');

      // Nếu có lỗi, trả về danh sách trường mặc định
      return [
        'id',
        'order_code',
        'customer_id',
        'customer_name',
        'customer_phone',
        'customer_email',
        'total_amount',
        'discount_amount',
        'tax_amount',
        'status',
        'payment_status',
        'payment_method',
        'note',
        'created_at',
        'updated_at',
      ];
    }
  }

  /**
   * Lấy danh sách danh mục từ iPOS
   * @param params Tham số
   */
  private async getCategories(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Thêm menu_type nếu không có
      if (!params.menu_type) {
        params.menu_type = 'DELI'; // DELI, STORE, PICK
      }

      // Gọi API với params được truyền trực tiếp
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/items', 'GET', params);

      // Log kết quả để debug
      this.logger.info({ dataLength: data.data?.item_types?.length || 0 }, 'Categories fetched');

      // Trả về danh sách danh mục từ response
      return data.data?.item_types || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching categories from iPOS');
      throw error;
    }
  }

  /**
   * Lấy danh sách trường dữ liệu của danh mục
   */
  private async getCategoryFields(): Promise<string[]> {
    try {
      // Nếu đây là môi trường development, trả về danh sách trường mặc định
      if (process.env.NODE_ENV === 'development') {
        return [
          'id',
          'name',
          'active',
          'sort',
          'store_id',
          'text_id'
        ];
      }

      // Lấy một danh mục mẫu để xác định các trường
      const categories = await this.getCategories({ limit: 1 });

      if (categories.length === 0) {
        // Nếu không có danh mục nào, trả về danh sách trường mặc định
        return [
          'id',
          'name',
          'active',
          'sort',
          'store_id',
          'text_id'
        ];
      }

      // Lấy tất cả các key từ danh mục đầu tiên
      const fields = Object.keys(categories[0]);
      return fields;
    } catch (error) {
      this.logger.error({ error }, 'Error getting category fields from iPOS');

      // Nếu có lỗi, trả về danh sách trường mặc định
      return [
        'id',
        'name',
        'active',
        'sort',
        'store_id',
        'text_id'
      ];
    }
  }

  /**
   * Lấy danh sách trường dữ liệu của chi nhánh
   */
  private async getBranchFields(): Promise<string[]> {
    try {
      // Nếu đây là môi trường development, trả về danh sách trường mặc định
      if (process.env.NODE_ENV === 'development') {
        return [
          'Id',
          'Pos_Name',
          'Pos_Address',
          'Phone_Number',
          'Pos_Latitude',
          'Pos_Longitude',
          'Open_Time',
          'Estimate_Price',
          'Wifi_Password',
          'Is_Car_Parking',
          'Is_Visa',
          'Delivery_Services',
          'City_Name',
          'Email',
          'Pos_Parent',
          'District_Id',
          'City_Id',
          'Active'
        ];
      }

      // Lấy một chi nhánh mẫu để xác định các trường
      const branches = await this.getBranches({ limit: 1 });

      if (!branches || branches.length === 0) {
        this.logger.warn('No branches found in iPOS');
        return [
          'Id',
          'Pos_Name',
          'Pos_Address',
          'Phone_Number',
          'Pos_Latitude',
          'Pos_Longitude',
          'Open_Time',
          'Estimate_Price',
          'Wifi_Password',
          'Is_Car_Parking',
          'Is_Visa',
          'Delivery_Services',
          'City_Name'
        ];
      }

      // Lấy tất cả các key từ chi nhánh đầu tiên
      const fields = Object.keys(branches[0]);
      return fields;
    } catch (error) {
      this.logger.error({ error }, 'Error getting branch fields from iPOS');

      // Nếu có lỗi, trả về danh sách trường mặc định
      return [
        'Id',
        'Pos_Name',
        'Pos_Address',
        'Phone_Number',
        'Pos_Latitude',
        'Pos_Longitude',
        'Open_Time',
        'Estimate_Price',
        'Wifi_Password',
        'Is_Car_Parking',
        'Is_Visa',
        'Delivery_Services',
        'City_Name'
      ];
    }
  }

  /**
   * Lấy danh sách chi nhánh từ iPOS
   * @param params Tham số cho việc lấy dữ liệu
   */
  private async getBranches(params: Record<string, any> = {}): Promise<any[]> {
    try {
      this.logger.info('Getting branches from iPOS');

      // Gọi API iPOS để lấy danh sách chi nhánh
      const endpoint = '/ipos/ws/xpartner/pos';
      const queryParams = {
        access_token: this.credentials.access_token,
        pos_parent: this.credentials.pos_parent,
        ...params
      };

      const response = await this.callIPOSAPI(endpoint, 'GET', queryParams);

      if (!response.data || !response.data.length) {
        this.logger.warn('No branches found in iPOS');
        return [];
      }

      this.logger.info({ dataLength: response.data.length }, 'Branches fetched');
      return response.data;
    } catch (error) {
      this.logger.error({ error }, 'Error getting branches from iPOS');

      // Nếu đang ở môi trường phát triển, trả về dữ liệu mẫu
      if (process.env.NODE_ENV === 'development') {
        this.logger.info('Using mock data for development');
        return [];
      }

      throw error;
    }
  }

  /**
   * Lấy danh sách trường dữ liệu của khách hàng
   */
  private async getCustomerFields(): Promise<string[]> {
    try {
      // Nếu đây là môi trường development, trả về danh sách trường mặc định
      if (process.env.NODE_ENV === 'development') {
        return [
          'id',
          'name',
          'phone',
          'email',
          'address',
          'city',
          'province',
          'country',
          'postal_code',
          'created_at',
          'updated_at',
        ];
      }

      // Lấy một khách hàng mẫu để xác định các trường
      const customers = await this.getCustomers({ limit: 1 });

      if (customers.length === 0) {
        // Nếu không có khách hàng nào, trả về danh sách trường mặc định
        return [
          'id',
          'name',
          'phone',
          'email',
          'address',
          'city',
          'province',
          'country',
          'postal_code',
          'created_at',
          'updated_at',
        ];
      }

      // Lấy tất cả các key từ khách hàng đầu tiên
      return Object.keys(customers[0]);
    } catch (error) {
      this.logger.error({ error }, 'Error getting customer fields from iPOS');

      // Nếu có lỗi, trả về danh sách trường mặc định
      return [
        'id',
        'name',
        'phone',
        'email',
        'address',
        'city',
        'province',
        'country',
        'postal_code',
        'created_at',
        'updated_at',
      ];
    }
  }

  /**
   * Xử lý webhook item_changed
   * @param data Dữ liệu webhook
   */
  private async handleItemChangedWebhook(data: any): Promise<void> {
    // Implement xử lý webhook item_changed
    // Ví dụ: cập nhật sản phẩm trong database
    this.logger.info({ data }, 'Processing item_changed webhook');
  }

  /**
   * Xử lý webhook item_out_of_stock
   * @param data Dữ liệu webhook
   */
  private async handleItemOutOfStockWebhook(data: any): Promise<void> {
    // Implement xử lý webhook item_out_of_stock
    // Ví dụ: cập nhật trạng thái sản phẩm trong database
    this.logger.info({ data }, 'Processing item_out_of_stock webhook');
  }
}
