import { Logger } from '@kit/logger';
import { prisma } from '@kit/db';
import { IPOSConnector } from './ipos-connector';
import { IPOSMapper } from './ipos-mapper';

/**
 * Lớp xử lý đồng bộ dữ liệu với iPOS
 */
export class IPOSSync {
  private connector: IPOSConnector;
  private mapper: IPOSMapper;
  private logger: Logger;
  private accountId: string;
  private integrationId: string;

  constructor(
    connector: IPOSConnector,
    mapper: IPOSMapper,
    logger: Logger,
    accountId: string,
    integrationId: string
  ) {
    this.connector = connector;
    this.mapper = mapper;
    this.logger = logger.child({ service: 'IPOSSync' });
    this.accountId = accountId;
    this.integrationId = integrationId;
  }

  /**
   * Đồng bộ chi nhánh từ iPOS
   */
  async syncBranches() {
    try {
      this.logger.info('Starting branch sync');
      
      // <PERSON><PERSON><PERSON> thông tin tích hợp
      const integration = await prisma.integrations.findUnique({
        where: { id: this.integrationId }
      });
      
      if (!integration) {
        throw new Error(`Integration not found: ${this.integrationId}`);
      }
      
      // L<PERSON>y danh sách chi nhánh từ iPOS
      const response = await this.connector.getBranches();
      
      if (!response.data || !response.data.pos) {
        this.logger.warn('No branches found in iPOS');
        return { success: true, count: 0, message: 'No branches found in iPOS' };
      }
      
      const branches = response.data.pos;
      let successCount = 0;
      let errorCount = 0;
      
      // Đồng bộ từng chi nhánh
      for (const branch of branches) {
        try {
          // Map dữ liệu chi nhánh
          const branchData = this.mapper.mapBranch(branch, this.accountId);
          
          // Tạo hoặc cập nhật chi nhánh trong hệ thống
          await prisma.branches.upsert({
            where: { 
              account_id_external_id: {
                account_id: this.accountId,
                external_id: branchData.external_id
              }
            },
            update: branchData,
            create: branchData
          });
          
          successCount++;
        } catch (error) {
          this.logger.error({ error, branch }, 'Error syncing branch');
          errorCount++;
        }
      }
      
      this.logger.info({ successCount, errorCount }, 'Branch sync completed');
      
      return {
        success: true,
        count: successCount,
        message: `Synced ${successCount} branches, ${errorCount} errors`
      };
    } catch (error) {
      this.logger.error({ error }, 'Error syncing branches');
      throw error;
    }
  }

  /**
   * Đồng bộ danh mục từ iPOS
   */
  async syncCategories() {
    try {
      this.logger.info('Starting category sync');
      
      // Lấy thông tin tích hợp
      const integration = await prisma.integrations.findUnique({
        where: { id: this.integrationId }
      });
      
      if (!integration) {
        throw new Error(`Integration not found: ${this.integrationId}`);
      }
      
      // Lấy cấu hình từ integration
      const config = integration.config as any;
      const posParent = config.pos_parent;
      const posId = config.pos_id;
      
      // Lấy danh sách danh mục từ iPOS
      const response = await this.connector.getItems(posParent, posId, 'DELI');
      
      if (!response.data || !response.data.item_types) {
        this.logger.warn('No categories found in iPOS');
        return { success: true, count: 0, message: 'No categories found in iPOS' };
      }
      
      const categories = response.data.item_types;
      let successCount = 0;
      let errorCount = 0;
      
      // Đồng bộ từng danh mục
      for (const category of categories) {
        try {
          // Map dữ liệu danh mục
          const categoryData = this.mapper.mapCategory(category, this.accountId);
          
          // Tạo hoặc cập nhật danh mục trong hệ thống
          await prisma.categories.upsert({
            where: { 
              account_id_external_id: {
                account_id: this.accountId,
                external_id: categoryData.external_id
              }
            },
            update: categoryData,
            create: categoryData
          });
          
          successCount++;
        } catch (error) {
          this.logger.error({ error, category }, 'Error syncing category');
          errorCount++;
        }
      }
      
      this.logger.info({ successCount, errorCount }, 'Category sync completed');
      
      return {
        success: true,
        count: successCount,
        message: `Synced ${successCount} categories, ${errorCount} errors`
      };
    } catch (error) {
      this.logger.error({ error }, 'Error syncing categories');
      throw error;
    }
  }

  /**
   * Đồng bộ sản phẩm từ iPOS
   */
  async syncProducts() {
    try {
      this.logger.info('Starting product sync');
      
      // Lấy thông tin tích hợp
      const integration = await prisma.integrations.findUnique({
        where: { id: this.integrationId }
      });
      
      if (!integration) {
        throw new Error(`Integration not found: ${this.integrationId}`);
      }
      
      // Lấy cấu hình từ integration
      const config = integration.config as any;
      const posParent = config.pos_parent;
      const posId = config.pos_id;
      
      // Lấy danh sách sản phẩm từ iPOS
      const response = await this.connector.getItems(posParent, posId, 'DELI');
      
      if (!response.data || !response.data.items) {
        this.logger.warn('No products found in iPOS');
        return { success: true, count: 0, message: 'No products found in iPOS' };
      }
      
      const products = response.data.items;
      let successCount = 0;
      let errorCount = 0;
      
      // Lấy danh sách danh mục đã đồng bộ
      const categories = await prisma.categories.findMany({
        where: { account_id: this.accountId, source: 'ipos' },
        select: { id: true, external_id: true }
      });
      
      // Tạo map để tra cứu nhanh
      const categoryMap = new Map();
      categories.forEach(category => {
        categoryMap.set(category.external_id, category.id);
      });
      
      // Đồng bộ từng sản phẩm
      for (const product of products) {
        try {
          // Tìm category_id tương ứng
          const categoryId = categoryMap.get(product.type_id);
          
          // Map dữ liệu sản phẩm
          const productData = this.mapper.mapProduct(product, this.accountId, categoryId);
          
          // Tạo hoặc cập nhật sản phẩm trong hệ thống
          await prisma.products.upsert({
            where: { 
              account_id_external_id: {
                account_id: this.accountId,
                external_id: productData.external_id
              }
            },
            update: productData,
            create: productData
          });
          
          successCount++;
        } catch (error) {
          this.logger.error({ error, product }, 'Error syncing product');
          errorCount++;
        }
      }
      
      this.logger.info({ successCount, errorCount }, 'Product sync completed');
      
      return {
        success: true,
        count: successCount,
        message: `Synced ${successCount} products, ${errorCount} errors`
      };
    } catch (error) {
      this.logger.error({ error }, 'Error syncing products');
      throw error;
    }
  }

  /**
   * Đồng bộ khách hàng từ iPOS
   * @param phone Số điện thoại khách hàng (nếu có)
   */
  async syncCustomer(phone: string) {
    try {
      this.logger.info({ phone }, 'Starting customer sync');
      
      // Lấy thông tin khách hàng từ iPOS
      const response = await this.connector.getMembershipDetail(phone);
      
      if (!response.data || !response.data.membership) {
        this.logger.warn({ phone }, 'Customer not found in iPOS');
        return { success: false, message: 'Customer not found in iPOS' };
      }
      
      const customer = response.data.membership;
      
      // Map dữ liệu khách hàng
      const customerData = this.mapper.mapCustomer(customer, this.accountId);
      
      // Tạo hoặc cập nhật khách hàng trong hệ thống
      const result = await prisma.users.upsert({
        where: { 
          account_id_external_id: {
            account_id: this.accountId,
            external_id: customerData.external_id
          }
        },
        update: customerData,
        create: customerData
      });
      
      this.logger.info({ customerId: result.id }, 'Customer sync completed');
      
      return {
        success: true,
        data: result,
        message: 'Customer synced successfully'
      };
    } catch (error) {
      this.logger.error({ error, phone }, 'Error syncing customer');
      throw error;
    }
  }

  /**
   * Đồng bộ voucher của khách hàng từ iPOS
   * @param membershipId ID thành viên trong iPOS
   */
  async syncVouchers(membershipId: string) {
    try {
      this.logger.info({ membershipId }, 'Starting voucher sync');
      
      // Lấy thông tin voucher từ iPOS
      const response = await this.connector.getMemberVouchers(membershipId);
      
      if (!response.data || !response.data.vouchers) {
        this.logger.warn({ membershipId }, 'No vouchers found in iPOS');
        return { success: true, count: 0, message: 'No vouchers found in iPOS' };
      }
      
      const vouchers = response.data.vouchers;
      let successCount = 0;
      let errorCount = 0;
      
      // Tìm user_id tương ứng
      const user = await prisma.users.findFirst({
        where: { 
          account_id: this.accountId,
          external_id: membershipId
        },
        select: { id: true }
      });
      
      const userId = user?.id;
      
      // Đồng bộ từng voucher
      for (const voucher of vouchers) {
        try {
          // Map dữ liệu voucher
          const voucherData = this.mapper.mapVoucher(voucher, this.accountId, userId);
          
          // Tạo hoặc cập nhật voucher trong hệ thống
          await prisma.vouchers.upsert({
            where: { 
              account_id_external_id: {
                account_id: this.accountId,
                external_id: voucherData.external_id
              }
            },
            update: voucherData,
            create: voucherData
          });
          
          successCount++;
        } catch (error) {
          this.logger.error({ error, voucher }, 'Error syncing voucher');
          errorCount++;
        }
      }
      
      this.logger.info({ successCount, errorCount }, 'Voucher sync completed');
      
      return {
        success: true,
        count: successCount,
        message: `Synced ${successCount} vouchers, ${errorCount} errors`
      };
    } catch (error) {
      this.logger.error({ error, membershipId }, 'Error syncing vouchers');
      throw error;
    }
  }

  /**
   * Đồng bộ trạng thái đơn hàng từ iPOS
   * @param foodbookCode Mã đơn hàng trong iPOS
   */
  async syncOrderStatus(foodbookCode: string) {
    try {
      this.logger.info({ foodbookCode }, 'Starting order status sync');
      
      // Lấy thông tin đơn hàng từ iPOS
      const response = await this.connector.getOrderInfo(foodbookCode);
      
      if (!response.data || !response.data.order) {
        this.logger.warn({ foodbookCode }, 'Order not found in iPOS');
        return { success: false, message: 'Order not found in iPOS' };
      }
      
      const order = response.data.order;
      
      // Cập nhật trạng thái đơn hàng trong hệ thống
      const result = await prisma.orders.updateMany({
        where: { 
          account_id: this.accountId,
          external_id: foodbookCode
        },
        data: {
          status: order.status,
          last_synced_at: new Date()
        }
      });
      
      this.logger.info({ foodbookCode, status: order.status }, 'Order status sync completed');
      
      return {
        success: true,
        data: { status: order.status },
        message: 'Order status synced successfully'
      };
    } catch (error) {
      this.logger.error({ error, foodbookCode }, 'Error syncing order status');
      throw error;
    }
  }

  /**
   * Tạo đơn hàng mới trong iPOS
   * @param orderId ID đơn hàng trong hệ thống
   */
  async createOrder(orderId: string) {
    try {
      this.logger.info({ orderId }, 'Starting order creation in iPOS');
      
      // Lấy thông tin đơn hàng từ hệ thống
      const order = await prisma.orders.findUnique({
        where: { id: orderId },
        include: {
          user: true,
          branch: true
        }
      });
      
      if (!order) {
        throw new Error(`Order not found: ${orderId}`);
      }
      
      // Lấy thông tin tích hợp
      const integration = await prisma.integrations.findUnique({
        where: { id: this.integrationId }
      });
      
      if (!integration) {
        throw new Error(`Integration not found: ${this.integrationId}`);
      }
      
      // Lấy cấu hình từ integration
      const config = integration.config as any;
      const posId = order.branch?.external_id || config.pos_id;
      
      // Lấy membership_id của khách hàng
      const membershipId = order.user?.external_id;
      
      // Map dữ liệu đơn hàng
      const orderData = this.mapper.mapOrderToIPOS(order, posId, membershipId);
      
      // Tạo đơn hàng trong iPOS
      const response = await this.connector.createOrder(orderData);
      
      if (!response.data || !response.data.foodbook_code) {
        throw new Error('Failed to create order in iPOS');
      }
      
      // Cập nhật external_id cho đơn hàng
      await prisma.orders.update({
        where: { id: orderId },
        data: {
          external_id: response.data.foodbook_code,
          last_synced_at: new Date()
        }
      });
      
      this.logger.info({ orderId, foodbookCode: response.data.foodbook_code }, 'Order created in iPOS');
      
      return {
        success: true,
        data: { foodbookCode: response.data.foodbook_code },
        message: 'Order created in iPOS successfully'
      };
    } catch (error) {
      this.logger.error({ error, orderId }, 'Error creating order in iPOS');
      throw error;
    }
  }

  /**
   * Tạo hoặc cập nhật khách hàng trong iPOS
   * @param userId ID khách hàng trong hệ thống
   */
  async createOrUpdateCustomer(userId: string) {
    try {
      this.logger.info({ userId }, 'Starting customer creation/update in iPOS');
      
      // Lấy thông tin khách hàng từ hệ thống
      const user = await prisma.users.findUnique({
        where: { id: userId }
      });
      
      if (!user) {
        throw new Error(`User not found: ${userId}`);
      }
      
      // Map dữ liệu khách hàng
      const customerData = this.mapper.mapCustomerToIPOS(user);
      
      // Kiểm tra xem khách hàng đã tồn tại trong iPOS chưa
      let response;
      if (user.external_id) {
        // Cập nhật khách hàng
        response = await this.connector.updateMembership({
          ...customerData,
          membership_id_new: user.external_id
        });
      } else {
        // Tạo khách hàng mới
        response = await this.connector.addMembership(customerData);
      }
      
      if (!response.data || !response.data.membership_id_new) {
        throw new Error('Failed to create/update customer in iPOS');
      }
      
      // Cập nhật external_id cho khách hàng
      await prisma.users.update({
        where: { id: userId },
        data: {
          external_id: response.data.membership_id_new,
          last_synced_at: new Date()
        }
      });
      
      this.logger.info({ userId, membershipId: response.data.membership_id_new }, 'Customer created/updated in iPOS');
      
      return {
        success: true,
        data: { membershipId: response.data.membership_id_new },
        message: 'Customer created/updated in iPOS successfully'
      };
    } catch (error) {
      this.logger.error({ error, userId }, 'Error creating/updating customer in iPOS');
      throw error;
    }
  }
}
