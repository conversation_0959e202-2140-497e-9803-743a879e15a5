import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';
import {
  Connector,
  ConflictStrategy,
  ResourceType,
  SyncAction,
  SyncConfig,
  SyncDirection,
  SyncStrategy,
} from './connector.interface';

/**
 * Abstract class cho connector
 * Triển khai các phương thức chung cho tất cả các connector
 */
export abstract class AbstractConnector<TCredentials = any, TConfig = any>
  implements Connector<TCredentials, TConfig>
{
  protected credentials: TCredentials;
  protected config: TConfig;
  protected logger: any;
  protected supabase: any;

  constructor(credentials: TCredentials, config?: TConfig) {
    this.credentials = credentials;
    this.config = config || ({} as TConfig);
    this.initializeLogger();
    this.supabase = getSupabaseServerClient<Database>();
  }

  /**
   * Khởi tạo logger
   */
  private async initializeLogger() {
    this.logger = await getLogger();
  }

  /**
   * <PERSON>ác thực với API của bên thứ 3
   * @param credentials Thông tin xác thực
   */
  abstract authenticate(credentials: TCredentials): Promise<boolean>;

  /**
   * Lấy schema của dữ liệu từ bên thứ 3
   * @param resourceType Loại resource
   */
  abstract getSchema(resourceType: string): Promise<string[]>;

  /**
   * Lấy dữ liệu từ bên thứ 3
   * @param params Tham số cho việc lấy dữ liệu
   */
  abstract getData(params: {
    resourceType: string;
    strategy?: 'full' | 'incremental';
    filters?: Record<string, any>;
    limit?: number;
    offset?: number;
  }): Promise<any[]>;

  /**
   * Gửi dữ liệu đến bên thứ 3
   * @param params Tham số cho việc gửi dữ liệu
   */
  abstract sendData(params: {
    resourceType: string;
    action: 'create' | 'update' | 'delete';
    data: any;
  }): Promise<any>;

  /**
   * Chuyển tiếp yêu cầu API đến bên thứ 3 (bắc cầu API)
   * @param resourceType Loại resource
   * @param action Hành động
   * @param payload Dữ liệu gửi đi
   */
  abstract proxyRequest(
    resourceType: string,
    action: string,
    payload: any
  ): Promise<any>;

  /**
   * Xử lý webhook từ bên thứ 3
   * @param payload Dữ liệu webhook
   */
  abstract handleWebhook(payload: any): Promise<void>;

  /**
   * Kiểm tra xung đột giữa dữ liệu local và remote
   * @param local Dữ liệu local
   * @param remote Dữ liệu remote
   * @param strategy Chiến lược xử lý xung đột
   */
  async resolveConflict(
    local: any,
    remote: any,
    strategy: string = ConflictStrategy.TIMESTAMP
  ): Promise<any> {
    switch (strategy) {
      case ConflictStrategy.TIMESTAMP:
        return new Date(local.updated_at) > new Date(remote.updated_at)
          ? local
          : remote;
      case ConflictStrategy.LOCAL:
        return local;
      case ConflictStrategy.REMOTE:
        return remote;
      case ConflictStrategy.MANUAL:
        // Lưu xung đột để người dùng xử lý sau
        await this.saveConflict(local, remote);
        return null;
      default:
        return null;
    }
  }

  /**
   * Lưu xung đột vào cơ sở dữ liệu
   * @param local Dữ liệu local
   * @param remote Dữ liệu remote
   */
  protected async saveConflict(local: any, remote: any): Promise<void> {
    try {
      const { error } = await this.supabase.from('integration_conflicts').insert({
        account_id: local.account_id,
        platform: this.getPlatformName(),
        resource: this.getResourceTypeFromData(local),
        local,
        remote,
      });

      if (error) {
        this.logger.error({ error }, 'Failed to save conflict');
      }
    } catch (error) {
      this.logger.error({ error }, 'Error saving conflict');
    }
  }

  /**
   * Lấy tên platform từ connector
   */
  protected abstract getPlatformName(): string;

  /**
   * Lấy loại resource từ dữ liệu
   * @param data Dữ liệu
   */
  protected getResourceTypeFromData(data: any): string {
    // Mặc định dựa vào cấu trúc dữ liệu để xác định loại resource
    // Các connector con có thể override phương thức này
    if (data.price !== undefined || data.sku !== undefined) {
      return ResourceType.PRODUCTS;
    } else if (data.total_amount !== undefined || data.order_code !== undefined) {
      return ResourceType.ORDERS;
    } else if (data.email !== undefined || data.phone !== undefined) {
      return ResourceType.CUSTOMERS;
    } else if (data.parent_id !== undefined) {
      return ResourceType.CATEGORIES;
    } else if (data.address !== undefined || data.location !== undefined) {
      return ResourceType.BRANCHES;
    }
    return 'unknown';
  }

  /**
   * Áp dụng mapping cho dữ liệu
   * @param data Dữ liệu gốc
   * @param mappings Mapping
   */
  protected applyMapping(data: any, mappings: any[]): any {
    const result: Record<string, any> = {};

    // Nếu không có mapping, trả về dữ liệu gốc
    if (!mappings || mappings.length === 0) {
      return data;
    }

    // Áp dụng từng mapping
    for (const mapping of mappings) {
      const { source_field, target_field, transform_function } = mapping;

      // Lấy giá trị từ source field
      let value = data[source_field];

      // Áp dụng hàm biến đổi nếu có
      if (transform_function && value !== undefined) {
        try {
          // Thực thi hàm biến đổi (cẩn thận với eval!)
          // Trong môi trường production, nên sử dụng cách an toàn hơn
          const transformFn = new Function('value', transform_function);
          value = transformFn(value);
        } catch (error) {
          this.logger.error(
            { error, sourceField: source_field },
            'Error applying transform function'
          );
        }
      }

      // Gán giá trị cho target field
      if (value !== undefined) {
        result[target_field] = value;
      }
    }

    return result;
  }

  /**
   * Lấy mappings từ cơ sở dữ liệu
   * @param integrationId ID của integration
   * @param resourceType Loại resource
   */
  protected async getMappings(
    integrationId: string,
    resourceType: string
  ): Promise<any[]> {
    try {
      const { data, error } = await this.supabase
        .from('integration_mappings')
        .select('*')
        .eq('integration_id', integrationId)
        .eq('resource_type', resourceType)
        .eq('is_active', true);

      if (error) {
        this.logger.error(
          { error, integrationId, resourceType },
          'Error getting mappings'
        );
        return [];
      }

      return data || [];
    } catch (error) {
      this.logger.error(
        { error, integrationId, resourceType },
        'Error getting mappings'
      );
      return [];
    }
  }

  /**
   * Kiểm tra quyền thực hiện hành động
   * @param config Cấu hình đồng bộ
   * @param action Hành động
   * @param direction Hướng đồng bộ
   */
  protected checkPermission(
    config: SyncConfig,
    action: SyncAction,
    direction: SyncDirection
  ): boolean {
    // Nếu đồng bộ hai chiều, luôn cho phép
    if (config.direction === SyncDirection.BIDIRECTIONAL) {
      return true;
    }

    // Nếu hướng đồng bộ không khớp với hướng cấu hình, không cho phép
    if (
      (direction === SyncDirection.INBOUND &&
        config.direction !== SyncDirection.INBOUND) ||
      (direction === SyncDirection.OUTBOUND &&
        config.direction !== SyncDirection.OUTBOUND)
    ) {
      return false;
    }

    // Kiểm tra quyền cụ thể
    switch (action) {
      case SyncAction.CREATE:
        return config.permissions.create;
      case SyncAction.UPDATE:
        return config.permissions.update;
      case SyncAction.DELETE:
        return config.permissions.delete;
      default:
        return false;
    }
  }

  /**
   * Lấy bảng tương ứng với loại resource
   * @param resourceType Loại resource
   */
  protected getTableForResource(resourceType: string): string {
    switch (resourceType) {
      case ResourceType.PRODUCTS:
        return 'products';
      case ResourceType.ORDERS:
        return 'customer_orders';
      case ResourceType.CUSTOMERS:
        return 'customers';
      case ResourceType.CATEGORIES:
        return 'categories';
      case ResourceType.BRANCHES:
        return 'branches';
      case ResourceType.VOUCHERS:
        return 'vouchers';
      default:
        throw new Error(`Unsupported resource type: ${resourceType}`);
    }
  }

  /**
   * Lưu dữ liệu vào bảng tương ứng
   * @param resourceType Loại resource
   * @param data Dữ liệu
   * @param accountId ID của account
   */
  protected async saveDataToTable(
    resourceType: string,
    data: any,
    accountId: string
  ): Promise<any> {
    const supabase = this.supabase;

    // Thêm account_id vào dữ liệu
    const dataWithAccount = {
      ...data,
      account_id: accountId,
    };

    // Xác định bảng dựa trên resource type
    const table = this.getTableForResource(resourceType);

    // Kiểm tra xem đã có dữ liệu chưa
    const { data: existingData, error: fetchError } = await supabase
      .from(table)
      .select('id')
      .eq('id', data.id)
      .eq('account_id', accountId)
      .maybeSingle();

    if (fetchError && fetchError.code !== 'PGRST116') {
      return { error: fetchError };
    }

    if (existingData) {
      // Cập nhật dữ liệu hiện có
      return await supabase
        .from(table)
        .update(dataWithAccount)
        .eq('id', data.id)
        .eq('account_id', accountId);
    } else {
      // Tạo dữ liệu mới
      return await supabase.from(table).insert(dataWithAccount);
    }
  }
}
