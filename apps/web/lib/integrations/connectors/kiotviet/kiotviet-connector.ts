import { AbstractConnector } from '../abstract-connector';
import { ResourceType, SyncAction } from '../connector.interface';
import { KiotVietConnectorServer, KiotVietCredentials as KiotVietCredentialsServer } from './kiotviet-connector-server';

/**
 * Interface cho thông tin xác thực KiotViet
 */
export interface KiotVietCredentials {
  client_id: string;
  client_secret: string;
  retailer: string;
  access_token?: string;
  refresh_token?: string;
  token_expires_at?: string;
}

/**
 * Interface cho cấu hình KiotViet
 */
export interface KiotVietConfig {
  webhook_url?: string;
  webhook_secret?: string;
}

/**
 * Connector cho KiotViet
 */
export class KiotVietConnector extends AbstractConnector<KiotVietCredentials, KiotVietConfig> {
  private kiotVietServer: KiotVietConnectorServer;

  constructor(credentials: KiotVietCredentials, config?: KiotVietConfig) {
    super(credentials, config);
    this.kiotVietServer = new KiotVietConnectorServer(credentials as KiotVietCredentialsServer);
  }

  /**
   * <PERSON><PERSON><PERSON> thực với KiotViet API
   * @param credentials Thông tin xác thực
   */
  async authenticate(credentials: KiotVietCredentials): Promise<boolean> {
    try {
      this.logger.info({ credentials }, 'Authenticating with KiotViet API');

      // Sử dụng KiotVietConnectorServer để kiểm tra kết nối
      const isAuthenticated = await this.kiotVietServer.testConnection();

      // Placeholder for development
      if (process.env.NODE_ENV === 'development' && !isAuthenticated) {
        this.logger.info('Using mock authentication for development');
        return true;
      }

      return isAuthenticated;
    } catch (error) {
      this.logger.error({ error, credentials }, 'Error authenticating with KiotViet API');
      throw error;
    }
  }

  /**
   * Lấy schema của dữ liệu từ KiotViet
   * @param resourceType Loại resource
   */
  async getSchema(resourceType: string): Promise<string[]> {
    try {
      this.logger.info({ resourceType }, 'Getting schema from KiotViet');

      // Sử dụng KiotVietConnectorServer để lấy schema
      switch (resourceType) {
        case ResourceType.PRODUCTS:
          return await this.kiotVietServer.getProductSchema();
        case ResourceType.ORDERS:
          return await this.kiotVietServer.getOrderSchema();
        case ResourceType.CUSTOMERS:
          return await this.kiotVietServer.getCustomerSchema();
        default:
          throw new Error(`Unsupported resource type: ${resourceType}`);
      }
    } catch (error) {
      this.logger.error({ error, resourceType }, 'Error getting schema from KiotViet');

      // Placeholder for development
      if (process.env.NODE_ENV === 'development') {
        this.logger.info('Using mock schema for development');
        switch (resourceType) {
          case ResourceType.PRODUCTS:
            return [
              'id', 'code', 'name', 'fullName', 'categoryId', 'categoryName',
              'basePrice', 'price', 'description', 'unit', 'barcode',
              'images', 'attributes', 'inventories', 'isActive'
            ];
          case ResourceType.ORDERS:
            return [
              'id', 'code', 'purchaseDate', 'branchId', 'branchName',
              'customerId', 'customerName', 'customerCode', 'total',
              'discount', 'status', 'statusValue', 'description',
              'orderDetails', 'payments'
            ];
          case ResourceType.CUSTOMERS:
            return [
              'id', 'code', 'name', 'contactNumber', 'email', 'address',
              'birthDate', 'gender', 'rewardPoint', 'debt', 'groups'
            ];
          default:
            throw new Error(`Unsupported resource type: ${resourceType}`);
        }
      }

      throw error;
    }
  }

  /**
   * Lấy dữ liệu từ KiotViet
   * @param params Tham số cho việc lấy dữ liệu
   */
  async getData(params: {
    resourceType: string;
    strategy?: 'full' | 'incremental';
    filters?: Record<string, any>;
    limit?: number;
    offset?: number;
  }): Promise<any[]> {
    const { resourceType, strategy = 'full', filters = {}, limit, offset } = params;

    try {
      this.logger.info({ resourceType, strategy, filters }, 'Getting data from KiotViet');

      // Chuẩn bị tham số API
      const apiParams: Record<string, any> = {
        ...filters,
      };

      if (limit) {
        apiParams.pageSize = limit;
      }

      if (offset) {
        apiParams.currentPage = Math.floor(offset / (limit || 20)) + 1;
      }

      // Nếu là incremental, thêm tham số updated_after
      if (strategy === 'incremental' && !filters.lastModifiedFrom) {
        const oneDayAgo = new Date();
        oneDayAgo.setDate(oneDayAgo.getDate() - 1);
        apiParams.lastModifiedFrom = oneDayAgo.toISOString();
      }

      // Sử dụng KiotVietConnectorServer để lấy dữ liệu
      switch (resourceType) {
        case ResourceType.PRODUCTS:
          return await this.kiotVietServer.getProducts(apiParams);
        case ResourceType.ORDERS:
          return await this.kiotVietServer.getOrders(apiParams);
        case ResourceType.CUSTOMERS:
          return await this.kiotVietServer.getCustomers(apiParams);
        default:
          throw new Error(`Unsupported resource type: ${resourceType}`);
      }
    } catch (error) {
      this.logger.error({ error, resourceType }, 'Error getting data from KiotViet');

      // Placeholder for development
      if (process.env.NODE_ENV === 'development') {
        this.logger.info('Using mock data for development');
        switch (resourceType) {
          case ResourceType.PRODUCTS:
            return [
              { id: '1', code: 'P001', name: 'Test Product 1', price: 100000, barcode: 'B001', isActive: true },
              { id: '2', code: 'P002', name: 'Test Product 2', price: 200000, barcode: 'B002', isActive: true },
            ];
          case ResourceType.ORDERS:
            return [
              { id: '1', code: 'ORD001', customerName: 'Customer 1', total: 300000, status: 1, statusValue: 'Completed' },
              { id: '2', code: 'ORD002', customerName: 'Customer 2', total: 450000, status: 1, statusValue: 'Completed' },
            ];
          case ResourceType.CUSTOMERS:
            return [
              { id: '1', code: 'C001', name: 'Customer 1', contactNumber: '0123456789', email: '<EMAIL>' },
              { id: '2', code: 'C002', name: 'Customer 2', contactNumber: '0987654321', email: '<EMAIL>' },
            ];
          default:
            return [];
        }
      }

      throw error;
    }
  }

  /**
   * Gửi dữ liệu đến KiotViet
   * @param params Tham số cho việc gửi dữ liệu
   */
  async sendData(params: {
    resourceType: string;
    action: 'create' | 'update' | 'delete';
    data: any;
  }): Promise<any> {
    const { resourceType, action, data } = params;

    try {
      this.logger.info({ resourceType, action, data }, 'Sending data to KiotViet');

      // Chỉ hỗ trợ orders hiện tại
      if (resourceType === ResourceType.ORDERS) {
        if (action === SyncAction.CREATE) {
          return await this.kiotVietServer.createOrder(data);
        } else if (action === SyncAction.UPDATE) {
          return await this.kiotVietServer.updateOrder(data.id, data);
        } else {
          throw new Error(`Action ${action} not supported for orders`);
        }
      } else {
        throw new Error(`Sending data not supported for resource type: ${resourceType}`);
      }
    } catch (error) {
      this.logger.error({ error, resourceType, action }, 'Error sending data to KiotViet');

      // Placeholder for development
      if (process.env.NODE_ENV === 'development') {
        this.logger.info('Using mock response for development');
        return { success: true, id: data.id || '123' };
      }

      throw error;
    }
  }

  /**
   * Chuyển tiếp yêu cầu API đến KiotViet (bắc cầu API)
   * @param resourceType Loại resource
   * @param action Hành động
   * @param payload Dữ liệu gửi đi
   */
  async proxyRequest(
    resourceType: string,
    action: string,
    payload: any
  ): Promise<any> {
    try {
      // TODO: Implement proxying request to KiotViet API
      this.logger.info({ resourceType, action, payload }, 'Proxying request to KiotViet');

      // Placeholder for development
      if (process.env.NODE_ENV === 'development') {
        return { success: true, data: { id: '123', name: 'Test' } };
      }

      return { success: true };
    } catch (error) {
      this.logger.error(
        { error, resourceType, action },
        'Error proxying request to KiotViet'
      );
      throw error;
    }
  }

  /**
   * Xử lý webhook từ KiotViet
   * @param payload Dữ liệu webhook
   */
  async handleWebhook(payload: any): Promise<void> {
    try {
      // TODO: Implement handling webhook from KiotViet
      this.logger.info({ payload }, 'Handling webhook from KiotViet');
    } catch (error) {
      this.logger.error({ error, payload }, 'Error handling KiotViet webhook');
      throw error;
    }
  }

  /**
   * Lấy tên platform
   */
  protected getPlatformName(): string {
    return 'kiotviet';
  }


}
