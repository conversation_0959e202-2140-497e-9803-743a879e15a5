import { KiotVietConnector } from '../kiotviet-connector';
import { ResourceType, SyncAction } from '../../connector.interface';

// Mock <PERSON>ietConnectorServer
jest.mock('../kiotviet-connector-server', () => {
  return {
    KiotVietConnectorServer: jest.fn().mockImplementation(() => {
      return {
        testConnection: jest.fn().mockResolvedValue(true),
        getProductSchema: jest.fn().mockResolvedValue(['id', 'name', 'price']),
        getOrderSchema: jest.fn().mockResolvedValue(['id', 'code', 'total']),
        getCustomerSchema: jest.fn().mockResolvedValue(['id', 'name', 'email']),
        getProducts: jest.fn().mockResolvedValue([
          { id: '1', name: 'Product 1', price: 100 },
          { id: '2', name: 'Product 2', price: 200 },
        ]),
        getOrders: jest.fn().mockResolvedValue([
          { id: '1', code: 'ORD001', total: 100 },
          { id: '2', code: 'ORD002', total: 200 },
        ]),
        getCustomers: jest.fn().mockResolvedValue([
          { id: '1', name: 'Customer 1', email: '<EMAIL>' },
          { id: '2', name: 'Customer 2', email: '<EMAIL>' },
        ]),
        createOrder: jest.fn().mockResolvedValue({ id: '3', code: 'ORD003' }),
        updateOrder: jest.fn().mockResolvedValue({ id: '1', code: 'ORD001', status: 'updated' }),
      };
    }),
  };
});

// Mock logger
jest.mock('@kit/shared/logger', () => ({
  getLogger: jest.fn().mockResolvedValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  }),
}));

describe('KiotVietConnector', () => {
  let connector: KiotVietConnector;
  const credentials = {
    client_id: 'test_client_id',
    client_secret: 'test_client_secret',
    retailer: 'test_retailer',
  };

  beforeEach(() => {
    connector = new KiotVietConnector(credentials);
  });

  test('should initialize with credentials', () => {
    expect(connector).toBeDefined();
  });

  test('should return platform name', () => {
    expect(connector.getPlatform()).toBe('kiotviet');
  });

  test('should authenticate successfully', async () => {
    const result = await connector.authenticate(credentials);
    expect(result).toBe(true);
  });

  test('should get product schema', async () => {
    const schema = await connector.getSchema(ResourceType.PRODUCTS);
    expect(schema).toEqual(['id', 'name', 'price']);
  });

  test('should get order schema', async () => {
    const schema = await connector.getSchema(ResourceType.ORDERS);
    expect(schema).toEqual(['id', 'code', 'total']);
  });

  test('should get customer schema', async () => {
    const schema = await connector.getSchema(ResourceType.CUSTOMERS);
    expect(schema).toEqual(['id', 'name', 'email']);
  });

  test('should get products data', async () => {
    const data = await connector.getData({
      resourceType: ResourceType.PRODUCTS,
    });
    expect(data).toEqual([
      { id: '1', name: 'Product 1', price: 100 },
      { id: '2', name: 'Product 2', price: 200 },
    ]);
  });

  test('should get orders data', async () => {
    const data = await connector.getData({
      resourceType: ResourceType.ORDERS,
    });
    expect(data).toEqual([
      { id: '1', code: 'ORD001', total: 100 },
      { id: '2', code: 'ORD002', total: 200 },
    ]);
  });

  test('should get customers data', async () => {
    const data = await connector.getData({
      resourceType: ResourceType.CUSTOMERS,
    });
    expect(data).toEqual([
      { id: '1', name: 'Customer 1', email: '<EMAIL>' },
      { id: '2', name: 'Customer 2', email: '<EMAIL>' },
    ]);
  });

  test('should create order', async () => {
    const result = await connector.sendData({
      resourceType: ResourceType.ORDERS,
      action: SyncAction.CREATE,
      data: { customer_id: '1', items: [] },
    });
    expect(result).toEqual({ id: '3', code: 'ORD003' });
  });

  test('should update order', async () => {
    const result = await connector.sendData({
      resourceType: ResourceType.ORDERS,
      action: SyncAction.UPDATE,
      data: { id: '1', status: 'updated' },
    });
    expect(result).toEqual({ id: '1', code: 'ORD001', status: 'updated' });
  });

  test('should throw error for unsupported resource type', async () => {
    await expect(
      connector.getSchema('unsupported_resource')
    ).rejects.toThrow('Unsupported resource type: unsupported_resource');
  });

  test('should throw error for unsupported action', async () => {
    await expect(
      connector.sendData({
        resourceType: ResourceType.PRODUCTS,
        action: SyncAction.CREATE,
        data: { name: 'New Product' },
      })
    ).rejects.toThrow('Unsupported action for resource type: products');
  });
});
