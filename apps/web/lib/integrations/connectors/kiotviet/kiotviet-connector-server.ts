/**
 * KiotViet API Connector Server
 * Triển khai các hàm gọi API đến KiotViet
 */

import { getLogger } from '@kit/shared/logger';

/**
 * Interface cho thông tin xác thực KiotViet
 */
export interface KiotVietCredentials {
  client_id: string;
  client_secret: string;
  retailer: string;
  access_token?: string;
  refresh_token?: string;
  token_expires_at?: string;
}

/**
 * Lớp KiotVietConnectorServer
 * Xử lý các tác vụ gọi API đến KiotViet
 */
export class KiotVietConnectorServer {
  private credentials: KiotVietCredentials;
  private logger: any;
  private baseUrl: string = 'https://public.kiotapi.com';
  private accessToken: string | null = null;
  private tokenExpiresAt: Date | null = null;

  constructor(credentials: KiotVietCredentials) {
    this.credentials = credentials;
    
    // Khởi tạo access token từ credentials nếu có
    if (credentials.access_token && credentials.token_expires_at) {
      this.accessToken = credentials.access_token;
      this.tokenExpiresAt = new Date(credentials.token_expires_at);
    }
    
    this.initializeLogger();
  }

  /**
   * Khởi tạo logger
   */
  private async initializeLogger() {
    this.logger = await getLogger();
  }

  /**
   * Kiểm tra kết nối với KiotViet API
   * @returns Promise<boolean> Kết quả kiểm tra
   */
  async testConnection(): Promise<boolean> {
    try {
      // Lấy access token nếu chưa có hoặc đã hết hạn
      await this.ensureAccessToken();
      
      // Gọi API để kiểm tra kết nối
      const response = await this.callKiotVietAPI('/products', 'GET', { pageSize: 1 });
      return !!response;
    } catch (error) {
      this.logger.error({ error }, 'Error testing connection to KiotViet API');
      return false;
    }
  }

  /**
   * Lấy danh sách sản phẩm từ KiotViet
   * @param params Tham số truy vấn
   * @returns Promise<any[]> Danh sách sản phẩm
   */
  async getProducts(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Đảm bảo có access token
      await this.ensureAccessToken();
      
      // Chuẩn bị tham số
      const queryParams: Record<string, any> = {
        pageSize: params.limit || 20,
        currentPage: params.page || 1,
        ...params
      };
      
      // Gọi API
      const response = await this.callKiotVietAPI('/products', 'GET', queryParams);
      
      // Trả về danh sách sản phẩm
      return response.data || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching products from KiotViet');
      throw error;
    }
  }

  /**
   * Lấy danh sách đơn hàng từ KiotViet
   * @param params Tham số truy vấn
   * @returns Promise<any[]> Danh sách đơn hàng
   */
  async getOrders(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Đảm bảo có access token
      await this.ensureAccessToken();
      
      // Chuẩn bị tham số
      const queryParams: Record<string, any> = {
        pageSize: params.limit || 20,
        currentPage: params.page || 1,
        ...params
      };
      
      // Gọi API
      const response = await this.callKiotVietAPI('/orders', 'GET', queryParams);
      
      // Trả về danh sách đơn hàng
      return response.data || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching orders from KiotViet');
      throw error;
    }
  }

  /**
   * Lấy danh sách khách hàng từ KiotViet
   * @param params Tham số truy vấn
   * @returns Promise<any[]> Danh sách khách hàng
   */
  async getCustomers(params: Record<string, any> = {}): Promise<any[]> {
    try {
      // Đảm bảo có access token
      await this.ensureAccessToken();
      
      // Chuẩn bị tham số
      const queryParams: Record<string, any> = {
        pageSize: params.limit || 20,
        currentPage: params.page || 1,
        ...params
      };
      
      // Gọi API
      const response = await this.callKiotVietAPI('/customers', 'GET', queryParams);
      
      // Trả về danh sách khách hàng
      return response.data || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching customers from KiotViet');
      throw error;
    }
  }

  /**
   * Tạo đơn hàng mới trong KiotViet
   * @param orderData Dữ liệu đơn hàng
   * @returns Promise<any> Đơn hàng đã tạo
   */
  async createOrder(orderData: any): Promise<any> {
    try {
      // Đảm bảo có access token
      await this.ensureAccessToken();
      
      // Gọi API để tạo đơn hàng
      const response = await this.callKiotVietAPI('/orders', 'POST', null, orderData);
      
      // Trả về đơn hàng đã tạo
      return response;
    } catch (error) {
      this.logger.error({ error }, 'Error creating order in KiotViet');
      throw error;
    }
  }

  /**
   * Cập nhật đơn hàng trong KiotViet
   * @param orderId ID đơn hàng
   * @param orderData Dữ liệu đơn hàng
   * @returns Promise<any> Đơn hàng đã cập nhật
   */
  async updateOrder(orderId: string, orderData: any): Promise<any> {
    try {
      // Đảm bảo có access token
      await this.ensureAccessToken();
      
      // Gọi API để cập nhật đơn hàng
      const response = await this.callKiotVietAPI(`/orders/${orderId}`, 'PUT', null, orderData);
      
      // Trả về đơn hàng đã cập nhật
      return response;
    } catch (error) {
      this.logger.error({ error }, 'Error updating order in KiotViet');
      throw error;
    }
  }

  /**
   * Lấy schema của sản phẩm từ KiotViet
   * @returns Promise<string[]> Danh sách trường
   */
  async getProductSchema(): Promise<string[]> {
    try {
      // Lấy một sản phẩm mẫu để xác định schema
      const products = await this.getProducts({ pageSize: 1 });
      if (products.length === 0) {
        // Nếu không có sản phẩm nào, trả về schema mặc định
        return [
          'id',
          'code',
          'name',
          'fullName',
          'categoryId',
          'categoryName',
          'basePrice',
          'price',
          'description',
          'unit',
          'barcode',
          'images',
          'attributes',
          'inventories',
          'isActive',
        ];
      }

      // Lấy tất cả các key từ sản phẩm đầu tiên
      return Object.keys(products[0]);
    } catch (error) {
      this.logger.error({ error }, 'Error getting product schema from KiotViet');
      // Trả về schema mặc định nếu có lỗi
      return [
        'id',
        'code',
        'name',
        'fullName',
        'categoryId',
        'categoryName',
        'basePrice',
        'price',
        'description',
        'unit',
        'barcode',
        'images',
        'attributes',
        'inventories',
        'isActive',
      ];
    }
  }

  /**
   * Lấy schema của đơn hàng từ KiotViet
   * @returns Promise<string[]> Danh sách trường
   */
  async getOrderSchema(): Promise<string[]> {
    try {
      // Lấy một đơn hàng mẫu để xác định schema
      const orders = await this.getOrders({ pageSize: 1 });
      if (orders.length === 0) {
        // Nếu không có đơn hàng nào, trả về schema mặc định
        return [
          'id',
          'code',
          'purchaseDate',
          'branchId',
          'branchName',
          'customerId',
          'customerName',
          'customerCode',
          'total',
          'discount',
          'status',
          'statusValue',
          'description',
          'orderDetails',
          'payments',
        ];
      }

      // Lấy tất cả các key từ đơn hàng đầu tiên
      return Object.keys(orders[0]);
    } catch (error) {
      this.logger.error({ error }, 'Error getting order schema from KiotViet');
      // Trả về schema mặc định nếu có lỗi
      return [
        'id',
        'code',
        'purchaseDate',
        'branchId',
        'branchName',
        'customerId',
        'customerName',
        'customerCode',
        'total',
        'discount',
        'status',
        'statusValue',
        'description',
        'orderDetails',
        'payments',
      ];
    }
  }

  /**
   * Lấy schema của khách hàng từ KiotViet
   * @returns Promise<string[]> Danh sách trường
   */
  async getCustomerSchema(): Promise<string[]> {
    try {
      // Lấy một khách hàng mẫu để xác định schema
      const customers = await this.getCustomers({ pageSize: 1 });
      if (customers.length === 0) {
        // Nếu không có khách hàng nào, trả về schema mặc định
        return [
          'id',
          'code',
          'name',
          'contactNumber',
          'email',
          'address',
          'birthDate',
          'gender',
          'rewardPoint',
          'debt',
          'groups',
        ];
      }

      // Lấy tất cả các key từ khách hàng đầu tiên
      return Object.keys(customers[0]);
    } catch (error) {
      this.logger.error({ error }, 'Error getting customer schema from KiotViet');
      // Trả về schema mặc định nếu có lỗi
      return [
        'id',
        'code',
        'name',
        'contactNumber',
        'email',
        'address',
        'birthDate',
        'gender',
        'rewardPoint',
        'debt',
        'groups',
      ];
    }
  }

  /**
   * Đảm bảo có access token hợp lệ
   */
  private async ensureAccessToken(): Promise<void> {
    // Kiểm tra nếu access token đã hết hạn hoặc chưa có
    if (!this.accessToken || !this.tokenExpiresAt || this.tokenExpiresAt <= new Date()) {
      await this.getAccessToken();
    }
  }

  /**
   * Lấy access token từ KiotViet
   */
  private async getAccessToken(): Promise<void> {
    try {
      const { client_id, client_secret, retailer } = this.credentials;
      
      // Chuẩn bị body request
      const body = new URLSearchParams();
      body.append('client_id', client_id);
      body.append('client_secret', client_secret);
      body.append('grant_type', 'client_credentials');
      body.append('scopes', 'PublicApi');
      
      // Gọi API để lấy token
      const response = await fetch('https://id.kiotviet.vn/connect/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: body.toString(),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get access token: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const data = await response.json();
      
      // Lưu access token và thời gian hết hạn
      this.accessToken = data.access_token;
      
      // Tính thời gian hết hạn (thường là 1 giờ)
      const expiresIn = data.expires_in || 3600;
      this.tokenExpiresAt = new Date(Date.now() + expiresIn * 1000);
      
      // Cập nhật credentials
      this.credentials.access_token = data.access_token;
      this.credentials.refresh_token = data.refresh_token;
      this.credentials.token_expires_at = this.tokenExpiresAt.toISOString();
      
      this.logger.info('KiotViet access token obtained successfully');
    } catch (error) {
      this.logger.error({ error }, 'Error getting access token from KiotViet');
      throw error;
    }
  }

  /**
   * Gọi API đến KiotViet
   * @param endpoint Endpoint
   * @param method Method
   * @param queryParams Query parameters
   * @param body Body
   * @returns Promise<any> Kết quả từ API
   */
  private async callKiotVietAPI(
    endpoint: string,
    method: string = 'GET',
    queryParams?: Record<string, any> | null,
    body?: any
  ): Promise<any> {
    try {
      // Đảm bảo có access token
      await this.ensureAccessToken();
      
      // Xây dựng URL
      let url = `${this.baseUrl}${endpoint}`;
      
      // Thêm query params nếu có
      if (queryParams) {
        const params = new URLSearchParams();
        for (const [key, value] of Object.entries(queryParams)) {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString());
          }
        }
        
        const queryString = params.toString();
        if (queryString) {
          url += (url.includes('?') ? '&' : '?') + queryString;
        }
      }
      
      // Chuẩn bị headers
      const headers: Record<string, string> = {
        'Authorization': `Bearer ${this.accessToken}`,
        'Retailer': this.credentials.retailer,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };
      
      // Chuẩn bị options
      const options: RequestInit = {
        method,
        headers,
        cache: 'no-store',
      };
      
      // Thêm body nếu cần
      if (body && method !== 'GET') {
        options.body = JSON.stringify(body);
      }
      
      // Gọi API
      const response = await fetch(url, options);
      
      // Kiểm tra response
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`KiotViet API request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      // Parse JSON
      const data = await response.json();
      return data;
    } catch (error) {
      this.logger.error({ error, endpoint }, 'Error calling KiotViet API');
      throw error;
    }
  }
}
