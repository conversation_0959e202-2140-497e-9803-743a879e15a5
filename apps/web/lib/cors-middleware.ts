import { NextRequest, NextResponse } from 'next/server';

import corsConfig from '~/config/cors.config';

/**
 * Dynamic CORS middleware that supports:
 * - Static allowed origins from config
 * - All localhost ports (http://localhost:*)
 * - Development flexibility
 */
export function createCorsResponse(
  request: NextRequest,
  response?: NextResponse
): NextResponse {
  const origin = request.headers.get('origin');
  const res = response || new NextResponse();

  // Check if origin is allowed
  const isAllowed = isOriginAllowed(origin);

  if (isAllowed) {
    res.headers.set('Access-Control-Allow-Origin', origin!);
  }

  res.headers.set('Access-Control-Allow-Methods', corsConfig.allowedMethods);
  res.headers.set('Access-Control-Allow-Headers', corsConfig.allowedHeaders);
  res.headers.set('Access-Control-Allow-Credentials', 'true');

  return res;
}

/**
 * Check if origin is allowed
 * Supports:
 * - Static origins from config
 * - All localhost ports in development
 */
function isOriginAllowed(origin: string | null): boolean {
  if (!origin) return false;

  // Check static allowed origins
  if (corsConfig.allowedOrigins.includes(origin)) {
    return true;
  }

  // In development, allow all localhost ports
  if (process.env.NODE_ENV === 'development') {
    const localhostPattern = /^http:\/\/localhost:\d+$/;
    if (localhostPattern.test(origin)) {
      return true;
    }

    // Also allow 127.0.0.1
    const localhostIpPattern = /^http:\/\/127\.0\.0\.1:\d+$/;
    if (localhostIpPattern.test(origin)) {
      return true;
    }
  }

  return false;
}

/**
 * Handle CORS preflight requests
 */
export function handleCorsPreflightRequest(request: NextRequest): NextResponse {
  const origin = request.headers.get('origin');
  
  if (!isOriginAllowed(origin)) {
    return new NextResponse(null, { status: 403 });
  }

  return createCorsResponse(request);
}

/**
 * Check if request requires CORS handling
 */
export function requiresCors(pathname: string): boolean {
  return corsConfig.requireCorsPaths.some(path => pathname.startsWith(path));
}
