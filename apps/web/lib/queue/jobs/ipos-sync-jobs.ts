import { Queue, Worker, QueueScheduler } from 'bullmq';
import { getLogger } from '@kit/logger';
import { prisma } from '@kit/db';
import { IPOSConnector } from '@/lib/integrations/connectors/ipos/ipos-connector';
import { IPOSMapper } from '@/lib/integrations/connectors/ipos/ipos-mapper';
import { IPOSSync } from '@/lib/integrations/connectors/ipos/ipos-sync';
import { redisConnection } from '../redis-connection';

const logger = getLogger({ service: 'ipos-sync-jobs' });

// Queue names
export const IPOS_SYNC_QUEUE = 'ipos-sync-queue';

// Job types
export const IPOS_SYNC_BRANCHES = 'sync:branches';
export const IPOS_SYNC_CATEGORIES = 'sync:categories';
export const IPOS_SYNC_PRODUCTS = 'sync:products';
export const IPOS_SYNC_USERS = 'sync:users';
export const IPOS_SYNC_VOUCHERS = 'sync:vouchers';
export const IPOS_SYNC_ORDERS = 'sync:orders';
export const IPOS_PUSH_ORDER = 'push:order';
export const IPOS_PUSH_VOUCHER = 'push:voucher';

// Create queue
export const iposSyncQueue = new Queue(IPOS_SYNC_QUEUE, {
  connection: redisConnection,
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 5000,
    },
    removeOnComplete: true,
    removeOnFail: 100,
  },
});

// Create scheduler
export const iposSyncScheduler = new QueueScheduler(IPOS_SYNC_QUEUE, {
  connection: redisConnection,
});

// Create worker
export const iposSyncWorker = new Worker(
  IPOS_SYNC_QUEUE,
  async (job) => {
    const { data } = job;
    const { type, accountId, integrationId, resourceId } = data;

    logger.info({ type, accountId, integrationId, resourceId }, 'Processing iPOS sync job');

    try {
      // Lấy thông tin tích hợp
      const integration = await prisma.integrations.findUnique({
        where: { id: integrationId }
      });

      if (!integration) {
        throw new Error(`Integration not found: ${integrationId}`);
      }

      // Lấy cấu hình từ integration
      const config = integration.config as any;
      const credentials = {
        access_token: config.access_token,
        pos_parent: config.pos_parent,
        pos_id: config.pos_id,
        baseUrl: config.baseUrl || 'https://api.foodbook.vn'
      };

      // Khởi tạo connector, mapper và sync
      const connector = new IPOSConnector(credentials, logger);
      const mapper = new IPOSMapper(logger);
      const sync = new IPOSSync(connector, mapper, logger, accountId, integrationId);

      // Xử lý job theo loại
      switch (type) {
        case IPOS_SYNC_BRANCHES:
          return await sync.syncBranches();

        case IPOS_SYNC_CATEGORIES:
          return await sync.syncCategories();

        case IPOS_SYNC_PRODUCTS:
          return await sync.syncProducts();

        case IPOS_SYNC_USERS:
          if (!resourceId) {
            throw new Error('Missing resourceId (phone) for user sync');
          }
          return await sync.syncCustomer(resourceId);

        case IPOS_SYNC_VOUCHERS:
          if (!resourceId) {
            throw new Error('Missing resourceId (membershipId) for voucher sync');
          }
          return await sync.syncVouchers(resourceId);

        case IPOS_SYNC_ORDERS:
          if (!resourceId) {
            throw new Error('Missing resourceId (foodbookCode) for order sync');
          }
          return await sync.syncOrderStatus(resourceId);

        case IPOS_PUSH_ORDER:
          if (!resourceId) {
            throw new Error('Missing resourceId (orderId) for order push');
          }
          return await sync.createOrder(resourceId);

        case IPOS_PUSH_VOUCHER:
          // Implement voucher push logic
          throw new Error('Not implemented');

        default:
          throw new Error(`Unknown job type: ${type}`);
      }
    } catch (error) {
      logger.error({ error, type, accountId, integrationId }, 'Error processing iPOS sync job');
      throw error;
    }
  },
  { connection: redisConnection, concurrency: 5 }
);

// Handle worker events
iposSyncWorker.on('completed', (job) => {
  logger.info(
    { jobId: job.id, type: job.data.type, result: job.returnvalue },
    'iPOS sync job completed'
  );
});

iposSyncWorker.on('failed', (job, error) => {
  logger.error(
    { jobId: job?.id, type: job?.data.type, error },
    'iPOS sync job failed'
  );
});

/**
 * Thêm job đồng bộ chi nhánh vào queue
 * @param accountId ID tài khoản
 * @param integrationId ID tích hợp
 */
export async function queueSyncBranches(accountId: string, integrationId: string) {
  return await iposSyncQueue.add(
    IPOS_SYNC_BRANCHES,
    { type: IPOS_SYNC_BRANCHES, accountId, integrationId },
    { jobId: `${IPOS_SYNC_BRANCHES}-${integrationId}` }
  );
}

/**
 * Thêm job đồng bộ danh mục vào queue
 * @param accountId ID tài khoản
 * @param integrationId ID tích hợp
 */
export async function queueSyncCategories(accountId: string, integrationId: string) {
  return await iposSyncQueue.add(
    IPOS_SYNC_CATEGORIES,
    { type: IPOS_SYNC_CATEGORIES, accountId, integrationId },
    { jobId: `${IPOS_SYNC_CATEGORIES}-${integrationId}` }
  );
}

/**
 * Thêm job đồng bộ sản phẩm vào queue
 * @param accountId ID tài khoản
 * @param integrationId ID tích hợp
 */
export async function queueSyncProducts(accountId: string, integrationId: string) {
  return await iposSyncQueue.add(
    IPOS_SYNC_PRODUCTS,
    { type: IPOS_SYNC_PRODUCTS, accountId, integrationId },
    { jobId: `${IPOS_SYNC_PRODUCTS}-${integrationId}` }
  );
}

/**
 * Thêm job đồng bộ khách hàng vào queue
 * @param accountId ID tài khoản
 * @param integrationId ID tích hợp
 * @param phone Số điện thoại khách hàng
 */
export async function queueSyncUser(accountId: string, integrationId: string, phone: string) {
  return await iposSyncQueue.add(
    IPOS_SYNC_USERS,
    { type: IPOS_SYNC_USERS, accountId, integrationId, resourceId: phone },
    { jobId: `${IPOS_SYNC_USERS}-${phone}-${integrationId}` }
  );
}

/**
 * Thêm job đồng bộ voucher vào queue
 * @param accountId ID tài khoản
 * @param integrationId ID tích hợp
 * @param membershipId ID thành viên
 */
export async function queueSyncVouchers(accountId: string, integrationId: string, membershipId: string) {
  return await iposSyncQueue.add(
    IPOS_SYNC_VOUCHERS,
    { type: IPOS_SYNC_VOUCHERS, accountId, integrationId, resourceId: membershipId },
    { jobId: `${IPOS_SYNC_VOUCHERS}-${membershipId}-${integrationId}` }
  );
}

/**
 * Thêm job đồng bộ trạng thái đơn hàng vào queue
 * @param accountId ID tài khoản
 * @param integrationId ID tích hợp
 * @param foodbookCode Mã đơn hàng
 */
export async function queueSyncOrderStatus(accountId: string, integrationId: string, foodbookCode: string) {
  return await iposSyncQueue.add(
    IPOS_SYNC_ORDERS,
    { type: IPOS_SYNC_ORDERS, accountId, integrationId, resourceId: foodbookCode },
    { jobId: `${IPOS_SYNC_ORDERS}-${foodbookCode}-${integrationId}` }
  );
}

/**
 * Thêm job tạo đơn hàng vào queue
 * @param accountId ID tài khoản
 * @param integrationId ID tích hợp
 * @param orderId ID đơn hàng
 */
export async function queueCreateOrder(accountId: string, integrationId: string, orderId: string) {
  return await iposSyncQueue.add(
    IPOS_PUSH_ORDER,
    { type: IPOS_PUSH_ORDER, accountId, integrationId, resourceId: orderId },
    { jobId: `${IPOS_PUSH_ORDER}-${orderId}-${integrationId}` }
  );
}

/**
 * Lên lịch đồng bộ định kỳ
 * @param accountId ID tài khoản
 * @param integrationId ID tích hợp
 * @param interval Khoảng thời gian (ms)
 */
export async function schedulePeriodicSync(accountId: string, integrationId: string, interval: number) {
  // Lên lịch đồng bộ chi nhánh
  await iposSyncQueue.add(
    IPOS_SYNC_BRANCHES,
    { type: IPOS_SYNC_BRANCHES, accountId, integrationId },
    { 
      jobId: `${IPOS_SYNC_BRANCHES}-${integrationId}-periodic`,
      repeat: { every: interval }
    }
  );

  // Lên lịch đồng bộ danh mục
  await iposSyncQueue.add(
    IPOS_SYNC_CATEGORIES,
    { type: IPOS_SYNC_CATEGORIES, accountId, integrationId },
    { 
      jobId: `${IPOS_SYNC_CATEGORIES}-${integrationId}-periodic`,
      repeat: { every: interval }
    }
  );

  // Lên lịch đồng bộ sản phẩm
  await iposSyncQueue.add(
    IPOS_SYNC_PRODUCTS,
    { type: IPOS_SYNC_PRODUCTS, accountId, integrationId },
    { 
      jobId: `${IPOS_SYNC_PRODUCTS}-${integrationId}-periodic`,
      repeat: { every: interval }
    }
  );
}

/**
 * Hủy lịch đồng bộ định kỳ
 * @param integrationId ID tích hợp
 */
export async function cancelPeriodicSync(integrationId: string) {
  const repeatableJobs = await iposSyncQueue.getRepeatableJobs();
  
  for (const job of repeatableJobs) {
    if (job.id.includes(integrationId)) {
      await iposSyncQueue.removeRepeatableByKey(job.key);
    }
  }
}
