import Redis from 'ioredis';
import { getLogger } from '@kit/logger';

const logger = getLogger({ service: 'redis-connection' });

// Lấy Redis URL từ biến môi trường
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';

// Tạo kết nối Redis
export const redisConnection = new Redis(REDIS_URL, {
  maxRetriesPerRequest: 3,
  enableReadyCheck: false,
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
});

// Xử lý sự kiện kết nối
redisConnection.on('connect', () => {
  logger.info('Connected to Redis');
});

redisConnection.on('error', (error) => {
  logger.error({ error }, 'Redis connection error');
});

redisConnection.on('reconnecting', () => {
  logger.info('Reconnecting to Redis');
});

// <PERSON>àm kiểm tra kết nối Redis
export async function checkRedisConnection(): Promise<boolean> {
  try {
    const pong = await redisConnection.ping();
    return pong === 'PONG';
  } catch (error) {
    logger.error({ error }, 'Error checking Redis connection');
    return false;
  }
}
