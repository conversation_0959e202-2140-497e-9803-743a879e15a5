import { Product } from '~/app/api/products/_lib/types';

/**
 * Calculate the final price of a product after applying all discounts
 * @param product The product object
 * @returns The final price after all discounts
 */
export function getProductFinalPrice(product: Product): number {
  if (!product) return 0;
  
  // If there's a flash sale, apply the discount
  if (product.flash_sale) {
    return product.price * (1 - product.flash_sale.discount_percentage / 100);
  }
  
  // If there's a compare_at_price and it's higher than the regular price,
  // the regular price is already discounted
  if (product.compare_at_price && product.compare_at_price > product.price) {
    return product.price;
  }
  
  return product.price;
}

/**
 * Calculate the discount percentage between original price and final price
 * @param originalPrice The original price
 * @param finalPrice The final price after discount
 * @returns The discount percentage (0-100)
 */
export function calculateDiscountPercentage(originalPrice: number, finalPrice: number): number {
  if (!originalPrice || originalPrice <= 0 || !finalPrice || finalPrice <= 0) {
    return 0;
  }
  
  if (finalPrice >= originalPrice) {
    return 0;
  }
  
  const percentage = ((originalPrice - finalPrice) / originalPrice) * 100;
  return Math.round(percentage * 10) / 10; // Round to 1 decimal place
}

/**
 * Format a price with the given currency
 * @param price The price to format
 * @param currency The currency code (default: VND)
 * @returns Formatted price string
 */
export function formatPrice(price: number, currency: string = 'VND'): string {
  if (price === undefined || price === null) {
    return '';
  }
  
  // For VND, don't show decimal places
  if (currency === 'VND') {
    return price.toLocaleString('vi-VN') + '₫';
  }
  
  // For other currencies, use the locale and currency
  return price.toLocaleString('en-US', {
    style: 'currency',
    currency: currency,
  });
}

/**
 * Check if a product is on sale (has a discount)
 * @param product The product object
 * @returns True if the product is on sale
 */
export function isProductOnSale(product: Product): boolean {
  if (!product) return false;
  
  // Check if there's an active flash sale
  if (product.flash_sale) {
    return true;
  }
  
  // Check if there's a compare_at_price and it's higher than the regular price
  if (product.compare_at_price && product.compare_at_price > product.price) {
    return true;
  }
  
  return false;
}

/**
 * Get the original price of a product (before any discounts)
 * @param product The product object
 * @returns The original price
 */
export function getProductOriginalPrice(product: Product): number {
  if (!product) return 0;
  
  // If there's a flash sale, the original price is the regular price
  if (product.flash_sale) {
    return product.price;
  }
  
  // If there's a compare_at_price and it's higher than the regular price,
  // the original price is the compare_at_price
  if (product.compare_at_price && product.compare_at_price > product.price) {
    return product.compare_at_price;
  }
  
  return product.price;
}
