export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      account_themes: {
        Row: {
          account_id: string
          config: <PERSON><PERSON>
          created_at: string | null
          id: string
          is_active: boolean | null
          mini_app_id: string | null
          name: string
          oa_config_id: string | null
          template_id: string | null
          updated_at: string | null
          version: string | null
        }
        Insert: {
          account_id: string
          config?: Json
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          mini_app_id?: string | null
          name: string
          oa_config_id?: string | null
          template_id?: string | null
          updated_at?: string | null
          version?: string | null
        }
        Update: {
          account_id?: string
          config?: Json
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          mini_app_id?: string | null
          name?: string
          oa_config_id?: string | null
          template_id?: string | null
          updated_at?: string | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "account_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_themes_oa_config_id_fkey"
            columns: ["oa_config_id"]
            isOneToOne: false
            referencedRelation: "oa_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_themes_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "themes"
            referencedColumns: ["id"]
          },
        ]
      }
      account_usage_stats: {
        Row: {
          account_id: string
          counters: Json | null
          last_updated: string | null
        }
        Insert: {
          account_id: string
          counters?: Json | null
          last_updated?: string | null
        }
        Update: {
          account_id?: string
          counters?: Json | null
          last_updated?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "account_usage_stats_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_usage_stats_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "account_usage_stats_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      accounts: {
        Row: {
          created_at: string | null
          created_by: string | null
          education_settings: Json | null
          email: string | null
          id: string
          is_personal_account: boolean
          name: string
          phone: string | null
          picture_url: string | null
          primary_owner_user_id: string
          public_data: Json
          slug: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          education_settings?: Json | null
          email?: string | null
          id?: string
          is_personal_account?: boolean
          name: string
          phone?: string | null
          picture_url?: string | null
          primary_owner_user_id?: string
          public_data?: Json
          slug?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          education_settings?: Json | null
          email?: string | null
          id?: string
          is_personal_account?: boolean
          name?: string
          phone?: string | null
          picture_url?: string | null
          primary_owner_user_id?: string
          public_data?: Json
          slug?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      accounts_memberships: {
        Row: {
          account_id: string
          account_role: string
          created_at: string
          created_by: string | null
          updated_at: string
          updated_by: string | null
          user_id: string
        }
        Insert: {
          account_id: string
          account_role: string
          created_at?: string
          created_by?: string | null
          updated_at?: string
          updated_by?: string | null
          user_id: string
        }
        Update: {
          account_id?: string
          account_role?: string
          created_at?: string
          created_by?: string | null
          updated_at?: string
          updated_by?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_role_fkey"
            columns: ["account_role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      achievements: {
        Row: {
          account_id: string
          achievement_type: string
          badge_color: string | null
          created_at: string | null
          criteria: Json
          description: string | null
          icon: string | null
          id: string
          is_active: boolean | null
          points_reward: number | null
          title: string
        }
        Insert: {
          account_id: string
          achievement_type: string
          badge_color?: string | null
          created_at?: string | null
          criteria: Json
          description?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          points_reward?: number | null
          title: string
        }
        Update: {
          account_id?: string
          achievement_type?: string
          badge_color?: string | null
          created_at?: string | null
          criteria?: Json
          description?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          points_reward?: number | null
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "achievements_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "achievements_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "achievements_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      ai_insights: {
        Row: {
          account_id: string
          category: string
          confidence: number | null
          created_at: string | null
          data: Json | null
          description: string | null
          id: string
          impact: string | null
          recommendations: string[] | null
          status: string | null
          title: string
          type: string
          updated_at: string | null
        }
        Insert: {
          account_id: string
          category: string
          confidence?: number | null
          created_at?: string | null
          data?: Json | null
          description?: string | null
          id?: string
          impact?: string | null
          recommendations?: string[] | null
          status?: string | null
          title: string
          type: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          category?: string
          confidence?: number | null
          created_at?: string | null
          data?: Json | null
          description?: string | null
          id?: string
          impact?: string | null
          recommendations?: string[] | null
          status?: string | null
          title?: string
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ai_insights_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ai_insights_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ai_insights_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      analytics_data: {
        Row: {
          account_id: string
          id: string
          metadata: Json | null
          metric_name: string
          metric_type: string
          metric_value: number
          period: string | null
          recorded_at: string | null
        }
        Insert: {
          account_id: string
          id?: string
          metadata?: Json | null
          metric_name: string
          metric_type: string
          metric_value: number
          period?: string | null
          recorded_at?: string | null
        }
        Update: {
          account_id?: string
          id?: string
          metadata?: Json | null
          metric_name?: string
          metric_type?: string
          metric_value?: number
          period?: string | null
          recorded_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "analytics_data_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analytics_data_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analytics_data_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      analytics_events: {
        Row: {
          account_id: string
          created_at: string
          device_type: string | null
          event_data: Json
          event_type: string
          id: string
          source: string | null
          theme_id: string | null
          user_id: string | null
          visitor_id: string | null
        }
        Insert: {
          account_id: string
          created_at?: string
          device_type?: string | null
          event_data?: Json
          event_type: string
          id?: string
          source?: string | null
          theme_id?: string | null
          user_id?: string | null
          visitor_id?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string
          device_type?: string | null
          event_data?: Json
          event_type?: string
          id?: string
          source?: string | null
          theme_id?: string | null
          user_id?: string | null
          visitor_id?: string | null
        }
        Relationships: []
      }
      attendance: {
        Row: {
          account_id: string
          attendance_method: string | null
          check_in_time: string | null
          check_out_time: string | null
          confidence_score: number | null
          created_at: string | null
          created_by: string | null
          device_info: Json | null
          id: string
          learner_id: string
          location_info: Json | null
          metadata: Json | null
          notes: string | null
          photo_url: string | null
          program_id: string
          session_date: string
          session_time: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          attendance_method?: string | null
          check_in_time?: string | null
          check_out_time?: string | null
          confidence_score?: number | null
          created_at?: string | null
          created_by?: string | null
          device_info?: Json | null
          id?: string
          learner_id: string
          location_info?: Json | null
          metadata?: Json | null
          notes?: string | null
          photo_url?: string | null
          program_id: string
          session_date: string
          session_time?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          attendance_method?: string | null
          check_in_time?: string | null
          check_out_time?: string | null
          confidence_score?: number | null
          created_at?: string | null
          created_by?: string | null
          device_info?: Json | null
          id?: string
          learner_id?: string
          location_info?: Json | null
          metadata?: Json | null
          notes?: string | null
          photo_url?: string | null
          program_id?: string
          session_date?: string
          session_time?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_learner_id_fkey"
            columns: ["learner_id"]
            isOneToOne: false
            referencedRelation: "learners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_program_id_fkey"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_analytics: {
        Row: {
          absent_sessions: number | null
          account_id: string
          analysis_date: string
          analysis_type: string | null
          attendance_rate: number | null
          created_at: string | null
          excused_sessions: number | null
          id: string
          late_sessions: number | null
          learner_id: string | null
          metadata: Json | null
          patterns: Json | null
          present_sessions: number | null
          program_id: string
          recommendations: Json | null
          risk_score: number | null
          streak_absent: number | null
          streak_present: number | null
          total_sessions: number | null
          updated_at: string | null
        }
        Insert: {
          absent_sessions?: number | null
          account_id: string
          analysis_date: string
          analysis_type?: string | null
          attendance_rate?: number | null
          created_at?: string | null
          excused_sessions?: number | null
          id?: string
          late_sessions?: number | null
          learner_id?: string | null
          metadata?: Json | null
          patterns?: Json | null
          present_sessions?: number | null
          program_id: string
          recommendations?: Json | null
          risk_score?: number | null
          streak_absent?: number | null
          streak_present?: number | null
          total_sessions?: number | null
          updated_at?: string | null
        }
        Update: {
          absent_sessions?: number | null
          account_id?: string
          analysis_date?: string
          analysis_type?: string | null
          attendance_rate?: number | null
          created_at?: string | null
          excused_sessions?: number | null
          id?: string
          late_sessions?: number | null
          learner_id?: string | null
          metadata?: Json | null
          patterns?: Json | null
          present_sessions?: number | null
          program_id?: string
          recommendations?: Json | null
          risk_score?: number | null
          streak_absent?: number | null
          streak_present?: number | null
          total_sessions?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_analytics_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_analytics_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_analytics_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_analytics_learner_id_fkey"
            columns: ["learner_id"]
            isOneToOne: false
            referencedRelation: "learners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_analytics_program_id_fkey"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_reports: {
        Row: {
          account_id: string
          charts_config: Json | null
          created_at: string | null
          created_by: string | null
          date_range: Json
          expires_at: string | null
          file_url: string | null
          filters: Json | null
          generated_at: string | null
          id: string
          is_public: boolean | null
          report_data: Json | null
          report_name: string
          report_scope: string | null
          report_type: string | null
          scope_id: string | null
          share_token: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          charts_config?: Json | null
          created_at?: string | null
          created_by?: string | null
          date_range: Json
          expires_at?: string | null
          file_url?: string | null
          filters?: Json | null
          generated_at?: string | null
          id?: string
          is_public?: boolean | null
          report_data?: Json | null
          report_name: string
          report_scope?: string | null
          report_type?: string | null
          scope_id?: string | null
          share_token?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          charts_config?: Json | null
          created_at?: string | null
          created_by?: string | null
          date_range?: Json
          expires_at?: string | null
          file_url?: string | null
          filters?: Json | null
          generated_at?: string | null
          id?: string
          is_public?: boolean | null
          report_data?: Json | null
          report_name?: string
          report_scope?: string | null
          report_type?: string | null
          scope_id?: string | null
          share_token?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_reports_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_reports_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_reports_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_sessions: {
        Row: {
          absent_count: number | null
          account_id: string
          created_at: string | null
          created_by: string | null
          id: string
          instructor_id: string | null
          late_count: number | null
          metadata: Json | null
          notes: string | null
          present_count: number | null
          program_id: string
          session_date: string
          session_time: string | null
          session_type: string | null
          status: string | null
          total_students: number | null
          updated_at: string | null
        }
        Insert: {
          absent_count?: number | null
          account_id: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          instructor_id?: string | null
          late_count?: number | null
          metadata?: Json | null
          notes?: string | null
          present_count?: number | null
          program_id: string
          session_date: string
          session_time?: string | null
          session_type?: string | null
          status?: string | null
          total_students?: number | null
          updated_at?: string | null
        }
        Update: {
          absent_count?: number | null
          account_id?: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          instructor_id?: string | null
          late_count?: number | null
          metadata?: Json | null
          notes?: string | null
          present_count?: number | null
          program_id?: string
          session_date?: string
          session_time?: string | null
          session_type?: string | null
          status?: string | null
          total_students?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_sessions_instructor_id_fkey"
            columns: ["instructor_id"]
            isOneToOne: false
            referencedRelation: "instructors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_sessions_program_id_fkey"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      attendance_templates: {
        Row: {
          account_id: string
          created_at: string | null
          created_by: string | null
          default_method: string | null
          description: string | null
          id: string
          is_active: boolean | null
          learner_ids: string[] | null
          name: string
          program_id: string | null
          settings: Json | null
          template_type: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          created_by?: string | null
          default_method?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          learner_ids?: string[] | null
          name: string
          program_id?: string | null
          settings?: Json | null
          template_type?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          created_by?: string | null
          default_method?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          learner_ids?: string[] | null
          name?: string
          program_id?: string | null
          settings?: Json | null
          template_type?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "attendance_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "attendance_templates_program_id_fkey"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      automation_logs: {
        Row: {
          account_id: string
          actions_performed: Json | null
          created_at: string | null
          error_details: Json | null
          execution_status: string | null
          execution_time_ms: number | null
          id: string
          results: Json | null
          rule_id: string
          target_users: Json | null
          trigger_data: Json
        }
        Insert: {
          account_id: string
          actions_performed?: Json | null
          created_at?: string | null
          error_details?: Json | null
          execution_status?: string | null
          execution_time_ms?: number | null
          id?: string
          results?: Json | null
          rule_id: string
          target_users?: Json | null
          trigger_data: Json
        }
        Update: {
          account_id?: string
          actions_performed?: Json | null
          created_at?: string | null
          error_details?: Json | null
          execution_status?: string | null
          execution_time_ms?: number | null
          id?: string
          results?: Json | null
          rule_id?: string
          target_users?: Json | null
          trigger_data?: Json
        }
        Relationships: [
          {
            foreignKeyName: "automation_logs_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "automation_logs_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "automation_logs_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "automation_logs_rule_id_fkey"
            columns: ["rule_id"]
            isOneToOne: false
            referencedRelation: "automation_rules"
            referencedColumns: ["id"]
          },
        ]
      }
      automation_rules: {
        Row: {
          account_id: string
          actions: Json
          created_at: string | null
          created_by: string | null
          failure_count: number | null
          id: string
          is_active: boolean | null
          last_triggered_at: string | null
          metadata: Json | null
          priority: number | null
          rule_name: string
          rule_type: string | null
          success_count: number | null
          target_audience: Json | null
          trigger_conditions: Json
          trigger_count: number | null
          trigger_frequency: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          actions: Json
          created_at?: string | null
          created_by?: string | null
          failure_count?: number | null
          id?: string
          is_active?: boolean | null
          last_triggered_at?: string | null
          metadata?: Json | null
          priority?: number | null
          rule_name: string
          rule_type?: string | null
          success_count?: number | null
          target_audience?: Json | null
          trigger_conditions: Json
          trigger_count?: number | null
          trigger_frequency?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          actions?: Json
          created_at?: string | null
          created_by?: string | null
          failure_count?: number | null
          id?: string
          is_active?: boolean | null
          last_triggered_at?: string | null
          metadata?: Json | null
          priority?: number | null
          rule_name?: string
          rule_type?: string | null
          success_count?: number | null
          target_audience?: Json | null
          trigger_conditions?: Json
          trigger_count?: number | null
          trigger_frequency?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "automation_rules_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "automation_rules_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "automation_rules_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      billing_customers: {
        Row: {
          account_id: string
          customer_id: string
          email: string | null
          id: number
          provider: Database["public"]["Enums"]["billing_provider"]
        }
        Insert: {
          account_id: string
          customer_id: string
          email?: string | null
          id?: number
          provider: Database["public"]["Enums"]["billing_provider"]
        }
        Update: {
          account_id?: string
          customer_id?: string
          email?: string | null
          id?: number
          provider?: Database["public"]["Enums"]["billing_provider"]
        }
        Relationships: [
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      branch_products: {
        Row: {
          branch_id: string
          created_at: string | null
          created_by: string | null
          id: string
          is_active: boolean
          product_id: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          branch_id: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean
          product_id: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          branch_id?: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean
          product_id?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "branch_products_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "branch_products_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      branches: {
        Row: {
          account_id: string
          address: string | null
          created_at: string | null
          id: string
          is_active: boolean | null
          location: string | null
          name: string
          phone: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          address?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          location?: string | null
          name: string
          phone?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          address?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          location?: string | null
          name?: string
          phone?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "branches_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "branches_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "branches_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          account_id: string
          created_at: string | null
          description: string | null
          id: string
          image_url: string | null
          name: string
          parent_id: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          name: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          name?: string
          parent_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "categories_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "categories_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "categories_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      config: {
        Row: {
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing: boolean
          enable_team_account_billing: boolean
          enable_team_accounts: boolean
        }
        Insert: {
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing?: boolean
          enable_team_account_billing?: boolean
          enable_team_accounts?: boolean
        }
        Update: {
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing?: boolean
          enable_team_account_billing?: boolean
          enable_team_accounts?: boolean
        }
        Relationships: []
      }
      curriculum: {
        Row: {
          account_id: string
          activities: Json | null
          age_group: string | null
          assessment_criteria: Json | null
          created_at: string | null
          created_by: string | null
          description: string | null
          difficulty_level: string | null
          duration_minutes: number | null
          id: string
          materials_needed: Json | null
          objectives: Json | null
          program_id: string | null
          status: string | null
          subject: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          account_id: string
          activities?: Json | null
          age_group?: string | null
          assessment_criteria?: Json | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          difficulty_level?: string | null
          duration_minutes?: number | null
          id?: string
          materials_needed?: Json | null
          objectives?: Json | null
          program_id?: string | null
          status?: string | null
          subject?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          activities?: Json | null
          age_group?: string | null
          assessment_criteria?: Json | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          difficulty_level?: string | null
          duration_minutes?: number | null
          id?: string
          materials_needed?: Json | null
          objectives?: Json | null
          program_id?: string | null
          status?: string | null
          subject?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "curriculum_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "curriculum_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "curriculum_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "curriculum_program_id_fkey"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_activities: {
        Row: {
          account_id: string
          action: string
          created_at: string | null
          customer_id: string
          details: Json | null
          id: string
        }
        Insert: {
          account_id: string
          action: string
          created_at?: string | null
          customer_id: string
          details?: Json | null
          id?: string
        }
        Update: {
          account_id?: string
          action?: string
          created_at?: string | null
          customer_id?: string
          details?: Json | null
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "customer_activities_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_activities_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_activities_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_journeys: {
        Row: {
          account_id: string
          completion_rate: number | null
          created_at: string | null
          description: string | null
          id: string
          metadata: Json | null
          name: string
          participants: number | null
          published_at: string | null
          status: string | null
          steps: Json | null
          tags: string[] | null
          trigger_config: Json | null
          trigger_type: string
          updated_at: string | null
        }
        Insert: {
          account_id: string
          completion_rate?: number | null
          created_at?: string | null
          description?: string | null
          id?: string
          metadata?: Json | null
          name: string
          participants?: number | null
          published_at?: string | null
          status?: string | null
          steps?: Json | null
          tags?: string[] | null
          trigger_config?: Json | null
          trigger_type: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          completion_rate?: number | null
          created_at?: string | null
          description?: string | null
          id?: string
          metadata?: Json | null
          name?: string
          participants?: number | null
          published_at?: string | null
          status?: string | null
          steps?: Json | null
          tags?: string[] | null
          trigger_config?: Json | null
          trigger_type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_journeys_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_journeys_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_journeys_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_order_items: {
        Row: {
          attribute_id: string | null
          created_at: string | null
          created_by: string | null
          discount_percentage: number | null
          flash_sale_id: string | null
          id: string
          order_id: string
          original_price: number | null
          price: number
          product_id: string
          quantity: number
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          attribute_id?: string | null
          created_at?: string | null
          created_by?: string | null
          discount_percentage?: number | null
          flash_sale_id?: string | null
          id?: string
          order_id: string
          original_price?: number | null
          price: number
          product_id: string
          quantity: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          attribute_id?: string | null
          created_at?: string | null
          created_by?: string | null
          discount_percentage?: number | null
          flash_sale_id?: string | null
          id?: string
          order_id?: string
          original_price?: number | null
          price?: number
          product_id?: string
          quantity?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_order_items_attribute_id_fkey"
            columns: ["attribute_id"]
            isOneToOne: false
            referencedRelation: "product_attributes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "customer_orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_order_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_orders: {
        Row: {
          account_id: string
          branch_id: string | null
          created_at: string | null
          created_by: string | null
          customer_id: string | null
          discount_amount: number
          id: string
          metadata: Json | null
          order_code: string | null
          payment_method: string | null
          status: string
          subtotal: number
          total_amount: number
          updated_at: string | null
          updated_by: string | null
          voucher_id: string | null
          webhook_data: Json | null
          webhook_processed: boolean | null
          webhook_url: string | null
        }
        Insert: {
          account_id: string
          branch_id?: string | null
          created_at?: string | null
          created_by?: string | null
          customer_id?: string | null
          discount_amount?: number
          id?: string
          metadata?: Json | null
          order_code?: string | null
          payment_method?: string | null
          status?: string
          subtotal?: number
          total_amount: number
          updated_at?: string | null
          updated_by?: string | null
          voucher_id?: string | null
          webhook_data?: Json | null
          webhook_processed?: boolean | null
          webhook_url?: string | null
        }
        Update: {
          account_id?: string
          branch_id?: string | null
          created_at?: string | null
          created_by?: string | null
          customer_id?: string | null
          discount_amount?: number
          id?: string
          metadata?: Json | null
          order_code?: string | null
          payment_method?: string | null
          status?: string
          subtotal?: number
          total_amount?: number
          updated_at?: string | null
          updated_by?: string | null
          voucher_id?: string | null
          webhook_data?: Json | null
          webhook_processed?: boolean | null
          webhook_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_orders_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_profiles: {
        Row: {
          avatar_url: string | null
          churn_risk_score: number | null
          created_at: string
          created_by: string | null
          customer_status: string | null
          email: string
          engagement_score: number | null
          first_name: string | null
          first_order_at: string | null
          id: string
          last_active_at: string | null
          last_name: string | null
          last_order_at: string | null
          metadata: Json | null
          phone: string | null
          team_account_id: string
          total_orders: number | null
          total_spent: number | null
          updated_at: string
          updated_by: string | null
          user_account_id: string | null
          value_tier: string | null
        }
        Insert: {
          avatar_url?: string | null
          churn_risk_score?: number | null
          created_at?: string
          created_by?: string | null
          customer_status?: string | null
          email: string
          engagement_score?: number | null
          first_name?: string | null
          first_order_at?: string | null
          id?: string
          last_active_at?: string | null
          last_name?: string | null
          last_order_at?: string | null
          metadata?: Json | null
          phone?: string | null
          team_account_id: string
          total_orders?: number | null
          total_spent?: number | null
          updated_at?: string
          updated_by?: string | null
          user_account_id?: string | null
          value_tier?: string | null
        }
        Update: {
          avatar_url?: string | null
          churn_risk_score?: number | null
          created_at?: string
          created_by?: string | null
          customer_status?: string | null
          email?: string
          engagement_score?: number | null
          first_name?: string | null
          first_order_at?: string | null
          id?: string
          last_active_at?: string | null
          last_name?: string | null
          last_order_at?: string | null
          metadata?: Json | null
          phone?: string | null
          team_account_id?: string
          total_orders?: number | null
          total_spent?: number | null
          updated_at?: string
          updated_by?: string | null
          user_account_id?: string | null
          value_tier?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_profiles_team_account_id_fkey"
            columns: ["team_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_profiles_team_account_id_fkey"
            columns: ["team_account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_profiles_team_account_id_fkey"
            columns: ["team_account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_profiles_user_account_id_fkey"
            columns: ["user_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_profiles_user_account_id_fkey"
            columns: ["user_account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_profiles_user_account_id_fkey"
            columns: ["user_account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_segments: {
        Row: {
          account_id: string
          avg_value: number | null
          created_at: string
          created_by: string | null
          criteria: Json
          customer_count: number | null
          description: string | null
          engagement_score: number | null
          growth_rate: number | null
          id: string
          is_active: boolean | null
          is_auto_updating: boolean | null
          metadata: Json | null
          name: string
          type: string
          updated_at: string
          updated_by: string | null
        }
        Insert: {
          account_id: string
          avg_value?: number | null
          created_at?: string
          created_by?: string | null
          criteria?: Json
          customer_count?: number | null
          description?: string | null
          engagement_score?: number | null
          growth_rate?: number | null
          id?: string
          is_active?: boolean | null
          is_auto_updating?: boolean | null
          metadata?: Json | null
          name: string
          type: string
          updated_at?: string
          updated_by?: string | null
        }
        Update: {
          account_id?: string
          avg_value?: number | null
          created_at?: string
          created_by?: string | null
          criteria?: Json
          customer_count?: number | null
          description?: string | null
          engagement_score?: number | null
          growth_rate?: number | null
          id?: string
          is_active?: boolean | null
          is_auto_updating?: boolean | null
          metadata?: Json | null
          name?: string
          type?: string
          updated_at?: string
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_segments_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_segments_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "customer_segments_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      enrollments: {
        Row: {
          account_id: string
          created_at: string | null
          created_by: string | null
          enrollment_date: string
          id: string
          learner_id: string
          notes: string | null
          program_id: string
          status: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          created_by?: string | null
          enrollment_date?: string
          id?: string
          learner_id: string
          notes?: string | null
          program_id: string
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          created_by?: string | null
          enrollment_date?: string
          id?: string
          learner_id?: string
          notes?: string | null
          program_id?: string
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "enrollments_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "enrollments_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "enrollments_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "enrollments_learner_id_fkey"
            columns: ["learner_id"]
            isOneToOne: false
            referencedRelation: "learners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "enrollments_program_id_fkey"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          account_id: string
          created_at: string | null
          current_participants: number | null
          description: string | null
          end_datetime: string | null
          event_type: string | null
          id: string
          location: string | null
          max_participants: number | null
          registration_required: boolean | null
          start_datetime: string
          title: string
        }
        Insert: {
          account_id: string
          created_at?: string | null
          current_participants?: number | null
          description?: string | null
          end_datetime?: string | null
          event_type?: string | null
          id?: string
          location?: string | null
          max_participants?: number | null
          registration_required?: boolean | null
          start_datetime: string
          title: string
        }
        Update: {
          account_id?: string
          created_at?: string | null
          current_participants?: number | null
          description?: string | null
          end_datetime?: string | null
          event_type?: string | null
          id?: string
          location?: string | null
          max_participants?: number | null
          registration_required?: boolean | null
          start_datetime?: string
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "events_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      fees: {
        Row: {
          account_id: string
          amount: number
          created_at: string | null
          created_by: string | null
          description: string | null
          due_date: string
          fee_category: string
          fee_type: string
          id: string
          learner_id: string
          payment_date: string | null
          payment_method: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          amount: number
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          due_date: string
          fee_category: string
          fee_type: string
          id?: string
          learner_id: string
          payment_date?: string | null
          payment_method?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          amount?: number
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          due_date?: string
          fee_category?: string
          fee_type?: string
          id?: string
          learner_id?: string
          payment_date?: string | null
          payment_method?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fees_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fees_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fees_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fees_learner_id_fkey"
            columns: ["learner_id"]
            isOneToOne: false
            referencedRelation: "learners"
            referencedColumns: ["id"]
          },
        ]
      }
      flash_sale_products: {
        Row: {
          created_at: string | null
          created_by: string | null
          discount_percentage: number
          flash_sale_id: string
          id: string
          product_id: string
          quantity_limit: number | null
          quantity_sold: number | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          discount_percentage: number
          flash_sale_id: string
          id?: string
          product_id: string
          quantity_limit?: number | null
          quantity_sold?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          discount_percentage?: number
          flash_sale_id?: string
          id?: string
          product_id?: string
          quantity_limit?: number | null
          quantity_sold?: number | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "flash_sale_products_flash_sale_id_fkey"
            columns: ["flash_sale_id"]
            isOneToOne: false
            referencedRelation: "flash_sales"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flash_sale_products_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      flash_sales: {
        Row: {
          account_id: string
          created_at: string | null
          created_by: string | null
          description: string | null
          end_time: string
          id: string
          name: string
          start_time: string
          status: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          end_time: string
          id?: string
          name: string
          start_time: string
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          end_time?: string
          id?: string
          name?: string
          start_time?: string
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "flash_sales_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flash_sales_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "flash_sales_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      game_assignments: {
        Row: {
          account_id: string
          assigned_by: string
          assigned_to_id: string
          assigned_to_type: string
          created_at: string | null
          due_date: string | null
          game_id: string
          id: string
          instructions: string | null
          is_active: boolean | null
          is_required: boolean | null
          max_attempts: number | null
          min_score: number | null
          title: string | null
        }
        Insert: {
          account_id: string
          assigned_by: string
          assigned_to_id: string
          assigned_to_type: string
          created_at?: string | null
          due_date?: string | null
          game_id: string
          id?: string
          instructions?: string | null
          is_active?: boolean | null
          is_required?: boolean | null
          max_attempts?: number | null
          min_score?: number | null
          title?: string | null
        }
        Update: {
          account_id?: string
          assigned_by?: string
          assigned_to_id?: string
          assigned_to_type?: string
          created_at?: string | null
          due_date?: string | null
          game_id?: string
          id?: string
          instructions?: string | null
          is_active?: boolean | null
          is_required?: boolean | null
          max_attempts?: number | null
          min_score?: number | null
          title?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "game_assignments_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "game_assignments_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "game_assignments_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "game_assignments_game_id_fkey"
            columns: ["game_id"]
            isOneToOne: false
            referencedRelation: "games"
            referencedColumns: ["id"]
          },
        ]
      }
      game_categories: {
        Row: {
          account_id: string
          age_group: string | null
          color: string | null
          created_at: string | null
          description: string | null
          icon: string | null
          id: string
          is_active: boolean | null
          name: string
          sort_order: number | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          age_group?: string | null
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          age_group?: string | null
          color?: string | null
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          sort_order?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "game_categories_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "game_categories_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "game_categories_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      game_sessions: {
        Row: {
          account_id: string
          accuracy_percentage: number | null
          attempts: number | null
          completed_at: string | null
          created_at: string | null
          duration_seconds: number | null
          game_data: Json | null
          game_id: string
          hints_used: number | null
          id: string
          is_completed: boolean | null
          max_score: number | null
          score: number | null
          session_type: string | null
          started_at: string | null
          student_id: string
        }
        Insert: {
          account_id: string
          accuracy_percentage?: number | null
          attempts?: number | null
          completed_at?: string | null
          created_at?: string | null
          duration_seconds?: number | null
          game_data?: Json | null
          game_id: string
          hints_used?: number | null
          id?: string
          is_completed?: boolean | null
          max_score?: number | null
          score?: number | null
          session_type?: string | null
          started_at?: string | null
          student_id: string
        }
        Update: {
          account_id?: string
          accuracy_percentage?: number | null
          attempts?: number | null
          completed_at?: string | null
          created_at?: string | null
          duration_seconds?: number | null
          game_data?: Json | null
          game_id?: string
          hints_used?: number | null
          id?: string
          is_completed?: boolean | null
          max_score?: number | null
          score?: number | null
          session_type?: string | null
          started_at?: string | null
          student_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "game_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "game_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "game_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "game_sessions_game_id_fkey"
            columns: ["game_id"]
            isOneToOne: false
            referencedRelation: "games"
            referencedColumns: ["id"]
          },
        ]
      }
      games: {
        Row: {
          account_id: string
          category_id: string
          created_at: string | null
          created_by: string | null
          description: string | null
          difficulty_level: number | null
          estimated_duration: number | null
          game_config: Json | null
          game_type: string
          id: string
          instructions: string | null
          is_active: boolean | null
          is_multiplayer: boolean | null
          learning_objectives: string[] | null
          max_age: number | null
          max_players: number | null
          min_age: number | null
          subject: string | null
          thumbnail_url: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          account_id: string
          category_id: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          difficulty_level?: number | null
          estimated_duration?: number | null
          game_config?: Json | null
          game_type: string
          id?: string
          instructions?: string | null
          is_active?: boolean | null
          is_multiplayer?: boolean | null
          learning_objectives?: string[] | null
          max_age?: number | null
          max_players?: number | null
          min_age?: number | null
          subject?: string | null
          thumbnail_url?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          category_id?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          difficulty_level?: number | null
          estimated_duration?: number | null
          game_config?: Json | null
          game_type?: string
          id?: string
          instructions?: string | null
          is_active?: boolean | null
          is_multiplayer?: boolean | null
          learning_objectives?: string[] | null
          max_age?: number | null
          max_players?: number | null
          min_age?: number | null
          subject?: string | null
          thumbnail_url?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "games_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "games_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "games_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "games_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "game_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      guardian_checkin_permissions: {
        Row: {
          created_at: string | null
          created_by: string | null
          guardian_id: string
          id: string
          is_active: boolean | null
          learner_id: string
          location_restrictions: Json | null
          metadata: Json | null
          permission_type: string | null
          requires_verification: boolean | null
          time_restrictions: Json | null
          updated_at: string | null
          valid_from: string | null
          valid_until: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          guardian_id: string
          id?: string
          is_active?: boolean | null
          learner_id: string
          location_restrictions?: Json | null
          metadata?: Json | null
          permission_type?: string | null
          requires_verification?: boolean | null
          time_restrictions?: Json | null
          updated_at?: string | null
          valid_from?: string | null
          valid_until?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          guardian_id?: string
          id?: string
          is_active?: boolean | null
          learner_id?: string
          location_restrictions?: Json | null
          metadata?: Json | null
          permission_type?: string | null
          requires_verification?: boolean | null
          time_restrictions?: Json | null
          updated_at?: string | null
          valid_from?: string | null
          valid_until?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "guardian_checkin_permissions_guardian_id_fkey"
            columns: ["guardian_id"]
            isOneToOne: false
            referencedRelation: "guardians"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guardian_checkin_permissions_learner_id_fkey"
            columns: ["learner_id"]
            isOneToOne: false
            referencedRelation: "learners"
            referencedColumns: ["id"]
          },
        ]
      }
      guardians: {
        Row: {
          account_id: string
          address: string | null
          created_at: string | null
          created_by: string | null
          email: string | null
          full_name: string
          id: string
          is_primary: boolean | null
          phone: string | null
          relationship: string | null
          status: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          account_id: string
          address?: string | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          full_name: string
          id?: string
          is_primary?: boolean | null
          phone?: string | null
          relationship?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          account_id?: string
          address?: string | null
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          full_name?: string
          id?: string
          is_primary?: boolean | null
          phone?: string | null
          relationship?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "guardians_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guardians_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guardians_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      health_records: {
        Row: {
          account_id: string
          allergies: Json | null
          created_at: string | null
          created_by: string | null
          description: string | null
          guardian_notified: boolean | null
          id: string
          is_emergency: boolean | null
          learner_id: string
          medications: Json | null
          notes: string | null
          record_date: string
          record_type: string
          title: string
          updated_at: string | null
          vaccinations: Json | null
          vital_signs: Json | null
        }
        Insert: {
          account_id: string
          allergies?: Json | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          guardian_notified?: boolean | null
          id?: string
          is_emergency?: boolean | null
          learner_id: string
          medications?: Json | null
          notes?: string | null
          record_date: string
          record_type: string
          title: string
          updated_at?: string | null
          vaccinations?: Json | null
          vital_signs?: Json | null
        }
        Update: {
          account_id?: string
          allergies?: Json | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          guardian_notified?: boolean | null
          id?: string
          is_emergency?: boolean | null
          learner_id?: string
          medications?: Json | null
          notes?: string | null
          record_date?: string
          record_type?: string
          title?: string
          updated_at?: string | null
          vaccinations?: Json | null
          vital_signs?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "health_records_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "health_records_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "health_records_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "health_records_learner_id_fkey"
            columns: ["learner_id"]
            isOneToOne: false
            referencedRelation: "learners"
            referencedColumns: ["id"]
          },
        ]
      }
      import_export_templates: {
        Row: {
          account_id: string
          created_at: string | null
          filters: Json | null
          id: string
          mapping: Json
          name: string
          resource: string
          updated_at: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          filters?: Json | null
          id?: string
          mapping: Json
          name: string
          resource: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          filters?: Json | null
          id?: string
          mapping?: Json
          name?: string
          resource?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "import_export_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "import_export_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "import_export_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      instructors: {
        Row: {
          account_id: string
          created_at: string | null
          created_by: string | null
          email: string | null
          employee_code: string | null
          experience_years: number | null
          full_name: string
          hire_date: string | null
          id: string
          phone: string | null
          qualifications: Json | null
          role: string | null
          specialization: string | null
          status: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          employee_code?: string | null
          experience_years?: number | null
          full_name: string
          hire_date?: string | null
          id?: string
          phone?: string | null
          qualifications?: Json | null
          role?: string | null
          specialization?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          employee_code?: string | null
          experience_years?: number | null
          full_name?: string
          hire_date?: string | null
          id?: string
          phone?: string | null
          qualifications?: Json | null
          role?: string | null
          specialization?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "instructors_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "instructors_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "instructors_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      integration_mappings: {
        Row: {
          created_at: string | null
          id: string
          integration_id: string | null
          is_active: boolean | null
          resource_type: string
          source_field: string
          target_field: string
          transform_function: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          integration_id?: string | null
          is_active?: boolean | null
          resource_type: string
          source_field: string
          target_field: string
          transform_function?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          integration_id?: string | null
          is_active?: boolean | null
          resource_type?: string
          source_field?: string
          target_field?: string
          transform_function?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "integration_mappings_integration_id_fkey"
            columns: ["integration_id"]
            isOneToOne: false
            referencedRelation: "integrations"
            referencedColumns: ["id"]
          },
        ]
      }
      integration_statuses: {
        Row: {
          account_id: string
          category: string
          config: Json | null
          created_at: string | null
          health_score: number | null
          id: string
          last_sync: string | null
          name: string
          provider: string
          records_synced: number | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          category: string
          config?: Json | null
          created_at?: string | null
          health_score?: number | null
          id?: string
          last_sync?: string | null
          name: string
          provider: string
          records_synced?: number | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          category?: string
          config?: Json | null
          created_at?: string | null
          health_score?: number | null
          id?: string
          last_sync?: string | null
          name?: string
          provider?: string
          records_synced?: number | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "integration_statuses_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "integration_statuses_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "integration_statuses_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      integration_sync_items: {
        Row: {
          created_at: string | null
          error_message: string | null
          external_id: string
          id: string
          internal_id: string | null
          processed_data: Json | null
          raw_data: Json | null
          resource_type: string
          status: string
          sync_log_id: string | null
        }
        Insert: {
          created_at?: string | null
          error_message?: string | null
          external_id: string
          id?: string
          internal_id?: string | null
          processed_data?: Json | null
          raw_data?: Json | null
          resource_type: string
          status: string
          sync_log_id?: string | null
        }
        Update: {
          created_at?: string | null
          error_message?: string | null
          external_id?: string
          id?: string
          internal_id?: string | null
          processed_data?: Json | null
          raw_data?: Json | null
          resource_type?: string
          status?: string
          sync_log_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "integration_sync_items_sync_log_id_fkey"
            columns: ["sync_log_id"]
            isOneToOne: false
            referencedRelation: "integration_sync_logs"
            referencedColumns: ["id"]
          },
        ]
      }
      integration_sync_logs: {
        Row: {
          completed_at: string | null
          created_by: string | null
          error_message: string | null
          id: string
          integration_id: string | null
          items_created: number | null
          items_failed: number | null
          items_processed: number | null
          items_updated: number | null
          metadata: Json | null
          resource_type: string
          started_at: string | null
          status: string
        }
        Insert: {
          completed_at?: string | null
          created_by?: string | null
          error_message?: string | null
          id?: string
          integration_id?: string | null
          items_created?: number | null
          items_failed?: number | null
          items_processed?: number | null
          items_updated?: number | null
          metadata?: Json | null
          resource_type: string
          started_at?: string | null
          status: string
        }
        Update: {
          completed_at?: string | null
          created_by?: string | null
          error_message?: string | null
          id?: string
          integration_id?: string | null
          items_created?: number | null
          items_failed?: number | null
          items_processed?: number | null
          items_updated?: number | null
          metadata?: Json | null
          resource_type?: string
          started_at?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "integration_sync_logs_integration_id_fkey"
            columns: ["integration_id"]
            isOneToOne: false
            referencedRelation: "integrations"
            referencedColumns: ["id"]
          },
        ]
      }
      integrations: {
        Row: {
          account_id: string | null
          config: Json | null
          created_at: string | null
          credentials: Json | null
          description: string | null
          enabled: boolean | null
          error_message: string | null
          id: string
          last_sync_at: string | null
          metadata: Json | null
          name: string | null
          oauth_access_token: string | null
          oauth_expires_at: string | null
          oauth_refresh_token: string | null
          status: Database["public"]["Enums"]["integration_status"] | null
          type: Database["public"]["Enums"]["integration_type"]
          updated_at: string | null
          webhook_secret: string | null
          webhook_url: string | null
        }
        Insert: {
          account_id?: string | null
          config?: Json | null
          created_at?: string | null
          credentials?: Json | null
          description?: string | null
          enabled?: boolean | null
          error_message?: string | null
          id?: string
          last_sync_at?: string | null
          metadata?: Json | null
          name?: string | null
          oauth_access_token?: string | null
          oauth_expires_at?: string | null
          oauth_refresh_token?: string | null
          status?: Database["public"]["Enums"]["integration_status"] | null
          type: Database["public"]["Enums"]["integration_type"]
          updated_at?: string | null
          webhook_secret?: string | null
          webhook_url?: string | null
        }
        Update: {
          account_id?: string | null
          config?: Json | null
          created_at?: string | null
          credentials?: Json | null
          description?: string | null
          enabled?: boolean | null
          error_message?: string | null
          id?: string
          last_sync_at?: string | null
          metadata?: Json | null
          name?: string | null
          oauth_access_token?: string | null
          oauth_expires_at?: string | null
          oauth_refresh_token?: string | null
          status?: Database["public"]["Enums"]["integration_status"] | null
          type?: Database["public"]["Enums"]["integration_type"]
          updated_at?: string | null
          webhook_secret?: string | null
          webhook_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "integrations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "integrations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "integrations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      inventory: {
        Row: {
          attribute_id: string | null
          branch_id: string
          created_at: string | null
          created_by: string | null
          id: string
          product_id: string
          reserved_stock: number
          stock: number
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          attribute_id?: string | null
          branch_id: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          product_id: string
          reserved_stock?: number
          stock?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          attribute_id?: string | null
          branch_id?: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          product_id?: string
          reserved_stock?: number
          stock?: number
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_attribute_id_fkey"
            columns: ["attribute_id"]
            isOneToOne: false
            referencedRelation: "product_attributes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      invitations: {
        Row: {
          account_id: string
          created_at: string
          email: string
          expires_at: string
          id: number
          invite_token: string
          invited_by: string
          role: string
          updated_at: string
        }
        Insert: {
          account_id: string
          created_at?: string
          email: string
          expires_at?: string
          id?: number
          invite_token: string
          invited_by: string
          role: string
          updated_at?: string
        }
        Update: {
          account_id?: string
          created_at?: string
          email?: string
          expires_at?: string
          id?: number
          invite_token?: string
          invited_by?: string
          role?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      journey_participants: {
        Row: {
          completed_at: string | null
          current_step: number | null
          customer_id: string
          id: string
          journey_id: string
          metadata: Json | null
          started_at: string | null
          status: string | null
        }
        Insert: {
          completed_at?: string | null
          current_step?: number | null
          customer_id: string
          id?: string
          journey_id: string
          metadata?: Json | null
          started_at?: string | null
          status?: string | null
        }
        Update: {
          completed_at?: string | null
          current_step?: number | null
          customer_id?: string
          id?: string
          journey_id?: string
          metadata?: Json | null
          started_at?: string | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "journey_participants_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "journey_participants_journey_id_fkey"
            columns: ["journey_id"]
            isOneToOne: false
            referencedRelation: "customer_journeys"
            referencedColumns: ["id"]
          },
        ]
      }
      leaderboard_entries: {
        Row: {
          average_score: number | null
          best_session_id: string | null
          id: string
          last_played_at: string | null
          leaderboard_id: string
          rank: number | null
          score: number
          student_id: string
          total_sessions: number | null
        }
        Insert: {
          average_score?: number | null
          best_session_id?: string | null
          id?: string
          last_played_at?: string | null
          leaderboard_id: string
          rank?: number | null
          score: number
          student_id: string
          total_sessions?: number | null
        }
        Update: {
          average_score?: number | null
          best_session_id?: string | null
          id?: string
          last_played_at?: string | null
          leaderboard_id?: string
          rank?: number | null
          score?: number
          student_id?: string
          total_sessions?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "leaderboard_entries_best_session_id_fkey"
            columns: ["best_session_id"]
            isOneToOne: false
            referencedRelation: "game_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "leaderboard_entries_leaderboard_id_fkey"
            columns: ["leaderboard_id"]
            isOneToOne: false
            referencedRelation: "leaderboards"
            referencedColumns: ["id"]
          },
        ]
      }
      leaderboards: {
        Row: {
          account_id: string
          category_id: string | null
          created_at: string | null
          end_date: string | null
          game_id: string | null
          id: string
          is_active: boolean | null
          leaderboard_type: string
          period: string
          start_date: string | null
          title: string
        }
        Insert: {
          account_id: string
          category_id?: string | null
          created_at?: string | null
          end_date?: string | null
          game_id?: string | null
          id?: string
          is_active?: boolean | null
          leaderboard_type: string
          period: string
          start_date?: string | null
          title: string
        }
        Update: {
          account_id?: string
          category_id?: string | null
          created_at?: string | null
          end_date?: string | null
          game_id?: string | null
          id?: string
          is_active?: boolean | null
          leaderboard_type?: string
          period?: string
          start_date?: string | null
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "leaderboards_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "leaderboards_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "leaderboards_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "leaderboards_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "game_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "leaderboards_game_id_fkey"
            columns: ["game_id"]
            isOneToOne: false
            referencedRelation: "games"
            referencedColumns: ["id"]
          },
        ]
      }
      learner_guardians: {
        Row: {
          created_at: string | null
          guardian_id: string
          id: string
          is_primary: boolean | null
          learner_id: string
          relationship: string | null
        }
        Insert: {
          created_at?: string | null
          guardian_id: string
          id?: string
          is_primary?: boolean | null
          learner_id: string
          relationship?: string | null
        }
        Update: {
          created_at?: string | null
          guardian_id?: string
          id?: string
          is_primary?: boolean | null
          learner_id?: string
          relationship?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "learner_guardians_guardian_id_fkey"
            columns: ["guardian_id"]
            isOneToOne: false
            referencedRelation: "guardians"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "learner_guardians_learner_id_fkey"
            columns: ["learner_id"]
            isOneToOne: false
            referencedRelation: "learners"
            referencedColumns: ["id"]
          },
        ]
      }
      learner_photos: {
        Row: {
          account_id: string
          created_at: string | null
          created_by: string | null
          face_encoding: Json | null
          id: string
          is_primary: boolean | null
          learner_id: string
          photo_type: string | null
          photo_url: string
          quality_score: number | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          created_by?: string | null
          face_encoding?: Json | null
          id?: string
          is_primary?: boolean | null
          learner_id: string
          photo_type?: string | null
          photo_url: string
          quality_score?: number | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          created_by?: string | null
          face_encoding?: Json | null
          id?: string
          is_primary?: boolean | null
          learner_id?: string
          photo_type?: string | null
          photo_url?: string
          quality_score?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "learner_photos_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "learner_photos_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "learner_photos_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "learner_photos_learner_id_fkey"
            columns: ["learner_id"]
            isOneToOne: false
            referencedRelation: "learners"
            referencedColumns: ["id"]
          },
        ]
      }
      learners: {
        Row: {
          account_id: string
          address: string | null
          created_at: string | null
          created_by: string | null
          date_of_birth: string | null
          email: string | null
          emergency_contact: Json | null
          full_name: string
          gender: string | null
          id: string
          learner_code: string | null
          medical_info: Json | null
          nickname: string | null
          notes: string | null
          phone: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          address?: string | null
          created_at?: string | null
          created_by?: string | null
          date_of_birth?: string | null
          email?: string | null
          emergency_contact?: Json | null
          full_name: string
          gender?: string | null
          id?: string
          learner_code?: string | null
          medical_info?: Json | null
          nickname?: string | null
          notes?: string | null
          phone?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          address?: string | null
          created_at?: string | null
          created_by?: string | null
          date_of_birth?: string | null
          email?: string | null
          emergency_contact?: Json | null
          full_name?: string
          gender?: string | null
          id?: string
          learner_code?: string | null
          medical_info?: Json | null
          nickname?: string | null
          notes?: string | null
          phone?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "learners_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "learners_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "learners_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      library_items: {
        Row: {
          account_id: string
          age_group: string | null
          category: string | null
          created_at: string | null
          description: string | null
          duration: number | null
          file_size: number | null
          file_url: string | null
          format: string | null
          id: string
          item_type: string
          language: string | null
          status: string | null
          tags: string[] | null
          target_audience: string | null
          thumbnail_url: string | null
          title: string
          updated_at: string | null
          upload_date: string | null
        }
        Insert: {
          account_id: string
          age_group?: string | null
          category?: string | null
          created_at?: string | null
          description?: string | null
          duration?: number | null
          file_size?: number | null
          file_url?: string | null
          format?: string | null
          id?: string
          item_type: string
          language?: string | null
          status?: string | null
          tags?: string[] | null
          target_audience?: string | null
          thumbnail_url?: string | null
          title: string
          updated_at?: string | null
          upload_date?: string | null
        }
        Update: {
          account_id?: string
          age_group?: string | null
          category?: string | null
          created_at?: string | null
          description?: string | null
          duration?: number | null
          file_size?: number | null
          file_url?: string | null
          format?: string | null
          id?: string
          item_type?: string
          language?: string | null
          status?: string | null
          tags?: string[] | null
          target_audience?: string | null
          thumbnail_url?: string | null
          title?: string
          updated_at?: string | null
          upload_date?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "library_items_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "library_items_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "library_items_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      marketplace_themes: {
        Row: {
          account_id: string | null
          account_theme_id: string | null
          category: string | null
          created_at: string | null
          description: string | null
          downloads_count: number | null
          id: string
          name: string
          price: number | null
          rating: number | null
          status: string | null
          tags: string[] | null
          updated_at: string | null
        }
        Insert: {
          account_id?: string | null
          account_theme_id?: string | null
          category?: string | null
          created_at?: string | null
          description?: string | null
          downloads_count?: number | null
          id?: string
          name: string
          price?: number | null
          rating?: number | null
          status?: string | null
          tags?: string[] | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string | null
          account_theme_id?: string | null
          category?: string | null
          created_at?: string | null
          description?: string | null
          downloads_count?: number | null
          id?: string
          name?: string
          price?: number | null
          rating?: number | null
          status?: string | null
          tags?: string[] | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "marketplace_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "marketplace_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "marketplace_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "marketplace_themes_account_theme_id_fkey"
            columns: ["account_theme_id"]
            isOneToOne: false
            referencedRelation: "account_themes"
            referencedColumns: ["id"]
          },
        ]
      }
      meal_plans: {
        Row: {
          account_id: string
          cost_per_serving: number | null
          created_at: string | null
          created_by: string | null
          date: string
          dietary_accommodations: Json | null
          id: string
          meal_type: string
          menu_items: Json
          special_notes: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          cost_per_serving?: number | null
          created_at?: string | null
          created_by?: string | null
          date: string
          dietary_accommodations?: Json | null
          id?: string
          meal_type: string
          menu_items?: Json
          special_notes?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          cost_per_serving?: number | null
          created_at?: string | null
          created_by?: string | null
          date?: string
          dietary_accommodations?: Json | null
          id?: string
          meal_type?: string
          menu_items?: Json
          special_notes?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "meal_plans_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "meal_plans_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "meal_plans_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      media_albums: {
        Row: {
          account_id: string
          album_type: string | null
          cover_image_id: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          name: string
          privacy_level: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          account_id: string
          album_type?: string | null
          cover_image_id?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          name: string
          privacy_level?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          account_id?: string
          album_type?: string | null
          cover_image_id?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          name?: string
          privacy_level?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_media_albums_cover_image"
            columns: ["cover_image_id"]
            isOneToOne: false
            referencedRelation: "media_files"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "media_albums_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "media_albums_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "media_albums_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      media_files: {
        Row: {
          account_id: string
          album_id: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          file_size: number | null
          file_type: string
          file_url: string
          id: string
          is_featured: boolean | null
          metadata: Json | null
          mime_type: string | null
          original_filename: string | null
          privacy_level: string | null
          tags: string[] | null
          thumbnail_url: string | null
          title: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          account_id: string
          album_id?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          file_size?: number | null
          file_type: string
          file_url: string
          id?: string
          is_featured?: boolean | null
          metadata?: Json | null
          mime_type?: string | null
          original_filename?: string | null
          privacy_level?: string | null
          tags?: string[] | null
          thumbnail_url?: string | null
          title: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          account_id?: string
          album_id?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          file_size?: number | null
          file_type?: string
          file_url?: string
          id?: string
          is_featured?: boolean | null
          metadata?: Json | null
          mime_type?: string | null
          original_filename?: string | null
          privacy_level?: string | null
          tags?: string[] | null
          thumbnail_url?: string | null
          title?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "media_files_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "media_files_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "media_files_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "media_files_album_id_fkey"
            columns: ["album_id"]
            isOneToOne: false
            referencedRelation: "media_albums"
            referencedColumns: ["id"]
          },
        ]
      }
      message_recipients: {
        Row: {
          created_at: string | null
          delivered_at: string | null
          delivery_status: string | null
          id: string
          message_id: string
          read_at: string | null
          read_status: string | null
          recipient_id: string
          recipient_type: string
        }
        Insert: {
          created_at?: string | null
          delivered_at?: string | null
          delivery_status?: string | null
          id?: string
          message_id: string
          read_at?: string | null
          read_status?: string | null
          recipient_id: string
          recipient_type: string
        }
        Update: {
          created_at?: string | null
          delivered_at?: string | null
          delivery_status?: string | null
          id?: string
          message_id?: string
          read_at?: string | null
          read_status?: string | null
          recipient_id?: string
          recipient_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "message_recipients_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          account_id: string
          content: string
          created_at: string | null
          created_by: string | null
          delivery_channels: Json | null
          id: string
          message_type: Database["public"]["Enums"]["message_type"]
          metadata: Json | null
          priority: Database["public"]["Enums"]["message_priority"] | null
          recipient_type: Database["public"]["Enums"]["recipient_type"]
          scheduled_at: string | null
          sender_name: string | null
          sent_at: string | null
          status: Database["public"]["Enums"]["message_status"] | null
          title: string
          updated_at: string | null
        }
        Insert: {
          account_id: string
          content: string
          created_at?: string | null
          created_by?: string | null
          delivery_channels?: Json | null
          id?: string
          message_type: Database["public"]["Enums"]["message_type"]
          metadata?: Json | null
          priority?: Database["public"]["Enums"]["message_priority"] | null
          recipient_type: Database["public"]["Enums"]["recipient_type"]
          scheduled_at?: string | null
          sender_name?: string | null
          sent_at?: string | null
          status?: Database["public"]["Enums"]["message_status"] | null
          title: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          content?: string
          created_at?: string | null
          created_by?: string | null
          delivery_channels?: Json | null
          id?: string
          message_type?: Database["public"]["Enums"]["message_type"]
          metadata?: Json | null
          priority?: Database["public"]["Enums"]["message_priority"] | null
          recipient_type?: Database["public"]["Enums"]["recipient_type"]
          scheduled_at?: string | null
          sender_name?: string | null
          sent_at?: string | null
          status?: Database["public"]["Enums"]["message_status"] | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      newsletter_subscribers: {
        Row: {
          created_at: string
          email: string
          id: string
          status: string
          subscribed_at: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          id?: string
          status?: string
          subscribed_at?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          id?: string
          status?: string
          subscribed_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      nfc_tags: {
        Row: {
          account_id: string
          created_at: string | null
          created_by: string | null
          id: string
          is_active: boolean | null
          last_used_at: string | null
          location_coordinates: Json | null
          location_name: string | null
          metadata: Json | null
          tag_name: string
          tag_type: string | null
          tag_uid: string
          updated_at: string | null
          usage_count: number | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          last_used_at?: string | null
          location_coordinates?: Json | null
          location_name?: string | null
          metadata?: Json | null
          tag_name: string
          tag_type?: string | null
          tag_uid: string
          updated_at?: string | null
          usage_count?: number | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          created_by?: string | null
          id?: string
          is_active?: boolean | null
          last_used_at?: string | null
          location_coordinates?: Json | null
          location_name?: string | null
          metadata?: Json | null
          tag_name?: string
          tag_type?: string | null
          tag_uid?: string
          updated_at?: string | null
          usage_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "nfc_tags_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "nfc_tags_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "nfc_tags_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      nonces: {
        Row: {
          client_token: string
          created_at: string
          expires_at: string
          id: string
          last_verification_at: string | null
          last_verification_ip: unknown | null
          last_verification_user_agent: string | null
          metadata: Json | null
          nonce: string
          purpose: string
          revoked: boolean
          revoked_reason: string | null
          scopes: string[] | null
          used_at: string | null
          user_id: string | null
          verification_attempts: number
        }
        Insert: {
          client_token: string
          created_at?: string
          expires_at: string
          id?: string
          last_verification_at?: string | null
          last_verification_ip?: unknown | null
          last_verification_user_agent?: string | null
          metadata?: Json | null
          nonce: string
          purpose: string
          revoked?: boolean
          revoked_reason?: string | null
          scopes?: string[] | null
          used_at?: string | null
          user_id?: string | null
          verification_attempts?: number
        }
        Update: {
          client_token?: string
          created_at?: string
          expires_at?: string
          id?: string
          last_verification_at?: string | null
          last_verification_ip?: unknown | null
          last_verification_user_agent?: string | null
          metadata?: Json | null
          nonce?: string
          purpose?: string
          revoked?: boolean
          revoked_reason?: string | null
          scopes?: string[] | null
          used_at?: string | null
          user_id?: string | null
          verification_attempts?: number
        }
        Relationships: []
      }
      notification_templates: {
        Row: {
          account_id: string
          content_template: string
          created_at: string | null
          created_by: string | null
          delivery_method: string | null
          id: string
          is_active: boolean | null
          last_used_at: string | null
          styling: Json | null
          subject_template: string | null
          template_name: string
          template_type: string | null
          updated_at: string | null
          usage_count: number | null
          variables: Json | null
        }
        Insert: {
          account_id: string
          content_template: string
          created_at?: string | null
          created_by?: string | null
          delivery_method?: string | null
          id?: string
          is_active?: boolean | null
          last_used_at?: string | null
          styling?: Json | null
          subject_template?: string | null
          template_name: string
          template_type?: string | null
          updated_at?: string | null
          usage_count?: number | null
          variables?: Json | null
        }
        Update: {
          account_id?: string
          content_template?: string
          created_at?: string | null
          created_by?: string | null
          delivery_method?: string | null
          id?: string
          is_active?: boolean | null
          last_used_at?: string | null
          styling?: Json | null
          subject_template?: string | null
          template_name?: string
          template_type?: string | null
          updated_at?: string | null
          usage_count?: number | null
          variables?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "notification_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notification_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notification_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          account_id: string
          body: string
          channel: Database["public"]["Enums"]["notification_channel"]
          created_at: string
          dismissed: boolean
          expires_at: string | null
          id: number
          link: string | null
          type: Database["public"]["Enums"]["notification_type"]
        }
        Insert: {
          account_id: string
          body: string
          channel?: Database["public"]["Enums"]["notification_channel"]
          created_at?: string
          dismissed?: boolean
          expires_at?: string | null
          id?: never
          link?: string | null
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Update: {
          account_id?: string
          body?: string
          channel?: Database["public"]["Enums"]["notification_channel"]
          created_at?: string
          dismissed?: boolean
          expires_at?: string | null
          id?: never
          link?: string | null
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Relationships: [
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      oa_configurations: {
        Row: {
          access_token: string | null
          account_id: string | null
          app_id: string | null
          author_id: string | null
          created_at: string | null
          id: string
          is_system_default: boolean | null
          oa_id: string | null
          oa_metadata: Json | null
          oa_type: Database["public"]["Enums"]["oa_type"]
          refresh_token: string | null
          secret_key: string | null
          theme_id: string | null
          token_expires_at: string | null
          updated_at: string | null
          user_oa_id: string | null
        }
        Insert: {
          access_token?: string | null
          account_id?: string | null
          app_id?: string | null
          author_id?: string | null
          created_at?: string | null
          id?: string
          is_system_default?: boolean | null
          oa_id?: string | null
          oa_metadata?: Json | null
          oa_type: Database["public"]["Enums"]["oa_type"]
          refresh_token?: string | null
          secret_key?: string | null
          theme_id?: string | null
          token_expires_at?: string | null
          updated_at?: string | null
          user_oa_id?: string | null
        }
        Update: {
          access_token?: string | null
          account_id?: string | null
          app_id?: string | null
          author_id?: string | null
          created_at?: string | null
          id?: string
          is_system_default?: boolean | null
          oa_id?: string | null
          oa_metadata?: Json | null
          oa_type?: Database["public"]["Enums"]["oa_type"]
          refresh_token?: string | null
          secret_key?: string | null
          theme_id?: string | null
          token_expires_at?: string | null
          updated_at?: string | null
          user_oa_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "oa_configurations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "oa_configurations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "oa_configurations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "oa_configurations_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "themes"
            referencedColumns: ["id"]
          },
        ]
      }
      order_items: {
        Row: {
          created_at: string
          id: string
          order_id: string
          price_amount: number | null
          product_id: string
          quantity: number
          updated_at: string
          variant_id: string
        }
        Insert: {
          created_at?: string
          id: string
          order_id: string
          price_amount?: number | null
          product_id: string
          quantity?: number
          updated_at?: string
          variant_id: string
        }
        Update: {
          created_at?: string
          id?: string
          order_id?: string
          price_amount?: number | null
          product_id?: string
          quantity?: number
          updated_at?: string
          variant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
        ]
      }
      orders: {
        Row: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at: string
        }
        Insert: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at?: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at?: string
        }
        Update: {
          account_id?: string
          billing_customer_id?: number
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          created_at?: string
          currency?: string
          id?: string
          status?: Database["public"]["Enums"]["payment_status"]
          total_amount?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_billing_customer_id_fkey"
            columns: ["billing_customer_id"]
            isOneToOne: false
            referencedRelation: "billing_customers"
            referencedColumns: ["id"]
          },
        ]
      }
      photo_attendance_sessions: {
        Row: {
          account_id: string
          batch_photo_url: string | null
          confidence_threshold: number | null
          created_at: string | null
          created_by: string | null
          id: string
          instructor_id: string | null
          processing_metadata: Json | null
          processing_status: string | null
          program_id: string
          session_date: string
          session_time: string | null
          total_faces_detected: number | null
          total_students_recognized: number | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          batch_photo_url?: string | null
          confidence_threshold?: number | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          instructor_id?: string | null
          processing_metadata?: Json | null
          processing_status?: string | null
          program_id: string
          session_date: string
          session_time?: string | null
          total_faces_detected?: number | null
          total_students_recognized?: number | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          batch_photo_url?: string | null
          confidence_threshold?: number | null
          created_at?: string | null
          created_by?: string | null
          id?: string
          instructor_id?: string | null
          processing_metadata?: Json | null
          processing_status?: string | null
          program_id?: string
          session_date?: string
          session_time?: string | null
          total_faces_detected?: number | null
          total_students_recognized?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "photo_attendance_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "photo_attendance_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "photo_attendance_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "photo_attendance_sessions_instructor_id_fkey"
            columns: ["instructor_id"]
            isOneToOne: false
            referencedRelation: "instructors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "photo_attendance_sessions_program_id_fkey"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      photo_recognition_results: {
        Row: {
          confidence_score: number | null
          created_at: string | null
          detected_face_box: Json | null
          id: string
          learner_id: string | null
          manual_verification: boolean | null
          photo_session_id: string
          recognition_status: string | null
          updated_at: string | null
          verification_notes: string | null
          verified_by: string | null
        }
        Insert: {
          confidence_score?: number | null
          created_at?: string | null
          detected_face_box?: Json | null
          id?: string
          learner_id?: string | null
          manual_verification?: boolean | null
          photo_session_id: string
          recognition_status?: string | null
          updated_at?: string | null
          verification_notes?: string | null
          verified_by?: string | null
        }
        Update: {
          confidence_score?: number | null
          created_at?: string | null
          detected_face_box?: Json | null
          id?: string
          learner_id?: string | null
          manual_verification?: boolean | null
          photo_session_id?: string
          recognition_status?: string | null
          updated_at?: string | null
          verification_notes?: string | null
          verified_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "photo_recognition_results_learner_id_fkey"
            columns: ["learner_id"]
            isOneToOne: false
            referencedRelation: "learners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "photo_recognition_results_photo_session_id_fkey"
            columns: ["photo_session_id"]
            isOneToOne: false
            referencedRelation: "photo_attendance_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      predictive_insights: {
        Row: {
          account_id: string
          acknowledged_at: string | null
          acknowledged_by: string | null
          confidence_score: number | null
          created_at: string | null
          id: string
          insight_type: string | null
          is_acknowledged: boolean | null
          metadata: Json | null
          prediction_data: Json
          recommendations: Json | null
          risk_level: string | null
          target_id: string
          target_type: string | null
          updated_at: string | null
          valid_until: string | null
        }
        Insert: {
          account_id: string
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          confidence_score?: number | null
          created_at?: string | null
          id?: string
          insight_type?: string | null
          is_acknowledged?: boolean | null
          metadata?: Json | null
          prediction_data: Json
          recommendations?: Json | null
          risk_level?: string | null
          target_id: string
          target_type?: string | null
          updated_at?: string | null
          valid_until?: string | null
        }
        Update: {
          account_id?: string
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          confidence_score?: number | null
          created_at?: string | null
          id?: string
          insight_type?: string | null
          is_acknowledged?: boolean | null
          metadata?: Json | null
          prediction_data?: Json
          recommendations?: Json | null
          risk_level?: string | null
          target_id?: string
          target_type?: string | null
          updated_at?: string | null
          valid_until?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "predictive_insights_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "predictive_insights_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "predictive_insights_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      product_attributes: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: string
          name: string
          price_modifier: number | null
          product_id: string
          updated_at: string | null
          updated_by: string | null
          value: string
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          name: string
          price_modifier?: number | null
          product_id: string
          updated_at?: string | null
          updated_by?: string | null
          value: string
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          name?: string
          price_modifier?: number | null
          product_id?: string
          updated_at?: string | null
          updated_by?: string | null
          value?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_attributes_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          account_id: string
          barcode: string | null
          category_id: string | null
          compare_at_price: number
          created_at: string | null
          created_by: string | null
          description: string | null
          dimensions: Json | null
          external_id: string | null
          id: string
          image_url: string | null
          image_urls: string[] | null
          metadata: Json | null
          name: string
          price: number
          sku: string | null
          status: string | null
          tax_rate: number | null
          type: Database["public"]["Enums"]["product_type"]
          updated_at: string | null
          updated_by: string | null
          weight: number | null
        }
        Insert: {
          account_id: string
          barcode?: string | null
          category_id?: string | null
          compare_at_price: number
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          dimensions?: Json | null
          external_id?: string | null
          id?: string
          image_url?: string | null
          image_urls?: string[] | null
          metadata?: Json | null
          name: string
          price: number
          sku?: string | null
          status?: string | null
          tax_rate?: number | null
          type?: Database["public"]["Enums"]["product_type"]
          updated_at?: string | null
          updated_by?: string | null
          weight?: number | null
        }
        Update: {
          account_id?: string
          barcode?: string | null
          category_id?: string | null
          compare_at_price?: number
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          dimensions?: Json | null
          external_id?: string | null
          id?: string
          image_url?: string | null
          image_urls?: string[] | null
          metadata?: Json | null
          name?: string
          price?: number
          sku?: string | null
          status?: string | null
          tax_rate?: number | null
          type?: Database["public"]["Enums"]["product_type"]
          updated_at?: string | null
          updated_by?: string | null
          weight?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      programs: {
        Row: {
          account_id: string
          age_group: string | null
          capacity: number | null
          created_at: string | null
          created_by: string | null
          description: string | null
          duration_weeks: number | null
          fees: Json | null
          id: string
          name: string
          program_type: string | null
          schedule: Json | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          age_group?: string | null
          capacity?: number | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          duration_weeks?: number | null
          fees?: Json | null
          id?: string
          name: string
          program_type?: string | null
          schedule?: Json | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          age_group?: string | null
          capacity?: number | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          duration_weeks?: number | null
          fees?: Json | null
          id?: string
          name?: string
          program_type?: string | null
          schedule?: Json | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "programs_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "programs_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "programs_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      qr_checkin_sessions: {
        Row: {
          account_id: string
          allowed_locations: Json | null
          checkin_window_minutes: number | null
          created_at: string | null
          created_by: string | null
          current_checkins: number | null
          expires_at: string
          id: string
          instructor_id: string | null
          location_required: boolean | null
          max_checkins: number | null
          metadata: Json | null
          program_id: string
          qr_code: string
          qr_data: Json
          session_date: string
          session_time: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          allowed_locations?: Json | null
          checkin_window_minutes?: number | null
          created_at?: string | null
          created_by?: string | null
          current_checkins?: number | null
          expires_at: string
          id?: string
          instructor_id?: string | null
          location_required?: boolean | null
          max_checkins?: number | null
          metadata?: Json | null
          program_id: string
          qr_code: string
          qr_data: Json
          session_date: string
          session_time?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          allowed_locations?: Json | null
          checkin_window_minutes?: number | null
          created_at?: string | null
          created_by?: string | null
          current_checkins?: number | null
          expires_at?: string
          id?: string
          instructor_id?: string | null
          location_required?: boolean | null
          max_checkins?: number | null
          metadata?: Json | null
          program_id?: string
          qr_code?: string
          qr_data?: Json
          session_date?: string
          session_time?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "qr_checkin_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "qr_checkin_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "qr_checkin_sessions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "qr_checkin_sessions_instructor_id_fkey"
            columns: ["instructor_id"]
            isOneToOne: false
            referencedRelation: "instructors"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "qr_checkin_sessions_program_id_fkey"
            columns: ["program_id"]
            isOneToOne: false
            referencedRelation: "programs"
            referencedColumns: ["id"]
          },
        ]
      }
      role_permissions: {
        Row: {
          id: number
          permission: Database["public"]["Enums"]["app_permissions"]
          role: string
        }
        Insert: {
          id?: number
          permission: Database["public"]["Enums"]["app_permissions"]
          role: string
        }
        Update: {
          id?: number
          permission?: Database["public"]["Enums"]["app_permissions"]
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "role_permissions_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      roles: {
        Row: {
          hierarchy_level: number
          name: string
        }
        Insert: {
          hierarchy_level: number
          name: string
        }
        Update: {
          hierarchy_level?: number
          name?: string
        }
        Relationships: []
      }
      segment_members: {
        Row: {
          added_at: string | null
          customer_id: string
          id: string
          segment_id: string
        }
        Insert: {
          added_at?: string | null
          customer_id: string
          id?: string
          segment_id: string
        }
        Update: {
          added_at?: string | null
          customer_id?: string
          id?: string
          segment_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "segment_members_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customer_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "segment_members_segment_id_fkey"
            columns: ["segment_id"]
            isOneToOne: false
            referencedRelation: "customer_segments"
            referencedColumns: ["id"]
          },
        ]
      }
      self_checkin_records: {
        Row: {
          checkin_location: Json | null
          checkin_method: string | null
          checkin_time: string | null
          created_at: string | null
          device_info: Json | null
          guardian_info: Json | null
          id: string
          learner_id: string
          metadata: Json | null
          qr_session_id: string
          verification_notes: string | null
          verification_status: string | null
          verified_at: string | null
          verified_by: string | null
        }
        Insert: {
          checkin_location?: Json | null
          checkin_method?: string | null
          checkin_time?: string | null
          created_at?: string | null
          device_info?: Json | null
          guardian_info?: Json | null
          id?: string
          learner_id: string
          metadata?: Json | null
          qr_session_id: string
          verification_notes?: string | null
          verification_status?: string | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Update: {
          checkin_location?: Json | null
          checkin_method?: string | null
          checkin_time?: string | null
          created_at?: string | null
          device_info?: Json | null
          guardian_info?: Json | null
          id?: string
          learner_id?: string
          metadata?: Json | null
          qr_session_id?: string
          verification_notes?: string | null
          verification_status?: string | null
          verified_at?: string | null
          verified_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "self_checkin_records_learner_id_fkey"
            columns: ["learner_id"]
            isOneToOne: false
            referencedRelation: "learners"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "self_checkin_records_qr_session_id_fkey"
            columns: ["qr_session_id"]
            isOneToOne: false
            referencedRelation: "qr_checkin_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      short_links: {
        Row: {
          account_id: string
          click_count: number | null
          created_at: string | null
          description: string | null
          expires_at: string | null
          id: string
          last_clicked_at: string | null
          metadata: Json | null
          reference_id: string
          reference_table: string
          short_id: string
          title: string | null
          type: Database["public"]["Enums"]["short_link_type"]
          updated_at: string | null
          url: string
        }
        Insert: {
          account_id: string
          click_count?: number | null
          created_at?: string | null
          description?: string | null
          expires_at?: string | null
          id?: string
          last_clicked_at?: string | null
          metadata?: Json | null
          reference_id: string
          reference_table: string
          short_id: string
          title?: string | null
          type?: Database["public"]["Enums"]["short_link_type"]
          updated_at?: string | null
          url: string
        }
        Update: {
          account_id?: string
          click_count?: number | null
          created_at?: string | null
          description?: string | null
          expires_at?: string | null
          id?: string
          last_clicked_at?: string | null
          metadata?: Json | null
          reference_id?: string
          reference_table?: string
          short_id?: string
          title?: string | null
          type?: Database["public"]["Enums"]["short_link_type"]
          updated_at?: string | null
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "short_links_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "short_links_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "short_links_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      student_achievements: {
        Row: {
          account_id: string
          achievement_id: string
          earned_at: string | null
          id: string
          session_id: string | null
          student_id: string
        }
        Insert: {
          account_id: string
          achievement_id: string
          earned_at?: string | null
          id?: string
          session_id?: string | null
          student_id: string
        }
        Update: {
          account_id?: string
          achievement_id?: string
          earned_at?: string | null
          id?: string
          session_id?: string | null
          student_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "student_achievements_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_achievements_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_achievements_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_achievements_achievement_id_fkey"
            columns: ["achievement_id"]
            isOneToOne: false
            referencedRelation: "achievements"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_achievements_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "game_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      student_game_progress: {
        Row: {
          account_id: string
          average_score: number | null
          best_score: number | null
          completion_rate: number | null
          current_streak: number | null
          game_id: string
          id: string
          last_played_at: string | null
          longest_streak: number | null
          mastery_level: number | null
          student_id: string
          total_sessions: number | null
          total_time_seconds: number | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          average_score?: number | null
          best_score?: number | null
          completion_rate?: number | null
          current_streak?: number | null
          game_id: string
          id?: string
          last_played_at?: string | null
          longest_streak?: number | null
          mastery_level?: number | null
          student_id: string
          total_sessions?: number | null
          total_time_seconds?: number | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          average_score?: number | null
          best_score?: number | null
          completion_rate?: number | null
          current_streak?: number | null
          game_id?: string
          id?: string
          last_played_at?: string | null
          longest_streak?: number | null
          mastery_level?: number | null
          student_id?: string
          total_sessions?: number | null
          total_time_seconds?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "student_game_progress_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_game_progress_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_game_progress_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_game_progress_game_id_fkey"
            columns: ["game_id"]
            isOneToOne: false
            referencedRelation: "games"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_items: {
        Row: {
          created_at: string
          id: string
          interval: string
          interval_count: number
          metadata: Json | null
          price_amount: number | null
          product_id: string
          quantity: number
          subscription_id: string
          type: Database["public"]["Enums"]["subscription_item_type"]
          updated_at: string
          variant_id: string
        }
        Insert: {
          created_at?: string
          id: string
          interval: string
          interval_count: number
          metadata?: Json | null
          price_amount?: number | null
          product_id: string
          quantity?: number
          subscription_id: string
          type: Database["public"]["Enums"]["subscription_item_type"]
          updated_at?: string
          variant_id: string
        }
        Update: {
          created_at?: string
          id?: string
          interval?: string
          interval_count?: number
          metadata?: Json | null
          price_amount?: number | null
          product_id?: string
          quantity?: number
          subscription_id?: string
          type?: Database["public"]["Enums"]["subscription_item_type"]
          updated_at?: string
          variant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscription_items_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      subscriptions: {
        Row: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at: string
          currency: string
          id: string
          is_free: boolean | null
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          trial_starts_at: string | null
          updated_at: string
        }
        Insert: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at?: string
          currency: string
          id: string
          is_free?: boolean | null
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          trial_starts_at?: string | null
          updated_at?: string
        }
        Update: {
          account_id?: string
          active?: boolean
          billing_customer_id?: number
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end?: boolean
          created_at?: string
          currency?: string
          id?: string
          is_free?: boolean | null
          period_ends_at?: string
          period_starts_at?: string
          status?: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          trial_starts_at?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_billing_customer_id_fkey"
            columns: ["billing_customer_id"]
            isOneToOne: false
            referencedRelation: "billing_customers"
            referencedColumns: ["id"]
          },
        ]
      }
      temp_images: {
        Row: {
          account_id: string
          created_at: string | null
          expires_at: string
          id: string
          is_moved: boolean | null
          temp_path: string
          url: string
        }
        Insert: {
          account_id: string
          created_at?: string | null
          expires_at: string
          id?: string
          is_moved?: boolean | null
          temp_path: string
          url: string
        }
        Update: {
          account_id?: string
          created_at?: string | null
          expires_at?: string
          id?: string
          is_moved?: boolean | null
          temp_path?: string
          url?: string
        }
        Relationships: []
      }
      temp_media_files: {
        Row: {
          account_id: string
          created_at: string | null
          expires_at: string
          file_size: number | null
          file_type: string
          id: string
          is_moved: boolean | null
          mime_type: string | null
          original_filename: string | null
          temp_path: string
          thumbnail_url: string | null
          url: string
        }
        Insert: {
          account_id: string
          created_at?: string | null
          expires_at: string
          file_size?: number | null
          file_type: string
          id?: string
          is_moved?: boolean | null
          mime_type?: string | null
          original_filename?: string | null
          temp_path: string
          thumbnail_url?: string | null
          url: string
        }
        Update: {
          account_id?: string
          created_at?: string | null
          expires_at?: string
          file_size?: number | null
          file_type?: string
          id?: string
          is_moved?: boolean | null
          mime_type?: string | null
          original_filename?: string | null
          temp_path?: string
          thumbnail_url?: string | null
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "temp_media_files_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temp_media_files_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temp_media_files_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      temp_themes: {
        Row: {
          account_id: string
          account_theme_id: string | null
          config: Json
          created_at: string | null
          expires_at: string
          id: string
          preview_token: string
          theme_id: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          account_theme_id?: string | null
          config?: Json
          created_at?: string | null
          expires_at: string
          id?: string
          preview_token: string
          theme_id?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          account_theme_id?: string | null
          config?: Json
          created_at?: string | null
          expires_at?: string
          id?: string
          preview_token?: string
          theme_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "temp_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temp_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temp_themes_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temp_themes_account_theme_id_fkey"
            columns: ["account_theme_id"]
            isOneToOne: false
            referencedRelation: "account_themes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "temp_themes_theme_id_fkey"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "themes"
            referencedColumns: ["id"]
          },
        ]
      }
      theme_purchases: {
        Row: {
          buyer_account_id: string
          created_at: string | null
          id: string
          marketplace_theme_id: string
          price_paid: number
          status: string | null
          updated_at: string | null
        }
        Insert: {
          buyer_account_id: string
          created_at?: string | null
          id?: string
          marketplace_theme_id: string
          price_paid: number
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          buyer_account_id?: string
          created_at?: string | null
          id?: string
          marketplace_theme_id?: string
          price_paid?: number
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "theme_purchases_buyer_account_id_fkey"
            columns: ["buyer_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_purchases_buyer_account_id_fkey"
            columns: ["buyer_account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_purchases_buyer_account_id_fkey"
            columns: ["buyer_account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_purchases_marketplace_theme_id_fkey"
            columns: ["marketplace_theme_id"]
            isOneToOne: false
            referencedRelation: "marketplace_themes"
            referencedColumns: ["id"]
          },
        ]
      }
      theme_reviews: {
        Row: {
          comment: string | null
          created_at: string | null
          id: string
          marketplace_theme_id: string
          rating: number
          reviewer_account_id: string
          updated_at: string | null
        }
        Insert: {
          comment?: string | null
          created_at?: string | null
          id?: string
          marketplace_theme_id: string
          rating: number
          reviewer_account_id: string
          updated_at?: string | null
        }
        Update: {
          comment?: string | null
          created_at?: string | null
          id?: string
          marketplace_theme_id?: string
          rating?: number
          reviewer_account_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "theme_reviews_marketplace_theme_id_fkey"
            columns: ["marketplace_theme_id"]
            isOneToOne: false
            referencedRelation: "marketplace_themes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_reviews_reviewer_account_id_fkey"
            columns: ["reviewer_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_reviews_reviewer_account_id_fkey"
            columns: ["reviewer_account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "theme_reviews_reviewer_account_id_fkey"
            columns: ["reviewer_account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      themes: {
        Row: {
          author_id: string | null
          category: string | null
          config: Json
          created_at: string | null
          description: string | null
          id: string
          mini_app_id: string | null
          name: string
          oa_config_id: string | null
          preview_url: string | null
          thumbnail_url: string | null
          type: Database["public"]["Enums"]["theme_type"]
          updated_at: string | null
          version: string | null
        }
        Insert: {
          author_id?: string | null
          category?: string | null
          config?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          mini_app_id?: string | null
          name: string
          oa_config_id?: string | null
          preview_url?: string | null
          thumbnail_url?: string | null
          type: Database["public"]["Enums"]["theme_type"]
          updated_at?: string | null
          version?: string | null
        }
        Update: {
          author_id?: string | null
          category?: string | null
          config?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          mini_app_id?: string | null
          name?: string
          oa_config_id?: string | null
          preview_url?: string | null
          thumbnail_url?: string | null
          type?: Database["public"]["Enums"]["theme_type"]
          updated_at?: string | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "themes_oa_config_id_fkey"
            columns: ["oa_config_id"]
            isOneToOne: false
            referencedRelation: "oa_configurations"
            referencedColumns: ["id"]
          },
        ]
      }
      vehicles: {
        Row: {
          account_id: string
          brand: string | null
          capacity: number
          created_at: string | null
          driver_license: string | null
          driver_name: string | null
          driver_phone: string | null
          id: string
          insurance_info: Json | null
          license_plate: string | null
          maintenance_schedule: Json | null
          model: string | null
          status: string | null
          updated_at: string | null
          vehicle_number: string
          vehicle_type: string
          year: number | null
        }
        Insert: {
          account_id: string
          brand?: string | null
          capacity: number
          created_at?: string | null
          driver_license?: string | null
          driver_name?: string | null
          driver_phone?: string | null
          id?: string
          insurance_info?: Json | null
          license_plate?: string | null
          maintenance_schedule?: Json | null
          model?: string | null
          status?: string | null
          updated_at?: string | null
          vehicle_number: string
          vehicle_type: string
          year?: number | null
        }
        Update: {
          account_id?: string
          brand?: string | null
          capacity?: number
          created_at?: string | null
          driver_license?: string | null
          driver_name?: string | null
          driver_phone?: string | null
          id?: string
          insurance_info?: Json | null
          license_plate?: string | null
          maintenance_schedule?: Json | null
          model?: string | null
          status?: string | null
          updated_at?: string | null
          vehicle_number?: string
          vehicle_type?: string
          year?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "vehicles_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vehicles_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vehicles_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      voucher_customer_phones: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: string
          phone_number: string
          voucher_id: string
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          phone_number: string
          voucher_id: string
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          phone_number?: string
          voucher_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "voucher_customer_phones_voucher_id_fkey"
            columns: ["voucher_id"]
            isOneToOne: false
            referencedRelation: "vouchers"
            referencedColumns: ["id"]
          },
        ]
      }
      voucher_redemptions: {
        Row: {
          customer_id: string | null
          discount_amount: number
          id: string
          order_id: string
          redeemed_at: string | null
          voucher_id: string
        }
        Insert: {
          customer_id?: string | null
          discount_amount: number
          id?: string
          order_id: string
          redeemed_at?: string | null
          voucher_id: string
        }
        Update: {
          customer_id?: string | null
          discount_amount?: number
          id?: string
          order_id?: string
          redeemed_at?: string | null
          voucher_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "voucher_redemptions_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "customer_orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voucher_redemptions_voucher_id_fkey"
            columns: ["voucher_id"]
            isOneToOne: false
            referencedRelation: "vouchers"
            referencedColumns: ["id"]
          },
        ]
      }
      vouchers: {
        Row: {
          account_id: string
          code: string
          created_at: string | null
          created_by: string | null
          description: string | null
          discount_type: string
          discount_value: number
          end_date: string
          excluded_category_ids: string[] | null
          excluded_product_ids: string[] | null
          first_time_customers_only: boolean | null
          id: string
          included_category_ids: string[] | null
          included_product_ids: string[] | null
          is_customer_specific: boolean | null
          max_discount_value: number | null
          max_uses: number | null
          min_order_value: number | null
          min_previous_orders: number | null
          name: string
          start_date: string
          status: string | null
          updated_at: string | null
          updated_by: string | null
          usage_limit_per_customer: number | null
          uses_count: number | null
        }
        Insert: {
          account_id: string
          code: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          discount_type: string
          discount_value: number
          end_date: string
          excluded_category_ids?: string[] | null
          excluded_product_ids?: string[] | null
          first_time_customers_only?: boolean | null
          id?: string
          included_category_ids?: string[] | null
          included_product_ids?: string[] | null
          is_customer_specific?: boolean | null
          max_discount_value?: number | null
          max_uses?: number | null
          min_order_value?: number | null
          min_previous_orders?: number | null
          name: string
          start_date: string
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
          usage_limit_per_customer?: number | null
          uses_count?: number | null
        }
        Update: {
          account_id?: string
          code?: string
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          discount_type?: string
          discount_value?: number
          end_date?: string
          excluded_category_ids?: string[] | null
          excluded_product_ids?: string[] | null
          first_time_customers_only?: boolean | null
          id?: string
          included_category_ids?: string[] | null
          included_product_ids?: string[] | null
          is_customer_specific?: boolean | null
          max_discount_value?: number | null
          max_uses?: number | null
          min_order_value?: number | null
          min_previous_orders?: number | null
          name?: string
          start_date?: string
          status?: string | null
          updated_at?: string | null
          updated_by?: string | null
          usage_limit_per_customer?: number | null
          uses_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "vouchers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vouchers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "vouchers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      zns_mappings: {
        Row: {
          account_id: string | null
          conditions: Json | null
          created_at: string | null
          created_by: string | null
          description: string | null
          enabled: boolean | null
          event_type: string
          id: string
          module: string
          name: string
          parameter_mapping: Json
          recipient_path: string
          template_id: string | null
          updated_at: string | null
        }
        Insert: {
          account_id?: string | null
          conditions?: Json | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          enabled?: boolean | null
          event_type: string
          id?: string
          module: string
          name: string
          parameter_mapping: Json
          recipient_path?: string
          template_id?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string | null
          conditions?: Json | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          enabled?: boolean | null
          event_type?: string
          id?: string
          module?: string
          name?: string
          parameter_mapping?: Json
          recipient_path?: string
          template_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "zns_mappings_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_mappings_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_mappings_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_mappings_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "zns_templates"
            referencedColumns: ["id"]
          },
        ]
      }
      zns_templates: {
        Row: {
          account_id: string | null
          content: string | null
          created_at: string | null
          enabled: boolean | null
          event_type: string | null
          id: string
          metadata: Json | null
          oa_config_id: string | null
          preview_url: string | null
          status: string | null
          tag: string | null
          template_id: string
          template_name: string | null
          updated_at: string | null
        }
        Insert: {
          account_id?: string | null
          content?: string | null
          created_at?: string | null
          enabled?: boolean | null
          event_type?: string | null
          id?: string
          metadata?: Json | null
          oa_config_id?: string | null
          preview_url?: string | null
          status?: string | null
          tag?: string | null
          template_id: string
          template_name?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string | null
          content?: string | null
          created_at?: string | null
          enabled?: boolean | null
          event_type?: string | null
          id?: string
          metadata?: Json | null
          oa_config_id?: string | null
          preview_url?: string | null
          status?: string | null
          tag?: string | null
          template_id?: string
          template_name?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "zns_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_templates_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_templates_oa_config_id_fkey"
            columns: ["oa_config_id"]
            isOneToOne: false
            referencedRelation: "oa_configurations"
            referencedColumns: ["id"]
          },
        ]
      }
      zns_usage: {
        Row: {
          account_id: string | null
          created_at: string | null
          error_message: string | null
          event_type: string
          id: string
          mapping_id: string | null
          message_id: string | null
          metadata: Json | null
          oa_config_id: string | null
          oa_type: Database["public"]["Enums"]["oa_type"]
          recipient: string | null
          sent_at: string | null
          status: Database["public"]["Enums"]["zns_status"]
          template_id: string | null
        }
        Insert: {
          account_id?: string | null
          created_at?: string | null
          error_message?: string | null
          event_type: string
          id?: string
          mapping_id?: string | null
          message_id?: string | null
          metadata?: Json | null
          oa_config_id?: string | null
          oa_type: Database["public"]["Enums"]["oa_type"]
          recipient?: string | null
          sent_at?: string | null
          status: Database["public"]["Enums"]["zns_status"]
          template_id?: string | null
        }
        Update: {
          account_id?: string | null
          created_at?: string | null
          error_message?: string | null
          event_type?: string
          id?: string
          mapping_id?: string | null
          message_id?: string | null
          metadata?: Json | null
          oa_config_id?: string | null
          oa_type?: Database["public"]["Enums"]["oa_type"]
          recipient?: string | null
          sent_at?: string | null
          status?: Database["public"]["Enums"]["zns_status"]
          template_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_usage_oa_config_id_fkey"
            columns: ["oa_config_id"]
            isOneToOne: false
            referencedRelation: "oa_configurations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_usage_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "zns_templates"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      account_product_counts: {
        Row: {
          account_id: string | null
          product_count: number | null
        }
        Relationships: [
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      account_zns_counts: {
        Row: {
          account_id: string | null
          zns_count: number | null
        }
        Relationships: [
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "zns_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      daily_orders: {
        Row: {
          account_id: string | null
          average_order_value: number | null
          customers_count: number | null
          date: string | null
          orders_count: number | null
          revenue: number | null
          source: string | null
          theme_id: string | null
        }
        Relationships: []
      }
      daily_pageviews: {
        Row: {
          account_id: string | null
          date: string | null
          desktop_pageviews: number | null
          desktop_visitors: number | null
          mobile_pageviews: number | null
          mobile_visitors: number | null
          pageviews_count: number | null
          source: string | null
          tablet_pageviews: number | null
          tablet_visitors: number | null
          theme_id: string | null
          visitors_count: number | null
        }
        Relationships: []
      }
      daily_product_events: {
        Row: {
          account_id: string | null
          add_to_carts: number | null
          date: string | null
          product_id: string | null
          purchases: number | null
          source: string | null
          theme_id: string | null
          views: number | null
        }
        Relationships: []
      }
      user_account_workspace: {
        Row: {
          id: string | null
          name: string | null
          picture_url: string | null
          subscription_status:
            | Database["public"]["Enums"]["subscription_status"]
            | null
        }
        Relationships: []
      }
      user_accounts: {
        Row: {
          id: string | null
          name: string | null
          picture_url: string | null
          role: string | null
          slug: string | null
        }
        Relationships: [
          {
            foreignKeyName: "accounts_memberships_account_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
    }
    Functions: {
      accept_invitation: {
        Args: { token: string; user_id: string }
        Returns: string
      }
      add_customer_phones_to_voucher: {
        Args: { p_voucher_id: string; p_phone_numbers: string[] }
        Returns: Json
      }
      add_invitations_to_account: {
        Args: {
          account_slug: string
          invitations: Database["public"]["CompositeTypes"]["invitation"][]
        }
        Returns: Database["public"]["Tables"]["invitations"]["Row"][]
      }
      apply_voucher_to_order: {
        Args: {
          p_order_id: string
          p_voucher_code: string
          p_customer_phone?: string
        }
        Returns: Json
      }
      bulk_update_attendance: {
        Args: { p_session_id: string; p_attendance_data: Json }
        Returns: Json
      }
      calculate_attendance_analytics: {
        Args: {
          p_account_id: string
          p_program_id?: string
          p_learner_id?: string
          p_start_date?: string
          p_end_date?: string
          p_analysis_type?: string
        }
        Returns: Json
      }
      calculate_value_tier: {
        Args: { spent: number }
        Returns: string
      }
      can_action_account_member: {
        Args: { target_team_account_id: string; target_user_id: string }
        Returns: boolean
      }
      can_connect_oa: {
        Args: { oa_config_id: string }
        Returns: boolean
      }
      can_create_resource: {
        Args: { p_account_id: string; p_resource_type: string }
        Returns: boolean
      }
      cleanup_expired_temp_themes: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_attendance_session: {
        Args: {
          p_account_id: string
          p_program_id: string
          p_session_date: string
          p_session_time?: string
          p_session_type?: string
          p_instructor_id?: string
        }
        Returns: Json
      }
      create_customer_profile_v2: {
        Args: {
          p_team_account_id: string
          p_email: string
          p_first_name: string
          p_last_name: string
          p_phone?: string
          p_avatar_url?: string
          p_metadata?: Json
        }
        Returns: string
      }
      create_default_integrations: {
        Args: { p_account_id: string }
        Returns: undefined
      }
      create_education_sample_data: {
        Args: { p_account_id: string }
        Returns: Json
      }
      create_education_sample_data_for_account: {
        Args: { p_account_id: string; p_education_template: string }
        Returns: Json
      }
      create_flash_sale: {
        Args: {
          p_account_id: string
          p_name: string
          p_description: string
          p_start_time: string
          p_end_time: string
          p_products: Json
          p_status?: string
        }
        Returns: Json
      }
      create_import_export_templates_table: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_invitation: {
        Args: { account_id: string; email: string; role: string }
        Returns: {
          account_id: string
          created_at: string
          email: string
          expires_at: string
          id: number
          invite_token: string
          invited_by: string
          role: string
          updated_at: string
        }
      }
      create_nonce: {
        Args: {
          p_user_id?: string
          p_purpose?: string
          p_expires_in_seconds?: number
          p_metadata?: Json
          p_scopes?: string[]
          p_revoke_previous?: boolean
        }
        Returns: Json
      }
      create_order: {
        Args: {
          p_account_id: string
          p_customer_id: string
          p_branch_id: string
          p_product_id: string
          p_quantity: number
          p_total_amount: number
          p_payment_method: string
          p_status: string
        }
        Returns: Json
      }
      create_order_with_inventory: {
        Args: {
          p_account_id: string
          p_customer_id: string
          p_branch_id: string
          p_items: Json
          p_subtotal: number
          p_discount: number
          p_total_amount: number
          p_payment_method: string
          p_status?: string
          p_voucher_code?: string
          p_voucher_id?: string
          p_voucher_discount?: number
          p_metadata?: Json
          p_webhook_url?: string
        }
        Returns: Json
      }
      create_photo_attendance_session: {
        Args: {
          p_account_id: string
          p_program_id: string
          p_session_date: string
          p_session_time?: string
          p_instructor_id?: string
          p_batch_photo_url?: string
        }
        Returns: Json
      }
      create_qr_checkin_session: {
        Args: {
          p_account_id: string
          p_program_id: string
          p_session_date: string
          p_session_time?: string
          p_instructor_id?: string
          p_expires_in_minutes?: number
          p_max_checkins?: number
          p_checkin_window_minutes?: number
        }
        Returns: Json
      }
      create_sample_data_for_account: {
        Args: { p_account_id: string; p_industry_template: string }
        Returns: Json
      }
      create_team_account: {
        Args: { account_name: string }
        Returns: {
          created_at: string | null
          created_by: string | null
          education_settings: Json | null
          email: string | null
          id: string
          is_personal_account: boolean
          name: string
          phone: string | null
          picture_url: string | null
          primary_owner_user_id: string
          public_data: Json
          slug: string | null
          updated_at: string | null
          updated_by: string | null
        }
      }
      create_voucher: {
        Args: {
          p_account_id: string
          p_code: string
          p_name: string
          p_description: string
          p_discount_type: string
          p_discount_value: number
          p_min_order_value: number
          p_max_discount_value: number
          p_max_uses: number
          p_start_date: string
          p_end_date: string
          p_is_customer_specific?: boolean
          p_usage_limit_per_customer?: number
          p_first_time_customers_only?: boolean
          p_min_previous_orders?: number
          p_excluded_product_ids?: string[]
          p_included_product_ids?: string[]
          p_excluded_category_ids?: string[]
          p_included_category_ids?: string[]
        }
        Returns: Json
      }
      decrement_account_counter: {
        Args: {
          p_account_id: string
          p_counter_name: string
          p_decrement?: number
        }
        Returns: undefined
      }
      delete_customer_profile_v2: {
        Args: { p_customer_id: string; p_team_account_id: string }
        Returns: boolean
      }
      delete_flash_sale: {
        Args: { p_flash_sale_id: string; p_account_id: string }
        Returns: Json
      }
      delete_voucher: {
        Args: { p_voucher_id: string; p_account_id: string }
        Returns: Json
      }
      generate_attendance_report: {
        Args: {
          p_account_id: string
          p_report_name: string
          p_report_scope: string
          p_scope_id?: string
          p_start_date?: string
          p_end_date?: string
          p_filters?: Json
        }
        Returns: Json
      }
      generate_unique_learner_codes: {
        Args: { p_account_id: string; p_count: number }
        Returns: string[]
      }
      get_account_invitations: {
        Args: { account_slug: string }
        Returns: {
          id: number
          email: string
          account_id: string
          invited_by: string
          role: string
          created_at: string
          updated_at: string
          expires_at: string
          inviter_name: string
          inviter_email: string
        }[]
      }
      get_account_members: {
        Args: { account_slug: string }
        Returns: {
          id: string
          user_id: string
          account_id: string
          role: string
          role_hierarchy_level: number
          primary_owner_user_id: string
          name: string
          email: string
          picture_url: string
          created_at: string
          updated_at: string
        }[]
      }
      get_config: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_dashboard_overview: {
        Args: { p_account_id: string; p_start_date: string }
        Returns: {
          total_visitors: number
          total_pageviews: number
          total_orders: number
          total_revenue: number
          conversion_rate: number
        }[]
      }
      get_nonce_status: {
        Args: { p_id: string }
        Returns: Json
      }
      get_oa_config_for_account: {
        Args: { p_account_id: string }
        Returns: string
      }
      get_optimal_attendance_method: {
        Args: { p_program_id: string; p_session_type?: string }
        Returns: string
      }
      get_product_flash_sale: {
        Args: { p_product_id: string; p_account_id: string }
        Returns: Json
      }
      get_upper_system_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      has_active_subscription: {
        Args: { target_account_id: string }
        Returns: boolean
      }
      has_more_elevated_role: {
        Args: {
          target_user_id: string
          target_account_id: string
          role_name: string
        }
        Returns: boolean
      }
      has_permission: {
        Args: {
          user_id: string
          account_id: string
          permission_name: Database["public"]["Enums"]["app_permissions"]
        }
        Returns: boolean
      }
      has_role_on_account: {
        Args: { account_id: string; account_role?: string }
        Returns: boolean
      }
      has_same_role_hierarchy_level: {
        Args: {
          target_user_id: string
          target_account_id: string
          role_name: string
        }
        Returns: boolean
      }
      increment_account_counter: {
        Args: {
          p_account_id: string
          p_counter_name: string
          p_increment?: number
        }
        Returns: undefined
      }
      initialize_account_usage_stats: {
        Args: { p_account_id: string }
        Returns: boolean
      }
      insert_analytics_event: {
        Args: {
          account_id: string
          theme_id: string
          event_type: string
          event_data: Json
          visitor_id: string
          user_id: string
          device_type: string
          source: string
        }
        Returns: Json
      }
      is_aal2: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_account_owner: {
        Args: { account_id: string }
        Returns: boolean
      }
      is_account_team_member: {
        Args: { target_account_id: string }
        Returns: boolean
      }
      is_mfa_compliant: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_oa_token_valid: {
        Args: { oa_config_id: string }
        Returns: boolean
      }
      is_set: {
        Args: { field_name: string }
        Returns: boolean
      }
      is_super_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_team_member: {
        Args: { account_id: string; user_id: string }
        Returns: boolean
      }
      load_customer_profiles_v2: {
        Args: {
          p_team_account_id: string
          p_search_query?: string
          p_filter?: string
          p_page?: number
          p_limit?: number
        }
        Returns: {
          id: string
          team_account_id: string
          email: string
          first_name: string
          last_name: string
          phone: string
          avatar_url: string
          customer_status: string
          value_tier: string
          total_spent: number
          total_orders: number
          engagement_score: number
          churn_risk_score: number
          churn_risk_level: string
          last_active_at: string
          created_at: string
          total_count: number
        }[]
      }
      load_customer_segments: {
        Args: {
          p_account_id: string
          p_search_query?: string
          p_filter?: string
          p_page?: number
          p_limit?: number
        }
        Returns: {
          id: string
          account_id: string
          name: string
          description: string
          type: string
          criteria: Json
          customer_count: number
          growth_rate: number
          engagement_score: number
          avg_value: number
          is_active: boolean
          is_auto_updating: boolean
          metadata: Json
          created_at: string
          updated_at: string
          total_count: number
        }[]
      }
      process_qr_checkin: {
        Args: {
          p_qr_code: string
          p_learner_id: string
          p_checkin_location?: Json
          p_device_info?: Json
          p_guardian_info?: Json
        }
        Returns: Json
      }
      refresh_usage_stats: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      remove_customer_phones_from_voucher: {
        Args: { p_voucher_id: string; p_phone_numbers: string[] }
        Returns: Json
      }
      revoke_nonce: {
        Args: { p_id: string; p_reason?: string }
        Returns: boolean
      }
      save_temp_theme_to_account: {
        Args: {
          p_temp_theme_id: string
          p_account_id: string
          p_mini_app_id: string
        }
        Returns: string
      }
      team_account_workspace: {
        Args: { account_slug: string }
        Returns: {
          id: string
          name: string
          picture_url: string
          slug: string
          role: string
          role_hierarchy_level: number
          primary_owner_user_id: string
          subscription_status: Database["public"]["Enums"]["subscription_status"]
          permissions: Database["public"]["Enums"]["app_permissions"][]
        }[]
      }
      transfer_team_account_ownership: {
        Args: { target_account_id: string; new_owner_id: string }
        Returns: undefined
      }
      update_customer_stats: {
        Args: {
          p_customer_id: string
          p_order_amount?: number
          p_order_date?: string
        }
        Returns: undefined
      }
      update_flash_sale: {
        Args: {
          p_flash_sale_id: string
          p_account_id: string
          p_name: string
          p_description: string
          p_start_time: string
          p_end_time: string
          p_products: Json
          p_status?: string
        }
        Returns: Json
      }
      update_photo_session_status: {
        Args: {
          p_session_id: string
          p_status: string
          p_total_faces?: number
          p_total_recognized?: number
          p_metadata?: Json
        }
        Returns: Json
      }
      upsert_order: {
        Args: {
          target_account_id: string
          target_customer_id: string
          target_order_id: string
          status: Database["public"]["Enums"]["payment_status"]
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          total_amount: number
          currency: string
          line_items: Json
        }
        Returns: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at: string
        }
      }
      upsert_subscription: {
        Args: {
          target_account_id: string
          target_customer_id: string
          target_subscription_id: string
          active: boolean
          status: Database["public"]["Enums"]["subscription_status"]
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          currency: string
          period_starts_at: string
          period_ends_at: string
          line_items: Json
          trial_starts_at?: string
          trial_ends_at?: string
        }
        Returns: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at: string
          currency: string
          id: string
          is_free: boolean | null
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          trial_starts_at: string | null
          updated_at: string
        }
      }
      verify_nonce: {
        Args: {
          p_token: string
          p_purpose: string
          p_user_id?: string
          p_required_scopes?: string[]
          p_max_verification_attempts?: number
          p_ip?: unknown
          p_user_agent?: string
        }
        Returns: Json
      }
    }
    Enums: {
      app_permissions:
        | "roles.manage"
        | "billing.manage"
        | "settings.manage"
        | "members.manage"
        | "invites.manage"
        | "teams.manage"
        | "products.manage"
        | "categories.manage"
        | "orders.manage"
        | "points.manage"
        | "branches.manage"
        | "inventory.manage"
        | "notifications.manage"
        | "miniapps.manage"
        | "miniapps.view"
        | "miniapps.themes.manage"
        | "zns.manage"
        | "zns.view"
        | "customers.manage"
        | "customers.view"
        | "orders.view"
        | "points.view"
        | "integrations.manage"
        | "integrations.view"
        | "integrations.connect"
        | "data.manage"
        | "cdp.manage"
        | "cdp.view"
        | "cdp.profiles.manage"
        | "cdp.profiles.view"
        | "cdp.segments.manage"
        | "cdp.segments.view"
        | "cdp.journeys.manage"
        | "cdp.journeys.view"
        | "cdp.analytics.view"
        | "cdp.insights.view"
        | "education.manage"
        | "education.view"
        | "education.learners.manage"
        | "education.learners.view"
        | "education.programs.manage"
        | "education.programs.view"
        | "education.instructors.manage"
        | "education.instructors.view"
        | "education.attendance.manage"
        | "education.attendance.view"
        | "education.fees.manage"
        | "education.fees.view"
        | "education.events.manage"
        | "education.events.view"
        | "education.messages.manage"
        | "education.messages.view"
        | "education.reports.view"
        | "education.analytics.view"
      billing_provider: "stripe" | "lemon-squeezy" | "paddle" | "zalopay"
      integration_status: "not_connected" | "connected" | "error" | "pending"
      integration_type:
        | "stripe"
        | "zapier"
        | "zalo"
        | "shopee"
        | "lazada"
        | "tiktok"
        | "facebook"
        | "google"
        | "custom"
        | "ipos"
      message_priority: "low" | "normal" | "high" | "urgent"
      message_status: "draft" | "scheduled" | "sent" | "failed"
      message_type: "announcement" | "invitation" | "fee_reminder" | "emergency"
      notification_channel: "in_app" | "email"
      notification_type: "info" | "warning" | "error"
      oa_type: "shared" | "private"
      payment_status: "pending" | "succeeded" | "failed"
      product_type: "physical" | "digital" | "service"
      recipient_type:
        | "all_parents"
        | "class_parents"
        | "specific_parents"
        | "instructors"
      short_link_type: "theme" | "product" | "campaign" | "custom"
      subscription_item_type: "flat" | "per_seat" | "metered"
      subscription_status:
        | "active"
        | "trialing"
        | "past_due"
        | "canceled"
        | "unpaid"
        | "incomplete"
        | "incomplete_expired"
        | "paused"
      theme_type: "free" | "paid" | "custom" | "default"
      zns_event_type:
        | "order_created"
        | "order_updated"
        | "promotion"
        | "theme_activated"
      zns_status: "pending" | "success" | "failed"
    }
    CompositeTypes: {
      invitation: {
        email: string | null
        role: string | null
      }
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          level: number | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          user_metadata: Json | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "objects_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      prefixes: {
        Row: {
          bucket_id: string
          created_at: string | null
          level: number
          name: string
          updated_at: string | null
        }
        Insert: {
          bucket_id: string
          created_at?: string | null
          level?: number
          name: string
          updated_at?: string | null
        }
        Update: {
          bucket_id?: string
          created_at?: string | null
          level?: number
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "prefixes_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          in_progress_size: number
          key: string
          owner_id: string | null
          upload_signature: string
          user_metadata: Json | null
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id: string
          in_progress_size?: number
          key: string
          owner_id?: string | null
          upload_signature: string
          user_metadata?: Json | null
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          in_progress_size?: number
          key?: string
          owner_id?: string | null
          upload_signature?: string
          user_metadata?: Json | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string
          created_at: string
          etag: string
          id: string
          key: string
          owner_id: string | null
          part_number: number
          size: number
          upload_id: string
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          etag: string
          id?: string
          key: string
          owner_id?: string | null
          part_number: number
          size?: number
          upload_id: string
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          etag?: string
          id?: string
          key?: string
          owner_id?: string | null
          part_number?: number
          size?: number
          upload_id?: string
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_parts_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "s3_multipart_uploads_parts_upload_id_fkey"
            columns: ["upload_id"]
            isOneToOne: false
            referencedRelation: "s3_multipart_uploads"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_prefixes: {
        Args: { _bucket_id: string; _name: string }
        Returns: undefined
      }
      can_insert_object: {
        Args: { bucketid: string; name: string; owner: string; metadata: Json }
        Returns: undefined
      }
      delete_prefix: {
        Args: { _bucket_id: string; _name: string }
        Returns: boolean
      }
      extension: {
        Args: { name: string }
        Returns: string
      }
      filename: {
        Args: { name: string }
        Returns: string
      }
      foldername: {
        Args: { name: string }
        Returns: string[]
      }
      get_level: {
        Args: { name: string }
        Returns: number
      }
      get_prefix: {
        Args: { name: string }
        Returns: string
      }
      get_prefixes: {
        Args: { name: string }
        Returns: string[]
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          size: number
          bucket_id: string
        }[]
      }
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          next_key_token?: string
          next_upload_token?: string
        }
        Returns: {
          key: string
          id: string
          created_at: string
        }[]
      }
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          start_after?: string
          next_token?: string
        }
        Returns: {
          name: string
          id: string
          metadata: Json
          updated_at: string
        }[]
      }
      operation: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_legacy_v1: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_v1_optimised: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_v2: {
        Args: {
          prefix: string
          bucket_name: string
          limits?: number
          levels?: number
          start_after?: string
        }
        Returns: {
          key: string
          name: string
          id: string
          updated_at: string
          created_at: string
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      app_permissions: [
        "roles.manage",
        "billing.manage",
        "settings.manage",
        "members.manage",
        "invites.manage",
        "teams.manage",
        "products.manage",
        "categories.manage",
        "orders.manage",
        "points.manage",
        "branches.manage",
        "inventory.manage",
        "notifications.manage",
        "miniapps.manage",
        "miniapps.view",
        "miniapps.themes.manage",
        "zns.manage",
        "zns.view",
        "customers.manage",
        "customers.view",
        "orders.view",
        "points.view",
        "integrations.manage",
        "integrations.view",
        "integrations.connect",
        "data.manage",
        "cdp.manage",
        "cdp.view",
        "cdp.profiles.manage",
        "cdp.profiles.view",
        "cdp.segments.manage",
        "cdp.segments.view",
        "cdp.journeys.manage",
        "cdp.journeys.view",
        "cdp.analytics.view",
        "cdp.insights.view",
        "education.manage",
        "education.view",
        "education.learners.manage",
        "education.learners.view",
        "education.programs.manage",
        "education.programs.view",
        "education.instructors.manage",
        "education.instructors.view",
        "education.attendance.manage",
        "education.attendance.view",
        "education.fees.manage",
        "education.fees.view",
        "education.events.manage",
        "education.events.view",
        "education.messages.manage",
        "education.messages.view",
        "education.reports.view",
        "education.analytics.view",
      ],
      billing_provider: ["stripe", "lemon-squeezy", "paddle", "zalopay"],
      integration_status: ["not_connected", "connected", "error", "pending"],
      integration_type: [
        "stripe",
        "zapier",
        "zalo",
        "shopee",
        "lazada",
        "tiktok",
        "facebook",
        "google",
        "custom",
        "ipos",
      ],
      message_priority: ["low", "normal", "high", "urgent"],
      message_status: ["draft", "scheduled", "sent", "failed"],
      message_type: ["announcement", "invitation", "fee_reminder", "emergency"],
      notification_channel: ["in_app", "email"],
      notification_type: ["info", "warning", "error"],
      oa_type: ["shared", "private"],
      payment_status: ["pending", "succeeded", "failed"],
      product_type: ["physical", "digital", "service"],
      recipient_type: [
        "all_parents",
        "class_parents",
        "specific_parents",
        "instructors",
      ],
      short_link_type: ["theme", "product", "campaign", "custom"],
      subscription_item_type: ["flat", "per_seat", "metered"],
      subscription_status: [
        "active",
        "trialing",
        "past_due",
        "canceled",
        "unpaid",
        "incomplete",
        "incomplete_expired",
        "paused",
      ],
      theme_type: ["free", "paid", "custom", "default"],
      zns_event_type: [
        "order_created",
        "order_updated",
        "promotion",
        "theme_activated",
      ],
      zns_status: ["pending", "success", "failed"],
    },
  },
  storage: {
    Enums: {},
  },
} as const

