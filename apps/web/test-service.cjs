// Test script to test education organization creation service
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testService() {
  try {
    console.log('Testing education organization creation...');
    
    // First, create a new account (team)
    const teamName = 'Trường Mầm non Test Education';
    const teamSlug = teamName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
    
    console.log('Creating account with name:', teamName);
    console.log('Slug:', teamSlug);
    
    // Use existing account instead of creating new one
    const { data: existingAccounts } = await supabase
      .from('accounts')
      .select('*')
      .eq('is_personal_account', false)
      .limit(1);

    let account;
    if (existingAccounts && existingAccounts.length > 0) {
      account = existingAccounts[0];
      console.log('Using existing account:', account.id, account.name);
    } else {
      console.error('No existing team accounts found');
      return;
    }

    console.log('✅ Account created:', account.id);

    // Now test education sample data creation
    const educationTemplate = {
      "programs": [
        {
          "name": "Lớp Chồi (3-4 tuổi)",
          "program_type": "preschool",
          "age_range_min": 36,
          "age_range_max": 48,
          "description": "Chương trình giáo dục mầm non cho trẻ 3-4 tuổi",
          "duration_months": 12,
          "tuition_fee": 4000000,
          "meal_fee": 800000,
          "schedule": "Thứ 2 - Thứ 6, 7:30 - 16:30",
          "max_students": 20,
          "status": "active"
        },
        {
          "name": "Lớp Lá (4-5 tuổi)",
          "program_type": "kindergarten",
          "age_range_min": 48,
          "age_range_max": 60,
          "description": "Chương trình giáo dục mầm non cho trẻ 4-5 tuổi",
          "duration_months": 12,
          "tuition_fee": 4500000,
          "meal_fee": 900000,
          "schedule": "Thứ 2 - Thứ 6, 7:30 - 16:30",
          "max_students": 25,
          "status": "active"
        }
      ],
      "instructors": [
        {
          "full_name": "Nguyễn Thị Hoa",
          "email": "<EMAIL>",
          "phone": "0909123456",
          "specialization": "Giáo dục mầm non",
          "experience_years": 8,
          "education_level": "Cử nhân Sư phạm Mầm non",
          "certifications": ["Chứng chỉ Montessori"],
          "status": "active",
          "hire_date": "2018-08-15"
        },
        {
          "full_name": "Trần Văn Nam",
          "email": "<EMAIL>",
          "phone": "0909123457",
          "specialization": "Thể dục mầm non",
          "experience_years": 5,
          "education_level": "Cử nhân Thể dục thể thao",
          "certifications": ["Chứng chỉ Yoga trẻ em"],
          "status": "active",
          "hire_date": "2020-09-01"
        }
      ],
      "learners": [
        {
          "learner_code": "HM2024001",
          "full_name": "Nguyễn Minh An",
          "nickname": "Bé An",
          "date_of_birth": "2021-03-15",
          "gender": "male",
          "address": "456 Đường Lê Lợi, Quận 1, TP.HCM",
          "guardian_name": "Nguyễn Văn Bình",
          "guardian_phone": "**********",
          "guardian_email": "<EMAIL>",
          "guardian_relationship": "father",
          "program_name": "Lớp Chồi (3-4 tuổi)",
          "enrollment_date": "2024-09-01",
          "status": "active",
          "health_info": {
            "allergies": [],
            "medical_conditions": [],
            "emergency_contact": {
              "name": "Nguyễn Thị Lan",
              "relationship": "mother",
              "phone": "**********"
            }
          }
        }
      ],
      "fees": [
        {
          "learner_code": "HM2024001",
          "fee_type": "tuition",
          "amount": 4000000,
          "due_date": "2024-12-05",
          "description": "Học phí tháng 12/2024",
          "status": "pending"
        }
      ],
      "events": [
        {
          "title": "Họp phụ huynh đầu năm học",
          "description": "Họp phụ huynh để thông báo kế hoạch năm học mới",
          "start_datetime": "2024-12-20T19:00:00+07:00",
          "end_datetime": "2024-12-20T21:00:00+07:00",
          "location": "Hội trường trường",
          "event_type": "meeting",
          "max_participants": 100,
          "registration_deadline": "2024-12-18T23:59:59+07:00",
          "status": "active"
        }
      ]
    };

    console.log('Creating education sample data...');

    // Call the education sample data function
    const { data, error } = await supabase
      .rpc('create_education_sample_data_for_account', {
        p_account_id: account.id,
        p_education_template: educationTemplate
      });

    console.log('Education sample data result:', { data, error });

    if (data && data.success) {
      console.log('✅ Education sample data created successfully!');
      console.log('Stats:', data.stats);
      
      // Check created data
      const { data: programs } = await supabase
        .from('programs')
        .select('*')
        .eq('account_id', account.id);
      
      const { data: instructors } = await supabase
        .from('instructors')
        .select('*')
        .eq('account_id', account.id);
        
      const { data: learners } = await supabase
        .from('learners')
        .select('*')
        .eq('account_id', account.id);

      const { data: fees } = await supabase
        .from('fees')
        .select('*')
        .eq('account_id', account.id);

      const { data: events } = await supabase
        .from('events')
        .select('*')
        .eq('account_id', account.id);

      console.log('✅ Final verification:');
      console.log('- Programs:', programs?.length || 0);
      console.log('- Instructors:', instructors?.length || 0);
      console.log('- Learners:', learners?.length || 0);
      console.log('- Fees:', fees?.length || 0);
      console.log('- Events:', events?.length || 0);

      if (programs?.length > 0) {
        console.log('✅ SUCCESS: Education organization created with sample data!');
        console.log('Account ID:', account.id);
        console.log('Account Slug:', account.slug);
        console.log('Visit: http://localhost:3001/home/' + account.slug + '/education');
      }
    } else {
      console.error('❌ Failed to create education sample data');
      console.error('Error:', error || data?.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testService();
