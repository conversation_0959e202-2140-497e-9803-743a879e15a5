'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { toast } from '@kit/ui/sonner';

import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Badge } from '@kit/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Bus, 
  Calendar,
  User,
  Phone,
  Shield,
  Wrench,
  Info,
} from 'lucide-react';

interface Props {
  account: string;
  vehicleId: string;
}

interface Vehicle {
  id: string;
  vehicle_number: string;
  license_plate: string;
  vehicle_type: string;
  brand: string;
  model: string;
  year: number;
  capacity: number;
  driver_name: string;
  driver_phone: string;
  driver_license: string;
  status: string;
  insurance_info: {
    company?: string;
    policy_number?: string;
    expiry_date?: string;
  };
  maintenance_schedule: {
    last_maintenance?: string;
    next_maintenance?: string;
    maintenance_notes?: string;
  };
  created_at: string;
  updated_at: string;
}

export function VehicleDetails({ account, vehicleId }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();
  
  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);

  const currentAccount = accounts?.find(acc => acc.slug === account);

  useEffect(() => {
    loadVehicleDetails();
  }, [vehicleId]);

  const loadVehicleDetails = async () => {
    try {
      setLoading(true);

      if (!currentAccount?.id) {
        setVehicle(null);
        return;
      }

      const { data: vehicleData, error: vehicleError } = await supabase
        .from('vehicles')
        .select('*')
        .eq('id', vehicleId)
        .eq('account_id', currentAccount.id)
        .single();

      if (vehicleError) {
        throw new Error(vehicleError.message);
      }

      if (!vehicleData) {
        setVehicle(null);
        return;
      }

      setVehicle(vehicleData);
    } catch (error: any) {
      console.error('Error loading vehicle details:', error);
      toast.error(error.message || 'Failed to load vehicle details');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!currentAccount?.id || !vehicle) return;

    try {
      setDeleting(true);

      const { error: deleteError } = await supabase
        .from('vehicles')
        .delete()
        .eq('id', vehicleId)
        .eq('account_id', currentAccount.id);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      toast.success('Phuong tien da duoc xoa thanh cong');
      router.push(`/home/<USER>/education/transportation/vehicles`);
    } catch (error: any) {
      console.error('Error deleting vehicle:', error);
      toast.error(error.message || 'Failed to delete vehicle');
    } finally {
      setDeleting(false);
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Hoạt động</Badge>;
      case 'maintenance':
        return <Badge className="bg-yellow-100 text-yellow-800">Bảo trì</Badge>;
      case 'retired':
        return <Badge className="bg-gray-100 text-gray-800">Ngừng hoạt động</Badge>;
      case 'out_of_service':
        return <Badge className="bg-red-100 text-red-800">Hỏng hóc</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getVehicleTypeBadge = (type: string) => {
    switch (type) {
      case 'bus':
        return <Badge className="bg-blue-100 text-blue-800">Xe buýt</Badge>;
      case 'van':
        return <Badge className="bg-purple-100 text-purple-800">Xe van</Badge>;
      case 'car':
        return <Badge className="bg-green-100 text-green-800">Ô tô</Badge>;
      case 'minibus':
        return <Badge className="bg-orange-100 text-orange-800">Xe khách nhỏ</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-2 text-sm">Đang tải...</p>
        </div>
      </div>
    );
  }

  if (!vehicle) {
    return <div className="text-center py-8 text-red-600">Vehicle not found</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Quay lại</span>
        </Button>

        <div className="flex items-center space-x-2">
          <Button variant="outline" asChild>
            <Link href={`/home/<USER>/education/transportation/vehicles/${vehicleId}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Link>
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                className="text-red-600 hover:text-red-700"
                disabled={deleting}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {deleting ? 'Dang xoa...' : 'Xoa'}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Xac nhan xoa phuong tien</AlertDialogTitle>
                <AlertDialogDescription>
                  Ban co chac chan muon xoa phuong tien nay? Hanh dong nay khong the hoan tac.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Huy</AlertDialogCancel>
                <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
                  Xoa phuong tien
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* Vehicle Details */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-xl">{vehicle.vehicle_number}</CardTitle>
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                <span>Biển số: {vehicle.license_plate}</span>
                <span>•</span>
                <span>Tạo lúc: {formatDateTime(vehicle.created_at)}</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Bus className="h-6 w-6 text-blue-600" />
              <div className="flex flex-col space-y-1">
                {getVehicleTypeBadge(vehicle.vehicle_type)}
                {getStatusBadge(vehicle.status)}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Thông tin cơ bản</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Hãng xe:</span>
                  <span>{vehicle.brand}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Model:</span>
                  <span>{vehicle.model}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Năm sản xuất:</span>
                  <span>{vehicle.year}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Sức chứa:</span>
                  <span>{vehicle.capacity} người</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3">Thông tin tài xế</h4>
              <div className="space-y-3">
                {vehicle.driver_name ? (
                  <>
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span>{vehicle.driver_name}</span>
                    </div>
                    {vehicle.driver_phone && (
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-gray-400" />
                        <span>{vehicle.driver_phone}</span>
                      </div>
                    )}
                    {vehicle.driver_license && (
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Số bằng lái:</span>
                        <span>{vehicle.driver_license}</span>
                      </div>
                    )}
                  </>
                ) : (
                  <p className="text-gray-500 italic">Chưa có thông tin tài xế</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Insurance Information */}
      {vehicle.insurance_info && Object.keys(vehicle.insurance_info).some(key => vehicle.insurance_info[key as keyof typeof vehicle.insurance_info]) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Thông tin bảo hiểm</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {vehicle.insurance_info.company && (
                <div>
                  <p className="text-sm text-gray-600">Công ty bảo hiểm</p>
                  <p className="font-medium">{vehicle.insurance_info.company}</p>
                </div>
              )}
              {vehicle.insurance_info.policy_number && (
                <div>
                  <p className="text-sm text-gray-600">Số hợp đồng</p>
                  <p className="font-medium">{vehicle.insurance_info.policy_number}</p>
                </div>
              )}
              {vehicle.insurance_info.expiry_date && (
                <div>
                  <p className="text-sm text-gray-600">Ngày hết hạn</p>
                  <p className="font-medium">{formatDate(vehicle.insurance_info.expiry_date)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Maintenance Schedule */}
      {vehicle.maintenance_schedule && Object.keys(vehicle.maintenance_schedule).some(key => vehicle.maintenance_schedule[key as keyof typeof vehicle.maintenance_schedule]) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Wrench className="h-5 w-5" />
              <span>Lịch bảo trì</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {vehicle.maintenance_schedule.last_maintenance && (
                  <div>
                    <p className="text-sm text-gray-600">Bảo trì lần cuối</p>
                    <p className="font-medium">{formatDate(vehicle.maintenance_schedule.last_maintenance)}</p>
                  </div>
                )}
                {vehicle.maintenance_schedule.next_maintenance && (
                  <div>
                    <p className="text-sm text-gray-600">Bảo trì tiếp theo</p>
                    <p className="font-medium">{formatDate(vehicle.maintenance_schedule.next_maintenance)}</p>
                  </div>
                )}
              </div>
              {vehicle.maintenance_schedule.maintenance_notes && (
                <div>
                  <p className="text-sm text-gray-600 mb-2">Ghi chú bảo trì</p>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm">{vehicle.maintenance_schedule.maintenance_notes}</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="h-5 w-5" />
            <span>Thông tin hệ thống</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Tạo lúc:</span>
              <span className="ml-2 font-medium">{formatDateTime(vehicle.created_at)}</span>
            </div>
            <div>
              <span className="text-gray-600">Cập nhật lúc:</span>
              <span className="ml-2 font-medium">{formatDateTime(vehicle.updated_at)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
