'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Textarea } from '@kit/ui/textarea';

interface Props {
  account: string;
}

function AddVehicleForm({ account }: Props) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();
  const router = useRouter();

  // Form state
  const [formData, setFormData] = useState({
    vehicle_number: '',
    license_plate: '',
    vehicle_type: 'bus',
    brand: '',
    model: '',
    year: new Date().getFullYear(),
    capacity: 0,
    driver_name: '',
    driver_phone: '',
    driver_license: '',
    status: 'active',
    insurance_info: {
      company: '',
      policy_number: '',
      expiry_date: '',
    },
    maintenance_schedule: {
      last_maintenance: '',
      next_maintenance: '',
      maintenance_notes: '',
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const accountId = workspace.account.id;

      const { error: insertError } = await supabase.from('vehicles').insert({
        account_id: accountId,
        vehicle_number: formData.vehicle_number,
        license_plate: formData.license_plate,
        vehicle_type: formData.vehicle_type,
        brand: formData.brand,
        model: formData.model,
        year: formData.year,
        capacity: formData.capacity,
        driver_name: formData.driver_name || null,
        driver_phone: formData.driver_phone || null,
        driver_license: formData.driver_license || null,
        status: formData.status,
        insurance_info: formData.insurance_info,
        maintenance_schedule: formData.maintenance_schedule,
      });

      if (insertError) throw insertError;

      toast.success('Phương tiện đã được thêm thành công');
      router.push(`/home/<USER>/education/transportation/vehicles`);
    } catch (err: any) {
      console.error('Error creating vehicle:', err);
      setError(err.message || 'Có lỗi xảy ra khi thêm phương tiện');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleInsuranceChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      insurance_info: {
        ...prev.insurance_info,
        [field]: value,
      },
    }));
  };

  const handleMaintenanceChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      maintenance_schedule: {
        ...prev.maintenance_schedule,
        [field]: value,
      },
    }));
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Thông tin cơ bản</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="vehicle_number">Số hiệu xe *</Label>
                <Input
                  id="vehicle_number"
                  value={formData.vehicle_number}
                  onChange={(e) =>
                    handleInputChange('vehicle_number', e.target.value)
                  }
                  placeholder="VD: BUS-001"
                  required
                />
              </div>
              <div>
                <Label htmlFor="license_plate">Biển số xe *</Label>
                <Input
                  id="license_plate"
                  value={formData.license_plate}
                  onChange={(e) =>
                    handleInputChange('license_plate', e.target.value)
                  }
                  placeholder="VD: 30A-12345"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="vehicle_type">Loại xe</Label>
                <Select
                  value={formData.vehicle_type}
                  onValueChange={(value) =>
                    handleInputChange('vehicle_type', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bus">Xe buýt</SelectItem>
                    <SelectItem value="van">Xe van</SelectItem>
                    <SelectItem value="car">Xe ô tô</SelectItem>
                    <SelectItem value="minibus">Xe buýt nhỏ</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="capacity">Sức chứa *</Label>
                <Input
                  id="capacity"
                  type="number"
                  value={formData.capacity}
                  onChange={(e) =>
                    handleInputChange('capacity', parseInt(e.target.value) || 0)
                  }
                  placeholder="Số chỗ ngồi"
                  min="1"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="brand">Hãng xe</Label>
                <Input
                  id="brand"
                  value={formData.brand}
                  onChange={(e) => handleInputChange('brand', e.target.value)}
                  placeholder="VD: Toyota, Hyundai"
                />
              </div>
              <div>
                <Label htmlFor="model">Mẫu xe</Label>
                <Input
                  id="model"
                  value={formData.model}
                  onChange={(e) => handleInputChange('model', e.target.value)}
                  placeholder="VD: Hiace, County"
                />
              </div>
              <div>
                <Label htmlFor="year">Năm sản xuất</Label>
                <Input
                  id="year"
                  type="number"
                  value={formData.year}
                  onChange={(e) =>
                    handleInputChange(
                      'year',
                      parseInt(e.target.value) || new Date().getFullYear(),
                    )
                  }
                  min="1990"
                  max={new Date().getFullYear() + 1}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="status">Trạng thái</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Hoạt động</SelectItem>
                  <SelectItem value="maintenance">Bảo trì</SelectItem>
                  <SelectItem value="retired">Ngừng hoạt động</SelectItem>
                  <SelectItem value="out_of_service">Hỏng hóc</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Thông tin tài xế</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="driver_name">Tên tài xế</Label>
              <Input
                id="driver_name"
                value={formData.driver_name}
                onChange={(e) =>
                  handleInputChange('driver_name', e.target.value)
                }
                placeholder="Họ và tên tài xế"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="driver_phone">Số điện thoại</Label>
                <Input
                  id="driver_phone"
                  value={formData.driver_phone}
                  onChange={(e) =>
                    handleInputChange('driver_phone', e.target.value)
                  }
                  placeholder="Số điện thoại tài xế"
                />
              </div>
              <div>
                <Label htmlFor="driver_license">Số bằng lái</Label>
                <Input
                  id="driver_license"
                  value={formData.driver_license}
                  onChange={(e) =>
                    handleInputChange('driver_license', e.target.value)
                  }
                  placeholder="Số giấy phép lái xe"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Thông tin bảo hiểm</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="insurance_company">Công ty bảo hiểm</Label>
              <Input
                id="insurance_company"
                value={formData.insurance_info.company}
                onChange={(e) =>
                  handleInsuranceChange('company', e.target.value)
                }
                placeholder="Tên công ty bảo hiểm"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="policy_number">Số hợp đồng</Label>
                <Input
                  id="policy_number"
                  value={formData.insurance_info.policy_number}
                  onChange={(e) =>
                    handleInsuranceChange('policy_number', e.target.value)
                  }
                  placeholder="Số hợp đồng bảo hiểm"
                />
              </div>
              <div>
                <Label htmlFor="insurance_expiry">Ngày hết hạn</Label>
                <Input
                  id="insurance_expiry"
                  type="date"
                  value={formData.insurance_info.expiry_date}
                  onChange={(e) =>
                    handleInsuranceChange('expiry_date', e.target.value)
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Lịch bảo trì</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="last_maintenance">Bảo trì lần cuối</Label>
                <Input
                  id="last_maintenance"
                  type="date"
                  value={formData.maintenance_schedule.last_maintenance}
                  onChange={(e) =>
                    handleMaintenanceChange('last_maintenance', e.target.value)
                  }
                />
              </div>
              <div>
                <Label htmlFor="next_maintenance">Bảo trì tiếp theo</Label>
                <Input
                  id="next_maintenance"
                  type="date"
                  value={formData.maintenance_schedule.next_maintenance}
                  onChange={(e) =>
                    handleMaintenanceChange('next_maintenance', e.target.value)
                  }
                />
              </div>
            </div>
            <div>
              <Label htmlFor="maintenance_notes">Ghi chú bảo trì</Label>
              <Textarea
                id="maintenance_notes"
                value={formData.maintenance_schedule.maintenance_notes}
                onChange={(e) =>
                  handleMaintenanceChange('maintenance_notes', e.target.value)
                }
                placeholder="Ghi chú về lịch bảo trì"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {error && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() =>
              router.push(`/home/<USER>/education/transportation/vehicles`)
            }
          >
            Hủy
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? 'Đang thêm...' : 'Thêm phương tiện'}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default AddVehicleForm;
