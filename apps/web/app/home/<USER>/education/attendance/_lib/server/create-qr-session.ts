'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export interface CreateQRSessionParams {
  accountId: string;
  programId: string;
  sessionDate: string;
  sessionTime?: string;
  instructorId?: string;
  expiresInMinutes?: number;
  maxCheckins?: number;
  checkinWindowMinutes?: number;
}

export interface CreateQRSessionResult {
  sessionId: string;
  qrCode: string;
  qrData: Record<string, any>;
  expiresAt: string;
  enrolledCount: number;
  message: string;
}

export async function createQRCheckinSession(
  params: CreateQRSessionParams
): Promise<CreateQRSessionResult> {
  const client = getSupabaseServerClient();

  try {
    const { data, error } = await client.rpc('create_qr_checkin_session', {
      p_account_id: params.accountId,
      p_program_id: params.programId,
      p_session_date: params.sessionDate,
      p_session_time: params.sessionTime || null,
      p_instructor_id: params.instructorId || null,
      p_expires_in_minutes: params.expiresInMinutes || 60,
      p_max_checkins: params.maxCheckins || 50,
      p_checkin_window_minutes: params.checkinWindowMinutes || 30,
    });

    if (error) {
      console.error('Supabase RPC error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        function: 'create_qr_checkin_session',
        params,
      });
      throw error;
    }

    if (!data.success) {
      throw new Error(data.error || 'Failed to create QR check-in session');
    }

    return {
      sessionId: data.session_id,
      qrCode: data.qr_code,
      qrData: data.qr_data,
      expiresAt: data.expires_at,
      enrolledCount: data.enrolled_count,
      message: data.message,
    };
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to create QR check-in session:', {
      error: message,
      params,
      context: 'create-qr-session',
    });
    throw new Error(`Failed to create QR check-in session: ${message}`);
  }
}

export async function updateQRSessionStatus(
  sessionId: string,
  status: 'active' | 'expired' | 'disabled' | 'completed'
): Promise<void> {
  const client = getSupabaseServerClient();

  try {
    const { error } = await client
      .from('qr_checkin_sessions')
      .update({
        status,
        updated_at: new Date().toISOString(),
      })
      .eq('id', sessionId);

    if (error) {
      console.error('Supabase update error:', {
        message: error.message,
        details: error.details,
        hint: error.hint,
        code: error.code,
        table: 'qr_checkin_sessions',
        sessionId,
        status,
      });
      throw error;
    }
  } catch (error: any) {
    const message = error?.message || 'Unknown error';
    console.error('Failed to update QR session status:', {
      error: message,
      sessionId,
      status,
      context: 'create-qr-session',
    });
    throw new Error(`Failed to update QR session status: ${message}`);
  }
}
