'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import {
  Folder,
  Plus,
  Search,
  Filter,
  Grid3X3,
  List,
  Eye,
  Edit,
  Trash2,
  Image,
  Video,
  FileText,
  Calendar,
  Users,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface Props {
  account: string;
}

function AlbumsManager({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [loading, setLoading] = useState(true);
  const [albums, setAlbums] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    loadAlbums();
  }, []);

  const loadAlbums = async () => {
    try {
      setLoading(true);
      const accountId = workspace.account.id;

      const { data: albumsData, error } = await supabase
        .from('media_albums')
        .select(`
          id,
          name,
          description,
          album_type,
          privacy_level,
          created_at,
          cover_image_id,
          media_files!media_files_album_id_fkey(id, title, file_type, thumbnail_url)
        `)
        .eq('account_id', accountId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Format albums with item counts
      const formattedAlbums = albumsData?.map((album: any) => ({
        id: album.id,
        name: album.name,
        description: album.description,
        type: album.album_type,
        privacy: album.privacy_level,
        createdAt: new Date(album.created_at).toLocaleDateString('vi-VN'),
        itemCount: album.media_files?.length || 0,
        coverImage: album.media_files?.[0]?.thumbnail_url || null,
        fileTypes: getFileTypeCounts(album.media_files || []),
      })) || [];

      setAlbums(formattedAlbums);
    } catch (error) {
      console.error('Error loading albums:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFileTypeCounts = (files: any[]) => {
    const counts = { images: 0, videos: 0, documents: 0 };
    files.forEach(file => {
      if (file.file_type === 'image') counts.images++;
      else if (file.file_type === 'video') counts.videos++;
      else counts.documents++;
    });
    return counts;
  };

  const getAlbumTypeIcon = (type: string) => {
    switch (type) {
      case 'event': return <Calendar className="h-4 w-4" />;
      case 'class': return <Users className="h-4 w-4" />;
      case 'activity': return <Image className="h-4 w-4" />;
      default: return <Folder className="h-4 w-4" />;
    }
  };

  const getPrivacyBadgeVariant = (privacy: string) => {
    switch (privacy) {
      case 'public': return 'default';
      case 'parents_only': return 'secondary';
      case 'staff_only': return 'outline';
      default: return 'destructive';
    }
  };

  const filteredAlbums = albums.filter(album => {
    const matchesSearch = album.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         album.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || album.type === filterType;
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="container mx-auto max-w-7xl space-y-6 px-4 py-6">
        <div className="flex items-center justify-center py-12">
          <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-7xl space-y-8 px-4 py-6">
      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-600 via-violet-600 to-indigo-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Folder className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Thư viện Album</h1>
                  <p className="text-white/90 text-lg">Tổ chức media thành các bộ sưu tập và album</p>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Folder className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{albums.length}</div>
                      <div className="text-white/80 text-sm">Tổng album</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Image className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{albums.reduce((sum, album) => sum + album.fileTypes.images, 0)}</div>
                      <div className="text-white/80 text-sm">Hình ảnh</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Video className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{albums.reduce((sum, album) => sum + album.fileTypes.videos, 0)}</div>
                      <div className="text-white/80 text-sm">Video</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{albums.reduce((sum, album) => sum + album.fileTypes.documents, 0)}</div>
                      <div className="text-white/80 text-sm">Tài liệu</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <Button asChild className="bg-white text-purple-600 hover:bg-white/90 transition-all duration-200">
              <Link href={`/home/<USER>/education/library/albums/new`}>
                <Plus className="mr-2 h-4 w-4" />
                Tạo Album mới
              </Link>
            </Button>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Enhanced Filters and Search */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center gap-3 text-xl">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Search className="h-5 w-5 text-purple-600" />
            </div>
            Tìm kiếm và lọc album
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex flex-1 gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Tìm kiếm album..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-12 bg-white border-2 border-gray-200 hover:border-purple-300 transition-colors"
                />
              </div>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-48 h-12 bg-white border-2 border-gray-200 hover:border-purple-300 transition-colors">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Lọc theo loại" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả loại</SelectItem>
                  <SelectItem value="event">Sự kiện</SelectItem>
                  <SelectItem value="class">Lớp học</SelectItem>
                  <SelectItem value="activity">Hoạt động</SelectItem>
                  <SelectItem value="general">Chung</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="h-12"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="h-12"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Albums Grid/List */}
      {filteredAlbums.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Folder className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {searchTerm || filterType !== 'all'
                ? t('education:library.albums.noResults', 'No albums found')
                : t('education:library.albums.noAlbums', 'No albums yet')
              }
            </h3>
            <p className="text-gray-600 text-center mb-4">
              {searchTerm || filterType !== 'all'
                ? t('education:library.albums.noResultsDescription', 'Try adjusting your search or filters')
                : t('education:library.albums.noAlbumsDescription', 'Create your first album to organize your media')
              }
            </p>
            {!searchTerm && filterType === 'all' && (
              <Button asChild>
                <Link href={`/home/<USER>/education/library/albums/new`}>
                  <Plus className="mr-2 h-4 w-4" />
                  {t('education:library.albums.createFirstAlbum', 'Create First Album')}
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className={viewMode === 'grid'
          ? 'grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
          : 'space-y-4'
        }>
          {filteredAlbums.map((album) => (
            <Card key={album.id} className="group cursor-pointer overflow-hidden transition-all hover:shadow-lg">
              {viewMode === 'grid' ? (
                <>
                  {/* Cover Image */}
                  <div className="aspect-video bg-gray-100 relative overflow-hidden">
                    {album.coverImage ? (
                      <img
                        src={album.coverImage}
                        alt={album.name}
                        className="h-full w-full object-cover transition-transform group-hover:scale-105"
                      />
                    ) : (
                      <div className="flex h-full items-center justify-center">
                        <Folder className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                    <div className="absolute top-2 right-2">
                      <Badge variant={getPrivacyBadgeVariant(album.privacy)}>
                        {album.privacy}
                      </Badge>
                    </div>
                  </div>

                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getAlbumTypeIcon(album.type)}
                        <h3 className="font-semibold text-gray-900 truncate">{album.name}</h3>
                      </div>
                    </div>

                    {album.description && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{album.description}</p>
                    )}

                    <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                      <span>{album.itemCount} items</span>
                      <span>{album.createdAt}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 text-xs text-gray-500">
                        {album.fileTypes.images > 0 && (
                          <div className="flex items-center gap-1">
                            <Image className="h-3 w-3" />
                            <span>{album.fileTypes.images}</span>
                          </div>
                        )}
                        {album.fileTypes.videos > 0 && (
                          <div className="flex items-center gap-1">
                            <Video className="h-3 w-3" />
                            <span>{album.fileTypes.videos}</span>
                          </div>
                        )}
                        {album.fileTypes.documents > 0 && (
                          <div className="flex items-center gap-1">
                            <FileText className="h-3 w-3" />
                            <span>{album.fileTypes.documents}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/home/<USER>/education/library/albums/${album.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/home/<USER>/education/library/albums/${album.id}/edit`}>
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </>
              ) : (
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <div className="h-16 w-16 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      {album.coverImage ? (
                        <img
                          src={album.coverImage}
                          alt={album.name}
                          className="h-full w-full object-cover rounded-lg"
                        />
                      ) : (
                        <Folder className="h-8 w-8 text-gray-400" />
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        {getAlbumTypeIcon(album.type)}
                        <h3 className="font-semibold text-gray-900 truncate">{album.name}</h3>
                        <Badge variant={getPrivacyBadgeVariant(album.privacy)} className="ml-auto">
                          {album.privacy}
                        </Badge>
                      </div>

                      {album.description && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-1">{album.description}</p>
                      )}

                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>{album.itemCount} items</span>
                        <span>{album.createdAt}</span>
                        <div className="flex items-center gap-3">
                          {album.fileTypes.images > 0 && (
                            <div className="flex items-center gap-1">
                              <Image className="h-3 w-3" />
                              <span>{album.fileTypes.images}</span>
                            </div>
                          )}
                          {album.fileTypes.videos > 0 && (
                            <div className="flex items-center gap-1">
                              <Video className="h-3 w-3" />
                              <span>{album.fileTypes.videos}</span>
                            </div>
                          )}
                          {album.fileTypes.documents > 0 && (
                            <div className="flex items-center gap-1">
                              <FileText className="h-3 w-3" />
                              <span>{album.fileTypes.documents}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-1">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/home/<USER>/education/library/albums/${album.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/home/<USER>/education/library/albums/${album.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

export { AlbumsManager };
