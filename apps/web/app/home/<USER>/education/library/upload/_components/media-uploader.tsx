'use client';

import { useCallback, useState } from 'react';



import { useRouter } from 'next/navigation';



import { AlertCircle, Check, FileText, Folder, Image, Tag, Upload, Video, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';



import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';





interface Props {
  account: string;
}

interface UploadFile {
  id: string;
  file: File;
  preview?: string;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
}

function MediaUploader({ account }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const workspace = useTeamAccountWorkspace();

  const [files, setFiles] = useState<UploadFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);

  // Form data
  const [category, setCategory] = useState('');
  const [album, setAlbum] = useState('');
  const [tags, setTags] = useState('');
  const [isPublic, setIsPublic] = useState(false);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files));
    }
  };

  const handleFiles = (fileList: File[]) => {
    const newFiles: UploadFile[] = fileList.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      status: 'pending',
      progress: 0,
      preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
    }));

    setFiles(prev => [...prev, ...newFiles]);
  };

  const removeFile = (id: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === id);
      if (file?.preview) {
        URL.revokeObjectURL(file.preview);
      }
      return prev.filter(f => f.id !== id);
    });
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return <Image className="h-8 w-8 text-green-600" />;
    if (file.type.startsWith('video/')) return <Video className="h-8 w-8 text-purple-600" />;
    return <FileText className="h-8 w-8 text-blue-600" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const uploadFiles = async () => {
    if (files.length === 0) {
      toast.error('Please select files to upload');
      return;
    }

    setUploading(true);

    try {
      const accountId = workspace.account.id;

      for (const uploadFile of files) {
        if (uploadFile.status !== 'pending') continue;

        // Update status to uploading
        setFiles(prev => prev.map(f =>
          f.id === uploadFile.id
            ? { ...f, status: 'uploading', progress: 0 }
            : f
        ));

        try {
          // Simulate file upload progress
          for (let progress = 0; progress <= 100; progress += 10) {
            await new Promise(resolve => setTimeout(resolve, 100));
            setFiles(prev => prev.map(f =>
              f.id === uploadFile.id
                ? { ...f, progress }
                : f
            ));
          }

          // In a real implementation, you would upload to storage here
          // For now, we'll just insert the metadata
          const fileType = uploadFile.file.type.startsWith('image/') ? 'image' :
                          uploadFile.file.type.startsWith('video/') ? 'video' : 'document';

          const { error } = await supabase
            .from('media_files')
            .insert({
              account_id: accountId,
              title: uploadFile.file.name.split('.')[0],
              description: '',
              file_name: uploadFile.file.name,
              file_path: `/uploads/${uploadFile.file.name}`,
              file_url: `https://example.com/uploads/${uploadFile.file.name}`,
              file_type: fileType,
              mime_type: uploadFile.file.type,
              file_size: uploadFile.file.size,
              tags: tags ? tags.split(',').map(t => t.trim()) : [],
              is_public: isPublic,
              status: 'active',
            });

          if (error) throw error;

          // Update status to success
          setFiles(prev => prev.map(f =>
            f.id === uploadFile.id
              ? { ...f, status: 'success', progress: 100 }
              : f
          ));

        } catch (error) {
          console.error('Error uploading file:', error);
          setFiles(prev => prev.map(f =>
            f.id === uploadFile.id
              ? { ...f, status: 'error', error: 'Upload failed' }
              : f
          ));
        }
      }

      const successCount = files.filter(f => f.status === 'success').length;
      if (successCount > 0) {
        toast.success(`Successfully uploaded ${successCount} file(s)`);

        // Redirect to library after successful upload
        setTimeout(() => {
          router.push(`/home/<USER>/education/library`);
        }, 2000);
      }

    } catch (error) {
      console.error('Error in upload process:', error);
      toast.error('Upload failed');
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            {t('education:library.upload.title', 'Upload Media Files')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {t('education:library.upload.dropFiles', 'Drop files here or click to browse')}
            </h3>
            <p className="text-gray-600 mb-4">
              {t('education:library.upload.supportedFormats', 'Supports images, videos, and documents')}
            </p>
            <input
              type="file"
              multiple
              accept="image/*,video/*,.pdf,.doc,.docx,.txt"
              onChange={handleFileInput}
              className="hidden"
              id="file-upload"
            />
            <Button asChild>
              <label htmlFor="file-upload" className="cursor-pointer">
                {t('education:library.upload.selectFiles', 'Select Files')}
              </label>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Upload Options */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Upload Options</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="event">Events</SelectItem>
                    <SelectItem value="activity">Activities</SelectItem>
                    <SelectItem value="promotion">Promotion</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="album">Album (Optional)</Label>
                <Select value={album} onValueChange={setAlbum}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select album" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="events-2024">Events 2024</SelectItem>
                    <SelectItem value="activities">Activities</SelectItem>
                    <SelectItem value="promotion">Promotion Materials</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="tags">Tags (comma separated)</Label>
              <Input
                id="tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                placeholder="e.g. school, event, 2024"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* File List */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Files to Upload ({files.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {files.map((uploadFile) => (
                <div key={uploadFile.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  {/* File Preview/Icon */}
                  <div className="flex-shrink-0">
                    {uploadFile.preview ? (
                      <img
                        src={uploadFile.preview}
                        alt={uploadFile.file.name}
                        className="h-12 w-12 object-cover rounded"
                      />
                    ) : (
                      getFileIcon(uploadFile.file)
                    )}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">{uploadFile.file.name}</p>
                    <p className="text-sm text-gray-600">{formatFileSize(uploadFile.file.size)}</p>

                    {/* Progress Bar */}
                    {uploadFile.status === 'uploading' && (
                      <div className="mt-2">
                        <div className="bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full transition-all"
                            style={{ width: `${uploadFile.progress}%` }}
                          />
                        </div>
                        <p className="text-xs text-gray-600 mt-1">{uploadFile.progress}%</p>
                      </div>
                    )}
                  </div>

                  {/* Status */}
                  <div className="flex items-center gap-2">
                    {uploadFile.status === 'pending' && (
                      <Badge variant="secondary">Pending</Badge>
                    )}
                    {uploadFile.status === 'uploading' && (
                      <Badge variant="default">Uploading...</Badge>
                    )}
                    {uploadFile.status === 'success' && (
                      <Badge variant="default" className="bg-green-600">
                        <Check className="h-3 w-3 mr-1" />
                        Success
                      </Badge>
                    )}
                    {uploadFile.status === 'error' && (
                      <Badge variant="destructive">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Error
                      </Badge>
                    )}

                    {uploadFile.status === 'pending' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(uploadFile.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Upload Button */}
            <div className="flex justify-end mt-6">
              <Button
                onClick={uploadFiles}
                disabled={uploading || files.every(f => f.status !== 'pending')}
                className="min-w-32"
              >
                {uploading ? 'Uploading...' : `Upload ${files.filter(f => f.status === 'pending').length} Files`}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export { MediaUploader };
