import { redirect } from 'next/navigation';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import AddMedicalRecordForm from './_components/add-medical-record-form';

interface Props {
  params: Promise<{
    account: string;
  }>;
}

export const metadata = {
  title: 'Thê<PERSON> hồ sơ y tế',
};

async function NewMedicalRecordPage({ params }: Props) {
  const i18n = await createI18nServerInstance();
  const resolvedParams = await params;
  const { user, account } = await loadTeamWorkspace(resolvedParams.account);

  if (!user) {
    redirect('/auth/sign-in');
  }

  return (
    <div className="flex flex-1 flex-col space-y-4 pb-36">
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title="Thêm hồ sơ y tế"
        description={
          <AppBreadcrumbs>
            <AppBreadcrumbs.Item href={`/home/<USER>
              {i18n.t('education:breadcrumbs.home')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education`}>
              {i18n.t('education:breadcrumbs.education')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education/health`}>
              {i18n.t('education:breadcrumbs.health')}
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item href={`/home/<USER>/education/health/medical-records`}>
              Hồ sơ y tế
            </AppBreadcrumbs.Item>
            <AppBreadcrumbs.Item>
              Thêm mới
            </AppBreadcrumbs.Item>
          </AppBreadcrumbs>
        }
      />

      <div className="mx-auto w-full max-w-4xl">
        <AddMedicalRecordForm account={resolvedParams.account} />
      </div>
    </div>
  );
}

export default NewMedicalRecordPage;
