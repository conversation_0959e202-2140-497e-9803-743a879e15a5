'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslation } from 'react-i18next';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Textarea } from '@kit/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { toast } from '@kit/ui/sonner';
import { Save, ArrowLeft } from 'lucide-react';

interface Props {
  account: string;
}

const eventSchema = z.object({
  title: z.string().min(1, 'Event title is required'),
  description: z.string().min(1, 'Description is required'),
  event_type: z.enum(['academic', 'extracurricular', 'parent_meeting', 'celebration', 'field_trip', 'other']),
  start_datetime: z.string().min(1, 'Start date and time is required'),
  end_datetime: z.string().min(1, 'End date and time is required'),
  location: z.string().min(1, 'Location is required'),
  max_participants: z.number().min(1, 'Max participants must be at least 1').optional(),
  registration_required: z.boolean(),
});

type EventFormData = z.infer<typeof eventSchema>;

export function AddEventForm({ account }: Props) {
  const { t } = useTranslation('education');
  const router = useRouter();
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();
  
  const [loading, setLoading] = useState(false);

  const currentAccount = accounts?.find(acc => acc.slug === account);

  const form = useForm<EventFormData>({
    resolver: zodResolver(eventSchema),
    defaultValues: {
      title: '',
      description: '',
      event_type: 'academic',
      start_datetime: '',
      end_datetime: '',
      location: '',
      max_participants: 50,
      registration_required: false,
    },
  });

  const onSubmit = async (data: EventFormData) => {
    if (!currentAccount?.id) {
      toast.error('Account not found');
      return;
    }

    try {
      setLoading(true);

      // Use account directly
      const accountId = currentAccount.id;

      // Create event
      const { error: eventError } = await supabase
        .from('events')
        .insert({
          title: data.title,
          description: data.description,
          event_type: data.event_type,
          start_datetime: data.start_datetime,
          end_datetime: data.end_datetime,
          location: data.location,
          max_participants: data.max_participants || null,
          registration_required: data.registration_required,
          account_id: accountId,
        });

      if (eventError) throw eventError;

      toast.success(t('events.addSuccess', 'Event added successfully'));
      router.push(`/home/<USER>/education/events`);
    } catch (error: any) {
      console.error('Error adding event:', error);
      toast.error(error.message || 'Failed to add event');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{t('common.back')}</span>
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Event Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('events.form.eventInfo', 'Event Information')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.form.title', 'Event Title')}</FormLabel>
                      <FormControl>
                        <Input placeholder="Ngày hội thể thao" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="event_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.form.eventType', 'Event Type')}</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select event type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="academic">{t('events.eventType.academic', 'Academic')}</SelectItem>
                          <SelectItem value="extracurricular">{t('events.eventType.extracurricular', 'Extracurricular')}</SelectItem>
                          <SelectItem value="parent_meeting">{t('events.eventType.parent_meeting', 'Parent Meeting')}</SelectItem>
                          <SelectItem value="celebration">{t('events.eventType.celebration', 'Celebration')}</SelectItem>
                          <SelectItem value="field_trip">{t('events.eventType.field_trip', 'Field Trip')}</SelectItem>
                          <SelectItem value="other">{t('events.eventType.other', 'Other')}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.form.location', 'Location')}</FormLabel>
                      <FormControl>
                        <Input placeholder="Sân trường" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="max_participants"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.form.maxParticipants', 'Max Participants')}</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="50" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="start_datetime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.form.startDateTime', 'Start Date & Time')}</FormLabel>
                      <FormControl>
                        <Input type="datetime-local" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="end_datetime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('events.form.endDateTime', 'End Date & Time')}</FormLabel>
                      <FormControl>
                        <Input type="datetime-local" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('events.form.description', 'Description')}</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Mô tả chi tiết về sự kiện..." 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Registration Settings */}
          <Card>
            <CardHeader>
              <CardTitle>{t('events.form.registrationSettings', 'Registration Settings')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="registration_required"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        {t('events.form.registrationRequired', 'Registration Required')}
                      </FormLabel>
                      <div className="text-sm text-muted-foreground">
                        {t('events.form.registrationRequiredDesc', 'Participants must register for this event')}
                      </div>
                    </div>
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="h-4 w-4"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="max_participants"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('events.form.maxParticipants', 'Max Participants')}</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        placeholder="50"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              {t('common.cancel')}
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="h-4 w-4 mr-2" />
              {loading ? t('common.saving') : t('common.save')}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
