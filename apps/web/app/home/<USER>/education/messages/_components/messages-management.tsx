'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import {
  Plus,
  RefreshCw,
  Search,
  Send,
  MessageSquare,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Filter,
  Download,
} from 'lucide-react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Badge } from '@kit/ui/badge';
import { Avatar, AvatarFallback } from '@kit/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface Props {
  account: string;
}

interface Message {
  id: string;
  title: string;
  content: string;
  message_type: string;
  recipient_type: string;
  status: string;
  scheduled_at?: string;
  sent_at?: string;
  created_at: string;
  sender_name: string;
  _count?: {
    recipients: number;
    delivered: number;
    read: number;
  };
}

export function MessagesManagement({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();
  
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  const currentAccount = accounts?.find(acc => acc.slug === account);

  useEffect(() => {
    if (!currentAccount?.id) return;
    loadMessages();
  }, [currentAccount?.id, supabase]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use account directly
      const accountId = currentAccount?.id;
      if (!accountId) {
        setMessages([]);
        return;
      }

      // Get messages from database
      const { data: messagesData, error: messagesError } = await supabase
        .from('messages')
        .select('*')
        .eq('account_id', accountId)
        .order('created_at', { ascending: false });

      if (messagesError) {
        throw new Error(messagesError.message);
      }

      // Transform data to match interface
      const transformedMessages: Message[] = (messagesData || []).map(msg => ({
        id: msg.id,
        title: msg.title,
        content: msg.content,
        message_type: msg.message_type,
        recipient_type: msg.recipient_type,
        status: msg.status,
        priority: msg.priority,
        sent_at: msg.sent_at,
        scheduled_at: msg.scheduled_at,
        created_at: msg.created_at,
        sender_name: msg.sender_name || 'System',
        _count: {
          recipients: 0, // Will be calculated from recipients table
          delivered: 0, // Will be calculated from delivery status
          read: 0, // Will be calculated from read status
        },
      }));

      setMessages(transformedMessages);
    } catch (err: any) {
      console.error('Error loading messages:', err);
      setError(err.message || 'Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const filteredMessages = messages.filter(message => {
    const matchesSearch = message.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.sender_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || message.status === statusFilter;
    const matchesType = typeFilter === 'all' || message.message_type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'scheduled':
        return <Clock className="h-5 w-5 text-blue-600" />;
      case 'draft':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />;
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <MessageSquare className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      sent: { label: t('messages.status.sent', 'Sent'), variant: 'default' as const },
      scheduled: { label: t('messages.status.scheduled', 'Scheduled'), variant: 'secondary' as const },
      draft: { label: t('messages.status.draft', 'Draft'), variant: 'outline' as const },
      failed: { label: t('messages.status.failed', 'Failed'), variant: 'destructive' as const },
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: 'secondary' as const };
    
    return (
      <Badge variant={statusInfo.variant}>
        {statusInfo.label}
      </Badge>
    );
  };

  const getMessageTypeText = (messageType: string) => {
    return t(`messages.messageType.${messageType}`, messageType);
  };

  const getRecipientTypeText = (recipientType: string) => {
    return t(`messages.recipientType.${recipientType}`, recipientType);
  };

  const formatDateTime = (dateTimeString?: string) => {
    if (!dateTimeString) return '-';
    return new Date(dateTimeString).toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const calculateStats = () => {
    const total = messages.length;
    const sent = messages.filter(m => m.status === 'sent').length;
    const scheduled = messages.filter(m => m.status === 'scheduled').length;
    const totalRecipients = messages.reduce((sum, m) => sum + (m._count?.recipients || 0), 0);
    const totalDelivered = messages.reduce((sum, m) => sum + (m._count?.delivered || 0), 0);
    const deliveryRate = totalRecipients > 0 ? Math.round((totalDelivered / totalRecipients) * 100) : 0;

    return { total, sent, scheduled, totalRecipients, deliveryRate };
  };

  const stats = calculateStats();

  if (loading) {
    return <div className="flex justify-center py-8">{t('common.loading')}</div>;
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={loadMessages} variant="outline">
          {t('common.refresh')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-cyan-600 via-blue-600 to-indigo-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <MessageSquare className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Quản lý tin nhắn</h1>
                  <p className="text-white/90 text-lg">Gửi thông báo và liên lạc với phụ huynh</p>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <MessageSquare className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.total}</div>
                      <div className="text-white/80 text-sm">Tổng tin nhắn</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Send className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.sent}</div>
                      <div className="text-white/80 text-sm">Đã gửi</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.scheduled}</div>
                      <div className="text-white/80 text-sm">Đã lên lịch</div>
                    </div>
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Users className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.totalRecipients}</div>
                      <div className="text-white/80 text-sm">Người nhận</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Enhanced Search and Filters */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center gap-3 text-xl">
            <div className="p-2 bg-cyan-100 rounded-lg">
              <Search className="h-5 w-5 text-cyan-600" />
            </div>
            Tìm kiếm và lọc tin nhắn
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Tìm kiếm tin nhắn..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64 h-12 bg-white border-2 border-gray-200 hover:border-cyan-300 transition-colors"
                />
              </div>

              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="h-12 px-4 py-2 bg-white border-2 border-gray-200 hover:border-cyan-300 rounded-md text-sm transition-colors"
              >
                <option value="all">Tất cả trạng thái</option>
                <option value="sent">Đã gửi</option>
                <option value="scheduled">Đã lên lịch</option>
                <option value="draft">Bản nháp</option>
                <option value="failed">Thất bại</option>
              </select>

              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="h-12 px-4 py-2 bg-white border-2 border-gray-200 hover:border-cyan-300 rounded-md text-sm transition-colors"
              >
                <option value="all">Tất cả loại</option>
                <option value="announcement">Thông báo</option>
                <option value="invitation">Lời mời</option>
                <option value="fee_reminder">Nhắc học phí</option>
                <option value="emergency">Khẩn cấp</option>
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <Button onClick={loadMessages} variant="outline" disabled={loading} className="h-12">
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Làm mới
              </Button>
              <Button variant="outline" className="h-12">
                <Download className="h-4 w-4 mr-2" />
                Xuất dữ liệu
              </Button>
              <Button asChild className="h-12 bg-cyan-600 hover:bg-cyan-700 transition-all duration-200">
                <Link href={`/home/<USER>/education/messages/new`}>
                  <Plus className="h-4 w-4 mr-2" />
                  Tin nhắn mới
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>



      {/* Messages Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('messages.title')} ({filteredMessages.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('messages.table.message', 'Message')}</TableHead>
                <TableHead>{t('messages.table.type', 'Type')}</TableHead>
                <TableHead>{t('messages.table.recipients', 'Recipients')}</TableHead>
                <TableHead>{t('messages.table.status', 'Status')}</TableHead>
                <TableHead>{t('messages.table.delivery', 'Delivery')}</TableHead>
                <TableHead>{t('messages.table.datetime', 'Date & Time')}</TableHead>
                <TableHead>{t('messages.table.actions', 'Actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMessages.map((message) => (
                <TableRow key={message.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{message.title}</div>
                      <div className="text-sm text-gray-500 line-clamp-2">
                        {message.content}
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        {t('messages.from', 'From')}: {message.sender_name}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {getMessageTypeText(message.message_type)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">{message._count?.recipients || 0}</span>
                    </div>
                    <div className="text-xs text-gray-500">
                      {getRecipientTypeText(message.recipient_type)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(message.status)}
                      {getStatusBadge(message.status)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {message.status === 'sent' && (
                      <div className="text-sm">
                        <div>{message._count?.delivered || 0}/{message._count?.recipients || 0} {t('messages.delivered', 'delivered')}</div>
                        <div className="text-gray-500">{message._count?.read || 0} {t('messages.read', 'read')}</div>
                      </div>
                    )}
                    {message.status !== 'sent' && <span className="text-gray-400">-</span>}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {message.status === 'sent' && formatDateTime(message.sent_at)}
                      {message.status === 'scheduled' && (
                        <div>
                          <div className="text-blue-600">{t('messages.scheduledFor', 'Scheduled for')}:</div>
                          <div>{formatDateTime(message.scheduled_at)}</div>
                        </div>
                      )}
                      {(message.status === 'draft' || message.status === 'failed') && formatDateTime(message.created_at)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/home/<USER>/education/messages/${message.id}`}>
                          <MessageSquare className="h-4 w-4" />
                        </Link>
                      </Button>
                      {message.status === 'draft' && (
                        <Button variant="ghost" size="sm">
                          <Send className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredMessages.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                ? t('messages.emptyState.noResults', 'No messages found matching the filters.')
                : t('messages.emptyState.noMessages', 'No messages yet. Send your first message!')
              }
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
