'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import {
  Plus,
  Search,
  Eye,
  Edit,
  DollarSign,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  TrendingUp,
  Receipt,
  AlertTriangle,
} from 'lucide-react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Badge } from '@kit/ui/badge';
import { Avatar, AvatarFallback } from '@kit/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { useTranslation } from 'react-i18next';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface Props {
  account: string;
}

interface Fee {
  id: string;
  learner: {
    id: string;
    learner_code: string;
    full_name: string;
    nickname?: string;
  };
  fee_type: string;
  amount: number;
  due_date: string;
  status: string;
  payment_date?: string;
  payment_method?: string;
  notes?: string;
  created_at: string;
}

export function FeeManagement({ account }: Props) {
  const { t } = useTranslation('education');
  const supabase = useSupabase();
  const { accounts } = useTeamAccountWorkspace();
  
  const [fees, setFees] = useState<Fee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const currentAccount = accounts?.find(acc => acc.slug === account);

  useEffect(() => {
    if (!currentAccount?.id) return;
    loadFees();
  }, [currentAccount?.id, supabase]);

  const loadFees = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use account directly
      const accountId = currentAccount?.id;
      if (!accountId) {
        setFees([]);
        return;
      }

      // Get fees first (without join to debug)
      const { data: feesData, error: feesError } = await supabase
        .from('fees')
        .select(`
          id,
          fee_category,
          amount,
          due_date,
          status,
          payment_date,
          payment_method,
          description,
          created_at,
          learner_id
        `)
        .eq('account_id', accountId)
        .order('created_at', { ascending: false });

      if (feesError) throw feesError;

      const processedFees = feesData?.map(fee => ({
        id: fee.id,
        learner: { id: fee.learner_id, full_name: 'Loading...', learner_code: '' }, // Simplified for now
        fee_type: fee.fee_category, // Map fee_category to fee_type for UI compatibility
        amount: fee.amount,
        due_date: fee.due_date,
        status: fee.status,
        payment_date: fee.payment_date,
        payment_method: fee.payment_method,
        notes: fee.description, // Map description to notes for UI compatibility
        created_at: fee.created_at,
      })) || [];

      setFees(processedFees);
    } catch (err: any) {
      console.error('Error loading fees:', err);
      console.error('Error details:', err.details);
      console.error('Error code:', err.code);
      setError(err.message || 'Failed to load fees');
    } finally {
      setLoading(false);
    }
  };

  const filteredFees = fees.filter(fee => {
    const matchesSearch = fee.learner.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         fee.learner.learner_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         fee.fee_type.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || fee.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'overdue':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      paid: { label: t('fees.status.paid'), variant: 'default' as const },
      pending: { label: t('fees.status.pending'), variant: 'secondary' as const },
      overdue: { label: t('fees.status.overdue'), variant: 'destructive' as const },
      cancelled: { label: t('fees.status.cancelled'), variant: 'outline' as const },
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, variant: 'secondary' as const };
    
    return (
      <Badge variant={statusInfo.variant}>
        {statusInfo.label}
      </Badge>
    );
  };

  const getFeeTypeText = (feeType: string) => {
    return t(`fees.feeType.${feeType}`, feeType);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const isOverdue = (dueDate: string, status: string) => {
    return status === 'pending' && new Date(dueDate) < new Date();
  };

  const calculateStats = () => {
    const total = fees.length;
    const paid = fees.filter(f => f.status === 'paid').length;
    const pending = fees.filter(f => f.status === 'pending').length;
    const overdue = fees.filter(f => f.status === 'overdue' || isOverdue(f.due_date, f.status)).length;
    
    const totalAmount = fees.reduce((sum, f) => sum + f.amount, 0);
    const paidAmount = fees.filter(f => f.status === 'paid').reduce((sum, f) => sum + f.amount, 0);
    const pendingAmount = fees.filter(f => f.status === 'pending').reduce((sum, f) => sum + f.amount, 0);

    return { total, paid, pending, overdue, totalAmount, paidAmount, pendingAmount };
  };

  const stats = calculateStats();

  if (loading) {
    return <div className="flex justify-center py-8">{t('common.loading')}</div>;
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={loadFees} variant="outline">
          {t('common.refresh')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Modern Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-600 via-emerald-600 to-teal-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <DollarSign className="h-6 w-6" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">Quản lý học phí</h1>
                  <p className="text-white/90 text-lg">Theo dõi và quản lý thanh toán học phí</p>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Receipt className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.total}</div>
                      <div className="text-white/80 text-sm">Tổng hóa đơn</div>
                    </div>
                  </div>
                  <div className="text-white/70 text-xs mt-1">
                    {formatCurrency(stats.totalAmount)}
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.paid}</div>
                      <div className="text-white/80 text-sm">Đã thanh toán</div>
                    </div>
                  </div>
                  <div className="text-white/70 text-xs mt-1">
                    {formatCurrency(stats.paidAmount)}
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.pending}</div>
                      <div className="text-white/80 text-sm">Chờ thanh toán</div>
                    </div>
                  </div>
                  <div className="text-white/70 text-xs mt-1">
                    {formatCurrency(stats.pendingAmount)}
                  </div>
                </div>
                <div className="p-4 bg-white/20 rounded-xl backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-5 w-5" />
                    <div>
                      <div className="text-2xl font-bold">{stats.overdue}</div>
                      <div className="text-white/80 text-sm">Quá hạn</div>
                    </div>
                  </div>
                  <div className="text-white/70 text-xs mt-1">
                    Cần xử lý ngay
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      {/* Enhanced Search and Filters */}
      <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center gap-3 text-xl">
            <div className="p-2 bg-green-100 rounded-lg">
              <Search className="h-5 w-5 text-green-600" />
            </div>
            Tìm kiếm và lọc học phí
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Tìm kiếm theo tên học viên..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64 h-12 bg-white border-2 border-gray-200 hover:border-green-300 transition-colors"
                />
              </div>

              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="h-12 px-4 py-2 bg-white border-2 border-gray-200 hover:border-green-300 rounded-md text-sm transition-colors"
              >
                <option value="all">Tất cả trạng thái</option>
                <option value="pending">Chờ thanh toán</option>
                <option value="paid">Đã thanh toán</option>
                <option value="overdue">Quá hạn</option>
                <option value="cancelled">Đã hủy</option>
              </select>
            </div>

            <Button asChild className="h-12 bg-green-600 hover:bg-green-700 transition-all duration-200">
              <Link href={`/home/<USER>/education/fees/new`}>
                <Plus className="h-4 w-4 mr-2" />
                Thêm học phí mới
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Fees Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('fees.title')} ({filteredFees.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('fees.table.learner')}</TableHead>
                <TableHead>{t('fees.table.feeType')}</TableHead>
                <TableHead>{t('fees.table.amount')}</TableHead>
                <TableHead>{t('fees.table.dueDate')}</TableHead>
                <TableHead>{t('fees.table.status')}</TableHead>
                <TableHead>{t('fees.table.paymentDate')}</TableHead>
                <TableHead>{t('fees.table.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredFees.map((fee) => (
                <TableRow key={fee.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarFallback>
                          {fee.learner.full_name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{fee.learner.full_name}</div>
                        <div className="text-sm text-gray-500">{fee.learner.learner_code}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-gray-400" />
                      <span>{getFeeTypeText(fee.fee_type)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{formatCurrency(fee.amount)}</div>
                  </TableCell>
                  <TableCell>
                    <div className={`flex items-center space-x-1 ${isOverdue(fee.due_date, fee.status) ? 'text-red-600' : ''}`}>
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(fee.due_date)}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(fee.status)}
                      {getStatusBadge(fee.status)}
                    </div>
                  </TableCell>
                  <TableCell>
                    {fee.payment_date ? formatDate(fee.payment_date) : '-'}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/home/<USER>/education/fees/${fee.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/home/<USER>/education/fees/${fee.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredFees.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || statusFilter !== 'all' 
                ? t('fees.emptyState.noResults')
                : t('fees.emptyState.noFees')
              }
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
