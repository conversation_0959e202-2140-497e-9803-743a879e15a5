'use client';

import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Users, 
  TrendingUp, 
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  MoreHorizontal,
  Star,
  Mail,
  Phone,
  MapPin,
  Calendar,
  DollarSign,
  Activity,
  Target,
  Zap,
  AlertTriangle,
  CheckCircle2,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Sparkles
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Input } from '@kit/ui/input';
import { Badge } from '@kit/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { Progress } from '@kit/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';

interface CustomerProfile {
  id: string;
  primary_email?: string;
  primary_phone?: string;
  first_name?: string;
  last_name?: string;
  lifecycle_stage: string;
  customer_value_tier: string;
  behavior: {
    total_sessions: number;
    total_pageviews: number;
    total_purchases: number;
    total_revenue: number;
  };
  engagement_scores: {
    overall_engagement_score: number;
  };
  predictive_scores: {
    churn_risk_score: number;
    lifetime_value_score: number;
  };
  created_at: string;
  updated_at: string;
}

interface ModernProfilesDashboardProps {
  accountId: string;
}

export function ModernProfilesDashboard({ accountId }: ModernProfilesDashboardProps) {
  const { t } = useTranslation(['cdp', 'common']);
  const [profiles, setProfiles] = useState<CustomerProfile[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Mock data for demonstration
  const mockProfiles: CustomerProfile[] = [
    {
      id: '1',
      primary_email: '<EMAIL>',
      first_name: 'John',
      last_name: 'Doe',
      primary_phone: '+***********',
      lifecycle_stage: 'customer',
      customer_value_tier: 'high_value',
      behavior: {
        total_sessions: 45,
        total_pageviews: 234,
        total_purchases: 8,
        total_revenue: 2450000
      },
      engagement_scores: {
        overall_engagement_score: 0.85
      },
      predictive_scores: {
        churn_risk_score: 0.15,
        lifetime_value_score: 0.92
      },
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-03-20T14:22:00Z'
    },
    {
      id: '2',
      primary_email: '<EMAIL>',
      first_name: 'Jane',
      last_name: 'Smith',
      lifecycle_stage: 'lead',
      customer_value_tier: 'medium_value',
      behavior: {
        total_sessions: 12,
        total_pageviews: 67,
        total_purchases: 2,
        total_revenue: 890000
      },
      engagement_scores: {
        overall_engagement_score: 0.62
      },
      predictive_scores: {
        churn_risk_score: 0.45,
        lifetime_value_score: 0.68
      },
      created_at: '2024-02-10T09:15:00Z',
      updated_at: '2024-03-19T16:45:00Z'
    },
    {
      id: '3',
      primary_email: '<EMAIL>',
      first_name: 'Mike',
      last_name: 'Wilson',
      lifecycle_stage: 'prospect',
      customer_value_tier: 'low_value',
      behavior: {
        total_sessions: 3,
        total_pageviews: 15,
        total_purchases: 0,
        total_revenue: 0
      },
      engagement_scores: {
        overall_engagement_score: 0.28
      },
      predictive_scores: {
        churn_risk_score: 0.78,
        lifetime_value_score: 0.34
      },
      created_at: '2024-03-05T11:20:00Z',
      updated_at: '2024-03-18T13:30:00Z'
    }
  ];

  useEffect(() => {
    setProfiles(mockProfiles);
  }, []);

  const getLifecycleColor = (stage: string) => {
    switch (stage) {
      case 'customer': return 'bg-green-500';
      case 'lead': return 'bg-blue-500';
      case 'prospect': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getValueTierColor = (tier: string) => {
    switch (tier) {
      case 'high_value': return 'text-green-600 bg-green-50 border-green-200';
      case 'medium_value': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'low_value': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getChurnRiskColor = (score: number) => {
    if (score > 0.7) return 'text-red-600 bg-red-50';
    if (score > 0.4) return 'text-yellow-600 bg-yellow-50';
    return 'text-green-600 bg-green-50';
  };

  const getEngagementIcon = (score: number) => {
    if (score > 0.8) return <Sparkles className="h-4 w-4 text-green-500" />;
    if (score > 0.6) return <CheckCircle2 className="h-4 w-4 text-blue-500" />;
    if (score > 0.4) return <Clock className="h-4 w-4 text-yellow-500" />;
    return <AlertTriangle className="h-4 w-4 text-red-500" />;
  };

  const filteredProfiles = profiles.filter(profile => {
    const matchesSearch = !searchTerm || 
      profile.primary_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      profile.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      profile.last_name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = selectedFilter === 'all' || profile.lifecycle_stage === selectedFilter;
    
    return matchesSearch && matchesFilter;
  });

  const stats = {
    total: profiles.length,
    customers: profiles.filter(p => p.lifecycle_stage === 'customer').length,
    leads: profiles.filter(p => p.lifecycle_stage === 'lead').length,
    prospects: profiles.filter(p => p.lifecycle_stage === 'prospect').length,
    highValue: profiles.filter(p => p.customer_value_tier === 'high_value').length,
    highRisk: profiles.filter(p => p.predictive_scores.churn_risk_score > 0.7).length
  };

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">Customer Profiles</h1>
          <p className="text-muted-foreground">
            Unified customer data and behavioral insights
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Profile
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Profiles</p>
                <p className="text-2xl font-bold text-blue-700 dark:text-blue-300">{stats.total}</p>
              </div>
              <div className="p-3 bg-blue-500 rounded-xl">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">+12% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">Active Customers</p>
                <p className="text-2xl font-bold text-green-700 dark:text-green-300">{stats.customers}</p>
              </div>
              <div className="p-3 bg-green-500 rounded-xl">
                <CheckCircle2 className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">+8% conversion rate</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600 dark:text-purple-400">High Value</p>
                <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">{stats.highValue}</p>
              </div>
              <div className="p-3 bg-purple-500 rounded-xl">
                <Star className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-600">Premium segment</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600 dark:text-orange-400">At Risk</p>
                <p className="text-2xl font-bold text-orange-700 dark:text-orange-300">{stats.highRisk}</p>
              </div>
              <div className="p-3 bg-orange-500 rounded-xl">
                <AlertTriangle className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <ArrowDownRight className="h-4 w-4 text-red-500 mr-1" />
              <span className="text-red-600">Needs attention</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search customers by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex items-center gap-3">
              <Tabs value={selectedFilter} onValueChange={setSelectedFilter}>
                <TabsList>
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="customer">Customers</TabsTrigger>
                  <TabsTrigger value="lead">Leads</TabsTrigger>
                  <TabsTrigger value="prospect">Prospects</TabsTrigger>
                </TabsList>
              </Tabs>
              
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                More Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Profiles Grid */}
      <div className="grid gap-6 lg:grid-cols-2 xl:grid-cols-3">
        {filteredProfiles.map((profile) => (
          <Card key={profile.id} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <CardContent className="p-6">
              {/* Profile Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${profile.primary_email}`} />
                    <AvatarFallback>
                      {profile.first_name?.[0]}{profile.last_name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold text-lg">
                      {profile.first_name && profile.last_name
                        ? `${profile.first_name} ${profile.last_name}`
                        : profile.primary_email}
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <div className={`w-2 h-2 rounded-full ${getLifecycleColor(profile.lifecycle_stage)}`} />
                      <span className="text-sm text-muted-foreground capitalize">
                        {profile.lifecycle_stage}
                      </span>
                    </div>
                  </div>
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem>
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Profile
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Contact Info */}
              <div className="space-y-2 mb-4">
                {profile.primary_email && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Mail className="h-4 w-4" />
                    <span className="truncate">{profile.primary_email}</span>
                  </div>
                )}
                {profile.primary_phone && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Phone className="h-4 w-4" />
                    <span>{profile.primary_phone}</span>
                  </div>
                )}
              </div>

              {/* Value Tier & Engagement */}
              <div className="flex items-center justify-between mb-4">
                <Badge className={`${getValueTierColor(profile.customer_value_tier)} border`}>
                  {profile.customer_value_tier.replace('_', ' ')}
                </Badge>
                
                <div className="flex items-center gap-1">
                  {getEngagementIcon(profile.engagement_scores.overall_engagement_score)}
                  <span className="text-sm font-medium">
                    {(profile.engagement_scores.overall_engagement_score * 100).toFixed(0)}%
                  </span>
                </div>
              </div>

              {/* Metrics */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className="text-lg font-bold text-green-600">
                    {new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND',
                      notation: 'compact'
                    }).format(profile.behavior.total_revenue)}
                  </div>
                  <div className="text-xs text-muted-foreground">Revenue</div>
                </div>
                
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className="text-lg font-bold text-blue-600">
                    {profile.behavior.total_purchases}
                  </div>
                  <div className="text-xs text-muted-foreground">Orders</div>
                </div>
              </div>

              {/* Churn Risk */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Churn Risk</span>
                  <span className={`text-xs px-2 py-1 rounded-full ${getChurnRiskColor(profile.predictive_scores.churn_risk_score)}`}>
                    {(profile.predictive_scores.churn_risk_score * 100).toFixed(0)}%
                  </span>
                </div>
                <Progress 
                  value={profile.predictive_scores.churn_risk_score * 100} 
                  className="h-2"
                />
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </Button>
                <Button size="sm" className="flex-1">
                  <Target className="h-4 w-4 mr-2" />
                  Engage
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredProfiles.length === 0 && (
        <Card className="border-0 shadow-lg">
          <CardContent className="p-12 text-center">
            <Users className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">No profiles found</h3>
            <p className="text-muted-foreground mb-6">
              {searchTerm ? 'Try adjusting your search terms' : 'Get started by creating your first customer profile'}
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Profile
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
