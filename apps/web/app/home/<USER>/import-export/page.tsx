import Link from 'next/link';

import {
  ArrowDownToLine,
  ArrowUpFromLine,
  Database,
  FileDown,
} from 'lucide-react';

import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Badge } from '@kit/ui/badge';
import { But<PERSON> } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { withI18n } from '~/lib/i18n/with-i18n';

import { getTemplates } from './_lib/server/export-data';
import { ExportTemplateList } from './_components/export-template-list';

interface ImportExportPageProps {
  params: Promise<{
    account: string;
  }>;
}

/**
 * Trang Import/Export
 */
async function ImportExportPage({ params }: ImportExportPageProps) {
  const { account: accountSlug } = await params;

  try {
    // Lấy thông tin workspace
    const { account } = await loadTeamWorkspace(accountSlug);

    if (!account) {
      return (
        <PageBody>
          <div className="text-destructive">Team not found</div>
        </PageBody>
      );
    }

    // Lấy danh sách templates cho các resource khác nhau
    let productTemplates = [];
    let categoryTemplates = [];
    let orderTemplates = [];
    let customerTemplates = [];
    
    try {
      productTemplates = await getTemplates('products', account.id);
    } catch (error) {
      console.error('Error getting product templates:', error);
    }
    
    try {
      categoryTemplates = await getTemplates('categories', account.id);
    } catch (error) {
      console.error('Error getting category templates:', error);
    }
    
    try {
      orderTemplates = await getTemplates('orders', account.id);
    } catch (error) {
      console.error('Error getting order templates:', error);
    }
    
    try {
      customerTemplates = await getTemplates('customers', account.id);
    } catch (error) {
      console.error('Error getting customer templates:', error);
    }

    return (
      <PageBody>
        <AppBreadcrumbs
          items={[
            {
              title: <Trans i18nKey="common:home">Home</Trans>,
              href: `/home/<USER>
            },
            {
              title: <Trans i18nKey="import-export:title">Import/Export</Trans>,
              href: `/home/<USER>/import-export`,
            },
          ]}
        />

        <div className="mx-auto max-w-7xl">
          <div className="mb-8 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Database className="text-primary h-5 w-5" />
                <h1 className="text-2xl font-bold tracking-tight">
                  <Trans i18nKey="import-export:main_title">Import/Export Data</Trans>
                </h1>
              </div>
              <p className="text-muted-foreground">
                <Trans i18nKey="import-export:main_description">Import and export your data using CSV or Excel files</Trans>
              </p>
            </div>
          </div>

          <div className="space-y-12">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <Card className="overflow-hidden">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-4">
                  <ArrowUpFromLine className="h-8 w-8 text-white" />
                </div>
                <CardContent className="space-y-4 p-6">
                  <div>
                    <h2 className="mb-2 text-xl font-semibold">
                      <Trans i18nKey="import-export:export_card_title">
                        Export Data
                      </Trans>
                    </h2>
                    <p className="text-muted-foreground">
                      <Trans i18nKey="import-export:export_card_description">
                        Export your data to CSV or Excel files. You can select
                        specific fields and apply filters.
                      </Trans>
                    </p>
                  </div>
                  <Button asChild className="w-full">
                    <Link href={`/home/<USER>/import-export/export`}>
                      <Trans i18nKey="import-export:go_to_export">
                        Go to Export
                      </Trans>
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              <Card className="overflow-hidden">
                <div className="bg-gradient-to-r from-green-500 to-green-600 p-4">
                  <ArrowDownToLine className="h-8 w-8 text-white" />
                </div>
                <CardContent className="space-y-4 p-6">
                  <div>
                    <h2 className="mb-2 text-xl font-semibold">
                      <Trans i18nKey="import-export:import_card_title">
                        Import Data
                      </Trans>
                    </h2>
                    <p className="text-muted-foreground">
                      <Trans i18nKey="import-export:import_card_description">
                        Import your data from CSV or Excel files. Map fields
                        from your file to database fields.
                      </Trans>
                    </p>
                  </div>
                  <Button asChild className="w-full">
                    <Link href={`/home/<USER>/import-export/import`}>
                      <Trans i18nKey="import-export:go_to_import">
                        Go to Import
                      </Trans>
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
            
            {/* Export Templates Section */}
            <div>
              <div className="mb-6">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <FileDown className="h-5 w-5 text-primary" />
                  <Trans i18nKey="import-export:saved_templates">Saved Export Templates</Trans>
                </h2>
                <p className="text-sm text-muted-foreground">
                  <Trans i18nKey="import-export:saved_templates_description">
                    Click on a template to export data immediately as CSV
                  </Trans>
                </p>
              </div>
              
              {/* Products Templates */}
              {productTemplates.length > 0 && (
                <div className="space-y-4 mb-8">
                  <h3 className="text-lg font-semibold flex items-center">
                    <Trans i18nKey="import-export:products">Products</Trans>
                    <Badge variant="outline" className="ml-2">
                      {productTemplates.length}
                    </Badge>
                  </h3>
                  
                  <ExportTemplateList 
                    templates={productTemplates} 
                    accountId={account.id} 
                    accountSlug={accountSlug} 
                    resourceType="products" 
                  />
                </div>
              )}
              
              {/* Categories Templates */}
              {categoryTemplates.length > 0 && (
                <div className="space-y-4 mb-8">
                  <h3 className="text-lg font-semibold flex items-center">
                    <Trans i18nKey="import-export:categories">Categories</Trans>
                    <Badge variant="outline" className="ml-2">
                      {categoryTemplates.length}
                    </Badge>
                  </h3>
                  
                  <ExportTemplateList 
                    templates={categoryTemplates} 
                    accountId={account.id} 
                    accountSlug={accountSlug} 
                    resourceType="categories" 
                  />
                </div>
              )}
              
              {/* Orders Templates */}
              {orderTemplates.length > 0 && (
                <div className="space-y-4 mb-8">
                  <h3 className="text-lg font-semibold flex items-center">
                    <Trans i18nKey="import-export:orders">Orders</Trans>
                    <Badge variant="outline" className="ml-2">
                      {orderTemplates.length}
                    </Badge>
                  </h3>
                  
                  <ExportTemplateList 
                    templates={orderTemplates} 
                    accountId={account.id} 
                    accountSlug={accountSlug} 
                    resourceType="orders" 
                  />
                </div>
              )}
              
              {/* Customers Templates */}
              {customerTemplates.length > 0 && (
                <div className="space-y-4 mb-8">
                  <h3 className="text-lg font-semibold flex items-center">
                    <Trans i18nKey="import-export:customers">Customers</Trans>
                    <Badge variant="outline" className="ml-2">
                      {customerTemplates.length}
                    </Badge>
                  </h3>
                  
                  <ExportTemplateList 
                    templates={customerTemplates} 
                    accountId={account.id} 
                    accountSlug={accountSlug} 
                    resourceType="customers" 
                  />
                </div>
              )}
              
              {/* No Templates */}
              {productTemplates.length === 0 && 
               categoryTemplates.length === 0 && 
               orderTemplates.length === 0 && 
               customerTemplates.length === 0 && (
                <Card className="col-span-full">
                  <CardContent className="p-6 text-center">
                    <div className="flex flex-col items-center gap-2">
                      <div className="rounded-full bg-muted p-3">
                        <FileDown className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <h3 className="font-medium">
                        <Trans i18nKey="import-export:no_templates">No templates found</Trans>
                      </h3>
                      <p className="text-sm text-muted-foreground max-w-md">
                        <Trans i18nKey="import-export:create_template_description">
                          Create a template by going to the export page and saving your export configuration.
                        </Trans>
                      </p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-2"
                        asChild
                      >
                        <Link href={`/home/<USER>/import-export/export`}>
                          <Trans i18nKey="import-export:create_template">Create Template</Trans>
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </PageBody>
    );
  } catch (error: any) {
    console.error('Error loading import/export page:', {
      error: error.message,
      params,
      context: 'import-export.page',
    });

    return (
      <PageBody>
        <div className="text-destructive">
          Failed to load data. Please try again later.
        </div>
      </PageBody>
    );
  }
}

export default withI18n(ImportExportPage);

// Metadata
export const metadata = {
  title: 'Import/Export Data',
};
