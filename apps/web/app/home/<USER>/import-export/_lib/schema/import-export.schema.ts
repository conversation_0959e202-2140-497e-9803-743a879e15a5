import { z } from 'zod';

// Schema cho export
export const ExportSchema = z.object({
  resource: z.enum(['products', 'categories', 'orders', 'customers', 'vouchers', 'analytics']),
  fields: z.array(z.string()),
});

export type ExportData = z.infer<typeof ExportSchema>;

// Schema cho import
export const ImportSchema = z.object({
  resource: z.enum(['products', 'categories', 'orders', 'customers', 'vouchers', 'analytics']),
  mapping: z.record(z.string(), z.string()),
  data: z.array(z.record(z.string(), z.any())),
});

export type ImportData = z.infer<typeof ImportSchema>;

// Schema cho file upload
export const FileUploadSchema = z.object({
  file: z.instanceof(File),
});

export type FileUploadData = z.infer<typeof FileUploadSchema>;
