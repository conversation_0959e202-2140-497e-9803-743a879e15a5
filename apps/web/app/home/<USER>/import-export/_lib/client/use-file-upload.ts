'use client';

import { useCallback, useState } from 'react';
import <PERSON> from 'papapar<PERSON>';
import * as XLSX from 'xlsx';

type FileType = 'csv' | 'excel';

interface FileProcessingOptions {
  batchSize?: number;
  maxPreviewRows?: number;
  onProgress?: (progress: number) => void;
}

/**
 * Hook xử lý upload file
 * @returns Các state và handlers
 */
export function useFileUpload() {
  const [file, setFile] = useState<File | null>(null);
  const [fileData, setFileData] = useState<Record<string, any>[]>([]);
  const [headers, setHeaders] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fileType, setFileType] = useState<FileType>('csv');
  const [totalRows, setTotalRows] = useState<number>(0);
  const [progress, setProgress] = useState<number>(0);
  const [availableSheets, setAvailableSheets] = useState<string[]>([]);
  const [selectedSheet, setSelectedSheet] = useState<string>('');

  /**
   * Xử lý khi chọn file
   */
  const handleFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    options: FileProcessingOptions = {}
  ) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    const {
      batchSize = 1000,
      maxPreviewRows = 5000,
      onProgress
    } = options;

    setFile(selectedFile);
    setIsLoading(true);
    setError(null);
    setProgress(0);

    try {
      // Xác định loại file
      const type = getFileType(selectedFile);
      setFileType(type);

      if (type === 'csv') {
        // Kiểm tra nội dung file CSV trước khi xử lý
        const isValidCsv = await checkCsvContent(selectedFile);
        if (!isValidCsv) {
          setError('Invalid CSV file: File is empty or has invalid format');
          setIsLoading(false);
          return;
        }

        // Đọc file như text trước, sau đó parse
        const reader = new FileReader();
        reader.onload = (e) => {
          const csvContent = e.target?.result as string;
          if (!csvContent || csvContent.trim() === '') {
            setError('Error: Empty CSV file');
            setIsLoading(false);
            return;
          }

          console.log('CSV content (first 100 chars):', csvContent.substring(0, 100));

          // Parse CSV file
          Papa.parse(csvContent, {
            header: true,
            skipEmptyLines: true,
            delimiter: ',',
            quoteChar: '"',
            complete: (results) => {
              console.log('Parse results:', results);

              if (!results || !results.data || !Array.isArray(results.data) || results.data.length === 0) {
                console.error('CSV parse error: No data found', results);
                setError('Error parsing CSV: No data found or empty file');
                setIsLoading(false);
                return;
              }

              const data = results.data as Record<string, any>[];
              const headers = results.meta?.fields || [];

              console.log('Parsed data:', { headers, dataLength: data.length, firstRow: data[0] });

              // Giới hạn số lượng dòng để preview
              const previewData = data.slice(0, maxPreviewRows);

              setFileData(previewData);
              setHeaders(headers);
              setTotalRows(data.length);
              setIsLoading(false);
              setProgress(100);
              if (onProgress) onProgress(100);
            },
            error: (error) => {
              console.error('Papa parse error:', error);
              setError(`Error parsing CSV: ${error.message}`);
              setIsLoading(false);
            },
          });
        };

        reader.onerror = (error) => {
          console.error('FileReader error:', error);
          setError('Error reading file');
          setIsLoading(false);
        };

        // Đọc file như text
        reader.readAsText(selectedFile);
      } else if (type === 'excel') {
        // Parse Excel file
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = e.target?.result;
            if (!data) {
              throw new Error('Failed to read file');
            }

            // Parse Excel data
            const workbook = XLSX.read(data, { type: 'array' });
            const sheetNames = workbook.SheetNames;

            // Lưu danh sách sheets
            setAvailableSheets(sheetNames);

            // Chọn sheet đầu tiên mặc định
            const sheetName = sheetNames[0];
            setSelectedSheet(sheetName);

            // Xử lý dữ liệu từ sheet đầu tiên
            processExcelSheet(workbook, sheetName, maxPreviewRows);

            setIsLoading(false);
            setProgress(100);
            if (onProgress) onProgress(100);
          } catch (error: any) {
            setError(`Error parsing Excel: ${error.message}`);
            setIsLoading(false);
          }
        };

        reader.onprogress = (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setProgress(progress);
            if (onProgress) onProgress(progress);
          }
        };

        reader.onerror = () => {
          setError('Error reading file');
          setIsLoading(false);
        };

        reader.readAsArrayBuffer(selectedFile);
      } else {
        setError('Unsupported file format. Please use CSV or Excel files.');
        setIsLoading(false);
      }
    } catch (error: any) {
      setError(`Error processing file: ${error.message}`);
      setIsLoading(false);
    }
  };

  /**
   * Xử lý dữ liệu từ một sheet Excel
   */
  const processExcelSheet = useCallback((
    workbook: XLSX.WorkBook,
    sheetName: string,
    maxRows: number = 5000
  ) => {
    const worksheet = workbook.Sheets[sheetName];

    if (!worksheet) {
      console.error('Worksheet not found:', sheetName);
      setError(`Worksheet "${sheetName}" not found in Excel file`);
      return;
    }

    // Convert to JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    // Extract headers and data
    if (!jsonData || !Array.isArray(jsonData) || jsonData.length === 0) {
      console.error('No data found in worksheet:', sheetName);
      setError(`No data found in worksheet "${sheetName}"`);
      return;
    }

    if (jsonData.length > 0) {
      const headers = jsonData[0] as string[];

      // Giới hạn số lượng dòng để preview
      const rowsToProcess = Math.min(jsonData.length - 1, maxRows);
      const rows = jsonData.slice(1, rowsToProcess + 1).map((row: any) => {
        const obj: Record<string, any> = {};
        headers.forEach((header, index) => {
          obj[header] = row[index];
        });
        return obj;
      });

      setHeaders(headers);
      setFileData(rows);
      setTotalRows(jsonData.length - 1); // Trừ đi dòng header
    }
  }, []);

  /**
   * Chọn sheet khác trong file Excel
   */
  const changeSheet = useCallback((sheetName: string) => {
    if (!file || fileType !== 'excel' || !availableSheets.includes(sheetName)) {
      return;
    }

    setIsLoading(true);
    setSelectedSheet(sheetName);

    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const data = e.target?.result;
        if (!data) {
          throw new Error('Failed to read file');
        }

        const workbook = XLSX.read(data, { type: 'array' });
        processExcelSheet(workbook, sheetName);
        setIsLoading(false);
      };

      reader.onerror = () => {
        setError('Error reading file');
        setIsLoading(false);
      };

      reader.readAsArrayBuffer(file);
    } catch (error: any) {
      setError(`Error processing Excel sheet: ${error.message}`);
      setIsLoading(false);
    }
  }, [file, fileType, availableSheets, processExcelSheet]);

  /**
   * Xác định loại file
   */
  const getFileType = (file: File): FileType => {
    const extension = file.name.split('.').pop()?.toLowerCase();

    if (extension === 'csv') {
      return 'csv';
    } else if (['xls', 'xlsx'].includes(extension || '')) {
      return 'excel';
    }

    // Mặc định là CSV
    return 'csv';
  };

  /**
   * Kiểm tra nội dung file CSV
   */
  const checkCsvContent = async (file: File): Promise<boolean> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (!content) {
          resolve(false);
          return;
        }

        // Kiểm tra xem file có nội dung không
        if (content.trim() === '') {
          resolve(false);
          return;
        }

        // Kiểm tra xem file có ít nhất một dòng và một dấu phẩy không
        const hasComma = content.includes(',');
        const hasNewline = content.includes('\n');

        resolve(hasComma && hasNewline);
      };
      reader.onerror = () => resolve(false);
      reader.readAsText(file);
    });
  };

  /**
   * Reset state
   */
  const resetFileUpload = () => {
    setFile(null);
    setFileData([]);
    setHeaders([]);
    setIsLoading(false);
    setError(null);
    setTotalRows(0);
    setProgress(0);
    setAvailableSheets([]);
    setSelectedSheet('');
  };

  /**
   * Lấy dữ liệu theo batch
   */
  const getDataBatch = (batchSize: number = 100, batchIndex: number = 0) => {
    const start = batchIndex * batchSize;
    const end = start + batchSize;
    return fileData.slice(start, end);
  };

  /**
   * Xử lý file lớn theo batch
   */
  const processBatches = async (
    processFn: (batch: Record<string, any>[], batchIndex: number) => Promise<void>,
    options: { batchSize?: number; onProgress?: (progress: number) => void } = {}
  ) => {
    const { batchSize = 1000, onProgress } = options;

    if (!file || fileData.length === 0) {
      return;
    }

    setIsLoading(true);
    setProgress(0);

    try {
      // Tính toán số lượng batch
      const totalBatches = Math.ceil(totalRows / batchSize);

      // Nếu file quá lớn, cần xử lý lại từ đầu
      if (fileData.length < totalRows) {
        // Xử lý lại file theo từng batch
        if (fileType === 'csv') {
          let currentBatch = 0;
          let processedRows = 0;

          await new Promise<void>((resolve, reject) => {
            // Đọc file như text trước
            const reader = new FileReader();

            reader.onload = async (e) => {
              try {
                const csvContent = e.target?.result as string;
                if (!csvContent || csvContent.trim() === '') {
                  reject(new Error('Empty CSV file'));
                  return;
                }

                console.log('Processing large CSV file...');

                // Parse toàn bộ file trước
                Papa.parse(csvContent, {
                  header: true,
                  skipEmptyLines: true,
                  delimiter: ',',
                  quoteChar: '"',
                  complete: async (results) => {
                    if (!results || !results.data || !Array.isArray(results.data)) {
                      reject(new Error('Error parsing CSV: No data found'));
                      return;
                    }

                    const allData = results.data as Record<string, any>[];
                    const totalItems = allData.length;
                    console.log(`CSV parsed successfully. Total rows: ${totalItems}`);

                    // Xử lý từng batch
                    const totalBatches = Math.ceil(totalItems / batchSize);

                    for (let i = 0; i < totalBatches; i++) {
                      const start = i * batchSize;
                      const end = Math.min(start + batchSize, totalItems);
                      const batchData = allData.slice(start, end);

                      try {
                        await processFn(batchData, i);
                        processedRows += batchData.length;

                        // Cập nhật progress
                        const progress = Math.min(Math.round((processedRows / totalItems) * 100), 99);
                        setProgress(progress);
                        if (onProgress) onProgress(progress);
                      } catch (error) {
                        console.error(`Error processing batch ${i}:`, error);
                        // Tiếp tục xử lý các batch khác
                      }
                    }

                    setProgress(100);
                    if (onProgress) onProgress(100);
                    resolve();
                  },
                  error: (error) => {
                    console.error('Error parsing CSV in batch mode:', error);
                    reject(error);
                  }
                });
              } catch (error) {
                console.error('Error in CSV batch processing:', error);
                reject(error);
              }
            };

            reader.onerror = (error) => {
              console.error('FileReader error in batch mode:', error);
              reject(new Error('Error reading file'));
            };

            reader.readAsText(file);
          });
        } else if (fileType === 'excel') {
          // Xử lý Excel file theo batch
          const reader = new FileReader();

          await new Promise<void>((resolve, reject) => {
            reader.onload = async (e) => {
              try {
                const data = e.target?.result;
                if (!data) {
                  throw new Error('Failed to read file');
                }

                const workbook = XLSX.read(data, { type: 'array' });
                if (!workbook || !workbook.SheetNames || workbook.SheetNames.length === 0) {
                  throw new Error('Invalid Excel file: No sheets found');
                }

                const sheetToUse = selectedSheet || workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetToUse];

                if (!worksheet) {
                  throw new Error(`Worksheet "${sheetToUse}" not found in Excel file`);
                }

                // Convert to JSON
                const jsonData = XLSX.utils.sheet_to_json(worksheet);

                if (!jsonData || !Array.isArray(jsonData) || jsonData.length === 0) {
                  throw new Error(`No data found in worksheet "${sheetToUse}"`);
                }

                // Xử lý từng batch
                for (let i = 0; i < totalBatches; i++) {
                  const start = i * batchSize;
                  const end = Math.min(start + batchSize, jsonData.length);
                  const batch = jsonData.slice(start, end) as Record<string, any>[];

                  await processFn(batch, i);

                  // Cập nhật progress
                  const progress = Math.min(Math.round(((i + 1) * batchSize / totalRows) * 100), 99);
                  setProgress(progress);
                  if (onProgress) onProgress(progress);
                }

                setProgress(100);
                if (onProgress) onProgress(100);
                resolve();
              } catch (error) {
                reject(error);
              }
            };

            reader.onerror = () => {
              reject(new Error('Error reading file'));
            };

            reader.readAsArrayBuffer(file);
          });
        }
      } else {
        // Nếu tất cả dữ liệu đã được load, xử lý từ memory
        for (let i = 0; i < totalBatches; i++) {
          const batch = getDataBatch(batchSize, i);
          await processFn(batch, i);

          // Cập nhật progress
          const progress = Math.min(Math.round(((i + 1) * batchSize / totalRows) * 100), 99);
          setProgress(progress);
          if (onProgress) onProgress(progress);
        }

        setProgress(100);
        if (onProgress) onProgress(100);
      }
    } catch (error: any) {
      setError(`Error processing batches: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    file,
    fileData,
    headers,
    isLoading,
    error,
    fileType,
    totalRows,
    progress,
    availableSheets,
    selectedSheet,
    handleFileChange,
    resetFileUpload,
    getDataBatch,
    processBatches,
    changeSheet,
  };
}
