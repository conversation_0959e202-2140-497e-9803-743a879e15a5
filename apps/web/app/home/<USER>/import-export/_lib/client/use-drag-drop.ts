'use client';

import { useState } from 'react';
import { DraggableLocation } from '@hello-pangea/dnd';

/**
 * Hook xử lý drag-drop giữa hai danh sách
 * @param initialSource Danh sách nguồn ban đầu
 * @param initialDestination Danh sách đích ban đầu
 * @returns Các state và handlers
 */
export function useDragDrop<T>(
  initialSource: T[],
  initialDestination: T[] = [],
) {
  const [sourceItems, setSourceItems] = useState<T[]>(initialSource);
  const [destinationItems, setDestinationItems] = useState<T[]>(initialDestination);

  /**
   * Xử lý khi kết thúc drag-drop
   */
  const onDragEnd = (result: {
    source: DraggableLocation;
    destination?: DraggableLocation;
  }) => {
    const { source, destination } = result;

    // Nếu không có destination hoặc destination giống source, không làm gì cả
    if (!destination) {
      return;
    }

    // Xử lý kéo thả trong cùng một danh sách
    if (source.droppableId === destination.droppableId) {
      const items =
        source.droppableId === 'source' ? sourceItems : destinationItems;
      const reordered = reorderItems(items, source.index, destination.index);

      if (source.droppableId === 'source') {
        setSourceItems(reordered);
      } else {
        setDestinationItems(reordered);
      }
    } else {
      // Xử lý kéo thả giữa hai danh sách
      const result = moveItems(
        sourceItems,
        destinationItems,
        source,
        destination,
      );
      setSourceItems(result.source);
      setDestinationItems(result.destination);
    }
  };

  /**
   * Sắp xếp lại các items trong một danh sách
   */
  const reorderItems = (
    list: T[],
    startIndex: number,
    endIndex: number,
  ): T[] => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);
    return result;
  };

  /**
   * Di chuyển item giữa hai danh sách
   */
  const moveItems = (
    source: T[],
    destination: T[],
    droppableSource: DraggableLocation,
    droppableDestination: DraggableLocation,
  ) => {
    const sourceClone = Array.from(source);
    const destClone = Array.from(destination);
    const [removed] = sourceClone.splice(droppableSource.index, 1);

    destClone.splice(droppableDestination.index, 0, removed);

    return {
      source: sourceClone,
      destination: destClone,
    };
  };

  return {
    sourceItems,
    setSourceItems,
    destinationItems,
    setDestinationItems,
    onDragEnd,
  };
}
