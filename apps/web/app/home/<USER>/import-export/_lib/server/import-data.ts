'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';

type Resource = 'products' | 'categories' | 'orders' | 'customers' | 'vouchers' | 'analytics';

interface ImportResult {
  success: boolean;
  count: number;
  data?: any[];
  errors?: string[];
  failedRows?: number;
}

/**
 * Nhập dữ liệu vào resource
 * @param resource Tên resource (products, categories, orders, customers)
 * @param data Dữ liệu cần nhập
 * @param mapping Mapping giữa trường nguồn và trường đích
 * @param accountId ID của account
 * @param batchSize Kích thước batch (mặc định: 100)
 * @returns Kết quả nhập dữ liệu
 */
export async function importData(
  resource: Resource,
  data: Record<string, any>[],
  mapping: Record<string, string>,
  accountId: string,
  batchSize: number = 100,
): Promise<ImportResult> {
  const supabase = getSupabaseServerClient();

  try {
    // Kiểm tra quyền truy cập
    if (!accountId) {
      throw new Error('Account ID is required');
    }

    // Xác định các trường dạng số và giá trị mặc định
    const numericFields: Record<string, number> = {
      price: 0,
      compare_at_price: 0,
      weight: 0,
      stock_quantity: 0,
      discount_value: 0,
      total: 0,
    };

    // Chuyển đổi dữ liệu theo mapping
    const mappedData = data.map((item) => {
      const mappedItem: Record<string, any> = {
        account_id: accountId,
      };

      // Áp dụng mapping
      Object.entries(mapping).forEach(([destField, sourceField]) => {
        if (sourceField && item[sourceField] !== undefined) {
          // Xử lý các trường dạng số
          if (destField in numericFields) {
            // Chuyển đổi sang số và sử dụng giá trị mặc định nếu không hợp lệ
            const numValue = parseFloat(item[sourceField]);
            mappedItem[destField] = isNaN(numValue) ? numericFields[destField] : numValue;
          } else {
            mappedItem[destField] = item[sourceField];
          }
        } else if (destField in numericFields) {
          // Đặt giá trị mặc định cho các trường dạng số không được mapping
          mappedItem[destField] = numericFields[destField];
        }
      });

      // Đảm bảo tất cả các trường dạng số bắt buộc đều có giá trị mặc định
      // Bất kể có được mapping hay không
      const requiredNumericFields = ['price', 'compare_at_price'];

      Object.entries(numericFields).forEach(([field, defaultValue]) => {
        // Nếu là trường bắt buộc hoặc đã được mapping nhưng chưa có giá trị
        if ((requiredNumericFields.includes(field) || mapping[field]) && !(field in mappedItem)) {
          mappedItem[field] = defaultValue;
        }
      });

      return mappedItem;
    });

    // Chia thành các batch để xử lý
    const batches: Record<string, any>[][] = [];
    for (let i = 0; i < mappedData.length; i += batchSize) {
      batches.push(mappedData.slice(i, i + batchSize));
    }

    // Kết quả tổng hợp
    let totalInserted = 0;
    let allInsertedData: any[] = [];
    const errors: string[] = [];
    let failedRows = 0;

    // Xử lý từng batch
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      try {
        // Kiểm tra lần cuối và đảm bảo tất cả các trường dạng số bắt buộc đều có giá trị
        const finalBatch = batch.map(item => {
          // Đảm bảo compare_at_price có giá trị
          if (resource === 'products' && item.compare_at_price === undefined) {
            item.compare_at_price = 0;
          }
          return item;
        });

        // Thực hiện insert dữ liệu
        const { data: insertedData, error } = await supabase
          .from(resource)
          .insert(finalBatch)
          .select();

        if (error) {
          console.error(`Error importing batch ${i} of ${resource}:`, error);
          errors.push(`Batch ${i}: ${error.message}`);
          failedRows += batch.length;
        } else {
          totalInserted += insertedData?.length || 0;
          if (insertedData) {
            allInsertedData = [...allInsertedData, ...insertedData];
          }
        }
      } catch (batchError: any) {
        console.error(`Error in batch ${i}:`, batchError);
        errors.push(`Batch ${i}: ${batchError.message}`);
        failedRows += batch.length;
      }
    }

    return {
      success: errors.length === 0,
      count: totalInserted,
      data: allInsertedData,
      errors: errors.length > 0 ? errors : undefined,
      failedRows: failedRows > 0 ? failedRows : undefined,
    };
  } catch (error: any) {
    console.error(`Error in importData:`, error);
    return {
      success: false,
      count: 0,
      errors: [error.message],
    };
  }
}

/**
 * Nhập dữ liệu vào resource theo batch
 * @param resource Tên resource (products, categories, orders, customers)
 * @param batch Dữ liệu batch cần nhập
 * @param mapping Mapping giữa trường nguồn và trường đích
 * @param accountId ID của account
 * @param batchIndex Chỉ số của batch (để tracking)
 * @returns Kết quả nhập dữ liệu
 */
export async function importBatch(
  resource: Resource,
  batch: Record<string, any>[],
  mapping: Record<string, string>,
  accountId: string,
  batchIndex: number = 0,
): Promise<ImportResult> {
  const supabase = getSupabaseServerClient();

  try {
    // Kiểm tra quyền truy cập
    if (!accountId) {
      throw new Error('Account ID is required');
    }

    // Nếu batch rỗng, trả về thành công
    if (batch.length === 0) {
      return {
        success: true,
        count: 0,
      };
    }

    // Xác định các trường dạng số và giá trị mặc định
    const numericFields: Record<string, number> = {
      price: 0,
      compare_at_price: 0,
      weight: 0,
      stock_quantity: 0,
      discount_value: 0,
      total: 0,
    };

    // Chuyển đổi dữ liệu theo mapping
    const mappedData = batch.map((item) => {
      const mappedItem: Record<string, any> = {
        account_id: accountId,
      };

      // Áp dụng mapping
      Object.entries(mapping).forEach(([destField, sourceField]) => {
        if (sourceField && item[sourceField] !== undefined) {
          // Xử lý các trường dạng số
          if (destField in numericFields) {
            // Chuyển đổi sang số và sử dụng giá trị mặc định nếu không hợp lệ
            const numValue = parseFloat(item[sourceField]);
            mappedItem[destField] = isNaN(numValue) ? numericFields[destField] : numValue;
          } else {
            mappedItem[destField] = item[sourceField];
          }
        } else if (destField in numericFields) {
          // Đặt giá trị mặc định cho các trường dạng số không được mapping
          mappedItem[destField] = numericFields[destField];
        }
      });

      // Đảm bảo tất cả các trường dạng số bắt buộc đều có giá trị mặc định
      // Bất kể có được mapping hay không
      const requiredNumericFields = ['price', 'compare_at_price'];

      Object.entries(numericFields).forEach(([field, defaultValue]) => {
        // Nếu là trường bắt buộc hoặc đã được mapping nhưng chưa có giá trị
        if ((requiredNumericFields.includes(field) || mapping[field]) && !(field in mappedItem)) {
          mappedItem[field] = defaultValue;
        }
      });

      return mappedItem;
    });

    // Kiểm tra lần cuối và đảm bảo tất cả các trường dạng số bắt buộc đều có giá trị
    const finalMappedData = mappedData.map(item => {
      // Đảm bảo compare_at_price có giá trị
      if (resource === 'products' && item.compare_at_price === undefined) {
        item.compare_at_price = 0;
      }
      return item;
    });

    // Thực hiện insert dữ liệu
    const { data: insertedData, error } = await supabase
      .from(resource)
      .insert(finalMappedData)
      .select();

    if (error) {
      console.error(`Error importing batch ${batchIndex} of ${resource}:`, error);
      return {
        success: false,
        count: 0,
        errors: [`Batch ${batchIndex}: ${error.message}`],
        failedRows: mappedData.length,
      };
    }

    return {
      success: true,
      count: insertedData?.length || 0,
      data: insertedData || [],
    };
  } catch (error: any) {
    console.error(`Error in importBatch:`, error);
    return {
      success: false,
      count: 0,
      errors: [`Batch ${batchIndex}: ${error.message}`],
      failedRows: batch.length,
    };
  }
}

/**
 * Xác thực dữ liệu trước khi nhập
 * @param resource Tên resource
 * @param data Dữ liệu cần xác thực
 * @param mapping Mapping giữa trường nguồn và trường đích
 * @returns Kết quả xác thực
 */
export async function validateImportData(
  resource: Resource,
  data: Record<string, any>[],
  mapping: Record<string, string>,
) {
  try {
    // Kiểm tra các trường bắt buộc
    const requiredFields: Record<Resource, string[]> = {
      products: ['name', 'price'],
      categories: ['name'],
      orders: ['customer_id', 'total'],
      customers: ['name', 'email'],
      vouchers: ['code', 'discount_type', 'discount_value'],
      analytics: ['date'],
    };

    const required = requiredFields[resource] || [];
    const missingFields: string[] = [];

    // Kiểm tra xem tất cả các trường bắt buộc có được map không
    required.forEach((field) => {
      if (!Object.keys(mapping).includes(field)) {
        missingFields.push(field);
      }
    });

    if (missingFields.length > 0) {
      return {
        valid: false,
        errors: [`Missing required fields: ${missingFields.join(', ')}`],
      };
    }

    // Kiểm tra dữ liệu
    const dataErrors: { row: number; errors: string[] }[] = [];
    const warnings: string[] = [];

    // Xác định các trường dạng số
    const numericFields = ['price', 'compare_at_price', 'weight', 'stock_quantity', 'discount_value', 'total'];

    data.forEach((row, index) => {
      const rowErrors: string[] = [];

      // Kiểm tra các trường bắt buộc có dữ liệu không
      required.forEach((field) => {
        const sourceField = mapping[field];
        if (sourceField && (row[sourceField] === undefined || row[sourceField] === null || row[sourceField] === '')) {
          rowErrors.push(`Missing value for required field: ${field}`);
        }
      });

      // Kiểm tra các trường dạng số có hợp lệ không
      Object.entries(mapping).forEach(([destField, sourceField]) => {
        if (numericFields.includes(destField) && sourceField && row[sourceField] !== undefined && row[sourceField] !== null && row[sourceField] !== '') {
          const value = row[sourceField];
          if (isNaN(parseFloat(String(value)))) {
            warnings.push(`Row ${index + 1}: Invalid numeric value "${value}" for field "${destField}". Will use default value 0.`);
          }
        }
      });

      if (rowErrors.length > 0) {
        dataErrors.push({ row: index + 1, errors: rowErrors });
      }
    });

    if (dataErrors.length > 0) {
      return {
        valid: false,
        errors: [`Found ${dataErrors.length} rows with errors`],
        dataErrors,
        warnings: warnings.length > 0 ? warnings : undefined,
      };
    }

    return {
      valid: true,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  } catch (error: any) {
    console.error(`Error in validateImportData:`, error);
    return {
      valid: false,
      errors: [error.message],
    };
  }
}
