'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

type Resource =
  | 'products'
  | 'categories'
  | 'orders'
  | 'customers'
  | 'vouchers'
  | 'analytics';

type FilterOperator =
  | 'eq'
  | 'neq'
  | 'gt'
  | 'gte'
  | 'lt'
  | 'lte'
  | 'like'
  | 'ilike';

interface Filter {
  field: string;
  operator: FilterOperator;
  value: string | number | boolean;
}

interface ExportOptions {
  filters?: Filter[];
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Xuất dữ liệu từ resource
 * @param resource Tên resource (products, categories, orders, customers)
 * @param fields Danh sách các trường cần xuất
 * @param accountId ID của account
 * @param options Tùy chọn xuất (filters, limit, sort)
 * @returns Dữ liệu dạng JSON
 */
export async function exportData(
  resource: Resource,
  fields: string[],
  accountId: string,
  options?: ExportOptions,
) {
  const supabase = getSupabaseServerClient();

  try {
    // Kiểm tra quyền truy cập
    if (!accountId) {
      throw new Error('Account ID is required');
    }

    // Tạo query với các trường được chọn
    let query = supabase
      .from(resource)
      .select(fields.join(','))
      .eq('account_id', accountId);

    // Áp dụng các bộ lọc
    if (options?.filters && options.filters.length > 0) {
      options.filters.forEach((filter) => {
        switch (filter.operator) {
          case 'eq':
            query = query.eq(filter.field, filter.value);
            break;
          case 'neq':
            query = query.neq(filter.field, filter.value);
            break;
          case 'gt':
            query = query.gt(filter.field, filter.value);
            break;
          case 'gte':
            query = query.gte(filter.field, filter.value);
            break;
          case 'lt':
            query = query.lt(filter.field, filter.value);
            break;
          case 'lte':
            query = query.lte(filter.field, filter.value);
            break;
          case 'like':
            query = query.like(filter.field, `%${filter.value}%`);
            break;
          case 'ilike':
            query = query.ilike(filter.field, `%${filter.value}%`);
            break;
        }
      });
    }

    // Sắp xếp
    if (options?.sortBy) {
      query = query.order(options.sortBy, {
        ascending: options.sortOrder === 'asc',
      });
    }

    // Giới hạn số lượng
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    const { data, error } = await query;

    if (error) {
      console.error(`Error exporting ${resource}:`, error);
      throw new Error(`Failed to export ${resource}: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error(`Error in exportData:`, error);
    throw new Error(`Export failed: ${error.message}`);
  }
}

/**
 * Chuyển đổi dữ liệu thành CSV
 * @param data Dữ liệu cần chuyển đổi
 * @param fields Danh sách các trường
 * @returns Chuỗi CSV
 */
function convertToCSV(data: any[], fields: string[]) {
  // Header row
  let csv = fields.join(',') + '\n';

  // Data rows
  data.forEach((item) => {
    const row = fields.map((field) => {
      const value = item[field];
      // Xử lý các giá trị đặc biệt (null, undefined, có dấu phẩy, dấu xuống dòng)
      if (value === null || value === undefined) return '';

      // Đảm bảo giá trị là chuỗi
      const strValue = String(value);

      // Nếu chuỗi có dấu phẩy, dấu xuống dòng, dấu ngoặc kép, bao quanh bằng dấu ngoặc kép
      if (
        strValue.includes(',') ||
        strValue.includes('\n') ||
        strValue.includes('"') ||
        strValue.includes('\r')
      ) {
        // Thay thế dấu ngoặc kép bằng hai dấu ngoặc kép liền nhau
        const escaped = strValue.replace(/"/g, '""');
        return `"${escaped}"`;
      }

      return strValue;
    });

    csv += row.join(',') + '\n';
  });

  return csv;
}

/**
 * Xuất dữ liệu dạng CSV
 * @param resource Tên resource
 * @param fields Danh sách các trường cần xuất
 * @param accountId ID của account
 * @param options Tùy chọn xuất
 * @returns Dữ liệu dạng CSV
 */
export async function exportDataAsCsv(
  resource: Resource,
  fields: string[],
  accountId: string,
  options?: ExportOptions,
) {
  const supabase = getSupabaseServerClient();

  try {
    // Kiểm tra quyền truy cập
    if (!accountId) {
      throw new Error('Account ID is required');
    }

    // Tạo query với các trường được chọn
    let query = supabase
      .from(resource)
      .select(fields.join(','))
      .eq('account_id', accountId);

    // Áp dụng các bộ lọc
    if (options?.filters && options.filters.length > 0) {
      options.filters.forEach((filter) => {
        switch (filter.operator) {
          case 'eq':
            query = query.eq(filter.field, filter.value);
            break;
          case 'neq':
            query = query.neq(filter.field, filter.value);
            break;
          case 'gt':
            query = query.gt(filter.field, filter.value);
            break;
          case 'gte':
            query = query.gte(filter.field, filter.value);
            break;
          case 'lt':
            query = query.lt(filter.field, filter.value);
            break;
          case 'lte':
            query = query.lte(filter.field, filter.value);
            break;
          case 'like':
            query = query.like(filter.field, `%${filter.value}%`);
            break;
          case 'ilike':
            query = query.ilike(filter.field, `%${filter.value}%`);
            break;
        }
      });
    }

    // Sắp xếp
    if (options?.sortBy) {
      query = query.order(options.sortBy, {
        ascending: options.sortOrder === 'asc',
      });
    }

    // Giới hạn số lượng
    if (options?.limit) {
      query = query.limit(options.limit);
    }

    const { data, error } = await query;

    if (error) {
      console.error(`Error exporting ${resource} as CSV:`, error);
      throw new Error(`Failed to export ${resource} as CSV: ${error.message}`);
    }

    // Chuyển đổi dữ liệu thành CSV
    const csv = convertToCSV(data, fields);
    return csv;
  } catch (error: any) {
    console.error(`Error in exportDataAsCsv:`, error);
    throw new Error(`Export as CSV failed: ${error.message}`);
  }
}

/**
 * Xuất dữ liệu dạng Excel
 * @param resource Tên resource
 * @param fields Danh sách các trường cần xuất
 * @param accountId ID của account
 * @param options Tùy chọn xuất
 * @returns Dữ liệu dạng Buffer
 */
export async function exportDataAsExcel(
  resource: Resource,
  fields: string[],
  accountId: string,
  options?: ExportOptions,
) {
  try {
    // Lấy dữ liệu JSON
    const data = await exportData(resource, fields, accountId, options);

    // Chuyển đổi dữ liệu thành Excel (xử lý ở client side)
    return {
      data,
      fields,
      resource,
    };
  } catch (error: any) {
    console.error(`Error in exportDataAsExcel:`, error);
    throw new Error(`Export as Excel failed: ${error.message}`);
  }
}

/**
 * Lấy danh sách các trường của resource
 * @param resource Tên resource
 * @returns Danh sách các trường
 */
export async function getResourceFields(resource: Resource) {
  // Định nghĩa các trường cho mỗi resource
  const resourceFields: Record<Resource, string[]> = {
    products: [
      'id',
      'name',
      'description',
      'price',
      'compare_at_price',
      'category_id',
      'sku',
      'barcode',
      'status',
      'weight',
      'image_url',
    ],
    categories: ['id', 'name', 'description', 'parent_id', 'image_url'],
    orders: [
      'id',
      'customer_id',
      'total',
      'status',
      'payment_status',
      'fulfillment_status',
    ],
    customers: ['id', 'name', 'email', 'phone', 'is_vip'],
    vouchers: [
      'id',
      'code',
      'discount_type',
      'discount_value',
      'min_purchase',
      'max_discount',
      'start_date',
      'end_date',
      'usage_limit',
      'used_count',
      'status',
    ],
    analytics: [
      'id',
      'date',
      'page_views',
      'unique_visitors',
      'bounce_rate',
      'avg_session_duration',
      'conversion_rate',
      'revenue',
    ],
  };

  return resourceFields[resource] || [];
}

/**
 * Lấy danh sách các trường có thể lọc của resource
 * @param resource Tên resource
 * @returns Danh sách các trường có thể lọc
 */
export async function getFilterableFields(resource: Resource) {
  // Định nghĩa các trường có thể lọc cho mỗi resource
  const filterableFields: Record<
    Resource,
    { field: string; type: 'string' | 'number' | 'boolean' }[]
  > = {
    products: [
      { field: 'name', type: 'string' },
      { field: 'price', type: 'number' },
      { field: 'status', type: 'string' },
      { field: 'category_id', type: 'string' },
    ],
    categories: [
      { field: 'name', type: 'string' },
      { field: 'parent_id', type: 'string' },
    ],
    orders: [
      { field: 'status', type: 'string' },
      { field: 'total', type: 'number' },
      { field: 'payment_status', type: 'string' },
      { field: 'fulfillment_status', type: 'string' },
    ],
    customers: [
      { field: 'name', type: 'string' },
      { field: 'email', type: 'string' },
      { field: 'is_vip', type: 'boolean' },
    ],
    vouchers: [
      { field: 'code', type: 'string' },
      { field: 'discount_type', type: 'string' },
      { field: 'discount_value', type: 'number' },
      { field: 'start_date', type: 'string' },
      { field: 'end_date', type: 'string' },
      { field: 'status', type: 'string' },
    ],
    analytics: [
      { field: 'date', type: 'string' },
      { field: 'page_views', type: 'number' },
      { field: 'unique_visitors', type: 'number' },
      { field: 'bounce_rate', type: 'number' },
      { field: 'conversion_rate', type: 'number' },
      { field: 'revenue', type: 'number' },
    ],
  };

  return filterableFields[resource] || [];
}

/**
 * Lấy danh sách các toán tử lọc
 * @returns Danh sách các toán tử lọc
 */
export async function getFilterOperators() {
  return [
    { value: 'eq', label: 'Equals', types: ['string', 'number', 'boolean'] },
    {
      value: 'neq',
      label: 'Not Equals',
      types: ['string', 'number', 'boolean'],
    },
    { value: 'gt', label: 'Greater Than', types: ['number'] },
    { value: 'gte', label: 'Greater Than or Equal', types: ['number'] },
    { value: 'lt', label: 'Less Than', types: ['number'] },
    { value: 'lte', label: 'Less Than or Equal', types: ['number'] },
    { value: 'like', label: 'Contains', types: ['string'] },
    { value: 'ilike', label: 'Contains (Case Insensitive)', types: ['string'] },
  ];
}

/**
 * Lấy danh sách các template mapping
 * @param resource Tên resource
 * @param accountId ID của account
 * @returns Danh sách các template mapping
 */
export async function getTemplates(resource: Resource, accountId: string) {
  const supabase = getSupabaseServerClient();

  try {
    // Kiểm tra xem bảng import_export_templates có tồn tại không
    const { data: tableExists, error: tableError } = await supabase
      .from('import_export_templates')
      .select('id')
      .limit(1);

    // Nếu bảng không tồn tại, tạo bảng mới
    if (tableError && tableError.code === '42P01') {
      // 42P01 là mã lỗi "relation does not exist"
      // Tạo bảng mới
      await supabase.rpc('create_import_export_templates_table');
    }

    // Lấy danh sách templates
    const { data, error } = await supabase
      .from('import_export_templates')
      .select('id, name, resource, mapping, filters, created_at')
      .eq('account_id', accountId)
      .eq('resource', resource)
      .order('created_at', { ascending: false });

    if (error) {
      console.error(`Error getting templates:`, error);
      return [];
    }

    // Chuyển đổi dữ liệu để thêm trường fields từ mapping
    return (data || []).map((template) => ({
      ...template,
      fields: template.mapping ? Object.values(template.mapping) : [],
    }));
  } catch (error: any) {
    console.error(`Error in getTemplates:`, error);
    return [];
  }
}

/**
 * Lưu template mapping
 * @param resource Tên resource
 * @param accountId ID của account
 * @param name Tên template
 * @param mapping Mapping giữa trường nguồn và trường đích
 * @param filters Các bộ lọc (tùy chọn)
 * @returns Template đã lưu
 */
export async function saveTemplate(
  resource: Resource,
  accountId: string,
  name: string,
  mapping: Record<string, string>,
  filters?: Filter[],
) {
  const supabase = getSupabaseServerClient();

  try {
    // Kiểm tra xem bảng import_export_templates có tồn tại không
    const { data: tableExists, error: tableError } = await supabase
      .from('import_export_templates')
      .select('id')
      .limit(1);

    // Nếu bảng không tồn tại, tạo bảng mới
    if (tableError && tableError.code === '42P01') {
      // 42P01 là mã lỗi "relation does not exist"
      // Tạo bảng mới
      await supabase.rpc('create_import_export_templates_table');
    }

    // Lưu template
    const { data, error } = await supabase
      .from('import_export_templates')
      .insert({
        account_id: accountId,
        resource,
        name,
        mapping,
        filters: filters || [],
      })
      .select()
      .single();

    if (error) {
      console.error(`Error saving template:`, error);
      throw new Error(`Failed to save template: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error(`Error in saveTemplate:`, error);
    throw new Error(`Save template failed: ${error.message}`);
  }
}

/**
 * Xóa template mapping
 * @param templateId ID của template
 * @param accountId ID của account
 * @returns Kết quả xóa
 */
export async function deleteTemplate(templateId: string, accountId: string) {
  const supabase = getSupabaseServerClient();

  try {
    const { error } = await supabase
      .from('import_export_templates')
      .delete()
      .eq('id', templateId)
      .eq('account_id', accountId);

    if (error) {
      console.error(`Error deleting template:`, error);
      throw new Error(`Failed to delete template: ${error.message}`);
    }

    return { success: true };
  } catch (error: any) {
    console.error(`Error in deleteTemplate:`, error);
    throw new Error(`Delete template failed: ${error.message}`);
  }
}
