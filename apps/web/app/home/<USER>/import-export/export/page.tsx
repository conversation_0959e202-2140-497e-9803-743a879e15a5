import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { withI18n } from '~/lib/i18n/with-i18n';

import { ExportSection } from '../_components/export-section';

interface ExportPageProps {
  params: Promise<{
    account: string;
  }>;
}

/**
 * Trang Export
 */
async function ExportPage({ params }: ExportPageProps) {
  const { account: accountSlug } = await params;

  try {
    // Lấy thông tin workspace
    const { account } = await loadTeamWorkspace(accountSlug);

    if (!account) {
      return (
        <PageBody>
          <div className="text-destructive">Team not found</div>
        </PageBody>
      );
    }

    return (
      <PageBody className="py-8">
        <TeamAccountLayoutPageHeader
          title={<Trans i18nKey="import-export:export_card_title" />}
          description={<AppBreadcrumbs />}
          account={account.slug}
        />
        <div className="mx-auto max-w-7xl">
          <div className="mb-8">
            <h1 className="mb-2 text-2xl font-bold tracking-tight">
              <Trans i18nKey="import-export:export_card_title">Export Data</Trans>
            </h1>
            <p className="text-muted-foreground">
              <Trans i18nKey="import-export:export_card_description">
                Export your data to CSV or Excel files. You can select specific fields and apply filters.
              </Trans>
            </p>
          </div>

          <ExportSection accountId={account.id} accountSlug={accountSlug} />
        </div>
      </PageBody>
    );
  } catch (error: any) {
    console.error('Error loading export page:', {
      error: error.message,
      params,
      context: 'export.page',
    });

    return (
      <PageBody>
        <div className="text-destructive">
          Failed to load data. Please try again later.
        </div>
      </PageBody>
    );
  }
}

export default withI18n(ExportPage);

// Metadata
export const metadata = {
  title: 'Export Data',
};
