import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { withI18n } from '~/lib/i18n/with-i18n';

import { ImportSection } from '../_components/import-section';

interface ImportPageProps {
  params: Promise<{
    account: string;
  }>;
}

/**
 * Trang Import
 */
async function ImportPage({ params }: ImportPageProps) {
  const { account: accountSlug } = await params;

  try {
    // Lấy thông tin workspace
    const { account } = await loadTeamWorkspace(accountSlug);

    if (!account) {
      return (
        <PageBody>
          <div className="text-destructive">Team not found</div>
        </PageBody>
      );
    }

    return (
      <PageBody className="py-8">
        <TeamAccountLayoutPageHeader
          title={<Trans i18nKey="import-export:import_card_title" />}
          description={<AppBreadcrumbs />}
          account={account.slug}
        />
        <div className="mx-auto max-w-7xl">
          <ImportSection accountId={account.id} accountSlug={accountSlug} />
        </div>
      </PageBody>
    );
  } catch (error: any) {
    console.error('Error loading import page:', {
      error: error.message,
      params,
      context: 'import.page',
    });

    return (
      <PageBody>
        <div className="text-destructive">
          Failed to load data. Please try again later.
        </div>
      </PageBody>
    );
  }
}

export default withI18n(ImportPage);

// Metadata
export const metadata = {
  title: 'Import Data',
};
