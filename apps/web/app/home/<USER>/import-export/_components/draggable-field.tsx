'use client';

import { Draggable } from '@hello-pangea/dnd';
import { GripVertical } from 'lucide-react';
import { cn } from '@kit/ui/utils';

interface DraggableFieldProps {
  id: string;
  index: number;
  field: string;
  className?: string;
  description?: string;
}

/**
 * Component hiển thị một field có thể kéo thả
 */
export function DraggableField({
  id,
  index,
  field,
  className,
  description,
}: DraggableFieldProps) {
  return (
    <Draggable draggableId={id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={cn(
            'flex items-center gap-2 rounded-md border bg-card/80 p-3 shadow-sm transition-all duration-300',
            snapshot.isDragging && 'border-primary border-2 bg-primary/5 shadow-lg scale-105 opacity-90 z-50 ring-2 ring-primary/30 ring-offset-2 cursor-grabbing',
            className
          )}
          data-testid={`field-${field}`}
          style={{
            ...provided.draggableProps.style,
          }}
        >
          <GripVertical className="h-4 w-4 text-muted-foreground/70" />
          <div className="flex flex-col flex-1 min-w-0">
            <span className="font-medium truncate">{field}</span>
            {description && (
              <span className="text-xs text-muted-foreground truncate">{description}</span>
            )}
          </div>
        </div>
      )}
    </Draggable>
  );
}
