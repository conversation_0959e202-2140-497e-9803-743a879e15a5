'use client';

import {useEffect, useState} from 'react';
import {useRouter} from 'next/navigation';
import {ArrowDownToLine} from 'lucide-react';
import {DragDropContext, Droppable} from '@hello-pangea/dnd';
import {useTranslation} from 'react-i18next';
import {toast} from 'sonner';
import * as XLSX from 'xlsx';

import {Button} from '@kit/ui/button';
import {Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle,} from '@kit/ui/dialog';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue,} from '@kit/ui/select';
import {Trans} from '@kit/ui/trans';
import {Input} from '@kit/ui/input';
import {Label} from '@kit/ui/label';
import {Accordion, AccordionContent, AccordionItem, AccordionTrigger,} from '@kit/ui/accordion';

import {useDragDrop} from '../_lib/client/use-drag-drop';
import {
  deleteTemplate,
  exportDataAsCsv,
  exportDataAsExcel,
  getFilterableFields,
  getFilterOperators,
  getResourceFields,
  getTemplates,
  saveTemplate,
} from '../_lib/server/export-data';
import {DraggableField} from './draggable-field';

interface ExportSectionProps {
  accountId: string;
  accountSlug: string;
}

type Resource = 'products' | 'categories' | 'orders' | 'customers' | 'vouchers' | 'analytics';
type ExportFormat = 'csv' | 'excel';
type FilterOperator = 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'ilike';

interface Filter {
  field: string;
  operator: FilterOperator;
  value: string | number | boolean;
}

interface FilterableField {
  field: string;
  type: 'string' | 'number' | 'boolean';
}

interface FilterOperator {
  value: string;
  label: string;
  types: string[];
}

interface Template {
  id: string;
  name: string;
  resource: Resource;
  mapping: Record<string, string>;
  filters?: Filter[];
  created_at: string;
}

/**
 * Component hiển thị phần xuất dữ liệu
 */
export function ExportSection({ accountId, accountSlug }: ExportSectionProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [selectedResource, setSelectedResource] = useState<Resource>('products');
  const [availableFields, setAvailableFields] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [exportFormat, setExportFormat] = useState<ExportFormat>('csv');
  const [showFilters, setShowFilters] = useState(false);
  const [filterableFields, setFilterableFields] = useState<FilterableField[]>([]);
  const [filterOperators, setFilterOperators] = useState<FilterOperator[]>([]);
  const [filters, setFilters] = useState<Filter[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [templateName, setTemplateName] = useState('');
  const [showSaveTemplate, setShowSaveTemplate] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [exportedCount, setExportedCount] = useState(0);

  // Sử dụng hook useDragDrop để xử lý kéo thả
  const {
    sourceItems: availableItems,
    destinationItems: selectedItems,
    onDragEnd,
    setSourceItems: setAvailableItems,
    setDestinationItems: setSelectedItems,
  } = useDragDrop<string>(availableFields);

  // Load các trường và operators khi component mount
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Lấy danh sách các toán tử lọc
        const operators = await getFilterOperators();
        setFilterOperators(operators);

        // Lấy danh sách các trường của resource mặc định
        await handleResourceChange(selectedResource);
      } catch (error: any) {
        console.error('Error loading initial data:', error);
      }
    };

    loadInitialData();
  }, []);

  // Xử lý khi chọn resource
  const handleResourceChange = async (value: Resource) => {
    setSelectedResource(value);
    setFilters([]);
    setSelectedItems([]);
    setShowSaveTemplate(false);
    setTemplateName('');

    try {
      // Lấy danh sách các trường của resource
      const fields = await getResourceFields(value);
      setAvailableItems(fields);

      // Lấy danh sách các trường có thể lọc
      const filterable = await getFilterableFields(value);
      setFilterableFields(filterable);

      // Lấy danh sách templates
      const templatesList = await getTemplates(value, accountId);
      setTemplates(templatesList);
    } catch (error: any) {
      toast.error(t('import-export:error_loading_fields'));
      console.error('Error loading fields:', error);
    }
  };

  // Xử lý khi thêm bộ lọc
  const handleAddFilter = () => {
    if (filterableFields.length === 0) return;

    const newFilter: Filter = {
      field: filterableFields[0].field,
      operator: 'eq',
      value: '',
    };

    setFilters([...filters, newFilter]);
  };

  // Xử lý khi xóa bộ lọc
  const handleRemoveFilter = (index: number) => {
    const newFilters = [...filters];
    newFilters.splice(index, 1);
    setFilters(newFilters);
  };

  // Xử lý khi thay đổi bộ lọc
  const handleFilterChange = (index: number, key: keyof Filter, value: any) => {
    const newFilters = [...filters];
    newFilters[index] = { ...newFilters[index], [key]: value };

    // Nếu thay đổi field, reset operator và value
    if (key === 'field') {
      const fieldType = filterableFields.find(f => f.field === value)?.type || 'string';
      const validOperators = filterOperators.filter(op => op.types.includes(fieldType));

      newFilters[index].operator = validOperators[0]?.value as FilterOperator || 'eq';
      newFilters[index].value = fieldType === 'boolean' ? false : '';
    }

    setFilters(newFilters);
  };

  // Xử lý khi lưu template
  const handleSaveTemplate = async () => {
    if (!templateName) {
      toast.error(t('import-export:template_name_required'));
      return;
    }

    try {
      // Tạo mapping từ selectedItems
      const mapping: Record<string, string> = {};
      selectedItems.forEach(field => {
        mapping[field] = field;
      });

      // Lưu template
      await saveTemplate(
        selectedResource,
        accountId,
        templateName,
        mapping,
        filters
      );

      // Cập nhật danh sách templates
      const templatesList = await getTemplates(selectedResource, accountId);
      setTemplates(templatesList);

      // Reset form
      setTemplateName('');
      setShowSaveTemplate(false);

      toast.success(t('import-export:template_saved'));
    } catch (error: any) {
      toast.error(t('import-export:template_save_error'));
      console.error('Error saving template:', error);
    }
  };

  // Xử lý khi xóa template
  const handleDeleteTemplate = async (templateId: string) => {
    try {
      // Xóa template
      await deleteTemplate(templateId, accountId);

      // Cập nhật danh sách templates
      const templatesList = await getTemplates(selectedResource, accountId);
      setTemplates(templatesList);

      toast.success(t('import-export:template_deleted'));
    } catch (error: any) {
      toast.error(t('import-export:template_delete_error'));
      console.error('Error deleting template:', error);
    }
  };

  // Xử lý khi chọn template
  const handleSelectTemplate = (template: Template) => {
    if (!template || !template.mapping) {
      console.error('Invalid template:', template);
      toast.error(t('import-export:invalid_template'));
      return;
    }

    // Áp dụng các trường đã chọn
    const fields = Object.keys(template.mapping);
    setSelectedItems(fields);

    // Áp dụng các bộ lọc
    if (template.filters && Array.isArray(template.filters) && template.filters.length > 0) {
      setFilters(template.filters);
      setShowFilters(true);
    }
  };

  // Xử lý khi xuất dữ liệu
  const handleExport = async () => {
    if (selectedItems.length === 0) {
      toast.error(t('import-export:select_fields'));
      return;
    }

    setIsLoading(true);

    try {
      // Tạo options xuất
      const exportOptions = {
        filters: filters.length > 0 ? filters : undefined,
      };

      // Biến để lưu số lượng dữ liệu đã xuất
      let exportCount = 0;

      if (exportFormat === 'csv') {
        // Xuất CSV
        const csvData = await exportDataAsCsv(
          selectedResource,
          selectedItems,
          accountId,
          exportOptions
        );

        // Tạo file và tải về
        const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
        downloadFile(
          blob,
          `${selectedResource}_export.csv`,
          'text/csv;charset=utf-8;'
        );

        // Đối với CSV, chúng ta không có thông tin về số lượng dữ liệu
        // Đặt một giá trị mặc định
        exportCount = 100;
      } else {
        // Xuất Excel
        const excelData = await exportDataAsExcel(
          selectedResource,
          selectedItems,
          accountId,
          exportOptions
        );

        // Chuyển đổi dữ liệu thành Excel
        if (!excelData || !excelData.data || !Array.isArray(excelData.data)) {
          throw new Error('Invalid data received from server');
        }

        const worksheet = XLSX.utils.json_to_sheet(excelData.data);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, selectedResource);

        // Tạo file và tải về
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const blob = new Blob([excelBuffer], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        downloadFile(
          blob,
          `${selectedResource}_export.xlsx`,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        );

        // Lưu số lượng dữ liệu đã xuất
        exportCount = excelData.data.length;
      }

      toast.success(t('import-export:export_success'));

      // Lưu số lượng dữ liệu đã xuất và hiển thị dialog thành công
      setExportedCount(exportCount);
      setShowSuccessDialog(true);
    } catch (error: any) {
      toast.error(t('import-export:export_error'));
      console.error('Export error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Tải file
  const downloadFile = (data: Blob, filename: string, mimeType: string) => {
    const url = URL.createObjectURL(data);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
   <>
     <div className="space-y-6">
       <div className="space-y-6">
         <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
           <div>
             <h2 className="text-xl font-semibold tracking-tight mb-1">
               <Trans i18nKey="import-export:export_title">Export Data</Trans>
             </h2>
             <p className="text-sm text-muted-foreground">
               <Trans i18nKey="import-export:export_description">
                 Select a resource and drag fields to export
               </Trans>
             </p>
           </div>
         </div>

         <div className="bg-card/50 rounded-lg border border-border/40 shadow-sm p-6">
           <div className="space-y-4">
             <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
               <div className="space-y-2">
                 <label className="text-sm font-medium">
                   <Trans i18nKey="import-export:select_resource">
                     Select Resource
                   </Trans>
                 </label>
                 <Select
                     value={selectedResource}
                     onValueChange={(value) => handleResourceChange(value as Resource)}
                 >
                   <SelectTrigger className="w-full">
                     <SelectValue placeholder={t('import-export:select_resource')} />
                   </SelectTrigger>
                   <SelectContent>
                     <SelectItem value="products">
                       {t('import-export:products')}
                     </SelectItem>
                     <SelectItem value="categories">
                       {t('import-export:categories')}
                     </SelectItem>
                     <SelectItem value="orders">
                       {t('import-export:orders')}
                     </SelectItem>
                     <SelectItem value="customers">
                       {t('import-export:customers')}
                     </SelectItem>
                     <SelectItem value="vouchers">
                       {t('import-export:vouchers')}
                     </SelectItem>
                     <SelectItem value="analytics">
                       {t('import-export:analytics')}
                     </SelectItem>
                   </SelectContent>
                 </Select>
               </div>

               <div className="space-y-2">
                 <label className="text-sm font-medium">
                   <Trans i18nKey="import-export:export_format">
                     Export Format
                   </Trans>
                 </label>
                 <Select
                     value={exportFormat}
                     onValueChange={(value) => setExportFormat(value as ExportFormat)}
                 >
                   <SelectTrigger className="w-full">
                     <SelectValue placeholder={t('import-export:select_format')} />
                   </SelectTrigger>
                   <SelectContent>
                     <SelectItem value="csv">CSV</SelectItem>
                     <SelectItem value="excel">Excel</SelectItem>
                   </SelectContent>
                 </Select>
               </div>
             </div>
             {templates.length > 0 && (
                 <Accordion type="single" collapsible className="w-full">
                   <AccordionItem value="templates">
                     <AccordionTrigger>
                       <Trans i18nKey="import-export:saved_templates">
                         Saved Templates
                       </Trans>
                     </AccordionTrigger>
                     <AccordionContent>
                       <div className="space-y-2">
                         {templates.map((template) => (
                             <div
                                 key={template.id}
                                 className="flex items-center justify-between rounded-md border p-2"
                             >
                               <span>{template.name}</span>
                               <div className="flex gap-2">
                                 <Button
                                     variant="outline"
                                     size="sm"
                                     onClick={() => handleSelectTemplate(template)}
                                 >
                                   <Trans i18nKey="import-export:use_template">
                                     Use
                                   </Trans>
                                 </Button>
                                 <Button
                                     variant="destructive"
                                     size="sm"
                                     onClick={() => handleDeleteTemplate(template.id)}
                                 >
                                   <Trans i18nKey="import-export:delete">
                                     Delete
                                   </Trans>
                                 </Button>
                               </div>
                             </div>
                         ))}
                       </div>
                     </AccordionContent>
                   </AccordionItem>
                 </Accordion>
             )}

             <div className="mt-8">
               <h3 className="text-sm font-medium mb-3">
                 <Trans i18nKey="import-export:field_selection">
                   Field Selection
                 </Trans>
               </h3>
               <p className="text-xs text-muted-foreground mb-4">
                 <Trans i18nKey="import-export:drag_fields_instruction">
                   Drag fields from the left to the right to select them for export. You can also reorder the selected fields by dragging them up or down.
                 </Trans>
               </p>

               <DragDropContext onDragEnd={onDragEnd}>
                 <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                   <div className="space-y-2">
                     <div className="flex items-center justify-between">
                       <h4 className="text-sm font-medium text-muted-foreground">
                         <Trans i18nKey="import-export:available_fields">
                           Available Fields
                         </Trans>
                       </h4>
                       <span className="text-xs text-muted-foreground">
                        {t('import-export:fields_count', { count: availableItems.length })}
                      </span>
                     </div>
                     <Droppable droppableId="source">
                       {(provided, snapshot) => (
                           <div
                               ref={provided.innerRef}
                               {...provided.droppableProps}
                               className={`min-h-[250px] rounded-md border ${snapshot.isDraggingOver ? 'border-primary/50 bg-primary/5' : 'border-dashed'} p-4 transition-colors duration-200`}
                           >
                             <div className="space-y-2">
                               {availableItems.map((field, index) => (
                                   <DraggableField
                                       key={field}
                                       id={`source-${field}`}
                                       index={index}
                                       field={field}
                                   />
                               ))}
                               {availableItems.length === 0 && (
                                   <div className="flex h-[200px] items-center justify-center text-center text-sm text-muted-foreground">
                                     <div>
                                       <Trans i18nKey="import-export:no_fields">
                                         No fields available
                                       </Trans>
                                     </div>
                                   </div>
                               )}
                             </div>
                             {provided.placeholder}
                           </div>
                       )}
                     </Droppable>
                   </div>

                   <div className="space-y-2">
                     <div className="flex items-center justify-between">
                       <h4 className="text-sm font-medium text-muted-foreground">
                         <Trans i18nKey="import-export:selected_fields">
                           Selected Fields
                         </Trans>
                       </h4>
                       <span className="text-xs text-muted-foreground">
                        {t('import-export:fields_selected', { count: selectedItems.length })}
                      </span>
                     </div>
                     <Droppable droppableId="destination">
                       {(provided, snapshot) => (
                           <div
                               ref={provided.innerRef}
                               {...provided.droppableProps}
                               className={`min-h-[250px] rounded-md border ${snapshot.isDraggingOver ? 'border-primary/50 bg-primary/5' : 'border-dashed'} p-4 transition-colors duration-200`}
                           >
                             <div className="space-y-2">
                               {selectedItems.map((field, index) => (
                                   <DraggableField
                                       key={field}
                                       id={`destination-${field}`}
                                       index={index}
                                       field={field}
                                   />
                               ))}
                               {selectedItems.length === 0 && (
                                   <div className="flex h-[200px] flex-col items-center justify-center text-center">
                                     <div className="rounded-full bg-muted/50 p-3 mb-2">
                                       <ArrowDownToLine className="h-5 w-5 text-muted-foreground" />
                                     </div>
                                     <div className="text-sm text-muted-foreground">
                                       <Trans i18nKey="import-export:drag_fields_here">
                                         Drag fields here to export
                                       </Trans>
                                     </div>
                                   </div>
                               )}
                             </div>
                             {provided.placeholder}
                           </div>
                       )}
                     </Droppable>
                   </div>
                 </div>
               </DragDropContext>
             </div>

             <div className="flex items-center gap-2">
               <Button
                   variant="outline"
                   onClick={() => setShowFilters(!showFilters)}
               >
                 {showFilters ? (
                     <Trans i18nKey="import-export:hide_filters">
                       Hide Filters
                     </Trans>
                 ) : (
                     <Trans i18nKey="import-export:show_filters">
                       Show Filters
                     </Trans>
                 )}
               </Button>

               <Button
                   variant="outline"
                   onClick={() => setShowSaveTemplate(!showSaveTemplate)}
               >
                 {showSaveTemplate ? (
                     <Trans i18nKey="import-export:hide_save_template">
                       Cancel
                     </Trans>
                 ) : (
                     <Trans i18nKey="import-export:show_save_template">
                       Save as Template
                     </Trans>
                 )}
               </Button>
             </div>

             {showSaveTemplate && (
                 <div className="rounded-md border p-4">
                   <h3 className="mb-2 text-sm font-medium">
                     <Trans i18nKey="import-export:save_template">
                       Save as Template
                     </Trans>
                   </h3>
                   <div className="space-y-4">
                     <div>
                       <Label htmlFor="template-name">
                         <Trans i18nKey="import-export:template_name">
                           Template Name
                         </Trans>
                       </Label>
                       <Input
                           id="template-name"
                           value={templateName}
                           onChange={(e) => setTemplateName(e.target.value)}
                           placeholder={t('import-export:template_name_placeholder')}
                       />
                     </div>
                     <Button onClick={handleSaveTemplate}>
                       <Trans i18nKey="import-export:save">Save</Trans>
                     </Button>
                   </div>
                 </div>
             )}

             {showFilters && (
                 <div className="rounded-md border p-4">
                   <h3 className="mb-2 text-sm font-medium">
                     <Trans i18nKey="import-export:filters">Filters</Trans>
                   </h3>
                   <div className="space-y-4">
                     {filters.map((filter, index) => (
                         <div key={index} className="flex flex-wrap items-end gap-2">
                           <div className="w-full sm:w-auto">
                             <Label className="mb-2 block text-xs">
                               <Trans i18nKey="import-export:field">Field</Trans>
                             </Label>
                             <Select
                                 value={filter.field}
                                 onValueChange={(value) =>
                                     handleFilterChange(index, 'field', value)
                                 }
                             >
                               <SelectTrigger className="w-full sm:w-[150px]">
                                 <SelectValue />
                               </SelectTrigger>
                               <SelectContent>
                                 {filterableFields.map((field) => (
                                     <SelectItem key={field.field} value={field.field}>
                                       {field.field}
                                     </SelectItem>
                                 ))}
                               </SelectContent>
                             </Select>
                           </div>

                           <div className="w-full sm:w-auto">
                             <Label className="mb-2 block text-xs">
                               <Trans i18nKey="import-export:operator">
                                 Operator
                               </Trans>
                             </Label>
                             <Select
                                 value={filter.operator}
                                 onValueChange={(value) =>
                                     handleFilterChange(index, 'operator', value)
                                 }
                             >
                               <SelectTrigger className="w-full sm:w-[200px]">
                                 <SelectValue />
                               </SelectTrigger>
                               <SelectContent>
                                 {filterOperators
                                     .filter((op) => {
                                       const fieldType =
                                           filterableFields.find(
                                               (f) => f.field === filter.field
                                           )?.type || 'string';
                                       return op.types.includes(fieldType);
                                     })
                                     .map((op) => (
                                         <SelectItem key={op.value} value={op.value}>
                                           {op.label}
                                         </SelectItem>
                                     ))}
                               </SelectContent>
                             </Select>
                           </div>

                           <div className="w-full sm:w-auto">
                             <Label className="mb-2 block text-xs">
                               <Trans i18nKey="import-export:value">Value</Trans>
                             </Label>
                             <Input
                                 value={filter.value as string}
                                 onChange={(e) =>
                                     handleFilterChange(index, 'value', e.target.value)
                                 }
                                 className="w-full sm:w-[200px]"
                             />
                           </div>

                           <Button
                               variant="destructive"
                               size="sm"
                               onClick={() => handleRemoveFilter(index)}
                           >
                             <Trans i18nKey="import-export:remove">Remove</Trans>
                           </Button>
                         </div>
                     ))}

                     <Button
                         variant="outline"
                         onClick={handleAddFilter}
                         disabled={filterableFields.length === 0}
                     >
                       <Trans i18nKey="import-export:add_filter">
                         Add Filter
                       </Trans>
                     </Button>
                   </div>
                 </div>
             )}

             <div className="flex justify-end">
               <Button
                   onClick={handleExport}
                   disabled={selectedItems.length === 0 || isLoading}
                   loading={isLoading}
               >
                 <Trans i18nKey="import-export:export">Export</Trans>
               </Button>
             </div>
           </div>
         </div>
       </div>
     </div>

     {/* Dialog thành công */}
     <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
       <DialogContent>
         <DialogHeader>
           <DialogTitle>
             <Trans i18nKey="import-export:export_success_title">Export Successful</Trans>
           </DialogTitle>
           <DialogDescription>
             <Trans i18nKey="import-export:export_success_description">
               Your data has been successfully exported. What would you like to do next?
             </Trans>
           </DialogDescription>
         </DialogHeader>

         <div className="flex flex-col gap-4 py-4">
           <div className="flex items-center justify-between">
             <div className="flex items-center gap-2">
               <div className="rounded-full bg-green-100 p-1.5 dark:bg-green-900">
                 <svg
                     xmlns="http://www.w3.org/2000/svg"
                     width="16"
                     height="16"
                     viewBox="0 0 24 24"
                     fill="none"
                     stroke="currentColor"
                     strokeWidth="2"
                     strokeLinecap="round"
                     strokeLinejoin="round"
                     className="text-green-600 dark:text-green-400"
                 >
                   <path d="M20 6 9 17l-5-5" />
                 </svg>
               </div>
               <span className="text-sm font-medium">
                <Trans i18nKey="import-export:exported_items" count={exportedCount}>
                  Successfully exported {{count: exportedCount}} items
                </Trans>
              </span>
             </div>
           </div>
         </div>

         <DialogFooter>
           <Button
               variant="outline"
               onClick={() => {
                 setShowSuccessDialog(false);
               }}
           >
             <Trans i18nKey="import-export:export_more">Export More Data</Trans>
           </Button>
           <Button
               onClick={() => {
                 router.push(`/home/<USER>/${selectedResource}`);
               }}
           >
             <Trans i18nKey="import-export:go_to_resource" values={{ resource: t(`import-export:${selectedResource}`) }}>
               Go to {{resource: selectedResource}}
             </Trans>
           </Button>
         </DialogFooter>
       </DialogContent>
     </Dialog>
   </>
  );
}
