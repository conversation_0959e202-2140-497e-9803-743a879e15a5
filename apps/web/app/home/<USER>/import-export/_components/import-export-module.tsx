'use client';

import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ArrowDownToLine, ArrowUpFromLine, Database } from 'lucide-react';

import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';
import { Card, CardContent } from '@kit/ui/card';
import { Separator } from '@kit/ui/separator';

import { ExportSection } from './export-section';
import { ImportSection } from './import-section';

interface ImportExportModuleProps {
  accountId: string;
  accountSlug: string;
}

/**
 * Component chính cho tính năng import/export
 */
export function ImportExportModule({ accountId, accountSlug }: ImportExportModuleProps) {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'export' | 'import'>('export');

  return (
    <div className="space-y-6 max-w-7xl mx-auto">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Database className="h-5 w-5 text-primary" />
            <h1 className="text-2xl font-bold tracking-tight">
              <Trans i18nKey="import-export:title">Import/Export Data</Trans>
            </h1>
          </div>
          <p className="text-muted-foreground">
            <Trans i18nKey="import-export:description">
              Import and export your data using CSV or Excel files
            </Trans>
          </p>
        </div>
      </div>

      <Separator className="my-6" />

      <Card className="border-none shadow-sm bg-card/50">
        <CardContent className="p-0">
          <Tabs
            defaultValue="export"
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as 'export' | 'import')}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2 md:w-[400px] p-1 bg-muted/50 rounded-t-lg">
              <TabsTrigger
                value="export"
                data-testid="export-tab"
                className="rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm"
              >
                <ArrowUpFromLine className="h-4 w-4 mr-2" />
                <Trans i18nKey="import-export:export">Export</Trans>
              </TabsTrigger>
              <TabsTrigger
                value="import"
                data-testid="import-tab"
                className="rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm"
              >
                <ArrowDownToLine className="h-4 w-4 mr-2" />
                <Trans i18nKey="import-export:import">Import</Trans>
              </TabsTrigger>
            </TabsList>
            <TabsContent value="export" className="p-6 pt-4">
              <ExportSection accountId={accountId} accountSlug={accountSlug} />
            </TabsContent>
            <TabsContent value="import" className="p-6 pt-4">
              <ImportSection accountId={accountId} accountSlug={accountSlug} />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
