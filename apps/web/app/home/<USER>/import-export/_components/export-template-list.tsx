'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FileDown, Download, Tag } from 'lucide-react';
import { Trans } from '@kit/ui/trans';
import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';

interface ExportTemplateProps {
  templates: any[];
  accountId: string;
  accountSlug: string;
  resourceType: string;
}

export function ExportTemplateList({
  templates,
  accountId,
  accountSlug,
  resourceType,
}: ExportTemplateProps) {
  const router = useRouter();

  const getBadgeColor = (type: string) => {
    switch (type) {
      case 'products':
        return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800';
      case 'categories':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800';
      case 'orders':
        return 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/20 dark:text-amber-300 dark:border-amber-800';
      case 'customers':
        return 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/20 dark:text-gray-300 dark:border-gray-800';
    }
  };

  const handleExport = (templateId: string) => {
    window.location.href = `/api/export?resource=${resourceType}&template=${templateId}&format=csv&accountId=${accountId}`;
  };

  const handleCustomize = (e: React.MouseEvent, templateId: string) => {
    e.stopPropagation();
    router.push(`/home/<USER>/import-export/export?resource=${resourceType}&template=${templateId}`);
  };

  return (
    <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
      {templates.length > 0 ? (
        templates.map((template) => (
          <Card 
            key={template.id} 
            className="overflow-hidden cursor-pointer transition-all hover:border-primary hover:shadow-md"
            onClick={() => handleExport(template.id)}
          >
            <CardContent className="p-0">
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <h4 className="font-medium truncate">{template.name}</h4>
                  </div>
                  <div className="ml-2 flex-shrink-0">
                    <Download className="h-4 w-4 text-primary" />
                  </div>
                </div>
                
                <div className="flex items-center gap-2 mb-3">
                  <Badge 
                    variant="outline" 
                    className={getBadgeColor(resourceType)}
                  >
                    <Tag className="h-3 w-3 mr-1" />
                    <Trans i18nKey={`import-export:${resourceType}`}>{resourceType}</Trans>
                  </Badge>
                  
                  <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/20 dark:text-gray-300 dark:border-gray-800">
                    <FileDown className="h-3 w-3 mr-1" />
                    CSV
                  </Badge>
                </div>
                
                <p className="text-xs text-muted-foreground">
                  {template.fields.length} <Trans i18nKey="import-export:fields">fields</Trans>
                </p>
              </div>
              
              <div className="bg-primary/5 border-t p-2 flex justify-end">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-xs h-7 hover:bg-primary/10"
                  onClick={(e) => handleCustomize(e, template.id)}
                >
                  <Trans i18nKey="import-export:customize">Customize</Trans>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))
      ) : (
        <Card className="col-span-full">
          <CardContent className="p-6 text-center">
            <div className="flex flex-col items-center gap-2">
              <div className="rounded-full bg-muted p-3">
                <FileDown className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="font-medium">
                <Trans i18nKey="import-export:no_templates">No templates found</Trans>
              </h3>
              <p className="text-sm text-muted-foreground max-w-md">
                <Trans i18nKey="import-export:create_template_description">
                  Create a template by going to the export page and saving your export configuration.
                </Trans>
              </p>
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-2"
                onClick={() => router.push(`/home/<USER>/import-export/export`)}
              >
                <Trans i18nKey="import-export:create_template">Create Template</Trans>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
