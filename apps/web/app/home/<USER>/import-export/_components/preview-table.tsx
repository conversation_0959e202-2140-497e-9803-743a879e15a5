'use client';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';

interface PreviewTableProps {
  data: Record<string, any>[];
  columns: string[];
  maxRows?: number;
}

/**
 * Component hiển thị bảng xem trước dữ liệu
 */
export function PreviewTable({
  data,
  columns,
  maxRows = 5,
}: PreviewTableProps) {
  // Giới hạn số lượng dòng hiển thị
  const previewData = data.slice(0, maxRows);

  return (
    <div className="rounded-md border overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/30 hover:bg-muted/30">
              {columns.map((column, columnIndex) => (
                <TableHead key={`header-${columnIndex}-${column}`} className="font-semibold text-xs uppercase tracking-wider py-3">
                  {column}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {previewData.length > 0 ? (
              previewData.map((row, rowIndex) => (
                <TableRow key={`row-${rowIndex}`} className="hover:bg-muted/30 transition-colors">
                  {columns.map((column, columnIndex) => (
                    <TableCell key={`cell-${rowIndex}-${columnIndex}`} className="py-3">
                      <div className="max-w-[200px] truncate">
                        {row[column] !== undefined ? String(row[column]) : ''}
                      </div>
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No data to preview
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {data.length > maxRows && (
        <div className="bg-muted/30 p-2 text-center text-xs text-muted-foreground border-t">
          Showing {maxRows} of {data.length} rows
        </div>
      )}
    </div>
  );
}
