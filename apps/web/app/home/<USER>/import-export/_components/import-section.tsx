'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { DragDropContext, Droppable } from '@hello-pangea/dnd';
import { ArrowDownToLine } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@kit/ui/accordion';
import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Progress } from '@kit/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';

import { useFileUpload } from '../_lib/client/use-file-upload';
import {
  getResourceFields,
  getTemplates,
  saveTemplate,
} from '../_lib/server/export-data';
import {
  importBatch,
  importData,
  validateImportData,
} from '../_lib/server/import-data';
import { DraggableField } from './draggable-field';
import { DroppableFieldIndicator } from './droppable-field-indicator';
import { MappedField } from './mapped-field';
import { PreviewTable } from './preview-table';

interface ImportSectionProps {
  accountId: string;
  accountSlug: string;
}

type Resource =
  | 'products'
  | 'categories'
  | 'orders'
  | 'customers'
  | 'vouchers'
  | 'analytics';

interface Template {
  id: string;
  name: string;
  resource: Resource;
  mapping: Record<string, string>;
  filters?: any[];
  created_at: string;
}

/**
 * Component hiển thị phần nhập dữ liệu
 */
export function ImportSection({ accountId, accountSlug }: ImportSectionProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [selectedResource, setSelectedResource] =
    useState<Resource>('products');
  const [destinationFields, setDestinationFields] = useState<string[]>([]);
  const [mapping, setMapping] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [previewColumns, setPreviewColumns] = useState<string[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [templateName, setTemplateName] = useState('');
  const [showSaveTemplate, setShowSaveTemplate] = useState(false);
  const [batchSize, setBatchSize] = useState(100);
  const [importProgress, setImportProgress] = useState(0);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [importStats, setImportStats] = useState<{
    total: number;
    success: number;
    failed: number;
    inProgress: boolean;
  }>({
    total: 0,
    success: 0,
    failed: 0,
    inProgress: false,
  });

  // Sử dụng hook useFileUpload để xử lý upload file
  const {
    file,
    fileData,
    headers: sourceFields,
    isLoading: isFileLoading,
    error: fileError,
    handleFileChange,
    resetFileUpload,
    processBatches,
    totalRows,
    progress: fileProgress,
    availableSheets,
    selectedSheet,
    changeSheet,
    fileType,
  } = useFileUpload();

  // Load templates và fields khi component mount
  useEffect(() => {
    const loadInitialData = async () => {
      setIsInitialLoading(true);
      try {
        // Tải templates
        const templatesList = await getTemplates(selectedResource, accountId);
        setTemplates(templatesList);

        // Tải trường đích
        const fields = await getResourceFields(selectedResource);
        setDestinationFields(fields);

        console.log('Initial load complete:', {
          resource: selectedResource,
          fields: fields.length,
          templates: templatesList.length,
        });
      } catch (error) {
        console.error('Error loading initial data:', error);
        toast.error(t('import-export:error_loading_fields'));
      } finally {
        setIsInitialLoading(false);
      }
    };

    loadInitialData();
  }, [selectedResource, accountId, t]);

  // Xử lý khi chọn resource
  const handleResourceChange = async (value: Resource) => {
    setSelectedResource(value);
    setMapping({});
    setPreviewColumns([]);
    setShowSaveTemplate(false);
    setTemplateName('');
    resetFileUpload();

    try {
      // Lấy danh sách các trường của resource
      const fields = await getResourceFields(value);
      setDestinationFields(fields);

      // Lấy danh sách templates
      const templatesList = await getTemplates(value, accountId);
      setTemplates(templatesList);
    } catch (error: any) {
      toast.error(t('import-export:error_loading_fields'));
      console.error('Error loading fields:', error);
    }
  };

  // Xử lý khi bắt đầu kéo
  const onDragStart = (result: any) => {
    // Reset activeDestination và activeSource khi bắt đầu kéo
    setActiveDestination(null);
    setActiveSource(null);
  };

  // Xử lý khi kết thúc kéo (bất kể thành công hay không)
  const onDragEnd = (result: any) => {
    const { source, destination } = result;

    // Reset activeDestination và activeSource khi kết thúc kéo
    setActiveDestination(null);
    setActiveSource(null);

    // Nếu không có destination, không làm gì cả
    if (!destination) {
      return;
    }

    // Chỉ xử lý khi kéo từ source đến destination
    if (
      source.droppableId === 'source' &&
      destination.droppableId === 'destination'
    ) {
      const sourceField = sourceFields[source.index];
      const destField = destinationFields[destination.index];

      // Cập nhật mapping
      setMapping((prev) => ({
        ...prev,
        [destField]: sourceField,
      }));

      // Cập nhật preview columns
      updatePreviewColumns(destField, sourceField);

      // Đã xóa hiệu ứng âm thanh không cần thiết

      // Hiển thị thông báo thành công
      toast.success(
        t('import-export:field_mapped_success', {
          source: sourceField,
          destination: destField,
        }),
        {
          duration: 2000,
          style: {
            borderRadius: '10px',
            background: '#10b981',
            color: '#fff',
          },
        },
      );

      // Thêm hiệu ứng highlight cho trường đã được mapping
      // Sử dụng setTimeout để đảm bảo DOM đã được cập nhật
      setTimeout(() => {
        try {
          const mappedFieldElement = document.querySelector(
            `[data-testid="mapped-field-${destField}"]`,
          );
          if (mappedFieldElement) {
            // Thêm class và sau đó xóa đi
            mappedFieldElement.classList.add(
              'bg-green-100',
              'dark:bg-green-900/30',
            );
            setTimeout(() => {
              mappedFieldElement.classList.remove(
                'bg-green-100',
                'dark:bg-green-900/30',
              );
            }, 1000);
          }
        } catch (e) {
          // Bỏ qua lỗi nếu có
        }
      }, 100);
    }
  };

  // Xử lý khi kéo qua một trường đích cụ thể
  const [activeDestination, setActiveDestination] = useState<string | null>(
    null,
  );
  const [activeSource, setActiveSource] = useState<string | null>(null);

  const onDragUpdate = (result: any) => {
    // Chỉ xử lý khi kéo từ source đến destination
    if (
      result.source?.droppableId === 'source' &&
      result.destination?.droppableId === 'destination'
    ) {
      const sourceIndex = result.source.index;
      const destIndex = result.destination.index;

      const sourceField = sourceFields[sourceIndex];
      const destField = destinationFields[destIndex];

      setActiveSource(sourceField);
      setActiveDestination(destField);
    } else {
      setActiveSource(null);
      setActiveDestination(null);
    }
  };

  // Cập nhật các cột hiển thị trong preview
  const updatePreviewColumns = (destField: string, sourceField: string) => {
    setPreviewColumns((prev) => {
      // Nếu destField đã có trong preview, thay thế sourceField
      if (prev.includes(destField)) {
        return prev;
      }
      // Nếu chưa có, thêm mới
      return [...prev, destField];
    });
  };

  // Xử lý khi chọn template
  const handleSelectTemplate = (template: Template) => {
    // Áp dụng mapping
    setMapping(template.mapping);

    // Cập nhật preview columns
    setPreviewColumns(Object.keys(template.mapping));
  };

  // Xử lý khi lưu template
  const handleSaveTemplate = async () => {
    if (!templateName) {
      toast.error(t('import-export:template_name_required'));
      return;
    }

    if (Object.keys(mapping).length === 0) {
      toast.error(t('import-export:mapping_required'));
      return;
    }

    try {
      // Lưu template
      await saveTemplate(selectedResource, accountId, templateName, mapping);

      // Cập nhật danh sách templates
      const templatesList = await getTemplates(selectedResource, accountId);
      setTemplates(templatesList);

      // Reset form
      setTemplateName('');
      setShowSaveTemplate(false);

      toast.success(t('import-export:template_saved'));
    } catch (error: any) {
      toast.error(t('import-export:template_save_error'));
      console.error('Error saving template:', error);
    }
  };

  // Xử lý khi nhập dữ liệu
  const handleImport = async () => {
    if (Object.keys(mapping).length === 0) {
      toast.error(t('import-export:select_mapping'));
      return;
    }

    setIsLoading(true);
    setImportStats({
      total: totalRows,
      success: 0,
      failed: 0,
      inProgress: true,
    });
    setImportProgress(0);

    try {
      // Xác thực dữ liệu trước khi nhập
      const validation = await validateImportData(
        selectedResource,
        fileData,
        mapping,
      );

      if (!validation.valid) {
        toast.error(
          validation.errors?.[0] || t('import-export:validation_error'),
        );
        setIsLoading(false);
        setImportStats((prev) => ({ ...prev, inProgress: false }));
        return;
      }

      // Hiển thị cảnh báo nếu có
      if (validation.warnings && validation.warnings.length > 0) {
        // Hiển thị tối đa 3 cảnh báo
        const warningsToShow = validation.warnings.slice(0, 3);
        const remainingWarnings = validation.warnings.length - 3;

        warningsToShow.forEach((warning) => {
          toast.warning(warning, { duration: 5000 });
        });

        if (remainingWarnings > 0) {
          toast.warning(
            t('import-export:more_warnings', { count: remainingWarnings }),
            { duration: 5000 },
          );
        }
      }

      // Nếu số lượng dòng nhỏ, sử dụng importData
      if (totalRows <= batchSize) {
        // Gọi API để nhập dữ liệu
        const result = await importData(
          selectedResource,
          fileData,
          mapping,
          accountId,
          batchSize,
        );

        setImportStats({
          total: totalRows,
          success: result.count,
          failed: result.failedRows || 0,
          inProgress: false,
        });
        setImportProgress(100);

        if (result.success) {
          toast.success(
            t('import-export:import_success_count', {
              count: result.count,
            }),
          );
        } else {
          toast.error(
            t('import-export:import_partial_success', {
              success: result.count,
              failed: result.failedRows,
            }),
          );
        }

        // Hiển thị dialog thành công
        setShowSuccessDialog(true);
      } else {
        // Sử dụng processBatches để xử lý file lớn
        await processBatches(
          async (batch, batchIndex) => {
            try {
              // Nhập từng batch
              const result = await importBatch(
                selectedResource,
                batch,
                mapping,
                accountId,
                batchIndex,
              );

              // Cập nhật stats
              setImportStats((prev) => ({
                ...prev,
                success: prev.success + (result.count || 0),
                failed: prev.failed + (result.failedRows || 0),
              }));

              return result;
            } catch (error) {
              console.error(`Error importing batch ${batchIndex}:`, error);
              // Cập nhật stats cho batch lỗi
              setImportStats((prev) => ({
                ...prev,
                failed: prev.failed + batch.length,
              }));
              return { success: false, count: 0, failedRows: batch.length };
            }
          },
          {
            batchSize,
            onProgress: (progress) => {
              setImportProgress(progress);
            },
          },
        );

        // Kết thúc import
        setImportStats((prev) => ({ ...prev, inProgress: false }));

        // Hiển thị thông báo
        if (importStats.failed === 0) {
          toast.success(
            t('import-export:import_success_count', {
              count: importStats.success,
            }),
          );
        } else {
          toast.error(
            t('import-export:import_partial_success', {
              success: importStats.success,
              failed: importStats.failed,
            }),
          );
        }

        // Hiển thị dialog thành công
        setShowSuccessDialog(true);
      }
    } catch (error: any) {
      toast.error(t('import-export:import_error'));
      console.error('Import error:', error);
      setImportStats((prev) => ({ ...prev, inProgress: false }));
    } finally {
      setIsLoading(false);
    }
  };

  // Tạo dữ liệu preview dựa trên mapping
  const getPreviewData = () => {
    if (!fileData || !Array.isArray(fileData) || fileData.length === 0) {
      return [];
    }

    return fileData.slice(0, 5).map((row) => {
      if (!row) return {};

      const mappedRow: Record<string, any> = {};

      Object.entries(mapping).forEach(([destField, sourceField]) => {
        if (sourceField) {
          mappedRow[destField] = row[sourceField];
        }
      });

      return mappedRow;
    });
  };

  return (
    <>
      <div className="space-y-6">
        <div className="space-y-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h2 className="mb-1 text-xl font-semibold tracking-tight">
                <Trans i18nKey="import-export:import_title">Import Data</Trans>
              </h2>
              <p className="text-muted-foreground text-sm">
                <Trans i18nKey="import-export:import_description">
                  Upload a CSV or Excel file and map fields to import
                </Trans>
              </p>
            </div>
          </div>

          <div className="bg-card/50 border-border/40 rounded-lg border p-6 shadow-sm md:p-8">
            <div className="space-y-4">
              <div className="mb-8 rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950/30">
                <h3 className="mb-2 flex items-center gap-2 text-sm font-medium text-blue-800 dark:text-blue-300">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <path d="M12 16v-4" />
                    <path d="M12 8h.01" />
                  </svg>
                  <Trans i18nKey="import-export:import_guide_title">
                    How to import data
                  </Trans>
                </h3>
                <ol className="ml-5 list-decimal space-y-2 text-sm text-blue-700 dark:text-blue-400">
                  <li>
                    <Trans i18nKey="import-export:import_guide_step1">
                      Select the resource type you want to import (Products,
                      Categories, etc.)
                    </Trans>
                  </li>
                  <li>
                    <Trans i18nKey="import-export:import_guide_step2">
                      Upload a CSV or Excel file containing your data
                    </Trans>
                  </li>
                  <li>
                    <Trans i18nKey="import-export:import_guide_step3">
                      Map the fields from your file to the database fields by
                      dragging them
                    </Trans>
                  </li>
                  <li>
                    <Trans i18nKey="import-export:import_guide_step4">
                      Preview the data and click Import to complete the process
                    </Trans>
                  </li>
                </ol>
              </div>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    <Trans i18nKey="import-export:select_resource">
                      Select Resource
                    </Trans>
                  </label>
                  <Select
                    value={selectedResource}
                    onValueChange={(value) =>
                      handleResourceChange(value as Resource)
                    }
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue
                        placeholder={t('import-export:select_resource')}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="products">
                        {t('import-export:products')}
                      </SelectItem>
                      <SelectItem value="categories">
                        {t('import-export:categories')}
                      </SelectItem>
                      <SelectItem value="orders">
                        {t('import-export:orders')}
                      </SelectItem>
                      <SelectItem value="customers">
                        {t('import-export:customers')}
                      </SelectItem>
                      <SelectItem value="vouchers">
                        {t('import-export:vouchers')}
                      </SelectItem>
                      <SelectItem value="analytics">
                        {t('import-export:analytics')}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="mt-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    <Trans i18nKey="import-export:upload_file">
                      Upload File
                    </Trans>
                  </label>
                  <div className="flex flex-col gap-2">
                    <div className="hover:bg-muted/30 relative cursor-pointer rounded-md border border-dashed p-6 text-center transition-colors">
                      <Input
                        type="file"
                        accept=".csv,.xlsx,.xls"
                        onChange={(e) =>
                          handleFileChange(e, { maxPreviewRows: 1000 })
                        }
                        disabled={isFileLoading}
                        className="absolute inset-0 z-10 h-full w-full cursor-pointer opacity-0"
                      />
                      <div className="space-y-2">
                        <div className="flex justify-center">
                          <div className="bg-muted/50 rounded-full p-3">
                            <ArrowDownToLine className="text-muted-foreground h-5 w-5" />
                          </div>
                        </div>
                        <div className="text-sm font-medium">
                          <Trans i18nKey="import-export:drop_file">
                            Drop your file here or click to browse
                          </Trans>
                        </div>
                        <p className="text-muted-foreground text-xs">
                          <Trans i18nKey="import-export:supported_formats">
                            Supported formats: CSV, Excel (.xlsx, .xls)
                          </Trans>
                        </p>
                      </div>
                    </div>

                    {file && (
                      <div className="bg-card/80 flex items-center gap-2 rounded-md border p-2">
                        <div className="flex-1 truncate">
                          <span className="text-sm font-medium">
                            {file.name}
                          </span>
                          <span className="text-muted-foreground ml-2 text-xs">
                            {(file.size / 1024).toFixed(1)} KB
                          </span>
                        </div>
                        {totalRows > 0 && (
                          <span className="bg-primary/10 text-primary rounded-full px-2 py-1 text-xs font-medium">
                            {totalRows} rows
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  {fileError && (
                    <div className="bg-destructive/10 text-destructive mt-2 rounded-md p-3 text-sm">
                      <div className="flex items-start gap-2">
                        <div className="bg-destructive/20 mt-0.5 rounded-full p-1">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="14"
                            height="14"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M18 6 6 18" />
                            <path d="m6 6 12 12" />
                          </svg>
                        </div>
                        <div>
                          <strong>Error:</strong> {fileError}
                          <p className="mt-1 text-xs">
                            Make sure your CSV file has headers and is properly
                            formatted. If you exported this file from the
                            system, try using the Excel format instead.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {isFileLoading && fileProgress > 0 && fileProgress < 100 && (
                    <div className="mt-2">
                      <Progress value={fileProgress} className="h-2" />
                      <p className="text-muted-foreground mt-1 text-xs">
                        {t('import-export:processing_file', {
                          progress: fileProgress,
                        })}
                      </p>
                    </div>
                  )}

                  {fileType === 'excel' && availableSheets.length > 0 && (
                    <div className="mt-4 space-y-2">
                      <label className="text-sm font-medium">
                        <Trans i18nKey="import-export:select_sheet">
                          Select Sheet
                        </Trans>
                      </label>
                      <Select value={selectedSheet} onValueChange={changeSheet}>
                        <SelectTrigger className="w-full md:w-[200px]">
                          <SelectValue
                            placeholder={t('import-export:select_sheet')}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          {availableSheets.map((sheet) => (
                            <SelectItem key={sheet} value={sheet}>
                              {sheet}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </div>
              {templates.length > 0 && (
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="templates">
                    <AccordionTrigger>
                      <Trans i18nKey="import-export:saved_templates">
                        Saved Templates
                      </Trans>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2">
                        {templates.map((template) => (
                          <div
                            key={template.id}
                            className="flex items-center justify-between rounded-md border p-2"
                          >
                            <span>{template.name}</span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSelectTemplate(template)}
                            >
                              <Trans i18nKey="import-export:use_template">
                                Use
                              </Trans>
                            </Button>
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              )}
              {(destinationFields.length > 0 || isInitialLoading) && (
                <div className="mt-8 border-t pt-8">
                  <h3 className="mb-4 text-base font-medium">
                    <Trans i18nKey="import-export:field_mapping_title">
                      Field Mapping
                    </Trans>
                  </h3>
                  <p className="text-muted-foreground mb-6 text-sm">
                    {file && sourceFields.length > 0 ? (
                      <Trans i18nKey="import-export:field_mapping_description">
                        Drag fields from the left (your file) to the right
                        (database) to create mappings. Required fields are
                        marked with an asterisk (*).
                      </Trans>
                    ) : (
                      <Trans i18nKey="import-export:field_mapping_no_file">
                        Upload a file first to map fields. You'll be able to
                        drag fields from your file to these database fields.
                      </Trans>
                    )}
                  </p>

                  <DragDropContext
                    onDragStart={onDragStart}
                    onDragUpdate={onDragUpdate}
                    onDragEnd={onDragEnd}
                  >
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="text-muted-foreground text-sm font-medium">
                            <Trans i18nKey="import-export:source_fields">
                              Source Fields (from file)
                            </Trans>
                          </h4>
                          {file && sourceFields.length > 0 && (
                            <span className="text-muted-foreground text-xs">
                              {sourceFields.length}{' '}
                              {sourceFields.length === 1 ? 'field' : 'fields'}
                            </span>
                          )}
                        </div>
                        <Droppable droppableId="source">
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.droppableProps}
                              className={`min-h-[300px] rounded-md border ${snapshot.isDraggingOver ? 'border-2 border-blue-400 bg-blue-50/50 dark:bg-blue-950/20' : 'border-dashed'} p-4 transition-all duration-300`}
                            >
                              {file && sourceFields.length > 0 ? (
                                <div className="space-y-2">
                                  {sourceFields.map((field, index) => (
                                    <DraggableField
                                      key={field}
                                      id={`source-${field}`}
                                      index={index}
                                      field={field}
                                      className="transition-colors duration-200 hover:border-blue-400/70 hover:bg-blue-50/30 dark:hover:bg-blue-950/10"
                                    />
                                  ))}
                                </div>
                              ) : (
                                <div className="flex h-full items-center justify-center">
                                  <div className="text-muted-foreground text-center text-sm">
                                    <div className="bg-muted/50 mx-auto mb-2 rounded-full p-3">
                                      <ArrowDownToLine className="text-muted-foreground h-5 w-5" />
                                    </div>
                                    <p>
                                      <Trans i18nKey="import-export:upload_file_first">
                                        Upload a file first to see source fields
                                      </Trans>
                                    </p>
                                  </div>
                                </div>
                              )}
                              {provided.placeholder}
                            </div>
                          )}
                        </Droppable>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="text-muted-foreground text-sm font-medium">
                            <Trans i18nKey="import-export:destination_fields">
                              Destination Fields (database)
                            </Trans>
                          </h4>
                          {!isInitialLoading && (
                            <span className="text-muted-foreground text-xs">
                              {t('import-export:fields_mapped', {
                                count: Object.keys(mapping).length,
                                total: destinationFields.length,
                              })}
                            </span>
                          )}
                        </div>
                        <Droppable droppableId="destination">
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.droppableProps}
                              className={`min-h-[300px] rounded-md border ${snapshot.isDraggingOver ? 'border-primary bg-primary/10 border-2 shadow-inner' : 'border-dashed'} p-4 transition-all duration-300`}
                            >
                              {isInitialLoading ? (
                                <div className="flex h-full items-center justify-center">
                                  <div className="text-center">
                                    <div className="border-primary inline-block h-8 w-8 animate-spin rounded-full border-2 border-t-transparent"></div>
                                    <p className="text-muted-foreground mt-2 text-sm">
                                      <Trans i18nKey="import-export:loading_fields">
                                        Loading database fields...
                                      </Trans>
                                    </p>
                                  </div>
                                </div>
                              ) : (
                                <div className="space-y-2">
                                  {destinationFields.map((field, index) => {
                                    // Kiểm tra xem trường có bắt buộc không
                                    const isRequired = [
                                      'name',
                                      'price',
                                      'email',
                                      'code',
                                      'discount_type',
                                      'discount_value',
                                      'date',
                                    ].includes(field);

                                    // Nếu trường đã được ánh xạ, hiển thị MappedField
                                    if (mapping[field]) {
                                      return (
                                        <MappedField
                                          key={field}
                                          sourceField={mapping[field]}
                                          destinationField={
                                            isRequired ? `${field} *` : field
                                          }
                                          onRemove={() => {
                                            // Xóa mapping cho trường này
                                            const newMapping = { ...mapping };
                                            delete newMapping[field];
                                            setMapping(newMapping);

                                            // Cập nhật preview columns nếu cần
                                            setPreviewColumns((prev) =>
                                              prev.filter(
                                                (col) => col !== field,
                                              ),
                                            );

                                            // Hiển thị thông báo
                                            toast.success(
                                              t(
                                                'import-export:mapping_removed',
                                                {
                                                  field: field,
                                                },
                                              ),
                                              {
                                                duration: 2000,
                                              },
                                            );
                                          }}
                                        />
                                      );
                                    }

                                    // Nếu chưa ánh xạ, hiển thị DraggableField
                                    return (
                                      <div className="relative" key={field}>
                                        <DraggableField
                                          id={`destination-${field}`}
                                          index={index}
                                          field={
                                            isRequired ? `${field} *` : field
                                          }
                                          description={
                                            isRequired
                                              ? t(
                                                  'import-export:required_field_instruction',
                                                )
                                              : t(
                                                  'import-export:optional_field_instruction',
                                                )
                                          }
                                          className={`${isRequired ? 'border-amber-300 dark:border-amber-700' : ''} hover:border-primary/70 hover:bg-primary/5 transition-colors duration-200`}
                                        />
                                        <DroppableFieldIndicator
                                          isOver={
                                            activeDestination === field &&
                                            snapshot.isDraggingOver
                                          }
                                          sourceField={activeSource}
                                          destinationField={field}
                                        />
                                      </div>
                                    );
                                  })}
                                </div>
                              )}
                              {provided.placeholder}
                            </div>
                          )}
                        </Droppable>
                      </div>
                    </div>
                  </DragDropContext>
                </div>
              )}
              {showSaveTemplate && (
                <div className="rounded-md border p-4">
                  <h3 className="mb-2 text-sm font-medium">
                    <Trans i18nKey="import-export:save_template">
                      Save as Template
                    </Trans>
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="template-name">
                        <Trans i18nKey="import-export:template_name">
                          Template Name
                        </Trans>
                      </Label>
                      <Input
                        id="template-name"
                        value={templateName}
                        onChange={(e) => setTemplateName(e.target.value)}
                        placeholder={t(
                          'import-export:template_name_placeholder',
                        )}
                      />
                    </div>
                    <Button onClick={handleSaveTemplate}>
                      <Trans i18nKey="import-export:save">Save</Trans>
                    </Button>
                  </div>
                </div>
              )}
              {Object.keys(mapping).length > 0 && (
                <div className="mt-8 border-t pt-8">
                  <div className="mb-4 flex items-center justify-between">
                    <h3 className="text-base font-medium">
                      <Trans i18nKey="import-export:preview">
                        Data Preview
                      </Trans>
                    </h3>
                    <div className="text-muted-foreground text-sm">
                      <Trans
                        i18nKey="import-export:preview_description"
                        values={{ count: Math.min(5, fileData.length) }}
                      >
                        Showing first {Math.min(5, fileData.length)} rows
                      </Trans>
                    </div>
                  </div>
                  <div className="bg-card/30 rounded-md border p-4">
                    <PreviewTable
                      data={getPreviewData()}
                      columns={previewColumns}
                      maxRows={5}
                    />
                  </div>
                </div>
              )}
              {totalRows > batchSize && Object.keys(mapping).length > 0 && (
                <div className="mt-8 border-t pt-8">
                  <h3 className="mb-4 text-base font-medium">
                    <Trans i18nKey="import-export:batch_settings">
                      Batch Import Settings
                    </Trans>
                  </h3>
                  <div className="mb-4 rounded-md border border-amber-200 bg-amber-50 p-4 dark:border-amber-800 dark:bg-amber-950/30">
                    <div className="mb-2 flex items-center gap-2 text-sm text-amber-800 dark:text-amber-300">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z" />
                        <path d="M12 9v4" />
                        <path d="M12 17h.01" />
                      </svg>
                      <span className="font-medium">
                        <Trans i18nKey="import-export:large_file_warning">
                          Large File Detected
                        </Trans>
                      </span>
                    </div>
                    <p className="text-sm text-amber-700 dark:text-amber-400">
                      <Trans i18nKey="import-export:large_file_description">
                        Your file contains {totalRows} rows and will be
                        processed in batches for better performance.
                      </Trans>
                    </p>
                  </div>

                  <div className="bg-card/30 grid grid-cols-1 gap-6 rounded-md border p-4 md:grid-cols-2">
                    <div>
                      <Label
                        htmlFor="batch-size"
                        className="mb-2 block text-sm font-medium"
                      >
                        <Trans i18nKey="import-export:batch_size">
                          Batch Size
                        </Trans>
                      </Label>
                      <div className="flex items-center gap-4">
                        <Input
                          id="batch-size"
                          type="number"
                          min={10}
                          max={1000}
                          value={batchSize}
                          onChange={(e) =>
                            setBatchSize(parseInt(e.target.value) || 100)
                          }
                          className="w-[150px]"
                        />
                        <div className="text-muted-foreground text-sm">
                          <Trans i18nKey="import-export:batch_size_description">
                            Number of rows to process at once
                          </Trans>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="mb-2 text-sm font-medium">
                        <Trans i18nKey="import-export:batch_summary">
                          Processing Summary
                        </Trans>
                      </h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">
                            <Trans i18nKey="import-export:total_rows">
                              Total Rows
                            </Trans>
                            :
                          </span>
                          <span className="font-medium">{totalRows}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">
                            <Trans i18nKey="import-export:batch_count">
                              Number of Batches
                            </Trans>
                            :
                          </span>
                          <span className="font-medium">
                            {Math.ceil(totalRows / batchSize)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">
                            <Trans i18nKey="import-export:estimated_time">
                              Estimated Time
                            </Trans>
                            :
                          </span>
                          <span className="font-medium">
                            {Math.ceil(totalRows / batchSize) < 10
                              ? 'Less than a minute'
                              : 'A few minutes'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {importStats.inProgress && (
                <div className="mt-8 border-t pt-8">
                  <h3 className="mb-4 text-base font-medium">
                    <Trans i18nKey="import-export:import_progress">
                      Import Progress
                    </Trans>
                  </h3>
                  <div className="bg-card/30 rounded-md border p-6">
                    <div className="mb-6">
                      <div className="mb-2 flex justify-between">
                        <span className="text-sm font-medium">
                          {importProgress}%
                        </span>
                        <span className="text-muted-foreground text-sm">
                          <Trans
                            i18nKey="import-export:processed"
                            values={{
                              processed:
                                importStats.success + importStats.failed,
                              total: importStats.total,
                            }}
                          >
                            {importStats.success + importStats.failed} of{' '}
                            {importStats.total} rows
                          </Trans>
                        </span>
                      </div>
                      <Progress
                        value={importProgress}
                        className="h-3 rounded-md"
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div className="flex items-center justify-between rounded-md border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-950/30">
                        <div className="flex items-center gap-2">
                          <div className="rounded-full bg-green-100 p-1.5 dark:bg-green-900">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-green-600 dark:text-green-400"
                            >
                              <path d="M20 6 9 17l-5-5" />
                            </svg>
                          </div>
                          <span className="text-sm font-medium text-green-800 dark:text-green-300">
                            <Trans i18nKey="import-export:success_count">
                              Successful
                            </Trans>
                          </span>
                        </div>
                        <span className="text-xl font-bold text-green-700 dark:text-green-400">
                          {importStats.success}
                        </span>
                      </div>

                      <div className="flex items-center justify-between rounded-md border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-950/30">
                        <div className="flex items-center gap-2">
                          <div className="rounded-full bg-red-100 p-1.5 dark:bg-red-900">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-red-600 dark:text-red-400"
                            >
                              <path d="M18 6 6 18" />
                              <path d="m6 6 12 12" />
                            </svg>
                          </div>
                          <span className="text-sm font-medium text-red-800 dark:text-red-300">
                            <Trans i18nKey="import-export:failed_count">
                              Failed
                            </Trans>
                          </span>
                        </div>
                        <span className="text-xl font-bold text-red-700 dark:text-red-400">
                          {importStats.failed}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              <div className="mt-8 border-t pt-8">
                <div className="flex flex-col-reverse gap-4 sm:flex-row sm:items-center sm:justify-between">
                  <div className="flex gap-2">
                    {Object.keys(mapping).length > 0 && (
                      <Button
                        variant="outline"
                        onClick={() => setShowSaveTemplate(!showSaveTemplate)}
                        disabled={isLoading || importStats.inProgress}
                      >
                        {showSaveTemplate ? (
                          <Trans i18nKey="import-export:cancel">Cancel</Trans>
                        ) : (
                          <>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="mr-2"
                            >
                              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
                              <polyline points="17 21 17 13 7 13 7 21" />
                              <polyline points="7 3 7 8 15 8" />
                            </svg>
                            <Trans i18nKey="import-export:save_as_template">
                              Save as Template
                            </Trans>
                          </>
                        )}
                      </Button>
                    )}
                  </div>

                  <div className="flex gap-2">
                    {file && (
                      <Button
                        variant="outline"
                        onClick={() => {
                          resetFileUpload();
                          setMapping({});
                          setPreviewColumns([]);
                        }}
                        disabled={isLoading || importStats.inProgress}
                      >
                        <Trans i18nKey="import-export:reset">Reset</Trans>
                      </Button>
                    )}

                    <Button
                      onClick={handleImport}
                      disabled={
                        Object.keys(mapping).length === 0 ||
                        isLoading ||
                        isFileLoading ||
                        importStats.inProgress
                      }
                      loading={isLoading || importStats.inProgress}
                      className="min-w-[120px]"
                    >
                      {!isLoading && !importStats.inProgress && (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="mr-2"
                        >
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                          <polyline points="17 8 12 3 7 8" />
                          <line x1="12" x2="12" y1="3" y2="15" />
                        </svg>
                      )}
                      <Trans i18nKey="import-export:import">Import</Trans>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dialog thành công */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              <Trans i18nKey="import-export:import_success_title">
                Import Successful
              </Trans>
            </DialogTitle>
            <DialogDescription>
              <Trans i18nKey="import-export:import_success_description">
                Your data has been successfully imported. What would you like to
                do next?
              </Trans>
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col gap-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="rounded-full bg-green-100 p-1.5 dark:bg-green-900">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-green-600 dark:text-green-400"
                  >
                    <path d="M20 6 9 17l-5-5" />
                  </svg>
                </div>
                <span className="text-sm font-medium">
                  <Trans
                    i18nKey="import-export:imported_items"
                    count={importStats.success}
                  >
                    Successfully imported {{ count: importStats.success }} items
                  </Trans>
                </span>
              </div>
            </div>

            {importStats.failed > 0 && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="rounded-full bg-red-100 p-1.5 dark:bg-red-900">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-red-600 dark:text-red-400"
                    >
                      <path d="M18 6 6 18" />
                      <path d="m6 6 12 12" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium text-red-800 dark:text-red-300">
                    <Trans
                      i18nKey="import-export:failed_items"
                      count={importStats.failed}
                    >
                      Failed to import {{ count: importStats.failed }} items
                    </Trans>
                  </span>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowSuccessDialog(false);
                resetFileUpload();
                setMapping({});
                setPreviewColumns([]);
              }}
            >
              <Trans i18nKey="import-export:import_more">
                Import More Data
              </Trans>
            </Button>
            <Button
              onClick={() => {
                router.push(`/home/<USER>/${selectedResource}`);
              }}
            >
              <Trans
                i18nKey="import-export:go_to_resource"
                values={{ resource: t(`import-export:${selectedResource}`) }}
              >
                Go to {{ resource: selectedResource }}
              </Trans>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
