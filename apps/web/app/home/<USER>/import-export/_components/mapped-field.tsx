'use client';

import { cn } from '@kit/ui/utils';
import { ArrowRight, Check, X } from 'lucide-react';
import { Button } from '@kit/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@kit/ui/tooltip';
import { Trans } from '@kit/ui/trans';

// Sử dụng các class Tailwind thay vì CSS tùy chỉnh

interface MappedFieldProps {
  sourceField: string;
  destinationField: string;
  className?: string;
  onRemove?: () => void;
}

/**
 * Component hiển thị trường đã được ánh xạ
 */
export function MappedField({
  sourceField,
  destinationField,
  className,
  onRemove,
}: MappedFieldProps) {
  return (
    <div
      className={cn(
        'flex items-center gap-2 rounded-md border-2 border-green-400 dark:border-green-600 bg-green-50 dark:bg-green-950/30 p-3 shadow-md animate-in fade-in-0 duration-300 group',
        className
      )}
      data-testid={`mapped-field-${destinationField}`}
    >
      <div className="rounded-full bg-green-100 dark:bg-green-900 p-1 text-green-600 dark:text-green-400">
        <Check className="h-3 w-3" />
      </div>

      <div className="flex flex-1 items-center gap-2 min-w-0 text-sm">
        <div className="font-medium truncate max-w-[120px]">{sourceField}</div>
        <ArrowRight className="h-3 w-3 text-muted-foreground flex-shrink-0" />
        <div className="font-medium truncate max-w-[120px]">{destinationField}</div>
      </div>

      {onRemove && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={onRemove}
                data-testid={`remove-mapping-${destinationField}`}
              >
                <X className="h-3.5 w-3.5 text-muted-foreground hover:text-destructive" />
                <span className="sr-only">
                  <Trans i18nKey="import-export:remove_mapping">Remove mapping</Trans>
                </span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <Trans i18nKey="import-export:remove_mapping">Remove mapping</Trans>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}
