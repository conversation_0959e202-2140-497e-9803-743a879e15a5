import { Suspense } from 'react';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { SearchListInput } from '@kit/ui/search-list-input';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { loadTeamWorkspace } from '../_lib/server/team-account-workspace.loader';
import { IntegrationCard } from './_components/integration-card';
import { loadIntegrations } from './_lib/server/integrations.loader';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  return { title: i18n.t('integrations:pageTitle') };
};

interface PageSearchParams {
  page?: string;
  query?: string;
}

async function IntegrationsPage({
  params,
  searchParams,
}: {
  params: Promise<{ account: string }>;
  searchParams: Promise<PageSearchParams>;
}) {
  const client = getSupabaseServerClient();
  const [resolvedParams, resolvedSearchParams] = await Promise.all([
    params,
    searchParams,
  ]);
  const accountSlug = resolvedParams.account;
  const searchQuery = resolvedSearchParams.query || '';
  const api = createTeamAccountsApi(client);

  try {
    const teamAccount = await api.getTeamAccount(accountSlug);
    if (!teamAccount) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    const [{ account }, integrations] = await Promise.all([
      loadTeamWorkspace(accountSlug),
      loadIntegrations(teamAccount.id),
    ]);

    if (!account) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    // Filter integrations based on search query
    const filteredIntegrations = searchQuery
      ? integrations.filter(
          (int) =>
            int.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            int.description?.toLowerCase().includes(searchQuery.toLowerCase()),
        )
      : integrations;
    return (
      <>
        <TeamAccountLayoutPageHeader
          title={<Trans i18nKey="integrations:pageTitle">Integrations</Trans>}
          description={<AppBreadcrumbs />}
          account={account.slug}
        />

        <PageBody>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle>
              <SearchListInput
                defaultValue={searchQuery}
                placeholder="Search integrations..."
              />
            </CardTitle>
          </CardHeader>

          <CardContent>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Suspense fallback={<div>Loading integrations...</div>}>
                {filteredIntegrations.map((integration) => (
                  <IntegrationCard
                    key={integration.type}
                    integration={integration}
                    accountSlug={account.slug}
                    accountId={account.id}
                  />
                ))}
              </Suspense>
            </div>
          </CardContent>
        </PageBody>
      </>
    );
  } catch (error: any) {
    console.error('Error loading integrations page:', error);
    return (
      <PageBody>
        <Alert variant="destructive">
          <AlertDescription>
            <Trans i18nKey="common:errors:loadingFailed">
              Failed to load data. Please try again later.
            </Trans>
          </AlertDescription>
        </Alert>
      </PageBody>
    );
  }
}

export default withI18n(IntegrationsPage);
