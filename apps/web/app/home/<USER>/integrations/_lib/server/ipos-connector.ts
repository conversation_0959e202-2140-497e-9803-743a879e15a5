'use server';

import { getLogger } from '@kit/shared/logger';
import { IPOSCredentials } from './ipos-connector-server';
import {
  authenticateIPOS,
  getIPOSProducts,
  getIPOSOrders,
  getIPOSCustomers,
  getIPOSProductById,
  getIPOSOrderById,
  getIPOSCustomerById,
  createIPOSOrder,
  updateIPOSOrderStatus,
  getIPOSCategories,
  getIPOSBranches,
  getIPOSVouchers,
  getIPOSVoucherById,
} from './ipos-connector-server';

/**
 * iPOS Connector
 * Lớp này cung cấp các phương thức để tương tác với iPOS API
 */
export class IPOSConnector {
  private credentials: IPOSCredentials;
  private logger: any;

  constructor(credentials: IPOSCredentials) {
    this.credentials = credentials;
    this.initLogger();
  }

  private async initLogger() {
    this.logger = await getLogger();
  }

  /**
   * <PERSON><PERSON><PERSON> thực với iPOS API
   */
  async authenticate(): Promise<boolean> {
    return await authenticateIPOS(this.credentials);
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách sản phẩm từ iPOS
   */
  async getProducts(params: Record<string, any> = {}): Promise<any[]> {
    return await getIPOSProducts(this.credentials, params);
  }

  /**
   * Lấy chi tiết sản phẩm từ iPOS
   */
  async getProductById(productId: string): Promise<any> {
    return await getIPOSProductById(this.credentials, productId);
  }

  /**
   * Lấy danh sách đơn hàng từ iPOS
   */
  async getOrders(params: Record<string, any> = {}): Promise<any[]> {
    return await getIPOSOrders(this.credentials, params);
  }

  /**
   * Lấy chi tiết đơn hàng từ iPOS
   */
  async getOrderById(orderId: string): Promise<any> {
    return await getIPOSOrderById(this.credentials, orderId);
  }

  /**
   * Lấy danh sách khách hàng từ iPOS
   */
  async getCustomers(params: Record<string, any> = {}): Promise<any[]> {
    return await getIPOSCustomers(this.credentials, params);
  }

  /**
   * Lấy chi tiết khách hàng từ iPOS
   */
  async getCustomerById(customerId: string): Promise<any> {
    return await getIPOSCustomerById(this.credentials, customerId);
  }

  /**
   * Lấy danh sách danh mục từ iPOS
   */
  async getCategories(params: Record<string, any> = {}): Promise<any[]> {
    return await getIPOSCategories(this.credentials, params);
  }

  /**
   * Lấy danh sách chi nhánh từ iPOS
   */
  async getBranches(params: Record<string, any> = {}): Promise<any[]> {
    return await getIPOSBranches(this.credentials, params);
  }

  /**
   * Lấy danh sách voucher từ iPOS
   */
  async getVouchers(params: Record<string, any> = {}): Promise<any[]> {
    return await getIPOSVouchers(this.credentials, params);
  }

  /**
   * Lấy chi tiết voucher từ iPOS
   */
  async getVoucherById(voucherId: string): Promise<any> {
    return await getIPOSVoucherById(this.credentials, voucherId);
  }

  /**
   * Tạo đơn hàng mới trên iPOS
   */
  async createOrder(orderData: any): Promise<any> {
    return await createIPOSOrder(this.credentials, orderData);
  }

  /**
   * Cập nhật trạng thái đơn hàng trên iPOS
   */
  async updateOrderStatus(orderId: string, status: string): Promise<any> {
    return await updateIPOSOrderStatus(this.credentials, orderId, status);
  }

  /**
   * Lấy thông tin giao dịch từ iPOS
   */
  async getTransactions(params: Record<string, any> = {}): Promise<any[]> {
    try {
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/transactions', 'GET', params);
      return data.data || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching transactions from iPOS');
      throw error;
    }
  }

  /**
   * Lấy báo cáo doanh thu từ iPOS
   */
  async getReports(params: Record<string, any> = {}): Promise<any> {
    try {
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/reports/revenue', 'GET', params);
      return data.data || {};
    } catch (error) {
      this.logger.error({ error }, 'Error fetching reports from iPOS');
      throw error;
    }
  }

  /**
   * Lấy thông tin bàn từ iPOS
   */
  async getTables(params: Record<string, any> = {}): Promise<any[]> {
    try {
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/tables', 'GET', params);
      return data.data || [];
    } catch (error) {
      this.logger.error({ error }, 'Error fetching tables from iPOS');
      throw error;
    }
  }

  /**
   * Đặt bàn trên iPOS
   */
  async reserveTable(tableData: any): Promise<any> {
    try {
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/tables/reserve', 'POST', tableData);
      return data.data || {};
    } catch (error) {
      this.logger.error({ error }, 'Error reserving table on iPOS');
      throw error;
    }
  }

  /**
   * Lấy thông tin tồn kho từ iPOS
   */
  async getInventory(params: Record<string, any> = {}): Promise<any> {
    try {
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/inventory', 'GET', params);
      return data.data || {};
    } catch (error) {
      this.logger.error({ error }, 'Error fetching inventory from iPOS');
      throw error;
    }
  }

  /**
   * Xử lý thanh toán trên iPOS
   */
  async processPayment(paymentData: any): Promise<any> {
    try {
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/payments', 'POST', paymentData);
      return data.data || {};
    } catch (error) {
      this.logger.error({ error }, 'Error processing payment on iPOS');
      throw error;
    }
  }

  /**
   * Kiểm tra voucher trên iPOS
   */
  async checkVoucher(voucherData: any): Promise<any> {
    try {
      const data = await this.callIPOSAPI('/ipos/ws/xpartner/v2/vouchers/check', 'POST', voucherData);
      return data.data || {};
    } catch (error) {
      this.logger.error({ error }, 'Error checking voucher on iPOS');
      throw error;
    }
  }

  /**
   * Gọi API iPOS
   */
  private async callIPOSAPI(endpoint: string, method: string = 'GET', body?: any): Promise<any> {
    try {
      const { access_token, pos_parent, pos_id } = this.credentials;
      const baseUrl = this.credentials.baseUrl || 'https://api.foodbook.vn';

      // Xây dựng URL với các tham số cần thiết
      let url = `${baseUrl}${endpoint}`;

      // Log URL để debug
      this.logger.info({ url, method }, 'Calling iPOS API');

      // Chuẩn bị body request theo đúng tài liệu API
      let requestBody: any = {};

      // Thêm các tham số xác thực vào body
      requestBody.access_token = access_token;
      requestBody.pos_parent = pos_parent;
      requestBody.pos_id = pos_id;

      // Thêm các tham số khác từ body
      if (body) {
        requestBody = { ...requestBody, ...body };
      }

      // Chuẩn bị options cho fetch
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      };

      // Xử lý các tham số cho GET request
      if (method === 'GET') {
        // Chuyển đổi requestBody thành query string
        const params = new URLSearchParams();
        for (const key in requestBody) {
          params.append(key, requestBody[key]);
        }
        url = `${url}?${params.toString()}`;
      } else {
        // Thêm body cho các request không phải GET
        options.body = JSON.stringify(requestBody);
      }

      const response = await fetch(url, options);

      // Kiểm tra lỗi từ API
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`iPOS API request failed: ${errorText}`);
      }

      // Parse JSON
      const jsonResponse = await response.json();
      return jsonResponse;
    } catch (error) {
      this.logger.error({ error, endpoint }, 'Error calling iPOS API');
      throw error;
    }
  }
}
