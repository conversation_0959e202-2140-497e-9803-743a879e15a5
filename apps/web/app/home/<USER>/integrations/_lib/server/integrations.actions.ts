'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function toggleIntegration(
  accountId: string,
  integrationId: string,
  enabled: boolean,
) {
  const supabase = getSupabaseServerClient();

  const { error } = await supabase
    .from('integrations')
    .update({ enabled })
    .eq('id', integrationId)
    .eq('account_id', accountId);

  if (error) throw error;

  // Send webhook to N8N if configured
  const { data: n8n } = await supabase
    .from('integrations')
    .select('config')
    .eq('account_id', accountId)
    .eq('type', 'n8n')
    .single();

  if (n8n?.config?.webhook_url) {
    await fetch(n8n.config.webhook_url, {
      method: 'POST',
      body: JSON.stringify({
        event: 'integration_toggled',
        integration_id: integrationId,
        enabled,
      }),
    });
  }

  revalidatePath(`/home/<USER>/integrations`);
}

export async function connectIntegration(accountId: string, type: string) {
  const supabase = getSupabaseServerClient();

  const { error } = await supabase.from('integrations').upsert({
    account_id: accountId,
    type,
    status: 'connected',
    enabled: true,
  });

  if (error) throw error;

  revalidatePath(`/home/<USER>/integrations`);
}
