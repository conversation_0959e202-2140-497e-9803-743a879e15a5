import { getSupabaseServerClient } from '@kit/supabase/server-client';

import {
  AVAILABLE_INTEGRATIONS,
  IntegrationType,
} from '../available-integrations';

export type Integration = {
  id: string;
  type: IntegrationType;
  status: 'not_connected' | 'connected' | 'error';
  enabled: boolean;
  config: Record<string, any>;
};

export async function loadIntegrations(accountId: string) {
  const supabase = getSupabaseServerClient();

  const { data: dbIntegrations, error } = await supabase
    .from('integrations')
    .select('*')
    .eq('account_id', accountId);

  if (error) throw error;

  // Đặc biệt xử lý cho ZNS integration
  const znsIntegration = dbIntegrations?.find(i => i.type === 'zns' || i.type === 'zalo');

  // Nếu có ZNS integration và có oa_config_id trong metadata
  if (znsIntegration?.metadata?.oa_config_id) {
    // <PERSON><PERSON><PERSON> tra token có hợp lệ không
    const { data: isTokenValid } = await supabase
      .rpc('is_oa_token_valid', { oa_config_id: znsIntegration.metadata.oa_config_id });

    // Cập nhật trạng thái dựa trên token và đảm bảo type là 'zns'
    if (znsIntegration.status !== (isTokenValid ? 'connected' : 'not_connected') || znsIntegration.type !== 'zns') {
      await supabase
        .from('integrations')
        .update({
          status: isTokenValid ? 'connected' : 'not_connected',
          type: 'zns' // Đảm bảo type là 'zns'
        })
        .eq('id', znsIntegration.id);

      // Cập nhật lại trạng thái và type trong object
      znsIntegration.status = isTokenValid ? 'connected' : 'not_connected';
      znsIntegration.type = 'zns';
    }
  }

  // Map available integrations with database state
  return AVAILABLE_INTEGRATIONS.map((defaultInt) => {
    const existing = dbIntegrations?.find((i) => i.type === defaultInt.type);

    return {
      id: existing?.id || defaultInt.type,
      type: defaultInt.type,
      name: defaultInt.name,
      status: existing?.status || 'not_connected',
      enabled: existing?.enabled || false,
      config: {
        ...defaultInt.config,
        ...(existing?.config || {}),
      },
      // Thêm metadata nếu có
      metadata: existing?.metadata || {},
    };
  });
}
