'use server';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';

// S<PERSON> dụng ki<PERSON>u dữ liệu từ database.types.ts
export interface IPOSCredentials {
  access_token: string;
  pos_parent: string;
  pos_id: string;
  baseUrl?: string;
}

export interface IPOSProduct {
  id: string;
  name: string;
  ta_price: number;
  ots_price: number;
  sort: number;
  store_id: number;
  store_item_id: string;
  type_id: string;
  image_url?: string;
  description?: string;
  status: string;
  update_at: string;
  customizations?: IPOSCustomization[];
  childs?: IPOSProduct[];
  [key: string]: any;
}

export interface IPOSCustomization {
  id: string;
  name: string;
  min_permitted: number;
  max_permitted: number;
  options: IPOSCustomizationOption[];
}

export interface IPOSCustomizationOption {
  id: string;
  name: string;
  ta_price: number;
  ots_price: number;
}

export interface IPOSOrder {
  id: string;
  order_code: string;
  customer_id?: string;
  customer_name?: string;
  customer_phone?: string;
  customer_email?: string;
  total_amount: number;
  discount_amount?: number;
  tax_amount?: number;
  status: string;
  payment_status: string;
  payment_method: string;
  note?: string;
  created_at: string;
  updated_at: string;
  items: IPOSOrderItem[];
  [key: string]: any;
}

export interface IPOSOrderItem {
  id: string;
  product_id: string;
  product_name: string;
  quantity: number;
  price: number;
  discount_amount?: number;
  total_amount: number;
  customizations?: IPOSOrderItemCustomization[];
  [key: string]: any;
}

export interface IPOSOrderItemCustomization {
  id: string;
  name: string;
  options: IPOSOrderItemCustomizationOption[];
}

export interface IPOSOrderItemCustomizationOption {
  id: string;
  name: string;
  price: number;
}

export interface IPOSCustomer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  address?: string;
  city?: string;
  province?: string;
  country?: string;
  postal_code?: string;
  created_at: string;
  updated_at: string;
  [key: string]: any;
}

/**
 * Gọi API iPOS
 */
async function callIPOSAPI(credentials: IPOSCredentials, endpoint: string, method: string = 'GET', body?: any): Promise<any> {
  const logger = await getLogger();
  try {
    const { access_token, pos_parent, pos_id } = credentials;
    const baseUrl = credentials.baseUrl || 'https://api.foodbook.vn';

    // Xây dựng URL với các tham số cần thiết
    let url = `${baseUrl}${endpoint}`;

    // Log URL để debug
    logger.info({ url, method, credentials }, 'Calling iPOS API');

    // Chuẩn bị body request theo đúng tài liệu API
    let requestBody: any = {};

    // Thêm các tham số xác thực vào body
    requestBody.access_token = access_token;
    requestBody.pos_parent = pos_parent;
    requestBody.pos_id = pos_id;

    // Thêm các tham số khác nếu có
    if (body) {
      requestBody = { ...requestBody, ...body };
    }

    // Nếu là GET request, thêm các tham số vào URL
    if (method === 'GET') {
      const queryParams = new URLSearchParams();
      Object.entries(requestBody).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const separator = url.includes('?') ? '&' : '?';
      url = `${url}${separator}${queryParams.toString()}`;

      // GET request không có body
      requestBody = undefined;
    }

    const options: RequestInit = {
      method,
      headers: {
        // Theo tài liệu IPOS HUB API INTEGRATION FOR MERCHANT, không cần Content-Type cho GET request
        // và sử dụng application/x-www-form-urlencoded cho POST/PUT request
        ...(method !== 'GET' ? {
          'Content-Type': 'application/x-www-form-urlencoded',
        } : {}),
        'Accept': 'application/json',
      },
      cache: 'no-store',
    };

    // Thêm body cho các request không phải GET
    if (requestBody && method !== 'GET') {
      // Chuyển đổi requestBody thành x-www-form-urlencoded format
      const formBody = Object.entries(requestBody)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
        .join('&');

      options.body = formBody;
      logger.info({ formBody }, 'Request body');
    }

    // Log URL cuối cùng
    logger.info({ finalUrl: url }, 'Final URL');

    const response = await fetch(url, options);

    // Log response status để debug
    logger.info({
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    }, 'iPOS API response');

    // Đọc response text trước
    const responseText = await response.text();
    logger.info({ responseText: responseText.substring(0, 500) }, 'Response text (first 500 chars)');

    // Kiểm tra nếu response rỗng hoặc không phải JSON
    if (!responseText || responseText.trim() === '') {
      logger.error({ url }, 'iPOS API returned empty response');

      // Nếu đây là môi trường development, trả về dữ liệu giả lập
      if (process.env.NODE_ENV === 'development') {
        logger.info('Using mock data for development');
        return { data: { items: [{ id: '1', name: 'Test Product' }] } };
      }

      throw new Error('iPOS API returned empty response');
    }

    // Parse JSON
    let jsonResponse;
    try {
      jsonResponse = JSON.parse(responseText);
    } catch (parseError) {
      logger.error({ parseError, responseText }, 'Failed to parse JSON response');

      // Nếu đây là môi trường development, trả về dữ liệu giả lập
      if (process.env.NODE_ENV === 'development') {
        logger.info('Using mock data for development');

        // Tạo dữ liệu giả lập phù hợp với endpoint
        if (url.includes('/items')) {
          return {
            data: {
              items: [{ id: '1', name: 'Test Product', ta_price: 100000, ots_price: 120000, status: 'active', description: 'Test description' }],
              item_types: [{ id: 1787532, name: 'BÌNH CÁ NHÂN', active: 1, sort: 1000, store_id: 3160, text_id: 'BCN' }]
            }
          };
        } else if (url.includes('/orders')) {
          return { data: [{ tran_id: '1', order_code: 'ORD001', tran_date: '2023-01-01', total_amount: 200000, status: 'completed', payment_method: 'cash' }] };
        } else if (url.includes('/customers')) {
          return { data: [{ membership_id: '1', membership_name: 'Test Customer', email: '<EMAIL>', phone: '0123456789', address: 'Test Address' }] };
        }

        // Mặc định trả về dữ liệu sản phẩm
        return { data: { items: [{ id: '1', name: 'Test Product', ta_price: 100000, ots_price: 120000, status: 'active', description: 'Test description' }] } };
      }

      throw new Error(`Failed to parse JSON response: ${responseText.substring(0, 100)}...`);
    }

    // Kiểm tra lỗi từ API
    if (!response.ok) {
      logger.error({ error: jsonResponse, endpoint }, 'iPOS API request failed');
      throw new Error(`iPOS API request failed: ${jsonResponse.message || response.statusText}`);
    }

    return jsonResponse;
  } catch (error) {
    logger.error({ error, endpoint }, 'Error calling iPOS API');

    // Nếu đây là môi trường development, trả về dữ liệu giả lập
    if (process.env.NODE_ENV === 'development') {
      logger.info('Using mock data for development');

      // Tạo dữ liệu giả lập phù hợp với endpoint
      if (endpoint.includes('/items')) {
        return {
          data: {
            items: [{ id: '1', name: 'Test Product', ta_price: 100000, ots_price: 120000, status: 'active', description: 'Test description' }],
            item_types: [{ id: 1787532, name: 'BÌNH CÁ NHÂN', active: 1, sort: 1000, store_id: 3160, text_id: 'BCN' }]
          }
        };
      } else if (endpoint.includes('/orders')) {
        return { data: [{ tran_id: '1', order_code: 'ORD001', tran_date: '2023-01-01', total_amount: 200000, status: 'completed', payment_method: 'cash' }] };
      } else if (endpoint.includes('/customers')) {
        return { data: [{ membership_id: '1', membership_name: 'Test Customer', email: '<EMAIL>', phone: '0123456789', address: 'Test Address' }] };
      } else if (endpoint.includes('/branches')) {
        return { data: [{ pos_id: '1', pos_name: 'Test Branch', pos_address: 'Test Address' }] };
      } else if (endpoint.includes('/vouchers')) {
        return { data: [{ coupon_code: 'TEST10', coupon_name: 'Test Voucher', coupon_amount: 10, coupon_type: 'percentage' }] };
      }

      // Mặc định trả về dữ liệu sản phẩm
      return { data: { items: [{ id: '1', name: 'Test Product', ta_price: 100000, ots_price: 120000, status: 'active', description: 'Test description' }] } };
    }

    throw error;
  }
}

/**
 * Xác thực với iPOS API
 */
export async function authenticateIPOS(credentials: IPOSCredentials): Promise<boolean> {
  try {
    // Kiểm tra access_token bằng cách gọi API lấy thông tin món ăn
    // Sử dụng URL test đơn giản hơn và giới hạn kết quả
    const testResponse = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/items', 'GET', { limit: 1 });

    // Log kết quả để debug
    const logger = await getLogger();
    logger.info({ testResponse }, 'Authentication response');

    return !!testResponse.data;
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error, credentials }, 'Error authenticating with iPOS API');
    throw error;
  }
}

/**
 * Lấy danh sách sản phẩm từ iPOS
 */
export async function getIPOSProducts(credentials: IPOSCredentials, params: Record<string, any> = {}): Promise<IPOSProduct[]> {
  try {
    // Thêm menu_type nếu không có
    if (!params.menu_type) {
      params.menu_type = 'DELI'; // DELI, STORE, ACTIVE, ALL
    }

    // Gọi API với params được truyền trực tiếp
    const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/items', 'GET', params);

    // Log kết quả để debug
    const logger = await getLogger();
    logger.info({ dataLength: data.data?.items?.length || 0 }, 'Products fetched');

    // Trả về danh sách sản phẩm từ response
    return data.data?.items || [];
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error fetching products from iPOS');
    throw error;
  }
}

/**
 * Lấy danh sách đơn hàng từ iPOS
 */
export async function getIPOSOrders(credentials: IPOSCredentials, params: Record<string, any> = {}): Promise<IPOSOrder[]> {
  try {
    // Gọi API với params được truyền trực tiếp
    const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/orders', 'GET', params);

    // Log kết quả để debug
    const logger = await getLogger();
    logger.info({ dataLength: data.data?.length || 0 }, 'Orders fetched');

    return data.data || [];
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error fetching orders from iPOS');
    throw error;
  }
}

/**
 * Lấy danh sách khách hàng từ iPOS
 */
export async function getIPOSCustomers(credentials: IPOSCredentials, params: Record<string, any> = {}): Promise<IPOSCustomer[]> {
  try {
    // Gọi API với params được truyền trực tiếp
    const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/customers', 'GET', params);

    // Log kết quả để debug
    const logger = await getLogger();
    logger.info({ dataLength: data.data?.length || 0 }, 'Customers fetched');

    return data.data || [];
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error fetching customers from iPOS');
    throw error;
  }
}

/**
 * Lấy danh sách danh mục từ iPOS
 */
export async function getIPOSCategories(credentials: IPOSCredentials, params: Record<string, any> = {}): Promise<any[]> {
  try {
    // Thêm menu_type nếu không có
    if (!params.menu_type) {
      params.menu_type = 'DELI'; // DELI, STORE, PICK
    }

    // Gọi API với params được truyền trực tiếp
    const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/items', 'GET', params);

    // Log kết quả để debug
    const logger = await getLogger();
    logger.info({ dataLength: data.data?.item_types?.length || 0 }, 'Categories fetched');

    // Trả về danh sách danh mục từ response
    return data.data?.item_types || [];
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error fetching categories from iPOS');
    throw error;
  }
}

/**
 * Lấy danh sách voucher từ iPOS
 */
export async function getIPOSVouchers(credentials: IPOSCredentials, params: Record<string, any> = {}): Promise<any[]> {
  try {
    // Gọi API với params được truyền trực tiếp
    const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/vouchers', 'GET', params);

    // Log kết quả để debug
    const logger = await getLogger();
    logger.info({ dataLength: data.data?.length || 0 }, 'Vouchers fetched');

    return data.data || [];
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error fetching vouchers from iPOS');
    throw error;
  }
}

/**
 * Lấy chi tiết voucher từ iPOS
 */
export async function getIPOSVoucherById(credentials: IPOSCredentials, voucherId: string): Promise<any> {
  try {
    // Gọi API với voucherId
    const data = await callIPOSAPI(credentials, `/ipos/ws/xpartner/v2/vouchers/${voucherId}`, 'GET');

    // Log kết quả để debug
    const logger = await getLogger();
    logger.info({ voucherId }, 'Voucher fetched');

    return data.data || {};
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error, voucherId }, 'Error fetching voucher from iPOS');
    throw error;
  }
}

/**
 * Lấy danh sách chi nhánh từ iPOS
 */
export async function getIPOSBranches(credentials: IPOSCredentials, params: Record<string, any> = {}): Promise<any[]> {
  try {
    // Gọi API với params được truyền trực tiếp
    const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/pos', 'GET', params);

    // Log kết quả để debug
    const logger = await getLogger();
    logger.info({ dataLength: data.data?.length || 0 }, 'Branches fetched');

    return data.data || [];
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error fetching branches from iPOS');
    throw error;
  }
}

/**
 * Lấy chi tiết sản phẩm từ iPOS
 */
export async function getIPOSProductById(credentials: IPOSCredentials, productId: string): Promise<IPOSProduct> {
  try {
    const data = await callIPOSAPI(credentials, `/ipos/ws/xpartner/v2/items/${productId}`, 'GET');

    // Log kết quả để debug
    const logger = await getLogger();
    logger.info({ productId, data }, 'Product details fetched');

    return data.data;
  } catch (error) {
    const logger = await getLogger();
    logger.error(
      { error, productId },
      'Error fetching product details from iPOS',
    );
    throw error;
  }
}

/**
 * Lấy chi tiết đơn hàng từ iPOS
 */
export async function getIPOSOrderById(credentials: IPOSCredentials, orderId: string): Promise<IPOSOrder> {
  try {
    const data = await callIPOSAPI(credentials, `/ipos/ws/xpartner/v2/orders/${orderId}`, 'GET');

    // Log kết quả để debug
    const logger = await getLogger();
    logger.info({ orderId, data }, 'Order details fetched');

    return data.data;
  } catch (error) {
    const logger = await getLogger();
    logger.error(
      { error, orderId },
      'Error fetching order details from iPOS',
    );
    throw error;
  }
}

/**
 * Lấy chi tiết khách hàng từ iPOS
 */
export async function getIPOSCustomerById(credentials: IPOSCredentials, customerId: string): Promise<IPOSCustomer> {
  try {
    const data = await callIPOSAPI(credentials, `/ipos/ws/xpartner/v2/customers/${customerId}`, 'GET');

    // Log kết quả để debug
    const logger = await getLogger();
    logger.info({ customerId, data }, 'Customer details fetched');

    return data.data;
  } catch (error) {
    const logger = await getLogger();
    logger.error(
      { error, customerId },
      'Error fetching customer details from iPOS',
    );
    throw error;
  }
}

/**
 * Tạo đơn hàng mới trên iPOS
 */
export async function createIPOSOrder(credentials: IPOSCredentials, orderData: any): Promise<any> {
  try {
    // Log dữ liệu đơn hàng để debug
    const logger = await getLogger();
    logger.info({ orderData }, 'Creating order on iPOS');

    const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/orders', 'POST', orderData);

    // Log kết quả để debug
    logger.info({ response: data }, 'Order created successfully');

    return data;
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error, orderData }, 'Error creating order on iPOS');
    throw error;
  }
}

/**
 * Cập nhật trạng thái đơn hàng trên iPOS
 */
export async function updateIPOSOrderStatus(credentials: IPOSCredentials, orderId: string, status: string): Promise<any> {
  try {
    // Log thông tin cập nhật để debug
    const logger = await getLogger();
    logger.info({ orderId, status }, 'Updating order status on iPOS');

    const data = await callIPOSAPI(
      credentials,
      `/ipos/ws/xpartner/v2/orders/${orderId}/status`,
      'PUT',
      { status },
    );

    // Log kết quả để debug
    logger.info({ response: data }, 'Order status updated successfully');

    return data;
  } catch (error) {
    const logger = await getLogger();
    logger.error(
      { error, orderId, status },
      'Error updating order status on iPOS',
    );
    throw error;
  }
}

/**
 * Lấy danh sách trường dữ liệu của sản phẩm
 */
export async function getIPOSProductFields(credentials: IPOSCredentials): Promise<string[]> {
  try {
    // Nếu đây là môi trường development, trả về danh sách trường mặc định
    if (process.env.NODE_ENV === 'development') {
      return [
        'id', 'name', 'ta_price', 'ots_price', 'sort', 'store_id', 'store_item_id',
        'type_id', 'image_url', 'description', 'status', 'update_at'
      ];
    }

    // Lấy một sản phẩm mẫu để xác định các trường
    const products = await getIPOSProducts(credentials, { limit: 1 });

    if (products.length === 0) {
      // Nếu không có sản phẩm nào, trả về danh sách trường mặc định
      return [
        'id', 'name', 'ta_price', 'ots_price', 'sort', 'store_id', 'store_item_id',
        'type_id', 'image_url', 'description', 'status', 'update_at'
      ];
    }

    // Lấy tất cả các key từ sản phẩm đầu tiên (loại bỏ customizations và childs)
    const fields = Object.keys(products[0]).filter(key => key !== 'customizations' && key !== 'childs');
    return fields;
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error getting product fields from iPOS');

    // Nếu có lỗi, trả về danh sách trường mặc định
    return [
      'id', 'name', 'ta_price', 'ots_price', 'sort', 'store_id', 'store_item_id',
      'type_id', 'image_url', 'description', 'status', 'update_at'
    ];
  }
}

/**
 * Lấy danh sách trường dữ liệu của đơn hàng
 */
export async function getIPOSOrderFields(credentials: IPOSCredentials): Promise<string[]> {
  try {
    // Nếu đây là môi trường development, trả về danh sách trường mặc định
    if (process.env.NODE_ENV === 'development') {
      return [
        'id', 'order_code', 'customer_id', 'customer_name',
        'customer_phone', 'customer_email', 'total_amount',
        'discount_amount', 'tax_amount', 'status', 'payment_status',
        'payment_method', 'note', 'created_at', 'updated_at'
      ];
    }

    // Lấy một đơn hàng mẫu để xác định các trường
    const orders = await getIPOSOrders(credentials, { limit: 1 });

    if (orders.length === 0) {
      // Nếu không có đơn hàng nào, trả về danh sách trường mặc định
      return [
        'id', 'order_code', 'customer_id', 'customer_name',
        'customer_phone', 'customer_email', 'total_amount',
        'discount_amount', 'tax_amount', 'status', 'payment_status',
        'payment_method', 'note', 'created_at', 'updated_at'
      ];
    }

    // Lấy tất cả các key từ đơn hàng đầu tiên (trừ items vì đó là một mảng)
    const fields = Object.keys(orders[0]).filter((key) => key !== 'items');
    return fields;
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error getting order fields from iPOS');

    // Nếu có lỗi, trả về danh sách trường mặc định
    return [
      'id', 'order_code', 'customer_id', 'customer_name',
      'customer_phone', 'customer_email', 'total_amount',
      'discount_amount', 'tax_amount', 'status', 'payment_status',
      'payment_method', 'note', 'created_at', 'updated_at'
    ];
  }
}

/**
 * Lấy danh sách trường dữ liệu của khách hàng
 */
export async function getIPOSCustomerFields(credentials: IPOSCredentials): Promise<string[]> {
  try {
    // Nếu đây là môi trường development, trả về danh sách trường mặc định
    if (process.env.NODE_ENV === 'development') {
      return [
        'id', 'name', 'phone', 'email', 'address',
        'city', 'province', 'country', 'postal_code',
        'created_at', 'updated_at'
      ];
    }

    // Lấy một khách hàng mẫu để xác định các trường
    const customers = await getIPOSCustomers(credentials, { limit: 1 });

    if (customers.length === 0) {
      // Nếu không có khách hàng nào, trả về danh sách trường mặc định
      return [
        'id', 'name', 'phone', 'email', 'address',
        'city', 'province', 'country', 'postal_code',
        'created_at', 'updated_at'
      ];
    }

    // Lấy tất cả các key từ khách hàng đầu tiên
    return Object.keys(customers[0]);
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error getting customer fields from iPOS');

    // Nếu có lỗi, trả về danh sách trường mặc định
    return [
      'id', 'name', 'phone', 'email', 'address',
      'city', 'province', 'country', 'postal_code',
      'created_at', 'updated_at'
    ];
  }
}

/**
 * Xử lý webhook từ iPOS
 */
export async function handleIPOSWebhook(credentials: IPOSCredentials, payload: any): Promise<void> {
  try {
    const { event_id, event, timestamp, data, integration_id } = payload;
    const logger = await getLogger();
    logger.info({ event_id, event }, 'Received webhook from iPOS');

    // Lấy integration từ integration_id
    const supabase = getSupabaseServerClient<Database>();
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('id, account_id')
      .eq('id', integration_id)
      .single();

    if (integrationError || !integration) {
      logger.error({ integrationError, integration_id }, 'Integration not found');
      throw new Error('Integration not found');
    }

    // Xử lý các sự kiện khác nhau
    switch (event) {
      case 'product.created':
      case 'product.updated':
      case 'item_changed':
        // Sự kiện khi có sản phẩm thay đổi thông tin
        await handleProductWebhook(integration.id, integration.account_id, data || payload.item_changed);
        break;

      case 'order.created':
      case 'order.updated':
      case 'order.status_changed':
        // Sự kiện khi có đơn hàng thay đổi
        await handleOrderWebhook(integration.id, integration.account_id, data);
        break;

      case 'customer.created':
      case 'customer.updated':
        // Sự kiện khi có khách hàng thay đổi
        await handleCustomerWebhook(integration.id, integration.account_id, data);
        break;

      case 'item_out_of_stock':
        // Sự kiện khi thay đổi trạng thái item thành OUT_OF_STOCK
        await handleProductWebhook(integration.id, integration.account_id, payload.item_status_changed, true);
        break;

      default:
        logger.warn({ event }, 'Unhandled webhook event from iPOS');
    }
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error, payload }, 'Error handling iPOS webhook');
    throw error;
  }
}

/**
 * Xử lý webhook sản phẩm
 */
async function handleProductWebhook(integrationId: string, accountId: string, data: any, isOutOfStock: boolean = false): Promise<void> {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    logger.info({ data, isOutOfStock }, 'Processing product webhook');

    // Lấy mappings
    const { data: mappings, error: mappingsError } = await supabase
      .from('integration_mappings')
      .select('source_field, target_field, transform_function')
      .eq('integration_id', integrationId)
      .eq('resource_type', 'products')
      .eq('is_active', true);

    if (mappingsError) {
      logger.error({ mappingsError }, 'Failed to get mappings');
      throw new Error('Failed to get mappings');
    }

    // Áp dụng mappings
    const mappedData: Record<string, any> = {};

    // Thêm account_id
    mappedData.account_id = accountId;

    // Thêm metadata fields
    mappedData.source = 'ipos';
    mappedData.external_id = data.id || data.item_id;
    mappedData.last_synced_at = new Date().toISOString();
    mappedData.integration_data = data;

    // Nếu là out of stock, cập nhật trạng thái
    if (isOutOfStock) {
      mappedData.status = 'out_of_stock';
    }

    // Áp dụng mappings
    mappings?.forEach(mapping => {
      const { source_field, target_field, transform_function } = mapping;

      // Lấy giá trị từ source field
      let value = data[source_field];

      // Áp dụng hàm biến đổi nếu có
      if (transform_function && value !== undefined) {
        try {
          const transformFn = new Function('value', transform_function);
          value = transformFn(value);
        } catch (error) {
          logger.error({ error, sourceField: source_field }, 'Error applying transform function');
        }
      }

      // Gán giá trị cho target field
      if (value !== undefined) {
        mappedData[target_field] = value;
      }
    });

    // Tìm sản phẩm hiện có
    const { data: existingProduct, error: productError } = await supabase
      .from('products')
      .select('id')
      .eq('external_id', mappedData.external_id)
      .eq('account_id', accountId)
      .eq('source', 'ipos')
      .maybeSingle();

    if (productError && productError.code !== 'PGRST116') {
      throw productError;
    }

    // Cập nhật hoặc thêm sản phẩm
    if (existingProduct) {
      const { error: updateError } = await supabase
        .from('products')
        .update(mappedData)
        .eq('id', existingProduct.id)
        .eq('account_id', accountId);

      if (updateError) {
        throw updateError;
      }

      logger.info({ productId: data.id, existingId: existingProduct.id }, 'Product updated successfully');
    } else {
      const { error: insertError } = await supabase
        .from('products')
        .insert(mappedData);

      if (insertError) {
        throw insertError;
      }

      logger.info({ productId: data.id }, 'Product created successfully');
    }
  } catch (error) {
    logger.error({ error, productId: data.id }, 'Error processing product webhook');
    throw error;
  }
}

/**
 * Xử lý webhook đơn hàng
 */
async function handleOrderWebhook(integrationId: string, accountId: string, data: any): Promise<void> {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    logger.info({ data }, 'Processing order webhook');

    // Lấy mappings
    const { data: mappings, error: mappingsError } = await supabase
      .from('integration_mappings')
      .select('source_field, target_field, transform_function')
      .eq('integration_id', integrationId)
      .eq('resource_type', 'orders')
      .eq('is_active', true);

    if (mappingsError) {
      logger.error({ mappingsError }, 'Failed to get mappings');
      throw new Error('Failed to get mappings');
    }

    // Áp dụng mappings
    const mappedData: Record<string, any> = {};

    // Thêm account_id
    mappedData.account_id = accountId;

    // Thêm metadata fields
    mappedData.source = 'ipos';
    mappedData.external_id = data.id || data.tran_id;
    mappedData.last_synced_at = new Date().toISOString();
    mappedData.integration_data = data;

    // Áp dụng mappings
    mappings?.forEach(mapping => {
      const { source_field, target_field, transform_function } = mapping;

      // Lấy giá trị từ source field
      let value = data[source_field];

      // Áp dụng hàm biến đổi nếu có
      if (transform_function && value !== undefined) {
        try {
          const transformFn = new Function('value', transform_function);
          value = transformFn(value);
        } catch (error) {
          logger.error({ error, sourceField: source_field }, 'Error applying transform function');
        }
      }

      // Gán giá trị cho target field
      if (value !== undefined) {
        mappedData[target_field] = value;
      }
    });

    // Tìm đơn hàng hiện có
    const { data: existingOrder, error: orderError } = await supabase
      .from('customer_orders')
      .select('id')
      .eq('external_id', mappedData.external_id)
      .eq('account_id', accountId)
      .eq('source', 'ipos')
      .maybeSingle();

    if (orderError && orderError.code !== 'PGRST116') {
      throw orderError;
    }

    // Cập nhật hoặc thêm đơn hàng
    if (existingOrder) {
      const { error: updateError } = await supabase
        .from('customer_orders')
        .update(mappedData)
        .eq('id', existingOrder.id)
        .eq('account_id', accountId);

      if (updateError) {
        throw updateError;
      }

      logger.info({ orderId: data.id, existingId: existingOrder.id }, 'Order updated successfully');
    } else {
      const { error: insertError } = await supabase
        .from('customer_orders')
        .insert(mappedData);

      if (insertError) {
        throw insertError;
      }

      logger.info({ orderId: data.id }, 'Order created successfully');
    }
  } catch (error) {
    logger.error({ error, orderId: data.id }, 'Error processing order webhook');
    throw error;
  }
}

/**
 * Xử lý webhook khách hàng
 */
async function handleCustomerWebhook(integrationId: string, accountId: string, data: any): Promise<void> {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    logger.info({ data }, 'Processing customer webhook');

    // Lấy mappings
    const { data: mappings, error: mappingsError } = await supabase
      .from('integration_mappings')
      .select('source_field, target_field, transform_function')
      .eq('integration_id', integrationId)
      .eq('resource_type', 'customers')
      .eq('is_active', true);

    if (mappingsError) {
      logger.error({ mappingsError }, 'Failed to get mappings');
      throw new Error('Failed to get mappings');
    }

    // Áp dụng mappings
    const mappedData: Record<string, any> = {};

    // Thêm account_id
    mappedData.account_id = accountId;

    // Thêm metadata fields
    mappedData.source = 'ipos';
    mappedData.external_id = data.id || data.membership_id;
    mappedData.last_synced_at = new Date().toISOString();
    mappedData.integration_data = data;

    // Áp dụng mappings
    mappings?.forEach(mapping => {
      const { source_field, target_field, transform_function } = mapping;

      // Lấy giá trị từ source field
      let value = data[source_field];

      // Áp dụng hàm biến đổi nếu có
      if (transform_function && value !== undefined) {
        try {
          const transformFn = new Function('value', transform_function);
          value = transformFn(value);
        } catch (error) {
          logger.error({ error, sourceField: source_field }, 'Error applying transform function');
        }
      }

      // Gán giá trị cho target field
      if (value !== undefined) {
        mappedData[target_field] = value;
      }
    });

    // Tìm khách hàng hiện có
    const { data: existingCustomer, error: customerError } = await supabase
      .from('customers')
      .select('id')
      .eq('external_id', mappedData.external_id)
      .eq('account_id', accountId)
      .eq('source', 'ipos')
      .maybeSingle();

    if (customerError && customerError.code !== 'PGRST116') {
      throw customerError;
    }

    // Cập nhật hoặc thêm khách hàng
    if (existingCustomer) {
      const { error: updateError } = await supabase
        .from('customers')
        .update(mappedData)
        .eq('id', existingCustomer.id)
        .eq('account_id', accountId);

      if (updateError) {
        throw updateError;
      }

      logger.info({ customerId: data.id, existingId: existingCustomer.id }, 'Customer updated successfully');
    } else {
      const { error: insertError } = await supabase
        .from('customers')
        .insert(mappedData);

      if (insertError) {
        throw insertError;
      }

      logger.info({ customerId: data.id }, 'Customer created successfully');
    }
  } catch (error) {
    logger.error({ error, customerId: data.id }, 'Error processing customer webhook');
    throw error;
  }
}

/**
 * Lấy danh sách trường dữ liệu của danh mục
 */
export async function getIPOSCategoryFields(credentials: IPOSCredentials): Promise<string[]> {
  try {
    // Nếu đây là môi trường development, trả về danh sách trường mặc định
    if (process.env.NODE_ENV === 'development') {
      return [
        'id', 'name', 'active', 'sort', 'store_id', 'text_id'
      ];
    }

    // Lấy một danh mục mẫu để xác định các trường
    const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/items', 'GET', {
      menu_type: 'DELI',
      limit: 1
    });
    const categories = data.data?.item_types || [];

    if (categories.length === 0) {
      // Nếu không có danh mục nào, trả về danh sách trường mặc định
      return [
        'id', 'name', 'active', 'sort', 'store_id', 'text_id'
      ];
    }

    // Lấy tất cả các key từ danh mục đầu tiên
    return Object.keys(categories[0]);
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error getting category fields from iPOS');

    // Nếu có lỗi, trả về danh sách trường mặc định
    return [
      'id', 'name', 'active', 'sort', 'store_id', 'text_id'
    ];
  }
}

/**
 * Lấy danh sách trường dữ liệu của voucher
 */
export async function getIPOSVoucherFields(credentials: IPOSCredentials): Promise<string[]> {
  try {
    // Nếu đây là môi trường development, trả về danh sách trường mặc định
    if (process.env.NODE_ENV === 'development') {
      return [
        'id', 'coupon_code', 'coupon_name', 'coupon_type', 'coupon_amount',
        'valid_from', 'valid_to', 'status', 'description', 'conditions',
        'usage_limit', 'usage_count'
      ];
    }

    // Lấy một voucher mẫu để xác định các trường
    const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/vouchers', 'GET', { limit: 1 });
    const vouchers = data.data || [];

    if (vouchers.length === 0) {
      // Nếu không có voucher nào, trả về danh sách trường mặc định
      return [
        'id', 'coupon_code', 'coupon_name', 'coupon_type', 'coupon_amount',
        'valid_from', 'valid_to', 'status', 'description', 'conditions',
        'usage_limit', 'usage_count'
      ];
    }

    // Lấy tất cả các key từ voucher đầu tiên
    return Object.keys(vouchers[0]);
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error getting voucher fields from iPOS');

    // Nếu có lỗi, trả về danh sách trường mặc định
    return [
      'id', 'coupon_code', 'coupon_name', 'coupon_type', 'coupon_amount',
      'valid_from', 'valid_to', 'status', 'description', 'conditions',
      'usage_limit', 'usage_count'
    ];
  }
}

/**
 * Lấy danh sách trường dữ liệu của chi nhánh
 */
export async function getIPOSBranchFields(credentials: IPOSCredentials): Promise<string[]> {
  try {
    // Nếu đây là môi trường development, trả về danh sách trường mặc định
    if (process.env.NODE_ENV === 'development') {
      return [
        'id', 'pos_id', 'pos_name', 'pos_address', 'pos_city', 'pos_district',
        'pos_phone', 'pos_email', 'status'
      ];
    }

    // Lấy một chi nhánh mẫu để xác định các trường
    const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/pos', 'GET', { limit: 1 });
    const branches = data.data || [];

    if (branches.length === 0) {
      // Nếu không có chi nhánh nào, trả về danh sách trường mặc định
      return [
        'id', 'pos_id', 'pos_name', 'pos_address', 'pos_city', 'pos_district',
        'pos_phone', 'pos_email', 'status'
      ];
    }

    // Lấy tất cả các key từ chi nhánh đầu tiên
    return Object.keys(branches[0]);
  } catch (error) {
    const logger = await getLogger();
    logger.error({ error }, 'Error getting branch fields from iPOS');

    // Nếu có lỗi, trả về danh sách trường mặc định
    return [
      'id', 'pos_id', 'pos_name', 'pos_address', 'pos_city', 'pos_district',
      'pos_phone', 'pos_email', 'status'
    ];
  }
}

/**
 * Tạo iPOS connector
 */
export async function createIPOSConnector(integrationId: string) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy thông tin integration
    const { data: integration, error } = await supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .eq('type', 'ipos' as Database['public']['Enums']['integration_type'])
      .single();

    if (error || !integration) {
      logger.error({ error, integrationId }, 'Failed to get iPOS integration');
      throw new Error('iPOS integration not found');
    }

    // Lấy credentials từ integration
    const credentials = integration.credentials as IPOSCredentials;

    if (!credentials || !credentials.access_token || !credentials.pos_parent || !credentials.pos_id) {
      logger.error({ integrationId }, 'Invalid iPOS credentials');
      throw new Error('Invalid iPOS credentials');
    }

    // Tạo connector object
    const logger = await getLogger();

    // Tạo connector
    return {
      getProducts: (params: Record<string, any> = {}) => getIPOSProducts(credentials, params),
      getProductById: (productId: string) => getIPOSProductById(credentials, productId),
      getOrders: (params: Record<string, any> = {}) => getIPOSOrders(credentials, params),
      getOrderById: (orderId: string) => getIPOSOrderById(credentials, orderId),
      getCustomers: (params: Record<string, any> = {}) => getIPOSCustomers(credentials, params),
      getCustomerById: (customerId: string) => getIPOSCustomerById(credentials, customerId),
      getCategories: (params: Record<string, any> = {}) => getIPOSCategories(credentials, params),
      getBranches: (params: Record<string, any> = {}) => getIPOSBranches(credentials, params),
      createOrder: (orderData: any) => createIPOSOrder(credentials, orderData),
      updateOrderStatus: (orderId: string, status: string) => updateIPOSOrderStatus(credentials, orderId, status),

      // Additional methods for API Bridge
      getTransactions: async (params: Record<string, any> = {}) => {
        try {
          const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/transactions', 'GET', params);
          return data.data || [];
        } catch (error) {
          logger.error({ error }, 'Error fetching transactions from iPOS');
          throw error;
        }
      },

      getReports: async (params: Record<string, any> = {}) => {
        try {
          const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/reports/revenue', 'GET', params);
          return data.data || {};
        } catch (error) {
          logger.error({ error }, 'Error fetching reports from iPOS');
          throw error;
        }
      },

      getTables: async (params: Record<string, any> = {}) => {
        try {
          const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/tables', 'GET', params);
          return data.data || [];
        } catch (error) {
          logger.error({ error }, 'Error fetching tables from iPOS');
          throw error;
        }
      },

      reserveTable: async (tableData: any) => {
        try {
          const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/tables/reserve', 'POST', tableData);
          return data.data || {};
        } catch (error) {
          logger.error({ error }, 'Error reserving table on iPOS');
          throw error;
        }
      },

      getInventory: async (params: Record<string, any> = {}) => {
        try {
          const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/inventory', 'GET', params);
          return data.data || {};
        } catch (error) {
          logger.error({ error }, 'Error fetching inventory from iPOS');
          throw error;
        }
      },

      processPayment: async (paymentData: any) => {
        try {
          const data = await callIPOSAPI(credentials, '/ipos/ws/xpartner/v2/payments', 'POST', paymentData);
          return data.data || {};
        } catch (error) {
          logger.error({ error }, 'Error processing payment on iPOS');
          throw error;
        }
      }
    };
  } catch (error) {
    logger.error({ error, integrationId }, 'Error creating iPOS connector');
    throw error;
  }
}

/**
 * Lấy credentials iPOS từ integration
 */
export async function getIPOSCredentialsFromIntegration(integrationId: string): Promise<IPOSCredentials> {
  const supabase = getSupabaseServerClient<Database>();
  const logger = await getLogger();

  try {
    // Lấy thông tin integration
    const { data: integration, error } = await supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .eq('type', 'ipos' as Database['public']['Enums']['integration_type'])
      .single();

    if (error || !integration) {
      logger.error({ error, integrationId }, 'Failed to get iPOS integration');
      throw new Error('iPOS integration not found');
    }

    // Lấy credentials từ integration
    const credentials = integration.credentials as IPOSCredentials;

    if (!credentials || !credentials.access_token || !credentials.pos_parent || !credentials.pos_id) {
      logger.error({ integrationId }, 'Invalid iPOS credentials');
      throw new Error('Invalid iPOS credentials');
    }

    return credentials;
  } catch (error) {
    logger.error({ error, integrationId }, 'Error getting iPOS credentials');
    throw error;
  }
}
