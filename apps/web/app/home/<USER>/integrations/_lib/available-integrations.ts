export const AVAILABLE_INTEGRATIONS = [
    {
        type: 'zns',
        name: 'Zalo Notification Service',
        description: 'Send notifications to your customers via Zalo OA. Enable transactional, customer care, and promotional messages.',
        config: {
            logo: '/images/zns-logo.png',
            features: [
                'Transactional notifications',
                'Customer care messages',
                'Promotional campaigns',
                'Template management',
                'Delivery tracking and analytics'
            ]
        },
    },
    {
        type: 'ipos',
        name: 'iPOS',
        description: 'Sync products, orders, customers, and vouchers with iPOS POS system. Enable loyalty points and seamless order management.',
        config: {
            logo: '/images/ipos-logo.png',
            features: [
                'Two-way synchronization of customers, orders, and vouchers',
                'Real-time order status updates',
                'Loyalty points integration',
                'Voucher redemption and validation',
                'Branch and product catalog synchronization'
            ]
        },
    },
    {
        type: 'misa-eshop',
        name: 'MISA eShop',
        description:
            'Sync customer and order data to MISA eShop. Keep records updated for better sales and inventory management.',
        config: {
            logo: '/images/misa-eshop-logo.png',
            learnMoreUrl: 'https://misaeshop.vn',
        },
    },
    {
        type: 'sapo',
        name: 'SAPO',
        description:
            'Automatically send customer and order details to Sapo. Ensure seamless synchronization for efficient business.',
        config: {
            logo: '/images/sapo-logo.png',
            learnMoreUrl: 'https://sapo.vn',
        },
    },
    {
        type: 'kiotviet',
        name: 'KiotViet',
        description:
            'Connect KiotViet to sync customer and order data. Maintain accurate records for streamlined sales and inventory tracking.',
        config: {
            logo: '/images/kiotviet-logo.png',
            learnMoreUrl: 'https://kiotviet.vn',
        },
    },
    {
        type: 'notion',
        name: 'Notion',
        description:
            'Sync customer and order records to Notion in real-time. Automatically organize and track.',
        config: {
            logo: '/images/notion-logo.png',
            estimatedAvailability: 'Q3 2025',
            learnMoreUrl: 'https://notion.so',
        },
    },
    {
        type: 'woocommerce',
        name: 'WooCommerce',
        description:
            'Sync customer and order data to WooCommerce. Automate seamless eCommerce management.',
        config: {
            logo: '/images/woocommerce-logo.png',
            estimatedAvailability: 'Q2 2025',
            learnMoreUrl: 'https://woocommerce.com',
        },
    },
    // {
    //   type: 'zalopay',
    //   name: 'Zalo Pay',
    //   description: 'Enable seamless payments via ZaloPay in Mini App. Allow customers to pay invoices securely and track transactions in real-time.',
    //   config: {
    //     logo: '/images/zalopay-logo.png',
    //   }
    // },
    {
        type: 'n8n',
        name: 'n8n',
        description:
            'Connect Mini App with n8n for automated workflow. Sync data, trigger actions seamlessly with other apps.',
        config: {
            logo: '/images/n8n-logo.png',
            estimatedAvailability: 'Q4 2025',
            learnMoreUrl: 'https://n8n.io',
        },
    },
] as const;

export type IntegrationType = (typeof AVAILABLE_INTEGRATIONS)[number]['type'];
