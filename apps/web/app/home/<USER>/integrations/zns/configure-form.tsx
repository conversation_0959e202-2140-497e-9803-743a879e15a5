'use client';

import {useState, useEffect} from 'react';

import {useRouter} from 'next/navigation';

import {zodResolver} from '@hookform/resolvers/zod';
import {useForm} from 'react-hook-form';
import {toast} from 'sonner';
import {z} from 'zod';

import {useSupabase} from '@kit/supabase/hooks/use-supabase';
import {useQuery} from '@tanstack/react-query';
import {AppBreadcrumbs} from '@kit/ui/app-breadcrumbs';
import {Button} from '@kit/ui/button';
import {Card, CardContent, CardDescription, CardHeader, CardTitle,} from '@kit/ui/card';
import {Form} from '@kit/ui/form';
import {PageBody} from '@kit/ui/page';
import {Tabs} from '@kit/ui/tabs';
import {Trans} from '@kit/ui/trans';

import {TeamAccountLayoutPageHeader} from '~/home/<USER>/_components/team-account-layout-page-header';
import {ZnsEventType} from "@kit/zns/types";

// Move constants here
const EVENT_DESCRIPTIONS: Record<
    ZnsEventType,
    { defaultParams: Record<string, string>; defaultContent: string }
> = {
    order_created: {
        defaultParams: {orderNumber: '', customerName: ''},
        defaultContent: 'New order #{orderNumber} from {customerName}',
    },
    order_updated: {
        defaultParams: {orderNumber: '', status: ''},
        defaultContent: 'Order #{orderNumber} status: {status}',
    },
    promotion: {
        defaultParams: {code: '', discount: ''},
        defaultContent: 'Use code {code} for {discount}% off',
    },
    theme_activated: {
        defaultParams: {themeName: ''},
        defaultContent: 'New theme {themeName} activated',
    },
};

const znsConfigSchema = z.object({
    appId: z.string().min(1, 'App ID is required'),
    secretKey: z.string().min(1, 'Secret key is required'),
    oaId: z.string().min(1, 'OA ID is required'),
    events: z.record(
        z.object({
            enabled: z.boolean(),
            templateId: z.string(),
            params: z.record(z.string()),
            content: z.string(),
        }),
    ),
});

type ZnsConfigFormData = z.infer<typeof znsConfigSchema>;

interface ConfigureFormProps {
    accountId: string;
}

export function ConfigureForm({accountId}: ConfigureFormProps) {
    const router = useRouter();
    const [activeTab, setActiveTab] = useState<ZnsEventType>('order_created');
    const supabase = useSupabase();

    // Fetch OA configuration and templates
    const { data: oaConfig, isLoading: isLoadingOaConfig } = useQuery({
        queryKey: ['oa-config', accountId],
        queryFn: async () => {
            // First try to get account-specific OA configuration
            let { data, error } = await supabase
                .from('oa_configurations')
                .select('*')
                .eq('account_id', accountId)
                .maybeSingle();

            if (error) throw error;

            // If no account-specific configuration exists, try to get system default
            if (!data) {
                const { data: systemData, error: systemError } = await supabase
                    .from('oa_configurations')
                    .select('*')
                    .eq('is_system_default', true)
                    .maybeSingle();

                if (systemError) throw systemError;
                data = systemData;
            }

            console.log('Loaded OA config for configure form:', data); // Debug log
            return data;
        }
    });

    // Fetch templates if OA config exists
    const { data: templates, isLoading: isLoadingTemplates } = useQuery({
        queryKey: ['zns-templates', oaConfig?.id],
        queryFn: async () => {
            if (!oaConfig?.id) return [];

            const { data, error } = await supabase
                .from('zns_templates')
                .select('*')
                .eq('oa_config_id', oaConfig.id);

            if (error) throw error;
            return data || [];
        },
        enabled: !!oaConfig?.id
    });

    // Initialize form with default values
    const form = useForm<ZnsConfigFormData>({
        resolver: zodResolver(znsConfigSchema),
        defaultValues: {
            appId: '',
            secretKey: '',
            oaId: '',
            events: Object.keys(EVENT_DESCRIPTIONS).reduce(
                (acc, event) => ({
                    ...acc,
                    [event]: {
                        enabled: false,
                        templateId: '',
                        params: EVENT_DESCRIPTIONS[event as ZnsEventType].defaultParams,
                        content: EVENT_DESCRIPTIONS[event as ZnsEventType].defaultContent,
                    },
                }),
                {},
            ),
        },
    });

    // Update form when data is loaded
    useEffect(() => {
        if (oaConfig && templates && !form.formState.isDirty) {
            // Prepare events data from templates
            const eventsData = Object.keys(EVENT_DESCRIPTIONS).reduce(
                (acc, event) => {
                    const template = templates.find(t => t.event_type === event);
                    return {
                        ...acc,
                        [event]: {
                            enabled: template?.enabled || false,
                            templateId: template?.template_id || '',
                            params: template?.params || EVENT_DESCRIPTIONS[event as ZnsEventType].defaultParams,
                            content: template?.content || EVENT_DESCRIPTIONS[event as ZnsEventType].defaultContent,
                        },
                    };
                },
                {}
            );

            // Reset form with loaded data
            form.reset({
                appId: oaConfig.app_id || '',
                secretKey: oaConfig.secret_key || '',
                oaId: oaConfig.oa_id || '',
                events: eventsData,
            });
        }
    }, [oaConfig, templates, form]);

    const onSubmit = async (data: ZnsConfigFormData) => {
        try {
            // Check if we're dealing with a system default OA configuration
            if (oaConfig?.is_system_default) {
                // If it's a system default, create a new one for this account
                toast.info('Creating a new OA configuration for your account based on the system default');
            }

            // Prepare data for upsert
            const upsertData: any = {
                app_id: data.appId,
                secret_key: data.secretKey,
                oa_id: data.oaId,
                oa_type: 'private',
                oa_metadata: {
                    ...(oaConfig?.oa_metadata || {}),
                    configured_at: new Date().toISOString()
                }
            };

            // If creating new or using system default, add account_id
            if (!oaConfig || oaConfig.is_system_default) {
                upsertData.account_id = accountId;
            }

            // Tạo hoặc cập nhật OA configuration
            const {data: newOaConfig, error: oaError} = await supabase
                .from('oa_configurations')
                .upsert(oaConfig && !oaConfig.is_system_default ?
                    { id: oaConfig.id, ...upsertData } :
                    upsertData
                )
                .select()
                .single();

            if (oaError) throw oaError;

            if (!newOaConfig) {
                throw new Error('Failed to create OA configuration');
            }

            // Cập nhật ZNS integration
            const {error: integrationError} = await supabase
                .from('integrations')
                .upsert({
                    account_id: accountId,
                    type: 'zalo',
                    name: 'Zalo Notification Service',
                    status: 'connected', // Sẽ được cập nhật lại bởi trigger nếu token không hợp lệ
                    enabled: true,
                    metadata: {oa_config_id: newOaConfig.id}
                });

            if (integrationError) throw integrationError;

            const templatePromises = Object.entries(data.events)
                .filter(([_, config]) => config.enabled)
                .map(([event, config]) =>
                    supabase.from('zns_templates').upsert({
                        account_id: accountId,
                        oa_config_id: newOaConfig.id,
                        event_type: event as ZnsEventType,
                        template_id: config.templateId,
                        enabled: config.enabled,
                        params: config.params,
                        content: config.content,
                    }),
                );

            await Promise.all(templatePromises);

            toast.success('ZNS configuration saved successfully');
            router.push(`/home/<USER>/integrations`);
        } catch (error) {
            console.error('ZNS configuration error:', error);
            toast.error(
                error instanceof Error
                    ? error.message
                    : 'Failed to save ZNS configuration',
            );
        }
    };

    return (
        <div className="space-y-6">
            <TeamAccountLayoutPageHeader
                title={
                    <Trans i18nKey="integrations:zns:configure:title">
                        Configure ZNS Integration
                    </Trans>
                }
                description={<AppBreadcrumbs/>}
                account={accountId}
            />
            <PageBody data-testid="zns-page">
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>OA Configuration</CardTitle>
                                <CardDescription>
                                    Enter your Zalo OA credentials
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {/* OA Configuration fields... */}
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Notification Templates</CardTitle>
                                <CardDescription>
                                    Configure templates for different notification events
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Tabs
                                    value={activeTab}
                                    onValueChange={(v) => setActiveTab(v as ZnsEventType)}
                                >
                                    {/* Template configuration tabs... */}
                                </Tabs>
                            </CardContent>
                        </Card>

                        <div className="flex justify-end space-x-4">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => router.push(`/home/<USER>/integrations`)}
                            >
                                Cancel
                            </Button>
                            <Button type="submit">Save Configuration</Button>
                        </div>
                    </form>
                </Form>
            </PageBody>
        </div>
    );
}
