'use client';

import { useState, useTransition } from 'react';

import { usePara<PERSON>, useRouter } from 'next/navigation';

import { useQuery } from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import {
  Activity,
  AlertCircle,
  BarChart,
  Info,
  MessageSquare,
  RefreshCw,
  Settings,
  Users,
  Zap,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { Progress } from '@kit/ui/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';
import { Trans } from '@kit/ui/trans';
import { getValidZnsToken, useZnsEventHandler } from '@kit/zns';
import { MappingOverviewCard } from '../_components/mapping-overview-card';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { toggleIntegration } from '../../_lib/server/integrations.actions';
import ConnectionStatusCard from '../_components/connection-status-card';

export default function ZNSDashboardPage() {
  const { account: accountSlug } = useParams<{ account: string }>();
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();
  const { account } = useTeamAccountWorkspace();

  // Sử dụng account ID trực tiếp từ hook useTeamAccountWorkspace
  const accountId = account?.id;

  const [isPending, startTransition] = useTransition();
  const [connectionDetails, setConnectionDetails] = useState<any>(null);
  const [templateStats, setTemplateStats] = useState({
    total: 0,
    enabled: 0,
    pending: 0,
    rejected: 0,
  });
  const [quotaStats, setQuotaStats] = useState({
    dailyQuota: 0,
    remainingQuota: 0,
    usedToday: 0,
    usagePercentage: 0,
  });
  const [healthStatus, setHealthStatus] = useState({
    apiLatency: 0,
    lastError: null as string | null,
    errorCount: 0,
    uptime: 100,
  });

  // Account usage statistics
  const [accountStats, setAccountStats] = useState({
    totalSent: 0,
    totalDelivered: 0,
    totalFailed: 0,
    deliveryRate: 0,
    lastSentAt: null as string | null,
    monthlySent: [] as { month: string; count: number }[],
  });

  // Fetch ZNS integration
  const {
    data: integration,
    isLoading: isLoadingIntegration,
    refetch: refetchIntegration,
  } = useQuery({
    queryKey: ['zns-integration', accountId],
    queryFn: async () => {
      if (!accountId) return null;

      try {
        // Lấy tất cả các bản ghi integration thỏa mãn điều kiện
        const { data, error } = await supabase
          .from('integrations')
          .select('*')
          .eq('account_id', accountId)
          .eq('type', 'zalo')
          .order('created_at', { ascending: false });

        if (error) {
          console.error(
            'Error fetching ZNS integration:',
            JSON.stringify(error, null, 2),
          );
          return null;
        }

        // Trả về bản ghi mới nhất nếu có
        return data && data.length > 0 ? data[0] : null;
      } catch (err) {
        console.error(
          'Exception fetching ZNS integration:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: !!accountId,
  });

  // Fetch OA configuration if we have oa_config_id
  const { data: oaConfig, isLoading: isLoadingOaConfig } = useQuery({
    queryKey: ['oa-config', integration?.metadata?.oa_config_id, accountId],
    queryFn: async () => {
      try {
        // First try to get OA configuration from integration metadata
        if (integration?.metadata?.oa_config_id) {
          const { data, error } = await supabase
            .from('oa_configurations')
            .select('*')
            .eq('id', integration.metadata.oa_config_id)
            .maybeSingle();

          if (error) {
            console.error(
              'Error fetching OA config by ID:',
              JSON.stringify(error, null, 2),
            );
          } else if (data) {
            return data;
          }
        }

        // If no OA config ID in metadata or not found, try to find account-specific configuration
        if (accountId) {
          const { data, error } = await supabase
            .from('oa_configurations')
            .select('*')
            .eq('account_id', accountId)
            .maybeSingle();

          if (error) {
            console.error(
              'Error fetching OA config by account ID:',
              JSON.stringify(error, null, 2),
            );
          } else if (data) {
            console.log('Loaded account-specific OA config:', data);
            return data;
          }

          // If no account-specific configuration exists, try to get system default
          const { data: systemData, error: systemError } = await supabase
            .from('oa_configurations')
            .select('*')
            .eq('is_system_default', true)
            .maybeSingle();

          if (systemError) {
            console.error(
              'Error fetching system default OA config:',
              JSON.stringify(systemError, null, 2),
            );
          } else if (systemData) {
            console.log('Loaded system default OA config:', systemData);
            return systemData;
          }
        }

        return null;
      } catch (err) {
        console.error(
          'Exception fetching OA configuration:',
          err instanceof Error ? err.message : JSON.stringify(err, null, 2),
        );
        return null;
      }
    },
    enabled: Boolean(accountId),
    onSuccess: (data) => {
      if (data) {
        setConnectionDetails(data);
      }
    },
  });

  // Kiểm tra token có còn hạn hay không và refresh nếu cần
  const { data: tokenStatus, isLoading: isCheckingToken } = useQuery({
    queryKey: ['zns-token-status', oaConfig?.id],
    queryFn: async () => {
      if (!oaConfig?.id) return { isValid: false };

      try {
        // Sử dụng getValidZnsToken để lấy token hợp lệ và refresh nếu cần
        const { accessToken, oaConfig: updatedConfig } = await getValidZnsToken(
          supabase,
          oaConfig.id,
        );

        // Nếu có accessToken, có nghĩa là token hợp lệ hoặc đã được refresh thành công
        return {
          isValid: true,
          accessToken,
          updatedConfig,
        };
      } catch (error) {
        console.error('Error validating ZNS token:', error);
        return { isValid: false };
      }
    },
    enabled: Boolean(oaConfig?.id),
    refetchInterval: 5 * 60 * 1000, // Kiểm tra lại mỗi 5 phút
  });

  // Kiểm tra xem OA đã được kết nối thực sự hay chưa
  const isReallyConnected = Boolean(tokenStatus?.isValid);

  // Tính toán isLoading
  const isLoading =
    isLoadingIntegration || isLoadingOaConfig || isCheckingToken;

  // Trạng thái enabled của integration
  const [isEnabled, setIsEnabled] = useState(false);

  // Cập nhật isEnabled khi integration thay đổi
  useQuery({
    queryKey: ['update-is-enabled', integration?.id],
    queryFn: () => {
      setIsEnabled(integration?.enabled || false);
      return null;
    },
    enabled: !!integration,
  });

  // Fetch template statistics from database
  useQuery({
    queryKey: ['zns-template-stats', accountId, isReallyConnected],
    queryFn: async () => {
      if (!accountId || !isReallyConnected) return null;

      try {
        const { data: templates, error } = await supabase
          .from('zns_templates')
          .select('enabled, status')
          .eq('account_id', accountId);

        if (error) {
          console.error('Error fetching template stats:', error);
          return null;
        }

        const total = templates?.length || 0;
        const enabled = templates?.filter(t => t.enabled).length || 0;
        const pending = templates?.filter(t => t.status === 'pending').length || 0;
        const rejected = templates?.filter(t => t.status === 'rejected').length || 0;

        setTemplateStats({
          total,
          enabled,
          pending,
          rejected,
        });

        return { total, enabled, pending, rejected };
      } catch (error) {
        console.error('Exception fetching template stats:', error);
        return null;
      }
    },
    enabled: Boolean(accountId) && isReallyConnected,
  });

  // Fetch quota information and calculate from usage
  useQuery({
    queryKey: ['zns-quota', oaConfig?.id, accountId, isReallyConnected],
    queryFn: async () => {
      if (!oaConfig?.id || !accountId || !isReallyConnected) return null;

      try {
        // Get today's usage from zns_usage table
        const today = new Date();
        const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString();
        const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1).toISOString();

        const { data: todayUsage, error } = await supabase
          .from('zns_usage')
          .select('status')
          .eq('account_id', accountId)
          .gte('created_at', todayStart)
          .lt('created_at', todayEnd);

        if (error) {
          console.error('Error fetching today usage:', error);
        }

        const usedToday = todayUsage?.length || 0;

        // Get quota from oa_metadata or use default
        const dailyQuota = oaConfig.oa_metadata?.daily_quota || 1000; // Default 1000 messages per day
        const remainingQuota = Math.max(0, dailyQuota - usedToday);
        const usagePercentage = Math.round((usedToday / dailyQuota) * 100);

        setQuotaStats({
          dailyQuota,
          remainingQuota,
          usedToday,
          usagePercentage,
        });

        // Lấy thông tin health status
        if (oaConfig.oa_metadata?.health_status) {
          setHealthStatus(oaConfig.oa_metadata.health_status);
        }

        return { dailyQuota, remainingQuota, usedToday, usagePercentage };
      } catch (error) {
        console.error('Exception fetching quota info:', error);
        return null;
      }
    },
    enabled: Boolean(oaConfig?.id) && Boolean(accountId) && isReallyConnected,
  });

  // Fetch usage statistics from zns_usage table
  useQuery({
    queryKey: ['zns-usage-stats', accountId, isReallyConnected],
    queryFn: async () => {
      if (!accountId || !isReallyConnected) return [];

      try {
        // Fetch usage data from zns_usage table
        const { data: usageData, error } = await supabase
          .from('zns_usage')
          .select('status, created_at, mapping_id, template_id')
          .eq('account_id', accountId)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching usage stats:', error);
          return [];
        }

        return usageData || [];
      } catch (error) {
        console.error('Exception fetching usage stats:', error);
        return [];
      }
    },
    enabled: Boolean(accountId) && isReallyConnected,
    onSuccess: (usageData) => {
      // Tính toán thống kê từ dữ liệu thực
      const totalSent = usageData.length;
      const totalDelivered = usageData.filter(
        (usage) => usage.status === 'success',
      ).length;
      const totalFailed = usageData.filter((usage) =>
        ['failed', 'error'].includes(usage.status),
      ).length;
      const deliveryRate =
        totalSent > 0 ? Math.round((totalDelivered / totalSent) * 100) : 0;

      // Lấy thời gian gửi gần nhất
      const lastSentAt = usageData.length > 0 ? usageData[0].created_at : null;

      // Tính toán số lượng tin nhắn theo tháng (6 tháng gần nhất)
      const now = new Date();
      const monthlySent = [];

      for (let i = 0; i < 6; i++) {
        const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthStr = month.toLocaleString('vi-VN', {
          month: 'short',
          year: 'numeric',
        });
        const monthStart = new Date(
          month.getFullYear(),
          month.getMonth(),
          1,
        ).toISOString();
        const monthEnd = new Date(
          month.getFullYear(),
          month.getMonth() + 1,
          0,
        ).toISOString();

        const count = usageData.filter(
          (usage) => usage.created_at >= monthStart && usage.created_at <= monthEnd,
        ).length;

        monthlySent.push({ month: monthStr, count });
      }

      // Cập nhật state với dữ liệu thực
      setAccountStats({
        totalSent,
        totalDelivered,
        totalFailed,
        deliveryRate,
        lastSentAt,
        monthlySent: monthlySent.reverse(),
      });
    },
  });

  // Toggle ZNS integration
  const handleToggleEnabled = (enabled: boolean) => {
    if (!integration || !account) return;

    startTransition(async () => {
      try {
        await toggleIntegration(account as string, integration.id, enabled);
        toast.success(
          enabled
            ? t('integrations:zns.connect.enabledSuccess')
            : t('integrations:zns.connect.disabledSuccess'),
        );
        setIsEnabled(enabled);
      } catch (error) {
        toast.error(t('integrations:zns.connect.updateStatusError'));
      }
    });
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        account={account as string}
        title={t('integrations:zns.title')}
        description={
          <AppBreadcrumbs
            values={{
              home: t('common:routes.home', 'Trang chủ'),
              [accountSlug]: accountSlug,
              integrations: t('common:routes.integrations', 'Tích hợp'),
              zns: 'ZNS',
              dashboard: t('integrations:dashboard', 'Bảng điều khiển')
            }}
          />
        }
      />

      <PageBody
        data-testid="zns-dashboard-page"
        className="w-full max-w-none px-0"
      >
        <div className="w-full px-4 md:px-6 lg:px-8">
          {/* Main content with grid layout */}
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-3 xl:grid-cols-4">
            {/* Left column - Connection Status */}
            <div className="w-full lg:col-span-1">
              <ConnectionStatusCard
                accountId={account.slug as string}
                isConnected={isReallyConnected}
                isEnabled={isEnabled}
                isLoading={false}
                isPending={isPending}
                oaConfig={connectionDetails}
                integration={integration}
                onToggleEnabled={handleToggleEnabled}
              />
            </div>

            {/* Right column - Stats and Information */}
            <div className="space-y-4 lg:col-span-2 xl:col-span-3">
              {/* Stats Cards Grid */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Template Stats Card */}
                <Card className="overflow-hidden border-0 shadow-sm transition-shadow hover:shadow-md">
                  <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 pb-2 dark:from-green-950/30 dark:to-emerald-950/30">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <MessageSquare className="h-5 w-5 text-green-500" />
                        <Trans i18nKey="integrations:zns.connect.dashboard.templateStats">
                          Thống kê mẫu
                        </Trans>
                      </CardTitle>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="text-muted-foreground h-4 w-4" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              <Trans i18nKey="integrations:zns.connect.dashboard.templateStatsTooltip">
                                Thống kê về các mẫu ZNS của bạn
                              </Trans>
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    {isLoading ? (
                      <div className="flex h-32 items-center justify-center">
                        <RefreshCw className="h-6 w-6 animate-spin text-green-500" />
                      </div>
                    ) : !isReallyConnected ? (
                      <div className="flex flex-col items-center justify-center space-y-4 py-4">
                        <AlertCircle className="h-12 w-12 text-amber-500" />
                        <p className="text-center text-sm">
                          <Trans i18nKey="integrations:zns.connect.dashboard.connectToViewTemplates">
                            Kết nối với ZNS để xem thống kê mẫu.
                          </Trans>
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.totalTemplates">
                              Tổng số mẫu
                            </Trans>
                          </span>
                          <Badge variant="outline" className="px-2 py-1">
                            {templateStats.total}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.enabledTemplates">
                              Mẫu đã kích hoạt
                            </Trans>
                          </span>
                          <Badge variant="success" className="px-2 py-1">
                            {templateStats.enabled}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.pendingTemplates">
                              Đang chờ duyệt
                            </Trans>
                          </span>
                          <Badge
                            variant="outline"
                            className="border-amber-200 bg-amber-50 px-2 py-1 text-amber-700"
                          >
                            {templateStats.pending}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.rejectedTemplates">
                              Mẫu bị từ chối
                            </Trans>
                          </span>
                          <Badge variant="destructive" className="px-2 py-1">
                            {templateStats.rejected}
                          </Badge>
                        </div>
                        <div className="pt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/zns/templates`,
                              )
                            }
                            disabled={!isReallyConnected}
                          >
                            <MessageSquare className="mr-1 h-4 w-4" />
                            <Trans i18nKey="integrations:zns.connect.dashboard.manageTemplates">
                              Quản lý mẫu
                            </Trans>
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Quota Usage Card */}
                <Card className="overflow-hidden border-0 shadow-sm transition-shadow hover:shadow-md">
                  <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 pb-2 dark:from-purple-950/30 dark:to-violet-950/30">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <BarChart className="h-5 w-5 text-purple-500" />
                        <Trans i18nKey="integrations:zns.connect.dashboard.quotaUsage">
                          Quota Usage
                        </Trans>
                      </CardTitle>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="text-muted-foreground h-4 w-4" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              <Trans i18nKey="integrations:zns.connect.dashboard.quotaUsageTooltip">
                                Your daily ZNS message quota usage
                              </Trans>
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    {isLoading ? (
                      <div className="flex h-32 items-center justify-center">
                        <RefreshCw className="h-6 w-6 animate-spin text-purple-500" />
                      </div>
                    ) : !isReallyConnected ? (
                      <div className="flex flex-col items-center justify-center space-y-4 py-4">
                        <AlertCircle className="h-12 w-12 text-amber-500" />
                        <p className="text-center text-sm">
                          <Trans i18nKey="integrations:zns.connect.dashboard.connectToViewQuota">
                            Connect to ZNS to view quota usage.
                          </Trans>
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <div className="mb-2 flex items-center justify-between">
                            <span className="text-sm font-medium">
                              <Trans i18nKey="integrations:zns.connect.dashboard.dailyQuota">
                                Daily Quota
                              </Trans>
                            </span>
                            <span className="text-sm">
                              {quotaStats.usedToday} / {quotaStats.dailyQuota}
                            </span>
                          </div>
                          <Progress
                            value={quotaStats.usagePercentage}
                            className="h-2"
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.remainingToday">
                              Remaining Today
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {quotaStats.remainingQuota}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.usedToday">
                              Used Today
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {quotaStats.usedToday}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.usagePercentage">
                              Usage Percentage
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {quotaStats.usagePercentage}%
                          </span>
                        </div>
                        <div className="pt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/zns/analytics`,
                              )
                            }
                            disabled={!isReallyConnected}
                          >
                            <BarChart className="mr-1 h-4 w-4" />
                            <Trans i18nKey="integrations:zns.connect.dashboard.viewAnalytics">
                              View Analytics
                            </Trans>
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Account Usage Statistics Card */}
              <Card className="overflow-hidden border-0 shadow-sm transition-shadow hover:shadow-md">
                <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 pb-2 dark:from-purple-950/30 dark:to-pink-950/30">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <Users className="h-5 w-5 text-purple-500" />
                      <Trans i18nKey="integrations:zns.dashboard.accountUsage">
                        Thống kê sử dụng tài khoản
                      </Trans>
                    </CardTitle>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="text-muted-foreground h-4 w-4" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>
                            <Trans i18nKey="integrations:zns.dashboard.accountUsageTooltip">
                              Thống kê sử dụng ZNS của tài khoản của bạn
                            </Trans>
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  {isLoading ? (
                    <div className="flex h-32 items-center justify-center">
                      <RefreshCw className="h-6 w-6 animate-spin text-purple-500" />
                    </div>
                  ) : !isReallyConnected ? (
                    <div className="flex flex-col items-center justify-center space-y-4 py-4">
                      <AlertCircle className="h-12 w-12 text-amber-500" />
                      <p className="text-center text-sm">
                        <Trans i18nKey="integrations:zns.dashboard.connectToViewStats">
                          Kết nối với ZNS để xem thống kê sử dụng tài khoản.
                        </Trans>
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
                        <div className="rounded-lg border border-purple-100 bg-purple-50 p-3 shadow-sm">
                          <div className="mb-1 text-xs font-medium text-purple-500">
                            <Trans i18nKey="integrations:zns.dashboard.totalSent">
                              Tổng đã gửi
                            </Trans>
                          </div>
                          <div className="text-2xl font-bold text-purple-700">
                            {accountStats.totalSent.toLocaleString()}
                          </div>
                        </div>
                        <div className="rounded-lg border border-green-100 bg-green-50 p-3 shadow-sm">
                          <div className="mb-1 text-xs font-medium text-green-500">
                            <Trans i18nKey="integrations:zns.dashboard.delivered">
                              Đã nhận
                            </Trans>
                          </div>
                          <div className="text-2xl font-bold text-green-700">
                            {accountStats.totalDelivered.toLocaleString()}
                          </div>
                        </div>
                        <div className="rounded-lg border border-red-100 bg-red-50 p-3 shadow-sm">
                          <div className="mb-1 text-xs font-medium text-red-500">
                            <Trans i18nKey="integrations:zns.dashboard.failed">
                              Thất bại
                            </Trans>
                          </div>
                          <div className="text-2xl font-bold text-red-700">
                            {accountStats.totalFailed.toLocaleString()}
                          </div>
                        </div>
                        <div className="rounded-lg border border-blue-100 bg-blue-50 p-3 shadow-sm">
                          <div className="mb-1 text-xs font-medium text-blue-500">
                            <Trans i18nKey="integrations:zns.dashboard.deliveryRate">
                              Tỷ lệ gửi thành công
                            </Trans>
                          </div>
                          <div className="text-2xl font-bold text-blue-700">
                            {accountStats.deliveryRate}%
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-gray-700">
                          <Trans i18nKey="integrations:zns.dashboard.monthlyMessageVolume">
                            Lượng tin nhắn hàng tháng
                          </Trans>
                        </h4>
                        <div className="h-40 w-full">
                          {accountStats.monthlySent.length > 0 ? (
                            <div className="flex h-full items-end gap-1">
                              {accountStats.monthlySent.map((item, index) => {
                                const maxCount = Math.max(
                                  ...accountStats.monthlySent.map(
                                    (i) => i.count,
                                  ),
                                );
                                const height =
                                  maxCount > 0
                                    ? (item.count / maxCount) * 100
                                    : 0;
                                return (
                                  <div
                                    key={index}
                                    className="flex flex-1 flex-col items-center"
                                  >
                                    <div
                                      className="w-full rounded-t-sm bg-gradient-to-t from-purple-400 to-pink-400"
                                      style={{
                                        height: `${Math.max(height, 5)}%`,
                                      }}
                                    ></div>
                                    <div className="mt-1 w-full truncate text-center text-xs text-gray-500">
                                      {item.month}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          ) : (
                            <div className="flex h-full items-center justify-center text-sm text-gray-500">
                              <Trans i18nKey="integrations:zns.dashboard.noDataAvailable">
                                Không có dữ liệu
                              </Trans>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="pt-2">
                        <div className="text-xs text-gray-500">
                          {accountStats.lastSentAt ? (
                            <span>
                              <Trans
                                i18nKey="integrations:zns.dashboard.lastMessageSent"
                                values={{
                                  time: formatDistanceToNow(
                                    new Date(accountStats.lastSentAt),
                                  ),
                                }}
                              >
                                Tin nhắn gửi gần nhất:{' '}
                                {formatDistanceToNow(
                                  new Date(accountStats.lastSentAt),
                                )}{' '}
                                trước
                              </Trans>
                            </span>
                          ) : (
                            <span>
                              <Trans i18nKey="integrations:zns.dashboard.noMessagesYet">
                                Chưa gửi tin nhắn nào
                              </Trans>
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Health Status and Recent Activity Cards */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Health Status Card */}
                <Card className="overflow-hidden border-0 shadow-sm transition-shadow hover:shadow-md">
                  <CardHeader className="bg-gradient-to-r from-amber-50 to-yellow-50 pb-2 dark:from-amber-950/30 dark:to-yellow-950/30">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <Zap className="h-5 w-5 text-amber-500" />
                        <Trans i18nKey="integrations:zns.connect.dashboard.healthStatus">
                          Health Status
                        </Trans>
                      </CardTitle>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className="text-muted-foreground h-4 w-4" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              <Trans i18nKey="integrations:zns.connect.dashboard.healthStatusTooltip">
                                Current health status of your ZNS connection
                              </Trans>
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4">
                    {isLoading ? (
                      <div className="flex h-32 items-center justify-center">
                        <RefreshCw className="h-6 w-6 animate-spin text-amber-500" />
                      </div>
                    ) : !isReallyConnected ? (
                      <div className="flex flex-col items-center justify-center space-y-4 py-4">
                        <AlertCircle className="h-12 w-12 text-amber-500" />
                        <p className="text-center text-sm">
                          <Trans i18nKey="integrations:zns.connect.dashboard.connectToViewHealth">
                            Connect to ZNS to view health status.
                          </Trans>
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.apiLatency">
                              API Latency
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {healthStatus.apiLatency}ms
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.uptime">
                              Uptime
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {healthStatus.uptime}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.errorCount">
                              Error Count
                            </Trans>
                          </span>
                          <span className="text-sm">
                            {healthStatus.errorCount}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">
                            <Trans i18nKey="integrations:zns.connect.dashboard.lastError">
                              Last Error
                            </Trans>
                          </span>
                          <span className="max-w-[150px] truncate text-sm">
                            {healthStatus.lastError || 'None'}
                          </span>
                        </div>
                        <div className="pt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/zns/connect`,
                              )
                            }
                          >
                            <Settings className="mr-1 h-4 w-4" />
                            <Trans i18nKey="integrations:zns.connect.dashboard.manageConnection">
                              Manage Connection
                            </Trans>
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Mapping Overview Card */}
                <MappingOverviewCard
                  accountId={account?.slug as string}
                  teamAccountId={account?.id as string}
                />
              </div>
            </div>
          </div>
        </div>
      </PageBody>
    </>
  );
}
