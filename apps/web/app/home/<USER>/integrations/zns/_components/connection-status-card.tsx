'use client';

import React, { memo, useState } from 'react';

import { useRouter } from 'next/navigation';

import { motion } from 'framer-motion';
import {
  AlertCircle,
  Clock,
  CheckCircle2,
  User,
  Info,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';

import { Label } from '@kit/ui/label';
import { Skeleton } from '@kit/ui/skeleton';
import { Switch } from '@kit/ui/switch';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';

import { ConnectButton } from './connect-button';

interface ConnectionStatusCardProps {
  accountId: string;
  isConnected: boolean;
  isEnabled: boolean;
  isLoading: boolean;
  isPending: boolean;
  oaConfig: any;
  integration: any;
  onToggleEnabled: (enabled: boolean) => void;
}

const ConnectionStatusCard = ({
  accountId,
  isConnected,
  isEnabled,
  isLoading,
  isPending,
  oaConfig,
  integration,
  onToggleEnabled,
}: ConnectionStatusCardProps) => {
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);

  // Calculate token expiration time
  const tokenExpirationInfo = React.useMemo(() => {
    if (!oaConfig?.token_expires_at) return null;

    const expiresAt = new Date(oaConfig.token_expires_at);
    const now = new Date();
    const isExpired = expiresAt <= now;

    if (isExpired) {
      return {
        isExpired: true,
        text: t('integrations:zns.connect.tokenExpired'),
        color: 'text-destructive',
      };
    }

    const diffMs = expiresAt.getTime() - now.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return {
        isExpired: false,
        text: t('integrations:zns.connect.expiresInDays', { days: diffDays }),
        color: 'text-green-600',
      };
    }

    return {
      isExpired: false,
      text: t('integrations:zns.connect.expiresInHours', { hours: diffHours }),
      color: diffHours < 24 ? 'text-amber-600' : 'text-green-600',
    };
  }, [oaConfig?.token_expires_at, t]);

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: 'easeOut',
      },
    },
  };

  const contentVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delay: 0.2,
        duration: 0.3,
      },
    },
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return t('common:notAvailable');
    return new Date(dateString).toLocaleString();
  };

  return (
    <motion.div initial="hidden" animate="visible" variants={cardVariants}>
      <Card className="overflow-hidden border border-blue-100 shadow-sm transition-all hover:shadow-md h-full">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100 pb-4">
          <div className="flex flex-col space-y-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-blue-800 text-xl font-bold">
                {t('integrations:zns.connect.status')}
              </CardTitle>
              {isConnected && tokenExpirationInfo && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className={`flex items-center gap-1 rounded-full px-3 py-1 text-sm ${tokenExpirationInfo.isExpired ? 'bg-red-50 text-red-700 border-red-200' : tokenExpirationInfo.color === 'text-amber-600' ? 'bg-amber-50 text-amber-700 border-amber-200' : 'bg-green-50 text-green-700 border-green-200'} border shadow-sm`}
                      >
                        <Clock className="h-3.5 w-3.5" />
                        {tokenExpirationInfo.text}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        {t('integrations:zns.connect.tokenExpiresAt')}:{' '}
                        {formatDate(oaConfig?.token_expires_at)}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>

            <div className="flex items-center">
              {isConnected ? (
                <div className="flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1.5 rounded-full border border-green-200 shadow-sm">
                  <CheckCircle2 className="h-4 w-4" />
                  <span className="font-medium">{t('integrations:zns.connect.connected')}</span>
                </div>
              ) : (
                <div className="flex items-center gap-2 bg-red-50 text-red-700 px-3 py-1.5 rounded-full border border-red-200 shadow-sm">
                  <AlertCircle className="h-4 w-4" />
                  <span className="font-medium">{t('integrations:zns.connect.notConnected')}</span>
                </div>
              )}
            </div>

            <CardDescription className="text-blue-600">
              {t('integrations:zns.connect.description')}
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent className="space-y-6 pt-6">
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-20 w-full rounded-md" />
              <div className="flex items-center justify-between">
                <Skeleton className="h-10 w-40 rounded-md" />
                <Skeleton className="h-6 w-10 rounded-full" />
              </div>
              <div className="flex justify-end space-x-2">
                <Skeleton className="h-10 w-32 rounded-md" />
                <Skeleton className="h-10 w-32 rounded-md" />
              </div>
            </div>
          ) : (
            <motion.div variants={contentVariants} className="space-y-6">
              {!isConnected && (
                <div className="bg-gradient-to-r from-red-50 to-orange-50 rounded-lg border border-red-100 p-4 shadow-sm">
                  <div className="flex items-start gap-3">
                    <div className="bg-red-100 rounded-full p-2 mt-0.5">
                      <AlertCircle className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <h4 className="text-red-700 font-medium text-base mb-1">
                        {t('integrations:zns.connect.notConnected')}
                      </h4>
                      <p className="text-red-600 text-sm">
                        {oaConfig && !oaConfig.access_token
                          ? t('integrations:zns.connect.notConnectedDescription')
                          : t('integrations:zns.connect.notConfiguredDescription')}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-blue-100 p-4 shadow-sm transition-all hover:shadow-md">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label
                      htmlFor="zns-enabled"
                      className="text-base font-medium text-blue-700 flex items-center gap-2"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-500">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                        <polyline points="22 4 12 14.01 9 11.01"/>
                      </svg>
                      {t('integrations:zns.connect.enableZns')}
                    </Label>
                    <div className="text-blue-600 text-sm pl-7">
                      {isEnabled
                        ? t('integrations:zns.connect.enabledDescription')
                        : t('integrations:zns.connect.disabledDescription')}
                    </div>
                  </div>
                  <Switch
                    id="zns-enabled"
                    checked={isEnabled}
                    onCheckedChange={onToggleEnabled}
                    disabled={!isConnected || isPending}
                    className="data-[state=checked]:bg-blue-600 h-6 w-11"
                  />
                </div>
              </div>

              <div className="pt-4 border-t border-gray-100">
                <div className="flex flex-col sm:flex-row sm:justify-between gap-3">
                  <Button
                    variant="outline"
                    onClick={() =>
                      router.push(`/home/<USER>/integrations/zns/setup`)
                    }
                    className="border-blue-200 bg-white text-blue-700 hover:bg-blue-50 shadow-sm w-full sm:w-auto"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1.5 h-4 w-4">
                      <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
                      <circle cx="12" cy="12" r="3"/>
                    </svg>
                    {t('integrations:zns.connect.configureOa')}
                  </Button>
                  <ConnectButton
                    accountId={accountId}
                    isConnected={isConnected}
                    oaConfigId={integration?.metadata?.oa_config_id}
                  />
                </div>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default memo(ConnectionStatusCard);
