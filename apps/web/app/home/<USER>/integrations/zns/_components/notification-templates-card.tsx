'use client';

import React, { memo } from 'react';

import { useRouter } from 'next/navigation';

import { motion } from 'framer-motion';
import {
  CheckCircle,
  Clock,
  Info,
  MessageSquare,
  Plus,
  Settings,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Skeleton } from '@kit/ui/skeleton';

interface NotificationTemplatesCardProps {
  accountId: string;
  templates: any[] | null;
  isLoading: boolean;
}

const NotificationTemplatesCard = ({
  accountId,
  templates,
  isLoading,
}: NotificationTemplatesCardProps) => {
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  // Nhóm các template theo tag (TRANSACTION, CUSTOMER_CARE, PROMOTION)
  const groupedTemplates = React.useMemo(() => {
    if (!templates || templates.length === 0) return {};

    return templates.reduce((acc, template) => {
      const tag = template.tag || 'CUSTOMER_CARE';
      if (!acc[tag]) acc[tag] = [];
      acc[tag].push(template);
      return acc;
    }, {});
  }, [templates]);

  // Lấy danh sách các tag
  const templateTags = React.useMemo(() => {
    return Object.keys(groupedTemplates);
  }, [groupedTemplates]);

  // Không cần hàm formatTemplateTag nữa vì đã có bản dịch trong file ngôn ngữ

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: 'easeOut',
        delay: 0.2,
      },
    },
  };

  const handleConfigureTemplates = () => {
    router.push(`/home/<USER>/integrations/zns/templates`);
  };

  // Đã xóa logic copy to clipboard không còn sử dụng

  return (
    <motion.div initial="hidden" animate="visible" variants={cardVariants}>
      <Card className="hover:border-primary/10 overflow-hidden border-2 border-transparent shadow-sm transition-all hover:shadow-md">
        <CardHeader className="from-primary/10 via-primary/5 flex flex-row items-center justify-between bg-gradient-to-r to-transparent">
          <div>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="text-primary h-5 w-5" />
              {t('integrations:zns.connect.templates.title')}
              <Badge
                variant="outline"
                className="bg-primary/10 text-primary border-primary/20 ml-2"
              >
                {templates?.length || 0}
              </Badge>
            </CardTitle>
            <CardDescription className="mt-1">
              {t('integrations:zns.connect.templates.description')}
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleConfigureTemplates}
            className="gap-1 shadow-sm hover:shadow"
          >
            <Settings className="h-4 w-4" />
            {t('integrations:zns.connect.templates.configureTemplates')}
          </Button>
        </CardHeader>
        <CardContent className="pt-6">
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-10 w-full rounded-md" />
              <Skeleton className="h-40 w-full rounded-md" />
            </div>
          ) : templates && templates.length > 0 ? (
            <div className="space-y-6">
              {/* Thống kê tổng quan */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                {/* Tổng số template */}
                <div className="bg-card rounded-lg border p-4 shadow-sm transition-all hover:shadow-md">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <h3 className="text-lg font-semibold">
                        {templates?.length || 0}
                      </h3>
                      <p className="text-muted-foreground text-sm">
                        {t(
                          'integrations:zns.connect.templates.totalTemplates',
                          'Tổng số mẫu',
                        )}
                      </p>
                    </div>
                    <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
                      <MessageSquare className="text-primary h-5 w-5" />
                    </div>
                  </div>
                </div>

                {/* Template theo trạng thái */}
                <div className="bg-card rounded-lg border p-4 shadow-sm transition-all hover:shadow-md">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <h3 className="text-lg font-semibold">
                        {templates?.filter((t) => t.status === 'APPROVED')
                          .length || 0}
                        <span className="text-muted-foreground text-sm">
                          {' '}
                          / {templates?.length || 0}
                        </span>
                      </h3>
                      <p className="text-muted-foreground text-sm">
                        {t(
                          'integrations:zns.connect.templates.approvedTemplates',
                          'Mẫu đã duyệt',
                        )}
                      </p>
                    </div>
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                  </div>
                </div>

                {/* Template đang chờ duyệt */}
                <div className="bg-card rounded-lg border p-4 shadow-sm transition-all hover:shadow-md">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <h3 className="text-lg font-semibold">
                        {templates?.filter((t) => t.status === 'PENDING_REVIEW')
                          .length || 0}
                        <span className="text-muted-foreground text-sm">
                          {' '}
                          / {templates?.length || 0}
                        </span>
                      </h3>
                      <p className="text-muted-foreground text-sm">
                        {t(
                          'integrations:zns.connect.templates.pendingTemplates',
                          'Mẫu đang chờ duyệt',
                        )}
                      </p>
                    </div>
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100">
                      <Clock className="h-5 w-5 text-amber-600" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Danh sách loại template */}
              <div className="bg-card rounded-lg border p-4 shadow-sm transition-all hover:shadow-md">
                <h3 className="text-md mb-4 font-medium">
                  {t(
                    'integrations:zns.connect.templates.templateTypes',
                    'Loại mẫu tin',
                  )}
                </h3>
                <div className="space-y-3">
                  {templateTags.map((tag) => (
                    <div
                      key={tag}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-full">
                          <MessageSquare className="text-primary h-4 w-4" />
                        </div>
                        <span>
                          {t(
                            `integrations:zns.connect.templates.tags.${tag.toLowerCase()}`,
                            tag,
                          )}
                        </span>
                      </div>
                      <Badge
                        variant="outline"
                        className="bg-primary/10 text-primary border-primary/20"
                      >
                        {groupedTemplates[tag]?.length || 0}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              {/* Nút xem tất cả template */}
              <div className="flex justify-center">
                <Button
                  onClick={() =>
                    router.push(`/home/<USER>/integrations/zns/templates`)
                  }
                  className="gap-2 shadow-sm hover:shadow"
                >
                  <Settings className="h-4 w-4" />
                  {t(
                    'integrations:zns.connect.templates.viewAllTemplates',
                    'Xem tất cả mẫu tin',
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Alert
                variant="default"
                className="border-blue-200 bg-blue-50 text-blue-800 shadow-sm"
              >
                <Info className="h-4 w-4 text-blue-500" />
                <AlertTitle>
                  {t('integrations:zns.connect.templates.noTemplates')}
                </AlertTitle>
                <AlertDescription>
                  {t(
                    'integrations:zns.connect.templates.noTemplatesDescription',
                  )}
                </AlertDescription>
              </Alert>

              <Button
                onClick={handleConfigureTemplates}
                className="w-full gap-2 shadow-sm hover:shadow"
              >
                <Plus className="h-4 w-4" />
                {t('integrations:zns.connect.templates.addTemplates')}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default memo(NotificationTemplatesCard);
