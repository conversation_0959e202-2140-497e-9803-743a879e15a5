'use client';

import { useState } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { AlertCircle, CheckCircle, Lock, Settings } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Skeleton } from '@kit/ui/skeleton';
import { Textarea } from '@kit/ui/textarea';
import {
  ZNS_EVENT_TYPES,
  ZNS_DEFAULT_KEYS,
  getZnsKeyConfig,
  categorizeTemplateParams,
  generateAutoMapping,
  getRecipientPathForEventType,
} from '@kit/zns';

import {
  type ParameterConfig,
  generateAutoParameterMapping,
  getAutoMappedParameters,
  getManualParameters,
  validateParameterMapping,
} from '../_lib/parameter-mapping';

// Sử dụng ZNS_EVENT_TYPES từ config để đồng nhất với template creation
// Nhóm event types theo module để dễ quản lý
const eventTypesByModule = {
  orders: [
    ZNS_EVENT_TYPES.find((t) => t.id === 'order_confirmation')!,
    ZNS_EVENT_TYPES.find((t) => t.id === 'payment_confirmation')!,
    ZNS_EVENT_TYPES.find((t) => t.id === 'shipping_notification')!,
  ],
  customer_service: [
    ZNS_EVENT_TYPES.find((t) => t.id === 'customer_support')!,
    ZNS_EVENT_TYPES.find((t) => t.id === 'appointment_reminder')!,
  ],
  marketing: [
    ZNS_EVENT_TYPES.find((t) => t.id === 'product_promotion')!,
    ZNS_EVENT_TYPES.find((t) => t.id === 'general_notification')!,
  ],
};

const modules = [
  {
    id: 'orders',
    name: 'Đơn hàng',
    description: 'Sự kiện liên quan đến đơn hàng và thanh toán',
    icon: '📦',
    events: eventTypesByModule.orders,
  },
  {
    id: 'customer_service',
    name: 'Chăm sóc khách hàng',
    description: 'Sự kiện hỗ trợ và chăm sóc khách hàng',
    icon: '🎧',
    events: eventTypesByModule.customer_service,
  },
  {
    id: 'marketing',
    name: 'Marketing',
    description: 'Sự kiện khuyến mãi và tiếp thị',
    icon: '🎉',
    events: eventTypesByModule.marketing,
  },
];

// Schema cho form tạo mapping
const createMappingSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  template_id: z.string().min(1, 'Template is required'),
  module: z.string().min(1, 'Module is required'),
  event_type: z.string().min(1, 'Event type is required'),
  parameter_mapping: z.record(z.string(), z.string()),
  recipient_path: z.string().default('customer.phone'),
});

type CreateMappingFormValues = z.infer<typeof createMappingSchema>;

interface CreateMappingDialogProps {
  teamAccountId: string;
  onSuccess?: () => void;
}

export function CreateMappingDialog({
  teamAccountId,
  onSuccess,
}: CreateMappingDialogProps) {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);
  const [autoParameters, setAutoParameters] = useState<ParameterConfig[]>([]);
  const [manualParameters, setManualParameters] = useState<ParameterConfig[]>(
    [],
  );

  // Form cho tạo mapping
  const form = useForm<CreateMappingFormValues>({
    resolver: zodResolver(createMappingSchema),
    defaultValues: {
      name: '',
      description: '',
      template_id: '',
      module: 'orders', // Default to orders module
      event_type: '',
      parameter_mapping: {},
      recipient_path: 'customer.phone',
    },
  });

  // Lấy danh sách template (chỉ enabled templates cho mapping)
  const { data: templates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ['zns-templates-for-mapping', teamAccountId],
    queryFn: async () => {
      if (!teamAccountId) return [];

      const response = await axios.get(
        `/api/zns/templates?accountId=${teamAccountId}&forMapping=true`,
      );
      return response.data.data;
    },
    enabled: !!teamAccountId,
  });

  // Mutation để tạo mapping
  const createMappingMutation = useMutation({
    mutationFn: async (
      data: CreateMappingFormValues & { account_id: string },
    ) => {
      const response = await axios.post('/api/zns/mappings', data);
      return response.data;
    },
    onSuccess: () => {
      toast.success(
        t(
          'integrations:zns.mappings.createSuccess',
          'Mapping created successfully',
        ),
      );
      queryClient.invalidateQueries({ queryKey: ['zns-mapping-stats'] });
      queryClient.invalidateQueries({ queryKey: ['zns-top-mappings'] });
      form.reset();
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        t('integrations:zns.mappings.createError', 'Failed to create mapping'),
      );
      console.error('Error creating mapping:', error);
    },
  });

  // Xử lý khi chọn template
  const handleTemplateChange = (templateId: string) => {
    console.log('🔍 handleTemplateChange called with templateId:', templateId);
    const template = templates?.find((t: any) => t.id === templateId);
    console.log('📋 Found template:', template);
    console.log('📋 Template metadata:', template?.metadata);
    console.log('📋 Template params:', template?.metadata?.params);

    setSelectedTemplate(template);

    if (template?.metadata?.params) {
      console.log('✅ Template has params, processing...');

      // Sử dụng ZNS config để phân loại parameters
      const { autoParams, manualParams } = categorizeTemplateParams(template.metadata.params);
      console.log('🔄 Categorization result:', {
        autoParams: autoParams.length,
        manualParams: manualParams.length,
        autoParamsDetails: autoParams,
        manualParamsDetails: manualParams
      });

      // Chuyển đổi sang ParameterConfig format cho UI
      const autoParameterConfigs: ParameterConfig[] = autoParams.map(({ param, config }) => ({
        name: param.name,
        type: config.paramType,
        isAutoMapped: true,
        autoPath: config.path,
        description: config.description,
        category: config.category,
        example: config.example,
      }));

      const manualParameterConfigs: ParameterConfig[] = manualParams.map(param => ({
        name: param.name,
        type: param.type || '13',
        isAutoMapped: false,
        description: `Tham số tùy chỉnh: ${param.name}`,
        placeholder: `Nhập giá trị cho ${param.name}`,
        category: 'custom' as const,
      }));

      console.log('🎯 Setting state:', {
        autoParameterConfigs: autoParameterConfigs.length,
        manualParameterConfigs: manualParameterConfigs.length,
      });

      setAutoParameters(autoParameterConfigs);
      setManualParameters(manualParameterConfigs);

      // Tự động tạo mapping cho auto parameters sử dụng ZNS config
      const autoMapping = generateAutoMapping(template.metadata.params);
      console.log('🔗 Auto mapping generated:', autoMapping);

      // Reset và set parameter mapping với auto mapping
      form.setValue('parameter_mapping', autoMapping);

      console.log('Template parameters categorized using ZNS config:', {
        total: template.metadata.params.length,
        auto: autoParameterConfigs.length,
        manual: manualParameterConfigs.length,
        autoMapping,
        autoParams: autoParameterConfigs.map(p => ({ name: p.name, path: p.autoPath })),
        manualParams: manualParameterConfigs.map(p => p.name),
      });
    } else {
      console.log('❌ Template has no params or template not found');
      console.log('❌ Condition check:', {
        hasTemplate: !!template,
        hasMetadata: !!template?.metadata,
        hasParams: !!template?.metadata?.params,
        paramsLength: template?.metadata?.params?.length || 0,
      });

      // Reset nếu không có parameters
      setAutoParameters([]);
      setManualParameters([]);
      form.setValue('parameter_mapping', {});
    }
  };

  // Xử lý khi submit form
  const onSubmit = (values: CreateMappingFormValues) => {
    // Validate parameter mapping sử dụng ZNS config
    if (selectedTemplate?.metadata?.params) {
      const { manualParams } = categorizeTemplateParams(selectedTemplate.metadata.params);
      const errors: string[] = [];

      // Chỉ validate manual parameters
      manualParams.forEach(param => {
        if (!values.parameter_mapping[param.name] || values.parameter_mapping[param.name].trim() === '') {
          errors.push(`Tham số "${param.name}" cần được cấu hình`);
        }
      });

      if (errors.length > 0) {
        errors.forEach((error) => {
          toast.error(error);
        });
        return;
      }
    }

    createMappingMutation.mutate({
      ...values,
      account_id: teamAccountId, // API expects account_id, not team_account_id
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Thông tin cơ bản</h3>

          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('integrations:zns.mappings.form.name', 'Tên')}
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Order confirmation notification"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('integrations:zns.mappings.form.description', 'Mô tả')}
                </FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Send notification when a new order is created"
                    value={field.value || ''}
                    rows={2}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Event Configuration */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Cấu hình sự kiện</h3>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="module"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('integrations:zns.mappings.form.module', 'Module')}
                  </FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      form.setValue('event_type', '');
                      form.setValue('recipient_path', 'customer.phone'); // Reset về default
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select module" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {modules.map((module) => (
                        <SelectItem key={module.id} value={module.id}>
                          <div className="flex items-center gap-2">
                            <span className="text-lg">{module.icon}</span>
                            <div>
                              <div className="font-medium">{module.name}</div>
                              <div className="text-muted-foreground text-xs">
                                {module.description}
                              </div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="event_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t(
                      'integrations:zns.mappings.form.eventType',
                      'Loại sự kiện',
                    )}
                  </FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      // Tự động set recipient_path dựa trên event type
                      const recipientPath = getRecipientPathForEventType(value);
                      form.setValue('recipient_path', recipientPath);
                      console.log('🎯 Event type changed:', { eventType: value, recipientPath });
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select event" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {modules
                        .find((m) => m.id === form.getValues('module'))
                        ?.events.map((event) => (
                          <SelectItem key={event.id} value={event.id}>
                            <div className="flex items-center gap-2">
                              <span className="text-lg">{event.icon}</span>
                              <div>
                                <div className="font-medium">{event.name}</div>
                                <div className="text-muted-foreground text-xs">
                                  {event.description}
                                </div>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Template Configuration */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Cấu hình template</h3>

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="template_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('integrations:zns.mappings.form.template', 'Mẫu tin')}
                  </FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleTemplateChange(value);
                    }}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select template" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {isLoadingTemplates ? (
                        <SelectItem value="loading" disabled>
                          Loading templates...
                        </SelectItem>
                      ) : templates?.length > 0 ? (
                        templates.map((template: any) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.template_name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="none" disabled>
                          No templates available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="recipient_path"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Lock className="h-4 w-4 text-green-600" />
                    {t(
                      'integrations:zns.mappings.form.recipientPath',
                      'Đường dẫn đến số điện thoại',
                    )}
                    <Badge variant="outline" className="border-green-300 bg-green-100 text-xs text-green-600">
                      Tự động
                    </Badge>
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      disabled
                      className="bg-green-50 border-green-200 text-green-700"
                      placeholder="customer.phone"
                    />
                  </FormControl>
                  <FormDescription className="text-xs text-green-600">
                    🔒 Được tự động xác định dựa trên loại sự kiện đã chọn. Đảm bảo tính nhất quán và tránh lỗi.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Debug info */}
        {selectedTemplate && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200 text-xs">
            <div><strong>🔍 Debug Info:</strong></div>
            <div>Selected Template: {selectedTemplate.template_name || selectedTemplate.name || 'Unknown'}</div>
            <div>Template ID: {selectedTemplate.id || selectedTemplate.template_id || 'Unknown'}</div>
            <div>Auto Parameters: {autoParameters.length}</div>
            <div>Manual Parameters: {manualParameters.length}</div>
            <div>Has Metadata: {selectedTemplate.metadata ? 'Yes' : 'No'}</div>
            <div>Has Params: {selectedTemplate.metadata?.params ? 'Yes' : 'No'}</div>
            <div>Params Count: {selectedTemplate.metadata?.params?.length || 0}</div>
            {selectedTemplate.metadata?.params && (
              <div>Params: {JSON.stringify(selectedTemplate.metadata.params.map((p: any) => p.name))}</div>
            )}
          </div>
        )}

        {selectedTemplate &&
          (autoParameters.length > 0 || manualParameters.length > 0) && (
            <div className="space-y-4">
              <h3 className="flex items-center gap-2 text-lg font-medium">
                <Settings className="h-4 w-4" />
                Parameter Mapping
              </h3>

              {/* Auto-mapped Parameters */}
              {autoParameters.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <h4 className="text-sm font-medium text-green-700">
                      Tự động ánh xạ ({autoParameters.length})
                    </h4>
                  </div>
                  <div className="grid max-h-40 grid-cols-1 gap-2 overflow-y-auto">
                    {autoParameters.map((param, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between rounded border border-green-200 bg-green-50 p-2"
                      >
                        <div className="flex items-center gap-2">
                          <Lock className="h-3.5 w-3.5 text-green-600" />
                          <span className="text-sm font-medium">
                            {param.name}
                          </span>
                          <Badge
                            variant="outline"
                            className="border-green-300 bg-green-100 text-xs text-green-700"
                          >
                            {param.type}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <code className="rounded bg-green-100 px-2 py-1 text-xs text-green-700">
                            {param.autoPath}
                          </code>
                          <Badge
                            variant="outline"
                            className="border-green-300 bg-green-100 text-xs text-green-600"
                          >
                            Auto
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Manual Parameters */}
              {manualParameters.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-orange-600" />
                    <h4 className="text-sm font-medium text-orange-700">
                      Cần cấu hình thủ công ({manualParameters.length})
                    </h4>
                  </div>
                  <div className="grid grid-cols-1 gap-3">
                    {manualParameters.map((param, index) => (
                      <div
                        key={index}
                        className="rounded border border-orange-200 bg-orange-50 p-3"
                      >
                        <div className="mb-2 flex items-center gap-2">
                          <Settings className="h-3.5 w-3.5 text-orange-600" />
                          <Label className="text-sm font-medium">
                            {param.name}
                          </Label>
                          <Badge
                            variant="outline"
                            className="border-orange-300 bg-orange-100 text-xs text-orange-700"
                          >
                            {param.type}
                          </Badge>
                          <Badge
                            variant="outline"
                            className="border-orange-300 bg-orange-100 text-xs text-orange-600"
                          >
                            Manual
                          </Badge>
                        </div>
                        <Input
                          placeholder={
                            param.placeholder ||
                            `Nhập giá trị cho ${param.name}`
                          }
                          value={
                            form.getValues(`parameter_mapping.${param.name}`) ||
                            ''
                          }
                          onChange={(e) => {
                            const mapping = {
                              ...form.getValues('parameter_mapping'),
                            };
                            mapping[param.name] = e.target.value;
                            form.setValue('parameter_mapping', mapping);
                          }}
                          className="mb-1 border-orange-300 focus:border-orange-500"
                        />
                        <p className="text-xs text-orange-600">
                          {param.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Summary */}
              <div className="rounded border border-blue-200 bg-blue-50 p-3">
                <div className="mb-2 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-blue-600" />
                  <h4 className="text-sm font-medium text-blue-700">Tóm tắt</h4>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs text-blue-600">
                  <p>
                    • <strong>{autoParameters.length}</strong> tham số tự động
                  </p>
                  <p>
                    • <strong>{manualParameters.length}</strong> tham số thủ
                    công
                  </p>
                </div>
              </div>
            </div>
          )}

        {/* Submit Button */}
        <div className="flex justify-end border-t pt-4">
          <Button
            type="submit"
            disabled={createMappingMutation.isPending}
            size="lg"
          >
            {createMappingMutation.isPending ? (
              <span className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 rounded-full" />
                {t('integrations:zns.mappings.createNew', 'Tạo mapping mới')}
              </span>
            ) : (
              <span className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                {t('integrations:zns.mappings.createNew', 'Tạo mapping mới')}
              </span>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
