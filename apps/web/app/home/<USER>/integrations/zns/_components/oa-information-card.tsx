'use client';

import React, { memo, useState } from 'react';

import { motion } from 'framer-motion';
import {
  AppWindow,
  Calendar,
  ChevronDown,
  ChevronUp,
  Clock,
  ExternalLink,
  Hash,
  Info,
  Key,
  RefreshCcw,
  Shield,
  User,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { Avatar, AvatarFallback, AvatarImage } from '@kit/ui/avatar';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@kit/ui/collapsible';
import { Label } from '@kit/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';

interface OaInformationCardProps {
  oaConfig: any;
  oaConfigType: string | null;
}

const OaInformationCard = ({
  oaConfig,
  oaConfigType,
}: OaInformationCardProps) => {
  const { t } = useTranslation(['integrations', 'common']);
  const [isConnectionDetailsOpen, setIsConnectionDetailsOpen] = useState(false);

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return t('common:notAvailable');
    return new Date(dateString).toLocaleString();
  };

  // Format token for display (show only first and last few characters)
  const formatToken = (token: string) => {
    if (!token) return t('common:notAvailable');
    if (token.length <= 10) return token;
    return `${token.substring(0, 5)}...${token.substring(token.length - 5)}`;
  };

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: 'easeOut',
        delay: 0.1,
      },
    },
  };
  // console.log(111, oaConfig);
  return (
    <motion.div initial="hidden" animate="visible" variants={cardVariants}>
      <Card className="overflow-hidden border border-indigo-100 shadow-sm transition-all hover:shadow-md">
        <CardHeader className="bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-indigo-100 pb-4">
          <div className="flex flex-col space-y-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-indigo-800 text-xl font-bold flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-600">
                  <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/>
                  <circle cx="12" cy="7" r="4"/>
                </svg>
                {t('integrations:zns.connect.oaInformation')}
              </CardTitle>
              {oaConfigType && (
                <Badge
                  variant="outline"
                  className={`shadow-sm font-medium ${oaConfigType === 'system' ? 'border-blue-200 bg-blue-50 text-blue-700' : ''} ${oaConfigType === 'theme' ? 'border-purple-200 bg-purple-50 text-purple-700' : ''} ${oaConfigType === 'account' ? 'border-green-200 bg-green-50 text-green-700' : ''} `}
                >
                  {oaConfigType === 'system' && (
                    <>
                      <Shield className="h-3 w-3 mr-1" />
                      {t('integrations:zns.connect.systemDefault')}
                    </>
                  )}
                  {oaConfigType === 'theme' && (
                    <>
                      <AppWindow className="h-3 w-3 mr-1" />
                      {t('integrations:zns.connect.theme')}
                    </>
                  )}
                  {oaConfigType === 'account' && (
                    <>
                      <User className="h-3 w-3 mr-1" />
                      {t('integrations:zns.connect.account')}
                    </>
                  )}
                </Badge>
              )}
            </div>
            <CardDescription className="text-indigo-600">
              {t('integrations:zns.connect.oaInformationDescription')}
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent className="pt-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="mb-6 grid w-full grid-cols-2 bg-gray-100 p-1 rounded-lg">
              <TabsTrigger value="basic" className="data-[state=active]:bg-white data-[state=active]:text-indigo-700 data-[state=active]:shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-2">
                  <rect width="18" height="18" x="3" y="3" rx="2" />
                  <path d="M3 9h18" />
                  <path d="M9 21V9" />
                </svg>
                {t('integrations:zns.connect.basicInfo')}
              </TabsTrigger>
              <TabsTrigger value="advanced" className="data-[state=active]:bg-white data-[state=active]:text-indigo-700 data-[state=active]:shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-2">
                  <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                  <circle cx="12" cy="12" r="3" />
                </svg>
                {t('integrations:zns.connect.advancedInfo')}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="basic" className="space-y-6">
              {/* User Info Card */}
              {oaConfig?.oa_metadata?.user_info && (
                <div className="bg-gradient-to-r from-indigo-50 to-purple-50 mb-4 rounded-lg border border-indigo-100 p-5 shadow-sm transition-all hover:shadow-md">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-20 w-20 border-2 border-white shadow-md">
                      {oaConfig.oa_metadata.user_info.picture ? (
                        <AvatarImage
                          src={oaConfig.oa_metadata.user_info.picture}
                          alt="User"
                        />
                      ) : (
                        <AvatarFallback className="bg-indigo-100 text-indigo-700">
                          <User className="h-8 w-8" />
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <div>
                      <div className="text-xl font-bold text-indigo-800">
                        {oaConfig.oa_metadata.user_info.name ||
                          t('integrations:zns.connect.zaloUser')}
                      </div>
                      <div className="mt-1 flex items-center gap-2 text-sm">
                        <Badge variant="outline" className="bg-white border-indigo-200 text-indigo-700 font-mono shadow-sm">
                          {oaConfig.oa_metadata.user_info.id}
                        </Badge>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Info className="text-indigo-400 h-4 w-4 cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{t('integrations:zns.connect.zaloUserId')}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* OA ID */}
                <div className="bg-white rounded-lg border border-blue-100 p-4 shadow-sm transition-all hover:shadow-md">
                  <div className="mb-2 flex items-center gap-2 text-blue-700">
                    <div className="bg-blue-100 rounded-full p-1.5">
                      <Hash className="h-4 w-4" />
                    </div>
                    <Label className="text-sm font-medium">
                      {t('integrations:zns.configure.oa_id')}
                    </Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-base font-bold font-mono bg-blue-50 px-3 py-1.5 rounded-md border border-blue-100">
                      {oaConfig.oa_id || t('common:notAvailable')}
                    </div>
                    {!oaConfig.oa_id && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            <Info className="text-blue-400 h-4 w-4" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{t('integrations:zns.connect.oaIdMissing')}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </div>

                {/* App ID */}
                <div className="bg-white rounded-lg border border-indigo-100 p-4 shadow-sm transition-all hover:shadow-md">
                  <div className="mb-2 flex items-center gap-2 text-indigo-700">
                    <div className="bg-indigo-100 rounded-full p-1.5">
                      <AppWindow className="h-4 w-4" />
                    </div>
                    <Label className="text-sm font-medium">
                      {t('integrations:zns.setup.form.appId')}
                    </Label>
                  </div>
                  <div className="flex flex-wrap items-center gap-2">
                    <div className="text-base font-bold font-mono bg-indigo-50 px-3 py-1.5 rounded-md border border-indigo-100 overflow-hidden text-ellipsis max-w-[200px] sm:max-w-[250px] md:max-w-[300px]">
                      {oaConfig.app_id || t('common:notAvailable')}
                    </div>
                    {oaConfig.app_id && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="ml-2 border-indigo-200 text-indigo-700 hover:bg-indigo-50 text-xs whitespace-nowrap"
                        onClick={() => window.open(`https://developers.zalo.me/app/${oaConfig.app_id}`, '_blank')}
                      >
                        <ExternalLink className="h-3.5 w-3.5 mr-1" />
                        {t('integrations:zns.connect.openInZaloDev', 'Mở trong Zalo Dev')}
                      </Button>
                    )}
                  </div>
                </div>

                {/* Last Connected */}
                <div className="bg-card rounded-lg border p-3 shadow-sm transition-all hover:shadow-md">
                  <div className="text-muted-foreground mb-2 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <Label className="text-sm font-medium">
                      {t('integrations:zns.connect.lastConnected')}
                    </Label>
                  </div>
                  <div className="space-y-1">
                    <div className="text-base font-medium">
                      {oaConfig.oa_metadata?.last_connected
                        ? formatDate(oaConfig.oa_metadata.last_connected)
                        : t('common:notAvailable')}
                    </div>
                    {oaConfig.oa_metadata?.connected_by && (
                      <div className="text-muted-foreground text-xs">
                        {t('integrations:zns.connect.connectedBy')}:{' '}
                        {oaConfig.oa_metadata.connected_by}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="advanced" className="space-y-6">
              {/* Connection Details Collapsible */}
              {oaConfig?.oa_metadata && (
                <Collapsible
                  open={isConnectionDetailsOpen}
                  onOpenChange={setIsConnectionDetailsOpen}
                  className="bg-card mb-6 rounded-lg border shadow-sm transition-all hover:shadow-md"
                >
                  <div className="flex items-center justify-between p-4">
                    <div className="flex items-center gap-2">
                      <Shield className="text-muted-foreground h-4 w-4" />
                      <h3 className="text-sm font-medium">
                        {t('integrations:zns.connect.connectionDetails')}
                      </h3>
                    </div>
                    <CollapsibleTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        {isConnectionDetailsOpen ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                        <span className="sr-only">Toggle</span>
                      </Button>
                    </CollapsibleTrigger>
                  </div>
                  <CollapsibleContent>
                    <div className="space-y-2 px-4 pb-4">
                      <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
                        {oaConfig.oa_metadata.configured_at && (
                          <div className="bg-muted/30 flex items-center justify-between rounded-md px-3 py-2">
                            <span className="text-sm font-medium">
                              {t('integrations:zns.connect.configuredAt')}
                            </span>
                            <span className="text-sm">
                              {formatDate(oaConfig.oa_metadata.configured_at)}
                            </span>
                          </div>
                        )}
                        {oaConfig.oa_metadata.token_validation_time && (
                          <div className="bg-muted/30 flex items-center justify-between rounded-md px-3 py-2">
                            <span className="text-sm font-medium">
                              {t('integrations:zns.connect.tokenValidatedAt')}
                            </span>
                            <span className="text-sm">
                              {formatDate(
                                oaConfig.oa_metadata.token_validation_time,
                              )}
                            </span>
                          </div>
                        )}
                        {oaConfig.oa_metadata.last_refreshed && (
                          <div className="bg-muted/30 flex items-center justify-between rounded-md px-3 py-2">
                            <span className="text-sm font-medium">
                              {t('integrations:zns.connect.tokenLastRefreshed')}
                            </span>
                            <span className="text-sm">
                              {formatDate(oaConfig.oa_metadata.last_refreshed)}
                            </span>
                          </div>
                        )}
                        {oaConfig.oa_metadata.auth_timestamp && (
                          <div className="bg-muted/30 flex items-center justify-between rounded-md px-3 py-2">
                            <span className="text-sm font-medium">
                              {t('integrations:zns.connect.authTimestamp')}
                            </span>
                            <span className="text-sm">
                              {formatDate(oaConfig.oa_metadata.auth_timestamp)}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              )}

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Access Token */}
                <div className="bg-card rounded-lg border p-3 shadow-sm transition-all hover:shadow-md">
                  <div className="text-muted-foreground mb-2 flex items-center gap-2">
                    <Key className="h-4 w-4" />
                    <Label className="text-sm font-medium">
                      {t('integrations:zns.setup.form.accessToken')}
                    </Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="font-mono text-base font-medium">
                      {formatToken(oaConfig.access_token)}
                    </div>
                    {oaConfig.access_token && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 rounded-full p-0"
                            >
                              <Info className="text-muted-foreground h-3.5 w-3.5" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{t('integrations:zns.connect.tokenInfo')}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </div>

                {/* Refresh Token */}
                <div className="bg-card rounded-lg border p-3 shadow-sm transition-all hover:shadow-md">
                  <div className="text-muted-foreground mb-2 flex items-center gap-2">
                    <RefreshCcw className="h-4 w-4" />
                    <Label className="text-sm font-medium">
                      {t('integrations:zns.setup.form.refreshToken')}
                    </Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="font-mono text-base font-medium">
                      {formatToken(oaConfig.refresh_token)}
                    </div>
                  </div>
                </div>

                {/* Configured At */}
                {oaConfig.oa_metadata?.configured_at && (
                  <div className="bg-card rounded-lg border p-3 shadow-sm transition-all hover:shadow-md">
                    <div className="text-muted-foreground mb-2 flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <Label className="text-sm font-medium">
                        {t('integrations:zns.connect.configuredAt')}
                      </Label>
                    </div>
                    <div className="text-base font-medium">
                      {formatDate(oaConfig.oa_metadata.configured_at)}
                    </div>
                  </div>
                )}

                {/* Token Validated At */}
                {oaConfig.oa_metadata?.token_validation_time && (
                  <div className="bg-card rounded-lg border p-3 shadow-sm transition-all hover:shadow-md">
                    <div className="text-muted-foreground mb-2 flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <Label className="text-sm font-medium">
                        {t('integrations:zns.connect.tokenValidatedAt')}
                      </Label>
                    </div>
                    <div className="text-base font-medium">
                      {formatDate(oaConfig.oa_metadata.token_validation_time)}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default memo(OaInformationCard);
