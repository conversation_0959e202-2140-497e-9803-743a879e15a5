'use client';

import { useState } from 'react';
import { ChevronDown, ChevronRight, Copy, Info } from 'lucide-react';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@kit/ui/collapsible';

import { getKeysByCategory, getDefaultKeys } from '../_lib/parameter-mapping';

interface ZnsKeyDocumentationProps {
  className?: string;
}

export function ZnsKeyDocumentation({ className }: ZnsKeyDocumentationProps) {
  const [openCategories, setOpenCategories] = useState<Record<string, boolean>>({
    customer: true, // Mở customer category mặc định
  });

  const categories = [
    { key: 'customer', name: '<PERSON>h<PERSON><PERSON> hàng', description: 'Thông tin khách hàng' },
    { key: 'order', name: '<PERSON><PERSON><PERSON> hàng', description: 'Thông tin đơn hàng' },
    { key: 'payment', name: '<PERSON>h to<PERSON>', description: 'Thông tin thanh toán' },
    { key: 'product', name: 'Sản phẩm', description: 'Thông tin sản phẩm' },
    { key: 'system', name: 'Hệ thống', description: 'Thông tin hệ thống' },
  ];

  const toggleCategory = (category: string) => {
    setOpenCategories(prev => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getParamTypeColor = (type: string) => {
    const typeMap: Record<string, string> = {
      '1': 'bg-blue-100 text-blue-700 border-blue-300',
      '2': 'bg-green-100 text-green-700 border-green-300',
      '3': 'bg-purple-100 text-purple-700 border-purple-300',
      '4': 'bg-orange-100 text-orange-700 border-orange-300',
      '5': 'bg-red-100 text-red-700 border-red-300',
      '6': 'bg-yellow-100 text-yellow-700 border-yellow-300',
      '7': 'bg-pink-100 text-pink-700 border-pink-300',
      '8': 'bg-indigo-100 text-indigo-700 border-indigo-300',
      '9': 'bg-teal-100 text-teal-700 border-teal-300',
      '10': 'bg-cyan-100 text-cyan-700 border-cyan-300',
      '11': 'bg-lime-100 text-lime-700 border-lime-300',
      '12': 'bg-emerald-100 text-emerald-700 border-emerald-300',
      '13': 'bg-gray-100 text-gray-700 border-gray-300',
    };
    return typeMap[type] || 'bg-gray-100 text-gray-700 border-gray-300';
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Info className="h-5 w-5" />
          Key mặc định được hỗ trợ
        </CardTitle>
        <CardDescription>
          Sử dụng các key sau trong template để tự động lấy dữ liệu từ hệ thống.
          Các key khác sẽ cần cấu hình thủ công khi tạo mapping.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {categories.map(category => {
          const keys = getKeysByCategory(category.key);
          if (keys.length === 0) return null;

          return (
            <Collapsible
              key={category.key}
              open={openCategories[category.key]}
              onOpenChange={() => toggleCategory(category.key)}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full justify-between p-3 h-auto"
                >
                  <div className="flex items-center gap-3">
                    {openCategories[category.key] ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                    <div className="text-left">
                      <div className="font-medium">{category.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {category.description} ({keys.length} key)
                      </div>
                    </div>
                  </div>
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-2 mt-2">
                {keys.map(key => (
                  <div
                    key={key.key}
                    className="flex items-center justify-between p-3 bg-muted/50 rounded-lg border"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <code className="font-mono text-sm font-medium bg-background px-2 py-1 rounded border">
                          {key.key}
                        </code>
                        <Badge
                          variant="outline"
                          className={`text-xs ${getParamTypeColor(key.paramType)}`}
                        >
                          {key.paramType}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground mb-1">
                        {key.description}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        <span className="font-medium">Ví dụ:</span> {key.example}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        <span className="font-medium">Đường dẫn:</span>{' '}
                        <code className="bg-background px-1 rounded">{key.path}</code>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(key.key)}
                      className="ml-2 h-8 w-8 p-0"
                    >
                      <Copy className="h-3.5 w-3.5" />
                    </Button>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>
          );
        })}

        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start gap-2">
            <Info className="h-4 w-4 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-700">
              <div className="font-medium mb-1">Lưu ý quan trọng:</div>
              <ul className="space-y-1 text-xs">
                <li>• Các key trên sẽ được <strong>tự động map</strong> khi tạo mapping</li>
                <li>• Key khác sẽ cần <strong>cấu hình thủ công</strong></li>
                <li>• Khuyến khích sử dụng key mặc định để đảm bảo tính nhất quán</li>
                <li>• Click vào icon copy để sao chép key vào clipboard</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-sm font-medium mb-2">Tổng quan:</div>
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <span className="font-medium">Tổng số key:</span> {getDefaultKeys().length}
            </div>
            <div>
              <span className="font-medium">Categories:</span> {categories.length}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
