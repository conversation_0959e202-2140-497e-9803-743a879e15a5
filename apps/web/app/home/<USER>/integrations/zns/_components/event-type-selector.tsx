'use client';

import { useState } from 'react';
import { ChevronDown, ChevronRight, Copy, Info, Lightbulb } from 'lucide-react';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@kit/ui/collapsible';
import { FormControl, FormDescription, FormItem, FormLabel, FormMessage } from '@kit/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';

import { ZNS_EVENT_TYPES, getKeySuggestionsForEventType, type ZnsEventType } from '@kit/zns';

interface EventTypeSelectorProps {
  value?: string;
  onValueChange: (value: string) => void;
  className?: string;
}

export function EventTypeSelector({ value, onValueChange, className }: EventTypeSelectorProps) {
  const [showKeySuggestions, setShowKeySuggestions] = useState(false);
  
  const selectedEventType = value ? ZNS_EVENT_TYPES.find(type => type.id === value) : null;
  const keySuggestions = value ? getKeySuggestionsForEventType(value) : [];

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getParamTypeColor = (type: string) => {
    const typeMap: Record<string, string> = {
      '1': 'bg-blue-100 text-blue-700 border-blue-300',
      '2': 'bg-green-100 text-green-700 border-green-300',
      '3': 'bg-purple-100 text-purple-700 border-purple-300',
      '4': 'bg-orange-100 text-orange-700 border-orange-300',
      '5': 'bg-red-100 text-red-700 border-red-300',
      '6': 'bg-yellow-100 text-yellow-700 border-yellow-300',
      '7': 'bg-pink-100 text-pink-700 border-pink-300',
      '8': 'bg-indigo-100 text-indigo-700 border-indigo-300',
      '9': 'bg-teal-100 text-teal-700 border-teal-300',
      '10': 'bg-cyan-100 text-cyan-700 border-cyan-300',
      '11': 'bg-lime-100 text-lime-700 border-lime-300',
      '12': 'bg-emerald-100 text-emerald-700 border-emerald-300',
      '13': 'bg-gray-100 text-gray-700 border-gray-300',
    };
    return typeMap[type] || 'bg-gray-100 text-gray-700 border-gray-300';
  };

  return (
    <div className={className}>
      <FormItem>
        <FormLabel className="flex items-center gap-2">
          <Info className="h-4 w-4" />
          Loại sự kiện
        </FormLabel>
        <FormControl>
          <Select value={value} onValueChange={onValueChange}>
            <SelectTrigger>
              <SelectValue placeholder="Chọn loại sự kiện để xem key suggestions" />
            </SelectTrigger>
            <SelectContent>
              {ZNS_EVENT_TYPES.map((eventType) => (
                <SelectItem key={eventType.id} value={eventType.id}>
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{eventType.icon}</span>
                    <div>
                      <div className="font-medium">{eventType.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {eventType.description}
                      </div>
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </FormControl>
        <FormDescription>
          Chọn loại sự kiện để xem các key mặc định được khuyến nghị cho template này.
        </FormDescription>
        <FormMessage />
      </FormItem>

      {/* Key Suggestions */}
      {selectedEventType && (
        <Card className="mt-4">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-yellow-600" />
                <span>Key được khuyến nghị</span>
                <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300">
                  {keySuggestions.length} key
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowKeySuggestions(!showKeySuggestions)}
              >
                {showKeySuggestions ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            </CardTitle>
            <CardDescription>
              Cho loại sự kiện: <strong>{selectedEventType.icon} {selectedEventType.name}</strong>
            </CardDescription>
          </CardHeader>
          
          <Collapsible open={showKeySuggestions} onOpenChange={setShowKeySuggestions}>
            <CollapsibleContent>
              <CardContent className="space-y-3">
                {/* Example */}
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-700">Ví dụ template:</span>
                  </div>
                  <p className="text-sm text-blue-700 font-mono bg-white p-2 rounded border">
                    {selectedEventType.example}
                  </p>
                </div>

                {/* Key suggestions */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Key mặc định được khuyến nghị:</h4>
                  {keySuggestions.map((key) => (
                    <div
                      key={key.key}
                      className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <code className="font-mono text-sm font-medium bg-white px-2 py-1 rounded border">
                            {key.key}
                          </code>
                          <Badge
                            variant="outline"
                            className={`text-xs ${getParamTypeColor(key.paramType)}`}
                          >
                            {key.paramType}
                          </Badge>
                        </div>
                        <div className="text-sm text-green-700 mb-1">
                          {key.description}
                        </div>
                        <div className="text-xs text-green-600">
                          <span className="font-medium">Ví dụ:</span> {key.example}
                        </div>
                        <div className="text-xs text-green-600">
                          <span className="font-medium">Đường dẫn:</span>{' '}
                          <code className="bg-white px-1 rounded">{key.path}</code>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(`<${key.key}>`)}
                        className="ml-2 h-8 w-8 p-0"
                        title={`Copy <${key.key}>`}
                      >
                        <Copy className="h-3.5 w-3.5" />
                      </Button>
                    </div>
                  ))}
                </div>

                {/* Usage tips */}
                <div className="p-3 bg-amber-50 rounded-lg border border-amber-200">
                  <div className="flex items-start gap-2">
                    <Lightbulb className="h-4 w-4 text-amber-600 mt-0.5" />
                    <div className="text-sm text-amber-700">
                      <div className="font-medium mb-1">Cách sử dụng:</div>
                      <ul className="space-y-1 text-xs">
                        <li>• Sử dụng key trong dấu &lt;&gt; ví dụ: <code>&lt;customer_name&gt;</code></li>
                        <li>• Key này sẽ được <strong>tự động map</strong> khi tạo mapping</li>
                        <li>• Click icon copy để sao chép key vào clipboard</li>
                        <li>• Key khác ngoài danh sách sẽ cần cấu hình thủ công</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      )}
    </div>
  );
}
