'use client';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { useQuery } from '@tanstack/react-query';
import { LogIn } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { isUUID } from '@kit/shared/uuid';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Button } from '@kit/ui/button';

interface ConnectButtonProps {
  accountId: string;
  isConnected?: boolean;
  oaConfigId?: string;
  appId?: string;
}

export function ConnectButton({
  accountId,
  oaConfigId,
  appId,
}: ConnectButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const supabase = useSupabase();
  const { t } = useTranslation(['integrations', 'common']);

  // L<PERSON>y thông tin OA configuration nếu có oaConfigId
  const { data: oaConfig, error: oaConfigError } = useQuery({
    queryKey: ['oa-config', oaConfigId],
    queryFn: async () => {
      if (!oaConfigId) return null;

      const { data, error } = await supabase
        .from('oa_configurations')
        .select('app_id, secret_key, id, token_expires_at, access_token')
        .eq('id', oaConfigId)
        .single();

      if (error) {
        console.error('Error fetching OA config:', error);
        throw error;
      }

      if (!data?.app_id || !data?.secret_key) {
        throw new Error(
          'Invalid OA configuration: missing app_id or secret_key',
        );
      }

      return data;
    },
    enabled: !!oaConfigId,
  });

  // Lấy thông tin OA configuration dựa vào accountId nếu không có oaConfigId
  const { data: accountOaConfig, error: accountOaConfigError } = useQuery({
    queryKey: ['account-oa-config', accountId],
    queryFn: async () => {
      if (!accountId || oaConfigId) return null;

      // Lấy thông tin account để lấy UUID
      let realAccountId = accountId;

      // Kiểm tra xem accountId có phải là UUID không
      if (!isUUID(accountId)) {
        try {
          // Nếu accountId là slug, cần lấy UUID tương ứng
          const { data: account, error: accountError } = await supabase
            .from('accounts')
            .select('id')
            .eq('slug', accountId)
            .single();

          if (accountError) {
            // Nếu không tìm thấy bằng slug, sử dụng accountId hiện tại
            console.warn(
              'Could not find account by slug, using provided ID:',
              accountId
            );
          } else if (account) {
            realAccountId = account.id;
          }
        } catch (err) {
          console.warn('Error finding account by slug, using provided ID:', accountId);
        }
      }

      const { data, error } = await supabase
        .from('oa_configurations')
        .select('app_id, secret_key, id, token_expires_at, access_token')
        .eq('account_id', realAccountId)
        .maybeSingle();

      if (error) {
        console.error('Error fetching account OA config:', error);
        throw error;
      }

      // Nếu không có OA configuration, tạo mới
      if (!data) {
        // Trường hợp này cần tạo OA configuration trước
        console.log(
          'No OA configuration found for account, need to create one first',
        );
        return null;
      }

      if (!data.app_id || !data.secret_key) {
        throw new Error(
          'Invalid OA configuration: missing app_id or secret_key',
        );
      }

      return data;
    },
    enabled: !!accountId && !oaConfigId,
  });

  // Kiểm tra token có còn hạn hay không
  const activeConfig = oaConfig || accountOaConfig;
  const hasValidToken =
    activeConfig &&
    activeConfig.access_token &&
    activeConfig.token_expires_at &&
    new Date(activeConfig.token_expires_at) > new Date();

  // Kiểm tra nếu không có OA configuration
  const hasOaConfig = !!oaConfig || !!accountOaConfig;

  // Tạo URL xác thực Zalo OAuth
  const activeAppId = activeConfig?.app_id || appId;

  // Sử dụng useState để lưu trữ authUrl
  const [authUrl, setAuthUrl] = useState<string | null>(null);

  // Sử dụng useEffect để đảm bảo code chỉ chạy ở phía client
  useEffect(() => {
    if (typeof window !== 'undefined' && activeAppId) {
      // Tạo state chứa thông tin oaConfig và accountId
      const stateData = {
        accountId,
        oaConfigId: oaConfig?.id || null,
        returnPath: window.location.pathname, // Thêm current path để quay về sau khi hoàn tất
      };
      const encodedState = encodeURIComponent(JSON.stringify(stateData));

      const url = `https://oauth.zaloapp.com/v4/oa/permission?app_id=${activeAppId}&redirect_uri=${encodeURIComponent(
        `${window.location.origin}/api/integrations/zalo/callback`,
      )}&state=${encodedState}`;

      setAuthUrl(url);
    }
  }, [activeAppId, accountId, oaConfig]);

  const handleConnect = async () => {
    setIsLoading(true);

    if (authUrl) {
      window.location.href = authUrl;
    } else if (!hasOaConfig) {
      // Nếu không có OA config, chuyển hướng đến trang setup
      router.push(`/home/<USER>/integrations/zns/setup`);
      // Đặt lại trạng thái loading sau khi chuyển hướng
      setTimeout(() => setIsLoading(false), 100);
    } else {
      toast.error(t('integrations:zns.connect.connectionFailed'), {
        description: t('integrations:zns.connect.checkOaConfig'),
      });
      setIsLoading(false);
    }
  };

  // Xác định trạng thái kết nối dựa trên token
  // Chỉ coi là đã kết nối khi có access_token và token còn hạn
  const connectionStatus = hasValidToken;

  // Kiểm tra lỗi OA configuration
  useEffect(() => {
    if (oaConfigError || accountOaConfigError) {
      console.error(
        'OA configuration error:',
        oaConfigError || accountOaConfigError,
      );
      toast.error(t('integrations:zns.connect.oaConfigError'), {
        description: t('integrations:zns.connect.checkSettings'),
      });
    }
  }, [oaConfigError, accountOaConfigError]);

  // Chỉ disable button khi đang loading, không disable khi không có OA config
  const buttonDisabled = isLoading;

  return (
    <Button
      onClick={handleConnect}
      disabled={buttonDisabled}
      variant={connectionStatus ? 'outline' : 'default'}
      title={!hasOaConfig ? t('integrations:zns.connect.setupOaFirst') : ''}
      className="relative overflow-hidden transition-all hover:shadow-md"
    >
      {isLoading && (
        <span className="bg-primary/90 absolute inset-0 flex items-center justify-center">
          <svg
            className="h-5 w-5 animate-spin text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        </span>
      )}
      <span
        className={`flex items-center gap-2 ${isLoading ? 'invisible' : ''}`}
      >
        {!connectionStatus && <LogIn className="h-4 w-4" />}
        {isLoading
          ? t('integrations:zns.connect.connecting')
          : connectionStatus
            ? t('integrations:zns.connect.reconnectOa')
            : t('integrations:zns.connect.loginOa')}
      </span>
    </Button>
  );
}
