'use client';

import { useEffect, useState } from 'react';

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Switch } from '@kit/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

// Schema cho form lập lịch
const schedulerFormSchema = z.object({
  integrationId: z.string().min(1, { message: 'Integration is required' }),
  resourceType: z.string().min(1, { message: 'Resource type is required' }),
  frequency: z.string().min(1, { message: 'Frequency is required' }),
  enabled: z.boolean().default(true),
  cronExpression: z.string().optional(),
  startTime: z.string().optional(),
  endTime: z.string().optional(),
});

type SchedulerFormValues = z.infer<typeof schedulerFormSchema>;

export default function IntegrationSchedulerPage() {
  const { account } = useParams();
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [integrations, setIntegrations] = useState<any[]>([]);
  const [schedules, setSchedules] = useState<any[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('create');

  // Form với validation và giá trị mặc định
  const form = useForm<SchedulerFormValues>({
    resolver: zodResolver(schedulerFormSchema),
    defaultValues: {
      integrationId: '',
      resourceType: '',
      frequency: 'daily',
      enabled: true,
      cronExpression: '',
      startTime: '00:00',
      endTime: '23:59',
    },
  });

  // Lấy danh sách tích hợp
  useEffect(() => {
    const fetchIntegrations = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(
          `/api/integrations/setup?accountId=${account}`
        );
        if (!response.ok) {
          throw new Error('Failed to fetch integrations');
        }
        const data = await response.json();
        setIntegrations(data.integrations || []);
      } catch (error: any) {
        setError(error.message || 'Failed to load integrations');
        toast.error('Failed to load integrations', {
          description: error.message || 'Please try again later',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchIntegrations();
  }, [account]);

  // Lấy danh sách lịch trình
  useEffect(() => {
    const fetchSchedules = async () => {
      try {
        const response = await fetch(
          `/api/integrations/scheduler?accountId=${account}`
        );
        if (!response.ok) {
          throw new Error('Failed to fetch schedules');
        }
        const data = await response.json();
        setSchedules(data.schedules || []);
      } catch (error: any) {
        toast.error('Failed to load schedules', {
          description: error.message || 'Please try again later',
        });
      }
    };

    fetchSchedules();
  }, [account]);

  // Xử lý khi submit form
  const onSubmit = async (values: SchedulerFormValues) => {
    setIsSubmitting(true);
    try {
      // Gọi API để tạo lịch trình
      const response = await fetch('/api/integrations/scheduler', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountId: account,
          ...values,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create schedule');
      }

      toast.success('Schedule created successfully');
      
      // Cập nhật danh sách lịch trình
      const schedulesResponse = await fetch(
        `/api/integrations/scheduler?accountId=${account}`
      );
      if (schedulesResponse.ok) {
        const data = await schedulesResponse.json();
        setSchedules(data.schedules || []);
      }
      
      // Chuyển sang tab danh sách
      setActiveTab('list');
      
      // Reset form
      form.reset();
    } catch (error: any) {
      toast.error('Failed to create schedule', {
        description: error.message || 'Please try again later',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý khi xóa lịch trình
  const handleDeleteSchedule = async (scheduleId: string) => {
    try {
      const response = await fetch(
        `/api/integrations/scheduler?accountId=${account}&scheduleId=${scheduleId}`,
        {
          method: 'DELETE',
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete schedule');
      }

      toast.success('Schedule deleted successfully');
      
      // Cập nhật danh sách lịch trình
      const schedulesResponse = await fetch(
        `/api/integrations/scheduler?accountId=${account}`
      );
      if (schedulesResponse.ok) {
        const data = await schedulesResponse.json();
        setSchedules(data.schedules || []);
      }
    } catch (error: any) {
      toast.error('Failed to delete schedule', {
        description: error.message || 'Please try again later',
      });
    }
  };

  // Xử lý khi bật/tắt lịch trình
  const handleToggleSchedule = async (scheduleId: string, enabled: boolean) => {
    try {
      const response = await fetch('/api/integrations/scheduler', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountId: account,
          scheduleId,
          enabled,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update schedule');
      }

      toast.success('Schedule updated successfully');
      
      // Cập nhật danh sách lịch trình
      const schedulesResponse = await fetch(
        `/api/integrations/scheduler?accountId=${account}`
      );
      if (schedulesResponse.ok) {
        const data = await schedulesResponse.json();
        setSchedules(data.schedules || []);
      }
    } catch (error: any) {
      toast.error('Failed to update schedule', {
        description: error.message || 'Please try again later',
      });
    }
  };

  // Lấy tên tích hợp
  const getIntegrationName = (integrationId: string) => {
    const integration = integrations.find((i) => i.id === integrationId);
    return integration ? integration.name || integration.type : integrationId;
  };

  // Lấy tên resource type
  const getResourceTypeName = (resourceType: string) => {
    switch (resourceType) {
      case 'products':
        return t('integrations:resourceType.products');
      case 'orders':
        return t('integrations:resourceType.orders');
      case 'customers':
        return t('integrations:resourceType.customers');
      default:
        return resourceType;
    }
  };

  // Lấy mô tả tần suất
  const getFrequencyDescription = (frequency: string, cronExpression?: string) => {
    if (cronExpression) {
      return `${t('integrations:scheduler.custom')}: ${cronExpression}`;
    }
    
    switch (frequency) {
      case 'hourly':
        return t('integrations:scheduler.hourly');
      case 'daily':
        return t('integrations:scheduler.daily');
      case 'weekly':
        return t('integrations:scheduler.weekly');
      case 'monthly':
        return t('integrations:scheduler.monthly');
      default:
        return frequency;
    }
  };

  if (isLoading) {
    return (
      <>
        <TeamAccountLayoutPageHeader
          title={
            <Trans i18nKey="integrations:scheduler.title">
              Integration Scheduler
            </Trans>
          }
          description={<AppBreadcrumbs />}
          account={account as string}
        />
        <PageBody>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-sm text-muted-foreground">
                <Trans i18nKey="common:loading">Loading...</Trans>
              </p>
            </div>
          </div>
        </PageBody>
      </>
    );
  }

  if (error) {
    return (
      <>
        <TeamAccountLayoutPageHeader
          title={
            <Trans i18nKey="integrations:scheduler.title">
              Integration Scheduler
            </Trans>
          }
          description={<AppBreadcrumbs />}
          account={account as string}
        />
        <PageBody>
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className="mt-4">
            <Button
              onClick={() => router.push(`/home/<USER>/integrations`)}
            >
              <Trans i18nKey="common:goBack">Go Back</Trans>
            </Button>
          </div>
        </PageBody>
      </>
    );
  }

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={
          <Trans i18nKey="integrations:scheduler.title">
            Integration Scheduler
          </Trans>
        }
        description={<AppBreadcrumbs />}
        account={account as string}
      />

      <PageBody>
        <Tabs
          defaultValue="create"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="create">
              <Trans i18nKey="integrations:scheduler.createTab">
                Create Schedule
              </Trans>
            </TabsTrigger>
            <TabsTrigger value="list">
              <Trans i18nKey="integrations:scheduler.listTab">
                Schedules
              </Trans>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-4 py-4">
            <Card>
              <CardHeader>
                <CardTitle>
                  <Trans i18nKey="integrations:scheduler.createTitle">
                    Create Sync Schedule
                  </Trans>
                </CardTitle>
                <CardDescription>
                  <Trans i18nKey="integrations:scheduler.createDescription">
                    Set up automatic synchronization for your integrations.
                  </Trans>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <FormField
                      control={form.control}
                      name="integrationId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="integrations:scheduler.integration">
                              Integration
                            </Trans>
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={t(
                                    'integrations:scheduler.selectIntegration'
                                  )}
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {integrations.map((integration) => (
                                <SelectItem
                                  key={integration.id}
                                  value={integration.id}
                                >
                                  {integration.name || integration.type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            <Trans i18nKey="integrations:scheduler.integrationDescription">
                              Select the integration to schedule.
                            </Trans>
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="resourceType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="integrations:scheduler.resourceType">
                              Resource Type
                            </Trans>
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={t(
                                    'integrations:scheduler.selectResourceType'
                                  )}
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="products">
                                <Trans i18nKey="integrations:resourceType.products">
                                  Products
                                </Trans>
                              </SelectItem>
                              <SelectItem value="orders">
                                <Trans i18nKey="integrations:resourceType.orders">
                                  Orders
                                </Trans>
                              </SelectItem>
                              <SelectItem value="customers">
                                <Trans i18nKey="integrations:resourceType.customers">
                                  Customers
                                </Trans>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            <Trans i18nKey="integrations:scheduler.resourceTypeDescription">
                              Select the type of data to synchronize.
                            </Trans>
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="frequency"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="integrations:scheduler.frequency">
                              Frequency
                            </Trans>
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue
                                  placeholder={t(
                                    'integrations:scheduler.selectFrequency'
                                  )}
                                />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="hourly">
                                <Trans i18nKey="integrations:scheduler.hourly">
                                  Hourly
                                </Trans>
                              </SelectItem>
                              <SelectItem value="daily">
                                <Trans i18nKey="integrations:scheduler.daily">
                                  Daily
                                </Trans>
                              </SelectItem>
                              <SelectItem value="weekly">
                                <Trans i18nKey="integrations:scheduler.weekly">
                                  Weekly
                                </Trans>
                              </SelectItem>
                              <SelectItem value="monthly">
                                <Trans i18nKey="integrations:scheduler.monthly">
                                  Monthly
                                </Trans>
                              </SelectItem>
                              <SelectItem value="custom">
                                <Trans i18nKey="integrations:scheduler.custom">
                                  Custom (Cron)
                                </Trans>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            <Trans i18nKey="integrations:scheduler.frequencyDescription">
                              How often to run the synchronization.
                            </Trans>
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch('frequency') === 'custom' && (
                      <FormField
                        control={form.control}
                        name="cronExpression"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="integrations:scheduler.cronExpression">
                                Cron Expression
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="0 0 * * *"
                              />
                            </FormControl>
                            <FormDescription>
                              <Trans i18nKey="integrations:scheduler.cronExpressionDescription">
                                Enter a cron expression (e.g., "0 0 * * *" for
                                daily at midnight).
                              </Trans>
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {form.watch('frequency') !== 'custom' && (
                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="startTime"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                <Trans i18nKey="integrations:scheduler.startTime">
                                  Start Time
                                </Trans>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  type="time"
                                />
                              </FormControl>
                              <FormDescription>
                                <Trans i18nKey="integrations:scheduler.startTimeDescription">
                                  When to start the sync window.
                                </Trans>
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="endTime"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                <Trans i18nKey="integrations:scheduler.endTime">
                                  End Time
                                </Trans>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  type="time"
                                />
                              </FormControl>
                              <FormDescription>
                                <Trans i18nKey="integrations:scheduler.endTimeDescription">
                                  When to end the sync window.
                                </Trans>
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}

                    <FormField
                      control={form.control}
                      name="enabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              <Trans i18nKey="integrations:scheduler.enabled">
                                Enabled
                              </Trans>
                            </FormLabel>
                            <FormDescription>
                              <Trans i18nKey="integrations:scheduler.enabledDescription">
                                Enable or disable this schedule.
                              </Trans>
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full"
                    >
                      {isSubmitting ? (
                        <Trans i18nKey="common:saving">Saving...</Trans>
                      ) : (
                        <Trans i18nKey="integrations:scheduler.createSchedule">
                          Create Schedule
                        </Trans>
                      )}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="list" className="space-y-4 py-4">
            <Card>
              <CardHeader>
                <CardTitle>
                  <Trans i18nKey="integrations:scheduler.listTitle">
                    Sync Schedules
                  </Trans>
                </CardTitle>
                <CardDescription>
                  <Trans i18nKey="integrations:scheduler.listDescription">
                    Manage your automatic synchronization schedules.
                  </Trans>
                </CardDescription>
              </CardHeader>
              <CardContent>
                {schedules.length === 0 ? (
                  <div className="text-center py-8 border border-dashed rounded-md">
                    <p className="text-muted-foreground">
                      <Trans i18nKey="integrations:scheduler.noSchedules">
                        No schedules found. Create a schedule to start automatic
                        synchronization.
                      </Trans>
                    </p>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>
                            <Trans i18nKey="integrations:scheduler.integration">
                              Integration
                            </Trans>
                          </TableHead>
                          <TableHead>
                            <Trans i18nKey="integrations:scheduler.resourceType">
                              Resource
                            </Trans>
                          </TableHead>
                          <TableHead>
                            <Trans i18nKey="integrations:scheduler.frequency">
                              Frequency
                            </Trans>
                          </TableHead>
                          <TableHead>
                            <Trans i18nKey="integrations:scheduler.status">
                              Status
                            </Trans>
                          </TableHead>
                          <TableHead>
                            <Trans i18nKey="integrations:scheduler.actions">
                              Actions
                            </Trans>
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {schedules.map((schedule) => (
                          <TableRow key={schedule.id}>
                            <TableCell className="font-medium">
                              {getIntegrationName(schedule.integration_id)}
                            </TableCell>
                            <TableCell>
                              {getResourceTypeName(schedule.resource_type)}
                            </TableCell>
                            <TableCell>
                              {getFrequencyDescription(
                                schedule.frequency,
                                schedule.cron_expression
                              )}
                            </TableCell>
                            <TableCell>
                              <Switch
                                checked={schedule.enabled}
                                onCheckedChange={(checked) =>
                                  handleToggleSchedule(schedule.id, checked)
                                }
                              />
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  handleDeleteSchedule(schedule.id)
                                }
                                className="h-8 w-8 p-0 text-destructive"
                              >
                                <span className="sr-only">Delete</span>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="h-4 w-4"
                                >
                                  <path d="M3 6h18" />
                                  <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                                  <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                                  <line x1="10" x2="10" y1="11" y2="17" />
                                  <line x1="14" x2="14" y1="11" y2="17" />
                                </svg>
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button
                  variant="outline"
                  onClick={() => router.push(`/home/<USER>/integrations`)}
                >
                  <Trans i18nKey="common:goBack">Go Back</Trans>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </PageBody>
    </>
  );
}
