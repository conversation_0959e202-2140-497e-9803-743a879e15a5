import { getSupabaseServerClient } from '@kit/supabase/server-client';

export type IntegrationField = {
  name: string;
  label: string;
  type: 'text' | 'password' | 'url';
};

export type IntegrationInfo = {
  type: string;
  name: string;
  logo: string;
  description: string;
  fields: IntegrationField[];
};

const INTEGRATION_CONFIGS: Record<
  string,
  {
    name: string;
    fields: IntegrationField[];
  }
> = {
  shopee: {
    name: 'Shopee',
    fields: [
      { name: 'apiKey', label: 'API Key', type: 'text' },
      { name: 'apiSecret', label: 'API Secret', type: 'password' },
    ],
  },
  sapo: {
    name: 'Sapo',
    fields: [
      { name: 'api<PERSON>ey', label: 'API Key', type: 'text' },
      { name: 'apiSecret', label: 'API Secret', type: 'password' },
    ],
  },
  n8n: {
    name: 'N8<PERSON>',
    fields: [{ name: 'webhookUrl', label: 'Webhook URL', type: 'url' }],
  },
};

export async function loadIntegrationInfo(
  accountId: string,
  type: string,
): Promise<IntegrationInfo> {
  const config = INTEGRATION_CONFIGS[type];
  if (!config) {
    throw new Error(`Unsupported integration type: ${type}`);
  }

  return {
    type,
    name: config.name,
    logo: `/images/${type}-logo.png`,
    description: `Connect your ${config.name} account to enable integration`,
    fields: config.fields,
  };
}
