'use server';

import { revalidatePath } from 'next/cache';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function connectIntegration(
  accountId: string,
  type: string,
  config: Record<string, string>,
) {
  const supabase = getSupabaseServerClient();

  // Save integration config
  const { error } = await supabase.from('integrations').upsert({
    account_id: accountId,
    type,
    status: 'connected',
    enabled: true,
    config,
  });

  if (error) throw error;

  // Notify N8N if configured
  const { data: n8n } = await supabase
    .from('integrations')
    .select('config')
    .eq('account_id', accountId)
    .eq('type', 'n8n')
    .single();

  if (n8n?.config?.webhook_url) {
    await fetch(n8n.config.webhook_url, {
      method: 'POST',
      body: JSON.stringify({
        event: 'integration_connected',
        integration_type: type,
        account_id: accountId,
      }),
    });
  }

  revalidatePath(`/home/<USER>/integrations`);
}
