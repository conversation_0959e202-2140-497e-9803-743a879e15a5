'use client';

import { useState } from 'react';

import { useRouter } from 'next/navigation';

import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { Card } from '@kit/ui/card';
import { Input } from '@kit/ui/input';

import { connectIntegration } from '../_lib/server/connect.actions';
import type { IntegrationInfo } from '../_lib/server/connect.loader';

interface ConnectFormProps {
  integration: IntegrationInfo;
  accountId: string;
}

export function ConnectForm({ integration, accountId }: ConnectFormProps) {
  const router = useRouter();
  const [formData, setFormData] = useState<Record<string, string>>(
    Object.fromEntries(integration.fields.map((f) => [f.name, ''])),
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await connectIntegration(accountId, integration.type, formData);
      toast.success(`${integration.name} connected successfully`);
      router.push(`/home/<USER>/integrations`);
    } catch (error) {
      toast.error('Failed to connect integration');
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="p-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        {integration.fields.map((field) => (
          <div key={field.name}>
            <label className="mb-1 block text-sm font-medium">
              {field.label}
            </label>
            <Input
              type={field.type}
              value={formData[field.name]}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  [field.name]: e.target.value,
                }))
              }
              required
            />
          </div>
        ))}

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push(`/home/<USER>/integrations`)}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            Connect
          </Button>
        </div>
      </form>
    </Card>
  );
}
