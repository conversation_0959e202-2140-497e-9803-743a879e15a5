import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { ConnectForm } from './_components/connect-form';
import { loadIntegrationInfo } from './_lib/server/connect.loader';

interface ConnectPageProps {
  params: {
    account: string;
    type: string;
  };
}

export default async function ConnectPage({ params }: ConnectPageProps) {
  const integration = await loadIntegrationInfo(params.account, params.type);

  return (
    <div className="container py-6">
      <TeamAccountLayoutPageHeader
        title={`Kết nối ${integration.name}`}
        description={<AppBreadcrumbs />}
        account={params.account}
      />

      <div className="mx-auto mt-6 max-w-xl">
        <ConnectForm integration={integration} accountId={params.account} />
      </div>
    </div>
  );
}
