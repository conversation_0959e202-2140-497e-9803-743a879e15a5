'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';

import { Button } from '@kit/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Trans } from '@kit/ui/trans';
import { Separator } from '@kit/ui/separator';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { IntegrationIcon } from '../_components/integration-icon';

// Define integration types
const integrations = [
  {
    id: 'ipos',
    name: 'iPOS',
    description: 'Connect with iPOS to sync products, orders, and customers.',
    features: ['Product sync', 'Order sync', 'Customer sync', 'Inventory management'],
    route: '/ipos/connect',
  },
  {
    id: 'zns',
    name: 'Zalo Notification Service',
    description: 'Send notifications to your customers via Zalo.',
    features: ['Order notifications', 'Promotional messages', 'Customer engagement'],
    route: '/zns/connect',
  },
];

export default function ConnectIntegrationPage() {
  const { account } = useParams();
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  
  return (
    <>
      <TeamAccountLayoutPageHeader
        title={<Trans i18nKey="integrations:connect.title">Connect Integration</Trans>}
        description={<AppBreadcrumbs />}
        account={account as string}
      />

      <PageBody>
        <div className="mx-auto max-w-4xl space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>
                <Trans i18nKey="integrations:connect.selectPlatform">Select a Platform</Trans>
              </CardTitle>
              <CardDescription>
                <Trans i18nKey="integrations:connect.selectPlatformDescription">
                  Choose a platform to integrate with your business.
                </Trans>
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {integrations.map((integration, index) => (
                  <motion.div
                    key={integration.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Card
                      className="cursor-pointer transition-all hover:border-primary/50 hover:shadow-md"
                      onClick={() => router.push(`/home/<USER>/integrations${integration.route}`)}
                      data-testid={`integration-${integration.id}-card`}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center space-x-2">
                          <IntegrationIcon type={integration.id} size={32} />
                          <CardTitle className="text-lg">{integration.name}</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="pb-4">
                        <p className="mb-4 text-sm text-muted-foreground">
                          {integration.description}
                        </p>
                        <div className="space-y-2">
                          <p className="text-xs font-medium uppercase text-muted-foreground">
                            <Trans i18nKey="integrations:connect.features">Features</Trans>
                          </p>
                          <ul className="grid grid-cols-2 gap-x-4 gap-y-1 text-sm">
                            {integration.features.map((feature) => (
                              <li key={feature} className="flex items-center">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="mr-2 h-4 w-4 text-primary"
                                >
                                  <path d="M20 6 9 17l-5-5" />
                                </svg>
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={() => router.push(`/home/<USER>/integrations`)}
              data-testid="cancel-button"
            >
              <Trans i18nKey="common:cancel">Cancel</Trans>
            </Button>
          </div>
        </div>
      </PageBody>
    </>
  );
}
