'use client';

import { useEffect, useState } from 'react';

import { DndContext, DragEndEvent, DragOverlay, DragStartEvent, useDraggable, useDroppable } from '@dnd-kit/core';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

interface DragDropMappingProps {
  sourceFields: any[];
  targetFields: any[];
  mappings: any[];
  onMappingsChange: (mappings: any[]) => void;
}

export function DragDropMapping({
  sourceFields,
  targetFields,
  mappings,
  onMappingsChange,
}: DragDropMappingProps) {
  const { t } = useTranslation(['integrations', 'common']);
  const [activeDragId, setActiveDragId] = useState<string | null>(null);
  const [activeDragData, setActiveDragData] = useState<any | null>(null);
  const [mappedSourceFields, setMappedSourceFields] = useState<string[]>([]);
  const [mappedTargetFields, setMappedTargetFields] = useState<string[]>([]);

  // Cập nhật danh sách các trường đã được map
  useEffect(() => {
    const sourceFieldNames = mappings.map((m) => m.source_field);
    const targetFieldNames = mappings.map((m) => m.target_field);
    
    setMappedSourceFields(sourceFieldNames);
    setMappedTargetFields(targetFieldNames);
  }, [mappings]);

  // Xử lý khi bắt đầu kéo
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveDragId(active.id as string);
    
    // Lấy dữ liệu của trường đang kéo
    const [type, fieldName] = (active.id as string).split(':');
    const fields = type === 'source' ? sourceFields : targetFields;
    const field = fields.find((f) => f.name === fieldName);
    
    setActiveDragData({ type, field });
  };

  // Xử lý khi kết thúc kéo
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over) {
      const [activeType, activeFieldName] = (active.id as string).split(':');
      const [overType, overFieldName] = (over.id as string).split(':');
      
      // Chỉ cho phép kéo từ source sang target hoặc ngược lại
      if (activeType !== overType) {
        // Xác định source và target field
        const sourceField = activeType === 'source' ? activeFieldName : overFieldName;
        const targetField = activeType === 'target' ? activeFieldName : overFieldName;
        
        // Kiểm tra xem mapping đã tồn tại chưa
        const existingMapping = mappings.find(
          (m) => m.source_field === sourceField && m.target_field === targetField
        );
        
        if (!existingMapping) {
          // Tạo mapping mới
          const newMapping = {
            id: `mapping-${Date.now()}`,
            source_field: sourceField,
            target_field: targetField,
            transform_function: '',
            is_required: false,
          };
          
          // Cập nhật danh sách mappings
          onMappingsChange([...mappings, newMapping]);
          
          toast.success('Field mapping created', {
            description: `Mapped ${sourceField} to ${targetField}`,
          });
        }
      }
    }
    
    // Reset state
    setActiveDragId(null);
    setActiveDragData(null);
  };

  // Xử lý khi xóa mapping
  const handleRemoveMapping = (sourceField: string, targetField: string) => {
    // Lọc ra mapping cần xóa
    const updatedMappings = mappings.filter(
      (m) => !(m.source_field === sourceField && m.target_field === targetField)
    );
    
    // Cập nhật danh sách mappings
    onMappingsChange(updatedMappings);
    
    toast.success('Field mapping removed');
  };

  return (
    <DndContext
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">
            <Trans i18nKey="integrations:mapping.sourceFields">
              Source Fields
            </Trans>
          </h3>
          <div className="space-y-2 max-h-[500px] overflow-y-auto pr-2">
            {sourceFields.map((field) => (
              <SourceFieldItem
                key={field.name}
                field={field}
                isMapped={mappedSourceFields.includes(field.name)}
              />
            ))}
          </div>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-medium">
            <Trans i18nKey="integrations:mapping.targetFields">
              Target Fields
            </Trans>
          </h3>
          <div className="space-y-2 max-h-[500px] overflow-y-auto pr-2">
            {targetFields.map((field) => (
              <TargetFieldItem
                key={field.name}
                field={field}
                isMapped={mappedTargetFields.includes(field.name)}
              />
            ))}
          </div>
        </div>
      </div>
      
      <div className="mt-8">
        <h3 className="text-lg font-medium mb-4">
          <Trans i18nKey="integrations:mapping.currentMappings">
            Current Mappings
          </Trans>
        </h3>
        
        {mappings.length === 0 ? (
          <div className="text-center py-8 border border-dashed rounded-md">
            <p className="text-muted-foreground">
              <Trans i18nKey="integrations:mapping.noMappings">
                No mappings defined yet. Drag fields from source to target to create mappings.
              </Trans>
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {mappings.map((mapping) => (
              <MappingItem
                key={mapping.id}
                mapping={mapping}
                onRemove={() => handleRemoveMapping(mapping.source_field, mapping.target_field)}
              />
            ))}
          </div>
        )}
      </div>
      
      <DragOverlay>
        {activeDragId && activeDragData && (
          <div className="bg-primary text-primary-foreground px-3 py-2 rounded-md opacity-80">
            {activeDragData.field.name}
          </div>
        )}
      </DragOverlay>
    </DndContext>
  );
}

// Component cho source field
function SourceFieldItem({ field, isMapped }: { field: any; isMapped: boolean }) {
  const id = `source:${field.name}`;
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id,
    data: { type: 'source', field },
    disabled: isMapped,
  });
  
  const { setNodeRef: setDropRef } = useDroppable({
    id,
    data: { type: 'source', field },
  });
  
  // Kết hợp cả hai ref
  const ref = (node: HTMLDivElement) => {
    setNodeRef(node);
    setDropRef(node);
  };
  
  return (
    <div
      ref={ref}
      {...listeners}
      {...attributes}
      className={`flex items-center justify-between p-3 rounded-md cursor-grab ${
        isDragging ? 'opacity-50' : ''
      } ${
        isMapped ? 'bg-primary/10 border-primary/30' : 'bg-card hover:bg-accent/5'
      } border`}
    >
      <div>
        <div className="font-medium">{field.name}</div>
        {field.type && (
          <div className="text-xs text-muted-foreground">
            {field.type}
          </div>
        )}
      </div>
      {isMapped && (
        <div className="text-xs bg-primary/20 text-primary px-2 py-1 rounded">
          Mapped
        </div>
      )}
    </div>
  );
}

// Component cho target field
function TargetFieldItem({ field, isMapped }: { field: any; isMapped: boolean }) {
  const id = `target:${field.name}`;
  const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
    id,
    data: { type: 'target', field },
    disabled: isMapped,
  });
  
  const { setNodeRef: setDropRef } = useDroppable({
    id,
    data: { type: 'target', field },
  });
  
  // Kết hợp cả hai ref
  const ref = (node: HTMLDivElement) => {
    setNodeRef(node);
    setDropRef(node);
  };
  
  return (
    <div
      ref={ref}
      {...listeners}
      {...attributes}
      className={`flex items-center justify-between p-3 rounded-md cursor-grab ${
        isDragging ? 'opacity-50' : ''
      } ${
        isMapped ? 'bg-primary/10 border-primary/30' : 'bg-card hover:bg-accent/5'
      } border`}
    >
      <div>
        <div className="font-medium">{field.name}</div>
        {field.type && (
          <div className="text-xs text-muted-foreground">
            {field.type}
          </div>
        )}
      </div>
      {isMapped && (
        <div className="text-xs bg-primary/20 text-primary px-2 py-1 rounded">
          Mapped
        </div>
      )}
    </div>
  );
}

// Component cho mapping
function MappingItem({ mapping, onRemove }: { mapping: any; onRemove: () => void }) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-muted p-2 rounded">
              <div className="font-medium">{mapping.source_field}</div>
              <div className="text-xs text-muted-foreground">Source</div>
            </div>
            
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-muted-foreground"
            >
              <path d="M5 12h14" />
              <path d="m12 5 7 7-7 7" />
            </svg>
            
            <div className="bg-muted p-2 rounded">
              <div className="font-medium">{mapping.target_field}</div>
              <div className="text-xs text-muted-foreground">Target</div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {mapping.transform_function && (
              <div className="text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded">
                Transform
              </div>
            )}
            {mapping.is_required && (
              <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                Required
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onRemove}
              className="h-8 w-8 p-0 text-destructive"
            >
              <span className="sr-only">Remove</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <path d="M3 6h18" />
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
                <line x1="10" x2="10" y1="11" y2="17" />
                <line x1="14" x2="14" y1="11" y2="17" />
              </svg>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
