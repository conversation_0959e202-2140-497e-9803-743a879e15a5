'use client';

import { useState } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { Progress } from '@kit/ui/progress';
import { Separator } from '@kit/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';

import { IntegrationCard } from './integration-card';
import { getAvailableIntegrations } from '../_lib/available-integrations';

interface IntegrationDashboardProps {
  accountId: string;
  integrations: any[];
}

export function IntegrationDashboard({
  accountId,
  integrations,
}: IntegrationDashboardProps) {
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const [activeTab, setActiveTab] = useState('all');
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [integrationToDelete, setIntegrationToDelete] = useState<any>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Lấy danh sách tích hợp có sẵn
  const availableIntegrations = getAvailableIntegrations();

  // Lọc tích hợp theo tab
  const filteredIntegrations = () => {
    if (activeTab === 'all') return integrations;
    return integrations.filter((integration) => integration.type === activeTab);
  };

  // Xử lý khi xóa tích hợp
  const handleDeleteIntegration = async () => {
    if (!integrationToDelete) return;

    setIsDeleting(true);
    try {
      const response = await fetch(
        `/api/integrations/setup?accountId=${accountId}&integrationId=${integrationToDelete.id}`,
        {
          method: 'DELETE',
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete integration');
      }

      toast.success('Integration deleted successfully');
      router.refresh();
    } catch (error: any) {
      toast.error('Failed to delete integration', {
        description: error.message || 'Please try again later',
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
      setIntegrationToDelete(null);
    }
  };

  // Xử lý khi mở dialog xóa
  const handleOpenDeleteDialog = (integration: any) => {
    setIntegrationToDelete(integration);
    setIsDeleteDialogOpen(true);
  };

  // Lấy trạng thái tích hợp
  const getIntegrationStatus = (status: string) => {
    switch (status) {
      case 'connected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <Trans i18nKey="integrations:status.connected">Connected</Trans>
          </span>
        );
      case 'error':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <Trans i18nKey="integrations:status.error">Error</Trans>
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <Trans i18nKey="integrations:status.pending">Pending</Trans>
          </span>
        );
      case 'not_connected':
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            <Trans i18nKey="integrations:status.notConnected">
              Not Connected
            </Trans>
          </span>
        );
    }
  };

  // Lấy thời gian đồng bộ cuối cùng
  const getLastSyncTime = (lastSyncAt: string | null) => {
    if (!lastSyncAt) {
      return (
        <span className="text-muted-foreground">
          <Trans i18nKey="integrations:dashboard.never">Never</Trans>
        </span>
      );
    }

    return new Date(lastSyncAt).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <Tabs
        defaultValue="all"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="all">
              <Trans i18nKey="integrations:dashboard.all">All</Trans>
            </TabsTrigger>
            <TabsTrigger value="ipos">iPOS</TabsTrigger>
            <TabsTrigger value="sapo">Sapo</TabsTrigger>
            <TabsTrigger value="kiotviet">KiotViet</TabsTrigger>
          </TabsList>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={() => router.refresh()}
              className="h-8 w-8 p-0"
            >
              <span className="sr-only">Refresh</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                <path d="M21 3v5h-5" />
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                <path d="M8 16H3v5" />
              </svg>
            </Button>
          </div>
        </div>

        <TabsContent value={activeTab} className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Hiển thị các tích hợp đã kết nối */}
            {filteredIntegrations().map((integration) => (
              <Card key={integration.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">
                        {integration.name || integration.type}
                      </CardTitle>
                      <CardDescription>
                        {integration.description || (
                          <Trans
                            i18nKey={`integrations:${integration.type}.description`}
                          >
                            Integration with {integration.type}
                          </Trans>
                        )}
                      </CardDescription>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          data-testid={`${integration.type}-menu-button`}
                        >
                          <span className="sr-only">Open menu</span>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="h-4 w-4"
                          >
                            <circle cx="12" cy="12" r="1" />
                            <circle cx="12" cy="5" r="1" />
                            <circle cx="12" cy="19" r="1" />
                          </svg>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() =>
                            router.push(
                              `/home/<USER>/integrations/${integration.type}`
                            )
                          }
                          data-testid={`${integration.type}-view-button`}
                        >
                          <Trans i18nKey="integrations:dashboard.view">
                            View
                          </Trans>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            router.push(
                              `/home/<USER>/integrations/${integration.type}/sync`
                            )
                          }
                          data-testid={`${integration.type}-sync-button`}
                        >
                          <Trans i18nKey="integrations:dashboard.sync">
                            Sync Data
                          </Trans>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() =>
                            router.push(
                              `/home/<USER>/integrations/${integration.type}/mapping`
                            )
                          }
                          data-testid={`${integration.type}-mapping-button`}
                        >
                          <Trans i18nKey="integrations:dashboard.mapping">
                            Data Mapping
                          </Trans>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleOpenDeleteDialog(integration)}
                          className="text-destructive focus:text-destructive"
                          data-testid={`${integration.type}-delete-button`}
                        >
                          <Trans i18nKey="integrations:dashboard.delete">
                            Delete
                          </Trans>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">
                        <Trans i18nKey="integrations:dashboard.status">
                          Status
                        </Trans>
                      </span>
                      {getIntegrationStatus(integration.status)}
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">
                        <Trans i18nKey="integrations:dashboard.lastSync">
                          Last Sync
                        </Trans>
                      </span>
                      <span className="text-sm">
                        {getLastSyncTime(integration.last_sync_at)}
                      </span>
                    </div>
                    {integration.error_message && (
                      <div className="text-xs text-destructive mt-2">
                        <span className="font-medium">Error: </span>
                        {integration.error_message}
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="bg-muted/50 p-4 flex justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      router.push(
                        `/home/<USER>/integrations/${integration.type}/connect`
                      )
                    }
                    data-testid={`${integration.type}-reconnect-button`}
                  >
                    <Trans i18nKey="integrations:dashboard.reconnect">
                      Reconnect
                    </Trans>
                  </Button>
                  <Button
                    size="sm"
                    onClick={() =>
                      router.push(
                        `/home/<USER>/integrations/${integration.type}/sync`
                      )
                    }
                    data-testid={`${integration.type}-sync-now-button`}
                  >
                    <Trans i18nKey="integrations:dashboard.syncNow">
                      Sync Now
                    </Trans>
                  </Button>
                </CardFooter>
              </Card>
            ))}

            {/* Hiển thị các tích hợp có sẵn */}
            {(activeTab === 'all' || filteredIntegrations().length === 0) &&
              availableIntegrations
                .filter(
                  (integration) =>
                    activeTab === 'all' || integration.id === activeTab
                )
                .filter(
                  (integration) =>
                    !integrations.some((i) => i.type === integration.id)
                )
                .map((integration) => (
                  <IntegrationCard
                    key={integration.id}
                    integration={integration}
                    accountId={accountId}
                  />
                ))}
          </div>

          {filteredIntegrations().length === 0 &&
            availableIntegrations.filter(
              (integration) =>
                activeTab === 'all' || integration.id === activeTab
            ).length === 0 && (
              <div className="text-center py-12">
                <h3 className="text-lg font-medium">
                  <Trans i18nKey="integrations:dashboard.noIntegrations">
                    No integrations available
                  </Trans>
                </h3>
                <p className="text-muted-foreground mt-2">
                  <Trans i18nKey="integrations:dashboard.noIntegrationsDescription">
                    There are no integrations available for this category.
                  </Trans>
                </p>
              </div>
            )}
        </TabsContent>
      </Tabs>

      <Dialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              <Trans i18nKey="integrations:dashboard.deleteIntegration">
                Delete Integration
              </Trans>
            </DialogTitle>
            <DialogDescription>
              <Trans i18nKey="integrations:dashboard.deleteIntegrationDescription">
                Are you sure you want to delete this integration? This action
                cannot be undone.
              </Trans>
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {integrationToDelete && (
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                    <span className="text-lg font-medium uppercase">
                      {integrationToDelete.type.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-medium">
                      {integrationToDelete.name || integrationToDelete.type}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {integrationToDelete.description || (
                        <Trans
                          i18nKey={`integrations:${integrationToDelete.type}.description`}
                        >
                          Integration with {integrationToDelete.type}
                        </Trans>
                      )}
                    </p>
                  </div>
                </div>
                <div className="text-sm text-destructive">
                  <Trans i18nKey="integrations:dashboard.deleteWarning">
                    Deleting this integration will remove all associated
                    mappings and configurations. Any scheduled syncs will be
                    cancelled.
                  </Trans>
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              <Trans i18nKey="common:cancel">Cancel</Trans>
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteIntegration}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <Trans i18nKey="common:deleting">Deleting...</Trans>
              ) : (
                <Trans i18nKey="common:delete">Delete</Trans>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
