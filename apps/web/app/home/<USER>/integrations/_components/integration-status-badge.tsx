'use client';

import { Badge } from '@kit/ui/badge';
import { Trans } from '@kit/ui/trans';

interface IntegrationStatusBadgeProps {
  status: string;
  enabled: boolean;
}

export function IntegrationStatusBadge({ status, enabled }: IntegrationStatusBadgeProps) {
  if (!enabled) {
    return (
      <Badge variant="outline" className="bg-muted text-muted-foreground">
        <Trans i18nKey="integrations:disabled">Disabled</Trans>
      </Badge>
    );
  }

  switch (status) {
    case 'connected':
      return (
        <Badge variant="default" className="bg-green-500">
          <Trans i18nKey="integrations:connected">Connected</Trans>
        </Badge>
      );
    case 'error':
      return (
        <Badge variant="destructive">
          <Trans i18nKey="integrations:error">Error</Trans>
        </Badge>
      );
    case 'pending':
      return (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
          <Trans i18nKey="integrations:pending">Pending</Trans>
        </Badge>
      );
    default:
      return (
        <Badge variant="outline">
          {status}
        </Badge>
      );
  }
}
