'use client';

import { useEffect, useState } from 'react';

import { useParams } from 'next/navigation';

import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Slider } from '@kit/ui/slider';
import { Trans } from '@kit/ui/trans';

interface SyncPerformanceConfigProps {
  integrationId: string;
  platform: string;
  onSave?: () => void;
}

interface SyncConfig {
  batch_size: number;
  concurrency: number;
  rate_limit: number;
}

export function SyncPerformanceConfig({
  integrationId,
  platform,
  onSave,
}: SyncPerformanceConfigProps) {
  const { account } = useParams();
  const { t } = useTranslation(['integrations', 'common']);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [syncConfig, setSyncConfig] = useState<SyncConfig>({
    batch_size: 50,
    concurrency: 5,
    rate_limit: 10,
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Lấy cấu hình hiện tại
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(
          `/api/integrations/sync-config?accountId=${account}&integrationId=${integrationId}`,
        );
        if (!response.ok) {
          throw new Error('Failed to fetch sync config');
        }
        const data = await response.json();
        
        setSyncConfig(data.syncConfig);
      } catch (error: any) {
        setError(error.message || 'Failed to load sync config');
        toast.error('Failed to load sync config', {
          description: error.message || 'Please try again later',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchConfig();
  }, [account, integrationId]);

  // Xử lý khi lưu cấu hình
  const handleSaveConfig = async () => {
    try {
      setIsSaving(true);
      
      // Gọi API để cập nhật cấu hình
      const response = await fetch('/api/integrations/sync-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountId: account,
          integrationId,
          syncConfig,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save sync config');
      }

      toast.success('Sync configuration saved successfully');
      setIsDialogOpen(false);
      
      // Gọi callback nếu có
      if (onSave) {
        onSave();
      }
    } catch (error: any) {
      toast.error('Failed to save sync config', {
        description: error.message || 'Please try again later',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Xử lý khi reset cấu hình
  const handleResetConfig = () => {
    setSyncConfig({
      batch_size: 50,
      concurrency: 5,
      rate_limit: 10,
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">
            <Trans i18nKey="common:loading">Loading...</Trans>
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">
            <Trans i18nKey="integrations:performance.title">
              Sync Performance Configuration
            </Trans>
          </h3>
          <p className="text-sm text-muted-foreground">
            <Trans i18nKey="integrations:performance.description">
              Configure performance settings for data synchronization.
            </Trans>
          </p>
        </div>
        <div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Trans i18nKey="integrations:performance.configure">
                  Configure
                </Trans>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  <Trans i18nKey="integrations:performance.configureTitle">
                    Configure Sync Performance
                  </Trans>
                </DialogTitle>
                <DialogDescription>
                  <Trans i18nKey="integrations:performance.configureDescription">
                    Adjust performance settings for data synchronization.
                    Higher values may improve speed but increase resource usage.
                  </Trans>
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-6 py-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="batch_size">
                      <Trans i18nKey="integrations:performance.batchSize">
                        Batch Size
                      </Trans>
                    </Label>
                    <Input
                      id="batch_size"
                      type="number"
                      value={syncConfig.batch_size}
                      onChange={(e) =>
                        setSyncConfig({
                          ...syncConfig,
                          batch_size: parseInt(e.target.value) || 1,
                        })
                      }
                      className="w-20 text-right"
                      min={1}
                      max={1000}
                    />
                  </div>
                  <Slider
                    value={[syncConfig.batch_size]}
                    onValueChange={(value) =>
                      setSyncConfig({
                        ...syncConfig,
                        batch_size: value[0],
                      })
                    }
                    min={1}
                    max={200}
                    step={1}
                  />
                  <p className="text-xs text-muted-foreground">
                    <Trans i18nKey="integrations:performance.batchSizeDescription">
                      Number of items to process in each batch (1-1000).
                      Larger batches are faster but use more memory.
                    </Trans>
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="concurrency">
                      <Trans i18nKey="integrations:performance.concurrency">
                        Concurrency
                      </Trans>
                    </Label>
                    <Input
                      id="concurrency"
                      type="number"
                      value={syncConfig.concurrency}
                      onChange={(e) =>
                        setSyncConfig({
                          ...syncConfig,
                          concurrency: parseInt(e.target.value) || 1,
                        })
                      }
                      className="w-20 text-right"
                      min={1}
                      max={20}
                    />
                  </div>
                  <Slider
                    value={[syncConfig.concurrency]}
                    onValueChange={(value) =>
                      setSyncConfig({
                        ...syncConfig,
                        concurrency: value[0],
                      })
                    }
                    min={1}
                    max={20}
                    step={1}
                  />
                  <p className="text-xs text-muted-foreground">
                    <Trans i18nKey="integrations:performance.concurrencyDescription">
                      Number of parallel operations (1-20).
                      Higher values may improve speed but increase CPU usage.
                    </Trans>
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="rate_limit">
                      <Trans i18nKey="integrations:performance.rateLimit">
                        Rate Limit
                      </Trans>
                    </Label>
                    <Input
                      id="rate_limit"
                      type="number"
                      value={syncConfig.rate_limit}
                      onChange={(e) =>
                        setSyncConfig({
                          ...syncConfig,
                          rate_limit: parseInt(e.target.value) || 1,
                        })
                      }
                      className="w-20 text-right"
                      min={1}
                      max={100}
                    />
                  </div>
                  <Slider
                    value={[syncConfig.rate_limit]}
                    onValueChange={(value) =>
                      setSyncConfig({
                        ...syncConfig,
                        rate_limit: value[0],
                      })
                    }
                    min={1}
                    max={50}
                    step={1}
                  />
                  <p className="text-xs text-muted-foreground">
                    <Trans i18nKey="integrations:performance.rateLimitDescription">
                      Maximum API requests per second (1-100).
                      Lower values prevent API rate limiting errors.
                    </Trans>
                  </p>
                </div>

                <Alert>
                  <AlertTitle>
                    <Trans i18nKey="integrations:performance.warning">
                      Performance Warning
                    </Trans>
                  </AlertTitle>
                  <AlertDescription className="text-xs">
                    <Trans i18nKey="integrations:performance.warningDescription">
                      Setting values too high may cause API rate limiting or
                      timeout errors. Start with conservative values and
                      increase gradually.
                    </Trans>
                  </AlertDescription>
                </Alert>
              </div>

              <DialogFooter className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={handleResetConfig}
                  disabled={isSaving}
                >
                  <Trans i18nKey="integrations:performance.reset">
                    Reset to Defaults
                  </Trans>
                </Button>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                    disabled={isSaving}
                  >
                    <Trans i18nKey="common:cancel">Cancel</Trans>
                  </Button>
                  <Button onClick={handleSaveConfig} disabled={isSaving}>
                    {isSaving ? (
                      <Trans i18nKey="common:saving">Saving...</Trans>
                    ) : (
                      <Trans i18nKey="common:save">Save</Trans>
                    )}
                  </Button>
                </div>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-1">
              <p className="text-sm font-medium">
                <Trans i18nKey="integrations:performance.batchSize">
                  Batch Size
                </Trans>
              </p>
              <p className="text-2xl font-bold">{syncConfig.batch_size}</p>
              <p className="text-xs text-muted-foreground">
                <Trans i18nKey="integrations:performance.itemsPerBatch">
                  items per batch
                </Trans>
              </p>
            </div>
            
            <div className="space-y-1">
              <p className="text-sm font-medium">
                <Trans i18nKey="integrations:performance.concurrency">
                  Concurrency
                </Trans>
              </p>
              <p className="text-2xl font-bold">{syncConfig.concurrency}</p>
              <p className="text-xs text-muted-foreground">
                <Trans i18nKey="integrations:performance.parallelOperations">
                  parallel operations
                </Trans>
              </p>
            </div>
            
            <div className="space-y-1">
              <p className="text-sm font-medium">
                <Trans i18nKey="integrations:performance.rateLimit">
                  Rate Limit
                </Trans>
              </p>
              <p className="text-2xl font-bold">{syncConfig.rate_limit}</p>
              <p className="text-xs text-muted-foreground">
                <Trans i18nKey="integrations:performance.requestsPerSecond">
                  requests per second
                </Trans>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
