'use client';

import { useEffect, useState } from 'react';

import { useParams } from 'next/navigation';

import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Switch } from '@kit/ui/switch';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';

interface ScheduleEditorProps {
  integrationId: string;
  resourceType: string;
  platform: string;
  onSave?: () => void;
}

interface Schedule {
  enabled: boolean;
  cron: string;
  strategy: 'full' | 'incremental';
  conflict_strategy: 'timestamp' | 'local' | 'remote' | 'manual';
}

// Mẫu cron expressions
const CRON_PRESETS = [
  { label: 'Every 15 minutes', value: '*/15 * * * *' },
  { label: 'Every hour', value: '0 * * * *' },
  { label: 'Every 6 hours', value: '0 */6 * * *' },
  { label: 'Every 12 hours', value: '0 */12 * * *' },
  { label: 'Every day at midnight', value: '0 0 * * *' },
  { label: 'Every Monday at 9am', value: '0 9 * * 1' },
];

export function ScheduleEditor({
  integrationId,
  resourceType,
  platform,
  onSave,
}: ScheduleEditorProps) {
  const { account } = useParams();
  const { t } = useTranslation(['integrations', 'common']);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [schedule, setSchedule] = useState<Schedule>({
    enabled: false,
    cron: '0 0 * * *',
    strategy: 'incremental',
    conflict_strategy: 'timestamp',
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [cronPreset, setCronPreset] = useState<string>('custom');

  // Lấy lịch trình hiện tại
  useEffect(() => {
    const fetchSchedule = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(
          `/api/integrations/schedule?accountId=${account}&integrationId=${integrationId}`,
        );
        if (!response.ok) {
          throw new Error('Failed to fetch schedule');
        }
        const data = await response.json();
        
        // Lấy lịch trình cho resource type
        const resourceSchedule = data.schedules[resourceType];
        if (resourceSchedule) {
          setSchedule(resourceSchedule);
          
          // Kiểm tra xem cron có khớp với preset nào không
          const preset = CRON_PRESETS.find(p => p.value === resourceSchedule.cron);
          setCronPreset(preset ? preset.value : 'custom');
        }
      } catch (error: any) {
        setError(error.message || 'Failed to load schedule');
        toast.error('Failed to load schedule', {
          description: error.message || 'Please try again later',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSchedule();
  }, [account, integrationId, resourceType]);

  // Xử lý khi thay đổi cron preset
  const handleCronPresetChange = (value: string) => {
    setCronPreset(value);
    if (value !== 'custom') {
      setSchedule({ ...schedule, cron: value });
    }
  };

  // Xử lý khi lưu lịch trình
  const handleSaveSchedule = async () => {
    try {
      setIsSaving(true);
      
      // Gọi API để cập nhật lịch trình
      const response = await fetch('/api/integrations/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountId: account,
          integrationId,
          resourceType,
          schedule,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save schedule');
      }

      toast.success('Schedule saved successfully');
      setIsDialogOpen(false);
      
      // Gọi callback nếu có
      if (onSave) {
        onSave();
      }
    } catch (error: any) {
      toast.error('Failed to save schedule', {
        description: error.message || 'Please try again later',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Xử lý khi xóa lịch trình
  const handleDeleteSchedule = async () => {
    try {
      setIsSaving(true);
      
      // Gọi API để xóa lịch trình
      const response = await fetch(
        `/api/integrations/schedule?accountId=${account}&integrationId=${integrationId}&resourceType=${resourceType}`,
        {
          method: 'DELETE',
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete schedule');
      }

      toast.success('Schedule deleted successfully');
      setIsDialogOpen(false);
      
      // Cập nhật state
      setSchedule({
        enabled: false,
        cron: '0 0 * * *',
        strategy: 'incremental',
        conflict_strategy: 'timestamp',
      });
      
      // Gọi callback nếu có
      if (onSave) {
        onSave();
      }
    } catch (error: any) {
      toast.error('Failed to delete schedule', {
        description: error.message || 'Please try again later',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">
            <Trans i18nKey="common:loading">Loading...</Trans>
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">
            <Trans
              i18nKey={`integrations:${platform}.schedule.${resourceType}Title`}
            >
              {resourceType.charAt(0).toUpperCase() + resourceType.slice(1)}{' '}
              Sync Schedule
            </Trans>
          </h3>
          <p className="text-sm text-muted-foreground">
            <Trans
              i18nKey={`integrations:${platform}.schedule.${resourceType}Description`}
            >
              Configure automatic synchronization for {resourceType}.
            </Trans>
          </p>
        </div>
        <div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                {schedule.enabled ? (
                  <Trans i18nKey="integrations:schedule.editSchedule">
                    Edit Schedule
                  </Trans>
                ) : (
                  <Trans i18nKey="integrations:schedule.createSchedule">
                    Create Schedule
                  </Trans>
                )}
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  <Trans i18nKey="integrations:schedule.configureSchedule">
                    Configure Sync Schedule
                  </Trans>
                </DialogTitle>
                <DialogDescription>
                  <Trans i18nKey="integrations:schedule.configureDescription">
                    Set up automatic synchronization for{' '}
                    {resourceType.toLowerCase()}.
                  </Trans>
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-4 py-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="enabled">
                    <Trans i18nKey="integrations:schedule.enabled">
                      Enable Scheduled Sync
                    </Trans>
                  </Label>
                  <Switch
                    id="enabled"
                    checked={schedule.enabled}
                    onCheckedChange={(checked) =>
                      setSchedule({ ...schedule, enabled: checked })
                    }
                  />
                </div>

                {schedule.enabled && (
                  <>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label
                        htmlFor="cron_preset"
                        className="text-right"
                      >
                        <Trans i18nKey="integrations:schedule.frequency">
                          Frequency
                        </Trans>
                      </Label>
                      <div className="col-span-3">
                        <Select
                          value={cronPreset}
                          onValueChange={handleCronPresetChange}
                        >
                          <SelectTrigger id="cron_preset">
                            <SelectValue
                              placeholder={t('integrations:schedule.selectFrequency')}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            {CRON_PRESETS.map((preset) => (
                              <SelectItem key={preset.value} value={preset.value}>
                                {preset.label}
                              </SelectItem>
                            ))}
                            <SelectItem value="custom">
                              <Trans i18nKey="integrations:schedule.customCron">
                                Custom (Advanced)
                              </Trans>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {cronPreset === 'custom' && (
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label
                          htmlFor="cron"
                          className="text-right"
                        >
                          <Trans i18nKey="integrations:schedule.cronExpression">
                            Cron Expression
                          </Trans>
                        </Label>
                        <div className="col-span-3">
                          <Input
                            id="cron"
                            value={schedule.cron}
                            onChange={(e) =>
                              setSchedule({ ...schedule, cron: e.target.value })
                            }
                            placeholder="0 0 * * *"
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            <Trans i18nKey="integrations:schedule.cronDescription">
                              Use cron syntax (minute hour day month weekday)
                            </Trans>
                          </p>
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label
                        htmlFor="strategy"
                        className="text-right"
                      >
                        <Trans i18nKey="integrations:schedule.syncStrategy">
                          Sync Strategy
                        </Trans>
                      </Label>
                      <div className="col-span-3">
                        <Select
                          value={schedule.strategy}
                          onValueChange={(value: 'full' | 'incremental') =>
                            setSchedule({ ...schedule, strategy: value })
                          }
                        >
                          <SelectTrigger id="strategy">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="incremental">
                              <Trans i18nKey="integrations:schedule.incremental">
                                Incremental (Only new/updated data)
                              </Trans>
                            </SelectItem>
                            <SelectItem value="full">
                              <Trans i18nKey="integrations:schedule.full">
                                Full (All data)
                              </Trans>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label
                        htmlFor="conflict_strategy"
                        className="text-right"
                      >
                        <Trans i18nKey="integrations:schedule.conflictStrategy">
                          Conflict Strategy
                        </Trans>
                      </Label>
                      <div className="col-span-3">
                        <Select
                          value={schedule.conflict_strategy}
                          onValueChange={(value: 'timestamp' | 'local' | 'remote' | 'manual') =>
                            setSchedule({ ...schedule, conflict_strategy: value })
                          }
                        >
                          <SelectTrigger id="conflict_strategy">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="timestamp">
                              <Trans i18nKey="integrations:schedule.timestamp">
                                Timestamp (Newest wins)
                              </Trans>
                            </SelectItem>
                            <SelectItem value="local">
                              <Trans i18nKey="integrations:schedule.local">
                                Local (Your system wins)
                              </Trans>
                            </SelectItem>
                            <SelectItem value="remote">
                              <Trans i18nKey="integrations:schedule.remote">
                                Remote ({platform} wins)
                              </Trans>
                            </SelectItem>
                            <SelectItem value="manual">
                              <Trans i18nKey="integrations:schedule.manual">
                                Manual (Require user resolution)
                              </Trans>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </>
                )}
              </div>

              <DialogFooter className="flex justify-between">
                {schedule.enabled && (
                  <Button
                    variant="outline"
                    onClick={handleDeleteSchedule}
                    disabled={isSaving}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trans i18nKey="integrations:schedule.deleteSchedule">
                      Delete Schedule
                    </Trans>
                  </Button>
                )}
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                    disabled={isSaving}
                  >
                    <Trans i18nKey="common:cancel">Cancel</Trans>
                  </Button>
                  <Button onClick={handleSaveSchedule} disabled={isSaving}>
                    {isSaving ? (
                      <Trans i18nKey="common:saving">Saving...</Trans>
                    ) : (
                      <Trans i18nKey="common:save">Save</Trans>
                    )}
                  </Button>
                </div>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Card>
        <CardContent className="pt-6">
          {schedule.enabled ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium">
                    <Trans i18nKey="integrations:schedule.status">
                      Status
                    </Trans>
                  </p>
                  <p className="text-sm text-green-600 font-medium">
                    <Trans i18nKey="integrations:schedule.active">
                      Active
                    </Trans>
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">
                    <Trans i18nKey="integrations:schedule.frequency">
                      Frequency
                    </Trans>
                  </p>
                  <p className="text-sm">
                    {CRON_PRESETS.find((p) => p.value === schedule.cron)?.label || (
                      <code className="text-xs bg-muted px-1 py-0.5 rounded">
                        {schedule.cron}
                      </code>
                    )}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium">
                    <Trans i18nKey="integrations:schedule.syncStrategy">
                      Sync Strategy
                    </Trans>
                  </p>
                  <p className="text-sm capitalize">{schedule.strategy}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">
                    <Trans i18nKey="integrations:schedule.conflictStrategy">
                      Conflict Strategy
                    </Trans>
                  </p>
                  <p className="text-sm capitalize">{schedule.conflict_strategy}</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-6">
              <p className="text-muted-foreground">
                <Trans i18nKey="integrations:schedule.noSchedule">
                  No schedule configured. Click "Create Schedule" to set up
                  automatic synchronization.
                </Trans>
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
