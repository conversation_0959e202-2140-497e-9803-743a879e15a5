'use client';

import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

interface EmptyIntegrationsProps {
  account: string;
}

export function EmptyIntegrations({ account }: EmptyIntegrationsProps) {
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);

  return (
    <Card className="border-dashed">
      <CardHeader className="text-center">
        <CardTitle>
          <Trans i18nKey="integrations:noIntegrations">No Integrations</Trans>
        </CardTitle>
        <CardDescription>
          <Trans i18nKey="integrations:noIntegrationsDescription">
            Connect your business with third-party services to enhance your capabilities.
          </Trans>
        </CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col items-center justify-center pb-6 pt-2">
        <div className="mb-4 rounded-full bg-primary/10 p-6">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-10 w-10 text-primary"
          >
            <path d="M12 22v-5" />
            <path d="M9 8V2" />
            <path d="M15 8V2" />
            <path d="M18 8v4" />
            <path d="M6 8v4" />
            <path d="M12 12v4" />
            <path d="M21 14a9 9 0 1 1-18 0" />
          </svg>
        </div>
        <div className="max-w-md text-center text-sm text-muted-foreground">
          <Trans i18nKey="integrations:emptyStateDescription">
            Integrations allow you to connect with third-party services like iPOS, ZNS, and more.
            Start by adding your first integration.
          </Trans>
        </div>
      </CardContent>
      <CardFooter className="flex justify-center">
        <Button
          onClick={() => router.push(`/home/<USER>/integrations/connect`)}
          data-testid="add-first-integration-button"
        >
          <Trans i18nKey="integrations:addFirstIntegration">Add Your First Integration</Trans>
        </Button>
      </CardFooter>
    </Card>
  );
}
