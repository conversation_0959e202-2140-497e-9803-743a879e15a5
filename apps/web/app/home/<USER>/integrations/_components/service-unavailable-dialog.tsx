'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

import { AlertCircle, Construction, ExternalLink, Info } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

interface ServiceUnavailableDialogProps {
  isOpen: boolean;
  onClose: () => void;
  serviceName: string;
  serviceType: string;
  estimatedAvailability?: string;
  learnMoreUrl?: string;
  contactEmail?: string;
}

export function ServiceUnavailableDialog({
  isOpen,
  onClose,
  serviceName,
  serviceType,
  estimatedAvailability,
  learnMoreUrl,
  contactEmail,
}: ServiceUnavailableDialogProps) {
  const { t } = useTranslation(['integrations', 'common']);
  const router = useRouter();

  const handleLearnMore = () => {
    if (learnMoreUrl) {
      window.open(learnMoreUrl, '_blank');
    }
  };

  const handleContactUs = () => {
    if (contactEmail) {
      window.location.href = `mailto:${contactEmail}?subject=${encodeURIComponent(
        `${t('integrations:serviceUnavailable.emailSubject')} - ${serviceName}`
      )}`;
    } else {
      // Fallback to contact page
      router.push('/contact');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
            <Construction className="h-6 w-6 text-amber-600" />
          </div>
          <DialogTitle className="text-center text-xl">
            {t('integrations:serviceUnavailable.title', { serviceName })}
          </DialogTitle>
          <DialogDescription className="text-center">
            {t('integrations:serviceUnavailable.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="rounded-md bg-muted p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <Info className="h-5 w-5 text-blue-500" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium">
                  {t('integrations:serviceUnavailable.serviceInfo')}
                </h3>
                <div className="mt-2 text-sm">
                  <p>
                    <Trans
                      i18nKey="integrations:serviceUnavailable.inDevelopment"
                      values={{ serviceName }}
                    />
                  </p>
                  {estimatedAvailability && (
                    <p className="mt-1">
                      <Trans
                        i18nKey="integrations:serviceUnavailable.estimatedAvailability"
                        values={{ date: estimatedAvailability }}
                      />
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-md bg-blue-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertCircle className="h-5 w-5 text-blue-500" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  {t('integrations:serviceUnavailable.interestedQuestion')}
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    <Trans i18nKey="integrations:serviceUnavailable.priorityMessage" />
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col gap-2 sm:flex-row">
          <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
            {t('common:cancel')}
          </Button>
          {learnMoreUrl && (
            <Button
              variant="outline"
              onClick={handleLearnMore}
              className="w-full sm:w-auto"
            >
              {t('integrations:serviceUnavailable.learnMore')}
              <ExternalLink className="ml-2 h-4 w-4" />
            </Button>
          )}
          <Button
            onClick={handleContactUs}
            className="w-full sm:w-auto"
          >
            {t('integrations:serviceUnavailable.contactUs')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
