'use client';

import Image from 'next/image';

interface IntegrationIconProps {
  type: string;
  size?: number;
}

export function IntegrationIcon({ type, size = 24 }: IntegrationIconProps) {
  const getIconPath = () => {
    switch (type) {
      case 'ipos':
        return '/images/ipos-logo.png';
      case 'zns':
        return '/images/zns-logo.png';
      default:
        return '/images/integration-default.png';
    }
  };

  return (
    <div className="relative h-6 w-6 overflow-hidden rounded-md">
      <Image
        src={getIconPath()}
        alt={`${type} logo`}
        width={size}
        height={size}
        className="object-contain"
      />
    </div>
  );
}
