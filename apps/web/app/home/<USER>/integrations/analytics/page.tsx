'use client';

import { useEffect, useState } from 'react';

import { useParams, useRouter } from 'next/navigation';

import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { Separator } from '@kit/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { IntegrationAnalytics } from './_components/integration-analytics';
import { ResourceAnalytics } from './_components/resource-analytics';
import { SyncHistoryChart } from './_components/sync-history-chart';

export default function IntegrationsAnalyticsPage() {
  const { account } = useParams();
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [integrations, setIntegrations] = useState<any[]>([]);
  const [syncHistory, setSyncHistory] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('overview');

  // Lấy danh sách tích hợp và lịch sử đồng bộ
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Lấy danh sách tích hợp
        const integrationsResponse = await fetch(
          `/api/integrations/setup?accountId=${account}`,
        );
        if (!integrationsResponse.ok) {
          throw new Error('Failed to fetch integrations');
        }
        const integrationsData = await integrationsResponse.json();
        setIntegrations(integrationsData.integrations || []);
        
        // Lấy lịch sử đồng bộ
        const syncHistoryResponse = await fetch(
          `/api/integrations/sync?accountId=${account}`,
        );
        if (!syncHistoryResponse.ok) {
          throw new Error('Failed to fetch sync history');
        }
        const syncHistoryData = await syncHistoryResponse.json();
        setSyncHistory(syncHistoryData.syncLogs || []);
      } catch (error: any) {
        setError(error.message || 'Failed to load data');
        toast.error('Failed to load analytics data', {
          description: error.message || 'Please try again later',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [account]);

  if (isLoading) {
    return (
      <>
        <TeamAccountLayoutPageHeader
          title={
            <Trans i18nKey="integrations:analytics.title">
              Integration Analytics
            </Trans>
          }
          description={<AppBreadcrumbs />}
          account={account as string}
        />
        <PageBody>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-sm text-muted-foreground">
                <Trans i18nKey="common:loading">Loading...</Trans>
              </p>
            </div>
          </div>
        </PageBody>
      </>
    );
  }

  if (error) {
    return (
      <>
        <TeamAccountLayoutPageHeader
          title={
            <Trans i18nKey="integrations:analytics.title">
              Integration Analytics
            </Trans>
          }
          description={<AppBreadcrumbs />}
          account={account as string}
        />
        <PageBody>
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className="mt-4">
            <Button
              onClick={() => router.push(`/home/<USER>/integrations`)}
            >
              <Trans i18nKey="common:goBack">Go Back</Trans>
            </Button>
          </div>
        </PageBody>
      </>
    );
  }

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={
          <Trans i18nKey="integrations:analytics.title">
            Integration Analytics
          </Trans>
        }
        description={<AppBreadcrumbs />}
        account={account as string}
      />

      <PageBody>
        <Tabs
          defaultValue="overview"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">
              <Trans i18nKey="integrations:analytics.overview">
                Overview
              </Trans>
            </TabsTrigger>
            <TabsTrigger value="integrations">
              <Trans i18nKey="integrations:analytics.integrations">
                Integrations
              </Trans>
            </TabsTrigger>
            <TabsTrigger value="resources">
              <Trans i18nKey="integrations:analytics.resources">
                Resources
              </Trans>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">
                    <Trans i18nKey="integrations:analytics.totalIntegrations">
                      Total Integrations
                    </Trans>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    {integrations.length}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    <Trans i18nKey="integrations:analytics.activeIntegrations">
                      {integrations.filter((i) => i.enabled).length} active
                    </Trans>
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">
                    <Trans i18nKey="integrations:analytics.totalSyncs">
                      Total Syncs
                    </Trans>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    {syncHistory.length}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    <Trans i18nKey="integrations:analytics.lastSync">
                      Last sync:{' '}
                      {syncHistory.length > 0
                        ? new Date(
                            syncHistory[0].started_at,
                          ).toLocaleString()
                        : 'Never'}
                    </Trans>
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">
                    <Trans i18nKey="integrations:analytics.syncSuccess">
                      Sync Success Rate
                    </Trans>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    {syncHistory.length > 0
                      ? `${Math.round(
                          (syncHistory.filter(
                            (s) => s.status === 'success' || s.status === 'partial',
                          ).length /
                            syncHistory.length) *
                            100,
                        )}%`
                      : '0%'}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    <Trans i18nKey="integrations:analytics.successfulSyncs">
                      {
                        syncHistory.filter(
                          (s) => s.status === 'success' || s.status === 'partial',
                        ).length
                      }{' '}
                      successful syncs
                    </Trans>
                  </p>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>
                  <Trans i18nKey="integrations:analytics.syncHistory">
                    Sync History
                  </Trans>
                </CardTitle>
                <CardDescription>
                  <Trans i18nKey="integrations:analytics.syncHistoryDescription">
                    History of synchronization operations over time
                  </Trans>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SyncHistoryChart syncHistory={syncHistory} />
              </CardContent>
            </Card>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>
                    <Trans i18nKey="integrations:analytics.topIntegrations">
                      Top Integrations
                    </Trans>
                  </CardTitle>
                  <CardDescription>
                    <Trans i18nKey="integrations:analytics.topIntegrationsDescription">
                      Most active integrations by sync count
                    </Trans>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {integrations.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">
                        <Trans i18nKey="integrations:analytics.noIntegrations">
                          No integrations found
                        </Trans>
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {integrations
                        .slice(0, 5)
                        .map((integration) => {
                          const integrationSyncs = syncHistory.filter(
                            (s) => s.integration?.id === integration.id,
                          );
                          return (
                            <div
                              key={integration.id}
                              className="flex items-center justify-between"
                            >
                              <div className="flex items-center space-x-2">
                                <div
                                  className={`w-3 h-3 rounded-full ${
                                    integration.enabled
                                      ? 'bg-green-500'
                                      : 'bg-gray-300'
                                  }`}
                                />
                                <span className="font-medium">
                                  {integration.name}
                                </span>
                              </div>
                              <span className="text-sm">
                                {integrationSyncs.length} syncs
                              </span>
                            </div>
                          );
                        })}
                    </div>
                  )}
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>
                    <Trans i18nKey="integrations:analytics.resourceDistribution">
                      Resource Distribution
                    </Trans>
                  </CardTitle>
                  <CardDescription>
                    <Trans i18nKey="integrations:analytics.resourceDistributionDescription">
                      Distribution of synced resources by type
                    </Trans>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {syncHistory.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">
                        <Trans i18nKey="integrations:analytics.noSyncHistory">
                          No sync history found
                        </Trans>
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {Object.entries(
                        syncHistory.reduce((acc, sync) => {
                          acc[sync.resource_type] = (acc[sync.resource_type] || 0) + 1;
                          return acc;
                        }, {} as Record<string, number>),
                      )
                        .sort((a, b) => b[1] - a[1])
                        .slice(0, 5)
                        .map(([resourceType, count]) => (
                          <div
                            key={resourceType}
                            className="flex items-center justify-between"
                          >
                            <span className="font-medium capitalize">
                              {resourceType}
                            </span>
                            <span className="text-sm">{count} syncs</span>
                          </div>
                        ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="integrations" className="space-y-6 py-4">
            <IntegrationAnalytics
              integrations={integrations}
              syncHistory={syncHistory}
              accountId={account as string}
            />
          </TabsContent>

          <TabsContent value="resources" className="space-y-6 py-4">
            <ResourceAnalytics
              syncHistory={syncHistory}
              accountId={account as string}
            />
          </TabsContent>
        </Tabs>
      </PageBody>
    </>
  );
}
