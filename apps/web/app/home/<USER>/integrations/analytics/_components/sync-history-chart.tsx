'use client';

import { useEffect, useState } from 'react';

import { useTranslation } from 'react-i18next';
import { <PERSON>, <PERSON>hart, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

import { Trans } from '@kit/ui/trans';

interface SyncHistoryChartProps {
  syncHistory: any[];
}

export function SyncHistoryChart({ syncHistory }: SyncHistoryChartProps) {
  const { t } = useTranslation(['integrations', 'common']);
  const [chartData, setChartData] = useState<any[]>([]);

  // Xử lý dữ liệu cho biểu đồ
  useEffect(() => {
    if (!syncHistory || syncHistory.length === 0) {
      setChartData([]);
      return;
    }

    // Nhóm dữ liệu theo ngày
    const groupedByDate = syncHistory.reduce((acc, sync) => {
      const date = new Date(sync.started_at).toLocaleDateString();
      
      if (!acc[date]) {
        acc[date] = {
          date,
          success: 0,
          error: 0,
          partial: 0,
          total: 0,
        };
      }
      
      acc[date].total += 1;
      
      if (sync.status === 'success') {
        acc[date].success += 1;
      } else if (sync.status === 'error') {
        acc[date].error += 1;
      } else if (sync.status === 'partial') {
        acc[date].partial += 1;
      }
      
      return acc;
    }, {} as Record<string, any>);
    
    // Chuyển đổi thành mảng và sắp xếp theo ngày
    const data = Object.values(groupedByDate).sort((a, b) => {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });
    
    // Giới hạn số lượng dữ liệu hiển thị
    setChartData(data.slice(-14)); // Hiển thị 14 ngày gần nhất
  }, [syncHistory]);

  if (chartData.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          <Trans i18nKey="integrations:analytics.noDataForChart">
            No data available for chart
          </Trans>
        </p>
      </div>
    );
  }

  return (
    <div className="w-full h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar
            dataKey="success"
            stackId="a"
            name={t('integrations:analytics.successfulSyncs')}
            fill="#10b981"
          />
          <Bar
            dataKey="partial"
            stackId="a"
            name={t('integrations:analytics.partialSyncs')}
            fill="#f59e0b"
          />
          <Bar
            dataKey="error"
            stackId="a"
            name={t('integrations:analytics.failedSyncs')}
            fill="#ef4444"
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
