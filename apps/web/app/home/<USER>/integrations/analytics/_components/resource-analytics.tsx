'use client';

import { useEffect, useState } from 'react';

import { useTranslation } from 'react-i18next';
import { <PERSON>, Legend, Pie, <PERSON><PERSON><PERSON>, ResponsiveContainer, Tooltip } from 'recharts';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { Trans } from '@kit/ui/trans';

interface ResourceAnalyticsProps {
  syncHistory: any[];
  accountId: string;
}

// <PERSON>àu sắc cho biểu đồ
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export function ResourceAnalytics({
  syncHistory,
  accountId,
}: ResourceAnalyticsProps) {
  const { t } = useTranslation(['integrations', 'common']);
  const [selectedTimeframe, setSelectedTimeframe] = useState<string>('all');
  const [resourceData, setResourceData] = useState<any[]>([]);
  const [statusData, setStatusData] = useState<any[]>([]);

  // Lọc lịch sử đồng bộ theo khoảng thời gian
  const filteredHistory = () => {
    if (selectedTimeframe === 'all') {
      return syncHistory;
    }
    
    const now = new Date();
    let startDate: Date;
    
    switch (selectedTimeframe) {
      case 'day':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 1);
        break;
      case 'week':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
        break;
      default:
        return syncHistory;
    }
    
    return syncHistory.filter((sync) => {
      const syncDate = new Date(sync.started_at);
      return syncDate >= startDate;
    });
  };

  // Cập nhật dữ liệu biểu đồ khi thay đổi khoảng thời gian
  useEffect(() => {
    const history = filteredHistory();
    
    // Dữ liệu cho biểu đồ resource
    const resourceCounts = history.reduce((acc, sync) => {
      const resourceType = sync.resource_type;
      acc[resourceType] = (acc[resourceType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const resourceChartData = Object.entries(resourceCounts).map(([name, value]) => ({
      name,
      value,
    }));
    
    setResourceData(resourceChartData);
    
    // Dữ liệu cho biểu đồ status
    const statusCounts = history.reduce((acc, sync) => {
      const status = sync.status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const statusChartData = Object.entries(statusCounts).map(([name, value]) => ({
      name,
      value,
    }));
    
    setStatusData(statusChartData);
  }, [selectedTimeframe, syncHistory]);

  // Tính toán thống kê cho từng loại resource
  const resourceStats = () => {
    const history = filteredHistory();
    
    // Nhóm theo resource type
    const groupedByResource = history.reduce((acc, sync) => {
      const resourceType = sync.resource_type;
      
      if (!acc[resourceType]) {
        acc[resourceType] = {
          resourceType,
          syncCount: 0,
          successCount: 0,
          errorCount: 0,
          partialCount: 0,
          itemsProcessed: 0,
          itemsCreated: 0,
          itemsFailed: 0,
        };
      }
      
      acc[resourceType].syncCount += 1;
      
      if (sync.status === 'success') {
        acc[resourceType].successCount += 1;
      } else if (sync.status === 'error') {
        acc[resourceType].errorCount += 1;
      } else if (sync.status === 'partial') {
        acc[resourceType].partialCount += 1;
      }
      
      acc[resourceType].itemsProcessed += (sync.items_processed || 0);
      acc[resourceType].itemsCreated += (sync.items_created || 0);
      acc[resourceType].itemsFailed += (sync.items_failed || 0);
      
      return acc;
    }, {} as Record<string, any>);
    
    return Object.values(groupedByResource);
  };

  // Format trạng thái
  const formatStatus = (status: string) => {
    switch (status) {
      case 'success':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <Trans i18nKey="integrations:sync.status.success">Success</Trans>
          </span>
        );
      case 'error':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <Trans i18nKey="integrations:sync.status.error">Error</Trans>
          </span>
        );
      case 'partial':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Trans i18nKey="integrations:sync.status.partial">Partial</Trans>
          </span>
        );
      case 'in_progress':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <Trans i18nKey="integrations:sync.status.inProgress">
              In Progress
            </Trans>
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-end">
        <Select
          value={selectedTimeframe}
          onValueChange={setSelectedTimeframe}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select timeframe" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">
              <Trans i18nKey="integrations:analytics.allTime">
                All Time
              </Trans>
            </SelectItem>
            <SelectItem value="day">
              <Trans i18nKey="integrations:analytics.last24Hours">
                Last 24 Hours
              </Trans>
            </SelectItem>
            <SelectItem value="week">
              <Trans i18nKey="integrations:analytics.lastWeek">
                Last Week
              </Trans>
            </SelectItem>
            <SelectItem value="month">
              <Trans i18nKey="integrations:analytics.lastMonth">
                Last Month
              </Trans>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>
              <Trans i18nKey="integrations:analytics.resourceDistribution">
                Resource Distribution
              </Trans>
            </CardTitle>
            <CardDescription>
              <Trans i18nKey="integrations:analytics.resourceDistributionDescription">
                Distribution of synced resources by type
              </Trans>
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[300px]">
            {resourceData.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <p className="text-muted-foreground">
                  <Trans i18nKey="integrations:analytics.noData">
                    No data available
                  </Trans>
                </p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={resourceData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {resourceData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>
              <Trans i18nKey="integrations:analytics.syncStatusDistribution">
                Sync Status Distribution
              </Trans>
            </CardTitle>
            <CardDescription>
              <Trans i18nKey="integrations:analytics.syncStatusDistributionDescription">
                Distribution of sync operations by status
              </Trans>
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[300px]">
            {statusData.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <p className="text-muted-foreground">
                  <Trans i18nKey="integrations:analytics.noData">
                    No data available
                  </Trans>
                </p>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {statusData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={
                          entry.name === 'success'
                            ? '#10b981'
                            : entry.name === 'partial'
                            ? '#f59e0b'
                            : entry.name === 'error'
                            ? '#ef4444'
                            : '#3b82f6'
                        }
                      />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>
            <Trans i18nKey="integrations:analytics.resourcePerformance">
              Resource Performance
            </Trans>
          </CardTitle>
          <CardDescription>
            <Trans i18nKey="integrations:analytics.resourcePerformanceDescription">
              Performance metrics for each resource type
            </Trans>
          </CardDescription>
        </CardHeader>
        <CardContent>
          {resourceStats().length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                <Trans i18nKey="integrations:analytics.noData">
                  No data available
                </Trans>
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.resourceType">
                        Resource Type
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.syncCount">
                        Sync Count
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.successRate">
                        Success Rate
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.itemsProcessed">
                        Items Processed
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.itemsCreated">
                        Items Created
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.itemsFailed">
                        Items Failed
                      </Trans>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {resourceStats().map((stat) => (
                    <TableRow key={stat.resourceType}>
                      <TableCell className="font-medium capitalize">
                        {stat.resourceType}
                      </TableCell>
                      <TableCell>{stat.syncCount}</TableCell>
                      <TableCell>
                        {stat.syncCount > 0
                          ? `${Math.round(
                              ((stat.successCount + stat.partialCount) / stat.syncCount) * 100
                            )}%`
                          : '0%'}
                      </TableCell>
                      <TableCell>{stat.itemsProcessed}</TableCell>
                      <TableCell>{stat.itemsCreated}</TableCell>
                      <TableCell>{stat.itemsFailed}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
