'use client';

import { useState } from 'react';

import Link from 'next/link';

import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { Trans } from '@kit/ui/trans';

interface IntegrationAnalyticsProps {
  integrations: any[];
  syncHistory: any[];
  accountId: string;
}

export function IntegrationAnalytics({
  integrations,
  syncHistory,
  accountId,
}: IntegrationAnalyticsProps) {
  const { t } = useTranslation(['integrations', 'common']);
  const [selectedIntegration, setSelectedIntegration] = useState<string>('all');

  // <PERSON><PERSON><PERSON> l<PERSON>ch sử đồng bộ theo integration
  const filteredHistory = selectedIntegration === 'all'
    ? syncHistory
    : syncHistory.filter((sync) => sync.integration?.id === selectedIntegration);

  // Tính toán thống kê cho từng integration
  const integrationStats = integrations.map((integration) => {
    const integrationSyncs = syncHistory.filter(
      (sync) => sync.integration?.id === integration.id
    );
    
    const successCount = integrationSyncs.filter(
      (sync) => sync.status === 'success'
    ).length;
    
    const partialCount = integrationSyncs.filter(
      (sync) => sync.status === 'partial'
    ).length;
    
    const errorCount = integrationSyncs.filter(
      (sync) => sync.status === 'error'
    ).length;
    
    const totalItems = integrationSyncs.reduce(
      (sum, sync) => sum + (sync.items_processed || 0),
      0
    );
    
    const successRate = integrationSyncs.length > 0
      ? ((successCount + partialCount) / integrationSyncs.length) * 100
      : 0;
    
    const lastSync = integrationSyncs.length > 0
      ? new Date(
          Math.max(...integrationSyncs.map((sync) => new Date(sync.started_at).getTime()))
        )
      : null;
    
    return {
      ...integration,
      syncCount: integrationSyncs.length,
      successCount,
      partialCount,
      errorCount,
      totalItems,
      successRate,
      lastSync,
    };
  });

  // Format thời gian
  const formatDate = (date: Date | null) => {
    if (!date) return 'Never';
    return date.toLocaleString();
  };

  // Format trạng thái
  const formatStatus = (status: string) => {
    switch (status) {
      case 'success':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <Trans i18nKey="integrations:sync.status.success">Success</Trans>
          </span>
        );
      case 'error':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <Trans i18nKey="integrations:sync.status.error">Error</Trans>
          </span>
        );
      case 'partial':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Trans i18nKey="integrations:sync.status.partial">Partial</Trans>
          </span>
        );
      case 'in_progress':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <Trans i18nKey="integrations:sync.status.inProgress">
              In Progress
            </Trans>
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">
              <Trans i18nKey="integrations:analytics.totalSyncs">
                Total Syncs
              </Trans>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {syncHistory.length}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">
              <Trans i18nKey="integrations:analytics.successRate">
                Success Rate
              </Trans>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {syncHistory.length > 0
                ? `${Math.round(
                    (syncHistory.filter(
                      (s) => s.status === 'success' || s.status === 'partial',
                    ).length /
                      syncHistory.length) *
                      100,
                  )}%`
                : '0%'}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">
              <Trans i18nKey="integrations:analytics.totalItems">
                Total Items Processed
              </Trans>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">
              {syncHistory.reduce(
                (sum, sync) => sum + (sync.items_processed || 0),
                0
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>
            <Trans i18nKey="integrations:analytics.integrationPerformance">
              Integration Performance
            </Trans>
          </CardTitle>
          <CardDescription>
            <Trans i18nKey="integrations:analytics.integrationPerformanceDescription">
              Performance metrics for each integration
            </Trans>
          </CardDescription>
        </CardHeader>
        <CardContent>
          {integrationStats.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                <Trans i18nKey="integrations:analytics.noIntegrations">
                  No integrations found
                </Trans>
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.integration">
                        Integration
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.syncCount">
                        Sync Count
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.successRate">
                        Success Rate
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.itemsProcessed">
                        Items Processed
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.lastSync">
                        Last Sync
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.status">
                        Status
                      </Trans>
                    </TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {integrationStats.map((integration) => (
                    <TableRow key={integration.id}>
                      <TableCell className="font-medium">
                        {integration.name}
                      </TableCell>
                      <TableCell>{integration.syncCount}</TableCell>
                      <TableCell>
                        {Math.round(integration.successRate)}%
                      </TableCell>
                      <TableCell>{integration.totalItems}</TableCell>
                      <TableCell>
                        {formatDate(integration.lastSync)}
                      </TableCell>
                      <TableCell>
                        <div
                          className={`w-3 h-3 rounded-full ${
                            integration.enabled
                              ? 'bg-green-500'
                              : 'bg-gray-300'
                          }`}
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link href={`/home/<USER>/integrations/${integration.type}`}>
                            <Trans i18nKey="integrations:analytics.view">
                              View
                            </Trans>
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle>
              <Trans i18nKey="integrations:analytics.syncHistory">
                Sync History
              </Trans>
            </CardTitle>
            <CardDescription>
              <Trans i18nKey="integrations:analytics.syncHistoryDescription">
                Recent synchronization operations
              </Trans>
            </CardDescription>
          </div>
          <Select
            value={selectedIntegration}
            onValueChange={setSelectedIntegration}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select integration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                <Trans i18nKey="integrations:analytics.allIntegrations">
                  All Integrations
                </Trans>
              </SelectItem>
              {integrations.map((integration) => (
                <SelectItem key={integration.id} value={integration.id}>
                  {integration.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardHeader>
        <CardContent>
          {filteredHistory.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                <Trans i18nKey="integrations:analytics.noSyncHistory">
                  No sync history found
                </Trans>
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.integration">
                        Integration
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.resourceType">
                        Resource Type
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.status">
                        Status
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.itemsProcessed">
                        Items Processed
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.startedAt">
                        Started At
                      </Trans>
                    </TableHead>
                    <TableHead>
                      <Trans i18nKey="integrations:analytics.completedAt">
                        Completed At
                      </Trans>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredHistory.slice(0, 10).map((sync) => (
                    <TableRow key={sync.id}>
                      <TableCell className="font-medium">
                        {sync.integration?.name || '-'}
                      </TableCell>
                      <TableCell className="capitalize">
                        {sync.resource_type}
                      </TableCell>
                      <TableCell>{formatStatus(sync.status)}</TableCell>
                      <TableCell>{sync.items_processed || 0}</TableCell>
                      <TableCell>
                        {new Date(sync.started_at).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {sync.completed_at
                          ? new Date(sync.completed_at).toLocaleString()
                          : '-'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button
            variant="outline"
            size="sm"
            asChild
            className="ml-auto"
          >
            <Link href={`/home/<USER>/integrations/sync-history`}>
              <Trans i18nKey="integrations:analytics.viewAllHistory">
                View All History
              </Trans>
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
