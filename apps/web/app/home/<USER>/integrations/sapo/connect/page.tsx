'use client';

import { useState } from 'react';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import { Separator } from '@kit/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

// Schema cho form kết nối Sapo
const sapoConnectionSchema = z.object({
  api_key: z.string().min(1, { message: 'API Key is required' }),
  app_id: z.string().min(1, { message: 'App ID is required' }),
  app_secret: z.string().min(1, { message: 'App Secret is required' }),
  baseUrl: z.string().url({ message: 'Please enter a valid URL' }).optional(),
});

type SapoConnectionFormValues = z.infer<typeof sapoConnectionSchema>;

export default function SapoConnectPage() {
  const { account } = useParams();
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [testStatus, setTestStatus] = useState<
    'idle' | 'testing' | 'success' | 'error'
  >('idle');
  const [testError, setTestError] = useState<string | null>(null);

  // Form với validation và giá trị mặc định
  const form = useForm<SapoConnectionFormValues>({
    resolver: zodResolver(sapoConnectionSchema),
    defaultValues: {
      api_key: 'sapo_api_key_example',
      app_id: 'sapo_app_id_example',
      app_secret: 'sapo_app_secret_example',
      baseUrl: 'https://api.sapo.vn',
    },
  });

  // Xử lý khi submit form
  const onSubmit = async (values: SapoConnectionFormValues) => {
    setIsSubmitting(true);
    try {
      // Gọi API để kết nối với Sapo
      const response = await fetch('/api/integrations/setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountId: account,
          platform: 'sapo',
          credentials: values,
          name: 'Sapo',
          description: 'Sync products, orders, and customers with Sapo',
          config: {
            exclusive_resources: {
              products: 'sapo',
              orders: 'sapo',
              customers: 'sapo',
            },
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to connect to Sapo');
      }

      toast.success('Connected to Sapo successfully', {
        description: 'Your Sapo account has been connected.',
      });
      router.push(`/home/<USER>/integrations`);
    } catch (error: any) {
      toast.error('Failed to connect to Sapo', {
        description: error.message || 'Something went wrong',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý khi test kết nối
  const handleTestConnection = async () => {
    const values = form.getValues();
    const isValid = await form.trigger();

    if (!isValid) {
      return;
    }

    setTestStatus('testing');
    setTestError(null);

    try {
      // Gọi API để test kết nối với Sapo
      const response = await fetch('/api/integrations/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountId: account,
          platform: 'sapo',
          credentials: values,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Connection test failed');
      }

      setTestStatus('success');
    } catch (error: any) {
      setTestStatus('error');
      setTestError(error.message || 'Something went wrong');
    }
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={
          <Trans i18nKey="integrations:sapo.connect.title">Connect Sapo</Trans>
        }
        description={<AppBreadcrumbs />}
        account={account as string}
      />

      <PageBody>
        <Card className="mx-auto max-w-3xl">
          <CardHeader>
            <CardTitle>
              <Trans i18nKey="integrations:sapo.connect.title">
                Connect Sapo
              </Trans>
            </CardTitle>
            <CardDescription>
              <Trans i18nKey="integrations:sapo.connect.description">
                Connect your Sapo account to sync products, orders, and
                customers.
              </Trans>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="credentials" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="credentials">
                  <Trans i18nKey="integrations:sapo.connect.credentials">
                    Credentials
                  </Trans>
                </TabsTrigger>
                <TabsTrigger value="webhook">
                  <Trans i18nKey="integrations:sapo.connect.webhook">
                    Webhook
                  </Trans>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="credentials" className="space-y-4 py-4">
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <FormField
                      control={form.control}
                      name="api_key"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="integrations:sapo.connect.apiKey">
                              API Key
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter your Sapo API Key"
                              data-testid="sapo-api-key-input"
                            />
                          </FormControl>
                          <FormDescription>
                            <Trans i18nKey="integrations:sapo.connect.apiKeyDescription">
                              Your Sapo API Key from the developer portal.
                            </Trans>
                          </FormDescription>
                          <p className="mt-1 text-xs text-muted-foreground">
                            <span className="font-medium">Example:</span> sapo_api_key_example
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="app_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="integrations:sapo.connect.appId">
                              App ID
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter your Sapo App ID"
                              data-testid="sapo-app-id-input"
                            />
                          </FormControl>
                          <FormDescription>
                            <Trans i18nKey="integrations:sapo.connect.appIdDescription">
                              Your Sapo App ID from the developer portal.
                            </Trans>
                          </FormDescription>
                          <p className="mt-1 text-xs text-muted-foreground">
                            <span className="font-medium">Example:</span> sapo_app_id_example
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="app_secret"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="integrations:sapo.connect.appSecret">
                              App Secret
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="password"
                              placeholder="Enter your Sapo App Secret"
                              data-testid="sapo-app-secret-input"
                            />
                          </FormControl>
                          <FormDescription>
                            <Trans i18nKey="integrations:sapo.connect.appSecretDescription">
                              Your Sapo App Secret from the developer portal.
                            </Trans>
                          </FormDescription>
                          <p className="mt-1 text-xs text-muted-foreground">
                            <span className="font-medium">Example:</span> sapo_app_secret_example
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="baseUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="integrations:sapo.connect.baseUrl">
                              API Base URL
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter the Sapo API Base URL"
                              data-testid="sapo-base-url-input"
                            />
                          </FormControl>
                          <FormDescription>
                            <Trans i18nKey="integrations:sapo.connect.baseUrlDescription">
                              The base URL for the Sapo API.
                            </Trans>
                          </FormDescription>
                          <p className="mt-1 text-xs text-muted-foreground">
                            <span className="font-medium">Default:</span> https://api.sapo.vn
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {testStatus === 'success' && (
                      <Alert className="border-green-200 bg-green-50">
                        <AlertTitle className="text-green-800">
                          <Trans i18nKey="integrations:sapo.connect.testSuccess">
                            Connection successful
                          </Trans>
                        </AlertTitle>
                        <AlertDescription className="text-green-700">
                          <Trans i18nKey="integrations:sapo.connect.testSuccessDescription">
                            Successfully connected to Sapo API.
                          </Trans>
                        </AlertDescription>
                      </Alert>
                    )}

                    {testStatus === 'error' && (
                      <Alert className="border-red-200 bg-red-50">
                        <AlertTitle className="text-red-800">
                          <Trans i18nKey="integrations:sapo.connect.testError">
                            Connection failed
                          </Trans>
                        </AlertTitle>
                        <AlertDescription className="text-red-700">
                          {testError ||
                            'Failed to connect to Sapo API. Please check your credentials.'}
                        </AlertDescription>
                      </Alert>
                    )}

                    <div className="flex justify-end space-x-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleTestConnection}
                        disabled={isSubmitting || testStatus === 'testing'}
                        data-testid="sapo-test-connection-button"
                      >
                        {testStatus === 'testing' ? (
                          <Trans i18nKey="integrations:sapo.connect.testing">
                            Testing...
                          </Trans>
                        ) : (
                          <Trans i18nKey="integrations:sapo.connect.testConnection">
                            Test Connection
                          </Trans>
                        )}
                      </Button>

                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        data-testid="sapo-connect-button"
                      >
                        {isSubmitting ? (
                          <Trans i18nKey="common:saving">Saving...</Trans>
                        ) : (
                          <Trans i18nKey="integrations:sapo.connect.connect">
                            Connect
                          </Trans>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </TabsContent>

              <TabsContent value="webhook" className="space-y-4 py-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">
                      <Trans i18nKey="integrations:sapo.connect.webhookSetup">
                        Webhook Setup
                      </Trans>
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      <Trans i18nKey="integrations:sapo.connect.webhookDescription">
                        Set up webhooks to receive real-time updates from Sapo.
                      </Trans>
                    </p>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="font-medium">
                      <Trans i18nKey="integrations:sapo.connect.webhookUrl">
                        Webhook URL
                      </Trans>
                    </h4>
                    <div className="flex items-center space-x-2">
                      <Input
                        readOnly
                        value={`${process.env.NEXT_PUBLIC_APP_URL}/api/integrations/webhook/sapo`}
                        className="bg-muted/50"
                        data-testid="sapo-webhook-url-input"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(
                            `${process.env.NEXT_PUBLIC_APP_URL}/api/integrations/webhook/sapo`,
                          );
                          toast.success('Copied to clipboard');
                        }}
                        data-testid="sapo-copy-webhook-url-button"
                      >
                        <Trans i18nKey="common:copy">Copy</Trans>
                      </Button>
                    </div>
                    <p className="text-muted-foreground text-xs">
                      <Trans i18nKey="integrations:sapo.connect.webhookUrlDescription">
                        Add this URL to your Sapo developer portal to receive
                        webhooks.
                      </Trans>
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">
                      <Trans i18nKey="integrations:sapo.connect.webhookEvents">
                        Webhook Events
                      </Trans>
                    </h4>
                    <p className="text-sm">
                      <Trans i18nKey="integrations:sapo.connect.webhookEventsDescription">
                        Subscribe to the following events in your Sapo developer
                        portal:
                      </Trans>
                    </p>
                    <ul className="list-disc pl-5 text-sm">
                      <li>product.created</li>
                      <li>product.updated</li>
                      <li>order.created</li>
                      <li>order.updated</li>
                      <li>order.status_changed</li>
                      <li>customer.created</li>
                      <li>customer.updated</li>
                    </ul>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-between border-t p-6">
            <Button
              variant="outline"
              onClick={() => router.push(`/home/<USER>/integrations`)}
              data-testid="sapo-cancel-button"
            >
              <Trans i18nKey="common:cancel">Cancel</Trans>
            </Button>
            <div className="text-muted-foreground text-xs">
              <Trans i18nKey="integrations:sapo.connect.footer">
                Need help? Check the{' '}
                <a
                  href="https://developers.sapo.vn/docs"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline"
                >
                  Sapo API documentation
                </a>
                .
              </Trans>
              <span className="block mt-1">
                <span className="font-medium">Note:</span> Example values are pre-filled for quick testing.
              </span>
            </div>
          </CardFooter>
        </Card>
      </PageBody>
    </>
  );
}
