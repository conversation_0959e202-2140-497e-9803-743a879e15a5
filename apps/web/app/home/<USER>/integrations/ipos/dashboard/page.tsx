'use client';

import { useEffect, useState } from 'react';

import { usePara<PERSON>, useRouter } from 'next/navigation';

import { formatDistanceToNow } from 'date-fns';
import {
  Activity,
  AlertCircle,
  ArrowRight,
  CheckCircle,
  Clock,
  Database,
  History,
  Info,
  Package,
  RefreshCw,
  Settings,
  ShoppingCart,
  Users,
  XCircle,
  Zap,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { isUUID } from '@kit/shared/utils';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { PageBody } from '@kit/ui/page';
import { Progress } from '@kit/ui/progress';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@kit/ui/tooltip';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { IPOSBreadcrumbs } from '../_components/ipos-breadcrumbs';
import { testIPOSConnection } from '../_lib/server/ipos-actions';

export default function IPOSDashboardPage() {
  const { account } = useParams();
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();

  const [isLoading, setIsLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<
    'connected' | 'disconnected' | 'error'
  >('disconnected');
  const [connectionDetails, setConnectionDetails] = useState<any>(null);
  const [lastSyncDate, setLastSyncDate] = useState<string | null>(null);
  const [syncStats, setSyncStats] = useState({
    products: {
      total: 0,
      synced: 0,
      failed: 0,
      lastSync: null as string | null,
    },
    orders: { total: 0, synced: 0, failed: 0, lastSync: null as string | null },
    customers: {
      total: 0,
      synced: 0,
      failed: 0,
      lastSync: null as string | null,
    },
    branches: {
      total: 0,
      synced: 0,
      failed: 0,
      lastSync: null as string | null,
    },
    categories: {
      total: 0,
      synced: 0,
      failed: 0,
      lastSync: null as string | null,
    },
    vouchers: {
      total: 0,
      synced: 0,
      failed: 0,
      lastSync: null as string | null,
    },
  });
  const [mappingStatus, setMappingStatus] = useState({
    products: false,
    orders: false,
    customers: false,
    branches: false,
    categories: false,
    vouchers: false,
  });
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [syncHistory, setSyncHistory] = useState<any[]>([]);
  const [healthStatus, setHealthStatus] = useState({
    apiLatency: 0,
    lastError: null as string | null,
    errorCount: 0,
    uptime: 100,
  });

  // Fetch integration data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Kiểm tra xem account có phải là UUID không
        let accountId = account;

        // Nếu không phải UUID, lấy ID từ slug
        if (!isUUID(account as string)) {
          const { data: accountData, error: accountError } = await supabase
            .from('accounts')
            .select('id')
            .eq('slug', account)
            .single();

          if (accountError) {
            toast.error(t('integrations:ipos.dashboard.accountNotFound'), {
              description: t('common:somethingWentWrong'),
            });
            router.push('/home');
            return;
          }

          accountId = accountData.id;
        }

        // Get integration details
        const { data: integration, error: integrationError } = await supabase
          .from('integrations')
          .select('*')
          .eq('account_id', accountId)
          .eq('type', 'ipos')
          .single();

        if (integrationError) {
          setConnectionStatus('disconnected');
          setIsLoading(false);
          return;
        }

        setConnectionDetails(integration);

        // Test connection
        const startTime = Date.now();
        try {
          await testIPOSConnection(
            account as string,
            integration.credentials || integration.config,
          );
          setConnectionStatus('connected');
          // Calculate API latency
          const endTime = Date.now();
          setHealthStatus((prev) => ({
            ...prev,
            apiLatency: endTime - startTime,
            uptime: 100,
          }));
        } catch (error: any) {
          setConnectionStatus('error');
          setHealthStatus((prev) => ({
            ...prev,
            lastError: error.message,
            errorCount: prev.errorCount + 1,
            uptime: 90, // Simulated value
          }));
        }

        // Get mapping status
        const { data: mappings, error: mappingsError } = await supabase
          .from('integration_mappings')
          .select('resource_type, is_active')
          .eq('integration_id', integration.id);

        if (!mappingsError && mappings) {
          // Create a map to count mappings per resource type
          const mappingCounts: Record<string, number> = {};

          // Count mappings for each resource type
          mappings.forEach((mapping) => {
            if (mapping.is_active) {
              mappingCounts[mapping.resource_type] =
                (mappingCounts[mapping.resource_type] || 0) + 1;
            }
          });

          // Update mapping status for all resource types
          const mappingStatusUpdate = {
            products: mappingCounts['products'] > 0,
            orders: mappingCounts['orders'] > 0,
            customers: mappingCounts['customers'] > 0,
            branches: mappingCounts['branches'] > 0,
            categories: mappingCounts['categories'] > 0,
            vouchers: mappingCounts['vouchers'] > 0,
          };

          setMappingStatus(mappingStatusUpdate);
        }

        // Get last sync info
        const { data: syncLogs, error: syncLogsError } = await supabase
          .from('integration_sync_logs')
          .select('*')
          .eq('integration_id', integration.id)
          .order('created_at', { ascending: false })
          .limit(10);

        if (!syncLogsError && syncLogs && syncLogs.length > 0) {
          setLastSyncDate(syncLogs[0].created_at);
          setSyncHistory(syncLogs);

          // Calculate sync stats
          const stats = {
            products: {
              total: 0,
              synced: 0,
              failed: 0,
              lastSync: null as string | null,
            },
            orders: {
              total: 0,
              synced: 0,
              failed: 0,
              lastSync: null as string | null,
            },
            customers: {
              total: 0,
              synced: 0,
              failed: 0,
              lastSync: null as string | null,
            },
            branches: {
              total: 0,
              synced: 0,
              failed: 0,
              lastSync: null as string | null,
            },
            categories: {
              total: 0,
              synced: 0,
              failed: 0,
              lastSync: null as string | null,
            },
            vouchers: {
              total: 0,
              synced: 0,
              failed: 0,
              lastSync: null as string | null,
            },
          };

          // Group logs by resource type to find the latest sync for each type
          const latestSyncByType: Record<string, any> = {};

          syncLogs.forEach((log) => {
            if (log.resource_type in stats) {
              const resourceType = log.resource_type as keyof typeof stats;
              stats[resourceType].total += log.items_processed || 0;
              stats[resourceType].synced += log.items_created || 0;
              stats[resourceType].failed += log.items_failed || 0;

              // Track the latest sync for each resource type
              if (
                !latestSyncByType[resourceType] ||
                new Date(log.created_at) >
                  new Date(latestSyncByType[resourceType].created_at)
              ) {
                latestSyncByType[resourceType] = log;
                stats[resourceType].lastSync = log.created_at;
              }
            }
          });

          setSyncStats(stats);
        }
      } catch (error) {
        console.error('Error fetching integration data:', error);
        toast.error(t('common:somethingWentWrong'));
      } finally {
        setIsLoading(false);
      }
    };

    if (account) {
      fetchData();
    }
  }, [account, supabase, t]);

  // Test connection
  const handleTestConnection = async () => {
    if (!connectionDetails) return;

    setIsTestingConnection(true);
    const startTime = Date.now();
    try {
      await testIPOSConnection(
        account as string,
        connectionDetails.credentials || connectionDetails.config,
      );
      setConnectionStatus('connected');

      // Calculate API latency
      const endTime = Date.now();
      const latency = endTime - startTime;

      setHealthStatus((prev) => ({
        ...prev,
        apiLatency: latency,
        uptime: 100,
      }));

      toast.success(t('integrations:ipos.dashboard.connectionSuccess'), {
        description: `${t('integrations:ipos.dashboard.apiLatency')}: ${latency}ms`,
      });
    } catch (error: any) {
      setConnectionStatus('error');

      setHealthStatus((prev) => ({
        ...prev,
        lastError: error.message,
        errorCount: prev.errorCount + 1,
        uptime: Math.max(0, prev.uptime - 10), // Decrease uptime on errors
      }));

      toast.error(t('integrations:ipos.dashboard.connectionError'), {
        description: error.message || t('common:somethingWentWrong'),
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={
          <div className="flex items-center gap-2">
            <img
              src="/images/ipos-logo.png"
              alt="iPOS Logo"
              className="h-6 w-6"
            />
            <Trans i18nKey="integrations:ipos.dashboard.title">
              iPOS Dashboard
            </Trans>
          </div>
        }
        description={<IPOSBreadcrumbs accountSlug={account as string} />}
        account={account as string}
        actions={
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/home/<USER>/integrations`)}
            >
              <ArrowRight className="mr-1 h-4 w-4 rotate-180" />
              <Trans i18nKey="integrations:ipos.dashboard.backToIntegrations">
                Back to Integrations
              </Trans>
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={() =>
                router.push(`/home/<USER>/integrations/ipos/sync`)
              }
              disabled={!connectionDetails || connectionStatus !== 'connected'}
            >
              <Database className="mr-1 h-4 w-4" />
              <Trans i18nKey="integrations:ipos.dashboard.syncNow">
                Sync Now
              </Trans>
            </Button>
          </div>
        }
      />

      <PageBody>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
          {/* Connection Status Card */}
          <Card className="col-span-1 overflow-hidden border-0 shadow-md lg:col-span-1">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 pb-2 dark:from-blue-950/30 dark:to-indigo-950/30">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Activity className="h-5 w-5 text-blue-500" />
                  <Trans i18nKey="integrations:ipos.dashboard.connectionStatus">
                    Connection Status
                  </Trans>
                </CardTitle>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge
                        variant={
                          connectionStatus === 'connected'
                            ? 'success'
                            : connectionStatus === 'error'
                              ? 'destructive'
                              : 'outline'
                        }
                        className="flex h-6 items-center gap-1 px-2"
                      >
                        {connectionStatus === 'connected' && (
                          <CheckCircle className="h-3 w-3" />
                        )}
                        {connectionStatus === 'error' && (
                          <XCircle className="h-3 w-3" />
                        )}
                        {connectionStatus === 'disconnected' && (
                          <AlertCircle className="h-3 w-3" />
                        )}
                        <span>
                          {connectionStatus === 'connected' &&
                            t('integrations:ipos.dashboard.connected')}
                          {connectionStatus === 'error' &&
                            t('integrations:ipos.dashboard.connectionIssue')}
                          {connectionStatus === 'disconnected' &&
                            t('integrations:ipos.dashboard.notConnected')}
                        </span>
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        {connectionStatus === 'connected' &&
                          t('integrations:ipos.dashboard.connectionHealthy')}
                        {connectionStatus === 'error' &&
                          t('integrations:ipos.dashboard.connectionUnhealthy')}
                        {connectionStatus === 'disconnected' &&
                          t(
                            'integrations:ipos.dashboard.connectionNotEstablished',
                          )}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              {isLoading ? (
                <div className="flex h-24 items-center justify-center">
                  <div className="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Connection Health Metrics */}
                  <div className="mb-4 grid grid-cols-2 gap-3">
                    <div className="flex flex-col rounded-lg bg-blue-50 p-3 dark:bg-blue-950/20">
                      <span className="text-muted-foreground mb-1 text-xs">
                        API Latency
                      </span>
                      <span className="text-xl font-semibold">
                        {healthStatus.apiLatency}ms
                      </span>
                    </div>
                    <div className="flex flex-col rounded-lg bg-green-50 p-3 dark:bg-green-950/20">
                      <span className="text-muted-foreground mb-1 text-xs">
                        Uptime
                      </span>
                      <span className="text-xl font-semibold">
                        {healthStatus.uptime}%
                      </span>
                    </div>
                  </div>

                  {connectionDetails && (
                    <div className="space-y-3 rounded-lg border p-3">
                      <div className="flex items-center justify-between">
                        <span className="flex items-center gap-1 text-sm font-medium">
                          <Info className="text-muted-foreground h-3.5 w-3.5" />
                          POS ID
                        </span>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="bg-muted rounded px-1.5 py-0.5 font-mono text-sm">
                                {connectionDetails.credentials?.pos_id ||
                                  connectionDetails.config?.pos_id ||
                                  'N/A'}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent className="w-80 p-4">
                              <div className="space-y-1">
                                <h4 className="text-sm font-semibold">
                                  POS ID
                                </h4>
                                <p className="text-sm">
                                  Unique identifier for your iPOS store.
                                </p>
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="flex items-center gap-1 text-sm font-medium">
                          <Info className="text-muted-foreground h-3.5 w-3.5" />
                          Brand
                        </span>
                        <span className="bg-muted rounded px-1.5 py-0.5 font-mono text-sm">
                          {connectionDetails.credentials?.pos_parent ||
                            connectionDetails.config?.pos_parent ||
                            'N/A'}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="flex items-center gap-1 text-sm font-medium">
                          <Info className="text-muted-foreground h-3.5 w-3.5" />
                          API URL
                        </span>
                        <span className="bg-muted max-w-[150px] truncate rounded px-1.5 py-0.5 font-mono text-sm">
                          {connectionDetails.credentials?.baseUrl ||
                            connectionDetails.config?.baseUrl ||
                            'https://api.foodbook.vn'}
                        </span>
                      </div>

                      {lastSyncDate && (
                        <div className="flex items-center justify-between">
                          <span className="flex items-center gap-1 text-sm font-medium">
                            <Clock className="text-muted-foreground h-3.5 w-3.5" />
                            Last Sync
                          </span>
                          <span className="text-sm">
                            {formatDistanceToNow(new Date(lastSyncDate), {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                  {healthStatus.lastError && (
                    <div className="rounded-lg border border-red-200 bg-red-50 p-3 text-sm dark:border-red-800 dark:bg-red-950/20">
                      <div className="mb-1 flex items-center gap-1 font-medium text-red-800 dark:text-red-300">
                        <AlertCircle className="h-4 w-4" />
                        Last Error
                      </div>
                      <p className="text-xs text-red-700 dark:text-red-400">
                        {healthStatus.lastError}
                      </p>
                    </div>
                  )}

                  <div className="flex justify-between gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        router.push(
                          `/home/<USER>/integrations/ipos/connect`,
                        )
                      }
                      className="flex-1"
                    >
                      <Settings className="mr-1 h-4 w-4" />
                      <Trans i18nKey="integrations:ipos.dashboard.configure">
                        Configure
                      </Trans>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleTestConnection}
                      disabled={isTestingConnection || !connectionDetails}
                      className="flex-1"
                    >
                      <RefreshCw
                        className={`mr-1 h-4 w-4 ${isTestingConnection ? 'animate-spin' : ''}`}
                      />
                      <Trans i18nKey="integrations:ipos.dashboard.testConnection">
                        Test
                      </Trans>
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Mapping Status Card */}
          <Card className="col-span-1 overflow-hidden border-0 shadow-md lg:col-span-2">
            <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 pb-2 dark:from-purple-950/30 dark:to-pink-950/30">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Settings className="h-5 w-5 text-purple-500" />
                  <Trans i18nKey="integrations:ipos.dashboard.mappingStatus">
                    Field Mapping Status
                  </Trans>
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    router.push(`/home/<USER>/integrations/ipos/mapping`)
                  }
                  className="h-8"
                >
                  <Trans i18nKey="integrations:ipos.dashboard.viewAllMappings">
                    View All
                  </Trans>
                  <ArrowRight className="ml-1 h-3.5 w-3.5" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              {isLoading ? (
                <div className="flex h-24 items-center justify-center">
                  <div className="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    {/* Products Card */}
                    <div className="relative flex h-full flex-col rounded-xl border bg-gradient-to-br from-white to-purple-50 p-4 shadow-sm dark:from-gray-950 dark:to-purple-950/20">
                      <div className="absolute top-0 right-0 h-16 w-16 opacity-10">
                        <Package className="h-full w-full text-purple-500" />
                      </div>
                      <div className="mb-3 flex items-start justify-between">
                        <h3 className="flex items-center gap-1.5 font-medium">
                          <Package className="h-4 w-4 text-purple-500" />
                          <Trans i18nKey="integrations:ipos.dashboard.products">
                            Products
                          </Trans>
                        </h3>
                        {mappingStatus.products ? (
                          <Badge
                            variant="success"
                            className="flex items-center gap-1"
                          >
                            <CheckCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.configured">
                              Configured
                            </Trans>
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="flex items-center gap-1"
                          >
                            <AlertCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.notConfigured">
                              Not Configured
                            </Trans>
                          </Badge>
                        )}
                      </div>

                      <div className="mb-3 space-y-2">
                        {syncStats.products.total > 0 ? (
                          <>
                            <div className="flex justify-between text-xs">
                              <span>
                                <Trans i18nKey="integrations:ipos.dashboard.syncedItems">
                                  Synced
                                </Trans>
                              </span>
                              <span>
                                {syncStats.products.synced} /{' '}
                                {syncStats.products.total}
                              </span>
                            </div>
                            <Progress
                              value={
                                (syncStats.products.synced /
                                  syncStats.products.total) *
                                100
                              }
                              className="h-1.5"
                            />
                            {syncStats.products.lastSync && (
                              <div className="text-muted-foreground mt-1 text-xs">
                                Last sync:{' '}
                                {formatDistanceToNow(
                                  new Date(syncStats.products.lastSync),
                                  { addSuffix: true },
                                )}
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="text-muted-foreground text-xs">
                            <Trans i18nKey="integrations:ipos.dashboard.noSyncYet">
                              No data synced yet
                            </Trans>
                          </div>
                        )}
                      </div>

                      <Button
                        variant="secondary"
                        size="sm"
                        className="mt-auto h-8 w-full text-xs"
                        onClick={() =>
                          router.push(
                            `/home/<USER>/integrations/ipos/mapping?resource=products`,
                          )
                        }
                      >
                        <div className="flex w-full items-center justify-center">
                          <span className="truncate">
                            {mappingStatus.products ? (
                              <Trans i18nKey="integrations:ipos.dashboard.editMapping">
                                Edit Mapping
                              </Trans>
                            ) : (
                              <Trans i18nKey="integrations:ipos.dashboard.setupMapping">
                                Setup Mapping
                              </Trans>
                            )}
                          </span>
                          <ArrowRight className="ml-1 h-3 w-3 flex-shrink-0" />
                        </div>
                      </Button>
                    </div>

                    {/* Orders Card */}
                    <div className="relative flex h-full flex-col rounded-xl border bg-gradient-to-br from-white to-blue-50 p-4 shadow-sm dark:from-gray-950 dark:to-blue-950/20">
                      <div className="absolute top-0 right-0 h-16 w-16 opacity-10">
                        <ShoppingCart className="h-full w-full text-blue-500" />
                      </div>
                      <div className="mb-3 flex items-start justify-between">
                        <h3 className="flex items-center gap-1.5 font-medium">
                          <ShoppingCart className="h-4 w-4 text-blue-500" />
                          <Trans i18nKey="integrations:ipos.dashboard.orders">
                            Orders
                          </Trans>
                        </h3>
                        {mappingStatus.orders ? (
                          <Badge
                            variant="success"
                            className="flex items-center gap-1"
                          >
                            <CheckCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.configured">
                              Configured
                            </Trans>
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="flex items-center gap-1"
                          >
                            <AlertCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.notConfigured">
                              Not Configured
                            </Trans>
                          </Badge>
                        )}
                      </div>

                      <div className="mb-3 space-y-2">
                        {syncStats.orders.total > 0 ? (
                          <>
                            <div className="flex justify-between text-xs">
                              <span>
                                <Trans i18nKey="integrations:ipos.dashboard.syncedItems">
                                  Synced
                                </Trans>
                              </span>
                              <span>
                                {syncStats.orders.synced} /{' '}
                                {syncStats.orders.total}
                              </span>
                            </div>
                            <Progress
                              value={
                                (syncStats.orders.synced /
                                  syncStats.orders.total) *
                                100
                              }
                              className="h-1.5"
                            />
                            {syncStats.orders.lastSync && (
                              <div className="text-muted-foreground mt-1 text-xs">
                                Last sync:{' '}
                                {formatDistanceToNow(
                                  new Date(syncStats.orders.lastSync),
                                  { addSuffix: true },
                                )}
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="text-muted-foreground text-xs">
                            <Trans i18nKey="integrations:ipos.dashboard.noSyncYet">
                              No data synced yet
                            </Trans>
                          </div>
                        )}
                      </div>

                      <Button
                        variant="secondary"
                        size="sm"
                        className="mt-auto h-8 w-full text-xs"
                        onClick={() =>
                          router.push(
                            `/home/<USER>/integrations/ipos/mapping?resource=orders`,
                          )
                        }
                      >
                        <div className="flex w-full items-center justify-center">
                          <span className="truncate">
                            {mappingStatus.orders ? (
                              <Trans i18nKey="integrations:ipos.dashboard.editMapping">
                                Edit Mapping
                              </Trans>
                            ) : (
                              <Trans i18nKey="integrations:ipos.dashboard.setupMapping">
                                Setup Mapping
                              </Trans>
                            )}
                          </span>
                          <ArrowRight className="ml-1 h-3 w-3 flex-shrink-0" />
                        </div>
                      </Button>
                    </div>

                    {/* Customers Card */}
                    <div className="relative flex h-full flex-col rounded-xl border bg-gradient-to-br from-white to-green-50 p-4 shadow-sm dark:from-gray-950 dark:to-green-950/20">
                      <div className="absolute top-0 right-0 h-16 w-16 opacity-10">
                        <Users className="h-full w-full text-green-500" />
                      </div>
                      <div className="mb-3 flex items-start justify-between">
                        <h3 className="flex items-center gap-1.5 font-medium">
                          <Users className="h-4 w-4 text-green-500" />
                          <Trans i18nKey="integrations:ipos.dashboard.customers">
                            Customers
                          </Trans>
                        </h3>
                        {mappingStatus.customers ? (
                          <Badge
                            variant="success"
                            className="flex items-center gap-1"
                          >
                            <CheckCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.configured">
                              Configured
                            </Trans>
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="flex items-center gap-1"
                          >
                            <AlertCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.notConfigured">
                              Not Configured
                            </Trans>
                          </Badge>
                        )}
                      </div>

                      <div className="mb-3 space-y-2">
                        {syncStats.customers.total > 0 ? (
                          <>
                            <div className="flex justify-between text-xs">
                              <span>
                                <Trans i18nKey="integrations:ipos.dashboard.syncedItems">
                                  Synced
                                </Trans>
                              </span>
                              <span>
                                {syncStats.customers.synced} /{' '}
                                {syncStats.customers.total}
                              </span>
                            </div>
                            <Progress
                              value={
                                (syncStats.customers.synced /
                                  syncStats.customers.total) *
                                100
                              }
                              className="h-1.5"
                            />
                            {syncStats.customers.lastSync && (
                              <div className="text-muted-foreground mt-1 text-xs">
                                Last sync:{' '}
                                {formatDistanceToNow(
                                  new Date(syncStats.customers.lastSync),
                                  { addSuffix: true },
                                )}
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="text-muted-foreground text-xs">
                            <Trans i18nKey="integrations:ipos.dashboard.noSyncYet">
                              No data synced yet
                            </Trans>
                          </div>
                        )}
                      </div>

                      <Button
                        variant="secondary"
                        size="sm"
                        className="mt-auto h-8 w-full text-xs"
                        onClick={() =>
                          router.push(
                            `/home/<USER>/integrations/ipos/mapping?resource=customers`,
                          )
                        }
                      >
                        <div className="flex w-full items-center justify-center">
                          <span className="truncate">
                            {mappingStatus.customers ? (
                              <Trans i18nKey="integrations:ipos.dashboard.editMapping">
                                Edit Mapping
                              </Trans>
                            ) : (
                              <Trans i18nKey="integrations:ipos.dashboard.setupMapping">
                                Setup Mapping
                              </Trans>
                            )}
                          </span>
                          <ArrowRight className="ml-1 h-3 w-3 flex-shrink-0" />
                        </div>
                      </Button>
                    </div>

                    {/* Branches Card */}
                    <div className="relative flex h-full flex-col rounded-xl border bg-gradient-to-br from-white to-amber-50 p-4 shadow-sm dark:from-gray-950 dark:to-amber-950/20">
                      <div className="absolute top-0 right-0 h-16 w-16 opacity-10">
                        <Database className="h-full w-full text-amber-500" />
                      </div>
                      <div className="mb-3 flex items-start justify-between">
                        <h3 className="flex items-center gap-1.5 font-medium">
                          <Database className="h-4 w-4 text-amber-500" />
                          <Trans i18nKey="integrations:ipos.dashboard.branches">
                            Branches
                          </Trans>
                        </h3>
                        {mappingStatus.branches ? (
                          <Badge
                            variant="success"
                            className="flex items-center gap-1"
                          >
                            <CheckCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.configured">
                              Configured
                            </Trans>
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="flex items-center gap-1"
                          >
                            <AlertCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.notConfigured">
                              Not Configured
                            </Trans>
                          </Badge>
                        )}
                      </div>

                      <div className="mb-3 space-y-2">
                        {syncStats.branches.total > 0 ? (
                          <>
                            <div className="flex justify-between text-xs">
                              <span>
                                <Trans i18nKey="integrations:ipos.dashboard.syncedItems">
                                  Synced
                                </Trans>
                              </span>
                              <span>
                                {syncStats.branches.synced} /{' '}
                                {syncStats.branches.total}
                              </span>
                            </div>
                            <Progress
                              value={
                                (syncStats.branches.synced /
                                  syncStats.branches.total) *
                                100
                              }
                              className="h-1.5"
                            />
                            {syncStats.branches.lastSync && (
                              <div className="text-muted-foreground mt-1 text-xs">
                                Last sync:{' '}
                                {formatDistanceToNow(
                                  new Date(syncStats.branches.lastSync),
                                  { addSuffix: true },
                                )}
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="text-muted-foreground text-xs">
                            <Trans i18nKey="integrations:ipos.dashboard.noSyncYet">
                              No data synced yet
                            </Trans>
                          </div>
                        )}
                      </div>

                      <Button
                        variant="secondary"
                        size="sm"
                        className="mt-auto h-8 w-full text-xs"
                        onClick={() =>
                          router.push(
                            `/home/<USER>/integrations/ipos/mapping?resource=branches`,
                          )
                        }
                      >
                        <div className="flex w-full items-center justify-center">
                          <span className="truncate">
                            {mappingStatus.branches ? (
                              <Trans i18nKey="integrations:ipos.dashboard.editMapping">
                                Edit Mapping
                              </Trans>
                            ) : (
                              <Trans i18nKey="integrations:ipos.dashboard.setupMapping">
                                Setup Mapping
                              </Trans>
                            )}
                          </span>
                          <ArrowRight className="ml-1 h-3 w-3 flex-shrink-0" />
                        </div>
                      </Button>
                    </div>

                    {/* Categories Card */}
                    <div className="relative flex h-full flex-col rounded-xl border bg-gradient-to-br from-white to-cyan-50 p-4 shadow-sm dark:from-gray-950 dark:to-cyan-950/20">
                      <div className="absolute top-0 right-0 h-16 w-16 opacity-10">
                        <Zap className="h-full w-full text-cyan-500" />
                      </div>
                      <div className="mb-3 flex items-start justify-between">
                        <h3 className="flex items-center gap-1.5 font-medium">
                          <Zap className="h-4 w-4 text-cyan-500" />
                          <Trans i18nKey="integrations:ipos.dashboard.categories">
                            Categories
                          </Trans>
                        </h3>
                        {mappingStatus.categories ? (
                          <Badge
                            variant="success"
                            className="flex items-center gap-1"
                          >
                            <CheckCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.configured">
                              Configured
                            </Trans>
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="flex items-center gap-1"
                          >
                            <AlertCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.notConfigured">
                              Not Configured
                            </Trans>
                          </Badge>
                        )}
                      </div>

                      <div className="mb-3 space-y-2">
                        {syncStats.categories.total > 0 ? (
                          <>
                            <div className="flex justify-between text-xs">
                              <span>
                                <Trans i18nKey="integrations:ipos.dashboard.syncedItems">
                                  Synced
                                </Trans>
                              </span>
                              <span>
                                {syncStats.categories.synced} /{' '}
                                {syncStats.categories.total}
                              </span>
                            </div>
                            <Progress
                              value={
                                (syncStats.categories.synced /
                                  syncStats.categories.total) *
                                100
                              }
                              className="h-1.5"
                            />
                            {syncStats.categories.lastSync && (
                              <div className="text-muted-foreground mt-1 text-xs">
                                Last sync:{' '}
                                {formatDistanceToNow(
                                  new Date(syncStats.categories.lastSync),
                                  { addSuffix: true },
                                )}
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="text-muted-foreground text-xs">
                            <Trans i18nKey="integrations:ipos.dashboard.noSyncYet">
                              No data synced yet
                            </Trans>
                          </div>
                        )}
                      </div>

                      <Button
                        variant="secondary"
                        size="sm"
                        className="mt-auto h-8 w-full text-xs"
                        onClick={() =>
                          router.push(
                            `/home/<USER>/integrations/ipos/mapping?resource=categories`,
                          )
                        }
                      >
                        <div className="flex w-full items-center justify-center">
                          <span className="truncate">
                            {mappingStatus.categories ? (
                              <Trans i18nKey="integrations:ipos.dashboard.editMapping">
                                Edit Mapping
                              </Trans>
                            ) : (
                              <Trans i18nKey="integrations:ipos.dashboard.setupMapping">
                                Setup Mapping
                              </Trans>
                            )}
                          </span>
                          <ArrowRight className="ml-1 h-3 w-3 flex-shrink-0" />
                        </div>
                      </Button>
                    </div>

                    {/* Vouchers Card */}
                    <div className="relative flex h-full flex-col rounded-xl border bg-gradient-to-br from-white to-pink-50 p-4 shadow-sm dark:from-gray-950 dark:to-pink-950/20">
                      <div className="absolute top-0 right-0 h-16 w-16 opacity-10">
                        <Activity className="h-full w-full text-pink-500" />
                      </div>
                      <div className="mb-3 flex items-start justify-between">
                        <h3 className="flex items-center gap-1.5 font-medium">
                          <Activity className="h-4 w-4 text-pink-500" />
                          <Trans i18nKey="integrations:ipos.dashboard.vouchers">
                            Vouchers
                          </Trans>
                        </h3>
                        {mappingStatus.vouchers ? (
                          <Badge
                            variant="success"
                            className="flex items-center gap-1"
                          >
                            <CheckCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.configured">
                              Configured
                            </Trans>
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="flex items-center gap-1"
                          >
                            <AlertCircle className="h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.dashboard.notConfigured">
                              Not Configured
                            </Trans>
                          </Badge>
                        )}
                      </div>

                      <div className="mb-3 space-y-2">
                        {syncStats.vouchers.total > 0 ? (
                          <>
                            <div className="flex justify-between text-xs">
                              <span>
                                <Trans i18nKey="integrations:ipos.dashboard.syncedItems">
                                  Synced
                                </Trans>
                              </span>
                              <span>
                                {syncStats.vouchers.synced} /{' '}
                                {syncStats.vouchers.total}
                              </span>
                            </div>
                            <Progress
                              value={
                                (syncStats.vouchers.synced /
                                  syncStats.vouchers.total) *
                                100
                              }
                              className="h-1.5"
                            />
                            {syncStats.vouchers.lastSync && (
                              <div className="text-muted-foreground mt-1 text-xs">
                                Last sync:{' '}
                                {formatDistanceToNow(
                                  new Date(syncStats.vouchers.lastSync),
                                  { addSuffix: true },
                                )}
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="text-muted-foreground text-xs">
                            <Trans i18nKey="integrations:ipos.dashboard.noSyncYet">
                              No data synced yet
                            </Trans>
                          </div>
                        )}
                      </div>

                      <Button
                        variant="secondary"
                        size="sm"
                        className="mt-auto h-8 w-full text-xs"
                        onClick={() =>
                          router.push(
                            `/home/<USER>/integrations/ipos/mapping?resource=vouchers`,
                          )
                        }
                      >
                        <div className="flex w-full items-center justify-center">
                          <span className="truncate">
                            {mappingStatus.vouchers ? (
                              <Trans i18nKey="integrations:ipos.dashboard.editMapping">
                                Edit Mapping
                              </Trans>
                            ) : (
                              <Trans i18nKey="integrations:ipos.dashboard.setupMapping">
                                Setup Mapping
                              </Trans>
                            )}
                          </span>
                          <ArrowRight className="ml-1 h-3 w-3 flex-shrink-0" />
                        </div>
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Sync History Card */}
          <Card className="col-span-1 overflow-hidden border-0 shadow-md lg:col-span-1">
            <CardHeader className="bg-gradient-to-r from-amber-50 to-yellow-50 pb-2 dark:from-amber-950/30 dark:to-yellow-950/30">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <History className="h-5 w-5 text-amber-500" />
                  <Trans i18nKey="integrations:ipos.dashboard.syncHistory">
                    Sync History
                  </Trans>
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    router.push(
                      `/home/<USER>/integrations/ipos/sync-history`,
                    )
                  }
                  className="h-8"
                  disabled={!connectionDetails}
                >
                  <Trans i18nKey="integrations:ipos.dashboard.viewAll">
                    View All
                  </Trans>
                  <ArrowRight className="ml-1 h-3.5 w-3.5" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              {isLoading ? (
                <div className="flex h-24 items-center justify-center">
                  <div className="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div>
                </div>
              ) : (
                <div className="space-y-4">
                  {syncHistory.length > 0 ? (
                    <div className="space-y-3">
                      {syncHistory.slice(0, 4).map((log, index) => (
                        <div
                          key={log.id}
                          className="flex items-center justify-between border-b pb-2 last:border-0 last:pb-0"
                        >
                          <div className="flex items-center gap-2">
                            <div className="flex-shrink-0">
                              {log.resource_type === 'products' && (
                                <Package className="h-4 w-4 text-purple-500" />
                              )}
                              {log.resource_type === 'orders' && (
                                <ShoppingCart className="h-4 w-4 text-blue-500" />
                              )}
                              {log.resource_type === 'customers' && (
                                <Users className="h-4 w-4 text-green-500" />
                              )}
                            </div>
                            <div>
                              <div className="text-sm font-medium capitalize">
                                {log.resource_type}
                              </div>
                              <div className="text-muted-foreground text-xs">
                                {formatDistanceToNow(new Date(log.created_at), {
                                  addSuffix: true,
                                })}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge
                              variant={
                                log.status === 'success'
                                  ? 'success'
                                  : log.status === 'error'
                                    ? 'destructive'
                                    : 'outline'
                              }
                              className="text-xs"
                            >
                              {log.status}
                            </Badge>
                            <div className="mt-1 text-xs">
                              {log.items_created} / {log.items_processed} items
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-muted-foreground py-6 text-center">
                      <History className="mx-auto mb-2 h-10 w-10 opacity-20" />
                      <p className="text-sm">
                        <Trans i18nKey="integrations:ipos.dashboard.noSyncHistoryYet">
                          No synchronization history yet
                        </Trans>
                      </p>
                    </div>
                  )}

                  <Button
                    variant="default"
                    className="w-full"
                    onClick={() =>
                      router.push(`/home/<USER>/integrations/ipos/sync`)
                    }
                    disabled={
                      !connectionDetails || connectionStatus !== 'connected'
                    }
                  >
                    <Zap className="mr-2 h-4 w-4" />
                    <Trans i18nKey="integrations:ipos.dashboard.syncNow">
                      Sync Now
                    </Trans>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions Card */}
          <Card className="col-span-1 mt-6 overflow-hidden border-0 shadow-md lg:col-span-4">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-slate-50 pb-2 dark:from-gray-950/50 dark:to-slate-950/50">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Zap className="h-5 w-5 text-slate-500" />
                <Trans i18nKey="integrations:ipos.dashboard.quickActions">
                  Quick Actions
                </Trans>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                <Button
                  variant="outline"
                  className="flex h-auto w-full flex-col items-center justify-center gap-2 border bg-gradient-to-br from-white to-blue-50 py-6 shadow-sm dark:from-gray-950 dark:to-blue-950/20"
                  onClick={() =>
                    router.push(`/home/<USER>/integrations/ipos/sync`)
                  }
                  disabled={
                    !connectionDetails || connectionStatus !== 'connected'
                  }
                >
                  <Database className="h-6 w-6 flex-shrink-0 text-blue-500" />
                  <span className="px-2 text-center font-medium">
                    <Trans i18nKey="integrations:ipos.dashboard.syncData">
                      Sync Data
                    </Trans>
                  </span>
                  <span
                    className="text-muted-foreground px-2 text-center text-xs break-words hyphens-auto"
                    style={{ wordBreak: 'break-word' }}
                  >
                    <Trans i18nKey="integrations:ipos.dashboard.syncDataDescription">
                      Import products, orders, and customers from iPOS
                    </Trans>
                  </span>
                </Button>

                <Button
                  variant="outline"
                  className="flex h-auto w-full flex-col items-center justify-center gap-2 border bg-gradient-to-br from-white to-purple-50 py-6 shadow-sm dark:from-gray-950 dark:to-purple-950/20"
                  onClick={() =>
                    router.push(`/home/<USER>/integrations/ipos/mapping`)
                  }
                  disabled={!connectionDetails}
                >
                  <Settings className="h-6 w-6 flex-shrink-0 text-purple-500" />
                  <span className="px-2 text-center font-medium">
                    <Trans i18nKey="integrations:ipos.dashboard.configureMapping">
                      Configure Mapping
                    </Trans>
                  </span>
                  <span
                    className="text-muted-foreground px-2 text-center text-xs break-words hyphens-auto"
                    style={{ wordBreak: 'break-word' }}
                  >
                    <Trans i18nKey="integrations:ipos.dashboard.configureMappingDescription">
                      Set up field mappings between iPOS and your system
                    </Trans>
                  </span>
                </Button>

                <Button
                  variant="outline"
                  className="flex h-auto w-full flex-col items-center justify-center gap-2 border bg-gradient-to-br from-white to-amber-50 py-6 shadow-sm dark:from-gray-950 dark:to-amber-950/20"
                  onClick={() =>
                    router.push(
                      `/home/<USER>/integrations/ipos/sync-history`,
                    )
                  }
                  disabled={!connectionDetails}
                >
                  <History className="h-6 w-6 flex-shrink-0 text-amber-500" />
                  <span className="px-2 text-center font-medium">
                    <Trans i18nKey="integrations:ipos.dashboard.viewSyncHistory">
                      View Sync History
                    </Trans>
                  </span>
                  <span
                    className="text-muted-foreground px-2 text-center text-xs break-words hyphens-auto"
                    style={{ wordBreak: 'break-word' }}
                  >
                    <Trans i18nKey="integrations:ipos.dashboard.viewSyncHistoryDescription">
                      Check past synchronization results and logs
                    </Trans>
                  </span>
                </Button>

                <Button
                  variant="outline"
                  className="flex h-auto w-full flex-col items-center justify-center gap-2 border bg-gradient-to-br from-white to-green-50 py-6 shadow-sm dark:from-gray-950 dark:to-green-950/20"
                  onClick={() =>
                    router.push(`/home/<USER>/integrations/ipos/connect`)
                  }
                  disabled={!connectionDetails}
                >
                  <Settings className="h-6 w-6 flex-shrink-0 text-green-500" />
                  <span className="px-2 text-center font-medium">
                    <Trans i18nKey="integrations:ipos.dashboard.configureConnection">
                      Configure Connection
                    </Trans>
                  </span>
                  <span
                    className="text-muted-foreground px-2 text-center text-xs break-words hyphens-auto"
                    style={{ wordBreak: 'break-word' }}
                  >
                    <Trans i18nKey="integrations:ipos.dashboard.configureConnectionDescription">
                      Update your iPOS connection settings and credentials
                    </Trans>
                  </span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageBody>
    </>
  );
}
