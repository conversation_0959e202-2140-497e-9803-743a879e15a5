'use client';

import { useEffect, useState } from 'react';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import { Separator } from '@kit/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { IPOSBreadcrumbs } from '../_components/ipos-breadcrumbs';
import {
  connectIPOSIntegration,
  testIPOSConnection,
} from '../_lib/server/ipos-actions';

// Schema cho form kết nối iPOS
const iPOSConnectionSchema = z.object({
  access_token: z.string().min(1, { message: 'Access Token is required' }),
  pos_parent: z.string().min(1, { message: 'POS Parent is required' }),
  pos_id: z.string().min(1, { message: 'POS ID is required' }),
  baseUrl: z.string().url({ message: 'Please enter a valid URL' }).optional(),
});

type IPOSConnectionFormValues = z.infer<typeof iPOSConnectionSchema>;

export default function IPOSConnectPage() {
  const { account } = useParams();
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [testStatus, setTestStatus] = useState<
    'idle' | 'testing' | 'success' | 'error'
  >('idle');
  const [testError, setTestError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [existingConfig, setExistingConfig] =
    useState<IPOSConnectionFormValues | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check for existing integration
  useEffect(() => {
    const checkExistingIntegration = async () => {
      setIsLoading(true);
      try {
        const { data: integration, error } = await supabase
          .from('integrations')
          .select('config')
          .eq('account_id', account)
          .eq('type', 'ipos')
          .single();

        if (!error && integration?.config) {
          setExistingConfig(integration.config as IPOSConnectionFormValues);
        }
      } catch (error) {
        console.error('Error checking existing integration:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (account) {
      checkExistingIntegration();
    }
  }, [account, supabase]);

  // Form với validation và giá trị mặc định từ tài liệu API
  const form = useForm<IPOSConnectionFormValues>({
    resolver: zodResolver(iPOSConnectionSchema),
    defaultValues: existingConfig || {
      access_token: 'JHTHWPCE6OCZBW0PBH9XRRBC6JTR1UWQ', // Mã cung cấp cho từng partner
      pos_parent: 'SAOBANG', // ID thương hiệu
      pos_id: '3160', // ID điểm bán hàng
      baseUrl: 'https://api.foodbook.vn',
    },
  });

  // Update form values when existingConfig changes
  useEffect(() => {
    if (existingConfig) {
      form.reset(existingConfig);
    }
  }, [existingConfig, form]);

  // Xử lý khi submit form
  const onSubmit = async (values: IPOSConnectionFormValues) => {
    setIsSubmitting(true);
    try {
      await connectIPOSIntegration(account as string, values);
      toast.success(t('integrations:ipos.connect.success'), {
        description: t('integrations:ipos.connect.successDescription'),
      });

      // Revalidate the integrations page to update the card status
      await fetch(`/api/revalidate?path=/home/<USER>/integrations`, {
        method: 'POST',
      });

      // Đợi một chút trước khi chuyển hướng
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Redirect to dashboard
      router.push(`/home/<USER>/integrations/ipos/dashboard`);
    } catch (error: any) {
      toast.error(t('integrations:ipos.connect.error'), {
        description: error.message || t('common:somethingWentWrong'),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý khi test kết nối
  const handleTestConnection = async () => {
    const values = form.getValues();
    const isValid = await form.trigger();

    if (!isValid) {
      return;
    }

    setTestStatus('testing');
    setTestError(null);

    try {
      await testIPOSConnection(account as string, values);
      setTestStatus('success');

      // Save the connection details to update the status
      if (!existingConfig) {
        try {
          await connectIPOSIntegration(account as string, values);

          // Revalidate the integrations page to update the card status
          await fetch(`/api/revalidate?path=/home/<USER>/integrations`, {
            method: 'POST',
          });

          // Đợi một chút để đảm bảo revalidation đã hoàn thành
          await new Promise((resolve) => setTimeout(resolve, 1000));
        } catch (saveError) {
          console.error('Failed to save connection after test:', saveError);
          // Continue with the flow even if saving fails
        }
      }

      // Move to next step after successful connection
      if (currentStep === 1) {
        setCurrentStep(2);
      }
    } catch (error: any) {
      setTestStatus('error');
      setTestError(error.message || t('common:somethingWentWrong'));
    }
  };

  // Handle step navigation
  const goToNextStep = () => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={
          <Trans i18nKey="integrations:ipos.connect.title">
            Set Up iPOS Integration
          </Trans>
        }
        description={<IPOSBreadcrumbs accountSlug={account as string} />}
        account={account as string}
        actions={
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/home/<USER>/integrations`)}
            >
              <Trans i18nKey="integrations:ipos.connect.backToIntegrations">
                Back to Integrations
              </Trans>
            </Button>
            {existingConfig && (
              <Button
                variant="default"
                size="sm"
                onClick={() =>
                  router.push(`/home/<USER>/integrations/ipos/dashboard`)
                }
              >
                <Trans i18nKey="integrations:ipos.connect.goToDashboard">
                  Go to Dashboard
                </Trans>
              </Button>
            )}
          </div>
        }
      />

      <PageBody>
        <Card className="mx-auto max-w-3xl">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="rounded-full bg-blue-50 p-2 dark:bg-blue-900/20">
                <img
                  src="/images/ipos-logo.png"
                  alt="iPOS Logo"
                  className="h-8 w-8"
                />
              </div>
              <div>
                <CardTitle>
                  <Trans i18nKey="integrations:ipos.connect.title">
                    Set Up iPOS Integration
                  </Trans>
                  F
                </CardTitle>
                <CardDescription>
                  <Trans i18nKey="integrations:ipos.connect.description">
                    Connect your iPOS account to sync products, orders, and
                    customers.
                  </Trans>
                </CardDescription>
              </div>
            </div>
            <div className="mt-4 rounded-lg bg-blue-50 p-4 text-sm dark:bg-blue-900/20">
              <h3 className="mb-1 font-medium">
                <Trans i18nKey="integrations:ipos.connect.benefitsTitle">
                  Integration Benefits:
                </Trans>
              </h3>
              <ul className="list-disc space-y-1 pl-5">
                <li>
                  <Trans i18nKey="integrations:ipos.connect.benefit1">
                    Automatically sync products from iPOS to your system
                  </Trans>
                </li>
                <li>
                  <Trans i18nKey="integrations:ipos.connect.benefit2">
                    Keep orders synchronized between platforms
                  </Trans>
                </li>
                <li>
                  <Trans i18nKey="integrations:ipos.connect.benefit3">
                    Maintain consistent customer data
                  </Trans>
                </li>
                <li>
                  <Trans i18nKey="integrations:ipos.connect.benefit4">
                    Receive real-time updates via webhooks
                  </Trans>
                </li>
              </ul>
            </div>
          </CardHeader>
          <CardContent>
            <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50/50 p-4 dark:border-blue-800 dark:bg-blue-900/10">
              <h3 className="mb-2 flex items-center gap-2 text-sm font-medium">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-500"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 16v-4"></path>
                  <path d="M12 8h.01"></path>
                </svg>
                <Trans i18nKey="integrations:ipos.connect.setupInstructions">
                  Setup Instructions
                </Trans>
              </h3>
              <p className="text-muted-foreground text-sm">
                <Trans i18nKey="integrations:ipos.connect.setupSteps">
                  Complete these steps to set up your iPOS integration:
                </Trans>
              </p>
              <ol className="mt-2 list-decimal space-y-1 pl-5 text-sm">
                <li>
                  <Trans i18nKey="integrations:ipos.connect.step1">
                    Enter your iPOS API credentials (required)
                  </Trans>
                </li>
                <li>
                  <Trans i18nKey="integrations:ipos.connect.step2">
                    Test the connection to verify credentials
                  </Trans>
                </li>
                <li>
                  <Trans i18nKey="integrations:ipos.connect.step3">
                    Set up webhooks for real-time updates (optional)
                  </Trans>
                </li>
                <li>
                  <Trans i18nKey="integrations:ipos.connect.step4">
                    Click "Complete Setup" to finish
                  </Trans>
                </li>
              </ol>
            </div>

            <Tabs defaultValue="credentials" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger
                  value="credentials"
                  className="flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
                  </svg>
                  <Trans i18nKey="integrations:ipos.connect.credentials">
                    API Credentials
                  </Trans>
                </TabsTrigger>
                <TabsTrigger
                  value="webhook"
                  className="flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                  </svg>
                  <Trans i18nKey="integrations:ipos.connect.webhook">
                    Webhook Setup
                  </Trans>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="credentials" className="space-y-4 py-4">
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <FormField
                      control={form.control}
                      name="access_token"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="integrations:ipos.connect.accessToken">
                              Access Token
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder={t(
                                'integrations:ipos.connect.accessTokenPlaceholder',
                              )}
                              data-testid="ipos-access-token-input"
                            />
                          </FormControl>
                          <FormDescription>
                            <Trans i18nKey="integrations:ipos.connect.accessTokenDescription">
                              Your iPOS Access Token from the developer portal.
                            </Trans>
                          </FormDescription>
                          <p className="text-muted-foreground mt-1 text-xs">
                            <Trans i18nKey="integrations:ipos.connect.accessTokenExample">
                              <span className="font-medium">Example:</span>{' '}
                              JHTHWPCE6OCZBW0PBH9XRRBC6JTR1UWQ
                            </Trans>
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pos_parent"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="integrations:ipos.connect.posParent">
                              POS Parent
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder={t(
                                'integrations:ipos.connect.posParentPlaceholder',
                              )}
                              data-testid="ipos-pos-parent-input"
                            />
                          </FormControl>
                          <FormDescription>
                            <Trans i18nKey="integrations:ipos.connect.posParentDescription">
                              Your iPOS Parent ID (Brand ID).
                            </Trans>
                          </FormDescription>
                          <p className="text-muted-foreground mt-1 text-xs">
                            <Trans i18nKey="integrations:ipos.connect.posParentExample">
                              <span className="font-medium">Example:</span>{' '}
                              SAOBANG
                            </Trans>
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pos_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="integrations:ipos.connect.posId">
                              POS ID
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder={t(
                                'integrations:ipos.connect.posIdPlaceholder',
                              )}
                              data-testid="ipos-pos-id-input"
                            />
                          </FormControl>
                          <FormDescription>
                            <Trans i18nKey="integrations:ipos.connect.posIdDescription">
                              Your iPOS Store ID.
                            </Trans>
                          </FormDescription>
                          <p className="text-muted-foreground mt-1 text-xs">
                            <Trans i18nKey="integrations:ipos.connect.posIdExample">
                              <span className="font-medium">Example:</span> 3160
                            </Trans>
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="baseUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="integrations:ipos.connect.baseUrl">
                              API Base URL
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder={t(
                                'integrations:ipos.connect.baseUrlPlaceholder',
                              )}
                              data-testid="ipos-base-url-input"
                            />
                          </FormControl>
                          <FormDescription>
                            <Trans i18nKey="integrations:ipos.connect.baseUrlDescription">
                              The base URL for the iPOS API.
                            </Trans>
                          </FormDescription>
                          <p className="text-muted-foreground mt-1 text-xs">
                            <Trans i18nKey="integrations:ipos.connect.baseUrlDefault">
                              <span className="font-medium">Default:</span>{' '}
                              https://api.foodbook.vn
                            </Trans>
                          </p>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {testStatus === 'success' && (
                      <Alert className="border-green-200 bg-green-50">
                        <AlertTitle className="text-green-800">
                          <Trans i18nKey="integrations:ipos.connect.testSuccess">
                            Connection successful
                          </Trans>
                        </AlertTitle>
                        <AlertDescription className="text-green-700">
                          <Trans i18nKey="integrations:ipos.connect.testSuccessDescription">
                            Successfully connected to iPOS API.
                          </Trans>
                        </AlertDescription>
                      </Alert>
                    )}

                    {testStatus === 'error' && (
                      <Alert className="border-red-200 bg-red-50">
                        <AlertTitle className="text-red-800">
                          <Trans i18nKey="integrations:ipos.connect.testError">
                            Connection failed
                          </Trans>
                        </AlertTitle>
                        <AlertDescription className="text-red-700">
                          {testError ||
                            t('integrations:ipos.connect.testErrorDescription')}
                        </AlertDescription>
                      </Alert>
                    )}

                    <div className="mt-6 flex justify-between space-x-4 border-t pt-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() =>
                          router.push(`/home/<USER>/integrations`)
                        }
                        disabled={isSubmitting}
                      >
                        <Trans i18nKey="common:cancel">Cancel</Trans>
                      </Button>

                      <div className="flex space-x-3">
                        <Button
                          type="button"
                          variant="secondary"
                          onClick={handleTestConnection}
                          disabled={isSubmitting || testStatus === 'testing'}
                          data-testid="ipos-test-connection-button"
                          className="flex items-center gap-2"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className={
                              testStatus === 'testing' ? 'animate-spin' : ''
                            }
                          >
                            <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
                          </svg>

                          {testStatus === 'testing' ? (
                            <Trans i18nKey="integrations:ipos.connect.testing">
                              Testing...
                            </Trans>
                          ) : (
                            <Trans i18nKey="integrations:ipos.connect.testConnection">
                              Test Connection
                            </Trans>
                          )}
                        </Button>

                        <Button
                          type="submit"
                          disabled={isSubmitting || testStatus !== 'success'}
                          data-testid="ipos-connect-button"
                          className="flex items-center gap-2"
                        >
                          {isSubmitting ? (
                            <>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="animate-spin"
                              >
                                <path d="M21 12a9 9 0 1 1-6.219-8.56"></path>
                              </svg>
                              <Trans i18nKey="common:saving">Saving...</Trans>
                            </>
                          ) : (
                            <>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                              </svg>
                              <Trans i18nKey="integrations:ipos.connect.completeSetup">
                                Complete Setup
                              </Trans>
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </form>
                </Form>
              </TabsContent>

              <TabsContent value="webhook" className="space-y-4 py-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">
                      <Trans i18nKey="integrations:ipos.connect.webhookSetup">
                        Webhook Setup
                      </Trans>
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      <Trans i18nKey="integrations:ipos.connect.webhookDescription">
                        Set up webhooks to receive real-time updates from iPOS.
                      </Trans>
                    </p>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="font-medium">
                      <Trans i18nKey="integrations:ipos.connect.webhookUrl">
                        Webhook URL
                      </Trans>
                    </h4>
                    <div className="flex items-center space-x-2">
                      <Input
                        readOnly
                        value={`${process.env.NEXT_PUBLIC_APP_URL}/api/webhooks/ipos`}
                        className="bg-muted/50"
                        data-testid="ipos-webhook-url-input"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(
                            `${process.env.NEXT_PUBLIC_APP_URL}/api/webhooks/ipos`,
                          );
                          toast.success(t('common:copied'));
                        }}
                        data-testid="ipos-copy-webhook-url-button"
                      >
                        <Trans i18nKey="common:copy">Copy</Trans>
                      </Button>
                    </div>
                    <p className="text-muted-foreground text-xs">
                      <Trans i18nKey="integrations:ipos.connect.webhookUrlDescription">
                        Add this URL to your iPOS developer portal to receive
                        webhooks.
                      </Trans>
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">
                      <Trans i18nKey="integrations:ipos.connect.webhookEvents">
                        Webhook Events
                      </Trans>
                    </h4>
                    <p className="text-sm">
                      <Trans i18nKey="integrations:ipos.connect.webhookEventsDescription">
                        Subscribe to the following events in your iPOS developer
                        portal:
                      </Trans>
                    </p>
                    <ul className="list-disc pl-5 text-sm">
                      <li>product.created</li>
                      <li>product.updated</li>
                      <li>order.created</li>
                      <li>order.updated</li>
                      <li>order.status_changed</li>
                      <li>customer.created</li>
                      <li>customer.updated</li>
                    </ul>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="border-t bg-gray-50 p-4 dark:bg-gray-900/20">
            <div className="flex flex-col space-y-2">
              <div className="text-muted-foreground flex items-center gap-2 text-xs">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-500"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 16v-4"></path>
                  <path d="M12 8h.01"></path>
                </svg>
                <Trans
                  i18nKey="integrations:ipos.connect.footer"
                  components={{
                    1: (
                      <a
                        href="https://docs.ipos.vn/api"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 underline dark:text-blue-400"
                      />
                    ),
                  }}
                />
              </div>

              <div className="text-muted-foreground flex items-center gap-2 text-xs">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-amber-500"
                >
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                  <line x1="12" y1="9" x2="12" y2="13"></line>
                  <line x1="12" y1="17" x2="12.01" y2="17"></line>
                </svg>
                <Trans
                  i18nKey="integrations:ipos.connect.exampleNote"
                  components={{
                    0: <span className="font-medium" />,
                  }}
                />
              </div>
            </div>
          </CardFooter>
        </Card>
      </PageBody>
    </>
  );
}
