'use server';

import { revalidatePath } from 'next/cache';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { loadTeamWorkspace } from '~/home/<USER>/_lib/server/team-account-workspace.loader';
import { Database } from '~/lib/database.types';

import {
  IPOSCredentials,
  authenticateIPOS,
} from '../../../_lib/server/ipos-connector-server';

/**
 * Kết nối với iPOS
 */
export async function connectIPOSIntegration(
  accountSlug: string,
  credentials: IPOSCredentials,
) {
  const logger = await getLogger();

  try {
    logger.info({ accountSlug }, 'Connecting iPOS integration');

    // Lấy thông tin workspace từ slug
    const { account } = await loadTeamWorkspace(accountSlug);

    if (!account || !account.id) {
      logger.error({ accountSlug }, 'Account not found');
      throw new Error('Account not found');
    }

    const accountId = account.id;
    logger.info({ accountSlug, accountId }, 'Found account ID');

    const supabase = getSupabaseServerClient<Database>();

    // Kiểm tra kết nối trước khi lưu
    await authenticateIPOS(credentials);

    // Kiểm tra xem đã có integration chưa
    const { data: existingIntegration, error: fetchError } = await supabase
      .from('integrations')
      .select('id')
      .eq('account_id', accountId)
      .eq('type', 'ipos' as Database['public']['Enums']['integration_type'])
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      // PGRST116 là lỗi "không tìm thấy"
      throw fetchError;
    }

    if (existingIntegration) {
      // Cập nhật integration hiện có
      const { error } = await supabase
        .from('integrations')
        .update({
          credentials,
          status: 'connected',
          enabled: true,
          error_message: null,
          last_sync_at: null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', existingIntegration.id);

      if (error) throw error;
    } else {
      // Tạo integration mới
      const { error } = await supabase.from('integrations').insert({
        account_id: accountId,
        type: 'ipos' as Database['public']['Enums']['integration_type'],
        name: 'iPOS',
        description:
          'Sync products, orders, and customers with iPOS POS system',
        status:
          'connected' as Database['public']['Enums']['integration_status'],
        enabled: true,
        credentials,
        config: {
          logo: '/images/ipos-logo.png',
        },
      });

      if (error) throw error;
    }

    // Revalidate paths để cập nhật UI
    revalidatePath(`/home/<USER>/integrations`);
    revalidatePath(`/home/<USER>/integrations/ipos/dashboard`);
    revalidatePath(`/home/<USER>/integrations/ipos/connect`);

    // Đợi một chút để đảm bảo revalidation đã hoàn thành
    await new Promise(resolve => setTimeout(resolve, 500));

    logger.info(
      { accountSlug, accountId },
      'iPOS integration connected successfully',
    );
    return { success: true };
  } catch (error: any) {
    logger.error({ error, accountSlug }, 'Error connecting iPOS integration');
    throw new Error(`Failed to connect iPOS: ${error.message}`);
  }
}

/**
 * Test kết nối với iPOS
 */
export async function testIPOSConnection(
  accountSlug: string,
  credentials: IPOSCredentials,
) {
  const logger = await getLogger();

  try {
    logger.info({ accountSlug, credentials }, 'Testing iPOS connection');

    // Thử xác thực với iPOS API
    await authenticateIPOS(credentials);

    logger.info({ accountSlug }, 'iPOS connection test successful');
    return { success: true };
  } catch (error: any) {
    logger.error(
      { error, accountSlug, credentials },
      'iPOS connection test failed',
    );
    throw new Error(`Connection test failed: ${error.message}`);
  }
}

/**
 * Đồng bộ dữ liệu từ iPOS
 */
export async function syncIPOSData(
  integrationId: string,
  resourceType: 'products' | 'orders' | 'customers',
  userId: string,
) {
  const supabase = getSupabaseServerClient<Database>();
  const logger = await getLogger();

  try {
    logger.info({ integrationId, resourceType }, 'Starting iPOS data sync');

    // Lấy thông tin integration
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .eq('type', 'ipos')
      .single();

    if (integrationError || !integration) {
      throw new Error('iPOS integration not found');
    }

    // Kiểm tra trạng thái integration
    if (integration.status !== 'connected' || !integration.enabled) {
      throw new Error('iPOS integration is not connected or disabled');
    }

    // Tạo sync log
    const { data: syncLog, error: syncLogError } = await supabase
      .from('integration_sync_logs')
      .insert({
        integration_id: integrationId,
        resource_type: resourceType,
        status:
          'in_progress' as Database['public']['Enums']['integration_sync_status'],
        created_by: userId,
      })
      .select()
      .single();

    if (syncLogError || !syncLog) {
      throw new Error('Failed to create sync log');
    }

    // Tạo connector
    const connector = new IPOSConnector(
      integration.credentials as IPOSCredentials,
    );

    // Lấy dữ liệu từ iPOS
    let data: any[] = [];
    switch (resourceType) {
      case 'products':
        data = await connector.getProducts();
        break;
      case 'orders':
        data = await connector.getOrders();
        break;
      case 'customers':
        data = await connector.getCustomers();
        break;
    }

    // Lấy mappings
    const { data: mappings } = await supabase
      .from('integration_mappings')
      .select('*')
      .eq('integration_id', integrationId)
      .eq('resource_type', resourceType)
      .eq('is_active', true);

    // Áp dụng mappings và lưu dữ liệu
    let successCount = 0;
    let failedCount = 0;

    for (const item of data) {
      try {
        // Áp dụng mapping
        const mappedData = applyMapping(item, mappings || []);

        // Lưu vào bảng tương ứng
        const { error: saveError } = await saveDataToTable(
          resourceType,
          mappedData,
          integration.account_id,
        );

        if (saveError) {
          // Lưu thông tin lỗi
          await supabase.from('integration_sync_items').insert({
            sync_log_id: syncLog.id,
            external_id: item.id,
            resource_type: resourceType,
            status:
              'error' as Database['public']['Enums']['integration_sync_status'],
            error_message: saveError.message,
            raw_data: item,
            processed_data: mappedData,
          });

          failedCount++;
        } else {
          // Lưu thông tin thành công
          await supabase.from('integration_sync_items').insert({
            sync_log_id: syncLog.id,
            external_id: item.id,
            internal_id: mappedData.id,
            resource_type: resourceType,
            status:
              'success' as Database['public']['Enums']['integration_sync_status'],
            raw_data: item,
            processed_data: mappedData,
          });

          successCount++;
        }
      } catch (itemError: any) {
        // Lưu thông tin lỗi
        await supabase.from('integration_sync_items').insert({
          sync_log_id: syncLog.id,
          external_id: item.id || 'unknown',
          resource_type: resourceType,
          status: 'error',
          error_message: itemError.message,
          raw_data: item,
        });

        failedCount++;
      }
    }

    // Cập nhật sync log
    await supabase
      .from('integration_sync_logs')
      .update({
        status:
          failedCount > 0
            ? successCount > 0
              ? ('partial' as Database['public']['Enums']['integration_sync_status'])
              : ('error' as Database['public']['Enums']['integration_sync_status'])
            : ('success' as Database['public']['Enums']['integration_sync_status']),
        items_processed: data.length,
        items_created: successCount,
        items_failed: failedCount,
        completed_at: new Date().toISOString(),
      })
      .eq('id', syncLog.id);

    // Cập nhật integration
    await supabase
      .from('integrations')
      .update({
        last_sync_at: new Date().toISOString(),
        error_message:
          failedCount > 0 ? `Failed to sync ${failedCount} items` : null,
      })
      .eq('id', integrationId);

    logger.info(
      {
        integrationId,
        resourceType,
        total: data.length,
        success: successCount,
        failed: failedCount,
      },
      'iPOS data sync completed',
    );

    return {
      success: true,
      syncLogId: syncLog.id,
      total: data.length,
      created: successCount,
      failed: failedCount,
    };
  } catch (error: any) {
    logger.error(
      { error, integrationId, resourceType },
      'Error syncing iPOS data',
    );

    // Cập nhật sync log nếu đã tạo
    const { data: syncLog } = await supabase
      .from('integration_sync_logs')
      .select('id')
      .eq('integration_id', integrationId)
      .eq('resource_type', resourceType)
      .eq('status', 'in_progress')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (syncLog) {
      await supabase
        .from('integration_sync_logs')
        .update({
          status: 'error',
          error_message: error.message,
          completed_at: new Date().toISOString(),
        })
        .eq('id', syncLog.id);
    }

    // Cập nhật integration
    await supabase
      .from('integrations')
      .update({
        error_message: `Sync failed: ${error.message}`,
      })
      .eq('id', integrationId);

    throw new Error(`Failed to sync iPOS data: ${error.message}`);
  }
}

/**
 * Áp dụng mapping cho dữ liệu
 */
function applyMapping(data: any, mappings: any[]): any {
  const result: Record<string, any> = {};

  // Nếu không có mapping, trả về dữ liệu gốc
  if (!mappings || mappings.length === 0) {
    return data;
  }

  // Áp dụng từng mapping
  for (const mapping of mappings) {
    const { source_field, target_field, transform_function } = mapping;

    // Lấy giá trị từ source field
    let value = data[source_field];

    // Áp dụng hàm biến đổi nếu có
    if (transform_function && value !== undefined) {
      try {
        // Thực thi hàm biến đổi (cẩn thận với eval!)
        // Trong môi trường production, nên sử dụng cách an toàn hơn
        const transformFn = new Function('value', transform_function);
        value = transformFn(value);
      } catch (error) {
        console.error(
          `Error applying transform function for ${source_field}:`,
          error,
        );
      }
    }

    // Gán giá trị cho target field
    if (value !== undefined) {
      result[target_field] = value;
    }
  }

  return result;
}

/**
 * Lưu dữ liệu vào bảng tương ứng
 */
async function saveDataToTable(
  resourceType: string,
  data: any,
  accountId: string,
) {
  const supabase = getSupabaseServerClient();

  // Thêm account_id vào dữ liệu
  const dataWithAccount = {
    ...data,
    account_id: accountId,
  };

  // Xác định bảng dựa trên resource type
  let table: string;
  switch (resourceType) {
    case 'products':
      table = 'products';
      break;
    case 'orders':
      table = 'customer_orders';
      break;
    case 'customers':
      table = 'customers';
      break;
    default:
      throw new Error(`Unsupported resource type: ${resourceType}`);
  }

  // Kiểm tra xem đã có dữ liệu chưa
  const { data: existingData, error: fetchError } = await supabase
    .from(table)
    .select('id')
    .eq('id', data.id)
    .eq('account_id', accountId)
    .maybeSingle();

  if (fetchError) {
    return { error: fetchError };
  }

  if (existingData) {
    // Cập nhật dữ liệu hiện có
    return await supabase
      .from(table)
      .update(dataWithAccount)
      .eq('id', data.id)
      .eq('account_id', accountId);
  } else {
    // Tạo dữ liệu mới
    return await supabase.from(table).insert(dataWithAccount);
  }
}
