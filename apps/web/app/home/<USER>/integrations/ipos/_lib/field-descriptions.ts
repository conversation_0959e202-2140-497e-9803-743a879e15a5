/**
 * <PERSON><PERSON> tả các trường dữ liệu từ API iPOS
 * Dựa trên tài liệu IPOS HUB API INTEGRATION FOR MERCHANT
 */

/**
 * API endpoints cho từng loại resource theo thứ tự đồng bộ
 */
export const apiEndpoints: Record<string, Record<string, string>> = {
  // 1. Branches (Chi nhánh)
  branches: {
    list: '/ipos/ws/xpartner/pos'
  },

  // 2. Categories (Danh mục)
  categories: {
    list: '/ipos/ws/xpartner/v2/items' // Phần item_types
  },

  // 3. Products (Sản phẩm)
  products: {
    list: '/ipos/ws/xpartner/v2/items' // Phần items
  },

  // 4. Users (Kh<PERSON>ch hàng)
  customers: {
    detail: '/ipos/ws/xpartner/membership_detail',
    add: '/ipos/ws/xpartner/add_membership',
    update: '/ipos/ws/xpartner/update_membership',
    transactions: '/ipos/ws/xpartner/member_transactions',
    points: '/ipos/ws/xpartner/membership_log'
  },

  // 5. Vouchers (Phiếu giảm giá)
  vouchers: {
    check: '/ipos/ws/xpartner/check_voucher',
    list: '/ipos/ws/xpartner/member_vouchers',
    generate: '/ipos/ws/xpartner/gen_vouchers',
    exchange: '/ipos/ws/xpartner/exchange_point',
    campaigns: '/ipos/ws/xpartner/campaigns'
  },

  // 6. Customer Orders (Đơn hàng)
  orders: {
    create: '/ipos/ws/xpartner/order_online',
    booking: '/ipos/ws/xpartner/booking_online',
    status: '/ipos/ws/xpartner/order_online_info',
    cancel: '/ipos/ws/xpartner/cancel_order',
    shipFee: '/ipos/ws/xpartner/estimate_ship_fee'
  },

  // 7. Points (Tích điểm - Không lưu trong hệ thống)
  points: {
    checkin: '/ipos/ws/xpartner/checkin_code',
    history: '/ipos/ws/xpartner/membership_log',
    exchange: '/ipos/ws/xpartner/exchange_point'
  }
};

export const fieldDescriptions: Record<string, Record<string, string>> = {
  vouchers: {
    // Trường hệ thống
    id: 'ID trong hệ thống. Không nên mapping trực tiếp với ID iPOS để tránh xung đột.',
    user_id: 'Liên kết với users.membership_id_new (số điện thoại).',
    name: 'Tên campaign voucher.',
    description: 'Mô tả voucher.',
    discount_type: 'Loại giảm giá (1: số tiền, 2: phần trăm).',
    discount_amount: 'Giá trị giảm giá (nếu là số tiền).',
    discount_extra: 'Giá trị giảm giá (nếu là phần trăm).',
    start_date: 'Ngày bắt đầu hiệu lực.',
    end_date: 'Ngày hết hạn.',
    status: 'Trạng thái voucher (e.g., 3: đã sử dụng, 4: khả dụng).',

    // Trường iPOS
    voucher_code: 'Mã voucher từ iPOS.',
    buyer_info: 'Liên kết với users.membership_id_new (số điện thoại).',
    voucher_campaign_name: 'Tên campaign voucher trong iPOS.',
    voucher_description: 'Mô tả voucher trong iPOS.',
    date_start: 'Ngày bắt đầu hiệu lực trong iPOS.',
    date_end: 'Ngày hết hạn trong iPOS.',

    // Trường metadata mở rộng
    external_id: 'Mã voucher từ iPOS (voucher_code).',
    source: 'Gán giá trị ipos để đánh dấu nguồn.',
    last_synced_at: 'Thời gian tạo voucher từ iPOS.',
    integration_data: 'Lưu metadata bổ sung trong JSON (voucher_campaign_id, is_all_item, item_type_id_list, discount_max, is_coupon, only_coupon).',
  },
  products: {
    // Trường hệ thống
    id: 'ID trong hệ thống. Không nên mapping trực tiếp với ID iPOS để tránh xung đột.',
    name: 'Tên sản phẩm.',
    price: 'Giá bán của sản phẩm.',
    category_id: 'Liên kết với categories.text_id.',

    // Trường iPOS
    id_ipos: 'ID sản phẩm từ iPOS.',
    name_ipos: 'Tên sản phẩm trong iPOS.',
    ta_price: 'Giá mang đi.',
    type_id: 'Liên kết với categories.text_id.',
    store_item_id: 'Mã sản phẩm từ iPOS (e.g., UT224).',

    // Trường metadata mở rộng
    external_id: 'Mã sản phẩm từ iPOS (store_item_id).',
    source: 'Gán giá trị ipos để đánh dấu nguồn.',
    last_synced_at: 'Thời gian cập nhật cuối cùng từ iPOS.',
    integration_data: 'Lưu metadata bổ sung trong JSON (ots_price, description, is_featured, allow_take_away, allow_self_order, time_sale_date_week, time_sale_hour_day).',
  },
  orders: {
    // Trường hệ thống
    id: 'ID trong hệ thống. Không nên mapping trực tiếp với ID iPOS để tránh xung đột.',
    user_id: 'Liên kết với users.membership_id_new.',
    branch_id: 'Liên kết với branches.Id.',
    items: 'Danh sách sản phẩm trong đơn hàng (JSON).',
    total_amount: 'Tổng giá trị đơn hàng (bao gồm phí dịch vụ, thuế, phí vận chuyển).',
    status: 'Trạng thái đơn hàng (e.g., WAIT_CONFIRM, USER_CANCELLED).',
    order_type: 'Loại đơn hàng (DELI, STORE, PICK, DELIAT).',

    // Trường iPOS
    foodbook_code: 'Mã đơn hàng từ iPOS.',
    user_id_ipos: 'Liên kết với users.membership_id_new.',
    pos_id: 'Liên kết với branches.Id.',
    order_data_item: 'Danh sách sản phẩm trong đơn hàng (JSON).',

    // Trường metadata mở rộng
    external_id: 'Mã đơn hàng từ iPOS (foodbook_code).',
    source: 'Gán giá trị ipos để đánh dấu nguồn.',
    last_synced_at: 'Thời gian cập nhật cuối cùng từ iPOS.',
    integration_data: 'Lưu metadata bổ sung trong JSON (service_charge_amount, vat_tax_amount, ship_price_real, booking_info, note).',
  },
  customers: {
    // Trường hệ thống
    id: 'ID trong hệ thống. Không nên mapping trực tiếp với ID iPOS để tránh xung đột.',
    phone: 'Số điện thoại của khách hàng, dùng làm cầu nối để kiểm tra trùng lặp.',
    name: 'Tên khách hàng.',
    email: 'Email khách hàng.',
    birthday: 'Ngày sinh của khách hàng.',
    gender: 'Giới tính của khách hàng (1: nam, 0: nữ, -1: không xác định).',
    address: 'Địa chỉ chính của khách hàng.',
    points: 'Điểm tích lũy của khách hàng.',

    // Trường iPOS
    membership_id_new: 'ID duy nhất của thành viên trong iPOS.',
    phone_number: 'Số điện thoại trong iPOS, dùng làm cầu nối kiểm tra trùng lặp.',
    point: 'Điểm tích lũy trong iPOS.',

    // Trường metadata mở rộng
    external_id: 'ID gốc từ iPOS (MongoDB ObjectId).',
    source: 'Gán giá trị ipos để đánh dấu nguồn.',
    last_synced_at: 'Thời gian cập nhật cuối cùng từ iPOS.',
    integration_data: 'Lưu metadata bổ sung từ iPOS trong JSON (membership_type_id, membership_type_name, point_amount, eat_times, first_eat_date, last_eat_date).',
  },
  branches: {
    // Trường hệ thống
    id: 'ID trong hệ thống. Không nên mapping trực tiếp với ID iPOS để tránh xung đột.',
    name: 'Tên chi nhánh.',
    address: 'Địa chỉ chi nhánh.',
    phone: 'Số điện thoại chi nhánh.',
    latitude: 'Vĩ độ của chi nhánh.',
    longitude: 'Kinh độ của chi nhánh.',

    // Trường iPOS
    Id: 'ID chi nhánh từ iPOS.',
    Pos_Name: 'Tên chi nhánh trong iPOS.',
    Pos_Address: 'Địa chỉ chi nhánh trong iPOS.',
    Phone_Number: 'Số điện thoại chi nhánh trong iPOS.',
    Pos_Latitude: 'Vĩ độ của chi nhánh trong iPOS.',
    Pos_Longitude: 'Kinh độ của chi nhánh trong iPOS.',

    // Trường metadata mở rộng
    external_id: 'ID chi nhánh từ iPOS.',
    source: 'Gán giá trị ipos để đánh dấu nguồn.',
    last_synced_at: 'Thời gian đồng bộ cuối cùng (tự quản lý).',
    integration_data: 'Lưu metadata bổ sung trong JSON (Open_Time, Estimate_Price, Wifi_Password, Is_Car_Parking, Is_Visa, Delivery_Services, City_Name).',
  },
  categories: {
    // Trường hệ thống
    id: 'ID trong hệ thống. Không nên mapping trực tiếp với ID iPOS để tránh xung đột.',
    name: 'Tên danh mục.',
    external_id: 'Mã danh mục từ iPOS (text_id).',
    source: 'Gán giá trị ipos để đánh dấu nguồn.',
    last_synced_at: 'Thời gian đồng bộ cuối cùng (tự quản lý).',
    integration_data: 'Lưu metadata bổ sung trong JSON (sort, active, store_id).',

    // Trường iPOS
    id_ipos: 'ID danh mục từ iPOS.',
    name_ipos: 'Tên danh mục trong iPOS.',
    text_id: 'Mã danh mục từ iPOS (e.g., BCN).',
    active: 'Trạng thái hoạt động của danh mục (1: active, 0: inactive).',
    sort: 'Thứ tự sắp xếp của danh mục.',
    store_id: 'ID cửa hàng mà danh mục thuộc về.',
  },
};

/**
 * Danh sách các trường đề xuất để đánh dấu bản ghi đồng bộ
 * Các trường này được sử dụng để xác định bản ghi khi đồng bộ
 */
export const recommendedSyncFields: Record<string, string[]> = {
  products: ['store_item_id', 'external_id'],
  orders: ['foodbook_code', 'external_id'],
  customers: ['membership_id_new', 'phone_number', 'external_id'],
  categories: ['text_id', 'external_id'],
  branches: ['Id', 'external_id'],
  vouchers: ['voucher_code', 'external_id'],
};

/**
 * Danh sách các trường không nên mapping trực tiếp với ID hệ thống
 */
export const avoidDirectIdMapping: Record<string, string[]> = {
  products: ['id'],
  orders: ['id'],
  customers: ['id'],
  categories: ['id'],
  branches: ['id'],
  vouchers: ['id'],
};

/**
 * Danh sách các trường bắt buộc cho mỗi loại tài nguyên
 */
export const requiredFields: Record<string, string[]> = {
  products: ['name', 'price', 'category_id'],
  orders: ['user_id', 'branch_id', 'items', 'total_amount', 'status', 'order_type'],
  customers: ['name', 'email', 'phone', 'external_id'],
  categories: ['name', 'external_id'],
  branches: ['name', 'address', 'phone', 'external_id'],
  vouchers: ['name', 'description', 'discount_type', 'discount_amount', 'start_date', 'end_date', 'user_id'],
};

/**
 * Danh sách các trường mặc định (read-only) cho mỗi loại tài nguyên
 * Người dùng không thể thay đổi mapping của các trường này
 */
export const defaultMappings: Record<string, Record<string, string>> = {
  products: {
    // Trường metadata
    store_item_id: 'external_id',
    source: 'ipos',
    last_synced_at: 'last_synced_at',
    integration_data: 'integration_data',
    // Trường bắt buộc
    name_ipos: 'name',
    ta_price: 'price',
    type_id: 'category_id'
  },
  orders: {
    // Trường metadata
    foodbook_code: 'external_id',
    source: 'ipos',
    last_synced_at: 'last_synced_at',
    integration_data: 'integration_data',
    // Trường bắt buộc
    user_id_ipos: 'user_id',
    pos_id: 'branch_id',
    order_data_item: 'items',
    total_amount: 'total_amount',
    status: 'status',
    order_type: 'order_type'
  },
  customers: {
    // Trường metadata
    membership_id_new: 'external_id',
    source: 'ipos',
    last_synced_at: 'last_synced_at',
    integration_data: 'integration_data',
    // Trường bắt buộc
    name: 'name',
    email: 'email',
    phone_number: 'phone',
    point: 'points',
    birthday: 'birthday',
    gender: 'gender'
  },
  categories: {
    // Trường metadata
    text_id: 'external_id',
    source: 'ipos',
    last_synced_at: 'last_synced_at',
    integration_data: 'integration_data',
    // Trường bắt buộc
    name: 'name',
    id: 'id_ipos',
    active: 'active',
    sort: 'sort',
    store_id: 'store_id'
  },
  branches: {
    // Trường metadata
    Id: 'external_id',
    source: 'ipos',
    last_synced_at: 'last_synced_at',
    integration_data: 'integration_data',
    // Trường bắt buộc
    Pos_Name: 'name',
    Pos_Address: 'address',
    Phone_Number: 'phone',
    Pos_Latitude: 'latitude',
    Pos_Longitude: 'longitude'
  },
  vouchers: {
    // Trường metadata
    voucher_code: 'external_id',
    source: 'ipos',
    last_synced_at: 'last_synced_at',
    integration_data: 'integration_data',
    // Trường bắt buộc
    voucher_campaign_name: 'name',
    voucher_description: 'description',
    discount_type: 'discount_type',
    discount_amount: 'discount_amount',
    discount_extra: 'discount_extra',
    date_start: 'start_date',
    date_end: 'end_date',
    buyer_info: 'user_id'
  },
};

/**
 * Danh sách các trường khuyến nghị (recommended) cho mỗi loại tài nguyên
 * Người dùng có thể thay đổi mapping của các trường này, nhưng chúng được đề xuất mặc định
 */
export const recommendedMappings: Record<string, Record<string, string>> = {
  products: {
    // Các trường đã có trong defaultMappings không cần liệt kê lại
    // store_item_id: 'external_id',
    // name_ipos: 'name',
    // ta_price: 'price',
    // type_id: 'category_id',
    description: 'description',
    image_url: 'image_url',
    ots_price: 'compare_at_price', // Giá tại chỗ có thể ánh xạ với giá so sánh
    is_featured: 'is_featured',
    allow_take_away: 'allow_take_away',
    allow_self_order: 'allow_self_order'
  },
  orders: {
    // Các trường đã có trong defaultMappings không cần liệt kê lại
    // foodbook_code: 'external_id',
    // user_id_ipos: 'user_id',
    // pos_id: 'branch_id',
    // order_data_item: 'items',
    // total_amount: 'total_amount',
    // status: 'status',
    // order_type: 'order_type',
    service_charge_amount: 'service_charge_amount',
    vat_tax_amount: 'vat_tax_amount',
    ship_price_real: 'shipping_fee',
    note: 'notes'
  },
  customers: {
    // Các trường đã có trong defaultMappings không cần liệt kê lại
    // membership_id_new: 'external_id',
    // name: 'name',
    // email: 'email',
    // phone_number: 'phone',
    // point: 'points',
    // birthday: 'birthday',
    // gender: 'gender',
    membership_type_id: 'membership_type_id',
    membership_type_name: 'membership_type_name',
    point_amount: 'point_amount',
    eat_times: 'eat_times',
    first_eat_date: 'first_eat_date',
    last_eat_date: 'last_eat_date'
  },
  categories: {
    // Các trường đã có trong defaultMappings không cần liệt kê lại
    // text_id: 'external_id',
    // name: 'name',
    // id: 'id_ipos',
    // active: 'active',
    // sort: 'sort',
    // store_id: 'store_id',
    description: 'description',
    parent_id: 'parent_id'
  },
  branches: {
    // Các trường đã có trong defaultMappings không cần liệt kê lại
    // Id: 'external_id',
    // Pos_Name: 'name',
    // Pos_Address: 'address',
    // Phone_Number: 'phone',
    // Pos_Latitude: 'latitude',
    // Pos_Longitude: 'longitude',
    Open_Time: 'opening_hours',
    Estimate_Price: 'price_range',
    Wifi_Password: 'wifi_password',
    Is_Car_Parking: 'has_parking',
    Is_Visa: 'accepts_credit_cards',
    Delivery_Services: 'delivery_services',
    City_Name: 'city'
  },
  vouchers: {
    // Các trường đã có trong defaultMappings không cần liệt kê lại
    // voucher_code: 'external_id',
    // voucher_campaign_name: 'name',
    // voucher_description: 'description',
    // discount_type: 'discount_type',
    // discount_amount: 'discount_amount',
    // discount_extra: 'discount_extra',
    // date_start: 'start_date',
    // date_end: 'end_date',
    // buyer_info: 'user_id',
    voucher_campaign_id: 'campaign_id',
    is_all_item: 'applies_to_all_items',
    item_type_id_list: 'applicable_categories',
    discount_max: 'maximum_discount',
    is_coupon: 'is_coupon',
    only_coupon: 'only_coupon',
    conditions: 'conditions',
    usage_limit: 'usage_limit',
    usage_count: 'usage_count'
  },
};
