'use client';

import { useEffect, useState } from 'react';

import { useParams } from 'next/navigation';

import { DndContext, DragEndEvent, closestCenter } from '@dnd-kit/core';
import { DragDropMapping } from '../../_components/drag-drop-mapping';
import {
  SortableContext,
  arrayMove,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Separator } from '@kit/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

interface MappingEditorProps {
  integrationId: string;
  resourceType: string;
  platform: string;
  onSave: (mappings: any[]) => void;
}

interface FieldMapping {
  id: string;
  source_field: string;
  target_field: string;
  transform_function?: string;
  is_required?: boolean;
}

interface SourceField {
  name: string;
  type: string;
  description?: string;
}

interface TargetField {
  name: string;
  type: string;
  description?: string;
}

export function MappingEditor({
  integrationId,
  resourceType,
  platform,
  onSave,
}: MappingEditorProps) {
  const { account } = useParams();
  const { t } = useTranslation(['integrations', 'common']);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sourceFields, setSourceFields] = useState<SourceField[]>([]);
  const [targetFields, setTargetFields] = useState<TargetField[]>([]);
  const [mappings, setMappings] = useState<FieldMapping[]>([]);
  const [editingMapping, setEditingMapping] = useState<FieldMapping | null>(
    null,
  );
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'list' | 'visual'>('list');

  // Lấy thông tin fields và mappings
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Lấy source fields từ platform
        const sourceResponse = await fetch(
          `/api/integrations/fields?platform=${platform}&resourceType=${resourceType}&source=true`,
        );
        if (!sourceResponse.ok) {
          throw new Error('Failed to fetch source fields');
        }
        const sourceData = await sourceResponse.json();
        setSourceFields(sourceData.fields || []);

        // Lấy target fields từ hệ thống
        const targetResponse = await fetch(
          `/api/integrations/fields?resourceType=${resourceType}&target=true`,
        );
        if (!targetResponse.ok) {
          throw new Error('Failed to fetch target fields');
        }
        const targetData = await targetResponse.json();
        setTargetFields(targetData.fields || []);

        // Lấy mappings hiện tại
        const mappingsResponse = await fetch(
          `/api/integrations/mapping?integrationId=${integrationId}&resourceType=${resourceType}`,
        );
        if (mappingsResponse.ok) {
          const mappingsData = await mappingsResponse.json();
          setMappings(mappingsData.mappings || []);
        }
      } catch (error: any) {
        setError(error.message || 'Failed to load data');
        toast.error('Failed to load mapping data', {
          description: error.message || 'Please try again later',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [integrationId, resourceType, platform]);

  // Xử lý khi thêm mapping mới
  const handleAddMapping = () => {
    setEditingMapping({
      id: `mapping-${Date.now()}`,
      source_field: '',
      target_field: '',
    });
    setIsDialogOpen(true);
  };

  // Xử lý khi sửa mapping
  const handleEditMapping = (mapping: FieldMapping) => {
    setEditingMapping({ ...mapping });
    setIsDialogOpen(true);
  };

  // Xử lý khi xóa mapping
  const handleDeleteMapping = (id: string) => {
    setMappings(mappings.filter((m) => m.id !== id));
  };

  // Xử lý khi lưu mapping trong dialog
  const handleSaveMapping = () => {
    if (!editingMapping) return;

    if (!editingMapping.source_field || !editingMapping.target_field) {
      toast.error('Source field and target field are required');
      return;
    }

    const existingIndex = mappings.findIndex((m) => m.id === editingMapping.id);
    if (existingIndex >= 0) {
      // Cập nhật mapping hiện có
      const updatedMappings = [...mappings];
      updatedMappings[existingIndex] = editingMapping;
      setMappings(updatedMappings);
    } else {
      // Thêm mapping mới
      setMappings([...mappings, editingMapping]);
    }

    setIsDialogOpen(false);
    setEditingMapping(null);
  };

  // Xử lý khi kéo thả mapping
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    setMappings((items) => {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over.id);
      return arrayMove(items, oldIndex, newIndex);
    });
  };

  // Xử lý khi xem trước dữ liệu
  const handlePreview = async () => {
    try {
      setIsPreviewLoading(true);

      // Gọi API để lấy dữ liệu mẫu và áp dụng mapping
      const response = await fetch('/api/integrations/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountId: account,
          integrationId,
          resourceType,
          mappings,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to preview data');
      }

      const data = await response.json();
      setPreviewData(data.preview);
    } catch (error: any) {
      toast.error('Failed to preview data', {
        description: error.message || 'Please try again later',
      });
    } finally {
      setIsPreviewLoading(false);
    }
  };

  // Xử lý khi lưu tất cả mappings
  const handleSaveAll = () => {
    onSave(mappings);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">
            <Trans i18nKey="common:loading">Loading...</Trans>
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">
            <Trans
              i18nKey={`integrations:${platform}.mapping.${resourceType}Title`}
            >
              {resourceType.charAt(0).toUpperCase() + resourceType.slice(1)}{' '}
              Mapping
            </Trans>
          </h3>
          <p className="text-sm text-muted-foreground">
            <Trans
              i18nKey={`integrations:${platform}.mapping.${resourceType}Description`}
            >
              Map {platform} {resourceType} fields to your system fields.
            </Trans>
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handlePreview}
            disabled={isPreviewLoading || mappings.length === 0}
          >
            {isPreviewLoading ? (
              <Trans i18nKey="common:loading">Loading...</Trans>
            ) : (
              <Trans i18nKey="integrations:mapping.preview">Preview</Trans>
            )}
          </Button>
          <Button onClick={handleAddMapping}>
            <Trans i18nKey="integrations:mapping.addMapping">Add Mapping</Trans>
          </Button>
        </div>
      </div>

      <Separator />

      <Tabs value={activeTab} onValueChange={(value: 'list' | 'visual') => setActiveTab(value)}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list">
            <Trans i18nKey="integrations:mapping.listView">List View</Trans>
          </TabsTrigger>
          <TabsTrigger value="visual">
            <Trans i18nKey="integrations:mapping.visualView">Visual Mapping</Trans>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="list">
          {mappings.length === 0 ? (
        <div className="text-center py-8 border border-dashed rounded-md">
          <p className="text-muted-foreground">
            <Trans i18nKey="integrations:mapping.noMappings">
              No mappings defined yet. Click "Add Mapping" to create your first
              mapping.
            </Trans>
          </p>
        </div>
      ) : (
        <DndContext
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={mappings.map((m) => m.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-2">
              {mappings.map((mapping) => (
                <SortableItem
                  key={mapping.id}
                  id={mapping.id}
                  mapping={mapping}
                  sourceFields={sourceFields}
                  targetFields={targetFields}
                  onEdit={handleEditMapping}
                  onDelete={handleDeleteMapping}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
      )}
        </TabsContent>

        <TabsContent value="visual">
          <DragDropMapping
            sourceFields={sourceFields}
            targetFields={targetFields}
            mappings={mappings}
            onMappingsChange={setMappings}
          />
        </TabsContent>
      </Tabs>

      {previewData && (
        <div className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>
                <Trans i18nKey="integrations:mapping.previewTitle">
                  Data Preview
                </Trans>
              </CardTitle>
              <CardDescription>
                <Trans i18nKey="integrations:mapping.previewDescription">
                  Preview of how your data will look after mapping.
                </Trans>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <pre className="text-xs bg-muted p-4 rounded-md">
                  {JSON.stringify(previewData, null, 2)}
                </pre>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="flex justify-end mt-6">
        <Button onClick={handleSaveAll} disabled={mappings.length === 0}>
          <Trans i18nKey="integrations:mapping.saveAll">Save All Mappings</Trans>
        </Button>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingMapping && editingMapping.id.startsWith('mapping-') ? (
                <Trans i18nKey="integrations:mapping.addMapping">
                  Add Mapping
                </Trans>
              ) : (
                <Trans i18nKey="integrations:mapping.editMapping">
                  Edit Mapping
                </Trans>
              )}
            </DialogTitle>
            <DialogDescription>
              <Trans i18nKey="integrations:mapping.dialogDescription">
                Define how fields from {platform} map to your system.
              </Trans>
            </DialogDescription>
          </DialogHeader>

          {editingMapping && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label
                  htmlFor="source_field"
                  className="text-right"
                >
                  <Trans i18nKey="integrations:mapping.sourceField">
                    Source Field
                  </Trans>
                </Label>
                <div className="col-span-3">
                  <Select
                    value={editingMapping.source_field}
                    onValueChange={(value) =>
                      setEditingMapping({
                        ...editingMapping,
                        source_field: value,
                      })
                    }
                  >
                    <SelectTrigger id="source_field">
                      <SelectValue
                        placeholder={t('integrations:mapping.selectSourceField')}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {sourceFields.map((field) => (
                        <SelectItem key={field.name} value={field.name}>
                          {field.name}{' '}
                          {field.type && (
                            <span className="text-muted-foreground">
                              ({field.type})
                            </span>
                          )}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label
                  htmlFor="target_field"
                  className="text-right"
                >
                  <Trans i18nKey="integrations:mapping.targetField">
                    Target Field
                  </Trans>
                </Label>
                <div className="col-span-3">
                  <Select
                    value={editingMapping.target_field}
                    onValueChange={(value) =>
                      setEditingMapping({
                        ...editingMapping,
                        target_field: value,
                      })
                    }
                  >
                    <SelectTrigger id="target_field">
                      <SelectValue
                        placeholder={t('integrations:mapping.selectTargetField')}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {targetFields.map((field) => (
                        <SelectItem key={field.name} value={field.name}>
                          {field.name}{' '}
                          {field.type && (
                            <span className="text-muted-foreground">
                              ({field.type})
                            </span>
                          )}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label
                  htmlFor="is_required"
                  className="text-right"
                >
                  <Trans i18nKey="integrations:mapping.required">
                    Required
                  </Trans>
                </Label>
                <div className="col-span-3">
                  <Select
                    value={editingMapping.is_required ? 'true' : 'false'}
                    onValueChange={(value) =>
                      setEditingMapping({
                        ...editingMapping,
                        is_required: value === 'true',
                      })
                    }
                  >
                    <SelectTrigger id="is_required">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">
                        <Trans i18nKey="common:yes">Yes</Trans>
                      </SelectItem>
                      <SelectItem value="false">
                        <Trans i18nKey="common:no">No</Trans>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-4 items-start gap-4">
                <Label
                  htmlFor="transform_function"
                  className="text-right pt-2"
                >
                  <Trans i18nKey="integrations:mapping.transformFunction">
                    Transform
                  </Trans>
                </Label>
                <div className="col-span-3">
                  <Textarea
                    id="transform_function"
                    value={editingMapping.transform_function || ''}
                    onChange={(e) =>
                      setEditingMapping({
                        ...editingMapping,
                        transform_function: e.target.value,
                      })
                    }
                    placeholder="return value.toString();"
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    <Trans i18nKey="integrations:mapping.transformDescription">
                      Optional JavaScript function to transform the value. Use
                      "value" as the input parameter.
                    </Trans>
                  </p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsDialogOpen(false);
                setEditingMapping(null);
              }}
            >
              <Trans i18nKey="common:cancel">Cancel</Trans>
            </Button>
            <Button onClick={handleSaveMapping}>
              <Trans i18nKey="common:save">Save</Trans>
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface SortableItemProps {
  id: string;
  mapping: FieldMapping;
  sourceFields: SourceField[];
  targetFields: TargetField[];
  onEdit: (mapping: FieldMapping) => void;
  onDelete: (id: string) => void;
}

function SortableItem({
  id,
  mapping,
  sourceFields,
  targetFields,
  onEdit,
  onDelete,
}: SortableItemProps) {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Tìm thông tin field
  const sourceField = sourceFields.find((f) => f.name === mapping.source_field);
  const targetField = targetFields.find((f) => f.name === mapping.target_field);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center space-x-2 p-3 border rounded-md bg-card hover:bg-accent/5 cursor-move"
    >
      <div className="flex-1 grid grid-cols-2 gap-4">
        <div>
          <div className="font-medium">{mapping.source_field}</div>
          {sourceField && sourceField.type && (
            <div className="text-xs text-muted-foreground">
              {sourceField.type}
            </div>
          )}
        </div>
        <div>
          <div className="font-medium">{mapping.target_field}</div>
          {targetField && targetField.type && (
            <div className="text-xs text-muted-foreground">
              {targetField.type}
            </div>
          )}
        </div>
      </div>
      <div className="flex items-center space-x-2">
        {mapping.is_required && (
          <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
            Required
          </span>
        )}
        {mapping.transform_function && (
          <span className="text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded">
            Transform
          </span>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onEdit(mapping)}
          className="h-8 w-8 p-0"
        >
          <span className="sr-only">Edit</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-4 w-4"
          >
            <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
            <path d="m15 5 4 4" />
          </svg>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDelete(mapping.id)}
          className="h-8 w-8 p-0 text-destructive"
        >
          <span className="sr-only">Delete</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-4 w-4"
          >
            <path d="M3 6h18" />
            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
            <line x1="10" x2="10" y1="11" y2="17" />
            <line x1="14" x2="14" y1="11" y2="17" />
          </svg>
        </Button>
        <div
          {...attributes}
          {...listeners}
          className="h-8 w-8 flex items-center justify-center cursor-grab"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-4 w-4"
          >
            <circle cx="9" cy="5" r="1" />
            <circle cx="9" cy="12" r="1" />
            <circle cx="9" cy="19" r="1" />
            <circle cx="15" cy="5" r="1" />
            <circle cx="15" cy="12" r="1" />
            <circle cx="15" cy="19" r="1" />
          </svg>
        </div>
      </div>
    </div>
  );
}
