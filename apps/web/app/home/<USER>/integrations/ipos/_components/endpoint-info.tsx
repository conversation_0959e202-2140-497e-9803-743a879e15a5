'use client';

import { ReactNode } from 'react';
import { ExternalLink } from 'lucide-react';
import { apiEndpoints } from '../_lib/field-descriptions';

interface EndpointInfoProps {
  resourceType: string;
  className?: string;
}

/**
 * Component hiển thị thông tin endpoint cho từng loại resource
 */
export function EndpointInfo({ resourceType, className }: EndpointInfoProps) {
  // Lấy endpoint cho resource type
  const endpoints = apiEndpoints[resourceType] || {};

  // Xác định endpoint chính dựa trên resource type
  let mainEndpoint = '';
  let endpointName = '';
  switch (resourceType) {
    case 'products':
    case 'categories':
    case 'branches':
      mainEndpoint = endpoints.list || '';
      endpointName = 'list';
      break;
    case 'customers':
      mainEndpoint = endpoints.detail || '';
      endpointName = 'detail';
      break;
    case 'orders':
      mainEndpoint = endpoints.create || '';
      endpointName = 'create';
      break;
    case 'vouchers':
      mainEndpoint = endpoints.list || '';
      endpointName = 'list';
      break;
    default:
      const firstKey = Object.keys(endpoints)[0] || '';
      mainEndpoint = endpoints[firstKey] || '';
      endpointName = firstKey;
  }

  // Tạo URL đầy đủ
  const fullUrl = `https://api.foodbook.vn${mainEndpoint}`;

  return (
    <div className={`flex flex-wrap items-center text-sm text-muted-foreground ${className}`}>
      <span className="mr-1">Endpoint ({endpointName}):</span>
      <a
        href={fullUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="inline-flex items-center hover:text-primary hover:underline break-all"
        title={fullUrl}
      >
        <span className="truncate max-w-[250px]">{fullUrl}</span>
        <ExternalLink className="ml-1 h-3 w-3 flex-shrink-0" />
      </a>
    </div>
  );
}
