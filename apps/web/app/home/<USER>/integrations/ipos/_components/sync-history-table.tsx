'use client';

import { useRouter } from 'next/navigation';

import { useTranslation } from 'react-i18next';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@kit/ui/table';
import { Trans } from '@kit/ui/trans';

interface SyncHistoryTableProps {
  history: any[];
  accountId: string;
}

export function SyncHistoryTable({
  history,
  accountId,
}: SyncHistoryTableProps) {
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);

  // Xử lý khi click vào một dòng
  const handleRowClick = (syncLogId: string) => {
    router.push(`/home/<USER>/integrations/ipos/sync-history/${syncLogId}`);
  };

  // Format thời gian
  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString();
  };

  // Format trạng thái
  const formatStatus = (status: string) => {
    switch (status) {
      case 'success':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <Trans i18nKey="integrations:sync.status.success">Success</Trans>
          </span>
        );
      case 'error':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <Trans i18nKey="integrations:sync.status.error">Error</Trans>
          </span>
        );
      case 'partial':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Trans i18nKey="integrations:sync.status.partial">Partial</Trans>
          </span>
        );
      case 'in_progress':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <Trans i18nKey="integrations:sync.status.inProgress">
              In Progress
            </Trans>
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              <Trans i18nKey="integrations:sync.history.resourceType">
                Resource Type
              </Trans>
            </TableHead>
            <TableHead>
              <Trans i18nKey="integrations:sync.history.status">Status</Trans>
            </TableHead>
            <TableHead>
              <Trans i18nKey="integrations:sync.history.items">Items</Trans>
            </TableHead>
            <TableHead>
              <Trans i18nKey="integrations:sync.history.startedAt">
                Started At
              </Trans>
            </TableHead>
            <TableHead>
              <Trans i18nKey="integrations:sync.history.completedAt">
                Completed At
              </Trans>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {history.map((log) => (
            <TableRow
              key={log.id}
              onClick={() => handleRowClick(log.id)}
              className="cursor-pointer hover:bg-muted/50"
            >
              <TableCell className="font-medium capitalize">
                {log.resource_type}
              </TableCell>
              <TableCell>{formatStatus(log.status)}</TableCell>
              <TableCell>
                {log.items_processed !== null
                  ? `${log.items_created || 0}/${log.items_processed}`
                  : '-'}
              </TableCell>
              <TableCell>{formatDate(log.started_at)}</TableCell>
              <TableCell>{formatDate(log.completed_at)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
