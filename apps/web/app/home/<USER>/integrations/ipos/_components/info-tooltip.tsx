'use client';

import { ReactNode } from 'react';
import { AlertCircle, Info } from 'lucide-react';

interface InfoTooltipProps {
  content: ReactNode;
  type?: 'info' | 'warning';
  className?: string;
}

/**
 * A tooltip component that shows information on hover
 */
export function InfoTooltip({ content, type = 'info', className = '' }: InfoTooltipProps) {
  const Icon = type === 'info' ? Info : AlertCircle;
  const iconColor = type === 'info' ? 'text-muted-foreground' : 'text-amber-500';

  const handleMouseEnter = (e: React.MouseEvent<SVGSVGElement>) => {
    const tooltip = e.currentTarget.nextElementSibling as HTMLElement;
    if (tooltip) {
      tooltip.classList.remove('opacity-0', 'invisible');
      tooltip.classList.add('opacity-100', 'visible');
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<SVGSVGElement>) => {
    const tooltip = e.currentTarget.nextElementSibling as HTMLElement;
    if (tooltip) {
      tooltip.classList.add('opacity-0', 'invisible');
      tooltip.classList.remove('opacity-100', 'visible');
    }
  };

  return (
    <span className={`inline-block relative ${className}`}>
      <Icon
        className={`h-3.5 w-3.5 ${iconColor} cursor-help`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      />
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 p-2 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 invisible transition-opacity z-50">
        {content}
      </div>
    </span>
  );
}
