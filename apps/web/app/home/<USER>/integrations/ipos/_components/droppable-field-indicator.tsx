'use client';

import { ArrowDown } from 'lucide-react';
import { cn } from '@kit/ui/utils';

interface DroppableFieldIndicatorProps {
  isOver: boolean;
  className?: string;
  sourceField?: string;
  targetField?: string;
}

/**
 * Component hiển thị chỉ báo khi kéo qua trường đích
 */
export function DroppableFieldIndicator({
  isOver,
  className,
  sourceField,
  targetField,
}: DroppableFieldIndicatorProps) {
  if (!isOver) return null;

  return (
    <div
      className={cn(
        'absolute inset-0 rounded-md border-2 border-primary bg-primary/20 shadow-inner pointer-events-none z-10',
        'animate-[pulse_1s_ease-in-out_infinite] flex items-center justify-center',
        className
      )}
      style={{
        boxShadow: 'inset 0 0 15px rgba(var(--color-primary-rgb), 0.3)',
      }}
    >
      <div className="flex flex-col items-center justify-center gap-1 text-primary font-medium">
        <ArrowDown className="h-5 w-5 animate-bounce" />
        <span className="text-xs bg-primary/10 px-2 py-0.5 rounded-full">
          {sourceField ? `Map "${sourceField}" here` : 'Drop Here'}
        </span>
      </div>
    </div>
  );
}
