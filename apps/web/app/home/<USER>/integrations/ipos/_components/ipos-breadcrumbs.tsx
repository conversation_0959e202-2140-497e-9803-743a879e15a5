'use client';

import { Fragment } from 'react';
import { usePathname, useRouter } from 'next/navigation';

import {
  Breadcrumb,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@kit/ui/breadcrumb';
import { If } from '@kit/ui/if';
import { Trans } from '@kit/ui/trans';

const unslugify = (slug: string) => slug.replace(/-/g, ' ');

export function IPOSBreadcrumbs(props: {
  values?: Record<string, string>;
  maxDepth?: number;
  accountSlug: string;
}) {
  const pathName = usePathname();
  const router = useRouter();
  const splitPath = pathName.split('/').filter(Boolean);
  const values = props.values ?? {};
  const maxDepth = props.maxDepth ?? 6;
  const { accountSlug } = props;

  const Ellipsis = (
    <BreadcrumbItem>
      <BreadcrumbEllipsis className="h-4 w-4" />
    </BreadcrumbItem>
  );

  const showEllipsis = splitPath.length > maxDepth;

  const visiblePaths = showEllipsis
    ? ([splitPath[0], ...splitPath.slice(-maxDepth + 1)] as string[])
    : splitPath;

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {visiblePaths.map((path, index) => {
          const label =
            path in values ? (
              values[path]
            ) : (
              <Trans
                i18nKey={`common:routes.${unslugify(path)}`}
                defaults={unslugify(path)}
              />
            );

          // Xử lý đặc biệt cho breadcrumb IPOS
          const isIPOS = path === 'ipos';
          const href = isIPOS 
            ? `/home/<USER>/integrations/ipos/dashboard`
            : '/' + splitPath.slice(0, splitPath.indexOf(path) + 1).join('/');

          return (
            <Fragment key={index}>
              <BreadcrumbItem className={'capitalize lg:text-xs'}>
                <If
                  condition={index < visiblePaths.length - 1}
                  fallback={label}
                >
                  <BreadcrumbLink
                    href={href}
                    onClick={(e) => {
                      if (isIPOS) {
                        e.preventDefault();
                        router.push(href);
                      }
                    }}
                  >
                    {label}
                  </BreadcrumbLink>
                </If>
              </BreadcrumbItem>

              {index === 0 && showEllipsis && (
                <>
                  <BreadcrumbSeparator />
                  {Ellipsis}
                </>
              )}

              <If condition={index !== visiblePaths.length - 1}>
                <BreadcrumbSeparator />
              </If>
            </Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
