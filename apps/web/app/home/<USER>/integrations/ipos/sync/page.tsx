'use client';

import { useEffect, useState } from 'react';

import { useParams, useRouter } from 'next/navigation';

import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Label } from '@kit/ui/label';
import { PageBody } from '@kit/ui/page';
import { Progress } from '@kit/ui/progress';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { IPOSBreadcrumbs } from '../_components/ipos-breadcrumbs';
import { syncIPOSData } from '../_lib/server/ipos-mapping-actions';

export default function IPOSSyncPage() {
  const { account } = useParams();
  const router = useRouter();
  const { t } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();

  const [resourceType, setResourceType] = useState<
    'products' | 'orders' | 'customers' | 'branches' | 'categories' | 'vouchers'
  >('products');
  const [isLoading, setIsLoading] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncProgress, setSyncProgress] = useState(0);
  const [integrationId, setIntegrationId] = useState<string | null>(null);
  const [hasMappings, setHasMappings] = useState<Record<string, boolean>>({
    products: false,
    orders: false,
    customers: false,
    branches: false,
    categories: false,
    vouchers: false,
  });
  const [syncResult, setSyncResult] = useState<{
    success: boolean;
    syncLogId: string;
    total: number;
    created: number;
    failed: number;
  } | null>(null);

  // Lấy thông tin integration và mappings
  useEffect(() => {
    const fetchIntegrationInfo = async () => {
      setIsLoading(true);
      try {
        // Lấy integration ID
        const { data: integration, error: integrationError } = await supabase
          .from('integrations')
          .select('id')
          .eq('account_id', account)
          .eq('type', 'ipos')
          .single();

        if (integrationError) {
          toast.error(t('integrations:ipos.sync.integrationNotFound'), {
            description: t('integrations:ipos.sync.connectFirst'),
          });
          router.push(`/home/<USER>/integrations/ipos/connect`);
          return;
        }

        setIntegrationId(integration.id);

        // Kiểm tra mappings cho từng loại resource
        const resourceTypes = ['products', 'orders', 'customers', 'branches', 'categories', 'vouchers'];
        const mappingsStatus: Record<string, boolean> = {};

        // Lấy tất cả mappings cho integration này
        const { data: allMappings, error: mappingsError } = await supabase
          .from('integration_mappings')
          .select('resource_type, is_active')
          .eq('integration_id', integration.id)
          .eq('is_active', true);

        if (!mappingsError && allMappings) {
          // Tạo map để đếm số lượng mapping cho mỗi loại resource
          const mappingCounts: Record<string, number> = {};

          // Đếm số lượng mapping cho mỗi loại resource
          allMappings.forEach((mapping) => {
            mappingCounts[mapping.resource_type] = (mappingCounts[mapping.resource_type] || 0) + 1;
          });

          // Cập nhật trạng thái mapping cho tất cả các loại resource
          resourceTypes.forEach((type) => {
            mappingsStatus[type] = (mappingCounts[type] || 0) > 0;
          });
        }

        setHasMappings(mappingsStatus);
      } catch (error) {
        console.error('Error fetching integration info:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (account) {
      fetchIntegrationInfo();
    }
  }, [account, supabase, router, t]);

  // Xử lý đồng bộ dữ liệu
  const handleSync = async () => {
    if (!integrationId) return;

    setIsSyncing(true);
    setSyncProgress(10);
    setSyncResult(null);

    try {
      // Lấy user ID
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Cập nhật progress
      setSyncProgress(30);

      // Bắt đầu đồng bộ
      const result = await syncIPOSData(integrationId, resourceType, user.id);

      // Cập nhật progress
      setSyncProgress(100);

      // Lưu kết quả
      setSyncResult(result);

      toast.success(t('integrations:ipos.sync.syncSuccess'), {
        description: t('integrations:ipos.sync.syncSuccessDescription', {
          total: result.total,
          created: result.created,
          failed: result.failed,
        }),
      });
    } catch (error: any) {
      toast.error(t('integrations:ipos.sync.syncError'), {
        description: error.message || t('common:somethingWentWrong'),
      });
      setSyncProgress(0);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={
          <Trans i18nKey="integrations:ipos.sync.title">Sync iPOS Data</Trans>
        }
        description={<IPOSBreadcrumbs accountSlug={account as string} />}
        account={account as string}
      />

      <PageBody>
        <Card className="mx-auto max-w-3xl">
          <CardHeader>
            <CardTitle>
              <Trans i18nKey="integrations:ipos.sync.title">
                Sync iPOS Data
              </Trans>
            </CardTitle>
            <CardDescription className="max-w-[600px] break-words">
              <Trans i18nKey="integrations:ipos.sync.description">
                Synchronize data from iPOS to your system.
              </Trans>
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex h-40 items-center justify-center">
                <div className="text-center">
                  <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div>
                  <p className="text-muted-foreground mt-2 text-sm">
                    <Trans i18nKey="integrations:ipos.sync.loading">
                      Loading...
                    </Trans>
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Resource Type Selection */}
                <div className="space-y-4">
                  <h3 className="text-base font-medium">
                    <Trans i18nKey="integrations:ipos.sync.selectResource">
                      Select Resource Type
                    </Trans>
                  </h3>
                  <RadioGroup
                    value={resourceType}
                    onValueChange={(value) => setResourceType(value as any)}
                    className="grid grid-cols-1 gap-4 md:grid-cols-3"
                    data-testid="resource-type-radio-group"
                  >
                    {/* Products */}
                    <div
                      className={`relative rounded-md border p-4 ${!hasMappings.products ? 'opacity-50' : ''}`}
                    >
                      <RadioGroupItem
                        value="products"
                        id="products"
                        disabled={!hasMappings.products}
                        data-testid="products-radio"
                      />
                      <Label
                        htmlFor="products"
                        className="flex cursor-pointer flex-col space-y-1"
                      >
                        <span className="font-medium">
                          <Trans i18nKey="integrations:ipos.sync.products">
                            Products
                          </Trans>
                        </span>
                        <span className="text-muted-foreground text-sm">
                          <Trans i18nKey="integrations:ipos.sync.productsDescription">
                            Sync product data from iPOS
                          </Trans>
                        </span>
                      </Label>
                      {!hasMappings.products && (
                        <div className="bg-background/50 absolute inset-0 flex items-center justify-center rounded-md">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/ipos/mapping?resource=products`,
                              )
                            }
                            data-testid="setup-products-mapping-button"
                          >
                            <Trans i18nKey="integrations:ipos.sync.setupMapping">
                              Setup Mapping
                            </Trans>
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Orders */}
                    <div
                      className={`relative rounded-md border p-4 ${!hasMappings.orders ? 'opacity-50' : ''}`}
                    >
                      <RadioGroupItem
                        value="orders"
                        id="orders"
                        disabled={!hasMappings.orders}
                        data-testid="orders-radio"
                      />
                      <Label
                        htmlFor="orders"
                        className="flex cursor-pointer flex-col space-y-1"
                      >
                        <span className="font-medium">
                          <Trans i18nKey="integrations:ipos.sync.orders">
                            Orders
                          </Trans>
                        </span>
                        <span className="text-muted-foreground text-sm">
                          <Trans i18nKey="integrations:ipos.sync.ordersDescription">
                            Sync order data from iPOS
                          </Trans>
                        </span>
                      </Label>
                      {!hasMappings.orders && (
                        <div className="bg-background/50 absolute inset-0 flex items-center justify-center rounded-md">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/ipos/mapping?resource=orders`,
                              )
                            }
                            data-testid="setup-orders-mapping-button"
                          >
                            <Trans i18nKey="integrations:ipos.sync.setupMapping">
                              Setup Mapping
                            </Trans>
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Customers */}
                    <div
                      className={`relative rounded-md border p-4 ${!hasMappings.customers ? 'opacity-50' : ''}`}
                    >
                      <RadioGroupItem
                        value="customers"
                        id="customers"
                        disabled={!hasMappings.customers}
                        data-testid="customers-radio"
                      />
                      <Label
                        htmlFor="customers"
                        className="flex cursor-pointer flex-col space-y-1"
                      >
                        <span className="font-medium">
                          <Trans i18nKey="integrations:ipos.sync.customers">
                            Customers
                          </Trans>
                        </span>
                        <span className="text-muted-foreground text-sm">
                          <Trans i18nKey="integrations:ipos.sync.customersDescription">
                            Sync customer data from iPOS
                          </Trans>
                        </span>
                      </Label>
                      {!hasMappings.customers && (
                        <div className="bg-background/50 absolute inset-0 flex items-center justify-center rounded-md">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/ipos/mapping?resource=customers`,
                              )
                            }
                            data-testid="setup-customers-mapping-button"
                          >
                            <Trans i18nKey="integrations:ipos.sync.setupMapping">
                              Setup Mapping
                            </Trans>
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Branches */}
                    <div
                      className={`relative rounded-md border p-4 ${!hasMappings.branches ? 'opacity-50' : ''}`}
                    >
                      <RadioGroupItem
                        value="branches"
                        id="branches"
                        disabled={!hasMappings.branches}
                        data-testid="branches-radio"
                      />
                      <Label
                        htmlFor="branches"
                        className="flex cursor-pointer flex-col space-y-1"
                      >
                        <span className="font-medium">
                          <Trans i18nKey="integrations:ipos.sync.branches">
                            Branches
                          </Trans>
                        </span>
                        <span className="text-muted-foreground text-sm">
                          <Trans i18nKey="integrations:ipos.sync.branchesDescription">
                            Sync branch data from iPOS
                          </Trans>
                        </span>
                      </Label>
                      {!hasMappings.branches && (
                        <div className="bg-background/50 absolute inset-0 flex items-center justify-center rounded-md">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/ipos/mapping?resource=branches`,
                              )
                            }
                            data-testid="setup-branches-mapping-button"
                          >
                            <Trans i18nKey="integrations:ipos.sync.setupMapping">
                              Setup Mapping
                            </Trans>
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Categories */}
                    <div
                      className={`relative rounded-md border p-4 ${!hasMappings.categories ? 'opacity-50' : ''}`}
                    >
                      <RadioGroupItem
                        value="categories"
                        id="categories"
                        disabled={!hasMappings.categories}
                        data-testid="categories-radio"
                      />
                      <Label
                        htmlFor="categories"
                        className="flex cursor-pointer flex-col space-y-1"
                      >
                        <span className="font-medium">
                          <Trans i18nKey="integrations:ipos.sync.categories">
                            Categories
                          </Trans>
                        </span>
                        <span className="text-muted-foreground text-sm">
                          <Trans i18nKey="integrations:ipos.sync.categoriesDescription">
                            Sync category data from iPOS
                          </Trans>
                        </span>
                      </Label>
                      {!hasMappings.categories && (
                        <div className="bg-background/50 absolute inset-0 flex items-center justify-center rounded-md">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/ipos/mapping?resource=categories`,
                              )
                            }
                            data-testid="setup-categories-mapping-button"
                          >
                            <Trans i18nKey="integrations:ipos.sync.setupMapping">
                              Setup Mapping
                            </Trans>
                          </Button>
                        </div>
                      )}
                    </div>

                    {/* Vouchers */}
                    <div
                      className={`relative rounded-md border p-4 ${!hasMappings.vouchers ? 'opacity-50' : ''}`}
                    >
                      <RadioGroupItem
                        value="vouchers"
                        id="vouchers"
                        disabled={!hasMappings.vouchers}
                        data-testid="vouchers-radio"
                      />
                      <Label
                        htmlFor="vouchers"
                        className="flex cursor-pointer flex-col space-y-1"
                      >
                        <span className="font-medium">
                          <Trans i18nKey="integrations:ipos.sync.vouchers">
                            Vouchers
                          </Trans>
                        </span>
                        <span className="text-muted-foreground text-sm">
                          <Trans i18nKey="integrations:ipos.sync.vouchersDescription">
                            Sync voucher data from iPOS
                          </Trans>
                        </span>
                      </Label>
                      {!hasMappings.vouchers && (
                        <div className="bg-background/50 absolute inset-0 flex items-center justify-center rounded-md">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              router.push(
                                `/home/<USER>/integrations/ipos/mapping?resource=vouchers`,
                              )
                            }
                            data-testid="setup-vouchers-mapping-button"
                          >
                            <Trans i18nKey="integrations:ipos.sync.setupMapping">
                              Setup Mapping
                            </Trans>
                          </Button>
                        </div>
                      )}
                    </div>
                  </RadioGroup>
                </div>

                {/* Sync Progress */}
                {isSyncing && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        <Trans i18nKey="integrations:ipos.sync.syncing">
                          Syncing...
                        </Trans>
                      </span>
                      <span className="text-muted-foreground text-sm">
                        {syncProgress}%
                      </span>
                    </div>
                    <Progress
                      value={syncProgress}
                      className="h-2"
                      data-testid="sync-progress"
                    />
                  </div>
                )}

                {/* Sync Result */}
                {syncResult && (
                  <Alert className="border-green-200 bg-green-50">
                    <AlertTitle className="text-green-800">
                      <Trans i18nKey="integrations:ipos.sync.syncComplete">
                        Sync Complete
                      </Trans>
                    </AlertTitle>
                    <AlertDescription className="text-green-700">
                      <Trans
                        i18nKey="integrations:ipos.sync.syncResultDescription"
                        values={{
                          total: syncResult.total,
                          created: syncResult.created,
                          failed: syncResult.failed,
                        }}
                      >
                        Processed {syncResult.total} items: {syncResult.created}{' '}
                        created/updated, {syncResult.failed} failed.
                      </Trans>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Actions */}
                <div className="flex justify-between pt-4">
                  <Button
                    variant="outline"
                    onClick={() =>
                      router.push(
                        `/home/<USER>/integrations/ipos/sync-history`,
                      )
                    }
                    data-testid="view-history-button"
                  >
                    <Trans i18nKey="integrations:ipos.sync.viewHistory">
                      View Sync History
                    </Trans>
                  </Button>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={() =>
                        router.push(
                          `/home/<USER>/integrations/ipos/mapping`,
                        )
                      }
                      data-testid="edit-mapping-button"
                    >
                      <Trans i18nKey="integrations:ipos.sync.editMapping">
                        Edit Mapping
                      </Trans>
                    </Button>
                    <Button
                      onClick={handleSync}
                      disabled={
                        isSyncing ||
                        !integrationId ||
                        (resourceType === 'products' &&
                          !hasMappings.products) ||
                        (resourceType === 'orders' && !hasMappings.orders) ||
                        (resourceType === 'customers' && !hasMappings.customers)
                      }
                      data-testid="start-sync-button"
                    >
                      {isSyncing ? (
                        <Trans i18nKey="integrations:ipos.sync.syncing">
                          Syncing...
                        </Trans>
                      ) : (
                        <Trans i18nKey="integrations:ipos.sync.startSync">
                          Start Sync
                        </Trans>
                      )}
                    </Button>
                  </div>
                </div>

                {/* View Details Button */}
                {syncResult && (
                  <div className="flex justify-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() =>
                        router.push(
                          `/home/<USER>/integrations/ipos/sync-history/${syncResult.syncLogId}`,
                        )
                      }
                      data-testid="view-details-button"
                    >
                      <Trans i18nKey="integrations:ipos.sync.viewDetails">
                        View Sync Details
                      </Trans>
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </PageBody>
    </>
  );
}
