'use client';

import { useEffect, useState } from 'react';

import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';

import {
  DragDropContext,
  DragStart,
  Draggable,
  Droppable,
} from '@hello-pangea/dnd';
import {
  AlertCircle,
  ArrowDown as ArrowDownIcon,
  ArrowRight as ArrowRightIcon,
  Eye as EyeIcon,
  GripVertical,
  Info,
  Lock,
  Lock as LockIcon,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

import { isUUID } from '@kit/shared/uuid';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Label } from '@kit/ui/label';
import { PageBody } from '@kit/ui/page';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import '../_components/animations.css';
import { DroppableFieldIndicator } from '../_components/droppable-field-indicator';
import { EndpointInfo } from '../_components/endpoint-info';
import { InfoTooltip } from '../_components/info-tooltip';
import { IPOSBreadcrumbs } from '../_components/ipos-breadcrumbs';
import {
  avoidDirectIdMapping,
  defaultMappings,
  fieldDescriptions,
  recommendedMappings,
  recommendedSyncFields,
  requiredFields,
} from '../_lib/field-descriptions';
import {
  getIPOSFields,
  saveIPOSMapping,
  syncIPOSData,
  testIPOSMapping,
} from '../_lib/server/ipos-mapping-actions';
import { MappedField } from './MappedField';

interface MappingField {
  id: string;
  name: string;
  description?: string;
  isRequired?: boolean;
  isRecommendedForSync?: boolean;
  avoidDirectMapping?: boolean;
}

export default function IPOSMappingPage() {
  const { account } = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { t } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();

  // Get resource type from URL query parameter
  const resourceParam = searchParams.get('resource');

  const [resourceType, setResourceType] = useState<
    'products' | 'orders' | 'customers' | 'branches' | 'categories' | 'vouchers'
  >('products');

  // Set resource type based on URL parameter
  useEffect(() => {
    if (resourceParam) {
      const validResourceTypes = [
        'products',
        'orders',
        'customers',
        'branches',
        'categories',
        'vouchers',
      ];
      if (validResourceTypes.includes(resourceParam)) {
        setResourceType(resourceParam as any);
      }
    }
  }, [resourceParam]);
  const [sourceFields, setSourceFields] = useState<MappingField[]>([]);
  const [targetFields, setTargetFields] = useState<MappingField[]>([]);
  const [mappings, setMappings] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);
  const [integrationId, setIntegrationId] = useState<string | null>(null);
  const [previewData, setPreviewData] = useState<any[]>([]);
  const [activeDragId, setActiveDragId] = useState<string | null>(null);
  const [activeSourceField, setActiveSourceField] = useState<string | null>(
    null,
  );
  const [activeTargetField, setActiveTargetField] = useState<string | null>(
    null,
  );

  // Lấy danh sách trường và mapping hiện tại
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Kiểm tra xem account có phải là UUID không
        let accountId = account;

        // Nếu không phải UUID, lấy ID từ slug
        if (!isUUID(account as string)) {
          const { data: accountData, error: accountError } = await supabase
            .from('accounts')
            .select('id')
            .eq('slug', account)
            .single();

          if (accountError) {
            toast.error(t('integrations:ipos.mapping.accountNotFound'), {
              description: t('common:somethingWentWrong'),
            });
            router.push(`/home/<USER>/integrations`);
            return;
          }

          accountId = accountData.id;
        }

        // Lấy integration ID
        const { data: integration, error: integrationError } = await supabase
          .from('integrations')
          .select('id')
          .eq('account_id', accountId)
          .eq('type', 'ipos')
          .single();

        if (integrationError) {
          toast.error(t('integrations:ipos.mapping.integrationNotFound'), {
            description: t('integrations:ipos.mapping.connectFirst'),
          });
          router.push(`/home/<USER>/integrations/ipos/connect`);
          return;
        }

        setIntegrationId(integration.id);

        // Lấy danh sách trường từ iPOS và hệ thống
        const result = await getIPOSFields(integration.id, resourceType);

        // Chuyển đổi danh sách trường thành định dạng phù hợp và thêm mô tả
        const sourceFieldsList = result.sourceFields.map((field) => {
          const description = fieldDescriptions[resourceType]?.[field] || '';
          const isRecommendedForSync =
            recommendedSyncFields[resourceType]?.includes(field) || false;
          const isRecommended = recommendedMappings[resourceType]
            ? Object.keys(recommendedMappings[resourceType]).includes(field)
            : false;
          const isDefault = defaultMappings[resourceType]
            ? Object.keys(defaultMappings[resourceType]).includes(field)
            : false;
          const isSystemField =
            field === 'id' ||
            field === 'external_id' ||
            field === 'source' ||
            field === 'last_synced_at' ||
            field === 'integration_data';

          return {
            id: field,
            name: field,
            description,
            isRecommendedForSync,
            isRecommended,
            isDefault,
            isSystemField,
          };
        });

        const targetFieldsList = result.targetFields.map((field) => {
          const isRequired =
            requiredFields[resourceType]?.includes(field) || false;
          const avoidDirectMapping =
            avoidDirectIdMapping[resourceType]?.includes(field) || false;
          const isRecommended = recommendedMappings[resourceType]
            ? Object.values(recommendedMappings[resourceType]).includes(field)
            : false;
          const isDefault = defaultMappings[resourceType]
            ? Object.values(defaultMappings[resourceType]).includes(field)
            : false;
          const isSystemField =
            field === 'external_id' ||
            field === 'source' ||
            field === 'last_synced_at' ||
            field === 'integration_data';
          const description =
            field === 'external_id'
              ? 'ID của bản ghi trong hệ thống bên thứ 3 (iPOS).'
              : field === 'source'
                ? 'Nguồn gốc của bản ghi (iPOS, manual, v.v.).'
                : field === 'last_synced_at'
                  ? 'Thời gian đồng bộ lần cuối.'
                  : field === 'integration_data'
                    ? 'Dữ liệu từ hệ thống bên thứ 3 dạng JSON.'
                    : '';

          return {
            id: field,
            name: field,
            isRequired,
            avoidDirectMapping,
            isRecommended,
            isDefault,
            isSystemField,
            description,
          };
        });

        setSourceFields(sourceFieldsList);
        setTargetFields(targetFieldsList);

        // Lấy mapping hiện có hoặc tạo mới
        const existingMappings = result.mappings || {};

        // Tạo mapping mới bao gồm cả default mappings
        const newMappings = { ...existingMappings };

        // Luôn áp dụng default mappings
        if (defaultMappings[resourceType]) {
          Object.entries(defaultMappings[resourceType]).forEach(
            ([sourceField, targetField]) => {
              // Kiểm tra xem source field có tồn tại trong sourceFieldsList không
              const sourceFieldExists = sourceFieldsList.some(
                (field) => field.id === sourceField,
              );
              // Kiểm tra xem target field có tồn tại trong targetFieldsList không
              const targetFieldExists = targetFieldsList.some(
                (field) => field.id === targetField,
              );

              if (sourceFieldExists && targetFieldExists) {
                // Đảo ngược mapping từ source -> target sang target -> source
                newMappings[targetField] = sourceField;
              }
            },
          );
        }

        // Nếu chưa có mapping nào (ngoài default), tự động áp dụng recommended mapping
        if (
          Object.keys(existingMappings).length === 0 &&
          recommendedMappings[resourceType]
        ) {
          // Áp dụng recommended mappings
          Object.entries(recommendedMappings[resourceType]).forEach(
            ([sourceField, targetField]) => {
              // Kiểm tra xem source field có tồn tại trong sourceFieldsList không
              const sourceFieldExists = sourceFieldsList.some(
                (field) => field.id === sourceField,
              );
              // Kiểm tra xem target field có tồn tại trong targetFieldsList không
              const targetFieldExists = targetFieldsList.some(
                (field) => field.id === targetField,
              );
              // Kiểm tra xem target field đã được mapping chưa
              const targetFieldMapped =
                Object.keys(newMappings).includes(targetField);

              if (
                sourceFieldExists &&
                targetFieldExists &&
                !targetFieldMapped
              ) {
                newMappings[targetField] = sourceField;
              }
            },
          );

          // Hiển thị thông báo
          toast.info(
            t('integrations:ipos.mapping.recommendedMappingsApplied') ||
              'Default and recommended mappings applied automatically',
          );
        }

        // Tự động mapping các trường required (trừ trường sku)
        targetFieldsList.forEach((targetField) => {
          if (
            targetField.isRequired &&
            !newMappings[targetField.id] &&
            targetField.id !== 'sku'
          ) {
            // Tìm source field phù hợp từ recommended mappings
            let sourceFieldToMap = null;

            // 1. Đầu tiên tìm trong recommended mappings
            if (recommendedMappings[resourceType]) {
              const recommendedSourceField = Object.entries(
                recommendedMappings[resourceType],
              ).find(([_, tgt]) => tgt === targetField.id)?.[0];

              if (recommendedSourceField) {
                const sourceFieldExists = sourceFieldsList.some(
                  (field) => field.id === recommendedSourceField,
                );

                const sourceFieldMapped = Object.values(newMappings).includes(
                  recommendedSourceField,
                );

                if (
                  sourceFieldExists &&
                  !sourceFieldMapped &&
                  sourceFieldsList.find((f) => f.id === recommendedSourceField)
                    ?.name !== 'source'
                ) {
                  sourceFieldToMap = recommendedSourceField;
                }
              }
            }

            // 2. Nếu không tìm thấy trong recommended, tìm source field có tên tương tự
            if (!sourceFieldToMap) {
              // Tìm source field có tên giống hoặc chứa tên của target field
              const matchingSourceField = sourceFieldsList.find(
                (field) =>
                  !Object.values(newMappings).includes(field.id) &&
                  field.name !== 'source' &&
                  (field.name
                    .toLowerCase()
                    .includes(targetField.name.toLowerCase()) ||
                    targetField.name
                      .toLowerCase()
                      .includes(field.name.toLowerCase())),
              );

              if (matchingSourceField) {
                sourceFieldToMap = matchingSourceField.id;
              }
            }

            // 3. Nếu vẫn không tìm thấy, lấy source field đầu tiên chưa được mapping
            if (!sourceFieldToMap) {
              const availableSourceField = sourceFieldsList.find(
                (field) =>
                  !Object.values(newMappings).includes(field.id) &&
                  !field.isSystemField &&
                  !field.isDefault &&
                  field.name !== 'source',
              );

              if (availableSourceField) {
                sourceFieldToMap = availableSourceField.id;
              }
            }

            // Áp dụng mapping nếu tìm thấy source field phù hợp
            if (sourceFieldToMap) {
              newMappings[targetField.id] = sourceFieldToMap;
            }
          }
        });

        // Cập nhật state
        setMappings(newMappings);

        setPreviewData(result.previewData || []);
      } catch (error: any) {
        toast.error(t('integrations:ipos.mapping.loadError'), {
          description: error.message || t('common:somethingWentWrong'),
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (account) {
      fetchData();
    }
  }, [account, resourceType, supabase, router, t]);

  // Xử lý khi bắt đầu kéo
  const handleDragStart = (start: DragStart) => {
    setActiveDragId(start.draggableId);

    // Xác định trường nguồn đang được kéo
    if (start.source.droppableId === 'sourceFields') {
      const sourceField = sourceFields[start.source.index];
      setActiveSourceField(sourceField.name);
    }
  };

  // Xử lý khi kéo qua một trường đích cụ thể
  const handleDragUpdate = (update: any) => {
    if (
      update.destination &&
      update.source.droppableId === 'sourceFields' &&
      update.destination.droppableId === 'targetFields' &&
      update.destination.index >= 0 &&
      update.destination.index < targetFields.length
    ) {
      const targetField = targetFields[update.destination.index];
      // Fix: Add null check for targetField and targetField.id
      if (targetField && targetField.id) {
        setActiveTargetField(targetField.id);
      } else {
        setActiveTargetField(null);
      }
    } else {
      setActiveTargetField(null);
    }
  };

  // Xử lý khi kết thúc kéo thả
  const handleDragEnd = (result: any) => {
    // Reset active states
    setActiveDragId(null);
    setActiveSourceField(null);
    setActiveTargetField(null);
    const { source, destination } = result;

    // Nếu không có destination hoặc kéo vào cùng một vị trí, không làm gì
    if (
      !destination ||
      (source.droppableId === destination.droppableId &&
        source.index === destination.index)
    ) {
      return;
    }

    // Nếu kéo từ source đến target
    if (
      source.droppableId === 'sourceFields' &&
      destination.droppableId === 'targetFields'
    ) {
      const sourceField = sourceFields[source.index];
      const targetField = targetFields[destination.index];

      // Kiểm tra xem target field có phải là id hoặc required hoặc default mapping không
      const isTargetLocked =
        targetField.id === 'id' ||
        targetField.isRequired ||
        (defaultMappings[resourceType] &&
          Object.entries(defaultMappings[resourceType]).some(
            ([src, tgt]) => tgt === targetField.id,
          ));

      // Kiểm tra xem source field có phải là source không
      const isSourceLocked = sourceField.name === 'source';

      if (isTargetLocked || isSourceLocked) {
        // Hiển thị thông báo cảnh báo
        toast.warning(
          t('integrations:ipos.mapping.cannotMapLockedField') ||
            'This field is locked and cannot be mapped manually. It is automatically mapped by the system.',
          {
            duration: 3000,
            position: 'bottom-right',
          },
        );
        return;
      }

      // Cập nhật mapping
      setMappings((prev) => ({
        ...prev,
        [targetField.id]: sourceField.id,
      }));

      // Hiển thị thông báo thành công
      toast.success(
        t('integrations:ipos.mapping.fieldMappedSuccess', {
          source: sourceField.name,
          target: targetField.name,
        }) || `Mapped ${sourceField.name} to ${targetField.name}`,
        {
          duration: 2000,
          position: 'bottom-right',
        },
      );
    }
  };

  // Lưu mapping
  const handleSaveMapping = async () => {
    if (!integrationId) return;

    // Kiểm tra các trường bắt buộc (trừ trường sku vì nó sẽ được tạo tự động)
    const requiredFieldsList = targetFields.filter(
      (field) => field.isRequired && field.id !== 'sku',
    );

    const missingRequiredFields = requiredFieldsList.filter(
      (field) => !mappings[field.id],
    );

    if (missingRequiredFields.length > 0) {
      const fieldNames = missingRequiredFields.map((f) => f.name).join(', ');
      toast.warning(
        t('integrations:ipos.mapping.missingRequiredFields', {
          fields: fieldNames,
        }) || `Missing required fields: ${fieldNames}`,
        {
          description:
            t('integrations:ipos.mapping.pleaseMapRequiredFields') ||
            'Please map all required fields before saving.',
          duration: 5000,
        },
      );
      return;
    }

    // Thêm các trường mặc định vào mapping
    const finalMappings = {
      ...mappings,
    };

    // Thêm các trường mặc định (read-only)
    if (defaultMappings[resourceType]) {
      // Chuyển đổi từ source -> target sang target -> source
      Object.entries(defaultMappings[resourceType]).forEach(
        ([sourceField, targetField]) => {
          finalMappings[targetField] = sourceField;
        },
      );
    }

    // Áp dụng các trường khuyến nghị nếu chưa được mapping
    if (recommendedMappings[resourceType]) {
      Object.entries(recommendedMappings[resourceType]).forEach(
        ([sourceField, targetField]) => {
          // Kiểm tra xem source field có tồn tại trong sourceFields không
          const sourceFieldExists = sourceFields.some(
            (field) => field.id === sourceField,
          );
          // Kiểm tra xem target field có tồn tại trong targetFields không
          const targetFieldExists = targetFields.some(
            (field) => field.id === targetField,
          );
          // Kiểm tra xem target field đã được mapping chưa
          const targetFieldMapped =
            Object.keys(finalMappings).includes(targetField);

          // Chỉ áp dụng nếu cả hai trường đều tồn tại và target field chưa được mapping
          if (sourceFieldExists && targetFieldExists && !targetFieldMapped) {
            finalMappings[targetField] = sourceField;
          }
        },
      );
    }

    setIsSaving(true);
    try {
      await saveIPOSMapping(integrationId, resourceType, finalMappings);
      toast.success(t('integrations:ipos.mapping.saveSuccess'), {
        description: t('integrations:ipos.mapping.saveSuccessDescription'),
      });
    } catch (error: any) {
      toast.error(t('integrations:ipos.mapping.saveError'), {
        description: error.message || t('common:somethingWentWrong'),
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Test mapping
  const handleTestMapping = async () => {
    if (!integrationId) return;

    // Thêm các trường mặc định vào mapping
    const finalMappings = {
      ...mappings,
    };

    // Thêm các trường mặc định (read-only)
    if (defaultMappings[resourceType]) {
      // Chuyển đổi từ source -> target sang target -> source
      Object.entries(defaultMappings[resourceType]).forEach(
        ([sourceField, targetField]) => {
          finalMappings[targetField] = sourceField;
        },
      );
    }

    // Áp dụng các trường khuyến nghị nếu chưa được mapping
    if (recommendedMappings[resourceType]) {
      Object.entries(recommendedMappings[resourceType]).forEach(
        ([sourceField, targetField]) => {
          // Kiểm tra xem source field có tồn tại trong sourceFields không
          const sourceFieldExists = sourceFields.some(
            (field) => field.id === sourceField,
          );
          // Kiểm tra xem target field có tồn tại trong targetFields không
          const targetFieldExists = targetFields.some(
            (field) => field.id === targetField,
          );
          // Kiểm tra xem target field đã được mapping chưa
          const targetFieldMapped =
            Object.keys(finalMappings).includes(targetField);

          // Chỉ áp dụng nếu cả hai trường đều tồn tại và target field chưa được mapping
          if (sourceFieldExists && targetFieldExists && !targetFieldMapped) {
            finalMappings[targetField] = sourceField;
          }
        },
      );
    }

    setIsTesting(true);
    setTestResult(null);
    try {
      const result = await testIPOSMapping(
        integrationId,
        resourceType,
        finalMappings,
      );

      // Kiểm tra xem có dữ liệu xem trước không
      if (result.data && result.data.length > 0) {
        setTestResult({
          success: true,
          count: result.data.length,
          message:
            t('integrations:ipos.mapping.testSuccess', {
              count: result.data.length,
            }) || `Test successful! Found ${result.data.length} records.`,
        });
        setPreviewData(result.data);
      } else {
        setTestResult({
          success: true,
          message:
            t('integrations:ipos.mapping.testSuccessNoData') ||
            'Test successful! No data found. This could be because there are no records in iPOS or the mapping is not correct.',
        });
        setPreviewData([]);
      }
    } catch (error: any) {
      setTestResult({
        success: false,
        message: error.message || t('integrations:ipos.mapping.testError'),
      });
    } finally {
      setIsTesting(false);
    }
  };

  // Đồng bộ dữ liệu
  const handleSyncData = async () => {
    if (!integrationId) return;

    // Kiểm tra các trường bắt buộc (trừ trường sku vì nó sẽ được tạo tự động)
    const requiredFieldsList = targetFields.filter(
      (field) => field.isRequired && field.id !== 'sku',
    );

    const missingRequiredFields = requiredFieldsList.filter(
      (field) => !mappings[field.id],
    );

    if (missingRequiredFields.length > 0) {
      const fieldNames = missingRequiredFields.map((f) => f.name).join(', ');
      toast.warning(
        t('integrations:ipos.mapping.missingRequiredFields', {
          fields: fieldNames,
        }) || `Missing required fields: ${fieldNames}`,
        {
          description:
            t('integrations:ipos.mapping.pleaseMapRequiredFields') ||
            'Please map all required fields before syncing.',
          duration: 5000,
        },
      );
      return;
    }

    // Thêm các trường mặc định vào mapping
    const finalMappings = {
      ...mappings,
    };

    // Thêm các trường mặc định (read-only)
    if (defaultMappings[resourceType]) {
      // Chuyển đổi từ source -> target sang target -> source
      Object.entries(defaultMappings[resourceType]).forEach(
        ([sourceField, targetField]) => {
          finalMappings[targetField] = sourceField;
        },
      );
    }

    // Áp dụng các trường khuyến nghị nếu chưa được mapping
    if (recommendedMappings[resourceType]) {
      Object.entries(recommendedMappings[resourceType]).forEach(
        ([sourceField, targetField]) => {
          // Kiểm tra xem source field có tồn tại trong sourceFields không
          const sourceFieldExists = sourceFields.some(
            (field) => field.id === sourceField,
          );
          // Kiểm tra xem target field có tồn tại trong targetFields không
          const targetFieldExists = targetFields.some(
            (field) => field.id === targetField,
          );
          // Kiểm tra xem target field đã được mapping chưa
          const targetFieldMapped =
            Object.keys(finalMappings).includes(targetField);

          // Chỉ áp dụng nếu cả hai trường đều tồn tại và target field chưa được mapping
          if (sourceFieldExists && targetFieldExists && !targetFieldMapped) {
            finalMappings[targetField] = sourceField;
          }
        },
      );
    }

    setIsSyncing(true);
    try {
      // Lưu mapping trước khi đồng bộ
      await saveIPOSMapping(integrationId, resourceType, finalMappings);

      // Lấy user ID
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Bắt đầu đồng bộ
      const result = await syncIPOSData(integrationId, resourceType, user.id);

      toast.success(t('integrations:ipos.mapping.syncSuccess'), {
        description: t('integrations:ipos.mapping.syncSuccessDescription', {
          total: result.total,
          created: result.created,
          failed: result.failed,
        }),
      });

      // Chuyển đến trang lịch sử đồng bộ
      router.push(
        `/home/<USER>/integrations/ipos/sync-history/${result.syncLogId}`,
      );
    } catch (error: any) {
      toast.error(t('integrations:ipos.mapping.syncError'), {
        description: error.message || t('common:somethingWentWrong'),
      });
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={
          <Trans i18nKey="integrations:ipos.mapping.title">
            iPOS Data Mapping
          </Trans>
        }
        description={<IPOSBreadcrumbs accountSlug={account as string} />}
        account={account as string}
      />

      <PageBody>
        <div className="mx-auto max-w-6xl">
          <Card>
            <CardHeader>
              <CardTitle>
                <Trans i18nKey="integrations:ipos.mapping.title">
                  iPOS Data Mapping
                </Trans>
              </CardTitle>
              <CardDescription className="max-w-[600px] break-words">
                <Trans i18nKey="integrations:ipos.mapping.description">
                  Map fields from iPOS to your system to sync data correctly.
                </Trans>
                {resourceType && (
                  <EndpointInfo
                    resourceType={resourceType}
                    className="mt-2 block"
                  />
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <Alert className="mb-4">
                  <Info className="h-4 w-4" />
                  <AlertTitle>
                    <Trans i18nKey="integrations:ipos.mapping.smartMappingTitle">
                      Smart ID Mapping
                    </Trans>
                  </AlertTitle>
                  <AlertDescription>
                    <Trans i18nKey="integrations:ipos.mapping.smartMappingDescription">
                      The system will intelligently match records using
                      recommended sync fields (marked with{' '}
                      <span className="rounded-full bg-green-100 px-1.5 py-0.5 text-xs text-green-800 dark:bg-green-900/30 dark:text-green-300">
                        Sync ID
                      </span>
                      ). This helps avoid ID conflicts and ensures proper record
                      tracking. Fields with{' '}
                      <span className="text-amber-500">*</span> are required.
                    </Trans>
                  </AlertDescription>
                </Alert>

                {/* Default Mappings (Read-only) */}
                <div className="border-primary mb-4 rounded border-l-4 bg-gray-100 p-3 dark:bg-gray-800">
                  <h3 className="flex items-center text-sm font-medium">
                    <LockIcon className="mr-1 h-4 w-4" />
                    <Trans i18nKey="integrations:ipos.mapping.defaultMappingsTitle">
                      Default Mappings (Read-only)
                    </Trans>
                  </h3>
                  <p className="text-muted-foreground mb-2 text-xs">
                    <Trans i18nKey="integrations:ipos.mapping.defaultMappingsDescription">
                      These mappings are required for the system to work
                      correctly and cannot be changed.
                    </Trans>
                  </p>
                  <div className="space-y-2">
                    {defaultMappings[resourceType] &&
                    Object.entries(defaultMappings[resourceType]).length > 0 ? (
                      Object.entries(defaultMappings[resourceType]).map(
                        ([sourceField, targetField]) => {
                          // Tìm mô tả cho source field và target field
                          const sourceDescription =
                            fieldDescriptions[resourceType]?.[sourceField] ||
                            '';
                          const targetDescription =
                            targetFields.find((f) => f.id === targetField)
                              ?.description || '';
                          const sourceFieldObj = sourceFields.find(
                            (f) => f.id === sourceField,
                          );
                          const targetFieldObj = targetFields.find(
                            (f) => f.id === targetField,
                          );

                          return (
                            <div
                              key={sourceField}
                              className="flex items-center justify-between rounded border bg-white p-2 dark:bg-gray-700"
                            >
                              <div className="flex flex-col">
                                <div className="flex items-center">
                                  <LockIcon className="text-muted-foreground mr-2 h-3 w-3" />
                                  <span className="font-medium">
                                    {sourceField}
                                  </span>
                                  {sourceDescription && (
                                    <InfoTooltip
                                      content={sourceDescription}
                                      type="info"
                                    />
                                  )}
                                  <span className="bg-primary/20 text-primary ml-1 rounded-full px-1 py-0.5 text-xs">
                                    <Trans i18nKey="integrations:ipos.mapping.defaultField">
                                      Default
                                    </Trans>
                                  </span>
                                </div>
                                {sourceDescription && (
                                  <span className="text-muted-foreground ml-5 line-clamp-1 text-xs">
                                    {sourceDescription}
                                  </span>
                                )}
                              </div>
                              <div className="flex items-center">
                                <ArrowRightIcon className="mx-2 h-3 w-3" />
                              </div>
                              <div className="flex flex-col items-end">
                                <div className="flex items-center">
                                  <span className="font-medium">
                                    {targetField}
                                  </span>
                                  {targetDescription && (
                                    <InfoTooltip
                                      content={targetDescription}
                                      type="info"
                                    />
                                  )}
                                  {targetFieldObj?.isRequired && (
                                    <span className="ml-1 rounded-full bg-amber-100 px-1.5 py-0.5 text-xs text-amber-800 dark:bg-amber-900/30 dark:text-amber-300">
                                      <Trans i18nKey="integrations:ipos.mapping.required">
                                        Required
                                      </Trans>
                                    </span>
                                  )}
                                </div>
                                {targetDescription && (
                                  <span className="text-muted-foreground mr-5 line-clamp-1 text-xs">
                                    {targetDescription}
                                  </span>
                                )}
                              </div>
                            </div>
                          );
                        },
                      )
                    ) : (
                      <div className="text-muted-foreground p-2 text-center text-sm">
                        <Trans i18nKey="integrations:ipos.mapping.noDefaultMappings">
                          No default mappings available for this resource type.
                        </Trans>
                      </div>
                    )}
                  </div>
                </div>

                <Alert className="mb-4" variant="outline">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>
                    <Trans i18nKey="integrations:ipos.mapping.metadataFieldsTitle">
                      Metadata Fields for Mobile Integration
                    </Trans>
                  </AlertTitle>
                  <AlertDescription>
                    <Trans i18nKey="integrations:ipos.mapping.metadataFieldsDescription">
                      The system includes special metadata fields (
                      <code>external_id</code>, <code>source</code>,{' '}
                      <code>last_synced_at</code>, <code>integration_data</code>
                      ) to store third-party identifiers and source information.
                      These fields are essential for mobile integration and
                      tracking synchronized records.
                    </Trans>
                  </AlertDescription>
                </Alert>

                <div className="flex items-center space-x-4">
                  <Label htmlFor="resource-type">
                    <Trans i18nKey="integrations:ipos.mapping.resourceType">
                      Loại tài nguyên
                    </Trans>
                  </Label>
                  <Select
                    value={resourceType}
                    onValueChange={(value) => {
                      setResourceType(value as any);
                      // Update URL when resource type changes
                      router.push(
                        `/home/<USER>/integrations/ipos/mapping?resource=${value}`,
                      );
                    }}
                    disabled={isLoading}
                  >
                    <SelectTrigger id="resource-type" className="w-[200px]">
                      <SelectValue
                        placeholder={t(
                          'integrations:ipos.mapping.selectResource',
                        )}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="products">
                        <Trans i18nKey="integrations:ipos.mapping.products">
                          Products
                        </Trans>
                      </SelectItem>
                      <SelectItem value="orders">
                        <Trans i18nKey="integrations:ipos.mapping.orders">
                          Orders
                        </Trans>
                      </SelectItem>
                      <SelectItem value="customers">
                        <Trans i18nKey="integrations:ipos.mapping.customers">
                          Customers
                        </Trans>
                      </SelectItem>
                      <SelectItem value="categories">
                        <Trans i18nKey="integrations:ipos.mapping.categories">
                          Categories
                        </Trans>
                      </SelectItem>
                      <SelectItem value="branches">
                        <Trans i18nKey="integrations:ipos.mapping.branches">
                          Branches
                        </Trans>
                      </SelectItem>
                      <SelectItem value="vouchers">
                        <Trans i18nKey="integrations:ipos.mapping.vouchers">
                          Vouchers
                        </Trans>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {isLoading ? (
                  <div className="flex h-40 items-center justify-center">
                    <div className="text-center">
                      <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div>
                      <p className="text-muted-foreground mt-2 text-sm">
                        <Trans i18nKey="integrations:ipos.mapping.loading">
                          Loading fields...
                        </Trans>
                      </p>
                    </div>
                  </div>
                ) : (
                  <DragDropContext
                    onDragStart={handleDragStart}
                    onDragUpdate={handleDragUpdate}
                    onDragEnd={handleDragEnd}
                  >
                    <div className="relative grid grid-cols-1 gap-6 md:grid-cols-2">
                      {/* Mapping Lines - Visual indicators for mappings */}
                      <div className="pointer-events-none absolute inset-0 z-0 hidden md:block">
                        {Object.entries(mappings).map(
                          ([targetId, sourceId]) => {
                            const targetField = targetFields.find(
                              (f) => f.id === targetId,
                            );
                            const sourceField = sourceFields.find(
                              (f) => f.id === sourceId,
                            );
                            if (!targetField || !sourceField) return null;

                            const targetIndex = targetFields.findIndex(
                              (f) => f.id === targetId,
                            );
                            const sourceIndex = sourceFields.findIndex(
                              (f) => f.id === sourceId,
                            );

                            return (
                              <svg
                                key={`${sourceId}-${targetId}`}
                                className="absolute inset-0 h-full w-full overflow-visible"
                              >
                                <path
                                  d={`M 0,${sourceIndex * 41 + 60} C 50%,${sourceIndex * 41 + 60} 50%,${targetIndex * 41 + 60} 100%,${targetIndex * 41 + 60}`}
                                  stroke="var(--primary)"
                                  strokeWidth="1.5"
                                  strokeDasharray="4 2"
                                  fill="none"
                                  opacity="0.5"
                                />
                              </svg>
                            );
                          },
                        )}
                      </div>
                      {/* Source Fields */}
                      <Card className="border-dashed">
                        <CardHeader className="pb-3">
                          <CardTitle className="flex items-center gap-2 text-base">
                            <Trans i18nKey="integrations:ipos.mapping.sourceFields">
                              iPOS Fields
                            </Trans>
                            {resourceType === 'products' && (
                              <span className="bg-primary/10 text-primary rounded-full px-2 py-1 font-mono text-xs">
                                /items
                              </span>
                            )}
                          </CardTitle>
                          <CardDescription className="max-w-[600px] break-words">
                            <Trans i18nKey="integrations:ipos.mapping.sourceFieldsDescription">
                              Drag fields from here to the target fields.
                            </Trans>
                            {resourceType && (
                              <EndpointInfo
                                resourceType={resourceType}
                                className="mt-1 block"
                              />
                            )}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <Droppable droppableId="sourceFields">
                            {(provided, snapshot) => (
                              <div
                                {...provided.droppableProps}
                                ref={provided.innerRef}
                                className={`min-h-[250px] space-y-2 rounded-md p-2 ${snapshot.isDraggingOver ? 'bg-primary/5 border-primary/30 border' : ''}`}
                              >
                                {sourceFields.map((field, index) => (
                                  <Draggable
                                    key={field.id}
                                    draggableId={`source-${field.id}`}
                                    index={index}
                                    isDragDisabled={
                                      // Vô hiệu hóa nếu trường đã được mapping
                                      Object.values(mappings).includes(
                                        field.id,
                                      ) ||
                                      // Vô hiệu hóa các trường nguy hiểm
                                      field.isSystemField ||
                                      // Vô hiệu hóa các trường mặc định
                                      field.isDefault ||
                                      // Vô hiệu hóa trường source
                                      field.id === 'source'
                                    }
                                  >
                                    {(provided, snapshot) => (
                                      <div
                                        ref={provided.innerRef}
                                        {...provided.draggableProps}
                                        className={`flex items-center justify-between rounded-md border p-3 text-sm transition-all duration-200 ${snapshot.isDragging ? 'border-primary bg-primary/5 ring-primary/30 z-50 scale-105 border-2 opacity-90 shadow-lg ring-2 ring-offset-2' : 'bg-card hover:bg-accent/5'} ${Object.values(mappings).includes(field.id) || field.isSystemField || field.isDefault ? 'bg-primary/10 border-primary/30' : ''}`}
                                        data-testid={`source-field-${field.id}`}
                                      >
                                        <div className="flex items-center gap-2">
                                          {!(
                                            field.isSystemField ||
                                            field.isDefault ||
                                            Object.values(mappings).includes(
                                              field.id,
                                            ) ||
                                            field.name === 'source'
                                          ) ? (
                                            <span {...provided.dragHandleProps}>
                                              <GripVertical className="text-muted-foreground/70 h-4 w-4" />
                                            </span>
                                          ) : (
                                            <span className="flex h-4 w-4 items-center justify-center">
                                              <Lock className="text-muted-foreground/50 h-3 w-3" />
                                            </span>
                                          )}
                                          <div className="flex flex-col">
                                            <div className="flex items-center gap-1">
                                              <span
                                                className={`font-medium ${field.isSystemField || field.isDefault ? 'text-muted-foreground/50' : ''}`}
                                              >
                                                {field.name}
                                              </span>
                                              {field.isRecommendedForSync && (
                                                <span className="rounded-full bg-green-100 px-1.5 py-0.5 text-xs text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                                  <Trans i18nKey="integrations:ipos.mapping.recommendedForSync">
                                                    Sync ID
                                                  </Trans>
                                                </span>
                                              )}
                                              {/* Only show Default tag if it's not already mapped */}

                                              {field.isRecommended &&
                                                !field.isDefault && (
                                                  <span className="rounded-full bg-amber-50 px-1.5 py-0.5 text-xs text-amber-600 dark:bg-amber-900/20 dark:text-amber-400">
                                                    <Trans i18nKey="integrations:ipos.mapping.recommendedField">
                                                      Recommended
                                                    </Trans>
                                                  </span>
                                                )}

                                              {field.description && (
                                                <InfoTooltip
                                                  content={
                                                    field.isSystemField
                                                      ? field.description +
                                                        ' ' +
                                                        t(
                                                          'integrations:ipos.mapping.systemFieldDescription',
                                                        )
                                                      : field.isDefault
                                                        ? field.description +
                                                          ' ' +
                                                          t(
                                                            'integrations:ipos.mapping.defaultFieldDescription',
                                                          )
                                                        : field.description
                                                  }
                                                  type="info"
                                                />
                                              )}
                                            </div>
                                            {field.description && (
                                              <span className="text-muted-foreground line-clamp-1 text-xs">
                                                {field.description}
                                              </span>
                                            )}
                                          </div>
                                        </div>
                                        {field.name === 'source' ? (
                                          <span className="bg-primary/10 text-primary rounded px-2 py-1 text-xs">
                                            <Trans i18nKey="integrations:ipos.mapping.alwaysIpos">
                                              Always "ipos"
                                            </Trans>
                                          </span>
                                        ) : field.id === 'id' ? (
                                          <span className="bg-primary/10 text-primary rounded px-2 py-1 text-xs">
                                            {/* Special case for id field */}
                                            <Trans i18nKey="integrations:ipos.mapping.mappedTo">
                                              Mapped to
                                            </Trans>{' '}
                                            external_id
                                          </span>
                                        ) : Object.values(mappings).includes(
                                            field.id,
                                          ) ? (
                                          <span className="bg-primary/20 text-primary rounded px-2 py-1 text-xs">
                                            {/* Show which target field this is mapped to */}
                                            <Trans i18nKey="integrations:ipos.mapping.mappedTo">
                                              Mapped to
                                            </Trans>{' '}
                                            {Object.entries(mappings).find(
                                              ([targetId, sourceId]) =>
                                                sourceId === field.id,
                                            )?.[0] || ''}
                                          </span>
                                        ) : field.isSystemField ||
                                          field.isDefault ? (
                                          <span className="bg-primary/10 text-primary rounded px-2 py-1 text-xs">
                                            {/* Show which target field this is mapped to by default */}
                                            <Trans i18nKey="integrations:ipos.mapping.mappedTo">
                                              Mapped to
                                            </Trans>{' '}
                                            {(defaultMappings[resourceType] &&
                                              Object.entries(
                                                defaultMappings[resourceType],
                                              ).find(
                                                ([sourceId, targetId]) =>
                                                  sourceId === field.id,
                                              )?.[1]) ||
                                              ''}
                                          </span>
                                        ) : (
                                          <span className="text-muted-foreground text-xs">
                                            <Trans i18nKey="integrations:ipos.mapping.drag">
                                              Drag
                                            </Trans>
                                          </span>
                                        )}
                                      </div>
                                    )}
                                  </Draggable>
                                ))}
                                {sourceFields.length === 0 && (
                                  <div className="text-muted-foreground flex h-[200px] items-center justify-center text-center text-sm">
                                    <div>
                                      <Trans i18nKey="integrations:ipos.mapping.noSourceFields">
                                        No source fields available
                                      </Trans>
                                    </div>
                                  </div>
                                )}
                                {provided.placeholder}
                              </div>
                            )}
                          </Droppable>
                        </CardContent>
                      </Card>

                      {/* Target Fields */}
                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-base">
                            <Trans i18nKey="integrations:ipos.mapping.targetFields">
                              System Fields
                            </Trans>
                          </CardTitle>
                          <CardDescription className="max-w-[600px] break-words">
                            <Trans i18nKey="integrations:ipos.mapping.targetFieldsDescription">
                              Drop iPOS fields here to create mappings.
                            </Trans>
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <Droppable droppableId="targetFields">
                            {(provided, snapshot) => (
                              <div
                                {...provided.droppableProps}
                                ref={provided.innerRef}
                                className={`min-h-[250px] space-y-2 rounded-md p-2 ${snapshot.isDraggingOver ? 'bg-primary/5 border-primary/30 border' : ''}`}
                              >
                                {targetFields.map((field, index) => {
                                  return (
                                    <div className="relative" key={field.id}>
                                      <Draggable
                                        key={field.id}
                                        draggableId={`target-${field.id}`}
                                        index={index}
                                        isDragDisabled={
                                          field.id === 'id' ? true : false
                                        } // Disable only id field on target side
                                      >
                                        {(provided, snapshot) => (
                                          <div
                                            ref={provided.innerRef}
                                            {...provided.draggableProps}
                                            className={`flex items-center justify-between rounded-md border p-3 text-sm transition-all duration-200 ${snapshot.isDragging ? 'border-primary bg-primary/5 ring-primary/30 z-50 scale-105 border-2 opacity-90 shadow-lg ring-2 ring-offset-2' : 'hover:bg-accent/5'} ${
                                              mappings[field.id]
                                                ? 'bg-primary/10 border-primary/30'
                                                : field.isRequired
                                                  ? 'bg-card border-amber-300 dark:border-amber-700'
                                                  : 'bg-card'
                                            }`}
                                            data-testid={`target-field-${field.id}`}
                                          >
                                            <div className="flex items-center gap-2">
                                              {field.id === 'id' ? (
                                                <span className="flex h-4 w-4 items-center justify-center">
                                                  <Lock className="text-muted-foreground/50 h-3 w-3" />
                                                </span>
                                              ) : (
                                                <span
                                                  {...provided.dragHandleProps}
                                                >
                                                  <GripVertical className="text-muted-foreground/70 h-4 w-4" />
                                                </span>
                                              )}
                                              <div className="flex flex-col">
                                                <div className="flex items-center gap-1">
                                                  <span
                                                    className={`font-medium ${field.isSystemField ? 'text-muted-foreground/50' : ''}`}
                                                  >
                                                    {field.name}
                                                    {field.isRequired && (
                                                      <span className="ml-1 text-amber-500">
                                                        *
                                                      </span>
                                                    )}
                                                  </span>
                                                  {field.description && (
                                                    <InfoTooltip
                                                      content={
                                                        field.description
                                                      }
                                                      type="info"
                                                    />
                                                  )}
                                                </div>
                                                {field.description &&
                                                  !mappings[field.id] && (
                                                    <span className="text-muted-foreground line-clamp-1 text-xs">
                                                      {field.description}
                                                    </span>
                                                  )}
                                                <div className="mt-1 flex items-center gap-1">
                                                  {/* Show Required tag only if it's not already mapped */}
                                                  {field.isRequired &&
                                                    !mappings[field.id] && (
                                                      <span className="rounded-full bg-amber-100 px-1.5 py-0.5 text-xs text-amber-800 dark:bg-amber-900/30 dark:text-amber-300">
                                                        <Trans i18nKey="integrations:ipos.mapping.required">
                                                          Required
                                                        </Trans>
                                                      </span>
                                                    )}

                                                  {field.isRecommended &&
                                                    !field.isDefault && (
                                                      <span className="rounded-full bg-amber-50 px-1.5 py-0.5 text-xs text-amber-600 dark:bg-amber-900/20 dark:text-amber-400">
                                                        <Trans i18nKey="integrations:ipos.mapping.recommendedField">
                                                          Recommended
                                                        </Trans>
                                                      </span>
                                                    )}
                                                </div>
                                              </div>
                                              {field.avoidDirectMapping &&
                                                mappings[field.id] && (
                                                  <span className="line-clamp-1 text-xs text-amber-500/80">
                                                    <Trans i18nKey="integrations:ipos.mapping.idMappingWarning">
                                                      ID mapping may cause
                                                      conflicts
                                                    </Trans>
                                                  </span>
                                                )}
                                            </div>
                                            {mappings[field.id] ? (
                                              <div className="flex items-center space-x-2">
                                                <MappedField
                                                  sourceField={
                                                    mappings[field.id]
                                                  }
                                                  sourceFieldName={
                                                    sourceFields.find(
                                                      (f) =>
                                                        f.id ===
                                                        mappings[field.id],
                                                    )?.name ||
                                                    mappings[field.id]
                                                  }
                                                  sourceFieldDescription={
                                                    sourceFields.find(
                                                      (f) =>
                                                        f.id ===
                                                        mappings[field.id],
                                                    )?.description
                                                  }
                                                  isDefault={
                                                    defaultMappings[
                                                      resourceType
                                                    ] &&
                                                    Object.entries(
                                                      defaultMappings[
                                                        resourceType
                                                      ],
                                                    ).some(
                                                      ([src, tgt]) =>
                                                        src ===
                                                          mappings[field.id] &&
                                                        tgt === field.id,
                                                    )
                                                  }
                                                  isSystemField={
                                                    field.isSystemField
                                                  }
                                                />
                                                <Button
                                                  variant="ghost"
                                                  size="sm"
                                                  className={`h-5 w-5 rounded-full p-0 ${(defaultMappings[resourceType] && Object.entries(defaultMappings[resourceType]).some(([src, tgt]) => src === mappings[field.id] && tgt === field.id)) || field.isSystemField ? 'cursor-not-allowed opacity-30' : 'hover:bg-primary/30'}`}
                                                  disabled={
                                                    (defaultMappings[
                                                      resourceType
                                                    ] &&
                                                      Object.entries(
                                                        defaultMappings[
                                                          resourceType
                                                        ],
                                                      ).some(
                                                        ([src, tgt]) =>
                                                          src ===
                                                            mappings[
                                                              field.id
                                                            ] &&
                                                          tgt === field.id,
                                                      )) ||
                                                    field.isSystemField
                                                  }
                                                  title={
                                                    (defaultMappings[
                                                      resourceType
                                                    ] &&
                                                      Object.entries(
                                                        defaultMappings[
                                                          resourceType
                                                        ],
                                                      ).some(
                                                        ([src, tgt]) =>
                                                          src ===
                                                            mappings[
                                                              field.id
                                                            ] &&
                                                          tgt === field.id,
                                                      )) ||
                                                    field.isSystemField
                                                      ? t(
                                                          'integrations:ipos.mapping.cannotRemoveDefaultMapping',
                                                        ) ||
                                                        'Cannot remove default mapping'
                                                      : t(
                                                          'integrations:ipos.mapping.removeMapping',
                                                        ) || 'Remove mapping'
                                                  }
                                                  onClick={() => {
                                                    // Kiểm tra xem field có phải là default hoặc system field không
                                                    const isDefaultMapping =
                                                      defaultMappings[
                                                        resourceType
                                                      ] &&
                                                      Object.entries(
                                                        defaultMappings[
                                                          resourceType
                                                        ],
                                                      ).some(
                                                        ([src, tgt]) =>
                                                          src ===
                                                            mappings[
                                                              field.id
                                                            ] &&
                                                          tgt === field.id,
                                                      );
                                                    if (
                                                      isDefaultMapping ||
                                                      field.isSystemField
                                                    ) {
                                                      toast.warning(
                                                        t(
                                                          'integrations:ipos.mapping.cannotRemoveDefaultMapping',
                                                        ) ||
                                                          'Cannot remove default mapping. This field is required for the integration to work properly.',
                                                      );
                                                      return;
                                                    }

                                                    setMappings((prev) => {
                                                      const newMappings = {
                                                        ...prev,
                                                      };
                                                      delete newMappings[
                                                        field.id
                                                      ];
                                                      return newMappings;
                                                    });
                                                  }}
                                                  data-testid={`remove-mapping-${field.id}`}
                                                >
                                                  <span className="sr-only">
                                                    Remove
                                                  </span>
                                                  <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="14"
                                                    height="14"
                                                    viewBox="0 0 24 24"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    strokeWidth="2"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    className="h-3 w-3"
                                                  >
                                                    <path d="M18 6 6 18"></path>
                                                    <path d="m6 6 12 12"></path>
                                                  </svg>
                                                </Button>
                                              </div>
                                            ) : (
                                              <div
                                                className={`rounded px-2 py-1 text-xs ${snapshot.isDraggingOver ? 'bg-primary/20 text-primary' : 'text-muted-foreground'}`}
                                              >
                                                {field.isRequired ||
                                                field.id === 'source' ||
                                                field.id === 'id' ||
                                                (defaultMappings[
                                                  resourceType
                                                ] &&
                                                  Object.entries(
                                                    defaultMappings[
                                                      resourceType
                                                    ],
                                                  ).some(
                                                    ([src, tgt]) =>
                                                      tgt === field.id,
                                                  )) ? (
                                                  <div className="flex items-center gap-1">
                                                    <Lock className="text-muted-foreground/50 h-3 w-3" />
                                                    <span className="text-muted-foreground/50">
                                                      <Trans i18nKey="integrations:ipos.mapping.autoMapped">
                                                        Auto-mapped
                                                      </Trans>
                                                    </span>
                                                  </div>
                                                ) : (
                                                  <Trans i18nKey="integrations:ipos.mapping.dropHere">
                                                    Drop here
                                                  </Trans>
                                                )}
                                              </div>
                                            )}
                                          </div>
                                        )}
                                      </Draggable>
                                      <DroppableFieldIndicator
                                        isOver={
                                          activeTargetField === field.id &&
                                          !mappings[field.id]
                                        }
                                        sourceField={activeSourceField}
                                        targetField={field.name}
                                      />
                                    </div>
                                  );
                                })}
                                {targetFields.length === 0 && (
                                  <div className="text-muted-foreground flex h-[200px] items-center justify-center text-center text-sm">
                                    <div>
                                      <Trans i18nKey="integrations:ipos.mapping.noTargetFields">
                                        No target fields available
                                      </Trans>
                                    </div>
                                  </div>
                                )}
                                {provided.placeholder}
                              </div>
                            )}
                          </Droppable>
                        </CardContent>
                      </Card>
                    </div>
                  </DragDropContext>
                )}

                {/* Test Result */}
                {testResult && (
                  <Alert
                    className={
                      testResult.success
                        ? 'border-green-200 bg-green-50'
                        : 'border-red-200 bg-red-50'
                    }
                  >
                    <AlertTitle
                      className={
                        testResult.success ? 'text-green-800' : 'text-red-800'
                      }
                    >
                      {testResult.success ? (
                        <Trans i18nKey="integrations:ipos.mapping.testSuccess">
                          Test successful
                        </Trans>
                      ) : (
                        <Trans i18nKey="integrations:ipos.mapping.testFailed">
                          Test failed
                        </Trans>
                      )}
                    </AlertTitle>
                    <AlertDescription
                      className={
                        testResult.success ? 'text-green-700' : 'text-red-700'
                      }
                    >
                      {testResult.success ? (
                        <div className="flex flex-col gap-1">
                          <span>
                            <span>
                              Đã xử lý <strong>{testResult.count || 0}</strong>{' '}
                              mục
                            </span>
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-1 text-xs"
                            onClick={() => {
                              document
                                .getElementById('preview-data')
                                ?.scrollIntoView({ behavior: 'smooth' });
                            }}
                          >
                            <ArrowDownIcon className="mr-1 h-3 w-3" />
                            <Trans i18nKey="integrations:ipos.mapping.scrollDownToPreview">
                              Cuộn xuống để xem trước dữ liệu
                            </Trans>
                          </Button>
                        </div>
                      ) : (
                        testResult.message
                      )}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Preview Data */}
                {testResult && testResult.success && (
                  <div
                    id="preview-data"
                    className="animate-in fade-in-0 mt-6 space-y-3 border-t pt-4 duration-300"
                  >
                    <h3 className="mb-2 flex items-center text-lg font-medium">
                      <EyeIcon className="text-primary mr-2 h-5 w-5" />
                      <Trans i18nKey="integrations:ipos.mapping.previewData">
                        Xem trước dữ liệu
                      </Trans>
                    </h3>
                    <div className="bg-muted/30 border-muted mb-3 rounded-md border p-3">
                      <p className="text-muted-foreground text-sm">
                        <Trans i18nKey="integrations:ipos.mapping.previewDataDescription">
                          Đây là bản xem trước dữ liệu của bạn sau khi ánh xạ.
                          Bảng hiển thị các trường đích và giá trị của chúng từ
                          iPOS.
                        </Trans>
                      </p>
                    </div>

                    {previewData.length > 0 ? (
                      <div className="rounded-md border shadow-sm">
                        <div className="overflow-x-auto">
                          <table className="w-full divide-y divide-gray-200 text-sm">
                            <thead>
                              <tr className="bg-primary/5 border-b">
                                <th className="w-12 px-4 py-2 text-left font-medium">
                                  #
                                </th>
                                {Object.keys(mappings).map((targetField) => (
                                  <th
                                    key={targetField}
                                    className="px-4 py-2 text-left font-medium"
                                  >
                                    {targetField}
                                    {targetFields.find(
                                      (f) => f.id === targetField,
                                    )?.isDefault && (
                                      <span className="bg-primary/10 text-primary ml-1 rounded-full px-1 py-0.5 text-xs">
                                        Default
                                      </span>
                                    )}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {previewData.slice(0, 5).map((item, index) => (
                                <tr
                                  key={index}
                                  className="hover:bg-muted/20 border-b"
                                >
                                  <td className="text-muted-foreground px-4 py-2">
                                    {index + 1}
                                  </td>
                                  {Object.entries(mappings).map(
                                    ([targetField, sourceField]) => {
                                      const isDefaultField = targetFields.find(
                                        (f) => f.id === targetField,
                                      )?.isDefault;
                                      // Lấy giá trị từ dữ liệu nguồn
                                      let value = '-';
                                      // Log để debug
                                      console.log('Item:', item);
                                      console.log('Source field:', sourceField);
                                      console.log('Target field:', targetField);
                                      console.log('Value:', item[sourceField]);

                                      if (item[sourceField] !== undefined) {
                                        if (
                                          typeof item[sourceField] === 'object'
                                        ) {
                                          value = JSON.stringify(
                                            item[sourceField],
                                          );
                                        } else {
                                          value = String(item[sourceField]);
                                        }
                                      }

                                      // Đảm bảo giá trị không rỗng
                                      if (
                                        value === '' ||
                                        value === 'undefined' ||
                                        value === 'null'
                                      ) {
                                        value = '-';
                                      }

                                      return (
                                        <td
                                          key={`${index}-${targetField}`}
                                          className={`px-4 py-2 ${isDefaultField ? 'bg-primary/5' : ''}`}
                                        >
                                          <div>
                                            <span>{value}</span>
                                          </div>
                                        </td>
                                      );
                                    },
                                  )}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                        {previewData.length > 5 && (
                          <div className="bg-muted/20 text-muted-foreground p-2 text-center text-xs">
                            <span>
                              Hiển thị <strong>5</strong> trên{' '}
                              <strong>{previewData.length}</strong> mục
                            </span>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-muted-foreground bg-muted/10 rounded-md border border-dashed p-8 text-center">
                        <Trans i18nKey="integrations:ipos.mapping.noPreviewData">
                          No data available for preview. This could be because
                          there are no records in iPOS or the mapping is not
                          correct.
                        </Trans>
                      </div>
                    )}
                  </div>
                )}

                {/* Actions */}
                <div className="flex justify-between pt-4">
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/home/<USER>/integrations`)}
                    data-testid="cancel-button"
                  >
                    <Trans i18nKey="common:cancel">Cancel</Trans>
                  </Button>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={handleTestMapping}
                      disabled={
                        isLoading ||
                        isTesting ||
                        Object.keys(mappings).length === 0
                      }
                      data-testid="test-mapping-button"
                    >
                      {isTesting ? (
                        <Trans i18nKey="integrations:ipos.mapping.testing">
                          Testing...
                        </Trans>
                      ) : (
                        <Trans i18nKey="integrations:ipos.mapping.testMapping">
                          Test Mapping
                        </Trans>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleSaveMapping}
                      disabled={
                        isLoading ||
                        isSaving ||
                        Object.keys(mappings).length === 0
                      }
                      data-testid="save-mapping-button"
                    >
                      {isSaving ? (
                        <Trans i18nKey="common:saving">Saving...</Trans>
                      ) : (
                        <Trans i18nKey="common:save">Save</Trans>
                      )}
                    </Button>
                    <Button
                      onClick={handleSyncData}
                      disabled={
                        isLoading ||
                        isSyncing ||
                        Object.keys(mappings).length === 0
                      }
                      data-testid="sync-data-button"
                    >
                      {isSyncing ? (
                        <Trans i18nKey="integrations:ipos.mapping.syncing">
                          Syncing...
                        </Trans>
                      ) : (
                        <Trans i18nKey="integrations:ipos.mapping.syncNow">
                          Sync Now
                        </Trans>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageBody>
    </>
  );
}
