import React from 'react';
import { Trans } from 'react-i18next';

interface MappedFieldProps {
  sourceField: string;
  sourceFieldName: string;
  sourceFieldDescription?: string;
  isDefault?: boolean;
  isSystemField?: boolean;
}

export function MappedField({
  sourceField,
  sourceFieldName,
  sourceFieldDescription,
  isDefault,
  isSystemField,
}: MappedFieldProps) {
  return (
    <div
      className={`flex items-center gap-1 rounded px-2 py-1 text-xs ${
        isDefault || isSystemField
          ? 'bg-primary/10 text-primary'
          : 'bg-primary/20 text-primary'
      }`}
    >
      <div className="flex flex-col">
        <div className="flex items-center">
          <span className="font-medium">{sourceFieldName}</span>
        </div>
      </div>

    </div>
  );
}
