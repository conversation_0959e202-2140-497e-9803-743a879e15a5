'use client';

import { useEffect, useState } from 'react';

import { usePara<PERSON>, useRouter } from 'next/navigation';

import { format, formatDistanceToNow } from 'date-fns';
import { enUS, vi } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';

import { isUUID } from '@kit/shared/utils';
import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';

import { IPOSBreadcrumbs } from '../_components/ipos-breadcrumbs';

interface SyncLog {
  id: string;
  resource_type: string;
  status: string;
  items_processed: number;
  items_created: number;
  items_updated: number;
  items_failed: number;
  error_message: string | null;
  started_at: string;
  completed_at: string | null;
  created_by: string;
}

export default function IPOSSyncHistoryPage() {
  const { account } = useParams();
  const router = useRouter();
  const { t, i18n } = useTranslation(['integrations', 'common']);
  const supabase = useSupabase();

  const [syncLogs, setSyncLogs] = useState<SyncLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [resourceFilter, setResourceFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [integrationId, setIntegrationId] = useState<string | null>(null);

  // Lấy danh sách sync logs
  useEffect(() => {
    const fetchSyncLogs = async () => {
      setIsLoading(true);
      try {
        // Chuyển đổi account slug thành account ID
        let accountId = account;

        if (!isUUID(account)) {
          // Nếu là slug, lấy UUID tương ứng
          const { data: accountData, error: accountError } = await supabase
            .from('accounts')
            .select('id')
            .eq('slug', account)
            .single();

          if (accountError) {
            console.error('Error fetching account by slug:', accountError);
            setIsLoading(false);
            return;
          }

          accountId = accountData.id;
        }

        // Lấy integration ID
        // Thử với cột 'type' trước
        let { data: integration, error: integrationError } = await supabase
          .from('integrations')
          .select('id')
          .eq('account_id', accountId)
          .eq('type', 'ipos')
          .eq('status', 'connected')
          .single();

        // Nếu không tìm thấy, thử với cột 'platform'
        if (integrationError && integrationError.code === 'PGRST116') {
          const { data: integrationByPlatform, error: platformError } =
            await supabase
              .from('integrations')
              .select('id')
              .eq('account_id', accountId)
              .eq('platform', 'ipos')
              .eq('status', 'connected')
              .single();

          if (!platformError) {
            integration = integrationByPlatform;
            integrationError = null;
          }
        }

        if (integrationError) {
          // Check if it's a "not found" error
          if (integrationError.code === 'PGRST116') {
            console.log('No active iPOS integration found for this account');
            setIsLoading(false);
            return;
          }

          console.error('Error fetching integration:', integrationError);
          setIsLoading(false);
          return;
        }

        if (!integration) {
          console.log('No iPOS integration found for this account');
          setIsLoading(false);
          return;
        }

        setIntegrationId(integration.id);

        // Xây dựng query
        let query = supabase
          .from('integration_sync_logs')
          .select('*')
          .eq('integration_id', integration.id)
          .order('started_at', { ascending: false });

        // Áp dụng filter
        if (resourceFilter !== 'all') {
          query = query.eq('resource_type', resourceFilter);
        }

        if (statusFilter !== 'all') {
          query = query.eq('status', statusFilter);
        }

        if (searchQuery) {
          query = query.or(
            `resource_type.ilike.%${searchQuery}%,error_message.ilike.%${searchQuery}%`,
          );
        }

        const { data, error } = await query;

        if (error) {
          console.error('Error fetching sync logs:', error);
          setSyncLogs([]);
          return;
        }

        setSyncLogs(data || []);
      } catch (error) {
        console.error('Error in fetchSyncLogs:', error);
        setSyncLogs([]);
      } finally {
        setIsLoading(false);
      }
    };

    if (account) {
      fetchSyncLogs();
    }
  }, [account, supabase, resourceFilter, statusFilter, searchQuery]);

  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'PPpp', {
        locale: i18n.language === 'vi' ? vi : enUS,
      });
    } catch (error) {
      return dateString;
    }
  };

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: i18n.language === 'vi' ? vi : enUS,
      });
    } catch (error) {
      return dateString;
    }
  };

  // Render status badge
  const renderStatusBadge = (status: string) => {
    const getTransContent = () => {
      switch (status) {
        case 'success':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.success">
              Success
            </Trans>
          );
        case 'error':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.error">Error</Trans>
          );
        case 'partial':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.partial">
              Partial
            </Trans>
          );
        case 'in_progress':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.inProgress">
              In Progress
            </Trans>
          );
        default:
          return status;
      }
    };

    switch (status) {
      case 'success':
        return (
          <Badge
            variant="outline"
            className="border-green-200 bg-green-50 text-green-700"
          >
            {getTransContent()}
          </Badge>
        );
      case 'error':
        return (
          <Badge
            variant="outline"
            className="border-red-200 bg-red-50 text-red-700"
          >
            {getTransContent()}
          </Badge>
        );
      case 'partial':
        return (
          <Badge
            variant="outline"
            className="border-amber-200 bg-amber-50 text-amber-700"
          >
            {getTransContent()}
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge
            variant="outline"
            className="border-blue-200 bg-blue-50 text-blue-700"
          >
            {getTransContent()}
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Render resource type badge
  const renderResourceTypeBadge = (resourceType: string) => {
    const getTransContent = () => {
      switch (resourceType) {
        case 'products':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.products">
              Products
            </Trans>
          );
        case 'orders':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.orders">Orders</Trans>
          );
        case 'customers':
          return (
            <Trans i18nKey="integrations:ipos.syncHistory.customers">
              Customers
            </Trans>
          );
        default:
          return resourceType;
      }
    };

    switch (resourceType) {
      case 'products':
        return (
          <Badge
            variant="outline"
            className="border-purple-200 bg-purple-50 text-purple-700"
          >
            {getTransContent()}
          </Badge>
        );
      case 'orders':
        return (
          <Badge
            variant="outline"
            className="border-indigo-200 bg-indigo-50 text-indigo-700"
          >
            {getTransContent()}
          </Badge>
        );
      case 'customers':
        return (
          <Badge
            variant="outline"
            className="border-cyan-200 bg-cyan-50 text-cyan-700"
          >
            {getTransContent()}
          </Badge>
        );
      default:
        return <Badge variant="outline">{resourceType}</Badge>;
    }
  };

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={
          <Trans i18nKey="integrations:ipos.syncHistory.title">
            iPOS Sync History
          </Trans>
        }
        description={<IPOSBreadcrumbs accountSlug={account as string} />}
        account={account as string}
      />

      <PageBody>
        <Card>
          <CardHeader>
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div>
                <CardTitle>
                  <Trans i18nKey="integrations:ipos.syncHistory.title">
                    iPOS Sync History
                  </Trans>
                </CardTitle>
                <CardDescription>
                  <Trans i18nKey="integrations:ipos.syncHistory.description">
                    View the history of data synchronization with iPOS.
                  </Trans>
                </CardDescription>
              </div>
              <div className="flex flex-col gap-2 sm:flex-row">
                <Button
                  variant="outline"
                  onClick={() =>
                    router.push(`/home/<USER>/integrations/ipos/mapping`)
                  }
                  data-testid="back-to-mapping-button"
                >
                  <Trans i18nKey="integrations:ipos.syncHistory.backToMapping">
                    Back to Mapping
                  </Trans>
                </Button>
                <Button
                  onClick={() => {
                    if (integrationId) {
                      router.push(`/home/<USER>/integrations/ipos/sync`);
                    }
                  }}
                  disabled={!integrationId}
                  data-testid="new-sync-button"
                >
                  <Trans i18nKey="integrations:ipos.syncHistory.newSync">
                    New Sync
                  </Trans>
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Filters */}
              <div className="flex flex-col gap-4 sm:flex-row">
                <div className="flex-1">
                  <Input
                    placeholder={t(
                      'integrations:ipos.syncHistory.searchPlaceholder',
                    )}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full"
                    data-testid="search-input"
                  />
                </div>
                <div className="flex gap-2">
                  <Select
                    value={resourceFilter}
                    onValueChange={setResourceFilter}
                  >
                    <SelectTrigger
                      className="w-[150px]"
                      data-testid="resource-filter"
                    >
                      <SelectValue
                        placeholder={t(
                          'integrations:ipos.syncHistory.resourceFilter',
                        )}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">
                        <Trans i18nKey="integrations:ipos.syncHistory.allResources">
                          All Resources
                        </Trans>
                      </SelectItem>
                      <SelectItem value="products">
                        <Trans i18nKey="integrations:ipos.syncHistory.products">
                          Products
                        </Trans>
                      </SelectItem>
                      <SelectItem value="orders">
                        <Trans i18nKey="integrations:ipos.syncHistory.orders">
                          Orders
                        </Trans>
                      </SelectItem>
                      <SelectItem value="customers">
                        <Trans i18nKey="integrations:ipos.syncHistory.customers">
                          Customers
                        </Trans>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger
                      className="w-[150px]"
                      data-testid="status-filter"
                    >
                      <SelectValue
                        placeholder={t(
                          'integrations:ipos.syncHistory.statusFilter',
                        )}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">
                        <Trans i18nKey="integrations:ipos.syncHistory.allStatuses">
                          All Statuses
                        </Trans>
                      </SelectItem>
                      <SelectItem value="success">
                        <Trans i18nKey="integrations:ipos.syncHistory.success">
                          Success
                        </Trans>
                      </SelectItem>
                      <SelectItem value="error">
                        <Trans i18nKey="integrations:ipos.syncHistory.error">
                          Error
                        </Trans>
                      </SelectItem>
                      <SelectItem value="partial">
                        <Trans i18nKey="integrations:ipos.syncHistory.partial">
                          Partial
                        </Trans>
                      </SelectItem>
                      <SelectItem value="in_progress">
                        <Trans i18nKey="integrations:ipos.syncHistory.inProgress">
                          In Progress
                        </Trans>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Sync Logs Table */}
              {isLoading ? (
                <div className="flex h-40 items-center justify-center">
                  <div className="text-center">
                    <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div>
                    <p className="text-muted-foreground mt-2 text-sm">
                      <Trans i18nKey="integrations:ipos.syncHistory.loading">
                        Loading sync history...
                      </Trans>
                    </p>
                  </div>
                </div>
              ) : !integrationId ? (
                <div className="flex h-40 items-center justify-center rounded-md border">
                  <div className="text-center">
                    <p className="text-muted-foreground">
                      <Trans i18nKey="integrations:ipos.syncHistory.noIntegration">
                        No active iPOS integration found for this account
                      </Trans>
                    </p>
                    <Button
                      variant="outline"
                      className="mt-4"
                      onClick={() => {
                        router.push(`/home/<USER>/integrations`);
                      }}
                      data-testid="setup-integration-button"
                    >
                      <Trans i18nKey="integrations:ipos.syncHistory.setupIntegration">
                        Set Up Integration
                      </Trans>
                    </Button>
                  </div>
                </div>
              ) : syncLogs.length === 0 ? (
                <div className="flex h-40 items-center justify-center rounded-md border">
                  <div className="text-center">
                    <p className="text-muted-foreground">
                      <Trans i18nKey="integrations:ipos.syncHistory.noLogs">
                        No sync logs found
                      </Trans>
                    </p>
                    <Button
                      variant="outline"
                      className="mt-4"
                      onClick={() => {
                        if (integrationId) {
                          router.push(
                            `/home/<USER>/integrations/ipos/sync`,
                          );
                        }
                      }}
                      disabled={!integrationId}
                      data-testid="start-sync-button"
                    >
                      <Trans i18nKey="integrations:ipos.syncHistory.startSync">
                        Start a Sync
                      </Trans>
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="rounded-md border">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="bg-muted/50 border-b">
                          <th className="px-4 py-3 text-left font-medium">
                            <Trans i18nKey="integrations:ipos.syncHistory.resourceType">
                              Resource
                            </Trans>
                          </th>
                          <th className="px-4 py-3 text-left font-medium">
                            <Trans i18nKey="integrations:ipos.syncHistory.status">
                              Status
                            </Trans>
                          </th>
                          <th className="px-4 py-3 text-left font-medium">
                            <Trans i18nKey="integrations:ipos.syncHistory.items">
                              Items
                            </Trans>
                          </th>
                          <th className="px-4 py-3 text-left font-medium">
                            <Trans i18nKey="integrations:ipos.syncHistory.startedAt">
                              Started
                            </Trans>
                          </th>
                          <th className="px-4 py-3 text-left font-medium">
                            <Trans i18nKey="integrations:ipos.syncHistory.duration">
                              Duration
                            </Trans>
                          </th>
                          <th className="px-4 py-3 text-left font-medium">
                            <Trans i18nKey="integrations:ipos.syncHistory.actions">
                              Actions
                            </Trans>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {syncLogs.map((log) => (
                          <tr key={log.id} className="border-b">
                            <td className="px-4 py-3">
                              {renderResourceTypeBadge(log.resource_type)}
                            </td>
                            <td className="px-4 py-3">
                              {renderStatusBadge(log.status)}
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex flex-col">
                                <span>
                                  <Trans
                                    i18nKey="integrations:ipos.syncHistory.itemsProcessed"
                                    values={{ count: log.items_processed }}
                                  >
                                    {log.items_processed} processed
                                  </Trans>
                                </span>
                                <span className="text-muted-foreground text-xs">
                                  <Trans
                                    i18nKey="integrations:ipos.syncHistory.itemsCreated"
                                    values={{ count: log.items_created }}
                                  >
                                    {log.items_created} created
                                  </Trans>
                                  {log.items_failed > 0 && (
                                    <>
                                      ,{' '}
                                      <Trans
                                        i18nKey="integrations:ipos.syncHistory.itemsFailed"
                                        values={{ count: log.items_failed }}
                                      >
                                        {log.items_failed} failed
                                      </Trans>
                                    </>
                                  )}
                                </span>
                              </div>
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex flex-col">
                                <span title={formatDate(log.started_at)}>
                                  {formatRelativeTime(log.started_at)}
                                </span>
                                <span className="text-muted-foreground text-xs">
                                  {formatDate(log.started_at)}
                                </span>
                              </div>
                            </td>
                            <td className="px-4 py-3">
                              {log.completed_at ? (
                                <span>
                                  {formatDistanceToNow(
                                    new Date(log.started_at),
                                    {
                                      locale:
                                        i18n.language === 'vi' ? vi : enUS,
                                    },
                                  )}
                                </span>
                              ) : (
                                <span className="text-muted-foreground">
                                  <Trans i18nKey="integrations:ipos.syncHistory.inProgress">
                                    In Progress
                                  </Trans>
                                </span>
                              )}
                            </td>
                            <td className="px-4 py-3">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  router.push(
                                    `/home/<USER>/integrations/ipos/sync-history/${log.id}`,
                                  )
                                }
                                data-testid={`view-details-button-${log.id}`}
                              >
                                <Trans i18nKey="integrations:ipos.syncHistory.viewDetails">
                                  View Details
                                </Trans>
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </PageBody>
    </>
  );
}
