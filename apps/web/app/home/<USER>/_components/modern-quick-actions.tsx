'use client';

import { useState } from 'react';

import Link from 'next/link';

import { motion } from 'framer-motion';
import {
  Bell,
  CreditCard,
  FileText,
  HelpCircle,
  PlusCircle,
  Settings,
} from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import featureFlagsConfig from '~/config/feature-flags.config';
import pathsConfig from '~/config/paths.config';

import { HomeAddAccountButton } from './home-add-account-button';

interface ModernQuickActionsProps {
  onStartTour?: () => void;
  canCreateTeam?: boolean;
}

export function ModernQuickActions({
  onStartTour,
  canCreateTeam = false,
}: ModernQuickActionsProps) {
  const [isAddingAccount, setIsAddingAccount] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <Card className="overflow-hidden border-none bg-gradient-to-br from-gray-50 to-gray-100 shadow-md dark:from-gray-900 dark:to-gray-800">
        <CardHeader className="border-b border-gray-200 bg-white/50 pb-3 dark:border-gray-700 dark:bg-white/5">
          <CardTitle className="text-lg font-medium">
            <Trans i18nKey="home:quickActions.title">Quick Actions</Trans>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <div className="grid gap-3">
            <Button
              variant="ghost"
              className="justify-start hover:bg-blue-50 dark:hover:bg-blue-900/20"
              asChild
            >
              <Link href={pathsConfig.app.personalAccountSettings}>
                <Settings className="mr-2 h-4 w-4 text-blue-600 dark:text-blue-400" />
                <span>
                  <Trans i18nKey="home:quickActions.settings">
                    Account Settings
                  </Trans>
                </span>
              </Link>
            </Button>

            {canCreateTeam && featureFlagsConfig.enableTeamCreation && (
              <Button
                variant="ghost"
                className="justify-start hover:bg-indigo-50 dark:hover:bg-indigo-900/20"
                onClick={() => setIsAddingAccount(true)}
              >
                <PlusCircle className="mr-2 h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                <span>
                  <Trans i18nKey="home:quickActions.createTeam">
                    Create New Business
                  </Trans>
                </span>
              </Button>
            )}

            {isAddingAccount && canCreateTeam && (
              <HomeAddAccountButton
                isOpen={isAddingAccount}
                setIsOpen={setIsAddingAccount}
              />
            )}

            {featureFlagsConfig.enablePersonalAccountBilling && (
              <Button
                variant="ghost"
                className="justify-start hover:bg-purple-50 dark:hover:bg-purple-900/20"
                asChild
              >
                <Link href={pathsConfig.app.personalAccountBilling}>
                  <CreditCard className="mr-2 h-4 w-4 text-purple-600 dark:text-purple-400" />
                  <span>
                    <Trans i18nKey="home:quickActions.billing">Billing</Trans>
                  </span>
                </Link>
              </Button>
            )}

            {onStartTour && (
              <Button
                variant="ghost"
                className="justify-start hover:bg-green-50 dark:hover:bg-green-900/20"
                onClick={onStartTour}
              >
                <HelpCircle className="mr-2 h-4 w-4 text-green-600 dark:text-green-400" />
                <span>
                  <Trans i18nKey="home:quickActions.startTour">
                    Start Tour
                  </Trans>
                </span>
              </Button>
            )}

            {featureFlagsConfig.enableNotifications && (
              <Button
                variant="ghost"
                className="justify-start hover:bg-amber-50 dark:hover:bg-amber-900/20"
                asChild
              >
                <Link href={pathsConfig.app.notifications || '#'}>
                  <Bell className="mr-2 h-4 w-4 text-amber-600 dark:text-amber-400" />
                  <span>
                    <Trans i18nKey="home:quickActions.notifications">
                      Notifications
                    </Trans>
                  </span>
                </Link>
              </Button>
            )}

            <Button
              variant="ghost"
              className="justify-start hover:bg-red-50 dark:hover:bg-red-900/20"
              asChild
            >
              <Link href={pathsConfig.app.documentation}>
                <FileText className="mr-2 h-4 w-4 text-red-600 dark:text-red-400" />
                <span>
                  <Trans i18nKey="home:quickActions.documentation">
                    Documentation
                  </Trans>
                </span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
