'use server';

import { getLogger } from '@kit/shared/logger';
import { requireUser } from '@kit/supabase/require-user';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';

/**
 * Kiểm tra quyền của user cho một account và permission cụ thể
 * @param accountId ID của account
 * @param permission Tên quyền cần kiểm tra
 * @param userId ID của người dùng (nếu không cung cấp, sẽ lấy từ session)
 * @returns Kết quả kiểm tra quyền
 */
export async function checkUserPermission(
  accountId: string,
  permission: string,
  userId?: string,
) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient();
  const teamAccountsApi = createTeamAccountsApi(supabase);

  try {
    // Nếu không có userId, sử dụng requireUser để đảm bảo người dùng đã đăng nhập
    if (!userId) {
      const { data, error } = await requireUser(supabase);

      if (error || !data) {
        return {
          hasPermission: false,
          reason: 'not_authenticated',
        };
      }

      userId = data.id;
    }

    // Kiểm tra userId
    if (!userId) {
      return {
        hasPermission: false,
        reason: 'not_authenticated',
      };
    }

    try {
      // Kiểm tra quyền sử dụng hàm hasPermission
      const hasPermission = await teamAccountsApi.hasPermission({
        accountId,
        userId,
        permission: permission as any,
      });

      return {
        hasPermission,
        reason: hasPermission ? undefined : 'no_permission',
      };
    } catch (error) {
      logger.error(
        { error, accountId, permission, userId },
        'Error checking user permission',
      );

      return {
        hasPermission: false,
        reason: 'permission_error',
      };
    }
  } catch (error) {
    logger.error(
      { error, accountId, permission },
      'Error checking user permission',
    );

    return {
      hasPermission: false,
      reason: 'unknown_error',
    };
  }
}

/**
 * Kiểm tra nhiều quyền của user cho một account
 * @param accountId ID của account
 * @param permissions Danh sách quyền cần kiểm tra
 * @param userId ID của người dùng (nếu không cung cấp, sẽ lấy từ session)
 * @returns Kết quả kiểm tra cho từng quyền
 */
export async function checkUserPermissions(
  accountId: string,
  permissions: string[],
  userId?: string,
) {
  const results: Record<string, { hasPermission: boolean; reason?: string }> =
    {};

  await Promise.all(
    permissions.map(async (permission) => {
      results[permission] = await checkUserPermission(
        accountId,
        permission,
        userId,
      );
    }),
  );

  return results;
}
