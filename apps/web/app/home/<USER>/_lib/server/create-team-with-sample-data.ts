import 'server-only';

import { revalidatePath } from 'next/cache';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { sampleDataTemplates } from '~/config/mockup.config';

interface CreateTeamWithSampleDataParams {
  name: string;
  industry: keyof typeof sampleDataTemplates;
  userId: string;
}

export async function createTeamWithSampleData({
  name,
  industry,
  userId,
}: CreateTeamWithSampleDataParams) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient();
  const adminClient = getSupabaseServerAdminClient();

  try {
    logger.info({ name, industry, userId }, 'Creating team with sample data');

    // 1. Create the team account
    const { data: teamData, error: teamError } = await supabase.rpc(
      'create_team_account',
      {
        account_name: name,
      },
    );

    if (teamError || !teamData) {
      logger.error({ error: teamError }, 'Failed to create team account');
      throw new Error('Failed to create team account');
    }

    const teamId = teamData.id;
    const teamSlug = teamData.slug;

    logger.info({ teamId, teamSlug }, 'Team account created successfully');
    // 2. Create sample data based on the selected industry
    if (industry && sampleDataTemplates[industry]) {
      try {
        // Get the sample data template
        const templateData = sampleDataTemplates[industry];

        // Check if this is education industry
        if (industry === 'education') {
          // Use the comprehensive education sample data function
          const { data: sampleData, error: sampleError } = await adminClient
            .rpc('create_education_sample_data', {
              p_account_id: teamId,
            })
            .single();

          console.log('Education sample data creation response:', {
            sampleData,
            sampleError,
          });

          if (sampleError || !sampleData || !sampleData.success) {
            logger.error(
              {
                error: sampleError,
                errorMessage: sampleError?.message,
                errorDetails: sampleError?.details,
                errorCode: sampleError?.code,
                hint: sampleError?.hint,
                sampleData,
              },
              'Failed to create education sample data',
            );

            console.error(
              'Education sample data creation error:',
              JSON.stringify(sampleError, null, 2),
            );
          } else {
            logger.info(
              { sampleData },
              'Education sample data created successfully',
            );
          }
        } else {
          // Use regular commerce function for other industries
          const dataToSend = {
            categories: templateData.categories || [],
            products: templateData.products || [],
            branches: templateData.branches || [],
            branch_products: templateData.branch_products || [],
            customer_orders: templateData.customer_orders || [],
            account_themes: templateData.account_themes || [],
          };

          const industryData = JSON.stringify(dataToSend);

          const { data: sampleData, error: sampleError } = await adminClient
            .rpc('create_sample_data_for_account', {
              p_account_id: teamId,
              p_industry_template: industryData,
            })
            .single();

          console.log('Commerce sample data creation response:', {
            sampleData,
            sampleError,
          });

          if (sampleError || !sampleData || !sampleData.success) {
            logger.error(
              {
                error: sampleError,
                errorMessage: sampleError?.message,
                errorDetails: sampleError?.details,
                errorCode: sampleError?.code,
                hint: sampleError?.hint,
                sampleData,
              },
              'Failed to create commerce sample data',
            );

            console.error(
              'Commerce sample data creation error:',
              JSON.stringify(sampleError, null, 2),
            );
          } else {
            logger.info(
              { sampleData },
              'Commerce sample data created successfully',
            );
          }
        }
      } catch (error) {
        logger.error(
          {
            error,
            errorMessage: error?.message,
            errorDetails: error?.details,
            errorCode: error?.code,
            hint: error?.hint,
          },
          'Failed to create sample data',
        );

        console.error(
          'Sample data creation error:',
          JSON.stringify(error, null, 2),
        );

        // Continue even if sample data creation fails
        logger.warn(
          { teamId, industry },
          'Continuing team creation despite sample data failure',
        );
      }
    }

    // Revalidate the home page to update the user workspace data
    revalidatePath('/home');

    return {
      success: true,
      teamId,
      teamSlug,
    };
  } catch (error) {
    logger.error({ error }, 'Failed to create team with sample data');
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
