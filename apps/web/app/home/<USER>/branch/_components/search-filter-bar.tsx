'use client';

import { useCallback } from 'react';

import { debounce } from 'next/dist/server/utils';
import { useRouter, useSearchParams } from 'next/navigation';

import { useTranslation } from 'react-i18next';

import { CardTitle } from '@kit/ui/card';
import { Input } from '@kit/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kit/ui/select';

interface SearchAndFilterBarProps {
  initialSearch?: string;
  initialFilter?: string;
  accountSlug: string;
}

export function SearchAndFilterBar({
  initialSearch = '',
  initialFilter = 'all',
  accountSlug,
}: SearchAndFilterBarProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const searchParams = useSearchParams();

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams);
      if (value) {
        params.set(name, value);
      } else {
        params.delete(name);
      }
      return params.toString();
    },
    [searchParams],
  );

  const handleSearch = debounce((value: string) => {
    const queryString = createQueryString('search', value);
    router.push(`/home/<USER>/branch?${queryString}`);
  }, 500);

  const handleFilter = (value: string) => {
    const queryString = createQueryString(
      'filter',
      value === 'all' ? '' : value,
    );
    router.push(`/home/<USER>/branch?${queryString}`);
  };

  return (
    <CardTitle className="flex space-x-2">
      <Select defaultValue={initialFilter} onValueChange={handleFilter} data-testid="branch-page-filter">
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder={t('branch:status.label')} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t('branch:status.all')}</SelectItem>
          <SelectItem value="active">{t('branch:status.active')}</SelectItem>
          <SelectItem value="inactive">
            {t('branch:status.inactive')}
          </SelectItem>
        </SelectContent>
      </Select>
      <Input
        placeholder={t('branch:search.placeholder')}
        className="w-64"
        defaultValue={initialSearch}
        onChange={(e) => handleSearch(e.target.value)}
        data-testid="branch-page-search"
      />
    </CardTitle>
  );
}
