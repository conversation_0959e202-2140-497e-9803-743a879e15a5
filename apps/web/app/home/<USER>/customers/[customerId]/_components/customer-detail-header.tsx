'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';

import { formatDistanceToNow } from 'date-fns';
import { ChevronRight, Home, Mail, ShoppingBag, User } from 'lucide-react';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import { CustomerDetail } from '../_lib/server/customer-detail.loader';

interface CustomerDetailHeaderProps {
  customer: CustomerDetail;
}

export function CustomerDetailHeader({ customer }: CustomerDetailHeaderProps) {
  const { account } = useParams<{ account: string }>();
  const signupDate = new Date(customer.created_at);
  const timeAgo = formatDistanceToNow(signupDate, { addSuffix: true });

  return (
    <div className="mb-6">
      {/* Breadcrumb */}
      <div className="mb-6 flex items-center text-sm text-gray-600">
        <Link href="/home" className="flex items-center hover:text-blue-600">
          <Home className="mr-1 h-4 w-4" />
          <span>
            <Trans i18nKey="common:navigation.home">Trang chủ</Trans>
          </span>
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <Link
          href={`/home/<USER>/customers`}
          className="hover:text-blue-600"
        >
          <span>
            <Trans i18nKey="common:navigation.customers">Khách hàng</Trans>
          </span>
        </Link>
        <ChevronRight className="mx-2 h-4 w-4" />
        <span className="text-gray-800">{customer.name}</span>
      </div>

      {/* Customer Header */}
      <div className="relative flex flex-col items-center justify-center rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 p-6 text-center shadow-sm sm:flex-row sm:justify-start sm:text-left">
        <div className="relative h-20 w-20 overflow-hidden rounded-full border-4 border-white shadow-lg sm:h-24 sm:w-24 md:h-28 md:w-28">
          {customer.avatar_url ? (
            <Image
              src={customer.avatar_url}
              alt={customer.name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-blue-100 text-3xl font-bold text-blue-600">
              {customer.name.charAt(0)}
            </div>
          )}
          {customer.is_vip && (
            <div className="absolute -top-1 -right-1 flex h-7 w-7 items-center justify-center rounded-full bg-green-500 text-xs font-bold text-white shadow-md">
              <User className="h-4 w-4" />
            </div>
          )}
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-8">
          <h1 className="text-xl font-bold text-gray-900 sm:text-2xl md:text-3xl">
            {customer.name}
          </h1>
          <p className="mt-2 text-sm font-medium text-gray-500">
            {customer.customer_id ? (
              <>
                I-641 {customer.customer_id.slice(0, 3)}{' '}
                {customer.customer_id.slice(3, 6)}{' '}
                {customer.customer_id.slice(6, 9)}
              </>
            ) : (
              <Trans i18nKey="customers:detail.idNotAvailable">
                ID không khả dụng
              </Trans>
            )}
          </p>
          <p className="mt-1 text-sm text-gray-500">
            <Trans i18nKey="customers:detail.signedUp" values={{ timeAgo }}>
              Đăng ký {{ timeAgo }}
            </Trans>
          </p>
          <div className="mt-3 flex flex-wrap gap-2">
            <Badge className="bg-blue-100 text-blue-800">
              <Trans
                i18nKey="customers:detail.totalOrders"
                values={{ count: 5 }}
              >
                Đơn hàng: 5
              </Trans>
            </Badge>
            <Badge className="bg-green-100 text-green-800">
              <Trans
                i18nKey="customers:detail.totalSpent"
                values={{ amount: '1,250,000đ' }}
              >
                Chi tiêu: 1,250,000đ
              </Trans>
            </Badge>
          </div>
        </div>
        <div className="absolute top-4 right-4 flex items-center gap-2">
          <Button size="sm" variant="outline" className="rounded-full">
            <Mail className="mr-1 h-4 w-4" />
            <Trans i18nKey="customers:actions.contact">Liên hệ</Trans>
          </Button>
          <Button size="sm" variant="outline" className="rounded-full">
            <ShoppingBag className="mr-1 h-4 w-4" />
            <Trans i18nKey="customers:actions.newOrder">Đơn mới</Trans>
          </Button>
        </div>
      </div>
    </div>
  );
}
