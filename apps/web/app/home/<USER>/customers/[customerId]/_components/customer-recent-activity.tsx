'use client';

import { useState } from 'react';
import { ChevronRight, Mail, LogIn, ShoppingBag, HelpCircle, MessageSquare, UserCog, Clock } from 'lucide-react';
import { Trans } from '@kit/ui/trans';
import { Button } from '@kit/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { Switch } from '@kit/ui/switch';
import { CustomerDetail } from '../_lib/server/customer-detail.loader';

interface CustomerRecentActivityProps {
  customer: CustomerDetail;
}

export function CustomerRecentActivity({ customer }: CustomerRecentActivityProps) {
  const [autoRefresh, setAutoRefresh] = useState(true);
  const activities = customer.recent_activities || [];

  // Mock data for activities if none exist
  const mockActivities = [
    {
      type: 'email_sent',
      description: "Jenny sent an email about the new product.",
      date: new Date().toISOString(),
      formatted_date: "Today, 8:00 AM"
    },
    {
      type: 'login',
      description: "Jenny's last login to the Customer Portal.",
      date: new Date(Date.now() - 86400000 * 5).toISOString(),
      formatted_date: "5 days ago, 4:07 PM"
    },
    {
      type: 'purchase',
      description: "Explored niche demo ideas for product-specific solutions.",
      date: new Date(Date.now() - 86400000 * 21).toISOString(),
      formatted_date: "3 weeks ago, 4:07 PM"
    }
  ];

  // Use real data if available, otherwise use mock data
  const displayActivities = activities.length > 0 ? activities : mockActivities;

  if (displayActivities.length === 0) {
    return null;
  }

  // Function to get activity icon based on type
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'email_sent':
        return <Mail className="h-3 w-3" />;
      case 'login':
        return <LogIn className="h-3 w-3" />;
      case 'purchase':
        return <ShoppingBag className="h-3 w-3" />;
      case 'support_request':
        return <HelpCircle className="h-3 w-3" />;
      case 'feedback':
        return <MessageSquare className="h-3 w-3" />;
      case 'profile_update':
        return <UserCog className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  return (
    <Card className="mb-6 h-full overflow-hidden border border-gray-200 shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between border-b border-gray-100 bg-gray-50 pb-2">
        <CardTitle className="text-sm font-bold uppercase tracking-wider text-gray-700">
          <Trans i18nKey="customers:detail.recentActivity">HOẠT ĐỘNG GẦN ĐÂY</Trans>
        </CardTitle>
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">
            <Trans i18nKey="customers:detail.autoRefresh">Tự động làm mới:</Trans>
          </span>
          <Switch
            checked={autoRefresh}
            onCheckedChange={setAutoRefresh}
            className="data-[state=checked]:bg-blue-600"
          />
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="relative pl-6">
          {/* Timeline line */}
          <div className="absolute left-2.5 top-0 h-full w-px bg-gray-200"></div>

          <div className="space-y-6">
            {displayActivities.map((activity, index) => (
              <div key={index} className="relative">
                {/* Timeline dot */}
                <div className="absolute -left-6 flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                  {getActivityIcon(activity.type)}
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-700">{activity.description}</p>
                  <p className="mt-1 text-xs text-gray-500">{activity.formatted_date}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-6 border-t border-gray-100 pt-4 text-center">
          <Button variant="link" className="text-blue-600 hover:text-blue-800">
            <Trans i18nKey="customers:detail.allTime">TẤT CẢ THỜI GIAN</Trans>
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
