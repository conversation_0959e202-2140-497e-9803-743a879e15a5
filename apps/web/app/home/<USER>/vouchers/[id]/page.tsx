'use client';

import { useCallback, useEffect, useState } from 'react';

import Link from 'next/link';
import { usePara<PERSON>, useRouter } from 'next/navigation';

import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeft, Percent, ShoppingBag, Ticket, Users } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { z } from 'zod';

import { useSupabase } from '@kit/supabase/hooks/use-supabase';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { Button } from '@kit/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@kit/ui/alert-dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Checkbox } from '@kit/ui/checkbox';
import { DateTimePicker } from '@kit/ui/date-time-picker';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PageBody } from '@kit/ui/page';
import { RadioGroup, RadioGroupItem } from '@kit/ui/radio-group';
import { Separator } from '@kit/ui/separator';
import { Skeleton } from '@kit/ui/skeleton';
import { Textarea } from '@kit/ui/textarea';
import { Trans } from '@kit/ui/trans';

import { Voucher } from '~/lib/types/voucher';

import { TeamAccountLayoutPageHeader } from '../../_components/team-account-layout-page-header';
import { getProductsAndCategories } from '../_actions/get-products-categories';
import { CustomerPhoneInput } from '../_components/customer-phone-input';
import { ProductCategorySelector } from '../_components/product-category-selector';
import { ValidPeriodDisplay } from '../_components/valid-period-display';
import { deleteVoucherAction, updateVoucherAction } from '../_lib/server/voucher.actions';
import { createVoucherApi } from '../_lib/server/voucher.api';

const voucherFormSchema = z.object({
  code: z.string().min(1, { message: 'Code is required' }),
  name: z.string().min(1, { message: 'Name is required' }),
  description: z.string().optional(),
  discount_type: z.enum(['percentage', 'fixed']),
  discount_value: z
    .number()
    .min(0.01, { message: 'Discount value must be greater than 0' }),
  min_order_value: z.number().optional(),
  max_discount_value: z.number().optional(),
  max_uses: z.number().optional(),
  start_date: z.date({
    required_error: 'Start date is required',
  }),
  end_date: z.date({
    required_error: 'End date is required',
  }),
  status: z.enum(['active', 'expired', 'disabled']),
  // Advanced restrictions
  is_customer_specific: z.boolean().optional(),
  customer_phones: z.array(z.string()).optional(),
  usage_limit_per_customer: z.number().optional(),
  first_time_customers_only: z.boolean().optional(),
  min_previous_orders: z.number().optional(),
  included_product_ids: z.array(z.string()).default([]),
  excluded_product_ids: z.array(z.string()).default([]),
  included_category_ids: z.array(z.string()).default([]),
  excluded_category_ids: z.array(z.string()).default([]),
});

type VoucherFormValues = z.infer<typeof voucherFormSchema>;

export default function VoucherDetailPage() {
  const { account: accountSlug, id } = useParams<{
    account: string;
    id: string;
  }>();
  const router = useRouter();
  const supabase = useSupabase();
  const { t } = useTranslation();
  const { account } = useTeamAccountWorkspace();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [voucher, setVoucher] = useState<Voucher | null>(null);

  const [products, setProducts] = useState<{ id: string; name: string }[]>([]);
  const [categories, setCategories] = useState<{ id: string; name: string }[]>(
    [],
  );
  const [customerPhones, setCustomerPhones] = useState<string[]>([]);

  const form = useForm<VoucherFormValues>({
    resolver: zodResolver(voucherFormSchema),
    defaultValues: {
      code: '',
      name: '',
      description: '',
      discount_type: 'percentage',
      discount_value: 10,
      min_order_value: undefined,
      max_discount_value: undefined,
      max_uses: undefined,
      start_date: new Date(),
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      status: 'active',
    },
  });

  const discountType = form.watch('discount_type');

  // Load products and categories
  const loadProductsAndCategories = useCallback(async () => {
    if (!account?.id) return;

    try {
      const result = await getProductsAndCategories(account.id);
      if (result.success) {
        setProducts(result.products);
        setCategories(result.categories);
      }
    } catch (error) {
      console.error('Failed to load products and categories', error);
    }
  }, [account?.id]);

  useEffect(() => {
    loadProductsAndCategories();
  }, [loadProductsAndCategories]);

  // Fetch voucher when component mounts
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const teamAccountsApi = createTeamAccountsApi(supabase);
        const teamAccount = await teamAccountsApi.getTeamAccount(accountSlug);

        if (!teamAccount || !teamAccount.id) {
          throw new Error('Failed to fetch account');
        }

        // Fetch voucher
        const api = createVoucherApi(supabase);
        const voucherData = await api.getVoucherById(id);

        if (!voucherData) {
          throw new Error('Voucher not found');
        }

        setVoucher(voucherData);

        // Load customer phones if voucher is customer-specific
        let phones: string[] = [];
        if (voucherData.is_customer_specific) {
          try {
            const voucherWithPhones =
              await api.getVoucherWithCustomerPhones(id);
            if (
              voucherWithPhones?.customer_phones &&
              voucherWithPhones.customer_phones.length > 0
            ) {
              phones = voucherWithPhones.customer_phones.map(
                (p) => p.phone_number,
              );
              setCustomerPhones(phones);
            }
          } catch (phoneError) {
            console.error('Error fetching customer phones:', phoneError);
          }
        }

        // Set form values
        form.reset({
          code: voucherData.code,
          name: voucherData.name,
          description: voucherData.description || '',
          discount_type: voucherData.discount_type,
          discount_value: voucherData.discount_value,
          min_order_value: voucherData.min_order_value,
          max_discount_value: voucherData.max_discount_value,
          max_uses: voucherData.max_uses,
          start_date: new Date(voucherData.start_date),
          end_date: new Date(voucherData.end_date),
          status: voucherData.status,
          // Advanced restrictions
          is_customer_specific: voucherData.is_customer_specific || false,
          customer_phones: phones,
          usage_limit_per_customer:
            voucherData.usage_limit_per_customer || undefined,
          first_time_customers_only:
            voucherData.first_time_customers_only || false,
          min_previous_orders: voucherData.min_previous_orders || undefined,
          included_product_ids: voucherData.included_product_ids || [],
          excluded_product_ids: voucherData.excluded_product_ids || [],
          included_category_ids: voucherData.included_category_ids || [],
          excluded_category_ids: voucherData.excluded_category_ids || [],
        });
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error(
          error instanceof Error ? error.message : 'Failed to load data',
        );
        router.push(`/home/<USER>/vouchers`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [accountSlug, id, supabase, router, form]);

  const onSubmit = async (values: VoucherFormValues) => {
    if (!voucher) return;

    setIsSubmitting(true);

    try {
      const teamAccountsApi = createTeamAccountsApi(supabase);
      const teamAccount = await teamAccountsApi.getTeamAccount(accountSlug);

      if (!teamAccount || !teamAccount.id) {
        throw new Error('Failed to fetch account');
      }

      // Validate percentage discount
      if (
        values.discount_type === 'percentage' &&
        (values.discount_value <= 0 || values.discount_value > 100)
      ) {
        form.setError('discount_value', {
          type: 'manual',
          message: 'Percentage discount must be between 0 and 100',
        });
        setIsSubmitting(false);
        return;
      }

      const result = await updateVoucherAction({
        id: voucher.id,
        accountId: teamAccount.id,
        accountSlug,
        code: values.code.toUpperCase(),
        name: values.name,
        description: values.description,
        discount_type: values.discount_type,
        discount_value: values.discount_value,
        min_order_value: values.min_order_value,
        max_discount_value: values.max_discount_value,
        max_uses: values.max_uses,
        start_date: values.start_date.toISOString(),
        end_date: values.end_date.toISOString(),
        status: values.status,
        // Advanced restrictions
        is_customer_specific: values.is_customer_specific,
        customer_phones: values.customer_phones,
        usage_limit_per_customer: values.usage_limit_per_customer,
        first_time_customers_only: values.first_time_customers_only,
        min_previous_orders: values.min_previous_orders,
        included_product_ids: values.included_product_ids,
        excluded_product_ids: values.excluded_product_ids,
        included_category_ids: values.included_category_ids,
        excluded_category_ids: values.excluded_category_ids,
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to update voucher');
      }

      // If customer-specific and has phone numbers, update them
      if (
        values.is_customer_specific &&
        values.customer_phones &&
        values.customer_phones.length > 0
      ) {
        try {
          // Import the action dynamically to avoid circular dependencies
          const { addCustomerPhonesToVoucher } = await import(
            '../_actions/add-customer-phones'
          );

          // First, remove all existing phone numbers
          const { removeCustomerPhonesFromVoucher } = await import(
            '../_actions/remove-customer-phones'
          );
          await removeCustomerPhonesFromVoucher(
            id,
            customerPhones,
            accountSlug,
          );

          // Then add the new phone numbers
          await addCustomerPhonesToVoucher(
            id,
            values.customer_phones,
            accountSlug,
          );
        } catch (phoneError) {
          console.error(
            'Failed to update customer phones for voucher',
            phoneError,
          );
          // Continue anyway, we'll show a success message for the voucher update
        }
      }

      toast.success('Voucher updated successfully');

      // Refresh the page to show updated data
      router.refresh();
    } catch (error) {
      console.error('Error updating voucher:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to update voucher',
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    try {
      const teamAccountsApi = createTeamAccountsApi(supabase);
      const teamAccount = await teamAccountsApi.getTeamAccount(accountSlug);

      if (!teamAccount || !teamAccount.id) {
        throw new Error('Failed to fetch account');
      }

      if (!voucher) {
        throw new Error('Voucher not found');
      }

      const result = await deleteVoucherAction({
        id: voucher.id,
        accountId: teamAccount.id,
        accountSlug,
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete voucher');
      }

      toast.success('Voucher deleted successfully');

      // Redirect to vouchers list
      router.push(`/home/<USER>/vouchers`);
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to delete voucher'
      );
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'expired':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      case 'disabled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  if (isLoading) {
    return (
      <>
        <TeamAccountLayoutPageHeader
          title={<Trans i18nKey="vouchers:voucher_details" />}
          description={<AppBreadcrumbs />}
          account={accountSlug}
        />

        <PageBody>
          <div className="mx-auto w-full max-w-3xl">
            <div className="mb-6 flex items-center">
              <Skeleton className="h-10 w-32" />
            </div>

            <Card className="w-full">
              <CardHeader>
                <Skeleton className="h-8 w-2/3" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent className="space-y-6">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </CardContent>
              <CardFooter>
                <Skeleton className="h-10 w-32" />
              </CardFooter>
            </Card>
          </div>
        </PageBody>
      </>
    );
  }

  return (
    <>
      <TeamAccountLayoutPageHeader
        title={<Trans i18nKey="vouchers:voucher_details" />}
        description={<AppBreadcrumbs />}
        account={accountSlug}
      />

      <PageBody>
        <div className="mx-auto max-w-3xl">
          <div className="mb-6 flex items-center">
            <Button variant="ghost" size="sm" asChild className="mr-2">
              <Link href={`/home/<USER>/vouchers`}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                <Trans i18nKey="common:back">Back</Trans>
              </Link>
            </Button>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="rounded-full bg-blue-100 p-2 dark:bg-blue-900/20">
                    <Ticket className="h-5 w-5 text-blue-600 dark:text-blue-500" />
                  </div>
                  <div>
                    <CardTitle>{voucher?.name}</CardTitle>
                    <CardDescription>
                      {voucher?.code && (
                        <span className="font-mono text-xs font-medium uppercase">
                          {voucher.code}
                        </span>
                      )}
                      {voucher?.code && voucher?.status && (
                        <span className="mx-2">•</span>
                      )}
                      {voucher?.status && (
                        <span
                          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusBadgeClass(voucher.status)}`}
                        >
                          <Trans i18nKey={`vouchers:status.${voucher.status}`}>
                            {voucher.status.charAt(0).toUpperCase() +
                              voucher.status.slice(1)}
                          </Trans>
                        </span>
                      )}
                    </CardDescription>
                    {voucher?.start_date && voucher?.end_date && (
                      <ValidPeriodDisplay
                        startDate={new Date(voucher.start_date)}
                        endDate={new Date(voucher.end_date)}
                      />
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <CardContent className="space-y-6">
                  {/* Basic Information */}
                  <div className="space-y-4">
                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="code"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:code">Code</Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t('vouchers:code_placeholder')}
                                className="uppercase"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(e.target.value.toUpperCase())
                                }
                              />
                            </FormControl>
                            <FormDescription>
                              <Trans i18nKey="vouchers:code_description">
                                A unique code that customers will enter to apply
                                the discount.
                              </Trans>
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:name">Name</Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder={t('vouchers:name_placeholder')}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="vouchers:description">
                              Description
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder={t(
                                'vouchers:description_placeholder',
                              )}
                              className="resize-none"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            <Trans i18nKey="vouchers:status.label">
                              Status
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <select
                              className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                              {...field}
                            >
                              <option value="active">
                                {t('vouchers:active', 'Active')}
                              </option>
                              <option value="expired">
                                {t('vouchers:expired', 'Expired')}
                              </option>
                              <option value="disabled">
                                {t('vouchers:disabled', 'Disabled')}
                              </option>
                            </select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="discount_type"
                      render={({ field }) => (
                        <FormItem className="space-y-2">
                          <FormLabel>
                            <Trans i18nKey="vouchers:discount_type">
                              Discount Type
                            </Trans>
                          </FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              className="flex space-x-4"
                            >
                              <div className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem
                                    value="percentage"
                                    id="percentage"
                                  />
                                </FormControl>
                                <label htmlFor="percentage" className="text-sm">
                                  <Trans i18nKey="vouchers:percentage">
                                    Percentage
                                  </Trans>
                                </label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <FormControl>
                                  <RadioGroupItem value="fixed" id="fixed" />
                                </FormControl>
                                <label htmlFor="fixed" className="text-sm">
                                  <Trans i18nKey="vouchers:fixed">
                                    Fixed Amount
                                  </Trans>
                                </label>
                              </div>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="discount_value"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:discount_value">
                                Discount Value
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Input
                                  type="number"
                                  placeholder={
                                    discountType === 'percentage'
                                      ? t('vouchers:percentage_placeholder')
                                      : t('vouchers:fixed_placeholder')
                                  }
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(parseFloat(e.target.value))
                                  }
                                  value={field.value || ''}
                                />
                                {discountType === 'percentage' && (
                                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                    <Percent className="text-muted-foreground h-4 w-4" />
                                  </div>
                                )}
                              </div>
                            </FormControl>
                            <FormDescription>
                              {discountType === 'percentage' ? (
                                <Trans i18nKey="vouchers:percentage_description">
                                  Percentage discount (1-100)
                                </Trans>
                              ) : (
                                <Trans i18nKey="vouchers:fixed_description">
                                  Fixed amount discount
                                </Trans>
                              )}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="min_order_value"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:min_order_value">
                                Minimum Order Value
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder={t(
                                  'vouchers:min_order_value_placeholder',
                                )}
                                {...field}
                                onChange={(e) =>
                                  field.onChange(
                                    e.target.value
                                      ? parseFloat(e.target.value)
                                      : undefined,
                                  )
                                }
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormDescription>
                              <Trans i18nKey="vouchers:min_order_value_description">
                                Minimum order value required to use this voucher
                                (optional)
                              </Trans>
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="start_date"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>
                              <Trans i18nKey="vouchers:start_date">
                                Start Date
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <DateTimePicker
                                date={field.value}
                                setDate={field.onChange}
                                minDate={new Date('1900-01-01')}
                                locale={t('common:locale', 'vi')}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="end_date"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>
                              <Trans i18nKey="vouchers:end_date">
                                End Date
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <DateTimePicker
                                date={field.value}
                                setDate={field.onChange}
                                minDate={new Date('1900-01-01')}
                                locale={t('common:locale', 'vi')}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="max_uses"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:max_uses">
                                Maximum Uses
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder={t('vouchers:max_uses_placeholder')}
                                {...field}
                                onChange={(e) =>
                                  field.onChange(
                                    e.target.value
                                      ? parseInt(e.target.value, 10)
                                      : undefined,
                                  )
                                }
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormDescription>
                              <Trans i18nKey="vouchers:max_uses_description">
                                Maximum number of times this voucher can be used
                                (optional)
                              </Trans>
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {discountType === 'percentage' && (
                        <FormField
                          control={form.control}
                          name="max_discount_value"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                <Trans i18nKey="vouchers:max_discount_value">
                                  Maximum Discount Value
                                </Trans>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder={t(
                                    'vouchers:max_discount_value_placeholder',
                                  )}
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(
                                      e.target.value
                                        ? parseFloat(e.target.value)
                                        : undefined,
                                    )
                                  }
                                  value={field.value || ''}
                                />
                              </FormControl>
                              <FormDescription>
                                <Trans i18nKey="vouchers:max_discount_value_description">
                                  Maximum discount amount when using percentage
                                  discount (optional)
                                </Trans>
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>

                    {/* Customer Restrictions */}
                    <div className="mt-8 space-y-4">
                      <div className="flex items-center space-x-2">
                        <div className="rounded-full bg-blue-100 p-2 dark:bg-blue-900/20">
                          <Users className="h-5 w-5 text-blue-600 dark:text-blue-500" />
                        </div>
                        <h3 className="text-lg font-medium">
                          <Trans i18nKey="vouchers:customer_restrictions">
                            Customer Restrictions
                          </Trans>
                        </h3>
                      </div>

                      <FormField
                        control={form.control}
                        name="is_customer_specific"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-y-0 space-x-3 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                <Trans i18nKey="vouchers:is_customer_specific">
                                  Customer-specific voucher
                                </Trans>
                              </FormLabel>
                              <FormDescription>
                                <Trans i18nKey="vouchers:is_customer_specific_description">
                                  If enabled, this voucher can only be used by
                                  specific customers
                                </Trans>
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      {form.watch('is_customer_specific') && (
                        <FormField
                          control={form.control}
                          name="customer_phones"
                          render={({ field }) => (
                            <CustomerPhoneInput
                              value={field.value || []}
                              onChange={field.onChange}
                              disabled={isSubmitting}
                            />
                          )}
                        />
                      )}

                      <FormField
                        control={form.control}
                        name="usage_limit_per_customer"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <Trans i18nKey="vouchers:usage_limit_per_customer">
                                Usage Limit Per Customer
                              </Trans>
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder={t(
                                  'vouchers:usage_limit_per_customer_placeholder',
                                  '1',
                                )}
                                {...field}
                                onChange={(e) =>
                                  field.onChange(
                                    e.target.value
                                      ? parseInt(e.target.value, 10)
                                      : undefined,
                                  )
                                }
                                value={field.value || ''}
                              />
                            </FormControl>
                            <FormDescription>
                              <Trans i18nKey="vouchers:usage_limit_per_customer_description">
                                Maximum number of times a single customer can
                                use this voucher (optional)
                              </Trans>
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid gap-4 sm:grid-cols-2">
                        <FormField
                          control={form.control}
                          name="first_time_customers_only"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-y-0 space-x-3 rounded-md border p-4">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>
                                  <Trans i18nKey="vouchers:first_time_customers_only">
                                    First-time customers only
                                  </Trans>
                                </FormLabel>
                                <FormDescription>
                                  <Trans i18nKey="vouchers:first_time_customers_only_description">
                                    If enabled, this voucher can only be used by
                                    customers with no previous orders
                                  </Trans>
                                </FormDescription>
                              </div>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="min_previous_orders"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                <Trans i18nKey="vouchers:min_previous_orders">
                                  Minimum Previous Orders
                                </Trans>
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder={t(
                                    'vouchers:min_previous_orders_placeholder',
                                    '1',
                                  )}
                                  {...field}
                                  onChange={(e) =>
                                    field.onChange(
                                      e.target.value
                                        ? parseInt(e.target.value, 10)
                                        : undefined,
                                    )
                                  }
                                  value={field.value || ''}
                                />
                              </FormControl>
                              <FormDescription>
                                <Trans i18nKey="vouchers:min_previous_orders_description">
                                  Minimum number of previous orders required to
                                  use this voucher (optional)
                                </Trans>
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Product and Category Restrictions */}
                    <div className="mt-8 space-y-4">
                      <div className="flex items-center space-x-2">
                        <div className="rounded-full bg-green-100 p-2 dark:bg-green-900/20">
                          <ShoppingBag className="h-5 w-5 text-green-600 dark:text-green-500" />
                        </div>
                        <h3 className="text-lg font-medium">
                          <Trans i18nKey="vouchers:product_restrictions">
                            Product Restrictions
                          </Trans>
                        </h3>
                      </div>

                      <FormField
                        control={form.control}
                        name="included_product_ids"
                        render={({ field }) => (
                          <ProductCategorySelector
                            label={t('vouchers:included_products')}
                            description={t(
                              'vouchers:included_products_description',
                            )}
                            items={products}
                            value={field.value || []}
                            onChange={field.onChange}
                            placeholder={t('vouchers:select_products')}
                            emptyMessage={t('vouchers:no_products')}
                            disabled={isSubmitting}
                          />
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="excluded_product_ids"
                        render={({ field }) => (
                          <ProductCategorySelector
                            label={t('vouchers:excluded_products')}
                            description={t(
                              'vouchers:excluded_products_description',
                            )}
                            items={products}
                            value={field.value || []}
                            onChange={field.onChange}
                            placeholder={t('vouchers:select_products')}
                            emptyMessage={t('vouchers:no_products')}
                            disabled={isSubmitting}
                          />
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="included_category_ids"
                        render={({ field }) => (
                          <ProductCategorySelector
                            label={t('vouchers:included_categories')}
                            description={t(
                              'vouchers:included_categories_description',
                            )}
                            items={categories}
                            value={field.value || []}
                            onChange={field.onChange}
                            placeholder={t('vouchers:select_categories')}
                            emptyMessage={t('vouchers:no_categories')}
                            disabled={isSubmitting}
                          />
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="excluded_category_ids"
                        render={({ field }) => (
                          <ProductCategorySelector
                            label={t('vouchers:excluded_categories')}
                            description={t(
                              'vouchers:excluded_categories_description',
                            )}
                            items={categories}
                            value={field.value || []}
                            onChange={field.onChange}
                            placeholder={t('vouchers:select_categories')}
                            emptyMessage={t('vouchers:no_categories')}
                            disabled={isSubmitting}
                          />
                        )}
                      />
                    </div>

                    {voucher && voucher.uses_count > 0 && (
                      <>
                        <Separator />

                        <div>
                          <h3 className="mb-2 text-base font-medium">
                            <Trans i18nKey="vouchers:redemptions">
                              Redemptions
                            </Trans>
                          </h3>
                          <div className="rounded-md border p-4">
                            <div className="flex items-center justify-between">
                              <span className="text-sm">
                                <Trans i18nKey="vouchers:uses_count">
                                  Uses Count
                                </Trans>
                                :{' '}
                                <span className="font-medium">
                                  {voucher.uses_count}
                                </span>
                              </span>
                              {voucher.max_uses && (
                                <span className="text-sm">
                                  <Trans i18nKey="vouchers:max_uses">
                                    Maximum Uses
                                  </Trans>
                                  :{' '}
                                  <span className="font-medium">
                                    {voucher.max_uses}
                                  </span>
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>

                <CardFooter className="bg-muted/10 flex justify-between border-t px-6 py-4">
                  <div className="flex space-x-2">
                    <Button variant="outline" type="button" asChild>
                      <Link href={`/home/<USER>/vouchers`}>
                        <Trans i18nKey="common:cancel">Cancel</Trans>
                      </Link>
                    </Button>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" type="button">
                          <Trans i18nKey="vouchers:delete_voucher">
                            Delete Voucher
                          </Trans>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            <Trans i18nKey="vouchers:confirm_delete">
                              Are you sure you want to delete this voucher?
                            </Trans>
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            <Trans i18nKey="vouchers:confirm_delete_description">
                              This action cannot be undone. This will permanently delete the voucher.
                            </Trans>
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>
                            <Trans i18nKey="common:cancel">
                              Cancel
                            </Trans>
                          </AlertDialogCancel>
                          <AlertDialogAction onClick={handleDelete}>
                            <Trans i18nKey="common:delete">
                              Delete
                            </Trans>
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>

                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <Trans i18nKey="common:saving">Saving...</Trans>
                    ) : (
                      <Trans i18nKey="vouchers:update_voucher">
                        Update Voucher
                      </Trans>
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </Card>
        </div>
      </PageBody>
    </>
  );
}
