'use server';

import { revalidatePath } from 'next/cache';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { createVoucherApi } from '../_lib/server/voucher.api';

export async function removeCustomerPhonesFromVoucher(
  voucherId: string,
  phoneNumbers: string[],
  accountSlug: string,
) {
  const logger = await getLogger();
  const client = getSupabaseServerClient();
  const api = createVoucherApi(client);

  const ctx = {
    name: 'vouchers.remove-customer-phones',
    voucherId,
    phoneCount: phoneNumbers.length,
  };

  logger.info(ctx, 'Removing customer phones from voucher');

  try {
    const params = {
      voucher_id: voucherId,
      phone_numbers: phoneNumbers,
    };

    const result = await api.removeCustomerPhonesFromVoucher(params);

    if (!result.success) {
      logger.error(
        { ...ctx, error: result.message },
        'Failed to remove customer phones from voucher',
      );
      return {
        success: false,
        error: result.message || 'Failed to remove customer phones from voucher',
      };
    }

    logger.info(
      { ...ctx, removedCount: result.removed_count },
      'Customer phones removed from voucher successfully',
    );

    revalidatePath(`/home/<USER>/vouchers/${voucherId}`);

    return { success: true, removed_count: result.removed_count };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to remove customer phones from voucher');
    return { success: false, error: 'Failed to remove customer phones from voucher' };
  }
}
