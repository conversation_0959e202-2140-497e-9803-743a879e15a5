import { Suspense } from 'react';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { CardContent, CardHeader, CardTitle } from '@kit/ui/card';
import { If } from '@kit/ui/if';
import { PageBody } from '@kit/ui/page';
import { SearchListInput } from '@kit/ui/search-list-input';
import { Trans } from '@kit/ui/trans';

import { TeamAccountLayoutPageHeader } from '~/home/<USER>/_components/team-account-layout-page-header';
import { createI18nServerInstance } from '~/lib/i18n/i18n.server';

import { checkCanCreateResource } from '../_lib/server/resource-access';
import { loadTeamWorkspace } from '../_lib/server/team-account-workspace.loader';
import { MiniAppActions } from './_components/miniapp-actions';
import { MiniAppCard } from './_components/miniapp-card';
import { MiniAppErrorHandler } from './_components/miniapp-error-handler';
import { loadMiniApps } from './_lib/server/miniapp.loader';

export default async function MiniAppPage({
  params,
  searchParams,
}: {
  params: Promise<{ account: string }>;
  searchParams: Promise<{ search?: string; filter?: string }>;
}) {
  const { t } = await createI18nServerInstance();
  const client = getSupabaseServerClient();
  try {
    const [resolvedParams, resolvedSearchParams] = await Promise.all([
      params,
      searchParams,
    ]);

    const accountSlug = resolvedParams.account;
    const search = resolvedSearchParams.search;
    const status = resolvedSearchParams.filter;

    const api = createTeamAccountsApi(client);

    // First get the team account to get the actual UUID
    const teamAccount = await api.getTeamAccount(accountSlug);

    if (!teamAccount) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    // Now load miniapps using the team's UUID
    const [miniApps, { account, user }, resourceLimits] = await Promise.all([
      loadMiniApps(teamAccount.id, search, status),
      loadTeamWorkspace(accountSlug),
      checkCanCreateResource(teamAccount.id, 'miniapps'),
    ]);

    const { canCreate, reason, current, limit } = resourceLimits;

    if (!account) {
      return (
        <PageBody>
          <Alert variant="destructive">
            <AlertDescription>
              <Trans i18nKey="common:errors:teamNotFound">Team not found</Trans>
            </AlertDescription>
          </Alert>
        </PageBody>
      );
    }

    const canManageMiniApps =
      account?.permissions?.includes('miniapps.manage') ?? false;

    // Kiểm tra quyền đã được thực hiện ở sidebar menu
    // Nếu người dùng có thể truy cập trang này, họ đã có quyền

    return (
      <>
        <TeamAccountLayoutPageHeader
          title={<Trans i18nKey="common:routes:miniapps">MiniApps</Trans>}
          description={<AppBreadcrumbs />}
          account={account.slug}
        />

        <PageBody data-testid="miniapp-page">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle>
              <SearchListInput
                defaultValue={search}
                placeholder={t('miniapp:search', 'Search mini apps...')}
              />
            </CardTitle>

            <If condition={canManageMiniApps}>
              <MiniAppActions
                accountSlug={account.slug}
                canCreate={canCreate}
                reason={reason}
                resourceCurrent={current}
                resourceLimit={limit}
              />
            </If>
          </CardHeader>

          <CardContent>
            <Suspense fallback={<div>Loading...</div>}>
              {miniApps.length > 0 ? (
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {miniApps.map((miniApp) => (
                    <MiniAppCard
                      key={miniApp.id}
                      miniApp={miniApp}
                      accountSlug={accountSlug}
                    />
                  ))}
                </div>
              ) : (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">
                    <Trans i18nKey="miniapp:noMiniApps">
                      No mini apps found
                    </Trans>
                  </p>
                </div>
              )}
            </Suspense>
          </CardContent>
        </PageBody>
        <MiniAppErrorHandler accountSlug={accountSlug} />
      </>
    );
  } catch (error: any) {
    // Use error logging that works on the server
    const errorDetails = {
      message: error.message,
      params: JSON.stringify(await params), // Make sure to await params before stringifying
      context: 'miniapps.page',
    };

    // Log to server console
    console.error(JSON.stringify(errorDetails));

    return (
      <PageBody>
        <Alert variant="destructive">
          <AlertDescription>
            <Trans i18nKey="common:errors:loadingFailed">
              Failed to load data. Please try again later.
            </Trans>
          </AlertDescription>
        </Alert>
      </PageBody>
    );
  }
}
