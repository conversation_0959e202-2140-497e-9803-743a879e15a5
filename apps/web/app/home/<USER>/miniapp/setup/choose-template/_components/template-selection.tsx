'use client';

import { useState } from 'react';

import { Trans } from '@kit/ui/trans';

import type { Template } from '../_lib/server/template.loader';
import { NavigationButtons } from './navigation-buttons';
import { TemplateCard } from './template-card';

interface TemplateSelectionProps {
  account: string;
  templates: Template[];
  miniAppId: string;
  currentStep: string;
}

export function TemplateSelection({
  account,
  templates,
  miniAppId,
  currentStep,
}: TemplateSelectionProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);

  return (
    <>
      <div className="mb-4 flex items-center justify-between">
        <div>
          <p className="text-gray-500">
            <Trans i18nKey="miniapp:setup:chooseTemplateDescription">
              Select or customize your UI theme
            </Trans>
          </p>
        </div>
      </div>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        {templates.map((template) => (
          <TemplateCard
            key={template.id}
            template={template}
            selected={selectedTemplate === template.id}
            onSelect={setSelectedTemplate}
          />
        ))}
      </div>

      <NavigationButtons
        account={account}
        currentStep={currentStep}
        themeId={selectedTemplate}
        miniAppId={miniAppId}
        isDisabled={!selectedTemplate}
      />
    </>
  );
}
