import { cn } from '@kit/ui/utils';

import type { Template } from '../_lib/server/template.loader';

interface TemplateCardProps {
  template: Template;
  selected: boolean;
  onSelect: (id: string) => void;
}

export function TemplateCard({
  template,
  selected,
  onSelect,
}: TemplateCardProps) {
  return (
    <div
      onClick={() => onSelect(template.id)}
      className={cn(
        'group relative cursor-pointer overflow-hidden rounded-xl',
        'transform transition-all duration-300 hover:scale-[1.02]',
        'bg-white shadow-sm hover:shadow-md',
        selected && 'ring-2 ring-blue-600',
      )}
    >
      {/* Template preview image */}
      <div className="relative aspect-[3/4] overflow-hidden">
        <img
          src={template.thumbnail_url || '/placeholder.png'}
          alt={template.name}
          className="h-full w-full object-contain"
        />

        {/* Overlay on hover or selected */}
        <div
          className={cn(
            'absolute inset-0 flex items-center justify-center bg-black/40',
            'transition-opacity duration-300',
            selected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100',
          )}
        >
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-white">
            <svg
              className={cn(
                'h-6 w-6 transition-colors duration-300',
                selected ? 'text-blue-600' : 'text-gray-400',
              )}
              fill="none"
              strokeWidth="2"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Template info */}
      <div className="p-4">
        <h3 className="mb-1 font-medium text-gray-900">{template.name}</h3>
        <p className="line-clamp-2 text-sm text-gray-500">
          {template.description}
        </p>

        {/* Template type badge */}
        <div className="mt-3">
          <span
            className={cn(
              'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium',
              template.type === 'free'
                ? 'bg-green-100 text-green-800'
                : template.type === 'premium'
                  ? 'bg-purple-100 text-purple-800'
                  : 'bg-gray-100 text-gray-800',
            )}
          >
            {template.type}
          </span>
        </div>
      </div>
    </div>
  );
}
