'use server';

import { nanoid } from 'nanoid';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import {
  DEFAULT_COLORS,
  DEFAULT_THEME_NAME,
  DEFAULT_TYPOGRAPHY,
  EMPTY_PUCK_DATA,
} from '../constants/theme-defaults';
import type { PuckData, ThemeConfig } from '../types/theme-config.types';

// Essential theme defaults using shared constants
const DEFAULT_THEME: ThemeConfig = {
  name: DEFAULT_THEME_NAME,
  puckData: EMPTY_PUCK_DATA,
  colors: DEFAULT_COLORS,
  typography: DEFAULT_TYPOGRAPHY,
};

/**
 * Maps Puck root props to ThemeConfig structure
 * Extracts theme configuration from puckData.root.props and maps to ThemeConfig
 */
function mapPuckDataToThemeConfig(
  puckData: PuckData,
  existingConfig: Partial<ThemeConfig> = {},
): ThemeConfig {
  const rootProps = puckData.root?.props || {};

  // Extract colors from root props with full structure
  const colors = {
    primary: {
      main: rootProps.primaryColor || DEFAULT_COLORS.primary.main,
      light: rootProps.primaryLightColor || DEFAULT_COLORS.primary.light,
      dark: rootProps.primaryDarkColor || DEFAULT_COLORS.primary.dark,
    },
    secondary: {
      main: rootProps.secondaryColor || DEFAULT_COLORS.secondary.main,
      light: rootProps.secondaryLightColor || DEFAULT_COLORS.secondary.light,
      dark: rootProps.secondaryDarkColor || DEFAULT_COLORS.secondary.dark,
    },
    accent: {
      main: rootProps.accentColor || DEFAULT_COLORS.accent.main,
      light: rootProps.accentLightColor || DEFAULT_COLORS.accent.light,
      dark: rootProps.accentDarkColor || DEFAULT_COLORS.accent.dark,
    },
    background: {
      default: rootProps.backgroundColor || DEFAULT_COLORS.background.default,
      paper: rootProps.paperBackgroundColor || DEFAULT_COLORS.background.paper,
    },
    text: {
      primary: rootProps.textColor || DEFAULT_COLORS.text.primary,
      secondary: rootProps.secondaryTextColor || DEFAULT_COLORS.text.secondary,
    },
  };

  // Extract typography from root props with full structure
  const typography = {
    fontFamily: rootProps.fontFamily || DEFAULT_TYPOGRAPHY.fontFamily,
    headings: {
      fontFamily:
        rootProps.headingsFontFamily ||
        rootProps.fontFamily ||
        DEFAULT_TYPOGRAPHY.headings.fontFamily,
      fontWeight:
        rootProps.headingsFontWeight || DEFAULT_TYPOGRAPHY.headings.fontWeight,
    },
    body: {
      fontFamily:
        rootProps.bodyFontFamily ||
        rootProps.fontFamily ||
        DEFAULT_TYPOGRAPHY.body.fontFamily,
      fontWeight:
        rootProps.bodyFontWeight || DEFAULT_TYPOGRAPHY.body.fontWeight,
    },
  };

  return {
    name: rootProps.title || existingConfig.name || 'My Theme',
    author_id: existingConfig.author_id,
    template_id: existingConfig.template_id,
    template: existingConfig.template,
    puckData,
    colors,
    typography,
  };
}

async function getExistingTempTheme(
  accountId: string,
  themeId?: string,
  editThemeId?: string,
) {
  const supabase = getSupabaseServerClient();

  try {
    // Build query conditions
    const query = supabase
      .from('temp_themes')
      .select('*')
      .eq('account_id', accountId);

    if (editThemeId) {
      query.eq('account_theme_id', editThemeId);
    } else {
      query.is('account_theme_id', null);
    }

    if (themeId) {
      query.eq('theme_id', themeId);
    } else {
      query.is('theme_id', null);
    }

    // Execute query
    const { data: existingTempThemes, error: searchError } = await query
      .order('created_at', { ascending: false })
      .limit(1);

    if (searchError) {
      throw searchError;
    }

    const result = existingTempThemes?.[0];
    return result;
  } catch (error) {
    console.error('Error searching for existing temp theme:', error);
    return null;
  }
}

export async function getInitialTheme(
  accountId: string,
  themeId?: string,
  editThemeId?: string,
  tempThemeId?: string,
): Promise<ThemeConfig> {
  try {
    const supabase = getSupabaseServerClient();

    // 1. Load from temp theme if tempThemeId provided
    if (tempThemeId) {
      const { data: tempTheme } = await supabase
        .from('temp_themes')
        .select('config')
        .eq('id', tempThemeId)
        .single();

      if (tempTheme?.config) {
        return {
          ...DEFAULT_THEME,
          ...tempTheme.config,
          puckData: tempTheme.config.puckData || EMPTY_PUCK_DATA,
        };
      }
    }

    // 2. Check for existing temp theme
    const existingTempTheme = await getExistingTempTheme(
      accountId,
      themeId,
      editThemeId,
    );
    if (existingTempTheme?.config) {
      return {
        ...DEFAULT_THEME,
        ...existingTempTheme.config,
        puckData: existingTempTheme.config.puckData || EMPTY_PUCK_DATA,
      };
    }

    // 3. Load from account theme if editing
    if (editThemeId) {
      const { data: accountTheme } = await supabase
        .from('account_themes')
        .select('*, theme:template_id(*)')
        .eq('id', editThemeId)
        .single();

      if (accountTheme) {
        return {
          ...DEFAULT_THEME,
          name: accountTheme.name || DEFAULT_THEME.name,
          author_id: accountTheme.theme?.author_id,
          ...accountTheme.config,
          puckData: accountTheme.config?.puckData || EMPTY_PUCK_DATA,
        };
      }
    }

    // 4. Load from theme template if creating new
    if (themeId) {
      const { data: theme } = await supabase
        .from('themes')
        .select('*')
        .eq('id', themeId)
        .single();

      if (theme) {
        return {
          ...DEFAULT_THEME,
          name: `${theme.name} (copy)`,
          author_id: theme.author_id,
          ...theme.config,
          puckData: theme.config?.puckData || EMPTY_PUCK_DATA,
        };
      }
    }

    // 5. Return default if nothing found
    return { ...DEFAULT_THEME };
  } catch (error) {
    console.error('Error getting theme:', error);
    return { ...DEFAULT_THEME };
  }
}

export async function createOrUpdateTempTheme(
  accountId: string,
  config: ThemeConfig,
  themeId?: string,
  editThemeId?: string,
) {
  const supabase = getSupabaseServerClient();
  const previewToken = nanoid();
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);

  try {
    // Map puckData to ThemeConfig if puckData exists
    let finalConfig = config;
    if (config.puckData) {
      finalConfig = mapPuckDataToThemeConfig(config.puckData, config);
    }

    // Check for existing temp theme
    const existingTempTheme = await getExistingTempTheme(
      accountId,
      themeId,
      editThemeId,
    );
    // If we found an existing temp theme, update it
    if (existingTempTheme) {
      const { data: updatedTempTheme, error: updateError } = await supabase
        .from('temp_themes')
        .update({
          config: finalConfig,
          preview_token: previewToken,
          expires_at: expiresAt,
          updated_at: new Date(),
        })
        .eq('id', existingTempTheme.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating temp theme:', updateError);
        throw updateError;
      }

      return updatedTempTheme;
    }

    // Validate that editThemeId exists in account_themes if provided
    let validatedEditThemeId = null;
    if (editThemeId) {
      const { data: existingAccountTheme } = await supabase
        .from('account_themes')
        .select('id')
        .eq('id', editThemeId)
        .maybeSingle();

      if (existingAccountTheme) {
        validatedEditThemeId = editThemeId;
      }
    }

    // Ensure at least one of theme_id or account_theme_id is not null
    // to satisfy the theme_reference_check constraint
    const finalThemeId = themeId || null;
    const finalAccountThemeId = validatedEditThemeId;

    if (!finalThemeId && !finalAccountThemeId) {
      // If neither is provided, try to find any existing account theme for this account
      // as a fallback to satisfy the constraint
      const { data: anyAccountTheme } = await supabase
        .from('account_themes')
        .select('id')
        .eq('account_id', accountId)
        .limit(1)
        .maybeSingle();

      if (anyAccountTheme) {
        validatedEditThemeId = anyAccountTheme.id;
      } else {
        throw new Error(
          'No valid theme reference found. Please provide either theme_id or account_theme_id.',
        );
      }
    }

    const { data: tempTheme, error: insertError } = await supabase
      .from('temp_themes')
      .insert({
        account_id: accountId,
        theme_id: finalThemeId,
        account_theme_id: validatedEditThemeId, // Use the validated value
        config: finalConfig,
        preview_token: previewToken,
        expires_at: expiresAt,
      })
      .select()
      .single();

    if (insertError) throw insertError;
    return tempTheme;
  } catch (error) {
    console.error('Error in createOrUpdateTempTheme:', error);
    throw error;
  }
}
