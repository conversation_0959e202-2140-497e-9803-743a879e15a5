// Marketing Templates for Education - Complete Page Templates
// Following correct Puck data model structure with Slot pattern

export const MARKETING_TEMPLATES = [
  {
    id: 'complete-landing-page',
    name: '🏠 Trang Chủ Hoàn Chỉnh',
    description: 'Landing page đầy đủ với hero, features, testimonials và CTA',
    category: 'landing',
    thumbnail: '/images/templates/complete-landing-page.jpg',
    data: {
      content: [
        {
          type: 'Text',
          props: {
            id: 'hero-title',
            text: '🌟 Trung Tâm Giáo Dục Sao Mai',
            tag: 'h1',
            size: '3xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '0 0 16px 0',
          },
        },
        {
          type: 'Text',
          props: {
            id: 'hero-subtitle',
            text: 'Nơi ươm mầm tài năng - Phát triển toàn diện cho con em bạn',
            tag: 'p',
            size: 'lg',
            weight: 'medium',
            align: 'center',
            color: '#374151',
            margin: '0 0 32px 0',
          },
        },
        {
          type: 'Button',
          props: {
            id: 'hero-cta',
            label: '📞 Đăng <PERSON>',
            variant: 'primary',
            size: 'large',
            fullWidth: false,
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'features-grid',
            columnCount: 2,
            columnGap: '1rem',
            rowGap: '1rem',
            items: [
              {
                type: 'Card',
                props: {
                  id: 'feature-1',
                  title: '🎯 Chương Trình Cá Nhân Hóa',
                  subtitle: 'Thiết kế riêng cho từng học sinh',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#f0f9ff',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'feature-1-detail',
                        text: 'Mỗi học sinh có phương pháp học riêng biệt phù hợp với khả năng và sở thích cá nhân.',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#4b5563',
                        margin: '8px 0',
                      },
                    },
                  ],
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'feature-2',
                  title: '👨‍🏫 Đội Ngũ Giáo Viên Chuyên Nghiệp',
                  subtitle: 'Kinh nghiệm 10+ năm giảng dạy',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#f0fdf4',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'feature-2-detail',
                        text: 'Đội ngũ giáo viên được đào tạo bài bản, có chứng chỉ quốc tế và kinh nghiệm thực tế.',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#4b5563',
                        margin: '8px 0',
                      },
                    },
                  ],
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'feature-3',
                  title: '📊 Theo Dõi Tiến Độ',
                  subtitle: 'Báo cáo chi tiết cho phụ huynh',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#fef3c7',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'feature-3-detail',
                        text: 'Hệ thống báo cáo tiến độ học tập hàng tuần giúp phụ huynh nắm bắt tình hình con em.',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#4b5563',
                        margin: '8px 0',
                      },
                    },
                  ],
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'feature-4',
                  title: '🏆 Cam Kết Chất Lượng',
                  subtitle: 'Hoàn tiền 100% nếu không hài lòng',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#fce7f3',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'feature-4-detail',
                        text: 'Chúng tôi tự tin về chất lượng giảng dạy và cam kết hoàn tiền nếu không đạt kết quả.',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#4b5563',
                        margin: '8px 0',
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          type: 'Text',
          props: {
            id: 'testimonials-title',
            text: '💬 Phụ Huynh Nói Gì Về Chúng Tôi',
            tag: 'h2',
            size: '2xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '48px 0 24px 0',
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'testimonials-grid',
            columnCount: 1,
            rowGap: '1rem',
            items: [
              {
                type: 'Card',
                props: {
                  id: 'testimonial-1',
                  title: '⭐⭐⭐⭐⭐ Chị Nguyễn Thị Lan',
                  subtitle: 'Phụ huynh học sinh Minh An',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#f0f9ff',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'testimonial-1-content',
                        text: '"Con tôi đã tiến bộ rất nhiều sau 3 tháng học tại trung tâm. Các thầy cô rất tận tâm và chuyên nghiệp. Điểm toán của con đã tăng từ 6 lên 8.5!"',
                        tag: 'p',
                        size: 'base',
                        align: 'left',
                        color: '#374151',
                        margin: '8px 0',
                        weight: 'medium',
                      },
                    },
                  ],
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'testimonial-2',
                  title: '⭐⭐⭐⭐⭐ Anh Trần Văn Nam',
                  subtitle: 'Phụ huynh học sinh Bảo Ngọc',
                  padding: '20px',
                  shadow: 'md',
                  borderRadius: '12px',
                  backgroundColor: '#f0fdf4',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'testimonial-2-content',
                        text: '"Chương trình học rất phù hợp với lứa tuổi. Con tôi rất thích đến lớp và học tập tích cực hơn. Đặc biệt là môn tiếng Anh, con đã có thể giao tiếp cơ bản."',
                        tag: 'p',
                        size: 'base',
                        align: 'left',
                        color: '#374151',
                        margin: '8px 0',
                        weight: 'medium',
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          type: 'Text',
          props: {
            id: 'contact-title',
            text: '📞 Liên Hệ Ngay Hôm Nay',
            tag: 'h2',
            size: '2xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '48px 0 16px 0',
          },
        },
        {
          type: 'List',
          props: {
            id: 'contact-info',
            items: [
              {
                id: 'phone',
                title: '📱 Hotline: 0283456789',
                subtitle: 'Tư vấn 24/7',
                prefix: '📞',
              },
              {
                id: 'address',
                title: '📍 123 Đường ABC, Quận 1, TP.HCM',
                subtitle: 'Ghé thăm cơ sở',
                prefix: '🏢',
              },
              {
                id: 'email',
                title: '✉️ <EMAIL>',
                subtitle: 'Gửi email tư vấn',
                prefix: '📧',
              },
            ],
            divider: true,
            clickable: true,
          },
        },
      ],
      root: {
        props: {
          title: 'Trung Tâm Giáo Dục Sao Mai',
          spacing: '16px',
          textColor: '#1f2937',
          fontFamily: 'Inter, system-ui, sans-serif',
          accentColor: '#f59e0b',
          borderRadius: '8px',
          primaryColor: '#2563eb',
          bodyFontFamily: 'Inter, system-ui, sans-serif',
          bodyFontWeight: '400',
          secondaryColor: '#10b981',
          accentDarkColor: '#d97706',
          backgroundColor: '#ffffff',
          accentLightColor: '#fbbf24',
          primaryDarkColor: '#1d4ed8',
          primaryLightColor: '#3b82f6',
          headingsFontFamily: 'Inter, system-ui, sans-serif',
          headingsFontWeight: '600',
          secondaryDarkColor: '#4338ca',
          secondaryTextColor: '#4b5563',
          secondaryLightColor: '#6366f1',
          paperBackgroundColor: '#f3f4f6',
        },
      },
      zones: {},
    },
  },

  {
    id: 'programs-detailed-page',
    name: '🎓 Trang Chương Trình Chi Tiết',
    description: 'Trang giới thiệu đầy đủ các chương trình học với giá cả và lịch học',
    category: 'programs',
    thumbnail: '/images/templates/programs-detailed-page.jpg',
    data: {
      content: [
        {
          type: 'Text',
          props: {
            id: 'programs-header',
            text: '🎓 Các Chương Trình Đào Tạo',
            tag: 'h1',
            size: '3xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '0 0 32px 0',
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'programs-grid',
            columnCount: 1,
            rowGap: '2rem',
            items: [
              {
                type: 'Card',
                props: {
                  id: 'program-math',
                  title: '🧮 Toán Tư Duy Sáng Tạo',
                  subtitle: 'Phát triển tư duy logic và khả năng giải quyết vấn đề',
                  padding: '24px',
                  shadow: 'lg',
                  borderRadius: '16px',
                  backgroundColor: '#f0f9ff',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'math-details',
                        text: '• Độ tuổi: 6-12 tuổi\n• Thời gian: 2 buổi/tuần, 90 phút/buổi\n• Học phí: 1.200.000đ/tháng\n• Giáo viên: Thạc sĩ Toán học',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#374151',
                        margin: '12px 0',
                      },
                    },
                    {
                      type: 'Button',
                      props: {
                        id: 'math-register',
                        label: '📝 Đăng Ký Học Thử',
                        variant: 'primary',
                        size: 'medium',
                        fullWidth: true,
                      },
                    },
                  ],
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'program-english',
                  title: '🗣️ Tiếng Anh Giao Tiếp',
                  subtitle: 'Học tiếng Anh qua hoạt động vui chơi và thực hành',
                  padding: '24px',
                  shadow: 'lg',
                  borderRadius: '16px',
                  backgroundColor: '#f0fdf4',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'english-details',
                        text: '• Độ tuổi: 4-15 tuổi\n• Thời gian: 3 buổi/tuần, 60 phút/buổi\n• Học phí: 1.500.000đ/tháng\n• Giáo viên: Bản ngữ + Việt Nam',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#374151',
                        margin: '12px 0',
                      },
                    },
                    {
                      type: 'Button',
                      props: {
                        id: 'english-register',
                        label: '📝 Đăng Ký Học Thử',
                        variant: 'primary',
                        size: 'medium',
                        fullWidth: true,
                      },
                    },
                  ],
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'program-art',
                  title: '🎨 Mỹ Thuật Sáng Tạo',
                  subtitle: 'Phát triển khả năng thẩm mỹ và sáng tạo nghệ thuật',
                  padding: '24px',
                  shadow: 'lg',
                  borderRadius: '16px',
                  backgroundColor: '#fef3c7',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'art-details',
                        text: '• Độ tuổi: 5-16 tuổi\n• Thời gian: 2 buổi/tuần, 120 phút/buổi\n• Học phí: 800.000đ/tháng\n• Giáo viên: Cử nhân Mỹ thuật',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#374151',
                        margin: '12px 0',
                      },
                    },
                    {
                      type: 'Button',
                      props: {
                        id: 'art-register',
                        label: '📝 Đăng Ký Học Thử',
                        variant: 'primary',
                        size: 'medium',
                        fullWidth: true,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          type: 'Text',
          props: {
            id: 'promotion-title',
            text: '🎉 Ưu Đãi Đặc Biệt Tháng Này',
            tag: 'h2',
            size: '2xl',
            weight: 'bold',
            align: 'center',
            color: '#dc2626',
            margin: '48px 0 16px 0',
          },
        },
        {
          type: 'Card',
          props: {
            id: 'promotion-card',
            title: '💝 Khuyến Mãi Hấp Dẫn',
            subtitle: 'Chỉ áp dụng cho 50 học sinh đầu tiên',
            padding: '24px',
            shadow: 'lg',
            borderRadius: '16px',
            backgroundColor: '#fef2f2',
            content: [
              {
                type: 'Text',
                props: {
                  id: 'promotion-details',
                  text: '✨ Giảm 30% học phí tháng đầu\n🎁 Tặng bộ dụng cụ học tập\n📚 Miễn phí sách giáo trình\n🏆 Bảo đảm kết quả hoặc học lại miễn phí',
                  tag: 'p',
                  size: 'base',
                  align: 'left',
                  color: '#374151',
                  margin: '12px 0',
                  weight: 'medium',
                },
              },
            ],
          },
        },
        {
          type: 'Button',
          props: {
            id: 'register-btn',
            label: '🎯 Đăng Ký Ngay - Nhận Ưu Đãi',
            variant: 'primary',
            size: 'large',
            fullWidth: true,
          },
        },
      ],
      root: {
        props: {
          title: 'Chương Trình Đào Tạo',
          spacing: '16px',
          textColor: '#1f2937',
          fontFamily: 'Inter, system-ui, sans-serif',
          accentColor: '#f59e0b',
          borderRadius: '8px',
          primaryColor: '#2563eb',
          bodyFontFamily: 'Inter, system-ui, sans-serif',
          bodyFontWeight: '400',
          secondaryColor: '#10b981',
          accentDarkColor: '#d97706',
          backgroundColor: '#ffffff',
          accentLightColor: '#fbbf24',
          primaryDarkColor: '#1d4ed8',
          primaryLightColor: '#3b82f6',
          headingsFontFamily: 'Inter, system-ui, sans-serif',
          headingsFontWeight: '600',
          secondaryDarkColor: '#4338ca',
          secondaryTextColor: '#4b5563',
          secondaryLightColor: '#6366f1',
          paperBackgroundColor: '#f3f4f6',
        },
      },
      zones: {},
    },
  },

  {
    id: 'registration-form-page',
    name: '📝 Trang Đăng Ký Học',
    description: 'Form đăng ký học chi tiết với thông tin học sinh và phụ huynh',
    category: 'registration',
    thumbnail: '/images/templates/registration-form-page.jpg',
    data: {
      content: [
        {
          type: 'Text',
          props: {
            id: 'form-header',
            text: '📝 Đăng Ký Học Tại Trung Tâm',
            tag: 'h1',
            size: '3xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '0 0 16px 0',
          },
        },
        {
          type: 'Text',
          props: {
            id: 'form-subtitle',
            text: 'Điền thông tin để chúng tôi tư vấn chương trình phù hợp nhất cho con bạn',
            tag: 'p',
            size: 'base',
            weight: 'normal',
            align: 'center',
            color: '#6b7280',
            margin: '0 0 32px 0',
          },
        },
        {
          type: 'Text',
          props: {
            id: 'parent-info-title',
            text: '👨‍👩‍👧‍👦 Thông Tin Phụ Huynh',
            tag: 'h2',
            size: 'xl',
            weight: 'semibold',
            align: 'left',
            color: '#1f2937',
            margin: '24px 0 16px 0',
          },
        },
        {
          type: 'Input',
          props: {
            id: 'parent-name',
            label: 'Họ tên phụ huynh *',
            placeholder: 'Nhập họ tên của bạn',
            required: true,
          },
        },
        {
          type: 'Input',
          props: {
            id: 'phone-number',
            label: 'Số điện thoại *',
            placeholder: 'Nhập số điện thoại',
            required: true,
          },
        },
        {
          type: 'Input',
          props: {
            id: 'email',
            label: 'Email',
            placeholder: 'Nhập email (không bắt buộc)',
            required: false,
          },
        },
        {
          type: 'Text',
          props: {
            id: 'child-info-title',
            text: '👶 Thông Tin Học Sinh',
            tag: 'h2',
            size: 'xl',
            weight: 'semibold',
            align: 'left',
            color: '#1f2937',
            margin: '32px 0 16px 0',
          },
        },
        {
          type: 'Input',
          props: {
            id: 'child-name',
            label: 'Họ tên học sinh *',
            placeholder: 'Nhập họ tên con em',
            required: true,
          },
        },
        {
          type: 'Select',
          props: {
            id: 'child-age',
            label: 'Độ tuổi *',
            placeholder: 'Chọn độ tuổi',
            options: [
              { label: '3-4 tuổi', value: '3-4' },
              { label: '5-6 tuổi', value: '5-6' },
              { label: '7-8 tuổi', value: '7-8' },
              { label: '9-10 tuổi', value: '9-10' },
              { label: '11-12 tuổi', value: '11-12' },
              { label: '13-15 tuổi', value: '13-15' },
              { label: '16+ tuổi', value: '16+' },
            ],
          },
        },
        {
          type: 'Select',
          props: {
            id: 'program-interest',
            label: 'Chương trình quan tâm *',
            placeholder: 'Chọn chương trình',
            options: [
              { label: '🧮 Toán Tư Duy Sáng Tạo', value: 'math' },
              { label: '🗣️ Tiếng Anh Giao Tiếp', value: 'english' },
              { label: '🎨 Mỹ Thuật Sáng Tạo', value: 'art' },
              { label: '🎵 Âm Nhạc', value: 'music' },
              { label: '💻 Lập Trình Thiếu Nhi', value: 'coding' },
              { label: '🤸‍♀️ Thể Dục Thể Thao', value: 'sports' },
            ],
          },
        },
        {
          type: 'TextArea',
          props: {
            id: 'additional-notes',
            label: 'Ghi chú thêm',
            placeholder: 'Chia sẻ thêm về mong muốn, tính cách của con...',
            rows: 3,
          },
        },
        {
          type: 'Button',
          props: {
            id: 'submit-registration',
            label: '📝 Gửi Đăng Ký & Nhận Tư Vấn',
            variant: 'primary',
            size: 'large',
            fullWidth: true,
          },
        },
        {
          type: 'Text',
          props: {
            id: 'contact-note',
            text: '📞 Chúng tôi sẽ liên hệ trong vòng 2 giờ để tư vấn chi tiết',
            tag: 'p',
            size: 'sm',
            weight: 'medium',
            align: 'center',
            color: '#10b981',
            margin: '16px 0 0 0',
          },
        },
      ],
      root: {
        props: {
          title: 'Đăng Ký Học',
          spacing: '16px',
          textColor: '#1f2937',
          fontFamily: 'Inter, system-ui, sans-serif',
          accentColor: '#f59e0b',
          borderRadius: '8px',
          primaryColor: '#2563eb',
          bodyFontFamily: 'Inter, system-ui, sans-serif',
          bodyFontWeight: '400',
          secondaryColor: '#10b981',
          accentDarkColor: '#d97706',
          backgroundColor: '#ffffff',
          accentLightColor: '#fbbf24',
          primaryDarkColor: '#1d4ed8',
          primaryLightColor: '#3b82f6',
          headingsFontFamily: 'Inter, system-ui, sans-serif',
          headingsFontWeight: '600',
          secondaryDarkColor: '#4338ca',
          secondaryTextColor: '#4b5563',
          secondaryLightColor: '#6366f1',
          paperBackgroundColor: '#f3f4f6',
        },
      },
      zones: {},
    },
  },

  {
    id: 'success-stories-page',
    name: '🏆 Trang Câu Chuyện Thành Công',
    description: 'Showcase học sinh xuất sắc và thành tích của trung tâm',
    category: 'testimonials',
    thumbnail: '/images/templates/success-stories-page.jpg',
    data: {
      content: [
        {
          type: 'Text',
          props: {
            id: 'success-header',
            text: '🏆 Câu Chuyện Thành Công',
            tag: 'h1',
            size: '3xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '0 0 16px 0',
          },
        },
        {
          type: 'Text',
          props: {
            id: 'success-subtitle',
            text: 'Những thành tích đáng tự hào của học sinh Trung Tâm Sao Mai',
            tag: 'p',
            size: 'lg',
            weight: 'medium',
            align: 'center',
            color: '#374151',
            margin: '0 0 32px 0',
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'achievements-grid',
            columnCount: 2,
            columnGap: '1rem',
            rowGap: '1rem',
            items: [
              {
                type: 'Card',
                props: {
                  id: 'achievement-1',
                  title: '🥇 Giải Nhất Toán Quốc Gia',
                  subtitle: 'Nguyễn Minh An - Lớp 6',
                  padding: '20px',
                  shadow: 'lg',
                  borderRadius: '12px',
                  backgroundColor: '#fef3c7',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'achievement-1-detail',
                        text: 'Sau 2 năm học tại trung tâm, An đã đạt giải Nhất cuộc thi Toán học Quốc gia dành cho học sinh THCS.',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#374151',
                        margin: '8px 0',
                      },
                    },
                  ],
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'achievement-2',
                  title: '🌟 IELTS 7.5 ở tuổi 14',
                  subtitle: 'Trần Bảo Ngọc - Lớp 8',
                  padding: '20px',
                  shadow: 'lg',
                  borderRadius: '12px',
                  backgroundColor: '#f0fdf4',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'achievement-2-detail',
                        text: 'Ngọc đã đạt IELTS 7.5 chỉ sau 18 tháng học tiếng Anh tại trung tâm, mở ra cơ hội du học sớm.',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#374151',
                        margin: '8px 0',
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          type: 'Text',
          props: {
            id: 'stats-title',
            text: '📊 Thống Kê Thành Tích',
            tag: 'h2',
            size: '2xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '48px 0 24px 0',
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'stats-grid',
            columnCount: 3,
            columnGap: '1rem',
            rowGap: '1rem',
            items: [
              {
                type: 'Card',
                props: {
                  id: 'stat-1',
                  title: '95%',
                  subtitle: 'Học sinh đạt điểm A',
                  padding: '16px',
                  shadow: 'md',
                  borderRadius: '8px',
                  backgroundColor: '#f0f9ff',
                  content: [],
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'stat-2',
                  title: '50+',
                  subtitle: 'Giải thưởng đạt được',
                  padding: '16px',
                  shadow: 'md',
                  borderRadius: '8px',
                  backgroundColor: '#f0fdf4',
                  content: [],
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'stat-3',
                  title: '1000+',
                  subtitle: 'Học sinh thành công',
                  padding: '16px',
                  shadow: 'md',
                  borderRadius: '8px',
                  backgroundColor: '#fef3c7',
                  content: [],
                },
              },
            ],
          },
        },
        {
          type: 'Button',
          props: {
            id: 'join-success',
            label: '🚀 Tham Gia Cùng Chúng Tôi',
            variant: 'primary',
            size: 'large',
            fullWidth: true,
          },
        },
      ],
      root: {
        props: {
          title: 'Câu Chuyện Thành Công',
          spacing: '16px',
          textColor: '#1f2937',
          fontFamily: 'Inter, system-ui, sans-serif',
          accentColor: '#f59e0b',
          borderRadius: '8px',
          primaryColor: '#2563eb',
          bodyFontFamily: 'Inter, system-ui, sans-serif',
          bodyFontWeight: '400',
          secondaryColor: '#10b981',
          accentDarkColor: '#d97706',
          backgroundColor: '#ffffff',
          accentLightColor: '#fbbf24',
          primaryDarkColor: '#1d4ed8',
          primaryLightColor: '#3b82f6',
          headingsFontFamily: 'Inter, system-ui, sans-serif',
          headingsFontWeight: '600',
          secondaryDarkColor: '#4338ca',
          secondaryTextColor: '#4b5563',
          secondaryLightColor: '#6366f1',
          paperBackgroundColor: '#f3f4f6',
        },
      },
      zones: {},
    },
  },

  {
    id: 'teacher-introduction-page',
    name: '👨‍🏫 Trang Giới Thiệu Đội Ngũ',
    description: 'Giới thiệu chi tiết về đội ngũ giáo viên và phương pháp giảng dạy',
    category: 'about',
    thumbnail: '/images/templates/teacher-introduction-page.jpg',
    data: {
      content: [
        {
          type: 'Text',
          props: {
            id: 'teachers-header',
            text: '👨‍🏫 Đội Ngũ Giáo Viên Xuất Sắc',
            tag: 'h1',
            size: '3xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '0 0 16px 0',
          },
        },
        {
          type: 'Text',
          props: {
            id: 'teachers-subtitle',
            text: 'Những người thầy tận tâm, giàu kinh nghiệm và đầy nhiệt huyết',
            tag: 'p',
            size: 'lg',
            weight: 'medium',
            align: 'center',
            color: '#374151',
            margin: '0 0 32px 0',
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'teachers-grid',
            columnCount: 1,
            rowGap: '2rem',
            items: [
              {
                type: 'Card',
                props: {
                  id: 'teacher-1',
                  title: '👩‍🏫 Cô Nguyễn Thị Mai',
                  subtitle: 'Giáo viên Toán - Thạc sĩ Toán học',
                  padding: '24px',
                  shadow: 'lg',
                  borderRadius: '16px',
                  backgroundColor: '#f0f9ff',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'teacher-1-info',
                        text: '• 12 năm kinh nghiệm giảng dạy\n• Thạc sĩ Toán học - ĐH Khoa học Tự nhiên\n• Chuyên gia phương pháp tư duy sáng tạo\n• Đã đào tạo 500+ học sinh đạt giải cao',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#374151',
                        margin: '12px 0',
                      },
                    },
                  ],
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'teacher-2',
                  title: '👨‍🏫 Thầy John Smith',
                  subtitle: 'Giáo viên Tiếng Anh - Bản ngữ Mỹ',
                  padding: '24px',
                  shadow: 'lg',
                  borderRadius: '16px',
                  backgroundColor: '#f0fdf4',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'teacher-2-info',
                        text: '• 8 năm kinh nghiệm tại Việt Nam\n• Cử nhân Ngôn ngữ học - UCLA\n• Chứng chỉ TESOL quốc tế\n• Chuyên gia giao tiếp và phát âm',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#374151',
                        margin: '12px 0',
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          type: 'Text',
          props: {
            id: 'method-title',
            text: '🎯 Phương Pháp Giảng Dạy',
            tag: 'h2',
            size: '2xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '48px 0 24px 0',
          },
        },
        {
          type: 'Card',
          props: {
            id: 'teaching-method',
            title: '✨ Phương Pháp Độc Quyền',
            subtitle: 'Kết hợp truyền thống và hiện đại',
            padding: '24px',
            shadow: 'lg',
            borderRadius: '16px',
            backgroundColor: '#fef3c7',
            content: [
              {
                type: 'Text',
                props: {
                  id: 'method-details',
                  text: '🎮 Học qua trò chơi và hoạt động thực hành\n📱 Ứng dụng công nghệ hiện đại\n👥 Lớp học nhỏ, chú ý cá nhân\n📊 Đánh giá tiến độ thường xuyên\n🏆 Động viên và khen thưởng tích cực',
                  tag: 'p',
                  size: 'base',
                  align: 'left',
                  color: '#374151',
                  margin: '12px 0',
                  weight: 'medium',
                },
              },
            ],
          },
        },
        {
          type: 'Button',
          props: {
            id: 'meet-teachers',
            label: '🤝 Gặp Gỡ Đội Ngũ Giáo Viên',
            variant: 'primary',
            size: 'large',
            fullWidth: true,
          },
        },
      ],
      root: {
        props: {
          title: 'Đội Ngũ Giáo Viên',
          spacing: '16px',
          textColor: '#1f2937',
          fontFamily: 'Inter, system-ui, sans-serif',
          accentColor: '#f59e0b',
          borderRadius: '8px',
          primaryColor: '#2563eb',
          bodyFontFamily: 'Inter, system-ui, sans-serif',
          bodyFontWeight: '400',
          secondaryColor: '#10b981',
          accentDarkColor: '#d97706',
          backgroundColor: '#ffffff',
          accentLightColor: '#fbbf24',
          primaryDarkColor: '#1d4ed8',
          primaryLightColor: '#3b82f6',
          headingsFontFamily: 'Inter, system-ui, sans-serif',
          headingsFontWeight: '600',
          secondaryDarkColor: '#4338ca',
          secondaryTextColor: '#4b5563',
          secondaryLightColor: '#6366f1',
          paperBackgroundColor: '#f3f4f6',
        },
      },
      zones: {},
    },
  },

  {
    id: 'pricing-promotion-page',
    name: '💰 Trang Bảng Giá & Khuyến Mãi',
    description: 'Hiển thị bảng giá chi tiết và các chương trình khuyến mãi hấp dẫn',
    category: 'pricing',
    thumbnail: '/images/templates/pricing-promotion-page.jpg',
    data: {
      content: [
        {
          type: 'Text',
          props: {
            id: 'pricing-header',
            text: '💰 Bảng Giá & Khuyến Mãi',
            tag: 'h1',
            size: '3xl',
            weight: 'bold',
            align: 'center',
            color: '#1e40af',
            margin: '0 0 16px 0',
          },
        },
        {
          type: 'Text',
          props: {
            id: 'pricing-subtitle',
            text: 'Học phí hợp lý - Chất lượng vượt trội - Nhiều ưu đãi hấp dẫn',
            tag: 'p',
            size: 'lg',
            weight: 'medium',
            align: 'center',
            color: '#374151',
            margin: '0 0 32px 0',
          },
        },
        {
          type: 'Grid',
          props: {
            id: 'pricing-grid',
            columnCount: 1,
            rowGap: '1.5rem',
            items: [
              {
                type: 'Card',
                props: {
                  id: 'price-basic',
                  title: '📚 Gói Cơ Bản',
                  subtitle: 'Phù hợp cho học sinh mới bắt đầu',
                  padding: '24px',
                  shadow: 'lg',
                  borderRadius: '16px',
                  backgroundColor: '#f0f9ff',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'basic-details',
                        text: '💵 Học phí: 1.000.000đ/tháng\n⏰ 2 buổi/tuần, 90 phút/buổi\n👥 Lớp 8-10 học sinh\n📖 Tặng sách giáo trình\n📊 Báo cáo tiến độ hàng tháng',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#374151',
                        margin: '12px 0',
                      },
                    },
                    {
                      type: 'Button',
                      props: {
                        id: 'choose-basic',
                        label: '✅ Chọn Gói Này',
                        variant: 'primary',
                        size: 'medium',
                        fullWidth: true,
                      },
                    },
                  ],
                },
              },
              {
                type: 'Card',
                props: {
                  id: 'price-premium',
                  title: '⭐ Gói Cao Cấp',
                  subtitle: 'Dành cho học sinh muốn phát triển toàn diện',
                  padding: '24px',
                  shadow: 'lg',
                  borderRadius: '16px',
                  backgroundColor: '#fef3c7',
                  content: [
                    {
                      type: 'Text',
                      props: {
                        id: 'premium-details',
                        text: '💵 Học phí: 1.500.000đ/tháng\n⏰ 3 buổi/tuần, 90 phút/buổi\n👥 Lớp 5-6 học sinh\n📖 Tặng full bộ tài liệu\n📊 Báo cáo tiến độ hàng tuần\n🎯 Hỗ trợ cá nhân 1-1',
                        tag: 'p',
                        size: 'sm',
                        align: 'left',
                        color: '#374151',
                        margin: '12px 0',
                      },
                    },
                    {
                      type: 'Button',
                      props: {
                        id: 'choose-premium',
                        label: '⭐ Chọn Gói Này',
                        variant: 'primary',
                        size: 'medium',
                        fullWidth: true,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          type: 'Text',
          props: {
            id: 'promotion-title',
            text: '🎉 Khuyến Mãi Đặc Biệt',
            tag: 'h2',
            size: '2xl',
            weight: 'bold',
            align: 'center',
            color: '#dc2626',
            margin: '48px 0 24px 0',
          },
        },
        {
          type: 'Card',
          props: {
            id: 'special-offer',
            title: '🔥 Ưu Đãi Tháng 12',
            subtitle: 'Chỉ còn 15 suất cuối cùng!',
            padding: '24px',
            shadow: 'lg',
            borderRadius: '16px',
            backgroundColor: '#fef2f2',
            content: [
              {
                type: 'Text',
                props: {
                  id: 'offer-details',
                  text: '🎁 Giảm 40% học phí tháng đầu\n📚 Tặng bộ sách trị giá 500.000đ\n🎒 Tặng balo và dụng cụ học tập\n🏆 Cam kết hoàn tiền 100% nếu không hài lòng\n⏰ Ưu đãi có hiệu lực đến 31/12',
                  tag: 'p',
                  size: 'base',
                  align: 'left',
                  color: '#374151',
                  margin: '12px 0',
                  weight: 'medium',
                },
              },
              {
                type: 'Button',
                props: {
                  id: 'claim-offer',
                  label: '🚀 Nhận Ưu Đãi Ngay',
                  variant: 'primary',
                  size: 'large',
                  fullWidth: true,
                },
              },
            ],
          },
        },
      ],
      root: {
        props: {
          title: 'Bảng Giá & Khuyến Mãi',
          spacing: '16px',
          textColor: '#1f2937',
          fontFamily: 'Inter, system-ui, sans-serif',
          accentColor: '#f59e0b',
          borderRadius: '8px',
          primaryColor: '#2563eb',
          bodyFontFamily: 'Inter, system-ui, sans-serif',
          bodyFontWeight: '400',
          secondaryColor: '#10b981',
          accentDarkColor: '#d97706',
          backgroundColor: '#ffffff',
          accentLightColor: '#fbbf24',
          primaryDarkColor: '#1d4ed8',
          primaryLightColor: '#3b82f6',
          headingsFontFamily: 'Inter, system-ui, sans-serif',
          headingsFontWeight: '600',
          secondaryDarkColor: '#4338ca',
          secondaryTextColor: '#4b5563',
          secondaryLightColor: '#6366f1',
          paperBackgroundColor: '#f3f4f6',
        },
      },
      zones: {},
    },
  },
];

export default MARKETING_TEMPLATES;
