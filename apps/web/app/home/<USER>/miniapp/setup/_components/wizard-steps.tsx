'use client';

import { Trans } from '@kit/ui/trans';
import { cn } from '@kit/ui/utils';

interface WizardStepsProps {
  currentStep: string;
}

export function WizardSteps({ currentStep }: WizardStepsProps) {
  const steps = [
    {
      id: '1',
      label: <Trans i18nKey="miniapp:setup:steps:choose:label" />,
      description: <Trans i18nKey="miniapp:setup:steps:choose:description" />,
      // icon: '/images/checkmark-icon.svg',
    },
    {
      id: '2',
      label: <Trans i18nKey="miniapp:setup:steps:customize:label" />,
      description: (
        <Trans i18nKey="miniapp:setup:steps:customize:description" />
      ),
    },
    {
      id: '3',
      label: <Trans i18nKey="miniapp:setup:steps:preview:label" />,
      description: <Trans i18nKey="miniapp:setup:steps:preview:description" />,
    },
  ];

  return (
    <div className="mx-auto flex w-full max-w-[940px] flex-col items-center">
      <div className="flex w-full rounded-lg border border-gray-200">
        {steps.map((step, index) => {
          const isCurrent = step.id === currentStep;

          return (
            <div
              key={step.id}
              className={cn(
                'flex-1 pt-4',
                index !== 0 && 'flex flex-col items-center pb-4',
              )}
            >
              <div
                className={cn(
                  'flex items-start',
                  index === 0 ? 'mx-6' : index === 1 ? 'w-64' : 'ml-6',
                  'mb-3',
                )}
              >
                <button
                  type="button"
                  className={cn(
                    'flex h-10 w-10 flex-col items-center justify-center rounded-full border-2',
                    isCurrent ? 'border-zinc-900' : 'border-gray-300',
                    'transition-colors duration-200',
                  )}
                  disabled
                >
                  {isCurrent && step.icon ? (
                    <img
                      src={step.icon}
                      className="h-6 w-6"
                      alt=""
                      aria-hidden="true"
                    />
                  ) : (
                    <span
                      className={cn(
                        'text-base font-bold',
                        isCurrent ? 'text-zinc-900' : 'text-gray-400',
                      )}
                    >
                      {`0${index + 1}`}
                    </span>
                  )}
                </button>
                <div className="ml-3 flex flex-col pt-1">
                  <span
                    className={cn(
                      'mb-2 text-sm font-bold',
                      isCurrent ? 'text-zinc-900' : 'text-zinc-500',
                    )}
                  >
                    {step.label}
                  </span>
                  <span
                    className={cn(
                      'text-sm',
                      isCurrent ? 'text-zinc-800' : 'text-zinc-500',
                    )}
                  >
                    {step.description}
                  </span>
                </div>
              </div>
              {isCurrent && (
                <div className="mx-6 h-1.5 rounded bg-zinc-900 shadow-md" />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
