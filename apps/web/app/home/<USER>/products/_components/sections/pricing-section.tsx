'use client';

import { useState } from 'react';

import { ChevronDown } from 'lucide-react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Button } from '@kit/ui/button';
import { Card } from '@kit/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@kit/ui/collapsible';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import { PriceInput } from '@kit/ui/price-input';
import { Trans } from '@kit/ui/trans';

export function PricingSection() {
  const [isOpen, setIsOpen] = useState(true);
  const { control } = useFormContext();
  const { t } = useTranslation();

  return (
    <Card className="p-6">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="flex w-full items-center justify-between p-0"
          >
            <h3 className="text-lg font-semibold">
              <Trans i18nKey="products:pricing.title">Pricing</Trans>
            </h3>
            <ChevronDown
              className={`h-4 w-4 transform transition-transform duration-200 ${
                isOpen ? 'rotate-180' : ''
              }`}
            />
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent className="mt-4 space-y-4">
          <FormField
            control={control}
            name="price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey="products:pricing.price.label">Price</Trans>
                </FormLabel>
                <FormControl>
                  <PriceInput
                    value={field.value}
                    onChange={field.onChange}
                    placeholder={t('products:pricing.price.placeholder')}
                    data-testid="product-price-input"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="compare_at_price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey="products:pricing.compare_price.label">
                    Compare at Price
                  </Trans>
                </FormLabel>
                <FormControl>
                  <PriceInput
                    value={field.value}
                    onChange={field.onChange}
                    placeholder={t('products:pricing.compare_price.placeholder')}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="cost_per_item"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey="products:pricing.cost.label">Cost per Item</Trans>
                </FormLabel>
                <FormControl>
                  <PriceInput value={field.value} onChange={field.onChange} placeholder={t('products:pricing.cost.placeholder')} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="tax_rate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  <Trans i18nKey="products:pricing.tax.label">Tax Rate (%)</Trans>
                </FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) => field.onChange(parseFloat(e.target.value))}
                    className="text-right"
                    placeholder={t('products:pricing.tax.placeholder')}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}
