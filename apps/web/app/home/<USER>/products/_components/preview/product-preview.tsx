'use client';

import { UseFormReturn } from 'react-hook-form';

import { FormattedPrice } from '../../../../../../components/currency/price-display';
import { Card } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';

import { ProductFormData } from '../../_lib/types';

interface ProductPreviewProps {
  form: UseFormReturn<ProductFormData>;
}

export function ProductPreview({ form }: ProductPreviewProps) {
  const watchedValues = form.watch();

  return (
    <Card className="p-4">
      <h3 className="mb-4 font-semibold">
        <Trans i18nKey="products:preview:title">Preview</Trans>
      </h3>

      <div className="space-y-4">
        {/* Preview Image */}
        <div className="bg-muted aspect-square rounded-lg border">
          {watchedValues.image_url && (
            <img
              src={watchedValues.image_url}
              alt={watchedValues.name}
              className="h-full w-full rounded-lg object-cover"
            />
          )}
        </div>

        {/* Product Info */}
        <div className="space-y-2">
          <h4 className="font-medium">
            {watchedValues.name || 'Tên sản phẩm'}
          </h4>
          <div className="space-y-1">
            {watchedValues.compare_at_price > 0 &&
              watchedValues.compare_at_price > watchedValues.price && (
                <p className="text-muted-foreground text-sm line-through">
                  <FormattedPrice amount={watchedValues.compare_at_price} />
                </p>
              )}
            <p className="text-2xl font-bold">
              <FormattedPrice amount={watchedValues.price} size="xl" />
            </p>
          </div>
          <div
            className="text-muted-foreground prose prose-sm line-clamp-3 text-sm"
            dangerouslySetInnerHTML={{
              __html: watchedValues.description || 'Mô tả...',
            }}
          />
        </div>
      </div>
    </Card>
  );
}
