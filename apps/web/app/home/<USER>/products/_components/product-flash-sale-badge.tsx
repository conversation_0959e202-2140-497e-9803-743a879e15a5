'use client';

import { useEffect, useState } from 'react';

import { Badge } from '@kit/ui/badge';
import { Card, CardContent } from '@kit/ui/card';
import { Progress } from '@kit/ui/progress';
import { Trans } from '@kit/ui/trans';
import { formatDistanceToNow } from 'date-fns';

interface ProductFlashSaleBadgeProps {
  flashSale: {
    flash_sale_id: string;
    flash_sale_name: string;
    discount_percentage: number;
    start_time: string;
    end_time: string;
    quantity_limit?: number;
    quantity_sold?: number;
  };
}

export function ProductFlashSaleBadge({ flashSale }: ProductFlashSaleBadgeProps) {
  const [timeLeft, setTimeLeft] = useState<string>('');
  const [progress, setProgress] = useState<number>(0);

  useEffect(() => {
    // Update time left
    const updateTimeLeft = () => {
      const now = new Date();
      const endTime = new Date(flashSale.end_time);
      
      if (now >= endTime) {
        setTimeLeft('Ended');
        return;
      }
      
      setTimeLeft(formatDistanceToNow(endTime, { addSuffix: true }));
    };

    // Update progress if quantity limit exists
    if (flashSale.quantity_limit) {
      const sold = flashSale.quantity_sold || 0;
      const limit = flashSale.quantity_limit;
      setProgress(Math.min(100, (sold / limit) * 100));
    }

    updateTimeLeft();
    const interval = setInterval(updateTimeLeft, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [flashSale]);

  return (
    <Card className="mb-4 border-red-500">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <Badge variant="destructive" className="text-sm">
            <Trans i18nKey="products:flash_sale">Flash Sale</Trans>
          </Badge>
          <span className="text-sm font-medium text-red-600">
            -{flashSale.discount_percentage}%
          </span>
        </div>
        
        <h3 className="text-base font-medium mb-1">{flashSale.flash_sale_name}</h3>
        
        <div className="text-sm text-muted-foreground mb-2">
          <Trans i18nKey="products:ends">Ends</Trans> {timeLeft}
        </div>
        
        {flashSale.quantity_limit && (
          <div className="mt-2">
            <div className="flex justify-between text-xs mb-1">
              <span>
                <Trans i18nKey="products:sold">Sold</Trans>: {flashSale.quantity_sold || 0}/{flashSale.quantity_limit}
              </span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
