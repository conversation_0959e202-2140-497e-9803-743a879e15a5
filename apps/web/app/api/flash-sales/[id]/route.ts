import { enhance<PERSON>outeHand<PERSON> } from '@kit/next/routes';

import { createCorsResponse } from '~/lib/cors';

/**
 * @swagger
 * /api/flash-sales/{id}:
 *   get:
 *     summary: Get flash sale details by ID
 *     description: Retrieves detailed information about a specific flash sale
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Flash sale ID
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Flash sale details
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       404:
 *         description: Flash sale not found
 *       500:
 *         description: Internal server error
 */
export const GET = enhanceRouteHandler(
  async ({ request, supabase, user, params }) => {
    try {
      const { id } = params;
      if (!id) {
        return createCorsResponse(
          request,
          { success: false, error: 'Flash sale ID is required' },
          400,
        );
      }

      const account_id = user.user_metadata?.account_id;
      if (!account_id) {
        return createCorsResponse(
          request,
          { success: false, error: 'No account_id found in token' },
          401,
        );
      }

      // Get flash sale details
      const { data: flashSaleData, error: flashSaleError } = await supabase
        .from('flash_sales')
        .select(
          `
          id,
          name,
          description,
          start_time,
          end_time,
          status,
          created_at,
          updated_at
          `,
        )
        .eq('id', id)
        .eq('account_id', account_id)
        .single();

      if (flashSaleError) {
        if (flashSaleError.code === 'PGRST116') {
          return createCorsResponse(
            request,
            { success: false, error: 'Flash sale not found' },
            404,
          );
        }
        throw new Error(flashSaleError.message || 'Database query failed');
      }

      // Get products in the flash sale
      const { data: productsData, error: productsError } = await supabase
        .from('flash_sale_products')
        .select(
          `
          id,
          product_id,
          discount_percentage,
          quantity_limit,
          quantity_sold,
          products(
            id,
            name,
            price,
            image_url,
            category_id,
            categories(
              id,
              name
            )
          )
          `,
        )
        .eq('flash_sale_id', id);

      if (productsError) {
        throw new Error(productsError.message || 'Database query failed');
      }

      // Format products data
      const products = (productsData || []).map((product) => {
        const originalPrice = product.products.price;
        const discountedPrice = originalPrice * (1 - product.discount_percentage / 100);
        
        return {
          id: product.product_id,
          name: product.products.name,
          image_url: product.products.image_url,
          category: {
            id: product.products.categories?.id,
            name: product.products.categories?.name,
          },
          original_price: originalPrice,
          discounted_price: discountedPrice,
          discount_percentage: product.discount_percentage,
          quantity_limit: product.quantity_limit,
          quantity_sold: product.quantity_sold,
        };
      });

      // Check if flash sale is active
      const now = new Date();
      const startTime = new Date(flashSaleData.start_time);
      const endTime = new Date(flashSaleData.end_time);
      const isActive = 
        flashSaleData.status === 'active' && 
        now >= startTime && 
        now <= endTime;

      // Calculate time remaining
      const timeRemaining = isActive ? Math.max(0, endTime.getTime() - now.getTime()) : 0;
      const hoursRemaining = Math.floor(timeRemaining / (1000 * 60 * 60));
      const minutesRemaining = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));

      return createCorsResponse(
        request,
        {
          success: true,
          data: {
            ...flashSaleData,
            is_active: isActive,
            time_remaining: {
              milliseconds: timeRemaining,
              hours: hoursRemaining,
              minutes: minutesRemaining,
              formatted: `${hoursRemaining}h ${minutesRemaining}m`,
            },
            total_products: products.length,
            products: products,
          },
        },
        200,
      );
    } catch (error: any) {
      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Internal server error',
          details: error.message,
        },
        500,
      );
    }
  },
  { auth: true },
);
