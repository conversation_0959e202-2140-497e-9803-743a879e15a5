import { z } from 'zod';

import { enhanceRouteHandler } from '@kit/next/routes';

import { createCorsResponse } from '~/lib/cors';

const CheckoutItemSchema = z.object({
  product_id: z.string().uuid(),
  quantity: z.number().int().positive(),
  attributes: z
    .array(
      z.object({
        id: z.string().uuid(),
        name: z.string(),
        value: z.string(),
        price_modifier: z.number(),
      }),
    )
    .optional(),
});

const CheckoutCalculateSchema = z.object({
  items: z.array(CheckoutItemSchema),
  voucher_code: z.string().optional(),
});

/**
 * @swagger
 * /api/checkout/calculate:
 *   post:
 *     summary: Calculate checkout totals
 *     description: Calculates subtotal, discounts, and total for a checkout with flash sales and vouchers
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - items
 *             properties:
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - product_id
 *                     - quantity
 *                   properties:
 *                     product_id:
 *                       type: string
 *                       format: uuid
 *                     quantity:
 *                       type: integer
 *                       minimum: 1
 *                     attributes:
 *                       type: array
 *                       items:
 *                         type: object
 *               voucher_code:
 *                 type: string
 *     security:
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Checkout calculation result
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *       500:
 *         description: Internal server error
 */
export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    try {
      const body = await request.json();
      const { items, voucher_code } = CheckoutCalculateSchema.parse(body);

      const account_id = user.user_metadata?.account_id;
      if (!account_id) {
        return createCorsResponse(
          request,
          { success: false, error: 'No account_id found in token' },
          401,
        );
      }

      const now = new Date().toISOString();
      let subtotal = 0;
      let discount = 0;
      let total = 0;
      let voucherInfo = null;

      // Process each item
      const processedItems = await Promise.all(
        items.map(async (item) => {
          // Get product details
          const { data: product, error: productError } = await supabase
            .from('products')
            .select(
              `
              id,
              name,
              price,
              image_url,
              flash_sale:flash_sale_products(
                flash_sale_id,
                discount_percentage,
                quantity_limit,
                quantity_sold,
                flash_sales!inner(
                  id, 
                  name, 
                  start_time, 
                  end_time, 
                  status
                )
              )
              `,
            )
            .eq('id', item.product_id)
            .eq('account_id', account_id)
            .eq('status', 'active')
            .single();

          if (productError) {
            throw new Error(
              `Product not found: ${item.product_id}`,
            );
          }

          // Calculate attribute price modifiers
          const attributesPriceModifier = item.attributes?.reduce(
            (sum, attr) => sum + attr.price_modifier,
            0,
          ) || 0;

          // Process flash sale
          let flashSale = null;
          let finalPrice = product.price + attributesPriceModifier;
          
          if (product.flash_sale && product.flash_sale.length > 0) {
            const fsProduct = product.flash_sale[0];
            const fs = fsProduct.flash_sales;
            
            // Check if flash sale is active
            const startTime = new Date(fs.start_time);
            const endTime = new Date(fs.end_time);
            const currentTime = new Date();
            
            if (
              fs.status === 'active' && 
              currentTime >= startTime && 
              currentTime <= endTime
            ) {
              const discountedPrice = finalPrice * (1 - fsProduct.discount_percentage / 100);
              
              flashSale = {
                id: fs.id,
                name: fs.name,
                discount_percentage: fsProduct.discount_percentage,
              };
              
              finalPrice = discountedPrice;
            }
          }

          // Calculate item total
          const itemTotal = finalPrice * item.quantity;
          subtotal += itemTotal;

          return {
            product_id: product.id,
            name: product.name,
            image_url: product.image_url,
            quantity: item.quantity,
            price: finalPrice,
            original_price: product.price + attributesPriceModifier,
            attributes: item.attributes || [],
            flash_sale: flashSale,
            total: itemTotal,
          };
        }),
      );

      // Apply voucher if provided
      if (voucher_code) {
        // Get voucher details
        const { data: voucher, error: voucherError } = await supabase
          .from('vouchers')
          .select('*')
          .eq('account_id', account_id)
          .eq('code', voucher_code.toUpperCase())
          .eq('status', 'active')
          .lte('start_date', now)
          .gte('end_date', now)
          .single();

        if (!voucherError && voucher) {
          // Check if voucher has reached max uses
          if (
            voucher.max_uses === null || 
            voucher.uses_count < voucher.max_uses
          ) {
            // Check minimum order value
            if (
              voucher.min_order_value === null || 
              subtotal >= voucher.min_order_value
            ) {
              // Calculate discount amount
              if (voucher.discount_type === 'percentage') {
                discount = (subtotal * voucher.discount_value) / 100;
                
                // Apply max discount if set
                if (
                  voucher.max_discount_value !== null && 
                  discount > voucher.max_discount_value
                ) {
                  discount = voucher.max_discount_value;
                }
              } else {
                // Fixed amount discount
                discount = voucher.discount_value;
                
                // Ensure discount doesn't exceed subtotal
                if (discount > subtotal) {
                  discount = subtotal;
                }
              }

              voucherInfo = {
                id: voucher.id,
                code: voucher.code,
                name: voucher.name,
                discount_type: voucher.discount_type,
                discount_value: voucher.discount_value,
                discount_amount: discount,
              };
            }
          }
        }
      }

      // Calculate total
      total = subtotal - discount;

      return createCorsResponse(
        request,
        {
          success: true,
          data: {
            items: processedItems,
            subtotal,
            discount,
            total,
            voucher: voucherInfo,
          },
        },
        200,
      );
    } catch (error: any) {
      if (error instanceof z.ZodError) {
        return createCorsResponse(
          request,
          {
            success: false,
            error: 'Invalid request parameters',
            details: error.errors,
          },
          400,
        );
      }

      return createCorsResponse(
        request,
        {
          success: false,
          error: 'Internal server error',
          details: error.message,
        },
        500,
      );
    }
  },
  { auth: true },
);
