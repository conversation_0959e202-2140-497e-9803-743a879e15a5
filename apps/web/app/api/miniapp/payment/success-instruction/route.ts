import { NextRequest, NextResponse } from 'next/server';





/**
 * Success instruction page for Mini App payment completion
 * This page is shown when user clicks "Continue" in Lemon Squeezy success page
 * It instructs user to click the manual success button in Mini App header
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const sessionId = searchParams.get('session_id');
  const status = searchParams.get('status');
  const autoClose = searchParams.get('auto_close') === 'true';
  const accountId = searchParams.get('account_id');

  // Create HTML page with instruction
  const html = `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thanh toán thành công</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: bounce 0.8s ease-out 0.3s both;
        }
        
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0,-15px,0);
            }
            70% {
                transform: translate3d(0,-7px,0);
            }
            90% {
                transform: translate3d(0,-2px,0);
            }
        }
        
        .checkmark {
            color: white;
            font-size: 40px;
            font-weight: bold;
        }
        
        h1 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .subtitle {
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        
        .instruction {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .instruction-title {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .instruction-text {
            color: #5a6c7d;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .button-demo {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            display: inline-block;
            margin: 10px 0;
            border: none;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }
        
        .arrow {
            font-size: 20px;
            color: #3498db;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .footer {
            color: #95a5a6;
            font-size: 12px;
            margin-top: 20px;
        }
        
        .session-info {
            background: #e8f5e8;
            border-radius: 8px;
            padding: 10px;
            margin-top: 15px;
            font-size: 12px;
            color: #2d5a2d;
        }

        .manual-close-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }

        .manual-close-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
        }

        .manual-close-btn:active {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">
            <div class="checkmark">✓</div>
        </div>
        
        <h1>Thanh toán thành công!</h1>
        <p class="subtitle">Giao dịch của bạn đã được xử lý thành công</p>

        ${autoClose ? `
        <div class="instruction">
            <div class="instruction-title">
                <span class="arrow">🔄</span>
                Đang xử lý...
            </div>
            <div class="instruction-text">
                Hệ thống đang tự động hoàn tất giao dịch và đưa bạn về ứng dụng
            </div>
            <div style="margin: 20px 0;">
                <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
            </div>
        </div>
        ` : `
        <div class="instruction">
            <div class="instruction-title">
                <span class="arrow">👆</span>
                Bước cuối cùng
            </div>
            <div class="instruction-text">
                Vui lòng click vào nút <strong>"✓ Thanh toán thành công"</strong> ở phía trên để hoàn tất và quay về ứng dụng
            </div>
            <div class="button-demo">✓ Thanh toán thành công</div>
        </div>
        `}
        
        ${sessionId ? `
        <div class="session-info">
            <strong>Mã giao dịch:</strong> ${sessionId.slice(-8)}
        </div>
        ` : ''}
       
 
        <div class="footer">
            Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi!
        </div>
    </div>
    
    <script>
        console.log('🎉 Payment success instruction page loaded');
        console.log('🔍 Environment check:', {
            isInFrame: window.parent !== window,
            userAgent: navigator.userAgent,
            autoClose: ${autoClose ? 'true' : 'false'}
        });

        // Multiple strategies to handle webview communication
        const autoClose = ${autoClose ? 'true' : 'false'};
        const accountId = '${accountId || ''}';

        // Strategy 1: Direct postMessage
        function tryPostMessage(message, retries = 3) {
            if (retries <= 0) return false;

            try {
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage(message, '*');
                    console.log('✅ PostMessage sent:', message);
                    return true;
                }
            } catch (error) {
                console.log('❌ PostMessage failed:', error);
            }

            // Retry after delay
            setTimeout(() => tryPostMessage(message, retries - 1), 500);
            return false;
        }

        // Strategy 2: Trigger success callback directly (no URL navigation)
        function triggerSuccessCallback() {
            console.log('🎯 Triggering success callback directly...');

            // Send success message to trigger the same logic as manual button click
            tryPostMessage({
                type: 'payment_success_instruction',
                action: 'trigger_success_callback',
                accountId: accountId,
                status: '${status || 'success'}',
                sessionId: '${sessionId || ''}'
            });
        }

        // Strategy 3: Update page content to show manual instructions
        function showManualInstructions() {
            const instruction = document.querySelector('.instruction');
            if (instruction) {
                instruction.innerHTML = \`
                    <div class="instruction-title">
                        <span class="arrow">👆</span>
                        Vui lòng thao tác thủ công
                    </div>
                    <div class="instruction-text">
                        Nếu trang không tự động đóng, vui lòng:<br>
                        1. Nhấn nút "..." ở góc trên bên phải<br>
                        2. Chọn tải lại
                    </div>
                \`;
            }
        }

        if (autoClose) {
            console.log('🚀 Auto-close mode enabled');

            // Try immediate postMessage
            const success1 = tryPostMessage({
                type: 'payment_success_instruction',
                action: 'auto_close_success',
                sessionId: '${sessionId || ''}',
                accountId: accountId,
                status: '${status || 'success'}'
            });

            // Fallback 1: Force close after 1 second
            setTimeout(() => {
                tryPostMessage({
                    type: 'payment_success_instruction',
                    action: 'force_close',
                    accountId: accountId
                });
            }, 1000);

            // Fallback 2: Trigger success callback after 2 seconds
            setTimeout(() => {
                console.log('🔄 Trying success callback fallback...');
                triggerSuccessCallback();
            }, 2000);

            // Fallback 3: Another success callback attempt after 4 seconds
            setTimeout(() => {
                console.log('🔄 Second success callback attempt...');
                triggerSuccessCallback();
            }, 4000);

            // Fallback 4: Show manual instructions after 6 seconds
            setTimeout(() => {
                console.log('📋 Showing manual instructions...');
                showManualInstructions();
            }, 6000);

        } else {
            // Manual mode
            tryPostMessage({
                type: 'payment_success_instruction',
                action: 'show_manual_button',
                sessionId: '${sessionId || ''}',
                status: '${status || 'success'}'
            });

            setTimeout(() => {
                tryPostMessage({
                    type: 'payment_success_instruction',
                    action: 'highlight_manual_button'
                });
            }, 3000);
        }

        // Global fallback: if nothing works after 10 seconds, show manual instructions
        setTimeout(() => {
            console.log('⏰ Global fallback: showing manual instructions');
            showManualInstructions();

            // Show manual close button
            const manualBtn = document.getElementById('manualCloseBtn');
            if (manualBtn) {
                manualBtn.style.display = 'block';
            }
        }, 10000);

       
    </script>
</body>
</html>
  `;

  return new NextResponse(html, {
    headers: {
      'Content-Type': 'text/html; charset=utf-8',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  });
}
