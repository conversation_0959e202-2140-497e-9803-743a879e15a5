import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';
import { createConnectorFromIntegrationId } from '~/lib/integrations/connectors/connector-factory';
import { checkResourceAccess } from '~/lib/check-resource-access';
import { ResourceType, SyncDirection, SyncStrategy } from '~/lib/integrations/connectors/connector.interface';

/**
 * API endpoint để đồng bộ dữ liệu
 * POST /api/integrations/sync
 */
export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy dữ liệu từ request
    const { accountId, integrationId, resourceType, action, data, userId } = await request.json();

    if (!accountId || !integrationId || !resourceType) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'accountId, integrationId, and resourceType are required' },
        { status: 400 }
      );
    }

    // Kiểm tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'manage');
    if (!accessCheck.allowed) {
      logger.warn({ accountId, integrationId }, 'Access denied to sync integration');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Lấy thông tin tích hợp
    const { data: integration, error: fetchError } = await supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .eq('account_id', accountId)
      .single();

    if (fetchError || !integration) {
      logger.error({ error: fetchError }, 'Error fetching integration');
      return NextResponse.json(
        { error: 'Not found', message: 'Integration not found' },
        { status: 404 }
      );
    }

    // Kiểm tra trạng thái tích hợp
    if (integration.status !== 'connected' || !integration.enabled) {
      logger.warn({ integrationId }, 'Integration is not connected or disabled');
      return NextResponse.json(
        { error: 'Integration unavailable', message: 'Integration is not connected or disabled' },
        { status: 400 }
      );
    }

    // Tạo sync log
    const { data: syncLog, error: syncLogError } = await supabase
      .from('integration_sync_logs')
      .insert({
        integration_id: integrationId,
        resource_type: resourceType,
        status: 'in_progress',
        created_by: userId,
        started_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (syncLogError || !syncLog) {
      logger.error({ error: syncLogError }, 'Error creating sync log');
      return NextResponse.json(
        { error: 'Database error', message: 'Failed to create sync log' },
        { status: 500 }
      );
    }

    // Tạo connector
    const connector = await createConnectorFromIntegrationId(integrationId);
    if (!connector) {
      logger.error({ integrationId }, 'Failed to create connector');
      
      // Cập nhật sync log
      await updateSyncLogStatus(syncLog.id, 'error', 'Failed to create connector');
      
      return NextResponse.json(
        { error: 'Connector error', message: 'Failed to create connector' },
        { status: 500 }
      );
    }

    // Xác định hướng đồng bộ
    const direction = action ? SyncDirection.OUTBOUND : SyncDirection.INBOUND;
    
    // Nếu có action và data, gửi dữ liệu đến bên thứ 3
    if (action && data) {
      try {
        const result = await connector.sendData({
          resourceType,
          action,
          data,
        });
        
        // Cập nhật sync log
        await updateSyncLogStatus(
          syncLog.id, 
          'success', 
          null, 
          { 
            items_processed: 1,
            items_created: action === 'create' ? 1 : 0,
            items_updated: action === 'update' ? 1 : 0,
          }
        );
        
        return NextResponse.json({
          success: true,
          syncLogId: syncLog.id,
          result,
        });
      } catch (error: any) {
        logger.error({ error, integrationId, resourceType, action }, 'Error sending data');
        
        // Cập nhật sync log
        await updateSyncLogStatus(syncLog.id, 'error', error.message);
        
        return NextResponse.json(
          { error: 'Sync error', message: error.message },
          { status: 500 }
        );
      }
    }
    
    // Nếu không có action, lấy dữ liệu từ bên thứ 3
    try {
      // Lấy mappings
      const { data: mappings } = await supabase
        .from('integration_mappings')
        .select('*')
        .eq('integration_id', integrationId)
        .eq('resource_type', resourceType)
        .eq('is_active', true);
      
      // Lấy dữ liệu từ bên thứ 3
      const data = await connector.getData({
        resourceType,
        strategy: SyncStrategy.INCREMENTAL,
        limit: 100, // Giới hạn số lượng để tránh quá tải
      });
      
      // Xác định bảng dữ liệu
      const table = getTableForResource(resourceType);
      
      // Đồng bộ dữ liệu
      let successCount = 0;
      let failedCount = 0;
      
      for (const item of data) {
        try {
          // Áp dụng mapping
          const mappedData = applyMapping(item, mappings || []);
          
          // Thêm account_id vào dữ liệu
          mappedData.account_id = accountId;
          
          // Kiểm tra xem đã có dữ liệu chưa
          const { data: existingData, error: fetchError } = await supabase
            .from(table)
            .select('id')
            .eq('id', mappedData.id)
            .eq('account_id', accountId)
            .maybeSingle();
          
          if (fetchError && fetchError.code !== 'PGRST116') {
            throw fetchError;
          }
          
          let saveResult;
          if (existingData) {
            // Cập nhật dữ liệu hiện có
            saveResult = await supabase
              .from(table)
              .update(mappedData)
              .eq('id', mappedData.id)
              .eq('account_id', accountId);
          } else {
            // Tạo dữ liệu mới
            saveResult = await supabase.from(table).insert(mappedData);
          }
          
          if (saveResult.error) {
            // Lưu thông tin lỗi
            await supabase.from('integration_sync_items').insert({
              sync_log_id: syncLog.id,
              external_id: item.id,
              resource_type: resourceType,
              status: 'error',
              error_message: saveResult.error.message,
              raw_data: item,
              processed_data: mappedData,
            });
            
            failedCount++;
          } else {
            // Lưu thông tin thành công
            await supabase.from('integration_sync_items').insert({
              sync_log_id: syncLog.id,
              external_id: item.id,
              internal_id: mappedData.id,
              resource_type: resourceType,
              status: 'success',
              raw_data: item,
              processed_data: mappedData,
            });
            
            successCount++;
          }
        } catch (itemError: any) {
          // Lưu thông tin lỗi
          await supabase.from('integration_sync_items').insert({
            sync_log_id: syncLog.id,
            external_id: item.id || 'unknown',
            resource_type: resourceType,
            status: 'error',
            error_message: itemError.message,
            raw_data: item,
          });
          
          failedCount++;
        }
      }
      
      // Cập nhật sync log
      const status = failedCount > 0 
        ? (successCount > 0 ? 'partial' : 'error') 
        : 'success';
      
      await updateSyncLogStatus(
        syncLog.id, 
        status, 
        failedCount > 0 ? `Failed to sync ${failedCount} items` : null,
        {
          items_processed: data.length,
          items_created: successCount,
          items_failed: failedCount,
        }
      );
      
      // Cập nhật integration
      await supabase
        .from('integrations')
        .update({
          last_sync_at: new Date().toISOString(),
          error_message: failedCount > 0 ? `Failed to sync ${failedCount} items` : null,
        })
        .eq('id', integrationId);
      
      return NextResponse.json({
        success: true,
        syncLogId: syncLog.id,
        total: data.length,
        created: successCount,
        failed: failedCount,
      });
    } catch (error: any) {
      logger.error({ error, integrationId, resourceType }, 'Error syncing data');
      
      // Cập nhật sync log
      await updateSyncLogStatus(syncLog.id, 'error', error.message);
      
      // Cập nhật integration
      await supabase
        .from('integrations')
        .update({
          error_message: `Sync failed: ${error.message}`,
        })
        .eq('id', integrationId);
      
      return NextResponse.json(
        { error: 'Sync error', message: error.message },
        { status: 500 }
      );
    }
  } catch (error: any) {
    logger.error({ error }, 'Error in sync endpoint');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * API endpoint để lấy lịch sử đồng bộ
 * GET /api/integrations/sync?accountId=xxx&integrationId=yyy
 */
export async function GET(request: NextRequest) {
  const logger = await getLogger();
  const supabase = getSupabaseServerClient<Database>();

  try {
    // Lấy tham số từ URL
    const searchParams = request.nextUrl.searchParams;
    const accountId = searchParams.get('accountId');
    const integrationId = searchParams.get('integrationId');
    const syncLogId = searchParams.get('syncLogId');
    const resourceType = searchParams.get('resourceType');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    if (!accountId) {
      return NextResponse.json(
        { error: 'Missing parameter', message: 'accountId is required' },
        { status: 400 }
      );
    }

    // Kiểm tra quyền truy cập
    const accessCheck = await checkResourceAccess(accountId, 'integrations', 'view');
    if (!accessCheck.allowed) {
      logger.warn({ accountId }, 'Access denied to view sync logs');
      return NextResponse.json(
        { error: 'Access denied', message: accessCheck.message },
        { status: 403 }
      );
    }

    // Nếu có syncLogId, lấy chi tiết sync log
    if (syncLogId) {
      // Lấy sync log
      const { data: syncLog, error: syncLogError } = await supabase
        .from('integration_sync_logs')
        .select('*')
        .eq('id', syncLogId)
        .single();

      if (syncLogError || !syncLog) {
        logger.error({ error: syncLogError }, 'Error fetching sync log');
        return NextResponse.json(
          { error: 'Not found', message: 'Sync log not found' },
          { status: 404 }
        );
      }

      // Lấy integration
      const { data: integration, error: integrationError } = await supabase
        .from('integrations')
        .select('account_id')
        .eq('id', syncLog.integration_id)
        .single();

      if (integrationError || !integration) {
        logger.error({ error: integrationError }, 'Error fetching integration');
        return NextResponse.json(
          { error: 'Not found', message: 'Integration not found' },
          { status: 404 }
        );
      }

      // Kiểm tra quyền truy cập
      if (integration.account_id !== accountId) {
        logger.warn({ accountId, syncLogId }, 'Access denied to view sync log');
        return NextResponse.json(
          { error: 'Access denied', message: 'You do not have access to this sync log' },
          { status: 403 }
        );
      }

      // Lấy sync items
      const { data: syncItems, error: syncItemsError } = await supabase
        .from('integration_sync_items')
        .select('*')
        .eq('sync_log_id', syncLogId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (syncItemsError) {
        logger.error({ error: syncItemsError }, 'Error fetching sync items');
        return NextResponse.json(
          { error: 'Database error', message: 'Failed to fetch sync items' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        syncLog,
        syncItems,
      });
    }

    // Xây dựng query
    let query = supabase
      .from('integration_sync_logs')
      .select('*, integration:integrations(id, type, name)')
      .order('created_at', { ascending: false });

    // Thêm các điều kiện
    if (integrationId) {
      query = query.eq('integration_id', integrationId);
    } else {
      // Nếu không có integrationId, lấy tất cả sync logs của account
      query = query.eq('integration.account_id', accountId);
    }

    if (resourceType) {
      query = query.eq('resource_type', resourceType);
    }

    // Phân trang
    query = query.range(offset, offset + limit - 1);

    // Thực hiện query
    const { data: syncLogs, error } = await query;

    if (error) {
      logger.error({ error }, 'Error fetching sync logs');
      return NextResponse.json(
        { error: 'Database error', message: 'Failed to fetch sync logs' },
        { status: 500 }
      );
    }

    return NextResponse.json({ syncLogs });
  } catch (error: any) {
    logger.error({ error }, 'Error getting sync logs');
    return NextResponse.json(
      { error: 'Internal server error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * Cập nhật trạng thái sync log
 * @param syncLogId ID của sync log
 * @param status Trạng thái mới
 * @param errorMessage Thông báo lỗi (nếu có)
 * @param stats Thống kê đồng bộ
 */
async function updateSyncLogStatus(
  syncLogId: string,
  status: string,
  errorMessage: string | null = null,
  stats: {
    items_processed?: number;
    items_created?: number;
    items_updated?: number;
    items_failed?: number;
  } = {}
) {
  const supabase = getSupabaseServerClient<Database>();
  const logger = await getLogger();

  try {
    const { error } = await supabase
      .from('integration_sync_logs')
      .update({
        status,
        error_message: errorMessage,
        completed_at: new Date().toISOString(),
        ...stats,
      })
      .eq('id', syncLogId);

    if (error) {
      logger.error({ error, syncLogId }, 'Error updating sync log status');
    }
  } catch (error) {
    logger.error({ error, syncLogId }, 'Error updating sync log status');
  }
}

/**
 * Áp dụng mapping cho dữ liệu
 * @param data Dữ liệu gốc
 * @param mappings Mapping
 */
function applyMapping(data: any, mappings: any[]): any {
  const result: Record<string, any> = {};

  // Nếu không có mapping, trả về dữ liệu gốc
  if (!mappings || mappings.length === 0) {
    return data;
  }

  // Áp dụng từng mapping
  for (const mapping of mappings) {
    const { source_field, target_field, transform_function } = mapping;

    // Lấy giá trị từ source field
    let value = data[source_field];

    // Áp dụng hàm biến đổi nếu có
    if (transform_function && value !== undefined) {
      try {
        // Thực thi hàm biến đổi (cẩn thận với eval!)
        // Trong môi trường production, nên sử dụng cách an toàn hơn
        const transformFn = new Function('value', transform_function);
        value = transformFn(value);
      } catch (error) {
        console.error(
          `Error applying transform function for ${source_field}:`,
          error
        );
      }
    }

    // Gán giá trị cho target field
    if (value !== undefined) {
      result[target_field] = value;
    }
  }

  return result;
}

/**
 * Lấy bảng tương ứng với loại resource
 * @param resourceType Loại resource
 */
function getTableForResource(resourceType: string): string {
  switch (resourceType) {
    case ResourceType.PRODUCTS:
      return 'products';
    case ResourceType.ORDERS:
      return 'customer_orders';
    case ResourceType.CUSTOMERS:
      return 'customers';
    case ResourceType.CATEGORIES:
      return 'categories';
    case ResourceType.BRANCHES:
      return 'branches';
    case ResourceType.VOUCHERS:
      return 'vouchers';
    default:
      throw new Error(`Unsupported resource type: ${resourceType}`);
  }
}
