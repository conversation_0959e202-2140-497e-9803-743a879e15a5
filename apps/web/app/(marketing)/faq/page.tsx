import Link from 'next/link';

import {ArrowR<PERSON>, ChevronDown} from 'lucide-react';

import {Button} from '@kit/ui/button';
import {Trans} from '@kit/ui/trans';

import {SitePageHeader} from '~/(marketing)/_components/site-page-header';
import {createI18nServerInstance} from '~/lib/i18n/i18n.server';
import {withI18n} from '~/lib/i18n/with-i18n';

export const generateMetadata = async () => {
    const {t} = await createI18nServerInstance();

    return {
        title: t('marketing:faq'),
    };
};

async function FAQPage() {
    const {t} = await createI18nServerInstance();

    // Câu hỏi thường gặp về MinApp
    const faqItems = [
        {
            question: `MinApp là gì?`,
            answer: `MinApp là nền tảng giúp doanh nghiệp tạo cửa hàng trực tuyến trên <PERSON> một cách nhanh chóng và hiệu qu<PERSON>, kh<PERSON><PERSON> cần kiến thức lập trình. <PERSON><PERSON><PERSON>, bạn có thể tiếp cận hơn 100 triệu người dùng Zalo tại Việt Nam và tận dụng sức mạnh của nền tảng này để phát triển kinh doanh.`,
        },
        {
            question: `MinApp có những gói dịch vụ nào?`,
            answer: `MinApp cung cấp 3 gói dịch vụ chính: Gói Cơ bản (phù hợp cho doanh nghiệp nhỏ), Gói Nâng cao (phù hợp cho doanh nghiệp vừa) và Gói Doanh nghiệp (phù hợp cho doanh nghiệp lớn). Mỗi gói có các tính năng và giới hạn khác nhau. Xem chi tiết tại trang Bảng giá.`,
        },
        {
            question: `Tôi có cần tài khoản Zalo OA để sử dụng MinApp không?`,
            answer: `Không, bạn không bắt buộc phải có tài khoản Zalo OA để sử dụng MinApp. Tuy nhiên, để tận dụng tối đa các tính năng như gửi thông báo ZNS, tương tác với khách hàng qua chat, bạn nên đăng ký tài khoản Zalo OA. MinApp có thể giúp bạn tạo và kết nối với Zalo OA dễ dàng.`,
        },
        {
            question: `MinApp có hỗ trợ thanh toán trực tuyến không?`,
            answer: `Có, MinApp hỗ trợ nhiều phương thức thanh toán trực tuyến như: Thẻ tín dụng/ghi nợ, Chuyển khoản ngân hàng, Ví điện tử (MoMo, ZaloPay, VNPay), và Thanh toán khi nhận hàng (COD).`,
        },
        {
            question: `Tôi có thể quản lý nhiều chi nhánh trên MinApp không?`,
            answer: `Có, MinApp hỗ trợ quản lý nhiều chi nhánh trên cùng một tài khoản. Bạn có thể phân phối sản phẩm theo chi nhánh và quản lý tồn kho riêng cho từng chi nhánh. Số lượng chi nhánh tối đa phụ thuộc vào gói dịch vụ bạn đăng ký.`,
        },
        {
            question: `MinApp có hỗ trợ quản lý thuộc tính sản phẩm không?`,
            answer: `Có, MinApp hỗ trợ quản lý thuộc tính sản phẩm đa dạng như kích thước, màu sắc, chất liệu, hương vị... Bạn có thể tạo nhiều thuộc tính cho mỗi sản phẩm, thiết lập giá khác nhau cho từng thuộc tính và quản lý tồn kho theo thuộc tính.`,
        },
        {
            question: `Làm thế nào để tôi nhận được thông báo khi có đơn hàng mới?`,
            answer: `MinApp sẽ gửi thông báo cho bạn qua nhiều kênh khi có đơn hàng mới: ZNS, Thông báo trong ứng dụng, Thông báo Zalo (nếu đã kết nối Zalo OA), và Thông báo đẩy trên thiết bị di động (nếu đã cài đặt ứng dụng MinApp).`,
        },
        {
            question: `MinApp có tính năng tích điểm khách hàng không?`,
            answer: `Có, MinApp cung cấp hệ thống tích điểm khách hàng toàn diện. Khách hàng sẽ nhận được điểm thưởng khi mua hàng và có thể sử dụng điểm để đổi quà hoặc giảm giá cho đơn hàng tiếp theo. Bạn có thể tùy chỉnh tỷ lệ tích điểm và cách sử dụng điểm.`,
        },
        {
            question: `Tôi có thể tùy chỉnh giao diện cửa hàng không?`,
            answer: `Có, MinApp cung cấp nhiều tùy chọn để tùy chỉnh giao diện cửa hàng: Nhiều mẫu giao diện có sẵn, Tùy chỉnh màu sắc và font chữ, Thêm logo và hình ảnh thương hiệu, Tùy chỉnh bố cục trang. Gói Doanh nghiệp còn cung cấp tùy chỉnh giao diện nâng cao và hỗ trợ thiết kế riêng.`,
        },
        {
            question: `MinApp có hỗ trợ kỹ thuật không?`,
            answer: `Có, MinApp cung cấp hỗ trợ kỹ thuật qua nhiều kênh: Email (<EMAIL>), Hotline (1900 xxxx, 8:00 - 18:00, Thứ 2 - Thứ 6), Chat trực tuyến trên website, Cộng đồng hỗ trợ, và Tài liệu hướng dẫn chi tiết. Mức độ hỗ trợ phụ thuộc vào gói dịch vụ bạn đăng ký.`,
        },
    ];

    const structuredData = {
        '@context': 'https://schema.org',
        '@type': 'FAQPage',
        mainEntity: faqItems.map((item) => {
            return {
                '@type': 'Question',
                name: item.question,
                acceptedAnswer: {
                    '@type': 'Answer',
                    text: item.answer,
                },
            };
        }),
    };

    return (
        <>
            <script
                key={'ld:json'}
                type="application/ld+json"
                dangerouslySetInnerHTML={{__html: JSON.stringify(structuredData)}}
            />

            <div className={'flex flex-col space-y-4 xl:space-y-8'}>
                <SitePageHeader
                    title={t('marketing:faq')}
                    subtitle={t('marketing:faqSubtitle')}
                />

                <div className={'container flex flex-col space-y-8 pb-16'}>
                    <div className="flex w-full max-w-xl flex-col">
                        {faqItems.map((item, index) => {
                            return <FaqItem key={index} item={item}/>;
                        })}
                    </div>

                    <div>
                        <Button asChild variant={'outline'}>
                            <Link href={'/contact'}>
                <span>
                  <Trans i18nKey={'marketing:contactFaq'}/>
                </span>

                                <ArrowRight className={'ml-2 w-4'}/>
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </>
    );
}

export default withI18n(FAQPage);

function FaqItem({
                     item,
                 }: React.PropsWithChildren<{
    item: {
        question: string;
        answer: string;
    };
}>) {
    return (
        <details className={'group border-b px-2 py-4 last:border-b-transparent'}>
            <summary
                className={
                    'flex items-center justify-between hover:cursor-pointer hover:underline'
                }
            >
                <h2
                    className={
                        'hover:underline-none cursor-pointer font-sans font-medium'
                    }
                >
                    <Trans i18nKey={item.question} defaults={item.question}/>
                </h2>

                <div>
                    <ChevronDown
                        className={'h-5 transition duration-300 group-open:-rotate-180'}
                    />
                </div>
            </summary>

            <div className={'text-muted-foreground flex flex-col gap-y-3 py-1'}>
                <Trans i18nKey={item.answer} defaults={item.answer}/>
            </div>
        </details>
    );
}
