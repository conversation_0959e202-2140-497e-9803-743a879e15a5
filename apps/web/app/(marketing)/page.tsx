import Image from 'next/image';
import Link from 'next/link';

import {
  ArrowRightIcon,
  CheckCircle2,
  CreditCard,
  MessageCircle,
  Smartphone,
  Star,
  Store,
  Users,
  Zap,
} from 'lucide-react';

import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Card, CardContent } from '@kit/ui/card';
import { CtaButton, Pill, PillActionButton } from '@kit/ui/marketing';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@kit/ui/tabs';

import { withI18n } from '~/lib/i18n/with-i18n';

import { ModernPricingTable } from './_components/modern-pricing-table';

function Home() {
  return (
    <div className={'flex flex-col space-y-24'}>
      {/* Hero Section - Brand Colors */}
      <div className="from-brand-blue/10 to-background relative overflow-hidden bg-gradient-to-b pt-16 pb-24">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))] bg-center bg-cover sm:bg-auto" />
        </div>
        {/* Brand color decorative elements */}
        <div className="bg-brand-blue/10 absolute -top-40 -left-40 h-[500px] w-[500px] rounded-full blur-3xl"></div>
        <div className="bg-brand-red/10 absolute -right-40 bottom-0 h-[300px] w-[300px] rounded-full blur-3xl"></div>
        <div className="bg-brand-yellow/15 absolute top-1/3 right-1/4 h-[200px] w-[200px] rounded-full blur-3xl"></div>

        <div className="relative container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mx-auto max-w-7xl">
            <div className="lg:grid lg:grid-cols-12 lg:gap-8">
              <div className="sm:text-center md:mx-auto md:max-w-2xl lg:col-span-6 lg:text-left">
                <div>
                  <Pill label={'Mới'}>
                    <span>Nền tảng quản lý giáo dục thông minh</span>
                    <PillActionButton asChild>
                      <Link href={'/auth/sign-up'}>
                        <ArrowRightIcon className={'h-4 w-4'} />
                      </Link>
                    </PillActionButton>
                  </Pill>
                </div>

                <h1 className="mt-6 font-extrabold tracking-tight">
                  <span className="block pb-2 text-5xl md:text-6xl lg:text-7xl xl:text-8xl">
                    <span className="text-brand-gradient-secondary inline-block">
                      Quản lý trường học
                    </span>
                  </span>
                  <span className="block text-4xl text-gray-900 md:text-5xl lg:text-6xl xl:text-7xl dark:text-white">
                    thông minh với Zalo Mini App
                  </span>
                </h1>

                <p className="text-muted-foreground mt-6 text-base sm:text-xl lg:text-lg xl:text-xl">
                  Nền tảng SaaS toàn diện cho trường mầm non và trung tâm giáo dục.
                  Tích hợp Zalo Mini App, ZNS thông báo tự động, và dashboard quản lý chuyên nghiệp.
                </p>

                <div className="mt-8 flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                  <CtaButton className="bg-brand-gradient-primary glow-brand-blue px-8 py-3 text-base font-medium">
                    <Link href={'/auth/sign-up'}>
                      <span className="flex items-center space-x-2">
                        <span>Dùng thử miễn phí 30 ngày</span>
                        <ArrowRightIcon className="h-4 w-4" />
                      </span>
                    </Link>
                  </CtaButton>

                  <Button
                    variant="outline"
                    className="border-brand-blue text-brand-blue hover:bg-brand-blue/10 px-8 py-3 text-base font-medium"
                  >
                    <Link href="/contact">
                      <span>Xem demo trường học</span>
                    </Link>
                  </Button>
                </div>

                <div className="mt-8 flex items-center">
                  <div className="flex -space-x-2">
                    {[
                      { color: 'var(--brand-red)', initials: 'TH' },
                      { color: 'var(--brand-blue)', initials: 'KD' },
                      { color: 'var(--brand-yellow)', initials: 'MN' },
                      { color: 'var(--brand-red-dark)', initials: 'PT' },
                      { color: 'var(--brand-blue-dark)', initials: 'HC' },
                    ].map((avatar, i) => (
                      <div
                        key={i}
                        className="inline-flex h-8 w-8 items-center justify-center rounded-full text-xs font-bold text-white ring-2 ring-white dark:ring-gray-900"
                        style={{ backgroundColor: avatar.color }}
                      >
                        {avatar.initials}
                      </div>
                    ))}
                  </div>
                  <div className="text-muted-foreground ml-3 text-sm font-medium">
                    <span className="text-foreground font-semibold">500+</span>{' '}
                    doanh nghiệp đã sử dụng
                  </div>
                </div>
              </div>

              <div className="relative mt-12 sm:mx-auto sm:max-w-lg lg:col-span-6 lg:mx-0 lg:mt-0 lg:flex lg:max-w-none lg:items-center">
                <div className="relative mx-auto w-full rounded-lg shadow-lg lg:max-w-md">
                  <div className="focus:ring-primary relative block w-full overflow-hidden rounded-lg bg-white focus:ring-2 focus:ring-offset-2 focus:outline-none">
                    <Image
                      priority
                      className="w-full rounded-lg border border-gray-200 shadow-xl dark:border-gray-800"
                      width={1200}
                      height={800}
                      src="/images/miniapp-showcase.png"
                      alt="Mini App Showcase"
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="bg-primary/90 flex h-16 w-16 items-center justify-center rounded-full text-white shadow-lg">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </span>
                    </div>
                  </div>
                </div>

                <div className="absolute -top-10 -right-20 hidden lg:block">
                  <div className="flex items-center space-x-6 rounded-2xl bg-white/90 p-4 shadow-lg ring-1 ring-gray-900/10 backdrop-blur-sm dark:bg-gray-800/90 dark:ring-white/10">
                    <div className="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full">
                      <Store className="text-primary h-6 w-6" />
                    </div>
                    <div>
                      <div className="text-sm font-medium">
                        Quản lý trường học thông minh
                      </div>
                      <div className="text-muted-foreground text-xs">
                        Tích hợp Zalo Mini App
                      </div>
                    </div>
                  </div>
                </div>

                <div className="absolute -bottom-20 -left-20 hidden lg:block">
                  <div className="flex items-center space-x-6 rounded-2xl bg-white/90 p-4 shadow-lg ring-1 ring-gray-900/10 backdrop-blur-sm dark:bg-gray-800/90 dark:ring-white/10">
                    <div className="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full">
                      <Zap className="text-primary h-6 w-6" />
                    </div>
                    <div>
                      <div className="text-sm font-medium">
                        Thông báo ZNS tự động
                      </div>
                      <div className="text-muted-foreground text-xs">
                        Gửi thông báo cho phụ huynh
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tính năng nổi bật - Tabs - Brand Colors */}
      <div className="relative overflow-hidden bg-gray-50 py-16 dark:bg-gray-900/50">
        {/* Brand color decorative elements */}
        <div className="bg-brand-yellow/10 absolute -top-40 right-0 h-[300px] w-[300px] rounded-full blur-3xl"></div>
        <div className="bg-brand-blue/10 absolute bottom-0 -left-40 h-[300px] w-[300px] rounded-full blur-3xl"></div>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight md:text-4xl lg:text-5xl">
              <span className="text-brand-gradient inline-block pb-1 leading-tight">
                Giải pháp toàn diện
              </span>{' '}
              cho trường học của bạn
            </h2>
            <p className="text-muted-foreground mx-auto max-w-3xl text-lg">
              Nền tảng quản lý giáo dục thông minh với Zalo Mini App tích hợp,
              ZNS thông báo tự động, và dashboard quản lý chuyên nghiệp.
              Kết nối trường học - phụ huynh - học sinh một cách hiệu quả.
            </p>
          </div>

          <Tabs defaultValue="management" className="mx-auto w-full max-w-5xl">
            <TabsList className="mb-12 flex w-full flex-col gap-2 rounded-xl border border-gray-200 bg-white/80 p-1 shadow-md backdrop-blur-sm sm:grid sm:grid-cols-3">
              <TabsTrigger
                value="management"
                className="tabs-management h-auto py-3 text-sm whitespace-normal sm:text-base"
              >
                Quản lý trường học
              </TabsTrigger>
              <TabsTrigger
                value="miniapp"
                className="tabs-miniapp h-auto py-3 text-sm whitespace-normal sm:text-base"
              >
                Zalo Mini App
              </TabsTrigger>
              <TabsTrigger
                value="communication"
                className="tabs-communication h-auto py-3 text-sm whitespace-normal sm:text-base"
              >
                Thông báo & Giao tiếp
              </TabsTrigger>
            </TabsList>

            <TabsContent value="management" className="mt-6">
              <div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2">
                <div>
                  <Image
                    src="/images/education-dashboard.png"
                    alt="Dashboard quản lý trường học"
                    width={600}
                    height={400}
                    className="rounded-xl border border-gray-200 shadow-lg dark:border-gray-800"
                  />
                </div>
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold">
                    Dashboard quản lý trường học thông minh
                  </h3>
                  <p className="text-muted-foreground">
                    Hệ thống quản lý toàn diện cho trường mầm non và trung tâm giáo dục.
                    Theo dõi học sinh, quản lý học phí, và báo cáo tiến độ một cách dễ dàng.
                  </p>

                  <div className="space-y-4">
                    {[
                      {
                        icon: <Users className="h-5 w-5" />,
                        title: 'Quản lý học sinh & phụ huynh',
                        desc: 'Hồ sơ học sinh chi tiết, thông tin phụ huynh, và lịch sử học tập',
                      },
                      {
                        icon: <CreditCard className="h-5 w-5" />,
                        title: 'Quản lý học phí',
                        desc: 'Theo dõi thanh toán, nhắc nhở tự động, và báo cáo tài chính',
                      },
                      {
                        icon: <CheckCircle2 className="h-5 w-5" />,
                        title: 'Điểm danh & báo cáo',
                        desc: 'Điểm danh nhanh chóng và tạo báo cáo học tập tự động',
                      },
                    ].map((feature, i) => (
                      <div key={i} className="flex items-start">
                        <div className="bg-brand-red/10 text-brand-red flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full">
                          {feature.icon}
                        </div>
                        <div className="ml-4">
                          <h4 className="text-base font-medium">
                            {feature.title}
                          </h4>
                          <p className="text-muted-foreground mt-1 text-sm">
                            {feature.desc}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="miniapp" className="mt-6">
              <div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2">
                <div className="order-2 md:order-1">
                  <div className="space-y-6">
                    <h3 className="text-2xl font-bold">
                      Zalo Mini App cho phụ huynh
                    </h3>
                    <p className="text-muted-foreground">
                      Ứng dụng mini trên Zalo giúp phụ huynh theo dõi con em,
                      thanh toán học phí và giao tiếp với nhà trường dễ dàng.
                    </p>

                    <div className="space-y-4">
                      {[
                        {
                          icon: <Smartphone className="h-5 w-5" />,
                          title: 'Theo dõi con em',
                          desc: 'Xem điểm danh, báo cáo học tập và hoạt động hàng ngày',
                        },
                        {
                          icon: <CreditCard className="h-5 w-5" />,
                          title: 'Thanh toán học phí',
                          desc: 'Thanh toán học phí trực tuyến qua ZaloPay, ATM, thẻ tín dụng',
                        },
                        {
                          icon: <MessageCircle className="h-5 w-5" />,
                          title: 'Giao tiếp với giáo viên',
                          desc: 'Nhắn tin trực tiếp với giáo viên và nhận thông báo quan trọng',
                        },
                      ].map((feature, i) => (
                        <div key={i} className="flex items-start">
                          <div className="bg-primary/10 text-primary flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full">
                            {feature.icon}
                          </div>
                          <div className="ml-4">
                            <h4 className="text-base font-medium">
                              {feature.title}
                            </h4>
                            <p className="text-muted-foreground mt-1 text-sm">
                              {feature.desc}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="order-1 md:order-2">
                  <Image
                    src="/images/zalo-miniapp-features.png"
                    alt="Zalo Mini App"
                    width={600}
                    height={400}
                    className="rounded-xl border border-gray-200 shadow-lg dark:border-gray-800"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="communication" className="mt-6">
              <div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2">
                <div>
                  <Image
                    src="/images/zns-communication.png"
                    alt="Thông báo ZNS tự động"
                    width={600}
                    height={400}
                    className="rounded-xl border border-gray-200 shadow-lg dark:border-gray-800"
                  />
                </div>
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold">
                    Thông báo ZNS tự động thông minh
                  </h3>
                  <p className="text-muted-foreground">
                    Hệ thống thông báo ZNS tự động giúp nhà trường gửi thông tin
                    quan trọng đến phụ huynh một cách kịp thời và hiệu quả.
                  </p>

                  <div className="space-y-4">
                    {[
                      {
                        icon: <Zap className="h-5 w-5" />,
                        title: 'Thông báo tự động',
                        desc: 'Gửi thông báo vắng mặt, học phí, sự kiện tự động',
                      },
                      {
                        icon: <MessageCircle className="h-5 w-5" />,
                        title: 'ZNS Pool chia sẻ',
                        desc: 'Tiết kiệm 70% chi phí thông báo với hệ thống pool chia sẻ',
                      },
                      {
                        icon: <Star className="h-5 w-5" />,
                        title: 'Thông báo khẩn cấp',
                        desc: 'Ưu tiên gửi thông báo khẩn cấp về sức khỏe và an toàn',
                      },
                    ].map((feature, i) => (
                      <div key={i} className="flex items-start">
                        <div className="bg-brand-yellow/10 text-brand-yellow-dark flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full">
                          {feature.icon}
                        </div>
                        <div className="ml-4">
                          <h4 className="text-base font-medium">
                            {feature.title}
                          </h4>
                          <p className="text-muted-foreground mt-1 text-sm">
                            {feature.desc}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Phần Lợi ích cho doanh nghiệp */}
      <div className="container mx-auto px-4 py-16 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <h2 className="mb-4 text-3xl font-bold tracking-tight md:text-4xl lg:text-5xl">
            Lợi ích cho{' '}
            <span className="text-brand-gradient-secondary inline-block pb-1 leading-tight">
              trường học của bạn
            </span>
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-lg">
            Tại sao các trường mầm non và trung tâm giáo dục nên chọn
            nền tảng quản lý giáo dục thông minh của chúng tôi?
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {[
            {
              title: 'Tiết kiệm thời gian quản lý',
              description:
                'Tự động hóa điểm danh, báo cáo, thông báo. Giảm 80% thời gian làm việc hành chính',
              icon: <Zap className="h-10 w-10" />,
            },
            {
              title: 'Kết nối phụ huynh hiệu quả',
              description:
                'Zalo Mini App giúp phụ huynh theo dõi con em 24/7, tăng sự hài lòng và tin tưởng',
              icon: <Smartphone className="h-10 w-10" />,
            },
            {
              title: 'Quản lý tài chính minh bạch',
              description:
                'Theo dõi học phí, thanh toán online, báo cáo tài chính tự động và chính xác',
              icon: <CreditCard className="h-10 w-10" />,
            },
            {
              title: 'Thông báo ZNS tiết kiệm',
              description:
                'Hệ thống ZNS pool chia sẻ giúp tiết kiệm 70% chi phí thông báo so với ZNS riêng',
              icon: <MessageCircle className="h-10 w-10" />,
            },
            {
              title: 'Báo cáo và phân tích thông minh',
              description:
                'Dashboard analytics chi tiết về học sinh, tài chính, và hiệu quả hoạt động',
              icon: <Star className="h-10 w-10" />,
            },
            {
              title: 'Hỗ trợ đa module',
              description:
                'Quản lý sức khỏe, dinh dưỡng, đưa đón, thư viện - tất cả trong một nền tảng',
              icon: <Users className="h-10 w-10" />,
            },
          ].map((benefit, i) => (
            <Card
              key={i}
              className="border border-gray-200 shadow-sm transition-all hover:shadow-md dark:border-gray-800"
            >
              <CardContent className="pt-6">
                <div className="from-brand-blue/20 to-brand-red/10 text-brand-blue mb-5 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br shadow-md">
                  {benefit.icon}
                </div>
                <h3 className="mb-2 text-xl font-bold">{benefit.title}</h3>
                <p className="text-muted-foreground">{benefit.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Phần Khách hàng đã sử dụng */}
      <div className="bg-gray-50 py-16 dark:bg-gray-900/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight md:text-4xl lg:text-5xl">
              <span className="text-brand-gradient inline-block pb-1 leading-tight">
                Trường học
              </span>{' '}
              đã tin dùng chúng tôi
            </h2>
            <p className="text-muted-foreground mx-auto max-w-3xl text-lg">
              Hàng trăm trường mầm non và trung tâm giáo dục đã sử dụng nền tảng
              của chúng tôi để nâng cao chất lượng quản lý và dịch vụ
            </p>
          </div>

          <div className="grid grid-cols-2 gap-8 md:grid-cols-3 lg:grid-cols-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div
                key={i}
                className="flex items-center justify-center rounded-lg bg-white p-4 shadow-sm transition-all hover:shadow-md dark:bg-gray-800"
              >
                <Image
                  src={`/images/client-logo-${i}.svg`}
                  alt={`Client ${i}`}
                  width={120}
                  height={60}
                  className="opacity-70 transition-opacity hover:opacity-100"
                />
              </div>
            ))}
          </div>

          <div className="mt-16 grid grid-cols-1 gap-8 md:grid-cols-3">
            {[
              {
                quote:
                  'Nền tảng đã giúp trường chúng tôi tiết kiệm 80% thời gian quản lý hành chính. Phụ huynh rất hài lòng với Mini App và thông báo ZNS tự động.',
                author: 'Cô Nguyễn Thị Mai',
                role: 'Hiệu trưởng trường mầm non Hoa Sen',
              },
              {
                quote:
                  'Hệ thống thanh toán học phí online rất tiện lợi. Phụ huynh có thể thanh toán mọi lúc mọi nơi, không cần đến trường. Tỷ lệ thu học phí đúng hạn tăng 95%.',
                author: 'Thầy Lê Văn Nam',
                role: 'Giám đốc trung tâm Anh ngữ SmartKids',
              },
              {
                quote:
                  'Dashboard báo cáo rất chi tiết và trực quan. Tôi có thể theo dõi tình hình hoạt động của trường mọi lúc mọi nơi. ZNS thông báo giúp tiết kiệm rất nhiều chi phí.',
                author: 'Bà Trần Thị Lan',
                role: 'Chủ trường mầm non Ánh Dương',
              },
            ].map((testimonial, i) => (
              <Card
                key={i}
                className="border border-gray-200 shadow-sm transition-all hover:shadow-md dark:border-gray-800"
              >
                <CardContent className="pt-6">
                  <div className="text-brand-yellow mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className="inline-block h-5 w-5 fill-current drop-shadow-sm"
                      />
                    ))}
                  </div>
                  <p className="text-muted-foreground mb-4 italic">
                    "{testimonial.quote}"
                  </p>
                  <div className="flex items-center">
                    <div className="bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-full font-bold">
                      {testimonial.author.charAt(0)}
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium">
                        {testimonial.author}
                      </p>
                      <p className="text-muted-foreground text-xs">
                        {testimonial.role}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Phần Giá cả - Enhanced with modern design */}
      <div className="relative overflow-hidden py-36">
        {/* Background elements - Enhanced with more dynamic gradients */}
        <div className="absolute inset-0 bg-gradient-to-b from-gray-50 via-white to-gray-50 dark:from-gray-900/50 dark:via-gray-900/30 dark:to-gray-900/50"></div>

        {/* Decorative elements - Enhanced with more dynamic effects */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Gradient blobs - Larger and more vibrant with subtle animation */}
          <div className="from-primary/15 animate-pulse-subtle absolute -top-40 -left-40 h-[700px] w-[700px] rounded-full bg-gradient-to-br to-purple-500/10 opacity-70 blur-3xl"></div>
          <div className="to-primary/10 animate-pulse-subtle absolute -right-40 -bottom-40 h-[700px] w-[700px] rounded-full bg-gradient-to-tl from-purple-500/15 opacity-70 blur-3xl"></div>
          <div className="animate-pulse-subtle absolute top-1/3 right-1/4 h-[300px] w-[300px] rounded-full bg-yellow-400/10 opacity-60 blur-3xl"></div>

          {/* Floating elements - More varied and dynamic */}
          <div className="animate-float bg-primary/10 absolute top-20 left-[10%] h-24 w-24 rounded-lg backdrop-blur-sm"></div>
          <div className="animate-float-slow absolute right-[15%] bottom-40 h-20 w-20 rounded-full bg-purple-500/15 backdrop-blur-sm"></div>
          <div className="animate-float-slower bg-primary/10 absolute top-1/2 left-[80%] h-28 w-28 rotate-45 rounded-lg backdrop-blur-sm"></div>
          <div className="animate-float absolute top-1/3 left-[30%] h-16 w-16 rounded-full bg-yellow-400/10 backdrop-blur-sm"></div>

          {/* Grid pattern - Enhanced with better opacity control */}
          <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-cover bg-center opacity-[0.07] dark:opacity-[0.05]"></div>
        </div>

        <div className="relative container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header section - Enhanced with modern design */}
          <div className="mb-24 text-center">
            <div className="from-brand-blue/20 to-brand-red/20 animate-shimmer inline-flex items-center justify-center rounded-full border border-white/20 bg-gradient-to-r px-5 py-2.5 shadow-lg backdrop-blur-sm">
              <span className="from-brand-blue to-brand-red mr-2 flex h-7 w-7 items-center justify-center rounded-full bg-gradient-to-br">
                <CreditCard className="h-3.5 w-3.5 text-white" />
              </span>
              <span className="text-sm font-medium">
                Bắt đầu miễn phí - Không cần thẻ tín dụng
              </span>
            </div>

            <h2 className="mt-8 mb-8 text-4xl font-extrabold tracking-tight md:text-5xl lg:text-6xl xl:text-7xl">
              <span className="text-brand-gradient inline-block pb-1 leading-tight">
                Giá cả hợp lý
              </span>{' '}
              <span className="mt-2 block md:inline">
                cho mọi quy mô doanh nghiệp
              </span>
            </h2>

            <p className="mx-auto max-w-3xl text-lg text-gray-600 md:text-xl dark:text-gray-300">
              Chọn gói phù hợp với nhu cầu của bạn. Bắt đầu với gói miễn phí và
              nâng cấp khi doanh nghiệp của bạn phát triển.
            </p>
          </div>

          {/* Pricing table section - Enhanced with better positioning and effects */}
          <div className="relative mx-auto w-full max-w-6xl">
            {/* Billing interval toggle */}
            <div className="absolute -top-5 left-1/2 z-10 -translate-x-1/2 transform">
              <div id="pricing-tabs-container"></div>
            </div>

            {/* Pricing table */}
            <div className="relative mt-8">
              <ModernPricingTable />
            </div>
          </div>

          {/* Support section - Enhanced with modern card design */}
          <div className="mt-28 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {/* Support card - Enhanced with hover effects and styling */}
            <div className="group hover:border-primary/20 dark:hover:border-primary/30 rounded-2xl border border-gray-200 bg-white/90 p-8 shadow-lg backdrop-blur-sm transition-all duration-300 hover:translate-y-[-5px] hover:shadow-xl dark:border-gray-800 dark:bg-gray-900/90">
              <div className="from-brand-blue/20 to-brand-blue/5 group-hover:from-brand-blue/30 group-hover:to-brand-blue/10 mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br transition-all duration-300">
                <CheckCircle2 className="text-brand-blue h-8 w-8" />
              </div>
              <h3 className="mb-4 text-xl font-bold">
                Có câu hỏi về các gói dịch vụ?
              </h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Đội ngũ hỗ trợ của chúng tôi luôn sẵn sàng giải đáp mọi thắc mắc
                của bạn về các gói dịch vụ.
              </p>
              <a
                href="/contact"
                className="text-primary group-hover:text-primary/80 inline-flex items-center transition-colors hover:underline"
              >
                <span>Liên hệ ngay</span>
                <ArrowRightIcon className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </a>
            </div>

            {/* Customization card - Enhanced with hover effects and styling */}
            <div className="group hover:border-primary/20 dark:hover:border-primary/30 rounded-2xl border border-gray-200 bg-white/90 p-8 shadow-lg backdrop-blur-sm transition-all duration-300 hover:translate-y-[-5px] hover:shadow-xl dark:border-gray-800 dark:bg-gray-900/90">
              <div className="from-brand-red/20 to-brand-red/5 group-hover:from-brand-red/30 group-hover:to-brand-red/10 mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br transition-all duration-300">
                <Store className="text-brand-red h-8 w-8" />
              </div>
              <h3 className="mb-4 text-xl font-bold">
                Cần giải pháp tùy chỉnh?
              </h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Chúng tôi cung cấp các giải pháp tùy chỉnh cho doanh nghiệp có
                nhu cầu đặc biệt.
              </p>
              <a
                href="/contact"
                className="text-primary group-hover:text-primary/80 inline-flex items-center transition-colors hover:underline"
              >
                <span>Tìm hiểu thêm</span>
                <ArrowRightIcon className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </a>
            </div>

            {/* Enterprise card - Enhanced with hover effects and styling */}
            <div className="group hover:border-primary/20 dark:hover:border-primary/30 rounded-2xl border border-gray-200 bg-white/90 p-8 shadow-lg backdrop-blur-sm transition-all duration-300 hover:translate-y-[-5px] hover:shadow-xl dark:border-gray-800 dark:bg-gray-900/90">
              <div className="from-brand-yellow/20 to-brand-yellow/5 group-hover:from-brand-yellow/30 group-hover:to-brand-yellow/10 mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br transition-all duration-300">
                <Users className="text-brand-yellow-dark h-8 w-8" />
              </div>
              <h3 className="mb-4 text-xl font-bold">Doanh nghiệp lớn</h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Các doanh nghiệp lớn có thể liên hệ với bộ phận bán hàng để được
                tư vấn gói doanh nghiệp.
              </p>
              <a
                href="/contact"
                className="text-primary group-hover:text-primary/80 inline-flex items-center transition-colors hover:underline"
              >
                <span>Liên hệ bộ phận bán hàng</span>
                <ArrowRightIcon className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </a>
            </div>
          </div>

          {/* Testimonials - Enhanced with modern design and multiple reviews */}
          <div className="mt-20">
            <div className="mb-12 text-center">
              <Badge
                variant="outline"
                className="border-primary/20 bg-primary/5 text-primary animate-shimmer mb-4 px-4 py-1"
              >
                <Star className="mr-1 h-3.5 w-3.5" />
                <span>Đánh giá từ khách hàng</span>
              </Badge>
              <h3 className="from-primary bg-gradient-to-r to-purple-600 bg-clip-text pb-1 text-3xl leading-tight font-bold text-transparent">
                Khách hàng nói gì về chúng tôi
              </h3>
              <p className="text-muted-foreground mx-auto mt-3 max-w-2xl">
                Hàng trăm doanh nghiệp đã tin tưởng và sử dụng nền tảng của
                chúng tôi
              </p>
            </div>

            {/* Featured testimonial */}
            <div className="from-primary/10 mb-12 rounded-3xl border border-white/10 bg-gradient-to-r to-purple-500/10 p-8 shadow-xl md:p-12">
              <div className="flex flex-col items-center gap-8 md:flex-row md:items-start">
                <div className="flex-shrink-0">
                  <div className="relative h-24 w-24 overflow-hidden rounded-full border-4 border-white/80 shadow-xl">
                    <Image
                      src="/images/avatar-1.png"
                      alt="Khách hàng"
                      width={96}
                      height={96}
                      className="object-cover"
                    />
                  </div>
                </div>
                <div>
                  <div className="mb-4 flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className="text-brand-yellow fill-brand-yellow h-6 w-6 drop-shadow-sm"
                      />
                    ))}
                  </div>
                  <p className="mb-4 text-xl leading-relaxed text-gray-700 italic dark:text-gray-300">
                    "Chúng tôi đã tiết kiệm được rất nhiều thời gian và chi phí
                    khi sử dụng nền tảng Mini App. Việc tạo và quản lý cửa hàng
                    trực tuyến trên Zalo chưa bao giờ dễ dàng đến thế! Doanh thu
                    của chúng tôi đã tăng 30% chỉ sau 3 tháng triển khai."
                  </p>
                  <p className="text-lg font-bold">Nguyễn Minh Tuấn</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Giám đốc Công ty TNHH Thương mại Minh Tuấn
                  </p>
                </div>
              </div>
            </div>

            {/* Grid of additional testimonials */}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {[
                {
                  name: 'Trần Thị Hương',
                  role: 'Chủ cửa hàng thời trang Lavender',
                  image: '/images/avatar-2.png',
                  quote:
                    'Tôi không có kiến thức về lập trình nhưng vẫn có thể tự tạo và quản lý cửa hàng trực tuyến của mình trên Zalo. Thật tuyệt vời!',
                  stars: 5,
                },
                {
                  name: 'Lê Văn Hùng',
                  role: 'Giám đốc Công ty TNHH Dịch vụ Số',
                  image: '/images/avatar-3.png',
                  quote:
                    'Hệ thống tích điểm thông minh giúp khách hàng quay lại mua sắm nhiều hơn. Đây là công cụ tuyệt vời để tăng lượng khách hàng trung thành.',
                  stars: 5,
                },
                {
                  name: 'Phạm Thanh Hà',
                  role: 'Chủ tiệm bánh Sweetie Bakery',
                  image: '/images/avatar-4.png',
                  quote:
                    'Tính năng quản lý đơn hàng và kho hàng giúp tôi tiết kiệm rất nhiều thời gian. Khách hàng của tôi rất hài lòng với trải nghiệm mua sắm trên Zalo.',
                  stars: 4,
                },
                {
                  name: 'Nguyễn Văn Đức',
                  role: 'Giám đốc Công ty Mỹ phẩm Bella',
                  image: '/images/avatar-5.png',
                  quote:
                    'Việc kết nối với Zalo OA giúp chúng tôi tương tác với khách hàng dễ dàng hơn. Chúng tôi đã tăng 40% doanh số sau 6 tháng sử dụng nền tảng.',
                  stars: 5,
                },
                {
                  name: 'Võ Thị Lan',
                  role: 'Chủ cửa hàng mỹ phẩm Glow Up',
                  image: '/images/avatar-6.png',
                  quote:
                    'Tôi đã thử nhiều nền tảng khác nhau nhưng Mini App là dễ sử dụng nhất. Giao diện quản trị rất thân thiện và dễ hiểu, không cần đào tạo nhiều.',
                  stars: 4,
                },
                {
                  name: 'Trần Văn Minh',
                  role: 'Chủ cửa hàng điện tử TechZone',
                  image: '/images/avatar-7.png',
                  quote:
                    'Tính năng phân tích dữ liệu giúp tôi hiểu rõ hơn về khách hàng của mình. Tôi có thể tối ưu chiến lược kinh doanh dựa trên những thông tin này.',
                  stars: 5,
                },
              ].map((testimonial, index) => (
                <div
                  key={index}
                  className="group hover:border-primary/20 dark:hover:border-primary/30 overflow-hidden rounded-xl border border-gray-200 bg-white/90 shadow-lg transition-all duration-300 hover:translate-y-[-5px] hover:shadow-xl dark:border-gray-800 dark:bg-gray-900/90"
                >
                  <div className="p-6">
                    <div className="mb-4 flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${i < testimonial.stars ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`}
                        />
                      ))}
                    </div>
                    <p className="mb-6 text-sm text-gray-700 italic dark:text-gray-300">
                      "{testimonial.quote}"
                    </p>
                    <div className="flex items-center">
                      <div className="border-primary/20 mr-3 h-10 w-10 overflow-hidden rounded-full border-2">
                        <Image
                          src={testimonial.image}
                          alt={testimonial.name}
                          width={40}
                          height={40}
                          className="object-cover"
                        />
                      </div>
                      <div>
                        <p className="text-sm font-semibold">
                          {testimonial.name}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {testimonial.role}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Testimonial stats */}
            <div className="mt-12 flex flex-wrap justify-center gap-8 md:gap-16">
              {[
                { number: '500+', label: 'Doanh nghiệp đã sử dụng' },
                { number: '4.9/5', label: 'Xếp hạng trung bình' },
                { number: '98%', label: 'Khách hàng hài lòng' },
                { number: '35%', label: 'Tăng trưởng doanh thu trung bình' },
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-brand-gradient pb-1 text-3xl leading-tight font-bold md:text-4xl">
                    {stat.number}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* CTA Button - Enhanced with modern design */}
          <div className="mt-20 text-center">
            <Link
              href="/contact"
              className="group from-brand-red to-brand-blue focus:ring-brand-blue relative inline-flex items-center justify-center overflow-hidden rounded-full bg-gradient-to-r p-0.5 text-lg font-bold text-white transition-all duration-300 hover:scale-105 focus:ring-2 focus:ring-offset-2 focus:outline-none dark:focus:ring-offset-gray-900"
            >
              <span className="relative flex items-center space-x-2 rounded-full bg-white px-10 py-5 dark:bg-gray-800">
                <span className="text-brand-gradient">
                  Liên hệ bộ phận bán hàng
                </span>
                <ArrowRightIcon className="text-brand-blue h-5 w-5 transition-transform group-hover:translate-x-1" />
              </span>
            </Link>
          </div>
        </div>
      </div>

      {/* Phần FAQ */}
      <div className="bg-gray-50 py-16 dark:bg-gray-900/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-16 text-center">
            <h2 className="mb-4 text-3xl font-bold tracking-tight md:text-4xl lg:text-5xl">
              Câu hỏi thường{' '}
              <span className="text-brand-gradient inline-block pb-1">gặp</span>
            </h2>
            <p className="text-muted-foreground mx-auto max-w-3xl text-lg">
              Giải đáp những thắc mắc phổ biến về nền tảng Mini App của chúng
              tôi
            </p>
          </div>

          <div className="mx-auto max-w-3xl">
            <Tabs defaultValue="general" className="w-full">
              <TabsList className="mb-12 flex w-full flex-col gap-2 rounded-xl border border-gray-200 bg-white/80 p-1 shadow-md backdrop-blur-sm sm:grid sm:grid-cols-3">
                <TabsTrigger
                  value="general"
                  className="tabs-store h-auto py-3 text-sm whitespace-normal sm:text-base"
                >
                  Tổng quan
                </TabsTrigger>
                <TabsTrigger
                  value="technical"
                  className="tabs-marketing h-auto py-3 text-sm whitespace-normal sm:text-base"
                >
                  Kỹ thuật
                </TabsTrigger>
                <TabsTrigger
                  value="billing"
                  className="tabs-loyalty h-auto py-3 text-sm whitespace-normal sm:text-base"
                >
                  Thanh toán
                </TabsTrigger>
              </TabsList>

              <TabsContent value="general" className="space-y-4">
                {[
                  {
                    question: 'Nền tảng quản lý giáo dục này phù hợp với loại trường nào?',
                    answer:
                      'Nền tảng phù hợp với trường mầm non, trung tâm anh ngữ, trung tâm năng khiếu, và các cơ sở giáo dục có quy mô từ 50-1000 học sinh.',
                  },
                  {
                    question:
                      'Tôi có cần kiến thức kỹ thuật để sử dụng hệ thống không?',
                    answer:
                      'Không, hệ thống được thiết kế thân thiện với người dùng. Chỉ cần biết sử dụng máy tính cơ bản là có thể quản lý toàn bộ trường học.',
                  },
                  {
                    question:
                      'Phụ huynh có cần cài đặt ứng dụng gì không?',
                    answer:
                      'Không cần cài đặt. Phụ huynh chỉ cần có Zalo (đã có sẵn) và truy cập Mini App trực tiếp trên Zalo để theo dõi con em.',
                  },
                ].map((faq, i) => (
                  <Card
                    key={i}
                    className="border border-gray-200 dark:border-gray-800"
                  >
                    <CardContent className="pt-6">
                      <h3 className="mb-2 text-lg font-bold">{faq.question}</h3>
                      <p className="text-muted-foreground">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              <TabsContent value="technical" className="space-y-4">
                {[
                  {
                    question:
                      'Hệ thống ZNS thông báo hoạt động như thế nào?',
                    answer:
                      'ZNS sẽ tự động gửi thông báo về điểm danh, học phí, sự kiện đến phụ huynh qua Zalo. Sử dụng pool chia sẻ giúp tiết kiệm 70% chi phí so với ZNS riêng.',
                  },
                  {
                    question: 'Dữ liệu học sinh có được bảo mật không?',
                    answer:
                      'Có, tất cả dữ liệu được mã hóa và lưu trữ an toàn. Chỉ những người có quyền mới truy cập được thông tin học sinh và phụ huynh.',
                  },
                  {
                    question: 'Tôi có thể xuất báo cáo và dữ liệu không?',
                    answer:
                      'Có, hệ thống cho phép xuất báo cáo điểm danh, học phí, tiến độ học tập dưới nhiều định dạng (Excel, PDF) để phục vụ báo cáo và phân tích.',
                  },
                ].map((faq, i) => (
                  <Card
                    key={i}
                    className="border border-gray-200 dark:border-gray-800"
                  >
                    <CardContent className="pt-6">
                      <h3 className="mb-2 text-lg font-bold">{faq.question}</h3>
                      <p className="text-muted-foreground">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>

              <TabsContent value="billing" className="space-y-4">
                {[
                  {
                    question: 'Có gói dùng thử miễn phí không?',
                    answer:
                      'Có, chúng tôi cung cấp gói dùng thử miễn phí 30 ngày với đầy đủ tính năng để bạn trải nghiệm trước khi quyết định sử dụng lâu dài.',
                  },
                  {
                    question:
                      'Chi phí sử dụng hàng tháng là bao nhiều?',
                    answer:
                      'Chi phí từ 500k-2M VND/tháng tùy theo quy mô trường học và tính năng sử dụng. Bao gồm cả ZNS thông báo và Mini App không giới hạn.',
                  },
                  {
                    question: 'Có hỗ trợ thanh toán theo quý hoặc năm không?',
                    answer:
                      'Có, chúng tôi hỗ trợ thanh toán theo quý (giảm 10%) và theo năm (giảm 20%). Thanh toán qua chuyển khoản hoặc thẻ tín dụng.',
                  },
                ].map((faq, i) => (
                  <Card
                    key={i}
                    className="border border-gray-200 dark:border-gray-800"
                  >
                    <CardContent className="pt-6">
                      <h3 className="mb-2 text-lg font-bold">{faq.question}</h3>
                      <p className="text-muted-foreground">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Phần CTA cuối trang */}
      <div className="py-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative overflow-hidden rounded-[2.5rem] shadow-2xl">
            {/* Background with 3D effect */}
            <div className="from-primary absolute inset-0 bg-gradient-to-br via-purple-600 to-indigo-700 opacity-90"></div>

            {/* Decorative elements */}
            <div className="absolute inset-0">
              <div className="absolute top-0 left-0 h-full w-full overflow-hidden">
                <div className="absolute -top-40 -left-40 h-80 w-80 rounded-full bg-white opacity-10 blur-3xl"></div>
                <div className="absolute top-60 -right-20 h-72 w-72 rounded-full bg-yellow-400 opacity-10 blur-3xl"></div>
                <div className="absolute -bottom-40 left-40 h-64 w-64 rounded-full bg-purple-300 opacity-10 blur-3xl"></div>

                {/* Floating shapes */}
                <div className="animate-float absolute top-20 left-[10%] h-16 w-16 rounded-lg bg-white opacity-20"></div>
                <div className="animate-float-slow absolute top-40 right-[15%] h-8 w-8 rounded-full bg-yellow-400 opacity-30"></div>
                <div className="animate-float-slower absolute bottom-32 left-[20%] h-12 w-12 rotate-45 rounded-lg bg-purple-300 opacity-20"></div>

                {/* Grid pattern */}
                <svg
                  className="absolute inset-0 h-full w-full"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs>
                    <pattern
                      id="grid-pattern"
                      width="40"
                      height="40"
                      patternUnits="userSpaceOnUse"
                      patternTransform="rotate(10)"
                    >
                      <path
                        d="M0 40V0H40"
                        fill="none"
                        stroke="white"
                        strokeWidth="1"
                        opacity="0.1"
                      />
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#grid-pattern)" />
                </svg>
              </div>
            </div>

            {/* Content */}
            <div className="relative px-6 py-24 sm:px-12 lg:px-16">
              <div className="mx-auto max-w-4xl text-center">
                {/* Badge */}
                <div className="mx-auto mb-8 inline-flex items-center rounded-full bg-white/10 px-4 py-2 backdrop-blur-sm">
                  <span className="mr-2 inline-block rounded-full bg-yellow-400 p-1">
                    <Zap className="text-primary h-4 w-4" />
                  </span>
                  <span className="text-sm font-medium text-white">
                    Thiết lập trường học thông minh chỉ trong vài phút
                  </span>
                </div>

                {/* Heading */}
                <h2 className="mb-6 text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">
                  <span className="mb-2 block">Sẵn sàng để nâng cao</span>
                  <span className="block">
                    chất lượng quản lý{' '}
                    <span className="relative inline-block">
                      <span className="relative z-10 text-yellow-300">
                        trường học
                      </span>
                      <span className="absolute bottom-2 left-0 z-0 h-3 w-full bg-purple-500/30 blur-sm"></span>
                    </span>
                    ?
                  </span>
                </h2>

                {/* Description */}
                <p className="mx-auto mb-12 max-w-2xl text-xl leading-relaxed text-white/90">
                  Dùng thử miễn phí 30 ngày và trải nghiệm sức mạnh của nền tảng
                  quản lý giáo dục thông minh với Zalo Mini App tích hợp
                </p>

                {/* Stats */}
                <div className="mb-12 grid grid-cols-1 gap-8 sm:grid-cols-3">
                  {[
                    { number: '200+', label: 'Trường học đã sử dụng' },
                    { number: '15,000+', label: 'Phụ huynh đang sử dụng' },
                    { number: '99.9%', label: 'Tỷ lệ hoạt động ổn định' },
                  ].map((stat, i) => (
                    <div
                      key={i}
                      className="rounded-xl bg-white/10 p-6 backdrop-blur-sm"
                    >
                      <div className="text-3xl font-bold text-white">
                        {stat.number}
                      </div>
                      <div className="text-sm text-white/80">{stat.label}</div>
                    </div>
                  ))}
                </div>

                {/* CTA Buttons */}
                <div className="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-y-0 sm:space-x-6">
                  <a
                    href="/auth/sign-up"
                    className="group text-primary relative inline-flex items-center justify-center overflow-hidden rounded-lg bg-white px-8 py-4 text-lg font-bold transition-all duration-300 ease-out hover:scale-105"
                  >
                    <span className="from-primary absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r to-purple-600 transition-all duration-500 ease-out group-hover:h-full"></span>
                    <span className="absolute right-0 -mt-12 h-32 w-8 translate-x-12 rotate-12 transform bg-white opacity-10 transition-all duration-1000 ease-out group-hover:-translate-x-40"></span>
                    <span className="relative flex items-center space-x-2">
                      <span className="relative z-20 transition-colors duration-300 ease-out group-hover:text-white">
                        Bắt đầu miễn phí
                      </span>
                      <ArrowRightIcon className="relative z-20 h-5 w-5 transition-colors duration-300 ease-out group-hover:text-white" />
                    </span>
                  </a>

                  <a
                    href="/contact"
                    className="group relative inline-flex items-center justify-center overflow-hidden rounded-lg border-2 border-white bg-transparent px-8 py-4 text-lg font-bold text-white transition-all duration-300 ease-out hover:scale-105"
                  >
                    <span className="absolute bottom-0 left-0 h-1 w-0 bg-white transition-all duration-500 ease-out group-hover:h-full group-hover:w-full"></span>
                    <span className="group-hover:text-primary relative z-20 transition-colors duration-300 ease-out">
                      Liên hệ tư vấn
                    </span>
                  </a>
                </div>

                {/* Trust indicators */}
                <div className="mt-10">
                  <div className="flex flex-wrap items-center justify-center gap-x-8 gap-y-4 text-white/80">
                    <div className="flex items-center">
                      <CheckCircle2 className="mr-2 h-5 w-5 text-yellow-300" />
                      <span>Không cần thẻ tín dụng</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle2 className="mr-2 h-5 w-5 text-yellow-300" />
                      <span>Hỗ trợ 24/7</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle2 className="mr-2 h-5 w-5 text-yellow-300" />
                      <span>Dễ dàng sử dụng</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle2 className="mr-2 h-5 w-5 text-yellow-300" />
                      <span>Cập nhật liên tục</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default withI18n(Home);
