'use server';

import { z } from 'zod';

import { getMailer } from '@kit/mailers';
import { enhanceAction } from '@kit/next/actions';

import { ContactEmailSchema } from '../contact-email.schema';

const contactEmail = z
  .string({
    description: `Email nơi bạn muốn nhận các yêu cầu liên hệ từ form.`,
    required_error:
      'Email liên hệ là bắt buộc. Vui lòng sử dụng biến môi trường CONTACT_EMAIL.',
  })
  .parse(process.env.CONTACT_EMAIL);

const emailFrom = z
  .string({
    description: `Địa chỉ email gửi.`,
    required_error:
      'Email người gửi là bắt buộc. Vui lòng sử dụng biến môi trường EMAIL_SENDER.',
  })
  .parse(process.env.EMAIL_SENDER);

export const sendContactEmail = enhanceAction(
  async (data) => {
    const mailer = await getMailer();

    await mailer.sendEmail({
      to: contactEmail,
      from: emailFrom,
      subject: '<PERSON><PERSON><PERSON> cầu liên hệ mới',
      html: `
        <p>
          Bạn đã nhận được một yêu cầu liên hệ mới từ form liên hệ.
        </p>

        <p>Họ tên: ${data.name}</p>
        <p>Email: ${data.email}</p>
        <p>Số điện thoại: ${data.phone}</p>
        <p>Nội dung: ${data.message}</p>
      `,
    });

    return {};
  },
  {
    schema: ContactEmailSchema,
    auth: false,
  },
);
