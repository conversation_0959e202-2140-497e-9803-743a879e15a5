/* Enhanced floating animations with subtle rotation and scaling */
@keyframes float {
  0% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
  25% {
    transform: translateY(-5px) rotate(1deg) scale(1.01);
  }
  50% {
    transform: translateY(-10px) rotate(0deg) scale(1.02);
  }
  75% {
    transform: translateY(-5px) rotate(-1deg) scale(1.01);
  }
  100% {
    transform: translateY(0px) rotate(0deg) scale(1);
  }
}

@keyframes float-slow {
  0% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-3px) rotate(-1deg);
  }
  50% {
    transform: translateY(-5px) rotate(0deg);
  }
  75% {
    transform: translateY(-3px) rotate(1deg);
  }
  100% {
    transform: translateY(0px) rotate(0deg);
  }
}

@keyframes float-slower {
  0% {
    transform: translateY(0px) rotate(45deg) scale(1);
  }
  33% {
    transform: translateY(-4px) rotate(46deg) scale(1.02);
  }
  66% {
    transform: translateY(-8px) rotate(44deg) scale(1.04);
  }
  100% {
    transform: translateY(0px) rotate(45deg) scale(1);
  }
}

/* Pulse animation for highlights */
@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Shimmer effect for gradients */
@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float-slower {
  animation: float-slower 10s ease-in-out infinite;
}

.animate-pulse-subtle {
  animation: pulse-subtle 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
}

/* Enhanced grid patterns with better contrast */
.bg-grid-gray-900\/5 {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(15 23 42 / 0.05)'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

.bg-grid-gray-100\/5 {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(241 245 249 / 0.05)'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

/* Custom styling for pricing table */
.pricing-table-wrapper {
  --card-gap: 1.5rem;
  --card-padding: 1.5rem;
  --card-radius: 1rem;
  --card-shadow: 0 4px 20px -5px rgba(0, 0, 0, 0.1);
  --card-shadow-hover: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  --card-border-width: 2px;
  --card-transition: all 0.3s ease-in-out;
}
