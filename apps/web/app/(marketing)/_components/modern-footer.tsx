'use client';

import {
  Facebook,
  Instagram,
  Linkedin,
  Mail,
  Phone,
  Twitter,
} from 'lucide-react';

import { Trans } from '@kit/ui/trans';

import { SubscribeForm } from './subscribe-form/subscribe-form';

import { AppLogo } from '~/components/app-logo';
import appConfig from '~/config/app.config';

export function ModernFooter() {

  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative overflow-hidden bg-gray-50 pt-24 pb-12 dark:bg-gray-900/80">
      {/* Decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="pointer-events-none absolute inset-0">
          {/* Gradient blobs */}
          <div className="from-primary/5 absolute -top-40 -left-40 h-[500px] w-[500px] rounded-full bg-gradient-to-br to-purple-500/5 opacity-70 blur-3xl"></div>
          <div className="from-brand-blue/5 to-brand-red/5 absolute -right-40 bottom-0 h-[300px] w-[300px] rounded-full bg-gradient-to-br opacity-70 blur-3xl"></div>
          <div className="from-brand-yellow/10 to-brand-yellow/5 absolute top-1/3 right-1/4 h-[200px] w-[200px] rounded-full bg-gradient-to-br opacity-70 blur-3xl"></div>

          {/* Grid pattern */}
          <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-center opacity-[0.03] dark:opacity-[0.02]"></div>
        </div>
      </div>

      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main footer content */}
        <div className="grid grid-cols-1 gap-12 md:grid-cols-2 lg:grid-cols-4">
          {/* Column 1: Logo and info */}
          <div className="flex flex-col space-y-6">
            <div className="flex items-center">
              <AppLogo className="w-[120px]" />
            </div>
            <p className="text-muted-foreground text-sm">
              <Trans i18nKey="marketing:footerDescription">
                MinApp là nền tảng giúp doanh nghiệp tạo cửa hàng trực tuyến
                trên Zalo một cách nhanh chóng và hiệu quả.
              </Trans>
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="text-muted-foreground hover:text-primary flex h-10 w-10 items-center justify-center rounded-full bg-white shadow-sm transition-colors dark:bg-gray-800"
              >
                <Facebook className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-muted-foreground hover:text-primary flex h-10 w-10 items-center justify-center rounded-full bg-white shadow-sm transition-colors dark:bg-gray-800"
              >
                <Instagram className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-muted-foreground hover:text-primary flex h-10 w-10 items-center justify-center rounded-full bg-white shadow-sm transition-colors dark:bg-gray-800"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="#"
                className="text-muted-foreground hover:text-primary flex h-10 w-10 items-center justify-center rounded-full bg-white shadow-sm transition-colors dark:bg-gray-800"
              >
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Column 2: Quick Links */}
          <div className="flex flex-col space-y-6">
            <h4 className="text-lg font-semibold">Liên kết nhanh</h4>
            <ul className="space-y-3">
              <li>
                <a
                  href="/about"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Về chúng tôi
                </a>
              </li>
              <li>
                <a
                  href="/blog"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Blog
                </a>
              </li>
              <li>
                <a
                  href="/docs"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Tài liệu hướng dẫn
                </a>
              </li>
              <li>
                <a
                  href="/pricing"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Bảng giá
                </a>
              </li>
              <li>
                <a
                  href="/contact"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Liên hệ
                </a>
              </li>
            </ul>
          </div>

          {/* Column 3: Legal */}
          <div className="flex flex-col space-y-6">
            <h4 className="text-lg font-semibold">Pháp lý</h4>
            <ul className="space-y-3">
              <li>
                <a
                  href="/terms-of-service"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Điều khoản dịch vụ
                </a>
              </li>
              <li>
                <a
                  href="/privacy-policy"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Chính sách bảo mật
                </a>
              </li>
              <li>
                <a
                  href="/cookie-policy"
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  Chính sách cookie
                </a>
              </li>
            </ul>
          </div>

          {/* Column 4: Newsletter */}
          <div className="flex flex-col space-y-6">
            <h4 className="text-lg font-semibold">Đăng ký nhận tin</h4>
            <p className="text-muted-foreground text-sm">
              Đăng ký để nhận thông tin mới nhất về sản phẩm, khuyến mãi và cập
              nhật từ chúng tôi.
            </p>
            <SubscribeForm />
            <div className="flex items-center space-x-3">
              <div className="from-primary/20 flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br to-purple-500/20">
                <Phone className="text-primary h-4 w-4" />
              </div>
              <div>
                <p className="text-xs font-medium">Hỗ trợ 24/7</p>
                <a
                  href="tel:02488888166"
                  className="text-muted-foreground hover:text-primary text-xs transition-colors"
                >
                  02 488 888 166
                </a>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="from-brand-blue/20 to-brand-red/20 flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br">
                <Mail className="text-brand-blue h-4 w-4" />
              </div>
              <div>
                <p className="text-xs font-medium">Email</p>
                <a
                  href="mailto:<EMAIL>"
                  className="text-muted-foreground hover:text-primary text-xs transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom bar */}
        <div className="mt-16 flex flex-col items-center justify-between space-y-4 border-t border-gray-200 pt-8 md:flex-row md:space-y-0 dark:border-gray-800">
          <p className="text-muted-foreground text-sm">
            © {currentYear} {appConfig.name}. Tất cả các quyền được bảo lưu.
          </p>
          <div className="flex space-x-6">
            <a
              href="#"
              className="text-muted-foreground hover:text-primary text-sm transition-colors"
            >
              Chính sách bảo mật
            </a>
            <a
              href="#"
              className="text-muted-foreground hover:text-primary text-sm transition-colors"
            >
              Điều khoản sử dụng
            </a>
            <a
              href="#"
              className="text-muted-foreground hover:text-primary text-sm transition-colors"
            >
              Cookie
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
