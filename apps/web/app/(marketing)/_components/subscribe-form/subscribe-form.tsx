'use client';

import { useState, useTransition } from 'react';

import { zodResolver } from '@hookform/resolvers/zod';
import { Mail, Zap } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { Alert, AlertDescription, AlertTitle } from '@kit/ui/alert';
import { Button } from '@kit/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';

import { SubscribeEmailSchema } from './_lib/subscribe-email.schema';
import { sendSubscribeEmail } from './_lib/server/server-actions';

export function SubscribeForm() {
  const [pending, startTransition] = useTransition();
  const [state, setState] = useState({
    success: false,
    error: false,
    alreadySubscribed: false,
  });

  const form = useForm({
    resolver: zodResolver(SubscribeEmailSchema),
    defaultValues: {
      email: '',
    },
  });

  if (state.success) {
    return <SuccessAlert />;
  }

  if (state.error) {
    return <ErrorAlert />;
  }

  if (state.alreadySubscribed) {
    return <AlreadySubscribedAlert />;
  }

  return (
    <Form {...form}>
      <form
        className={'flex flex-col space-y-3'}
        onSubmit={form.handleSubmit((data) => {
          startTransition(async () => {
            try {
              const result = await sendSubscribeEmail(data);
              
              if (result.message === 'Email này đã đăng ký nhận tin trước đó') {
                setState({ success: false, error: false, alreadySubscribed: true });
              } else {
                setState({ success: true, error: false, alreadySubscribed: false });
              }
            } catch {
              setState({ error: true, success: false, alreadySubscribed: false });
            }
          });
        })}
      >
        <FormField
          name={'email'}
          render={({ field }) => {
            return (
              <FormItem>
                <FormControl>
                  <div className="relative">
                    <Mail className="text-muted-foreground absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform" />
                    <Input 
                      type={'email'} 
                      placeholder="Email của bạn" 
                      className="pl-10" 
                      {...field} 
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            );
          }}
        />

        <Button 
          disabled={pending} 
          type={'submit'} 
          className="bg-brand-gradient-primary w-full"
        >
          {pending ? (
            <span>Đang xử lý...</span>
          ) : (
            <>
              <span>Đăng ký</span>
              <Zap className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </form>
    </Form>
  );
}

function SuccessAlert() {
  return (
    <Alert variant={'success'} className="bg-green-50 text-green-800 dark:bg-green-950 dark:text-green-300">
      <AlertTitle className="text-green-800 dark:text-green-300">
        Đăng ký thành công!
      </AlertTitle>

      <AlertDescription className="text-green-700 dark:text-green-400">
        Cảm ơn bạn đã đăng ký nhận tin. Chúng tôi sẽ gửi cho bạn những thông tin mới nhất.
      </AlertDescription>
    </Alert>
  );
}

function ErrorAlert() {
  return (
    <Alert variant={'destructive'} className="bg-red-50 text-red-800 dark:bg-red-950 dark:text-red-300">
      <AlertTitle className="text-red-800 dark:text-red-300">
        Đã xảy ra lỗi!
      </AlertTitle>

      <AlertDescription className="text-red-700 dark:text-red-400">
        Không thể đăng ký nhận tin. Vui lòng thử lại sau.
      </AlertDescription>
    </Alert>
  );
}

function AlreadySubscribedAlert() {
  return (
    <Alert variant={'info'} className="bg-blue-50 text-blue-800 dark:bg-blue-950 dark:text-blue-300">
      <AlertTitle className="text-blue-800 dark:text-blue-300">
        Email đã đăng ký!
      </AlertTitle>

      <AlertDescription className="text-blue-700 dark:text-blue-400">
        Email này đã đăng ký nhận tin từ trước. Cảm ơn bạn đã quan tâm.
      </AlertDescription>
    </Alert>
  );
}
