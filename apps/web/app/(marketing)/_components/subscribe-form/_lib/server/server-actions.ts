'use server';

import { z } from 'zod';

import { getMailer } from '@kit/mailers';
import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerAdminClient } from '@kit/supabase/server-admin-client';

import { SubscribeEmailSchema } from '../subscribe-email.schema';

const contactEmail = z
  .string({
    description: `Email nơi bạn muốn nhận các yêu cầu đăng ký từ form.`,
    required_error:
      'Email liên hệ là bắt buộc. Vui lòng sử dụng biến môi trường CONTACT_EMAIL.',
  })
  .parse(process.env.CONTACT_EMAIL);

const emailFrom = z
  .string({
    description: `Địa chỉ email gửi.`,
    required_error:
      'Email người gửi là bắt buộc. Vui lòng sử dụng biến môi trường EMAIL_SENDER.',
  })
  .parse(process.env.EMAIL_SENDER);

export const sendSubscribeEmail = enhanceAction(
  async (data) => {
    const { email } = data;

    try {
      // Kiểm tra và lưu vào cơ sở dữ liệu
      const supabase = getSupabaseServerAdminClient();

      // Kiểm tra xem email đã tồn tại trong danh sách chưa
      const { data: existingSubscriber, error: selectError } = await supabase
        .from('newsletter_subscribers')
        .select('*')
        .eq('email', email)
        .maybeSingle();

      if (selectError) {
        console.error('Error checking existing subscriber:', selectError);
      } else if (existingSubscriber) {
        return {
          success: true,
          message: 'Email này đã đăng ký nhận tin trước đó',
        };
      }

      // Thêm email vào danh sách đăng ký
      const { error: insertError } = await supabase.from('newsletter_subscribers').insert([
        {
          email,
          subscribed_at: new Date().toISOString(),
          status: 'active',
        },
      ]);

      if (insertError) {
        console.error('Error inserting subscriber:', insertError);
        throw new Error('Lỗi khi lưu thông tin đăng ký');
      }
    } catch (dbError) {
      console.error('Database operation error:', dbError);
      // Tiếp tục gửi email ngay cả khi có lỗi cơ sở dữ liệu
    }

    const mailer = await getMailer();

    // Gửi email thông báo cho admin
    await mailer.sendEmail({
      to: contactEmail,
      from: emailFrom,
      subject: 'Đăng ký nhận tin mới',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4f46e5;">Đăng ký nhận tin mới</h2>
          <p>Bạn đã nhận được một đăng ký nhận tin mới từ website.</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Thời gian:</strong> ${new Date().toLocaleString('vi-VN')}</p>
        </div>
      `,
    });

    // Gửi email xác nhận cho người đăng ký
    await mailer.sendEmail({
      to: email,
      from: emailFrom,
      subject: 'Đăng ký nhận tin tức thành công',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4f46e5;">Cảm ơn bạn đã đăng ký!</h2>
          <p>Xin chào,</p>
          <p>Cảm ơn bạn đã đăng ký nhận tin tức từ chúng tôi. Bạn sẽ được cập nhật về các sản phẩm mới, khuyến mãi và tin tức mới nhất.</p>
          <p>Nếu bạn không đăng ký nhận tin này, vui lòng bỏ qua email này.</p>
          <p>Trân trọng,<br>Team MinApp</p>
        </div>
      `,
    });

    return {
      success: true,
      message: 'Đăng ký nhận tin thành công',
    };
  },
  {
    schema: SubscribeEmailSchema,
    auth: false,
  },
);
