'use client';

import { useEffect, useState } from 'react';

import { createPortal } from 'react-dom';

import { <PERSON>R<PERSON>, Check, Shield, Sparkles, Star, Zap } from 'lucide-react';

import { PricingTable } from '@kit/billing-gateway/marketing';
import { Badge } from '@kit/ui/badge';
import { cn } from '@kit/ui/utils';

import billingConfig from '~/config/billing.config';
import pathsConfig from '~/config/paths.config';

import './pricing-animations.css';
import './pricing-table-styles.css';

// Custom component to handle interval switching
function CustomPricingTable({
  billingConfig,
  pathsConfig,
  selectedInterval,
}: {
  billingConfig: any;
  pathsConfig: any;
  selectedInterval: 'month' | 'year';
}) {
  // Create a filtered config with only the selected interval
  const filteredConfig = {
    ...billingConfig,
    products: billingConfig.products
      .map((product: any) => {
        const filteredPlans = product.plans.filter(
          (plan: any) =>
            plan.paymentType === 'one-time' ||
            plan.interval === selectedInterval,
        );

        console.log(
          `Product ${product.name} has ${filteredPlans.length} plans for interval ${selectedInterval}:`,
        );
        filteredPlans.forEach((plan: any) => {
          console.log(
            ` - Plan: ${plan.name}, Interval: ${plan.interval || 'none'}, PaymentType: ${plan.paymentType}`,
          );
        });

        return {
          ...product,
          plans: filteredPlans,
        };
      })
      // Lọc bỏ các sản phẩm không có plan nào
      .filter((product: any) => product.plans.length > 0),
  };

  // Force re-render when selectedInterval changes
  const [key, setKey] = useState(0);

  useEffect(() => {
    // Update key to force re-render of PricingTable
    console.log(
      'CustomPricingTable: selectedInterval changed to',
      selectedInterval,
    );
    setKey((prev) => prev + 1);
  }, [selectedInterval]);

  // Kiểm tra xem có ít nhất một sản phẩm để hiển thị không
  const hasProducts = filteredConfig.products.length > 0;
  console.log(`Has products to display: ${hasProducts}`);

  return (
    <div
      key={key}
      className="animate-in fade-in slide-in-from-bottom-4 duration-500"
    >
      {hasProducts ? (
        <div className="pricing-table-wrapper">
          <PricingTable
            config={filteredConfig}
            paths={{
              signUp: pathsConfig.auth.signUp,
              return: pathsConfig.app.home,
            }}
          />
        </div>
      ) : (
        <div className="rounded-xl border border-gray-200 bg-white/80 p-8 text-center shadow-md backdrop-blur-sm dark:border-gray-800 dark:bg-gray-900/80">
          <p className="text-lg text-gray-500">
            Không có gói dịch vụ nào cho kỳ thanh toán đã chọn.
          </p>
        </div>
      )}
    </div>
  );
}

export function ModernPricingTable() {
  const [billingInterval, setBillingInterval] = useState<'month' | 'year'>(
    'month',
  );

  // Lấy các interval từ config
  const intervals = billingConfig.products
    .flatMap((product) => product.plans)
    .map((plan) => plan.interval)
    .filter(
      (interval, index, self) => interval && self.indexOf(interval) === index,
    ) as ('month' | 'year')[];

  // State to track if we're on the client side
  const [isMounted, setIsMounted] = useState(false);

  // Only run on client side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <div className="w-full">
      {/* Billing interval toggle - Modern Design */}
      {intervals.length > 1 &&
        isMounted &&
        document.getElementById('pricing-tabs-container') &&
        createPortal(
          <div className="flex justify-center">
            <div
              className="relative flex items-center justify-center overflow-hidden rounded-full border border-gray-200/60 bg-gradient-to-r from-gray-50/90 to-gray-100/90 p-1 shadow-lg backdrop-blur-sm transition-all duration-300 hover:border-gray-300/80 hover:shadow-xl dark:border-gray-700/60 dark:from-gray-800/70 dark:to-gray-900/70 dark:hover:border-gray-600/80"
              style={{ width: '350px', maxWidth: '100%' }}
            >
              {/* Hai nút chuyển đổi */}
              {intervals.map((interval) => (
                <button
                  key={interval}
                  onClick={() => {
                    console.log('Setting billing interval to:', interval);
                    setBillingInterval(interval);
                  }}
                  className={cn(
                    'relative z-10 flex w-1/2 items-center justify-center rounded-full px-2 py-1.5 text-sm font-medium transition-all duration-300 ease-in-out',
                    billingInterval === interval
                      ? 'text-white' // Active tab
                      : 'text-gray-500 hover:bg-white/10 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700/20 dark:hover:text-gray-100', // Inactive tab with hover effect
                  )}
                >
                  <div className="flex w-full items-center justify-center">
                    {interval === 'month' ? (
                      <div className="flex items-center space-x-1.5 px-1">
                        <Zap className="h-3.5 w-3.5" />
                        <span>Hàng tháng</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1.5 px-1">
                        <Star className="h-3.5 w-3.5" />
                        <span>Hàng năm</span>
                        <Badge
                          variant="success"
                          className="ml-1 bg-gradient-to-r from-green-500 to-emerald-500 px-1.5 py-0.5 text-[10px] font-semibold text-white shadow-sm"
                        >
                          -20%
                        </Badge>
                      </div>
                    )}
                  </div>
                </button>
              ))}

              {/* Animated background with gradient */}
              <div
                className="absolute top-0 left-0 h-full w-1/2"
                style={{
                  transition:
                    'transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)',
                  transform: `translateX(${billingInterval === 'month' ? '0%' : '100%'})`,
                }}
              >
                <div className="from-primary h-full w-full rounded-full bg-gradient-to-r to-purple-600 opacity-95 shadow-md"></div>
              </div>
            </div>
          </div>,
          document.getElementById('pricing-tabs-container')!,
        )}

      {/* Pricing cards container - Enhanced with 3D effects */}
      <div className="relative">
        {/* Decorative elements - Enhanced glow effects with animations */}
        <div className="from-primary/20 to-primary/5 animate-pulse-subtle absolute -top-10 -left-10 h-40 w-40 rounded-full bg-gradient-to-br blur-3xl"></div>
        <div className="animate-pulse-subtle absolute -right-10 -bottom-10 h-40 w-40 rounded-full bg-gradient-to-tl from-purple-500/20 to-purple-500/5 blur-3xl"></div>
        <div className="animate-pulse-subtle absolute top-1/3 right-1/4 h-20 w-20 rounded-full bg-yellow-400/10 blur-2xl"></div>

        {/* Floating elements */}
        <div className="animate-float bg-primary/10 absolute -top-5 left-10 h-10 w-10 rounded-lg backdrop-blur-sm"></div>
        <div className="animate-float-slow absolute right-5 bottom-10 h-8 w-8 rounded-full bg-purple-500/15 backdrop-blur-sm"></div>

        {/* Pricing table wrapper with enhanced styling - Glass morphism effect */}
        <div className="relative overflow-hidden rounded-3xl border border-gray-200 bg-white/90 p-2 shadow-2xl backdrop-blur-sm dark:border-gray-800 dark:bg-gray-900/90">
          <div className="absolute inset-0 overflow-hidden">
            <div className="bg-grid-gray-900/5 dark:bg-grid-gray-100/5 pointer-events-none absolute inset-0 [mask-image:linear-gradient(0deg,#fff,rgba(255,255,255,0.6))]"></div>
          </div>

          {/* Subtle inner glow */}
          <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/5 to-transparent"></div>

          <div className="relative">
            {/* Tạo một phiên bản mới của billingConfig với chỉ một interval để ẩn bộ chuyển đổi trong PricingTable */}
            <CustomPricingTable
              billingConfig={billingConfig}
              pathsConfig={pathsConfig}
              selectedInterval={billingInterval}
            />
          </div>
        </div>
      </div>

      {/* Feature comparison - Enhanced with modern design */}
      <div className="mt-24">
        <div className="mb-12 text-center">
          <Badge
            variant="outline"
            className="border-primary/20 bg-primary/5 text-primary animate-shimmer mb-4 px-4 py-1"
          >
            <Shield className="mr-1 h-3.5 w-3.5" />
            <span>So sánh chi tiết</span>
          </Badge>
          <h3 className="from-primary bg-gradient-to-r to-purple-600 bg-clip-text text-3xl font-bold text-transparent">
            So sánh các tính năng
          </h3>
          <p className="text-muted-foreground mx-auto mt-3 max-w-2xl">
            Chọn gói phù hợp với nhu cầu kinh doanh của bạn
          </p>
        </div>

        <div className="overflow-hidden rounded-2xl border border-gray-200 bg-white/90 shadow-xl backdrop-blur-sm dark:border-gray-800 dark:bg-gray-900/90">
          <div
            className="overflow-x-auto"
            style={{ WebkitOverflowScrolling: 'touch' }}
          >
            <div className="min-w-[768px]">
              <table className="w-full border-collapse text-left">
                <thead>
                  <tr className="border-b border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-800/50">
                    <th
                      className="px-6 py-5 text-lg font-medium"
                      style={{ minWidth: '180px' }}
                    >
                      Tính năng
                    </th>
                    {billingConfig.products
                      .filter((p) => !p.hidden)
                      .map((product, productIndex) => (
                        <th
                          key={`header-${product.id}-${productIndex}`}
                          className="px-6 py-5 text-center"
                          style={{ minWidth: '140px' }}
                        >
                          <div className="flex flex-col items-center">
                            <span className="from-primary bg-gradient-to-r to-purple-600 bg-clip-text pb-1 text-lg leading-tight font-bold text-transparent">
                              {product.name}
                            </span>
                            {product.badge && (
                              <Badge
                                variant="outline"
                                className="bg-primary/10 text-primary border-primary/20 mt-1"
                              >
                                {product.badge}
                              </Badge>
                            )}
                          </div>
                        </th>
                      ))}
                  </tr>
                </thead>
                <tbody>
                  {[
                    'Tạo Mini App',
                    'Tích hợp Zalo OA',
                    'Quản lý sản phẩm',
                    'Quản lý đơn hàng',
                    'Phân tích dữ liệu',
                    'Tích điểm thông minh',
                    'Hỗ trợ 24/7',
                    'API tùy chỉnh',
                  ].map((feature, i) => (
                    <tr
                      key={i}
                      className={cn(
                        'border-b border-gray-200 transition-colors hover:bg-gray-50/50 dark:border-gray-800 dark:hover:bg-gray-800/30',
                        i % 2 === 0 ? 'bg-gray-50/50 dark:bg-gray-800/20' : '',
                      )}
                    >
                      <td className="sticky left-0 z-10 bg-inherit px-6 py-5 font-medium">
                        {feature}
                      </td>
                      {billingConfig.products
                        .filter((p) => !p.hidden)
                        .map((product, productIndex) => {
                          // Simulate feature availability based on product tier
                          const isAvailable =
                            productIndex === 0
                              ? i < 4 // Free tier has basic features
                              : productIndex === 1
                                ? i < 6 // Standard tier has more features
                                : true; // Premium tier has all features

                          return (
                            <td
                              key={`cell-${product.id}-${productIndex}-${i}`}
                              className="px-6 py-5 text-center"
                            >
                              {isAvailable ? (
                                <div className="mx-auto flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-green-100 to-green-50 shadow-sm transition-all duration-300 hover:scale-110 hover:shadow-md dark:from-green-900/30 dark:to-green-800/20">
                                  <Check className="h-5 w-5 text-green-600 dark:text-green-400" />
                                </div>
                              ) : (
                                <div className="mx-auto flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-gray-100 to-gray-50 shadow-sm transition-all duration-300 hover:shadow-md dark:from-gray-800 dark:to-gray-900">
                                  <span className="block h-1.5 w-1.5 rounded-full bg-gray-400 dark:bg-gray-600"></span>
                                </div>
                              )}
                            </td>
                          );
                        })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className="px-4 py-2 text-center text-xs text-gray-500 md:hidden dark:text-gray-400">
            <span>Vuốt sang phải để xem đầy đủ bảng so sánh</span>
          </div>
        </div>
      </div>

      {/* FAQ section - Enhanced with modern design */}
      <div className="mt-24">
        <div className="mb-12 text-center">
          <Badge
            variant="outline"
            className="border-primary/20 bg-primary/5 text-primary animate-shimmer mb-4 px-4 py-1"
          >
            <Zap className="mr-1 h-3.5 w-3.5" />
            <span>Giải đáp thắc mắc</span>
          </Badge>
          <h3 className="from-primary bg-gradient-to-r to-purple-600 bg-clip-text text-3xl font-bold text-transparent">
            Câu hỏi thường gặp về giá
          </h3>
          <p className="text-muted-foreground mx-auto mt-3 max-w-2xl">
            Những thông tin quan trọng về các gói dịch vụ của chúng tôi
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {[
            {
              question:
                'Tôi có thể nâng cấp hoặc hạ cấp gói bất kỳ lúc nào không?',
              answer:
                'Có, bạn có thể dễ dàng thay đổi gói dịch vụ bất kỳ lúc nào. Khi nâng cấp, bạn sẽ chỉ phải trả thêm phần chênh lệch cho thời gian còn lại của chu kỳ thanh toán.',
            },
            {
              question: 'Có phí thiết lập ban đầu không?',
              answer:
                'Không, chúng tôi không thu phí thiết lập ban đầu cho bất kỳ gói dịch vụ nào. Bạn chỉ trả tiền cho gói dịch vụ bạn chọn.',
            },
            {
              question: 'Tôi có thể hủy gói dịch vụ bất kỳ lúc nào không?',
              answer:
                'Có, bạn có thể hủy gói dịch vụ bất kỳ lúc nào. Nếu bạn hủy, bạn vẫn có thể sử dụng dịch vụ cho đến hết chu kỳ thanh toán hiện tại.',
            },
            {
              question: 'Có gói dùng thử miễn phí không?',
              answer:
                'Có, chúng tôi cung cấp gói miễn phí với các tính năng cơ bản để bạn có thể dùng thử trước khi quyết định nâng cấp lên các gói cao cấp hơn.',
            },
          ].map((faq, i) => (
            <div
              key={i}
              className="hover:border-primary/20 dark:hover:border-primary/30 rounded-xl border border-gray-200 bg-white/90 p-8 shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl dark:border-gray-800 dark:bg-gray-900/90"
            >
              <h4 className="mb-4 text-xl font-bold">{faq.question}</h4>
              <p className="text-muted-foreground">{faq.answer}</p>
            </div>
          ))}
        </div>
      </div>

      {/* CTA - Enhanced with modern design */}
      <div className="mt-24 text-center">
        <div className="from-primary/20 animate-shimmer inline-flex items-center rounded-full bg-gradient-to-r to-purple-500/20 px-5 py-2.5 backdrop-blur-sm">
          <Sparkles className="text-primary mr-2 h-4 w-4" />
          <span className="text-primary text-sm font-medium">
            Bắt đầu miễn phí, không cần thẻ tín dụng
          </span>
        </div>

        <h3 className="from-primary mt-6 bg-gradient-to-r to-purple-600 bg-clip-text text-3xl font-bold text-transparent">
          Sẵn sàng để nâng cao chất lượng quản lý trường học?
        </h3>
        <p className="text-muted-foreground mx-auto mt-4 max-w-2xl text-lg">
          Chọn gói phù hợp với quy mô trường học và bắt đầu trải nghiệm ngay
          hôm nay.
        </p>

        <div className="mt-10 flex flex-col items-center justify-center gap-4 sm:flex-row">
          <a
            href={pathsConfig.auth.signUp}
            className="group from-primary focus:ring-primary relative inline-flex items-center justify-center overflow-hidden rounded-full bg-gradient-to-r to-purple-600 p-0.5 text-lg font-bold text-white transition-all duration-300 hover:scale-105 focus:ring-2 focus:ring-offset-2 focus:outline-none dark:focus:ring-offset-gray-900"
          >
            <span className="from-primary relative flex items-center space-x-2 rounded-full bg-gradient-to-r to-purple-600 px-8 py-4">
              <span>Bắt đầu miễn phí</span>
              <ArrowRight className="h-5 w-5 transition-transform group-hover:translate-x-1" />
            </span>
          </a>

          <a
            href="/contact"
            className="group relative inline-flex items-center justify-center overflow-hidden rounded-full bg-white p-0.5 text-lg font-bold transition-all duration-300 hover:scale-105 focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:outline-none dark:bg-gray-800 dark:focus:ring-offset-gray-900"
          >
            <span className="text-primary relative flex items-center space-x-2 rounded-full bg-white px-8 py-4 dark:bg-gray-800 dark:text-white">
              <span>Liên hệ tư vấn</span>
            </span>
          </a>
        </div>
      </div>
    </div>
  );
}
