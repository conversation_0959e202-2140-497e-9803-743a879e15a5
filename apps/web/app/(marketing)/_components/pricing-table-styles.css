/* Custom styling for pricing table */
.pricing-table-wrapper {
  --card-gap: 1.5rem;
  --card-padding: 1.5rem;
  --card-radius: 1rem;
  --card-shadow: 0 4px 20px -5px rgba(0, 0, 0, 0.1);
  --card-shadow-hover: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  --card-border-width: 2px;
  --card-transition: all 0.3s ease-in-out;
}

/* Improve spacing between pricing cards */
.pricing-table-wrapper > div > div {
  gap: 1.5rem !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Style pricing cards */
.pricing-table-wrapper > div > div > div {
  padding: 1.75rem !important;
  transition: all 0.3s ease-in-out;
  border-radius: 1.25rem !important;
  box-shadow: 0 4px 20px -5px rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(229, 231, 235, 0.5) !important;
  margin-top: 1.5rem !important;
}

/* Hover effect for pricing cards */
.pricing-table-wrapper > div > div > div:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1) !important;
}

/* Highlighted card */
.pricing-table-wrapper > div > div > div[class*="border-primary"] {
  border-width: 2px !important;
  box-shadow: 0 8px 30px -5px rgba(0, 0, 0, 0.1) !important;
  z-index: 1;
  position: relative;
}

/* Improve spacing in pricing cards */
.pricing-table-wrapper > div > div > div > div {
  gap: 1.25rem !important;
}

/* Improve pricing display */
.pricing-table-wrapper .text-4xl {
  font-weight: 700 !important;
  background: linear-gradient(to right, var(--primary), #9333ea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

/* Improve badge display */
.pricing-table-wrapper .absolute.-top-2\.5 {
  top: -1rem !important;
}

/* Improve button display */
.pricing-table-wrapper button[class*="rounded-lg"] {
  border-radius: 9999px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.3s ease-in-out !important;
}

.pricing-table-wrapper button[class*="rounded-lg"]:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}
