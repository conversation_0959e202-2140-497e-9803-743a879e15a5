import { useCallback } from 'react';
import { formatCurrency, parseCurrency } from '../lib/currency';
import currencyConfig, { CurrencyCode } from '../config/currency.config';

/**
 * Hook for currency formatting and parsing
 * @param currencyCode - Optional currency code to override the default
 */
export function useCurrency(currencyCode?: CurrencyCode) {
  const code = currencyCode || currencyConfig.defaultCurrency;
  
  // Format a number as currency
  const format = useCallback(
    (amount: number) => formatCurrency(amount, code),
    [code]
  );
  
  // Parse a currency string to a number
  const parse = useCallback(
    (currencyString: string) => parseCurrency(currencyString, code),
    [code]
  );
  
  // Get currency symbol
  const getSymbol = useCallback(
    () => currencyConfig.currencyFormats[code]?.symbol || '',
    [code]
  );
  
  // Get currency position (prefix or suffix)
  const getPosition = useCallback(
    () => currencyConfig.currencyFormats[code]?.position || 'suffix',
    [code]
  );
  
  return {
    format,
    parse,
    getSymbol,
    getPosition,
    currencyCode: code,
  };
}
