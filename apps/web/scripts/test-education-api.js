#!/usr/bin/env node

/**
 * Test script for Education API endpoints
 * Usage: node scripts/test-education-api.js
 */

const BASE_URL = 'http://localhost:3000';

// Mock authentication token (you'll need to get a real one)
const AUTH_TOKEN = 'your-auth-token-here';

async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const config = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      ...options.headers,
    },
    ...options,
  };

  try {
    console.log(`\n🔄 ${config.method || 'GET'} ${endpoint}`);
    const response = await fetch(url, config);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Success:', data);
    } else {
      console.log('❌ Error:', data);
    }
    
    return { response, data };
  } catch (error) {
    console.log('💥 Request failed:', error.message);
    return { error };
  }
}

async function testEducationAPI() {
  console.log('🧪 Testing Education API Endpoints');
  console.log('=====================================');

  // Test 1: Get organization (should return null for new account)
  await makeRequest('/api/education/accounts?account_id=test-account-id');

  // Test 2: Create organization
  const orgData = {
    account_id: 'test-account-id',
    name: 'Mầm Non Test',
    organization_type: 'kindergarten',
    address: '123 Test Street',
    phone: '**********',
    email: '<EMAIL>',
    director_name: 'Cô Test',
    license_number: 'TEST123',
    business_hours: {
      monday: { start: '07:00', end: '17:00' },
      tuesday: { start: '07:00', end: '17:00' },
    },
    settings: {
      max_class_size: 20,
      lunch_provided: true,
    },
  };

  await makeRequest('/api/education/organizations', {
    method: 'POST',
    body: JSON.stringify(orgData),
  });

  // Test 3: Initialize education platform
  const initData = {
    account_id: 'test-account-id',
    education_type: 'kindergarten',
    custom_config: {
      school_name: 'Mầm Non Test Initialize',
      director_name: 'Cô Test Initialize',
    },
  };

  await makeRequest('/api/education/initialize', {
    method: 'POST',
    body: JSON.stringify(initData),
  });

  // Test 4: Get learners (should return empty array)
  await makeRequest('/api/education/learners?account_id=test-org-id');

  // Test 5: Guardian APIs
  await makeRequest('/api/education/guardian/children?account_id=test-org-id');

  await makeRequest('/api/education/guardian/attendance?learner_id=test-learner-id&account_id=test-org-id&month=2024-01');

  await makeRequest('/api/education/guardian/reports?learner_id=test-learner-id&account_id=test-org-id&limit=10');

  await makeRequest('/api/education/guardian/fees?account_id=test-org-id&status=pending');

  // Test 6: Instructor APIs
  await makeRequest('/api/education/instructor/classes?account_id=test-org-id');

  console.log('\n🎉 Enhanced API testing completed!');
  console.log('Note: Most requests will fail without proper authentication.');
  console.log('This script is for testing API structure and error handling.');
  console.log('\n📋 Available Endpoints:');
  console.log('- Accounts: GET, POST, PUT, DELETE /api/education/organizations');
  console.log('- Learners: GET, POST, PUT, DELETE /api/education/learners');
  console.log('- Initialize: POST /api/education/initialize');
  console.log('- Guardian Children: GET, POST /api/education/guardian/children');
  console.log('- Guardian Attendance: GET, POST /api/education/guardian/attendance');
  console.log('- Guardian Reports: GET, POST, PUT /api/education/guardian/reports');
  console.log('- Guardian Fees: GET, POST /api/education/guardian/fees');
  console.log('- Instructor Classes: GET, POST /api/education/instructor/classes');
}

// Check if we have fetch available (Node 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with fetch support');
  console.log('Or install node-fetch: npm install node-fetch');
  process.exit(1);
}

// Run tests
testEducationAPI().catch(console.error);
