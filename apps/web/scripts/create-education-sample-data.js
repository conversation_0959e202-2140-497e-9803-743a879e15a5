#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create education sample data using the migration function
 * This script calls the create_education_sample_data function from the database
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function createEducationSampleData() {
  try {
    console.log('🌱 Starting education sample data creation...');

    // 1. Find the makerkit account
    const { data: account, error: accountError } = await supabase
      .from('accounts')
      .select('id, slug')
      .eq('slug', 'makerkit')
      .single();

    if (accountError || !account) {
      console.error('❌ Failed to find makerkit account:', accountError);
      return;
    }

    console.log('✅ Found makerkit account:', account.id);

    // 2. Call the migration function to create sample data
    console.log('📊 Creating education sample data using migration function...');
    const { data: result, error: functionError } = await supabase
      .rpc('create_education_sample_data', {
        p_account_id: account.id
      });

    if (functionError) {
      console.error('❌ Failed to create sample data:', functionError);
      return;
    }

    console.log('✅ Sample data created successfully!');
    
    if (result && result.stats) {
      console.log('📊 Summary:');
      const stats = result.stats;
      Object.keys(stats).forEach(key => {
        console.log(`   - ${key}: ${stats[key]}`);
      });
    }
    
    console.log('🔗 Visit: http://localhost:3000/home/<USER>/education');

  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Run the script
createEducationSampleData();
