#!/usr/bin/env node

/**
 * Seed Education Data Script
 * Usage: node scripts/seed-education-data.js
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.development' });

// Check if running in Node.js 18+ with fetch support
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with fetch support');
  process.exit(1);
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Education sample data
const educationData = {
  // Organization data
  organization: {
    name: '<PERSON><PERSON><PERSON>',
    organization_type: 'kindergarten',
    address: '123 Đường Hoa Mai, Quận 1, TP.HCM',
    phone: '028-1234-5678',
    email: '<EMAIL>',
    director_name: '<PERSON>uyễn Thị Lan',
    license_number: 'MN-001-2015',
    logo_url: '/images/education/hoamai-logo.png',
    business_hours: {
      monday: { open: '07:00', close: '17:00' },
      tuesday: { open: '07:00', close: '17:00' },
      wednesday: { open: '07:00', close: '17:00' },
      thursday: { open: '07:00', close: '17:00' },
      friday: { open: '07:00', close: '17:00' },
      saturday: { open: '07:00', close: '12:00' },
      sunday: { open: null, close: null },
    },
    settings: {
      academic_year: '2024-2025',
      semester: 'Học kỳ 1',
      timezone: 'Asia/Ho_Chi_Minh',
      currency: 'VND',
      language: 'vi',
      established_year: 2015,
      capacity: 200,
    },
  },

  // Programs
  programs: [
    {
      name: 'Lớp Mầm (2-3 tuổi)',
      program_type: 'early_childhood',
      age_range: '2-3 tuổi',
      duration_months: 12,
      capacity: 15,
      schedule: {
        days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        start_time: '07:30',
        end_time: '16:30',
      },
      fee_structure: {
        tuition: 3500000,
        meal: 800000,
        material: 200000,
        currency: 'VND',
        billing_period: 'monthly'
      },
      description: 'Chương trình giáo dục cho trẻ 2-3 tuổi với phương pháp Montessori',
      location: 'Phòng A1',
      status: 'active',
    },
    {
      name: 'Lớp Chồi (3-4 tuổi)',
      program_type: 'preschool',
      age_range: '3-4 tuổi',
      duration_months: 12,
      capacity: 18,
      schedule: {
        days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        start_time: '07:30',
        end_time: '16:30',
      },
      fee_structure: {
        tuition: 4000000,
        meal: 900000,
        material: 250000,
        currency: 'VND',
        billing_period: 'monthly'
      },
      description: 'Chương trình phát triển toàn diện cho trẻ 3-4 tuổi',
      location: 'Phòng B1',
      status: 'active',
    },
  ],

  // Learners
  learners: [
    {
      learner_code: `HM${Date.now()}001`,
      full_name: 'Nguyễn Minh An',
      nickname: 'Bé An',
      date_of_birth: '2021-03-15',
      gender: 'male',
      address: '456 Đường Lê Lợi, Quận 1, TP.HCM',
      enrollment_date: '2024-09-01',
      status: 'active',
      health_info: {
        allergies: [],
        medical_conditions: [],
        emergency_contact: {
          name: 'Nguyễn Văn Bình',
          relationship: 'father',
          phone: '**********',
        },
      },
    },
    {
      learner_code: `HM${Date.now()}002`,
      full_name: 'Trần Thị Bảo',
      nickname: 'Bé Bảo',
      date_of_birth: '2020-07-22',
      gender: 'female',
      address: '789 Đường Nguyễn Huệ, Quận 1, TP.HCM',
      enrollment_date: '2024-09-01',
      status: 'active',
      health_info: {
        allergies: ['peanuts'],
        medical_conditions: [],
        emergency_contact: {
          name: 'Trần Thị Cúc',
          relationship: 'mother',
          phone: '**********',
        },
      },
    },
  ],

  // Events
  events: [
    {
      title: 'Họp phụ huynh đầu năm học',
      description: 'Họp phụ huynh để thông báo kế hoạch năm học mới 2024-2025',
      event_type: 'meeting',
      start_datetime: '2024-12-20T19:00:00+07:00',
      end_datetime: '2024-12-20T21:00:00+07:00',
      location: 'Hội trường trường',
      target_audience: 'guardians',
      registration_required: true,
      max_participants: 100,
      current_participants: 45,
      fee: 0,
    },
    {
      title: 'Biểu diễn Giáng sinh',
      description: 'Chương trình biểu diễn Giáng sinh của các bé',
      event_type: 'performance',
      start_datetime: '2024-12-24T09:00:00+07:00',
      end_datetime: '2024-12-24T11:00:00+07:00',
      location: 'Sân trường',
      target_audience: 'all',
      registration_required: false,
      max_participants: null,
      current_participants: 0,
      fee: 0,
    },
  ],
};

async function seedEducationData() {
  console.log('🌱 Starting education data seeding...');

  try {
    // 1. Get the makerkit account
    const { data: account, error: accountError } = await supabase
      .from('accounts')
      .select('id')
      .eq('slug', 'makerkit')
      .single();

    if (accountError || !account) {
      console.error('❌ Could not find makerkit account:', accountError);
      return;
    }

    console.log('✅ Found makerkit account:', account.id);

    // 2. Update account with education settings (no need for separate organization table)
    const { error: updateError } = await supabase
      .from('accounts')
      .update({
        education_settings: {
          organization_type: 'preschool',
          license_number: 'MN-2024-001',
          capacity: 200,
          established_year: 2015,
          address: '123 Đường Nguyễn Văn Cừ, Quận 5, TP.HCM',
          phone: '028-3838-1234',
          website: 'https://truonghoamai.edu.vn',
          description: 'Trường mầm non uy tín với phương pháp giáo dục hiện đại'
        }
      })
      .eq('id', account.id);

    if (updateError) {
      console.error('❌ Failed to update account with education settings:', updateError);
      return;
    }

    console.log('✅ Updated account with education settings');

    // 3. Create programs
    const samplePrograms = [
      {
        account_id: account.id,
        name: 'Lớp Mầm (2-3 tuổi)',
        description: 'Chương trình giáo dục cho trẻ từ 2-3 tuổi',
        program_type: 'nursery',
        capacity: 20,
        schedule: {
          days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
          start_time: '07:30',
          end_time: '16:30'
        },
        status: 'active',
      },
      {
        account_id: account.id,
        name: 'Lớp Chồi (3-4 tuổi)',
        description: 'Chương trình giáo dục cho trẻ từ 3-4 tuổi',
        program_type: 'preschool',
        capacity: 25,
        schedule: {
          days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
          start_time: '07:30',
          end_time: '16:30'
        },
        status: 'active',
      },
    ];

    const createdPrograms = [];
    console.log('\n📚 Creating programs...');
    for (const program of samplePrograms) {
      const { data: createdProgram, error: programError } = await supabase
        .from('programs')
        .upsert(program)
        .select()
        .single();

      if (programError) {
        console.error('❌ Failed to create program:', program.name, programError);
      } else {
        console.log('✅ Created program:', program.name);
        createdPrograms.push(createdProgram);
      }
    }

    // 4. Create learners
    const sampleLearners = [
      {
        account_id: account.id,
        learner_code: 'HV001',
        full_name: 'Nguyễn Minh An',
        nickname: 'Bé An',
        date_of_birth: '2021-03-15',
        gender: 'male',
        address: '123 Đường ABC, Quận 1, TP.HCM',
        health_info: {
          allergies: ['Không có'],
          medical_conditions: []
        },
        emergency_contact: {
          name: 'Nguyễn Thị Lan',
          relationship: 'Mẹ',
          phone: '**********'
        },
        enrollment_date: '2024-09-01',
        status: 'active',
      },
      {
        account_id: account.id,
        learner_code: 'HV002',
        full_name: 'Trần Thị Bảo',
        nickname: 'Bé Bảo',
        date_of_birth: '2020-08-22',
        gender: 'female',
        address: '456 Đường XYZ, Quận 3, TP.HCM',
        health_info: {
          allergies: ['Dị ứng đậu phộng'],
          medical_conditions: []
        },
        emergency_contact: {
          name: 'Trần Văn Nam',
          relationship: 'Bố',
          phone: '**********'
        },
        enrollment_date: '2024-09-01',
        status: 'active',
      },
    ];

    const createdLearners = [];
    console.log('\n👶 Creating learners...');
    for (const learner of sampleLearners) {
      const { data: createdLearner, error: learnerError } = await supabase
        .from('learners')
        .upsert(learner)
        .select()
        .single();

      if (learnerError) {
        console.error('❌ Failed to create learner:', learner.full_name, learnerError);
      } else {
        console.log('✅ Created learner:', learner.full_name);
        createdLearners.push(createdLearner);
      }
    }

    // 5. Create events
    const sampleEvents = [
      {
        account_id: account.id,
        title: 'Họp phụ huynh đầu năm học',
        description: 'Buổi họp gặp gỡ phụ huynh và giới thiệu chương trình học năm học mới',
        event_type: 'meeting',
        start_datetime: '2024-12-15T19:00:00+07:00',
        end_datetime: '2024-12-15T21:00:00+07:00',
        location: 'Phòng họp chính',
        max_participants: 100,
        registration_required: true,
      },
      {
        account_id: account.id,
        title: 'Biểu diễn Giáng sinh',
        description: 'Chương trình biểu diễn văn nghệ chào mừng Giáng sinh',
        event_type: 'performance',
        start_datetime: '2024-12-24T15:00:00+07:00',
        end_datetime: '2024-12-24T17:00:00+07:00',
        location: 'Sân khấu trường',
        max_participants: 200,
        registration_required: false,
      },
    ];

    console.log('\n🎉 Creating events...');
    for (const event of sampleEvents) {
      const { error: eventError } = await supabase
        .from('events')
        .upsert(event);

      if (eventError) {
        console.error('❌ Failed to create event:', event.title, eventError);
      } else {
        console.log('✅ Created event:', event.title);
      }
    }

    // 6. Create sample fees
    const sampleFees = [];
    if (createdLearners.length > 0) {
      sampleFees.push(
        {
          learner_id: createdLearners[0].id,
          account_id: account.id,
          fee_category: 'tuition',
          amount: 4000000,
          currency: 'VND',
          billing_period: 'Tháng 12/2024',
          due_date: '2024-12-05',
          description: 'Học phí tháng 12/2024 - Lớp Chồi',
          status: 'pending',
        },
        {
          learner_id: createdLearners[0].id,
          account_id: account.id,
          fee_category: 'meal',
          amount: 800000,
          currency: 'VND',
          billing_period: 'Tháng 12/2024',
          due_date: '2024-12-05',
          description: 'Tiền ăn tháng 12/2024',
          status: 'pending',
        }
      );
    }

    for (const fee of sampleFees) {
      const { error: feeError } = await supabase
        .from('fees')
        .upsert(fee);

      if (feeError) {
        console.error('❌ Failed to create fee:', fee.description, feeError);
      } else {
        console.log('✅ Created fee:', fee.description);
      }
    }

    // 7. Create enrollments
    const enrollments = [];
    if (createdLearners.length >= 2 && createdPrograms.length >= 2) {
      enrollments.push(
        {
          learner_id: createdLearners[0].id,
          program_id: createdPrograms[1].id, // Lớp Chồi
          enrollment_date: '2024-09-01',
          status: 'active',
        },
        {
          learner_id: createdLearners[1].id,
          program_id: createdPrograms[0].id, // Lớp Mầm
          enrollment_date: '2024-09-01',
          status: 'active',
        }
      );

      for (const enrollment of enrollments) {
        const { error: enrollmentError } = await supabase
          .from('enrollments')
          .upsert(enrollment);

        if (enrollmentError) {
          console.error('❌ Failed to create enrollment:', enrollmentError);
        } else {
          console.log('✅ Created enrollment for learner');
        }
      }
    }

    // 8. Create sample attendance
    const sampleAttendance = [];
    if (createdLearners.length >= 2 && createdPrograms.length >= 2) {
      sampleAttendance.push(
        {
          learner_id: createdLearners[0].id,
          program_id: createdPrograms[1].id, // Lớp Chồi
          session_date: '2024-12-01',
          session_time: 'full_day',
          status: 'present',
          check_in_time: '2024-12-01T07:45:00+07:00',
          check_out_time: '2024-12-01T16:20:00+07:00',
          notes: 'Bé vui vẻ và tham gia tích cực',
          guardian_notified: false,
        },
        {
          learner_id: createdLearners[1].id,
          program_id: createdPrograms[0].id, // Lớp Mầm
          session_date: '2024-12-01',
          session_time: 'full_day',
          status: 'late',
          check_in_time: '2024-12-01T08:15:00+07:00',
          check_out_time: '2024-12-01T16:30:00+07:00',
          notes: 'Đến muộn do kẹt xe',
          guardian_notified: false,
        }
      );
    }

    for (const attendance of sampleAttendance) {
      const { error: attendanceError } = await supabase
        .from('attendance')
        .upsert(attendance);

      if (attendanceError) {
        console.error('❌ Failed to create attendance:', attendanceError);
      } else {
        console.log('✅ Created attendance record');
      }
    }

    // 9. Create sample curriculum
    const sampleCurriculum = [
      {
        account_id: account.id,
        title: 'Chương trình Toán học Mầm non',
        description: 'Chương trình toán học cơ bản cho trẻ mầm non',
        subject: 'Toán học',
        age_group: 'Mầm non',
        duration_minutes: 30,
        objectives: ['Nhận biết số từ 1-10', 'Phân biệt hình dạng cơ bản', 'So sánh nhiều ít'],
        assessment_criteria: ['Quan sát', 'Thực hành', 'Trò chơi'],
        status: 'active',
      },
      {
        account_id: account.id,
        title: 'Chương trình Ngôn ngữ',
        description: 'Phát triển kỹ năng ngôn ngữ cho trẻ',
        subject: 'Ngôn ngữ',
        age_group: 'Mầm non',
        duration_minutes: 45,
        objectives: ['Phát âm rõ ràng', 'Từ vựng cơ bản', 'Kể chuyện đơn giản'],
        assessment_criteria: ['Trò chuyện', 'Kể chuyện', 'Hát'],
        status: 'active',
      },
    ];

    console.log('\n📚 Creating curriculum...');
    for (const curriculum of sampleCurriculum) {
      const { error: curriculumError } = await supabase
        .from('curriculum')
        .upsert(curriculum);

      if (curriculumError) {
        console.error('❌ Failed to create curriculum:', curriculumError);
      } else {
        console.log('✅ Created curriculum:', curriculum.title);
      }
    }

    // 10. Create sample health records
    const sampleHealthRecords = [];
    if (createdLearners.length > 0) {
      sampleHealthRecords.push({
        account_id: account.id,
        learner_id: createdLearners[0].id,
        record_type: 'checkup',
        title: 'Khám sức khỏe định kỳ',
        description: 'Khám sức khỏe tổng quát cho bé',
        record_date: '2024-11-15',
        medications: [],
        allergies: ['Không có dị ứng'],
        notes: 'Sức khỏe tốt, phát triển bình thường',
        is_emergency: false,
        guardian_notified: true,
        status: 'active',
      });
    }

    console.log('\n🏥 Creating health records...');
    for (const healthRecord of sampleHealthRecords) {
      const { error: healthError } = await supabase
        .from('health_records')
        .upsert(healthRecord);

      if (healthError) {
        console.error('❌ Failed to create health record:', healthError);
      } else {
        console.log('✅ Created health record');
      }
    }

    // 11. Create sample transportation
    const sampleVehicles = [
      {
        account_id: account.id,
        vehicle_type: 'bus',
        vehicle_number: 'XE01',
        brand: 'Hyundai',
        model: 'County',
        year: 2020,
        capacity: 25,
        license_plate: '51B-12345',
        driver_name: 'Nguyễn Văn Tài',
        driver_phone: '**********',
        driver_license: 'D123456789',
        insurance_info: {
          provider: 'Bảo Việt',
          policy_number: '*********',
          expiry_date: '2025-12-31'
        },
        status: 'active',
      },
      {
        account_id: account.id,
        vehicle_type: 'van',
        vehicle_number: 'XE02',
        brand: 'Ford',
        model: 'Transit',
        year: 2019,
        capacity: 12,
        license_plate: '51B-67890',
        driver_name: 'Trần Thị Hoa',
        driver_phone: '**********',
        driver_license: 'D987654321',
        insurance_info: {
          provider: 'Bảo Minh',
          policy_number: '*********',
          expiry_date: '2025-06-30'
        },
        status: 'active',
      },
    ];

    console.log('\n🚌 Creating transportation...');
    for (const vehicle of sampleVehicles) {
      const { error: vehicleError } = await supabase
        .from('vehicles')
        .upsert(vehicle);

      if (vehicleError) {
        console.error('❌ Failed to create vehicle:', vehicleError);
      } else {
        console.log('✅ Created vehicle:', vehicle.vehicle_number);
      }
    }

    // 12. Create sample meal plans
    const sampleMealPlans = [
      {
        account_id: account.id,
        date: new Date().toISOString().split('T')[0],
        meal_type: 'lunch',
        menu_items: [
          {
            name: 'Cơm gà xối mỡ',
            ingredients: ['Gạo', 'Thịt gà', 'Rau cải', 'Canh chua'],
            nutritional_info: {
              calories: 450,
              protein: '25g',
              carbs: '60g',
              fat: '12g'
            }
          }
        ],
        special_notes: 'Món ăn phù hợp cho trẻ từ 2-5 tuổi',
        dietary_accommodations: ['Không có'],
        cost_per_serving: 35000,
      },
      {
        account_id: account.id,
        date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        meal_type: 'lunch',
        menu_items: [
          {
            name: 'Phở bò',
            ingredients: ['Bánh phở', 'Thịt bò', 'Hành lá', 'Rau thơm'],
            nutritional_info: {
              calories: 380,
              protein: '22g',
              carbs: '55g',
              fat: '8g'
            }
          }
        ],
        special_notes: 'Có chứa gluten',
        dietary_accommodations: ['Gluten'],
        cost_per_serving: 40000,
      },
    ];

    console.log('\n🍽️ Creating meal plans...');
    for (const mealPlan of sampleMealPlans) {
      const { error: mealError } = await supabase
        .from('meal_plans')
        .upsert(mealPlan);

      if (mealError) {
        console.error('❌ Failed to create meal plan:', mealError);
      } else {
        console.log('✅ Created meal plan for:', mealPlan.date);
      }
    }

    // 13. Create sample library items
    const sampleLibraryItems = [
      {
        account_id: account.id,
        title: 'Video: Bài hát ABC',
        item_type: 'video',
        description: 'Video học bảng chữ cái tiếng Anh cho trẻ mầm non',
        file_url: 'https://example.com/abc-song.mp4',
        thumbnail_url: 'https://example.com/abc-thumb.jpg',
        tags: ['alphabet', 'english', 'song'],
        category: 'educational',
        target_audience: 'students',
        age_group: 'Mầm non',
        language: 'vietnamese',
        duration: 180,
        format: 'mp4',
        status: 'active',
      },
      {
        account_id: account.id,
        title: 'Hình ảnh: Động vật rừng',
        item_type: 'image',
        description: 'Bộ sưu tập hình ảnh các loài động vật rừng',
        file_url: 'https://example.com/animals.jpg',
        thumbnail_url: 'https://example.com/animals-thumb.jpg',
        tags: ['animals', 'nature', 'learning'],
        category: 'educational',
        target_audience: 'all',
        age_group: 'Tất cả',
        language: 'vietnamese',
        format: 'jpg',
        status: 'active',
      },
    ];

    console.log('\n📚 Creating library items...');
    for (const libraryItem of sampleLibraryItems) {
      const { error: libraryError } = await supabase
        .from('library_items')
        .upsert(libraryItem);

      if (libraryError) {
        console.error('❌ Failed to create library item:', libraryError);
      } else {
        console.log('✅ Created library item:', libraryItem.title);
      }
    }

    console.log('\n🎉 Education data seeding completed successfully!');
    console.log('📊 Summary:');
    console.log(`   - Programs: ${createdPrograms.length}`);
    console.log(`   - Learners: ${createdLearners.length}`);
    console.log(`   - Events: ${sampleEvents.length}`);
    console.log(`   - Curriculum: ${sampleCurriculum.length}`);
    console.log(`   - Health Records: ${sampleHealthRecords.length}`);
    console.log(`   - Vehicles: ${sampleVehicles.length}`);
    console.log(`   - Meal Plans: ${sampleMealPlans.length}`);
    console.log(`   - Library Items: ${sampleLibraryItems.length}`);
    console.log('🔗 Visit: http://localhost:3000/home/<USER>/education');

  } catch (error) {
    console.error('💥 Seeding failed:', error);
  }
}

// Run the seeder
seedEducationData().catch(console.error);
