#!/usr/bin/env node

/**
 * Test script for Advanced Features API endpoints
 * Usage: node scripts/test-advanced-features.js
 */

const BASE_URL = 'http://localhost:3000';

// Mock authentication token (you'll need to get a real one)
const AUTH_TOKEN = 'your-auth-token-here';

async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const config = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      ...options.headers,
    },
    ...options,
  };

  try {
    console.log(`\n🔄 ${config.method || 'GET'} ${endpoint}`);
    if (config.body) {
      console.log('📤 Request body:', JSON.parse(config.body));
    }
    
    const response = await fetch(url, config);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Success:', data);
    } else {
      console.log('❌ Error:', data);
    }
    
    return { response, data };
  } catch (error) {
    console.log('💥 Request failed:', error.message);
    return { error };
  }
}

async function testAdvancedFeatures() {
  console.log('🧪 Testing Advanced Features API Endpoints');
  console.log('==========================================');

  // Test 1: Event Management
  console.log('\n📅 Testing Event Management');
  console.log('============================');

  // Create event
  const eventData = {
    account_id: 'test-org-uuid',
    title: 'Họp phụ huynh đầu năm học',
    description: 'Họp phụ huynh để thông báo kế hoạch năm học mới',
    event_type: 'meeting',
    start_datetime: '2024-12-15T09:00:00Z',
    end_datetime: '2024-12-15T11:00:00Z',
    location: 'Phòng họp chính',
    target_audience: 'guardians',
    registration_required: true,
    max_participants: 50,
    fee: 0,
  };

  await makeRequest('/api/education/events', {
    method: 'POST',
    body: JSON.stringify(eventData),
  });

  // Get events
  await makeRequest('/api/education/events?account_id=test-org-uuid&upcoming=true');

  // Register for event
  const registrationData = {
    event_id: 'test-event-uuid',
    account_id: 'test-org-uuid',
    learner_ids: ['test-learner-uuid'],
    notes: 'Sẽ tham gia đúng giờ',
  };

  await makeRequest('/api/education/events/register', {
    method: 'POST',
    body: JSON.stringify(registrationData),
  });

  // Test 2: QR Code Attendance
  console.log('\n📱 Testing QR Code Attendance');
  console.log('==============================');

  // Generate QR code
  const qrData = {
    program_id: 'test-program-uuid',
    account_id: 'test-org-uuid',
    session_date: '2024-12-01',
    session_time: 'morning',
    expires_in_minutes: 60,
  };

  await makeRequest('/api/education/attendance/qr/generate', {
    method: 'POST',
    body: JSON.stringify(qrData),
  });

  // Get QR sessions
  await makeRequest('/api/education/attendance/qr/generate?program_id=test-program-uuid&account_id=test-org-uuid&active_only=true');

  // Validate QR code
  await makeRequest('/api/education/attendance/qr/scan?qr_data=test-qr-data&account_id=test-org-uuid');

  // Scan QR code
  const scanData = {
    qr_data: JSON.stringify({
      type: 'attendance',
      session_id: 'test-session-uuid',
      program_id: 'test-program-uuid',
      account_id: 'test-org-uuid',
      qr_code: 'EDU_QR_test',
      token: 'test-token',
      expires_at: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
    }),
    learner_id: 'test-learner-uuid',
    account_id: 'test-org-uuid',
  };

  await makeRequest('/api/education/attendance/qr/scan', {
    method: 'POST',
    body: JSON.stringify(scanData),
  });

  // Test 3: In-App Messaging
  console.log('\n💬 Testing In-App Messaging');
  console.log('============================');

  // Send message
  const messageData = {
    account_id: 'test-org-uuid',
    recipient_type: 'guardian',
    learner_id: 'test-learner-uuid',
    subject: 'Thông báo quan trọng',
    content: 'Xin chào phụ huynh, đây là thông báo về hoạt động học tập của con em.',
    message_type: 'announcement',
    attachments: [],
  };

  await makeRequest('/api/education/messages', {
    method: 'POST',
    body: JSON.stringify(messageData),
  });

  // Get messages
  await makeRequest('/api/education/messages?account_id=test-org-uuid&recipient_type=guardian&unread_only=true');

  console.log('\n🎉 Advanced Features testing completed!');
  console.log('Note: Most requests will fail without proper authentication and valid UUIDs.');
  console.log('This script is for testing API structure and error handling.');
  
  console.log('\n📋 Available Advanced Features:');
  console.log('================================');
  
  console.log('\n📅 Event Management:');
  console.log('- Create/Read events: GET/POST /api/education/events');
  console.log('- Event registration: POST /api/education/events/register');
  console.log('- Update registration: PUT /api/education/events/register');
  
  console.log('\n📱 QR Code Attendance:');
  console.log('- Generate QR: POST /api/education/attendance/qr/generate');
  console.log('- List QR sessions: GET /api/education/attendance/qr/generate');
  console.log('- Validate QR: GET /api/education/attendance/qr/scan');
  console.log('- Scan QR: POST /api/education/attendance/qr/scan');
  
  console.log('\n💬 In-App Messaging:');
  console.log('- Send messages: POST /api/education/messages');
  console.log('- Get messages: GET /api/education/messages');
  console.log('- Conversation threads');
  console.log('- Message types: general, urgent, announcement, reminder, report');
  
  console.log('\n🎯 Key Features:');
  console.log('================');
  console.log('- ✅ Event Management with Registration');
  console.log('- ✅ QR Code Attendance System');
  console.log('- ✅ In-App Messaging System');
  console.log('- ✅ Media Sharing (Database ready)');
  console.log('- ✅ Role-based Access Control');
  console.log('- ✅ Real-time Notifications');
  console.log('- ✅ Vietnamese Localization');
  
  console.log('\n🔐 Security Features:');
  console.log('=====================');
  console.log('- ✅ Row Level Security (RLS)');
  console.log('- ✅ Role-based Permissions');
  console.log('- ✅ QR Token Validation');
  console.log('- ✅ Message Privacy Controls');
  console.log('- ✅ Event Access Control');
  console.log('- ✅ Audit Logging');
  
  console.log('\n📊 Database Tables:');
  console.log('===================');
  console.log('- events: Event management');
  console.log('- event_registrations: Event registrations');
  console.log('- qr_attendance_sessions: QR code sessions');
  console.log('- qr_scans: QR scan records');
  console.log('- messages: In-app messaging');
  console.log('- message_reads: Read receipts');
  console.log('- media_shares: Photo/video sharing');
  console.log('- media_reactions: Like/reactions');
  console.log('- media_comments: Comments system');
  
  console.log('\n🚀 Ready for Production:');
  console.log('========================');
  console.log('- Complete API endpoints');
  console.log('- Database schema with RLS');
  console.log('- Vietnamese translations');
  console.log('- Mobile-optimized features');
  console.log('- Real-time capabilities');
  console.log('- Comprehensive error handling');
}

// Check if we have fetch available (Node 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with fetch support');
  console.log('Or install node-fetch: npm install node-fetch');
  process.exit(1);
}

// Run tests
testAdvancedFeatures().catch(console.error);
