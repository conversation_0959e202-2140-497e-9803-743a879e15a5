/**
 * Setup Education ZNS Integration
 * Script để setup templates, mappings và jobs cho education ZNS
 */

import { createClient } from '@kit/supabase/server-client';
import { 
  EDUCATION_ZNS_TEMPLATES,
  EDUCATION_EVENT_MAPPINGS 
} from '../app/home/<USER>/education/_lib/education-zns-events';

async function setupEducationZns() {
  console.log('🚀 Setting up Education ZNS Integration...');
  
  const supabase = createClient();

  try {
    // 1. Setup ZNS Templates
    console.log('📝 Setting up ZNS templates...');
    
    for (const [templateKey, template] of Object.entries(EDUCATION_ZNS_TEMPLATES)) {
      const { error } = await supabase
        .from('zns_templates')
        .upsert({
          template_key: templateKey,
          name: template.name,
          content: template.content,
          variables: template.variables,
          category: template.category || 'education',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'template_key'
        });

      if (error) {
        console.error(`❌ Failed to create template ${templateKey}:`, error);
      } else {
        console.log(`✅ Template created: ${templateKey}`);
      }
    }

    // 2. Setup Event Mappings
    console.log('🔗 Setting up event mappings...');
    
    for (const mapping of EDUCATION_EVENT_MAPPINGS) {
      const { error } = await supabase
        .from('zns_event_mappings')
        .upsert({
          module: 'education',
          event_type: mapping.event_type,
          template_key: mapping.template_key,
          conditions: mapping.conditions,
          enabled: mapping.enabled,
          priority: mapping.priority,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'module,event_type'
        });

      if (error) {
        console.error(`❌ Failed to create mapping ${mapping.event_type}:`, error);
      } else {
        console.log(`✅ Mapping created: ${mapping.event_type} -> ${mapping.template_key}`);
      }
    }

    // 3. Setup Education-specific ZNS Settings
    console.log('⚙️ Setting up ZNS settings...');
    
    const educationSettings = {
      module: 'education',
      settings: {
        // Rate limiting
        rate_limits: {
          per_organization_per_minute: 50,
          per_organization_per_hour: 1000,
          per_organization_per_day: 5000
        },
        
        // Retry configuration
        retry_config: {
          max_retries: 3,
          retry_delay: 5000,
          exponential_backoff: true
        },
        
        // Notification preferences
        notification_preferences: {
          emergency_immediate: true,
          batch_non_urgent: true,
          respect_quiet_hours: true,
          quiet_hours: {
            start: '22:00',
            end: '07:00'
          }
        },
        
        // Cost optimization
        cost_optimization: {
          batch_similar_messages: true,
          avoid_peak_hours: true,
          deduplicate_messages: true,
          smart_scheduling: true
        }
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error: settingsError } = await supabase
      .from('zns_module_settings')
      .upsert(educationSettings, {
        onConflict: 'module'
      });

    if (settingsError) {
      console.error('❌ Failed to create education ZNS settings:', settingsError);
    } else {
      console.log('✅ Education ZNS settings created');
    }

    // 4. Setup Shared ZNS Pools for Education
    console.log('🏊 Setting up shared ZNS pools...');
    
    const sharedPools = [
      {
        pool_id: 'education_pool_1',
        name: 'Education Shared Pool 1',
        oa_id: 'shared_education_oa_1',
        app_id: 'shared_education_app_1',
        monthly_quota: 10000,
        cost_per_message: 500,
        priority_events: ['health_emergency', 'transport_incident'],
        is_active: true
      },
      {
        pool_id: 'education_pool_2',
        name: 'Education Shared Pool 2', 
        oa_id: 'shared_education_oa_2',
        app_id: 'shared_education_app_2',
        monthly_quota: 10000,
        cost_per_message: 500,
        priority_events: ['attendance_absent', 'payment_overdue'],
        is_active: true
      }
    ];

    for (const pool of sharedPools) {
      const { error } = await supabase
        .from('zns_shared_pools')
        .upsert({
          ...pool,
          current_usage: 0,
          assigned_organizations: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'pool_id'
        });

      if (error) {
        console.error(`❌ Failed to create shared pool ${pool.pool_id}:`, error);
      } else {
        console.log(`✅ Shared pool created: ${pool.pool_id}`);
      }
    }

    // 5. Setup Job Schedules
    console.log('⏰ Setting up job schedules...');
    
    const jobSchedules = [
      {
        job_name: 'daily_payment_reminders',
        schedule: '0 9 * * *',
        description: 'Send daily payment due reminders',
        module: 'education',
        handler: 'educationZnsJobs.sendDailyPaymentReminders',
        is_active: true
      },
      {
        job_name: 'daily_event_reminders',
        schedule: '0 18 * * *',
        description: 'Send daily event reminders',
        module: 'education',
        handler: 'educationZnsJobs.sendDailyEventReminders',
        is_active: true
      },
      {
        job_name: 'weekly_overdue_notifications',
        schedule: '0 9 * * 1',
        description: 'Send weekly overdue payment notifications',
        module: 'education',
        handler: 'educationZnsJobs.sendWeeklyOverdueNotifications',
        is_active: true
      },
      {
        job_name: 'weekly_attendance_summary',
        schedule: '0 17 * * 5',
        description: 'Send weekly attendance summary',
        module: 'education',
        handler: 'educationZnsJobs.sendWeeklyAttendanceSummary',
        is_active: true
      },
      {
        job_name: 'transport_pickup_reminders',
        schedule: '*/30 6-18 * * 1-5',
        description: 'Send transport pickup reminders',
        module: 'education',
        handler: 'educationZnsJobs.sendTransportPickupReminders',
        is_active: true
      },
      {
        job_name: 'monthly_usage_report',
        schedule: '0 8 1 * *',
        description: 'Generate monthly ZNS usage report',
        module: 'education',
        handler: 'educationZnsJobs.generateMonthlyUsageReport',
        is_active: true
      }
    ];

    for (const job of jobSchedules) {
      const { error } = await supabase
        .from('zns_job_schedules')
        .upsert({
          ...job,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'job_name'
        });

      if (error) {
        console.error(`❌ Failed to create job schedule ${job.job_name}:`, error);
      } else {
        console.log(`✅ Job schedule created: ${job.job_name}`);
      }
    }

    // 6. Create sample organization ZNS configuration
    console.log('🏢 Setting up sample organization ZNS config...');
    
    const { data: sampleOrg } = await supabase
      .from('accounts')
      .select('id')
      .limit(1)
      .single();

    if (sampleOrg) {
      const orgZnsConfig = {
        account_id: sampleOrg.id,
        zns_enabled: true,
        use_shared_pool: true,
        shared_pool_id: 'education_pool_1',
        monthly_quota: 1000,
        current_usage: 0,
        cost_per_message: 500,
        settings: {
          auto_send_attendance: true,
          auto_send_payment: true,
          auto_send_health: true,
          auto_send_events: true,
          quiet_hours_enabled: true,
          quiet_hours: {
            start: '22:00',
            end: '07:00'
          }
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('organization_zns_config')
        .upsert(orgZnsConfig, {
          onConflict: 'account_id'
        });

      if (error) {
        console.error('❌ Failed to create sample org ZNS config:', error);
      } else {
        console.log('✅ Sample organization ZNS config created');
      }
    }

    console.log('\n🎉 Education ZNS Integration setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`✅ ${Object.keys(EDUCATION_ZNS_TEMPLATES).length} ZNS templates created`);
    console.log(`✅ ${EDUCATION_EVENT_MAPPINGS.length} event mappings created`);
    console.log(`✅ 2 shared ZNS pools configured`);
    console.log(`✅ ${jobSchedules.length} job schedules created`);
    console.log('✅ Education ZNS settings configured');
    console.log('✅ Sample organization config created');
    
    console.log('\n🚀 Next steps:');
    console.log('1. Configure actual ZNS OA IDs and App IDs in shared pools');
    console.log('2. Set up cron jobs to run the scheduled tasks');
    console.log('3. Test ZNS integration with sample events');
    console.log('4. Configure organization-specific ZNS settings');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Run setup if called directly
if (require.main === module) {
  setupEducationZns()
    .then(() => {
      console.log('✅ Setup completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Setup failed:', error);
      process.exit(1);
    });
}

export { setupEducationZns };
