import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { getApiDocs } from '../lib/swagger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Generate OpenAPI documentation and save it to a file
 */
async function generateApiDocs() {
  try {
    console.log('Generating API documentation...');
    
    // Get the API documentation
    const apiDocs = getApiDocs();
    
    // Create the output directory if it doesn't exist
    const outputDir = path.join(__dirname, '../public/api-docs');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Write the API documentation to a file
    const outputPath = path.join(outputDir, 'openapi.json');
    fs.writeFileSync(outputPath, JSON.stringify(apiDocs, null, 2));
    
    console.log(`API documentation generated successfully: ${outputPath}`);
  } catch (error) {
    console.error('Error generating API documentation:', error);
    process.exit(1);
  }
}

// Run the function
generateApiDocs();
