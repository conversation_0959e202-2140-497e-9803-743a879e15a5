---
title: "Hướng dẫn cấu hình Mini App mới trên hệ thống MinApp"
description: "Bài viết hướng dẫn chi tiết cách cấu hình Mini App mới trên hệ thống <PERSON>, từ việc tạo mới, chọn mẫu giao diện đến tùy chỉnh theo thương hiệu của bạn."
categories: ['huong-dan', 'cau-hinh']
tags: ['miniapp', 'zalo', 'setup']
image: "/images/dashboard.webp"
publishedAt: 2024-05-18
status: "published"
---

# Hướng dẫn cấu hình Mini App mới trên hệ thống MinApp

Chào mừng bạn đến với hướng dẫn cấu hình Mini App mới trên hệ thống MinApp! Trong bài viết này, chúng tôi sẽ hướng dẫn bạn từng bước để tạo và cấu hình Mini App Zalo cho doanh nghiệp của mình. <PERSON><PERSON><PERSON> làm theo các bước dưới đây để bắt đầu hành trình số hóa doanh nghiệp của bạn.

## Phần 1: Chuẩn bị trước khi cấu hình

Trước khi bắt đầu cấu hình Mini App mới, bạn cần chuẩn bị một số thông tin và tài nguyên sau:

### 1. Thông tin cơ bản về doanh nghiệp

- Tên doanh nghiệp
- Logo doanh nghiệp (định dạng PNG hoặc SVG, kích thước tối thiểu 512x512px)
- Màu sắc thương hiệu (mã màu HEX)
- Mô tả ngắn về doanh nghiệp
- Thông tin liên hệ (địa chỉ, số điện thoại, email)

### 2. Tài khoản Zalo Official Account (OA)

Để tận dụng tối đa tính năng của Mini App, bạn nên có sẵn Zalo OA cho doanh nghiệp. Nếu chưa có, bạn có thể tạo tại [https://oa.zalo.me](https://oa.zalo.me).

### 3. Danh sách sản phẩm/dịch vụ (nếu có)

- Tên sản phẩm/dịch vụ
- Mô tả
- Hình ảnh
- Giá cả
- Danh mục

### 4. Tài khoản MinApp đã đăng ký

Đảm bảo bạn đã đăng ký tài khoản MinApp và đăng nhập vào hệ thống. Nếu chưa, hãy tham khảo bài viết [Hướng dẫn đăng ký và thanh toán MinApp](/blog/huong-dan-dang-ky-thanh-toan).

## Phần 2: Tạo Mini App mới

### Bước 1: Truy cập Dashboard

- Đăng nhập vào tài khoản MinApp tại [https://miniapp.vn/login](https://miniapp.vn/login)
- Sau khi đăng nhập, bạn sẽ được chuyển đến Dashboard

### Bước 2: Tạo Mini App mới

- Từ Dashboard, nhấp vào nút "Tạo Mini App mới" ở góc phải trên cùng
- Hoặc nhấp vào mục "Mini Apps" ở menu bên trái, sau đó nhấp vào nút "Tạo mới"

### Bước 3: Nhập thông tin cơ bản

- Nhập tên Mini App (tên này sẽ hiển thị cho người dùng Zalo)
- Chọn loại Mini App:
  - **Cửa hàng trực tuyến**: Bán sản phẩm vật lý
  - **Dịch vụ**: Cung cấp dịch vụ
  - **Nhà hàng/Cafe**: Đặt món và giao đồ ăn
  - **Khác**: Các loại Mini App khác
- Nhập mô tả ngắn về Mini App (tối đa 200 ký tự)
- Nhấp vào nút "Tiếp tục"

## Phần 3: Chọn mẫu giao diện

### Bước 1: Duyệt qua các mẫu giao diện

- MinApp cung cấp nhiều mẫu giao diện đẹp mắt và chuyên nghiệp cho nhiều ngành hàng khác nhau
- Bạn có thể lọc mẫu giao diện theo ngành hàng:
  - Thời trang
  - Mỹ phẩm
  - Điện tử
  - Nhà hàng
  - Tạp hóa
  - Và nhiều ngành khác

### Bước 2: Xem trước mẫu giao diện

- Nhấp vào nút "Xem trước" để xem chi tiết mẫu giao diện
- Trong chế độ xem trước, bạn có thể:
  - Xem các trang khác nhau của mẫu (Trang chủ, Danh mục, Chi tiết sản phẩm, Giỏ hàng...)
  - Kiểm tra giao diện trên các kích thước màn hình khác nhau
  - Xem các tính năng có sẵn trong mẫu

### Bước 3: Chọn mẫu giao diện

- Sau khi xem xét các mẫu, nhấp vào nút "Chọn mẫu này" bên dưới mẫu giao diện bạn muốn sử dụng
- Xác nhận lựa chọn của bạn khi được nhắc
- Nhấp vào nút "Tiếp tục" để chuyển đến bước tiếp theo

## Phần 4: Tùy chỉnh giao diện theo thương hiệu

### Bước 1: Tải lên logo

- Nhấp vào khu vực "Tải lên logo" để chọn file logo từ máy tính của bạn
- Định dạng hỗ trợ: PNG, JPG, SVG
- Kích thước khuyến nghị: 512x512px
- Sau khi tải lên, bạn có thể điều chỉnh vị trí và kích thước logo

### Bước 2: Chọn màu sắc thương hiệu

- Chọn màu chính (Primary color): Đây là màu chủ đạo của thương hiệu, sẽ được sử dụng cho các nút, tiêu đề...
- Chọn màu phụ (Secondary color): Màu phụ trợ, sẽ được sử dụng cho các chi tiết nhỏ
- Chọn màu nền (Background color): Màu nền của Mini App
- Bạn có thể nhập mã màu HEX hoặc sử dụng bảng màu để chọn

### Bước 3: Tùy chỉnh font chữ

- Chọn font chữ cho tiêu đề
- Chọn font chữ cho nội dung
- MinApp hỗ trợ nhiều font chữ phổ biến và cả font chữ tiếng Việt

### Bước 4: Tùy chỉnh các thành phần giao diện

- **Header (Đầu trang)**:
  - Kiểu hiển thị (cố định hoặc cuộn)
  - Màu nền
  - Hiển thị logo
- **Footer (Chân trang)**:
  - Số cột
  - Các liên kết
  - Thông tin liên hệ
- **Trang chủ**:
  - Banner chính
  - Các phần nổi bật
  - Cách hiển thị sản phẩm
- **Trang danh mục**:
  - Kiểu hiển thị danh mục
  - Số sản phẩm mỗi hàng
- **Trang chi tiết sản phẩm**:
  - Bố cục
  - Các thông tin hiển thị

### Bước 5: Xem trước và lưu thay đổi

- Nhấp vào nút "Xem trước" để kiểm tra giao diện sau khi tùy chỉnh
- Nếu hài lòng, nhấp vào nút "Lưu thay đổi"
- Nhấp vào nút "Tiếp tục" để chuyển đến bước tiếp theo

## Phần 5: Cấu hình Zalo OA và tích hợp

### Bước 1: Kết nối Zalo OA

- Nhấp vào nút "Kết nối Zalo OA"
- Đăng nhập vào tài khoản Zalo quản lý OA của bạn (nếu chưa đăng nhập)
- Chọn Zalo OA bạn muốn kết nối với Mini App
- Xác nhận quyền truy cập khi được nhắc
- Sau khi kết nối thành công, bạn sẽ thấy thông tin OA hiển thị trên màn hình

### Bước 2: Cấu hình thông báo

- **Thông báo đơn hàng**:
  - Bật/tắt thông báo khi có đơn hàng mới
  - Tùy chỉnh nội dung thông báo
  - Chọn người nhận thông báo (admin, nhân viên)
- **Thông báo cho khách hàng**:
  - Bật/tắt thông báo xác nhận đơn hàng
  - Bật/tắt thông báo cập nhật trạng thái đơn hàng
  - Bật/tắt thông báo khi đơn hàng được giao
  - Tùy chỉnh nội dung các loại thông báo

### Bước 3: Cấu hình ZNS (Zalo Notification Service)

- Nếu bạn đã đăng ký ZNS cho Zalo OA, bạn có thể cấu hình:
  - Mẫu thông báo đơn hàng
  - Mẫu thông báo khuyến mãi
  - Mẫu thông báo nhắc nhở
- Nhập thông tin API key ZNS (nếu có)
- Nhấp vào nút "Lưu cấu hình ZNS"

### Bước 4: Cấu hình Chatbot (tùy chọn)

- Bật/tắt tính năng Chatbot
- Chọn loại Chatbot:
  - Chatbot đơn giản (trả lời tự động theo kịch bản)
  - Chatbot AI (sử dụng trí tuệ nhân tạo)
- Cấu hình các câu hỏi và câu trả lời thường gặp
- Nhấp vào nút "Lưu cấu hình Chatbot"

### Bước 5: Xem trước và hoàn tất

- Nhấp vào nút "Xem trước tích hợp" để kiểm tra tích hợp Zalo OA
- Nếu hài lòng, nhấp vào nút "Hoàn tất tích hợp"
- Nhấp vào nút "Tiếp tục" để chuyển đến bước tiếp theo

## Phần 6: Cấu hình thanh toán và vận chuyển

### Bước 1: Cấu hình phương thức thanh toán

- **Thanh toán khi nhận hàng (COD)**:
  - Bật/tắt phương thức thanh toán COD
  - Cấu hình các điều kiện áp dụng (nếu có)
- **Thanh toán qua ZaloPay**:
  - Bật/tắt phương thức thanh toán ZaloPay
  - Nhập thông tin Merchant ID và Key (nếu có)
- **Thanh toán qua thẻ ngân hàng**:
  - Bật/tắt phương thức thanh toán qua thẻ
  - Chọn cổng thanh toán (VNPay, OnePay, Momo...)
  - Nhập thông tin API key
- **Thanh toán qua chuyển khoản ngân hàng**:
  - Bật/tắt phương thức thanh toán chuyển khoản
  - Nhập thông tin tài khoản ngân hàng

### Bước 2: Cấu hình phương thức vận chuyển

- **Tự vận chuyển**:
  - Bật/tắt phương thức tự vận chuyển
  - Cấu hình phí vận chuyển theo khu vực
  - Cấu hình thời gian giao hàng dự kiến
- **Đối tác vận chuyển**:
  - Bật/tắt tích hợp với đối tác vận chuyển
  - Chọn đối tác vận chuyển (GHN, GHTK, Viettel Post...)
  - Nhập thông tin API key
  - Cấu hình kho hàng và địa chỉ lấy hàng

### Bước 3: Cấu hình thuế và phí

- Cấu hình thuế VAT (nếu có)
- Cấu hình phí xử lý đơn hàng (nếu có)
- Cấu hình phí đóng gói (nếu có)

### Bước 4: Xem trước và lưu cấu hình

- Nhấp vào nút "Xem trước" để kiểm tra quy trình thanh toán và vận chuyển
- Nếu hài lòng, nhấp vào nút "Lưu cấu hình"
- Nhấp vào nút "Tiếp tục" để chuyển đến bước tiếp theo

## Phần 7: Cấu hình SEO và phân tích

### Bước 1: Cấu hình SEO

- Nhập tiêu đề SEO cho Mini App
- Nhập mô tả SEO (tối đa 160 ký tự)
- Nhập từ khóa SEO (cách nhau bằng dấu phẩy)
- Tải lên hình ảnh chia sẻ (Open Graph image)

### Bước 2: Cấu hình Google Analytics (tùy chọn)

- Bật/tắt tích hợp Google Analytics
- Nhập Measurement ID (G-XXXXXXXX)

### Bước 3: Cấu hình Facebook Pixel (tùy chọn)

- Bật/tắt tích hợp Facebook Pixel
- Nhập Pixel ID

### Bước 4: Lưu cấu hình

- Nhấp vào nút "Lưu cấu hình"
- Nhấp vào nút "Tiếp tục" để chuyển đến bước cuối cùng

## Phần 8: Kiểm tra và xuất bản Mini App

### Bước 1: Kiểm tra tổng thể

- Nhấp vào nút "Kiểm tra tổng thể" để hệ thống tự động kiểm tra các cấu hình
- Hệ thống sẽ hiển thị danh sách các mục đã cấu hình và chưa cấu hình
- Sửa các lỗi hoặc cảnh báo (nếu có)

### Bước 2: Xem trước Mini App

- Nhấp vào nút "Xem trước Mini App" để xem trước Mini App hoàn chỉnh
- Kiểm tra các tính năng chính:
  - Duyệt danh mục và sản phẩm
  - Thêm sản phẩm vào giỏ hàng
  - Quy trình thanh toán
  - Trang thông tin liên hệ
- Kiểm tra giao diện trên các kích thước màn hình khác nhau

### Bước 3: Xuất bản Mini App

- Khi bạn đã hài lòng với Mini App, nhấp vào nút "Xuất bản Mini App"
- Xác nhận việc xuất bản khi được nhắc
- Hệ thống sẽ tiến hành xuất bản Mini App lên Zalo
- Quá trình xuất bản có thể mất từ vài phút đến vài giờ, tùy thuộc vào độ phức tạp của Mini App

### Bước 4: Nhận thông tin truy cập

- Sau khi xuất bản thành công, bạn sẽ nhận được:
  - Link truy cập Mini App
  - Mã QR để chia sẻ Mini App
  - Hướng dẫn thêm Mini App vào Zalo OA

## Phần 9: Quản lý Mini App sau khi xuất bản

### Cập nhật nội dung

- Đăng nhập vào Dashboard MinApp
- Chọn Mini App bạn muốn cập nhật
- Nhấp vào mục "Nội dung" để cập nhật:
  - Sản phẩm/dịch vụ
  - Danh mục
  - Trang thông tin
  - Banner và khuyến mãi

### Theo dõi hiệu suất

- Từ Dashboard, nhấp vào mục "Phân tích" để xem:
  - Số lượng người dùng
  - Lượt xem trang
  - Tỷ lệ chuyển đổi
  - Doanh số bán hàng
  - Sản phẩm phổ biến

### Quản lý đơn hàng

- Từ Dashboard, nhấp vào mục "Đơn hàng" để:
  - Xem danh sách đơn hàng
  - Cập nhật trạng thái đơn hàng
  - Xuất báo cáo đơn hàng

### Cập nhật cấu hình

- Nếu bạn muốn thay đổi cấu hình Mini App, nhấp vào mục "Cài đặt" và chọn phần cấu hình bạn muốn thay đổi
- Sau khi cập nhật cấu hình, nhấp vào nút "Lưu thay đổi" và "Xuất bản lại" để áp dụng các thay đổi

## Phần 10: Các mẹo và thực hành tốt nhất

### Tối ưu hóa hình ảnh

- Sử dụng hình ảnh chất lượng cao nhưng đã được tối ưu hóa kích thước
- Đặt tên file hình ảnh có ý nghĩa và liên quan đến nội dung
- Thêm văn bản thay thế (alt text) cho tất cả hình ảnh

### Tổ chức danh mục sản phẩm

- Tạo cấu trúc danh mục rõ ràng và logic
- Giới hạn số lượng danh mục chính (không quá 7-8 danh mục)
- Sử dụng danh mục con để tổ chức sản phẩm tốt hơn

### Tối ưu hóa quy trình thanh toán

- Giảm thiểu số bước trong quy trình thanh toán
- Cung cấp nhiều phương thức thanh toán
- Hiển thị rõ ràng các chi phí (sản phẩm, vận chuyển, thuế...)

### Tương tác với khách hàng

- Phản hồi nhanh chóng các câu hỏi và đánh giá
- Sử dụng Zalo OA để gửi thông báo về khuyến mãi và sản phẩm mới
- Tạo chương trình khách hàng thân thiết để khuyến khích mua hàng lặp lại

## Kết luận

Cấu hình Mini App mới trên hệ thống MinApp là một quá trình đơn giản và trực quan. Bằng cách làm theo hướng dẫn chi tiết trong bài viết này, bạn có thể dễ dàng tạo Mini App Zalo chuyên nghiệp cho doanh nghiệp của mình mà không cần kiến thức lập trình.

Hãy nhớ rằng, một Mini App hiệu quả không chỉ đẹp mắt mà còn phải dễ sử dụng và đáp ứng nhu cầu của khách hàng. Thường xuyên cập nhật nội dung, theo dõi hiệu suất và lắng nghe phản hồi của khách hàng để liên tục cải thiện Mini App của bạn.

Nếu bạn gặp bất kỳ khó khăn nào trong quá trình cấu hình, đừng ngần ngại liên hệ với đội ngũ hỗ trợ của MinApp <NAME_EMAIL> hoặc hotline 1900 1234.

Chúc bạn thành công trong việc tạo Mini App Zalo cho doanh nghiệp của mình!
