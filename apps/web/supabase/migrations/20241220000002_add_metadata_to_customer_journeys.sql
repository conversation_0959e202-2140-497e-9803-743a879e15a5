-- Add metadata column to customer_journeys table
-- This migration adds the missing metadata column that is referenced in the application code

-- Add metadata column to customer_journeys table
ALTER TABLE customer_journeys 
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- Create index for metadata column for better performance
CREATE INDEX IF NOT EXISTS idx_customer_journeys_metadata_gin 
ON customer_journeys USING gin(metadata);

-- Update existing rows to have empty metadata if null
UPDATE customer_journeys 
SET metadata = '{}' 
WHERE metadata IS NULL;
