-- Create Games Module Tables

-- Game Categories
CREATE TABLE IF NOT EXISTS game_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  icon VARCHAR(50) DEFAULT 'gamepad-2',
  color VARCHAR(20) DEFAULT 'blue',
  age_group VARCHAR(20) DEFAULT 'all', -- 'preschool', 'elementary', 'middle', 'high', 'all'
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Games
CREATE TABLE IF NOT EXISTS games (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  category_id UUID NOT NULL REFERENCES game_categories(id) ON DELETE CASCADE,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  instructions TEXT,
  thumbnail_url TEXT,
  game_type VARCHAR(50) NOT NULL, -- 'quiz', 'puzzle', 'memory', 'math', 'word', 'drawing'
  difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level BETWEEN 1 AND 5),
  estimated_duration INTEGER DEFAULT 300, -- seconds
  min_age INTEGER DEFAULT 6,
  max_age INTEGER DEFAULT 18,
  subject VARCHAR(100), -- 'math', 'english', 'science', 'history', 'art'
  learning_objectives TEXT[],
  game_config JSONB DEFAULT '{}', -- Game-specific configuration
  is_multiplayer BOOLEAN DEFAULT FALSE,
  max_players INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Game Sessions (Individual play sessions)
CREATE TABLE IF NOT EXISTS game_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  session_type VARCHAR(20) DEFAULT 'practice', -- 'practice', 'assignment', 'competition'
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  duration_seconds INTEGER,
  score INTEGER DEFAULT 0,
  max_score INTEGER DEFAULT 100,
  accuracy_percentage DECIMAL(5,2),
  attempts INTEGER DEFAULT 1,
  hints_used INTEGER DEFAULT 0,
  game_data JSONB DEFAULT '{}', -- Session-specific data
  is_completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Achievements
CREATE TABLE IF NOT EXISTS achievements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  icon VARCHAR(50) DEFAULT 'trophy',
  badge_color VARCHAR(20) DEFAULT 'gold',
  achievement_type VARCHAR(50) NOT NULL, -- 'score', 'streak', 'completion', 'time', 'accuracy'
  criteria JSONB NOT NULL, -- Achievement criteria configuration
  points_reward INTEGER DEFAULT 10,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Student Achievements
CREATE TABLE IF NOT EXISTS student_achievements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_id UUID NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
  earned_at TIMESTAMPTZ DEFAULT NOW(),
  session_id UUID REFERENCES game_sessions(id),
  UNIQUE(student_id, achievement_id)
);

-- Leaderboards
CREATE TABLE IF NOT EXISTS leaderboards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  game_id UUID REFERENCES games(id) ON DELETE CASCADE,
  category_id UUID REFERENCES game_categories(id) ON DELETE CASCADE,
  title VARCHAR(200) NOT NULL,
  leaderboard_type VARCHAR(20) NOT NULL, -- 'game', 'category', 'overall'
  period VARCHAR(20) NOT NULL, -- 'daily', 'weekly', 'monthly', 'all_time'
  start_date DATE,
  end_date DATE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Leaderboard Entries
CREATE TABLE IF NOT EXISTS leaderboard_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  leaderboard_id UUID NOT NULL REFERENCES leaderboards(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  score INTEGER NOT NULL,
  rank INTEGER,
  total_sessions INTEGER DEFAULT 1,
  average_score DECIMAL(8,2),
  best_session_id UUID REFERENCES game_sessions(id),
  last_played_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(leaderboard_id, student_id)
);

-- Game Assignments (Teachers assign games to students/classes)
CREATE TABLE IF NOT EXISTS game_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
  assigned_by UUID NOT NULL REFERENCES auth.users(id),
  assigned_to_type VARCHAR(20) NOT NULL, -- 'student', 'class', 'group'
  assigned_to_id UUID NOT NULL, -- student_id, class_id, or group_id
  title VARCHAR(200),
  instructions TEXT,
  due_date TIMESTAMPTZ,
  max_attempts INTEGER DEFAULT 3,
  min_score INTEGER,
  is_required BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Student Progress Tracking
CREATE TABLE IF NOT EXISTS student_game_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
  total_sessions INTEGER DEFAULT 0,
  total_time_seconds INTEGER DEFAULT 0,
  best_score INTEGER DEFAULT 0,
  average_score DECIMAL(8,2) DEFAULT 0,
  completion_rate DECIMAL(5,2) DEFAULT 0,
  current_streak INTEGER DEFAULT 0,
  longest_streak INTEGER DEFAULT 0,
  last_played_at TIMESTAMPTZ,
  mastery_level INTEGER DEFAULT 0 CHECK (mastery_level BETWEEN 0 AND 5),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(student_id, game_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_game_categories_account_id ON game_categories(account_id);
CREATE INDEX IF NOT EXISTS idx_games_account_id ON games(account_id);
CREATE INDEX IF NOT EXISTS idx_games_category_id ON games(category_id);
CREATE INDEX IF NOT EXISTS idx_game_sessions_account_id ON game_sessions(account_id);
CREATE INDEX IF NOT EXISTS idx_game_sessions_student_id ON game_sessions(student_id);
CREATE INDEX IF NOT EXISTS idx_game_sessions_game_id ON game_sessions(game_id);
CREATE INDEX IF NOT EXISTS idx_student_achievements_student_id ON student_achievements(student_id);
CREATE INDEX IF NOT EXISTS idx_leaderboard_entries_leaderboard_id ON leaderboard_entries(leaderboard_id);
CREATE INDEX IF NOT EXISTS idx_game_assignments_account_id ON game_assignments(account_id);
CREATE INDEX IF NOT EXISTS idx_student_game_progress_student_id ON student_game_progress(student_id);

-- Create updated_at triggers
CREATE TRIGGER update_game_categories_updated_at 
  BEFORE UPDATE ON game_categories 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_games_updated_at 
  BEFORE UPDATE ON games 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_student_game_progress_updated_at 
  BEFORE UPDATE ON student_game_progress 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
