-- Create messages table for education platform
-- This table stores messages sent to parents, instructors, and other stakeholders

-- Create message status enum
CREATE TYPE message_status AS ENUM ('draft', 'scheduled', 'sent', 'failed');

-- Create message type enum  
CREATE TYPE message_type AS ENUM ('announcement', 'invitation', 'fee_reminder', 'emergency');

-- Create recipient type enum
CREATE TYPE recipient_type AS ENUM ('all_parents', 'class_parents', 'specific_parents', 'instructors');

-- Create priority enum
CREATE TYPE message_priority AS ENUM ('low', 'normal', 'high', 'urgent');

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  message_type message_type NOT NULL,
  recipient_type recipient_type NOT NULL,
  priority message_priority DEFAULT 'normal',
  status message_status DEFAULT 'draft',
  sender_name <PERSON><PERSON>HA<PERSON>(255),
  sent_at TIMESTAMPTZ,
  scheduled_at TIMESTAMPTZ,
  delivery_channels JSONB DEFAULT '{"sms": true, "email": true, "push": true}'::jsonb,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- Create message recipients table
CREATE TABLE IF NOT EXISTS message_recipients (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
  recipient_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  recipient_type VARCHAR(50) NOT NULL, -- 'guardian', 'instructor', etc.
  delivery_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'sent', 'delivered', 'failed'
  read_status VARCHAR(50) DEFAULT 'unread', -- 'unread', 'read'
  delivered_at TIMESTAMPTZ,
  read_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(message_id, recipient_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_messages_account_id ON messages(account_id);
CREATE INDEX IF NOT EXISTS idx_messages_status ON messages(status);
CREATE INDEX IF NOT EXISTS idx_messages_message_type ON messages(message_type);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_sent_at ON messages(sent_at);

CREATE INDEX IF NOT EXISTS idx_message_recipients_message_id ON message_recipients(message_id);
CREATE INDEX IF NOT EXISTS idx_message_recipients_recipient_id ON message_recipients(recipient_id);
CREATE INDEX IF NOT EXISTS idx_message_recipients_delivery_status ON message_recipients(delivery_status);

-- Enable RLS
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_recipients ENABLE ROW LEVEL SECURITY;

-- RLS Policies for messages
CREATE POLICY "Users can view messages in their accounts" ON messages
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage messages in their accounts" ON messages
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member', 'teacher')
    )
  );

-- RLS Policies for message recipients
CREATE POLICY "Users can view message recipients in their accounts" ON message_recipients
  FOR SELECT USING (
    message_id IN (
      SELECT id FROM messages 
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can manage message recipients in their accounts" ON message_recipients
  FOR ALL USING (
    message_id IN (
      SELECT id FROM messages 
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships 
        WHERE user_id = auth.uid() 
        AND account_role IN ('owner', 'member', 'teacher')
      )
    )
  );

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_messages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_messages_updated_at_trigger
  BEFORE UPDATE ON messages
  FOR EACH ROW
  EXECUTE FUNCTION update_messages_updated_at();
