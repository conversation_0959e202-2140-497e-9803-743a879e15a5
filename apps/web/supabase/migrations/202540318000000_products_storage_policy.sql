-- Create storage bucket
insert into storage.buckets (id, name, public)
values ('products', 'products', true);
-- <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> s<PERSON>ch products_storage_insert

create policy "products_storage_insert"
on storage.objects
for insert
with check (
    bucket_id = 'products'
    and (
        public.has_permission(
            auth.uid(),
            -- <PERSON><PERSON><PERSON><PERSON> xu<PERSON><PERSON> phần thứ hai của đường dẫn (sau tempFolder)
            (regexp_match(name, '^[^/]+/([^/]+)'))[1]::uuid,
            'products.manage'::app_permissions
        )
    )
);

-- <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> ch<PERSON> s<PERSON>ch kh<PERSON>c tư<PERSON> tự
create policy "products_storage_read"
on storage.objects
for select
                      using (
                      bucket_id = 'products'
                      and (
                      public.has_permission(
                      auth.uid(),
                      (regexp_match(name, '^[^/]+/([^/]+)'))[1]::uuid,
                      'products.manage'::app_permissions
                      )
                      or
                      public.is_team_member(
                      (regexp_match(name, '^[^/]+/([^/]+)'))[1]::uuid,
                      auth.uid()
                      )
                      )
                      );

create policy "products_storage_update"
on storage.objects
for update
               using (
               bucket_id = 'products'
               and (
               public.has_permission(
               auth.uid(),
               (regexp_match(name, '^[^/]+/([^/]+)'))[1]::uuid,
               'products.manage'::app_permissions
               )
               )
               );

create policy "products_storage_delete"
on storage.objects
for delete
using (
    bucket_id = 'products'
    and (
        public.has_permission(
            auth.uid(),
            (regexp_match(name, '^[^/]+/([^/]+)'))[1]::uuid,
            'products.manage'::app_permissions
        )
    )
);

CREATE TABLE temp_images (
                             id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                             temp_path TEXT NOT NULL, -- Đường dẫn tạm thời (e.g., uuid/accountId/...)
                             url TEXT NOT NULL, -- URL công khai
                             created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                             expires_at TIMESTAMP WITH TIME ZONE NOT NULL, -- Thời gian hết hạn
                             is_moved BOOLEAN DEFAULT FALSE, -- Đánh dấu hình ảnh đã được di chuyển
                             account_id UUID NOT NULL -- Lưu accountId để quản lý theo tenant
);
