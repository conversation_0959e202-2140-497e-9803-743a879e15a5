-- Add order_code column to customer_orders table
ALTER TABLE public.customer_orders
ADD COLUMN IF NOT EXISTS order_code VARCHAR(50);

-- Update existing records with a default order code
UPDATE public.customer_orders
SET order_code = 'ORD-' || LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0')
WHERE order_code IS NULL;

-- Create a function to generate order code
CREATE OR REPLACE FUNCTION public.generate_order_code()
RETURNS TRIGGER AS $$
BEGIN
    -- Generate order code if not provided
    IF NEW.order_code IS NULL THEN
        NEW.order_code := 'ORD-' || LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically generate order code
DROP TRIGGER IF EXISTS generate_order_code_trigger ON public.customer_orders;
CREATE TRIGGER generate_order_code_trigger
BEFORE INSERT ON public.customer_orders
FOR EACH ROW
EXECUTE FUNCTION public.generate_order_code();

-- Add comment to the column
COMMENT ON COLUMN public.customer_orders.order_code IS 'Unique order code for display purposes';
