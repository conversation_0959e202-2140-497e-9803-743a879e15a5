-- T<PERSON><PERSON><PERSON> cột author_id vào bảng oa_configurations để biết ai tạo ra
ALTER TABLE public.oa_configurations ADD COLUMN IF NOT EXISTS author_id UUID REFERENCES auth.users(id);

-- Cập nhật constraint để phân biệt giữa OA của theme và OA của account
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'valid_oa_config') THEN
        ALTER TABLE public.oa_configurations DROP CONSTRAINT valid_oa_config;
    END IF;
END $$;

ALTER TABLE public.oa_configurations ADD CONSTRAINT valid_oa_config CHECK (
  (oa_type = 'shared' AND account_id IS NULL) OR
  (oa_type = 'private' AND account_id IS NOT NULL)
);

-- Cập nhật RLS policy cho oa_configurations
DROP POLICY IF EXISTS "Enable read access for all users" ON public.oa_configurations;
DROP POLICY IF EXISTS "Enable write access for all users" ON public.oa_configurations;

-- Policy cho SELECT - Service role và authenticated users
CREATE POLICY "Enable read access for all users" ON public.oa_configurations
  FOR SELECT
  USING (
    (auth.role() = 'service_role') OR  -- Service role có thể đọc tất cả
    (
      -- Cho phép đọc OA nếu user là author
      author_id = auth.uid() OR
      -- Cho phép đọc OA nếu user có quyền xem theme sử dụng OA đó
      EXISTS (
        SELECT 1 FROM public.themes t
        WHERE t.oa_config_id = oa_configurations.id
      ) OR
      -- Cho phép đọc OA nếu user có quyền xem account theme sử dụng OA đó
      EXISTS (
        SELECT 1 FROM public.account_themes at
        WHERE at.oa_config_id = oa_configurations.id
        AND public.has_role_on_account(at.account_id)
      ) OR
      -- Cho phép đọc OA nếu user có quyền quản lý account sở hữu OA đó
      (account_id IS NOT NULL AND public.has_role_on_account(account_id))
    )
  );

-- Policy cho INSERT/UPDATE/DELETE - Service role và authenticated users
CREATE POLICY "Enable write access for all users" ON public.oa_configurations
  FOR ALL
  USING (
    (auth.role() = 'service_role') OR  -- Service role có thể write tất cả
    (
      auth.role() = 'authenticated' AND
      (
        -- Cho phép write OA nếu user là author
        author_id = auth.uid() OR
        -- Cho phép write OA nếu user là author của theme sử dụng OA đó
        EXISTS (
          SELECT 1 FROM public.themes t
          WHERE t.oa_config_id = oa_configurations.id
          AND t.author_id = auth.uid()
        ) OR
        -- Cho phép write OA nếu user có quyền quản lý account sở hữu OA đó
        (account_id IS NOT NULL AND public.has_role_on_account(account_id))
      )
    )
  )
  WITH CHECK (
    (auth.role() = 'service_role') OR  -- Service role có thể write tất cả
    (
      auth.role() = 'authenticated' AND
      (
        -- Cho phép write OA nếu user là author
        author_id = auth.uid() OR
        -- Cho phép write OA nếu user là author của theme sử dụng OA đó
        EXISTS (
          SELECT 1 FROM public.themes t
          WHERE t.oa_config_id = oa_configurations.id
          AND t.author_id = auth.uid()
        ) OR
        -- Cho phép write OA nếu user có quyền quản lý account sở hữu OA đó
        (account_id IS NOT NULL AND public.has_role_on_account(account_id))
      )
    )
  );

-- Tạo function để kiểm tra quyền connect OA
CREATE OR REPLACE FUNCTION public.can_connect_oa(oa_config_id UUID) RETURNS BOOLEAN AS $$
DECLARE
  can_connect BOOLEAN;
BEGIN
  SELECT 
    CASE 
      -- Nếu user là author của OA
      WHEN author_id = auth.uid() THEN TRUE
      -- Nếu user là author của theme sử dụng OA đó
      WHEN EXISTS (
        SELECT 1 FROM public.themes t
        WHERE t.oa_config_id = oa_configurations.id
        AND t.author_id = auth.uid()
      ) THEN TRUE
      -- Nếu user có quyền quản lý account sở hữu OA đó
      WHEN account_id IS NOT NULL AND public.has_role_on_account(account_id) THEN TRUE
      ELSE FALSE
    END INTO can_connect
  FROM public.oa_configurations
  WHERE id = oa_config_id;
  
  RETURN COALESCE(can_connect, FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Cập nhật author_id cho các OA configurations hiện có
UPDATE public.oa_configurations oc
SET author_id = t.author_id
FROM public.themes t
WHERE oc.id = t.oa_config_id AND t.author_id IS NOT NULL;
