-- Create import_export_templates table
CREATE TABLE IF NOT EXISTS public.import_export_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  resource TEXT NOT NULL,
  name TEXT NOT NULL,
  mapping JSO<PERSON>B NOT NULL,
  filters <PERSON><PERSON><PERSON><PERSON>,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Add RLS policies
ALTER TABLE public.import_export_templates ENABLE ROW LEVEL SECURITY;

-- Policy for select
CREATE POLICY "Users can view their own templates"
  ON public.import_export_templates
  FOR SELECT
  USING (public.has_permission(auth.uid(), account_id, 'data.manage'::public.app_permissions));

-- Policy for insert
CREATE POLICY "Users can create templates for their accounts"
  ON public.import_export_templates
  FOR INSERT
  WITH CHECK (public.has_permission(auth.uid(), account_id, 'data.manage'::public.app_permissions));

-- Policy for update
CREATE POLICY "Users can update their own templates"
  ON public.import_export_templates
  FOR UPDATE
  USING (public.has_permission(auth.uid(), account_id, 'data.manage'::public.app_permissions));

-- Policy for delete
CREATE POLICY "Users can delete their own templates"
  ON public.import_export_templates
  FOR DELETE
  USING (public.has_permission(auth.uid(), account_id, 'data.manage'::public.app_permissions));

-- Create function to create the table if it doesn't exist
CREATE OR REPLACE FUNCTION public.create_import_export_templates_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the table exists
  IF NOT EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public'
    AND tablename = 'import_export_templates'
  ) THEN
    -- Create the table
    CREATE TABLE public.import_export_templates (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
      resource TEXT NOT NULL,
      name TEXT NOT NULL,
      mapping JSONB NOT NULL,
      filters JSONB,
      created_at TIMESTAMPTZ DEFAULT now(),
      updated_at TIMESTAMPTZ DEFAULT now()
    );

    -- Add RLS policies
    ALTER TABLE public.import_export_templates ENABLE ROW LEVEL SECURITY;

    -- Policy for select
    CREATE POLICY "Users can view their own templates"
      ON public.import_export_templates
      FOR SELECT
      USING (public.has_permission(auth.uid(), account_id, 'data.manage'::public.app_permissions));

    -- Policy for insert
    CREATE POLICY "Users can create templates for their accounts"
      ON public.import_export_templates
      FOR INSERT
      WITH CHECK (public.has_permission(auth.uid(), account_id, 'data.manage'::public.app_permissions));

    -- Policy for update
    CREATE POLICY "Users can update their own templates"
      ON public.import_export_templates
      FOR UPDATE
      USING (public.has_permission(auth.uid(), account_id, 'data.manage'::public.app_permissions));

    -- Policy for delete
    CREATE POLICY "Users can delete their own templates"
      ON public.import_export_templates
      FOR DELETE
      USING (public.has_permission(auth.uid(), account_id, 'data.manage'::public.app_permissions));
  END IF;
END;
$$;
