-- Migration để cập nhật bảng oa_configurations hỗ trợ mô hình phân cấp

-- Thê<PERSON> các cột mới vào bảng oa_configurations
ALTER TABLE public.oa_configurations
ADD COLUMN IF NOT EXISTS is_system_default BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS theme_id UUID REFERENCES public.themes(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS author_id UUID REFERENCES auth.users(id);

-- Cập nhật dữ liệu hiện có trước khi thêm constraint
-- Đặt is_system_default = TRUE cho các bản ghi không có account_id, theme_id hoặc is_system_default
UPDATE public.oa_configurations
SET is_system_default = TRUE
WHERE account_id IS NULL AND theme_id IS NULL AND (is_system_default IS NULL OR is_system_default = FALSE);

-- C<PERSON><PERSON> nhật author_id cho các bản ghi hiện có
-- Lấy user đầu tiên từ auth.users
UPDATE public.oa_configurations
SET author_id = (
  SELECT id FROM auth.users LIMIT 1
)
WHERE author_id IS NULL;

-- Cập nhật constraint để phân biệt giữa các loại OA
ALTER TABLE public.oa_configurations DROP CONSTRAINT IF EXISTS valid_oa_config;
ALTER TABLE public.oa_configurations ADD CONSTRAINT valid_oa_config CHECK (
  (is_system_default = TRUE) OR
  (theme_id IS NOT NULL) OR
  (account_id IS NOT NULL)
);

-- Tạo index để tối ưu truy vấn
CREATE INDEX IF NOT EXISTS idx_oa_configurations_system_default ON public.oa_configurations(is_system_default) WHERE is_system_default = TRUE;
CREATE INDEX IF NOT EXISTS idx_oa_configurations_theme_id ON public.oa_configurations(theme_id) WHERE theme_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_oa_configurations_account_id ON public.oa_configurations(account_id) WHERE account_id IS NOT NULL;

-- Create function to check if OA token is valid
CREATE OR REPLACE FUNCTION public.is_oa_token_valid(oa_config_id UUID) RETURNS BOOLEAN AS $$
DECLARE
  token_valid BOOLEAN;
BEGIN
  SELECT
    CASE
      WHEN access_token IS NULL THEN FALSE
      WHEN token_expires_at IS NULL THEN FALSE
      WHEN token_expires_at < NOW() THEN FALSE
      ELSE TRUE
    END INTO token_valid
  FROM public.oa_configurations
  WHERE id = oa_config_id;

  RETURN COALESCE(token_valid, FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Cập nhật function để kiểm tra quyền connect OA
CREATE OR REPLACE FUNCTION public.can_connect_oa(oa_config_id UUID) RETURNS BOOLEAN AS $$
DECLARE
  can_connect BOOLEAN;
BEGIN
  SELECT
    CASE
      -- Nếu user là author của OA
      WHEN author_id = auth.uid() THEN TRUE
      -- Nếu user là author của theme sử dụng OA đó
      WHEN theme_id IS NOT NULL AND EXISTS (
        SELECT 1 FROM public.themes t
        WHERE t.id = oa_configurations.theme_id
        AND t.author_id = auth.uid()
      ) THEN TRUE
      -- Nếu user có quyền quản lý account sở hữu OA đó
      WHEN account_id IS NOT NULL AND public.has_role_on_account(account_id) THEN TRUE
      ELSE FALSE
    END INTO can_connect
  FROM public.oa_configurations
  WHERE id = oa_config_id;

  RETURN COALESCE(can_connect, FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Tạo function để lấy OA configuration phù hợp cho một account
CREATE OR REPLACE FUNCTION public.get_oa_config_for_account(p_account_id UUID) RETURNS UUID AS $$
DECLARE
  oa_config_id UUID;
BEGIN
  -- 1. Kiểm tra OA của tài khoản
  SELECT id INTO oa_config_id
  FROM public.oa_configurations
  WHERE account_id = p_account_id
  LIMIT 1;

  -- Nếu tìm thấy, trả về kết quả
  IF oa_config_id IS NOT NULL THEN
    RETURN oa_config_id;
  END IF;

  -- 2. Kiểm tra OA của theme mà tài khoản đang sử dụng
  SELECT oc.id INTO oa_config_id
  FROM public.account_themes at
  JOIN public.oa_configurations oc ON at.theme_id = oc.theme_id
  WHERE at.account_id = p_account_id
  AND at.is_active = TRUE
  LIMIT 1;

  -- Nếu tìm thấy, trả về kết quả
  IF oa_config_id IS NOT NULL THEN
    RETURN oa_config_id;
  END IF;

  -- 3. Sử dụng OA mặc định của hệ thống
  SELECT id INTO oa_config_id
  FROM public.oa_configurations
  WHERE is_system_default = TRUE
  LIMIT 1;

  RETURN oa_config_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Cấp quyền cho các function
-- Create function to update ZNS integration status based on OA configuration
CREATE OR REPLACE FUNCTION public.update_zns_integration_status() RETURNS TRIGGER AS $$
DECLARE
  integration_id UUID;
  token_valid BOOLEAN;
BEGIN
  -- Find ZNS integration for this account
  SELECT id INTO integration_id
  FROM public.integrations
  WHERE account_id = NEW.account_id AND type = 'zalo';

  -- If integration exists, update its status
  IF integration_id IS NOT NULL THEN
    token_valid := public.is_oa_token_valid(NEW.id);

    UPDATE public.integrations
    SET
      status = CASE WHEN token_valid THEN 'connected' ELSE 'not_connected' END,
      metadata = jsonb_set(
        COALESCE(metadata, '{}'::jsonb),
        '{oa_config_id}',
        to_jsonb(NEW.id)
      )
    WHERE id = integration_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update ZNS integration status when OA configuration changes
-- Check if trigger exists before creating it
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'update_zns_integration_status_trigger'
  ) THEN
    CREATE TRIGGER update_zns_integration_status_trigger
    AFTER INSERT OR UPDATE OF access_token, token_expires_at ON public.oa_configurations
    FOR EACH ROW
    EXECUTE FUNCTION public.update_zns_integration_status();
  END IF;
END
$$;

GRANT EXECUTE ON FUNCTION public.can_connect_oa TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.get_oa_config_for_account TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.is_oa_token_valid TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.update_zns_integration_status TO authenticated, service_role;
