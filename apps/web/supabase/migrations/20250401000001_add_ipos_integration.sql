-- Add iPOS to integration_type enum
ALTER TYPE public.integration_type ADD VALUE 'ipos' AFTER 'custom';

-- Create integration_mappings table
CREATE TABLE public.integration_mappings (
  id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  integration_id uuid REFERENCES public.integrations(id) ON DELETE CASCADE,
  resource_type text NOT NULL, -- 'products', 'orders', 'customers', etc.
  source_field text NOT NULL,
  target_field text NOT NULL,
  transform_function text, -- Optional transformation function
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create indexes for integration_mappings
CREATE INDEX idx_integration_mappings_integration_id ON public.integration_mappings(integration_id);
CREATE INDEX idx_integration_mappings_resource_type ON public.integration_mappings(resource_type);

-- Create integration_sync_logs table
CREATE TABLE public.integration_sync_logs (
  id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  integration_id uuid REFERENCES public.integrations(id) ON DELETE CASCADE,
  resource_type text NOT NULL,
  status text NOT NULL, -- 'success', 'error', 'in_progress'
  items_processed integer DEFAULT 0,
  items_created integer DEFAULT 0,
  items_updated integer DEFAULT 0,
  items_failed integer DEFAULT 0,
  error_message text,
  started_at timestamptz DEFAULT now(),
  completed_at timestamptz,
  created_by uuid REFERENCES auth.users(id),
  metadata jsonb DEFAULT '{}'::jsonb
);

-- Create indexes for integration_sync_logs
CREATE INDEX idx_integration_sync_logs_integration_id ON public.integration_sync_logs(integration_id);
CREATE INDEX idx_integration_sync_logs_status ON public.integration_sync_logs(status);

-- Create integration_sync_items table
CREATE TABLE public.integration_sync_items (
  id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
  sync_log_id uuid REFERENCES public.integration_sync_logs(id) ON DELETE CASCADE,
  external_id text NOT NULL, -- ID của item trên hệ thống bên thứ 3
  internal_id text, -- ID của item trên hệ thống của chúng ta
  resource_type text NOT NULL,
  status text NOT NULL, -- 'success', 'error'
  error_message text,
  raw_data jsonb, -- Dữ liệu gốc từ API bên thứ 3
  processed_data jsonb, -- Dữ liệu sau khi xử lý mapping
  created_at timestamptz DEFAULT now()
);

-- Create indexes for integration_sync_items
CREATE INDEX idx_integration_sync_items_sync_log_id ON public.integration_sync_items(sync_log_id);
CREATE INDEX idx_integration_sync_items_external_id ON public.integration_sync_items(external_id);
CREATE INDEX idx_integration_sync_items_internal_id ON public.integration_sync_items(internal_id);

-- RLS policies for integration_mappings
ALTER TABLE public.integration_mappings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view integration mappings"
  ON public.integration_mappings FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.integrations i
      WHERE i.id = integration_id
      AND public.has_role_on_account(i.account_id)
    )
  );

CREATE POLICY "Users can create integration mappings"
  ON public.integration_mappings FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.integrations i
      WHERE i.id = integration_id
      AND public.has_role_on_account(i.account_id)
    )
  );

CREATE POLICY "Users can update integration mappings"
  ON public.integration_mappings FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.integrations i
      WHERE i.id = integration_id
      AND public.has_role_on_account(i.account_id)
    )
  );

CREATE POLICY "Users can delete integration mappings"
  ON public.integration_mappings FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.integrations i
      WHERE i.id = integration_id
      AND public.has_role_on_account(i.account_id)
    )
  );

-- RLS policies for integration_sync_logs
ALTER TABLE public.integration_sync_logs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view integration sync logs"
  ON public.integration_sync_logs FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.integrations i
      WHERE i.id = integration_id
      AND public.has_role_on_account(i.account_id)
    )
  );

CREATE POLICY "Users can create integration sync logs"
  ON public.integration_sync_logs FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.integrations i
      WHERE i.id = integration_id
      AND public.has_role_on_account(i.account_id)
    )
  );

CREATE POLICY "Users can update integration sync logs"
  ON public.integration_sync_logs FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.integrations i
      WHERE i.id = integration_id
      AND public.has_role_on_account(i.account_id)
    )
  );

-- RLS policies for integration_sync_items
ALTER TABLE public.integration_sync_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view integration sync items"
  ON public.integration_sync_items FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.integration_sync_logs l
      JOIN public.integrations i ON i.id = l.integration_id
      WHERE l.id = sync_log_id
      AND public.has_role_on_account(i.account_id)
    )
  );

CREATE POLICY "Users can create integration sync items"
  ON public.integration_sync_items FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.integration_sync_logs l
      JOIN public.integrations i ON i.id = l.integration_id
      WHERE l.id = sync_log_id
      AND public.has_role_on_account(i.account_id)
    )
  );

CREATE POLICY "Users can update integration sync items"
  ON public.integration_sync_items FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.integration_sync_logs l
      JOIN public.integrations i ON i.id = l.integration_id
      WHERE l.id = sync_log_id
      AND public.has_role_on_account(i.account_id)
    )
  );

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.integration_mappings TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.integration_sync_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.integration_sync_items TO authenticated;

-- Add iPOS to available integrations
-- Note: We're not inserting into integration_types as it doesn't exist
-- This would be handled by the application logic instead

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for updated_at
CREATE TRIGGER update_integration_mappings_updated_at
  BEFORE UPDATE ON public.integration_mappings
  FOR EACH ROW
  EXECUTE PROCEDURE public.update_updated_at_column();
