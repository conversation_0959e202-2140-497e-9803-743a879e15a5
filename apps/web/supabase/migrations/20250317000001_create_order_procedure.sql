create or replace function create_order(
  p_account_id uuid,
  p_customer_id uuid,
  p_branch_id uuid,
  p_product_id uuid,
  p_quantity integer,
  p_total_amount numeric,
  p_payment_method text,
  p_status text
) returns json as $$
declare
  v_order_id uuid;
begin
  -- Start transaction
  begin
    -- Insert order
    insert into customer_orders (
      account_id,
      customer_id,
      branch_id,
      product_id,
      quantity,
      total_amount,
      payment_method,
      status
    ) values (
      p_account_id,
      p_customer_id,
      p_branch_id,
      p_product_id,
      p_quantity,
      p_total_amount,
      p_payment_method,
      p_status
    ) returning id into v_order_id;

    -- Update product stock
    update products
    set stock = stock - p_quantity
    where id = p_product_id;

    -- Create activity log
    insert into customer_activities (
      customer_id,
      account_id,
      action,
      details
    ) values (
      p_customer_id,
      p_account_id,
      'create_order',
      jsonb_build_object(
        'order_id', v_order_id,
        'product_id', p_product_id,
        'quantity', p_quantity,
        'total_amount', p_total_amount
      )
    );

    return jsonb_build_object(
      'order_id', v_order_id,
      'status', 'success'
    );
  exception
    when others then
      -- Rollback happens automatically
      return jsonb_build_object(
        'status', 'error',
        'message', SQLERRM
      );
  end;
end;
$$ language plpgsql;