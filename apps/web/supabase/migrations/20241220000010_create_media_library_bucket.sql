-- Create media-library storage bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'media-library',
  'media-library',
  true,
  ********, -- 50MB limit
  ARRAY[
    'image/jpeg',
    'image/png', 
    'image/gif',
    'image/webp',
    'image/svg+xml',
    'video/mp4',
    'video/mpeg',
    'video/quicktime',
    'video/webm',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ]
) ON CONFLICT (id) DO NOTHING;

-- Create storage policies for media-library bucket
CREATE POLICY "Users can view media files from their account" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'media-library' AND (
      -- For temp files: temp/uuid/account_id/file
      (
        (storage.foldername(name))[1] = 'temp' AND
        public.has_role_on_account((storage.foldername(name))[3]::uuid)
      ) OR
      -- For permanent files: account_id/album_id/file
      (
        (storage.foldername(name))[1] != 'temp' AND
        public.has_role_on_account((storage.foldername(name))[1]::uuid)
      )
    )
  );

CREATE POLICY "Users can upload media files to their account" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-library' AND (
      -- For temp files: temp/uuid/account_id/file
      (
        (storage.foldername(name))[1] = 'temp' AND
        public.has_role_on_account((storage.foldername(name))[3]::uuid)
      ) OR
      -- For permanent files: account_id/album_id/file
      (
        (storage.foldername(name))[1] != 'temp' AND
        public.has_role_on_account((storage.foldername(name))[1]::uuid)
      )
    )
  );

CREATE POLICY "Users can update media files in their account" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'media-library' AND (
      -- For temp files: temp/uuid/account_id/file
      (
        (storage.foldername(name))[1] = 'temp' AND
        public.has_role_on_account((storage.foldername(name))[3]::uuid)
      ) OR
      -- For permanent files: account_id/album_id/file
      (
        (storage.foldername(name))[1] != 'temp' AND
        public.has_role_on_account((storage.foldername(name))[1]::uuid)
      )
    )
  );

CREATE POLICY "Users can delete media files from their account" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'media-library' AND (
      -- For temp files: temp/uuid/account_id/file
      (
        (storage.foldername(name))[1] = 'temp' AND
        public.has_role_on_account((storage.foldername(name))[3]::uuid)
      ) OR
      -- For permanent files: account_id/album_id/file
      (
        (storage.foldername(name))[1] != 'temp' AND
        public.has_role_on_account((storage.foldername(name))[1]::uuid)
      )
    )
  );
