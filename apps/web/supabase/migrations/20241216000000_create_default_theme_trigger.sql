-- Migration: Create default theme trigger for new accounts
-- This migration creates a function and trigger to automatically create default themes for new team accounts

-- Function to create default theme for new accounts
create or replace function public.create_default_theme_for_account()
returns trigger as $$
declare
  default_theme_id uuid := '********-0000-4000-a000-************';
begin
  -- Log for debugging
  raise notice 'Checking account type for default theme creation. Account ID: %, Is Personal: %', new.id, new.is_personal_account;

  -- Only create theme if:
  -- 1. This is a team account (not personal account)
  -- 2. No theme exists for this account yet
  if new.is_personal_account = false and not exists (
    select 1 from public.account_themes where account_id = new.id
  ) then
    -- <PERSON><PERSON><PERSON> bảo chỉ có một theme is_active cho mỗi account
    update public.account_themes
    set is_active = false
    where account_id = new.id;

    -- Insert default theme for the new team account
    -- Use account name instead of template name
    insert into public.account_themes (
      account_id,
      template_id,
      name,
      config,
      is_active,
      oa_config_id,
      mini_app_id,
      created_at,
      updated_at
    )
    select
      new.id,
      t.id,
      new.name, -- Use account name instead of template name
      t.config,
      true,
      t.oa_config_id, -- <PERSON><PERSON><PERSON> oa_config_id từ themes
      t.mini_app_id,
      now(),
      now()
    from public.themes t
    where t.id = default_theme_id;

    raise notice 'Default theme created for team account: % with name: %', new.id, new.name;
  end if;
  return new;
end;
$$ language plpgsql security definer;

-- Create trigger to automatically create default theme for new accounts
drop trigger if exists create_default_theme_after_account_creation on public.accounts;

create trigger create_default_theme_after_account_creation
  after insert on public.accounts
  for each row
  execute function public.create_default_theme_for_account();
