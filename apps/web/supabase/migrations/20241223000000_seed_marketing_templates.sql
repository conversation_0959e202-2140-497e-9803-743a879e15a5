-- Migration: Seed Marketing Templates for Education
-- This migration adds marketing templates for education sector to attract parents

-- Insert marketing templates for education
INSERT INTO public.themes (
  id,
  name,
  description,
  thumbnail_url,
  preview_url,
  type,
  category,
  config,
  version,
  created_at,
  updated_at
) VALUES 
(
  'education-hero-landing',
  'Hero Landing - <PERSON><PERSON>',
  'Trang chủ thu hút phụ huynh với hero section và thông tin chương trình',
  '/images/templates/education-hero-landing.jpg',
  '/preview/education-hero-landing',
  'default',
  'landing',
  '{
    "content": [
      {
        "type": "Flex",
        "props": {
          "id": "hero-section",
          "direction": "column",
          "justifyContent": "center",
          "alignItems": "center",
          "gap": "2rem"
        },
        "slots": {
          "items": [
            {
              "type": "Text",
              "props": {
                "id": "hero-title",
                "text": "🌟 Trung Tâm <PERSON>",
                "tag": "h1",
                "size": "3xl",
                "weight": "bold",
                "align": "center",
                "color": "#1e40af",
                "margin": "0 0 16px 0"
              }
            },
            {
              "type": "Text",
              "props": {
                "id": "hero-subtitle",
                "text": "Nơi ươm mầm tài năng - Phát triển toàn diện cho con em bạn",
                "tag": "p",
                "size": "lg",
                "weight": "medium",
                "align": "center",
                "color": "#374151",
                "margin": "0 0 32px 0"
              }
            },
            {
              "type": "Button",
              "props": {
                "id": "hero-cta",
                "label": "📞 Đăng Ký Tư Vấn Miễn Phí",
                "variant": "primary",
                "size": "large",
                "fullWidth": false
              }
            }
          ]
        }
      }
    ],
    "root": {
      "props": {
        "title": "Trung Tâm Giáo Dục Sao Mai",
        "backgroundColor": "#ffffff",
        "textColor": "#1f2937",
        "fontFamily": "Inter, system-ui, sans-serif",
        "primaryColor": "#2563eb",
        "secondaryColor": "#10b981",
        "borderRadius": "8px",
        "spacing": "16px"
      }
    },
    "zones": {}
  }',
  '1.0.0',
  NOW(),
  NOW()
),
(
  'programs-showcase',
  'Giới Thiệu Chương Trình',
  'Showcase các chương trình học với thông tin chi tiết và giá cả',
  '/images/templates/programs-showcase.jpg',
  '/preview/programs-showcase',
  'default',
  'programs',
  '{
    "content": [
      {
        "type": "Text",
        "props": {
          "id": "programs-header",
          "text": "🎓 Các Chương Trình Đào Tạo",
          "tag": "h1",
          "size": "3xl",
          "weight": "bold",
          "align": "center",
          "color": "#1e40af",
          "margin": "0 0 32px 0"
        }
      },
      {
        "type": "Grid",
        "props": {
          "id": "programs-grid",
          "columnCount": 1,
          "rowGap": "2rem"
        },
        "slots": {
          "items": [
            {
              "type": "Card",
              "props": {
                "id": "program-math",
                "title": "🧮 Toán Tư Duy Sáng Tạo",
                "subtitle": "Phát triển tư duy logic và khả năng giải quyết vấn đề",
                "padding": "24px",
                "shadow": "lg",
                "borderRadius": "16px",
                "backgroundColor": "#f0f9ff"
              }
            }
          ]
        }
      }
    ],
    "root": {
      "props": {
        "title": "Chương Trình Đào Tạo",
        "backgroundColor": "#ffffff",
        "textColor": "#1f2937",
        "fontFamily": "Inter, system-ui, sans-serif",
        "primaryColor": "#2563eb",
        "secondaryColor": "#10b981",
        "borderRadius": "8px",
        "spacing": "16px"
      }
    },
    "zones": {}
  }',
  '1.0.0',
  NOW(),
  NOW()
),
(
  'testimonials-social-proof',
  'Phản Hồi Phụ Huynh',
  'Hiển thị testimonials và đánh giá từ phụ huynh để tăng độ tin cậy',
  '/images/templates/testimonials-social-proof.jpg',
  '/preview/testimonials-social-proof',
  'default',
  'testimonials',
  '{
    "content": [
      {
        "type": "Text",
        "props": {
          "id": "testimonials-header",
          "text": "💬 Phụ Huynh Nói Gì Về Chúng Tôi",
          "tag": "h1",
          "size": "3xl",
          "weight": "bold",
          "align": "center",
          "color": "#1e40af",
          "margin": "0 0 32px 0"
        }
      },
      {
        "type": "Swiper",
        "props": {
          "id": "testimonials-swiper",
          "slides": [
            {
              "id": "testimonial-1",
              "content": "\"Con tôi đã tiến bộ rất nhiều sau 3 tháng học tại trung tâm. Các thầy cô rất tận tâm và chuyên nghiệp.\" - Chị Nguyễn Thị Lan",
              "backgroundColor": "#f0f9ff"
            }
          ],
          "autoplay": true,
          "defaultActive": "0"
        }
      }
    ],
    "root": {
      "props": {
        "title": "Phản Hồi Phụ Huynh",
        "backgroundColor": "#ffffff",
        "textColor": "#1f2937",
        "fontFamily": "Inter, system-ui, sans-serif",
        "primaryColor": "#2563eb",
        "secondaryColor": "#10b981",
        "borderRadius": "8px",
        "spacing": "16px"
      }
    },
    "zones": {}
  }',
  '1.0.0',
  NOW(),
  NOW()
)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  thumbnail_url = EXCLUDED.thumbnail_url,
  preview_url = EXCLUDED.preview_url,
  config = EXCLUDED.config,
  updated_at = NOW();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_themes_category_type ON public.themes(category, type);
CREATE INDEX IF NOT EXISTS idx_themes_type_created_at ON public.themes(type, created_at DESC);

-- Add comment
COMMENT ON TABLE public.themes IS 'Theme templates including marketing templates for education sector';
