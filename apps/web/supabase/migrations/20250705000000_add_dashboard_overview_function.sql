-- <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> stored procedure để lấy dữ liệu tổng quan cho dashboard
CREATE OR REPLACE FUNCTION public.get_dashboard_overview(
  p_account_id UUID,
  p_start_date DATE
)
RETURNS TABLE (
  total_visitors BIGINT,
  total_pageviews BIGINT,
  total_orders BIGINT,
  total_revenue NUMERIC,
  conversion_rate NUMERIC
) AS $$
DECLARE
  v_total_visitors BIGINT;
  v_total_pageviews BIGINT;
  v_total_orders BIGINT;
  v_total_revenue NUMERIC;
  v_conversion_rate NUMERIC;
BEGIN
  -- L<PERSON>y tổng số người truy cập
  SELECT COUNT(DISTINCT visitor_id)
  INTO v_total_visitors
  FROM public.analytics_events
  WHERE account_id = p_account_id
    AND created_at::date >= p_start_date
    AND event_type = 'pageview';
  
  -- L<PERSON>y tổng số lượt xem trang
  SELECT COUNT(*)
  INTO v_total_pageviews
  FROM public.analytics_events
  WHERE account_id = p_account_id
    AND created_at::date >= p_start_date
    AND event_type = 'pageview';
  
  -- <PERSON><PERSON>y tổng số đơn hàng
  SELECT COUNT(*)
  INTO v_total_orders
  FROM public.analytics_events
  WHERE account_id = p_account_id
    AND created_at::date >= p_start_date
    AND event_type = 'purchase';
  
  -- Lấy tổng doanh thu
  SELECT COALESCE(SUM((event_data->>'amount')::numeric), 0)
  INTO v_total_revenue
  FROM public.analytics_events
  WHERE account_id = p_account_id
    AND created_at::date >= p_start_date
    AND event_type = 'purchase';
  
  -- Tính tỷ lệ chuyển đổi
  IF v_total_visitors > 0 THEN
    v_conversion_rate := (v_total_orders::numeric / v_total_visitors) * 100;
  ELSE
    v_conversion_rate := 0;
  END IF;
  
  -- Trả về kết quả
  RETURN QUERY SELECT
    v_total_visitors,
    v_total_pageviews,
    v_total_orders,
    v_total_revenue,
    v_conversion_rate;
END;
$$ LANGUAGE plpgsql;
