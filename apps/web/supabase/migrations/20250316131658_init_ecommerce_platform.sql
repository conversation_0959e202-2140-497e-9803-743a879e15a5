set search_path to public;

-- <PERSON><PERSON><PERSON> danh mục sản phẩm
create table public.categories
(
    id          uuid primary key default gen_random_uuid(),
    account_id  uuid not null references public.accounts (id) on delete cascade,
    name        text not null,
    description text,
    image_url        text,
    parent_id   uuid references public.categories (id) on delete set null,
    created_at  timestamptz      default now(),
    updated_at  timestamptz      default now()
);

alter table public.categories
    enable row level security;
-- Thêm policies mới
-- Policy cho SELECT - Cho phép đọc nếu user thuộc account
create policy "Users can view categories in their account" on public.categories
    for select to authenticated
    using (public.has_role_on_account(account_id));

-- Policy cho INSERT/UPDATE/DELETE - <PERSON><PERSON>u cầu quyền categories.manage
create policy "Users can manage categories with permission" on public.categories
    for all using (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'categories.manage'::public.app_permissions)
    )
    with check (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'categories.manage'::public.app_permissions)
    );

create index idx_categories_parent_id on public.categories (parent_id);


-- Bảng products (xóa stock và thêm chính sách đọc công khai)
create type product_type as enum ('physical', 'digital', 'service');

create table public.products
(
    id               uuid primary key      default gen_random_uuid(),
    account_id       uuid         not null references public.accounts (id) on delete cascade,
    category_id      uuid references public.categories (id),
    name             text         not null check (length(name) <= 255), -- Thêm ràng buộc độ dài
    description      text,
    type             product_type not null default 'physical',
    compare_at_price numeric      not null check (compare_at_price >= 0),
    price            numeric      not null check (price >= 0),
    sku              text,
    barcode          text,
    status           text                  default 'active' check (status in ('active', 'inactive', 'draft')),

    -- Physical product specific fields
    weight           numeric check (weight >= 0),
    dimensions       jsonb,                                             -- Store as {length: number, width: number, height: number}

    -- Tax and pricing
    tax_rate         numeric check (tax_rate >= 0 and tax_rate <= 100),

    -- Media
    image_url        text,
    image_urls       text[],

    -- Metadata and external references
    external_id      text,
    metadata         jsonb                 default '{}'::jsonb,         -- For categories, custom fields, etc.

    -- Tracking
    created_at       timestamptz           default now(),
    updated_at       timestamptz           default now(),
    created_by       uuid references auth.users (id),
    updated_by       uuid references auth.users (id)
);

-- Add indexes for common queries
create index idx_products_account_id on public.products (account_id);
create index idx_products_category_id on public.products (category_id);
create index idx_products_sku on public.products (sku) where sku is not null;
create index idx_products_status on public.products (status);

-- Add trigger for updating timestamps
create trigger set_products_timestamp
    before update
    on public.products
    for each row
execute procedure trigger_set_timestamps();

-- Add trigger for user tracking
create trigger set_products_user_tracking
    before insert or update
    on public.products
    for each row
execute procedure public.trigger_set_user_tracking();

-- Comments for documentation
comment on table public.products is 'Stores product information across all types (physical, digital, service)';
comment on column public.products.type is 'Product type: physical, digital, or service';
comment on column public.products.dimensions is 'Product dimensions in JSON format: {length, width, height}';
comment on column public.products.metadata is 'Additional product metadata in JSON format';

alter table public.products
    enable row level security;

-- Thêm policies mới
-- Policy cho SELECT - Cho phép đọc nếu user thuộc account
create policy "Users can view products in their account" on public.products
    for select to authenticated
    using (public.has_role_on_account(account_id));

-- Policy cho INSERT/UPDATE/DELETE - Yêu cầu quyền products.manage
create policy "Users can manage products with permission" on public.products
    for all using (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'products.manage'::public.app_permissions)
    )
    with check (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'products.manage'::public.app_permissions)
    );

-- Bảng chi nhánh
create table public.branches
(
    id         uuid primary key default gen_random_uuid(),
    account_id uuid not null references public.accounts (id) on delete cascade,
    name       text not null,
    address    text,
    phone      text,
    is_active  boolean          default true, -- Thêm trường is_active
    location text,
    created_at timestamptz      default now(),
    updated_at timestamptz      default now()
);

alter table public.branches
    enable row level security;

-- Policy cho SELECT - Cho phép đọc nếu user thuộc account
create policy "Users can view branches in their account" on public.branches
    for select to authenticated
    using (public.has_role_on_account(account_id));

-- Policy cho INSERT/UPDATE/DELETE - Yêu cầu quyền branches.manage
create policy "Users can manage branches with permission" on public.branches
    for all using (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'branches.manage'::public.app_permissions)
    )
    with check (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'branches.manage'::public.app_permissions)
    );

-- Add timestamps trigger
create trigger set_timestamp
    before update
    on public.branches
    for each row
execute function public.trigger_set_timestamps();

-- 1. Migration cho branch_products
-- Tạo bảng trung gian để liên kết sản phẩm với chi nhánh:
-- sql
-- File: 20250322100000_create_branch_products.sql
create table if not exists public.branch_products
(
    id         uuid primary key default extensions.uuid_generate_v4(),
    branch_id  uuid                          not null references public.branches (id) on delete cascade,
    product_id uuid                          not null references public.products (id) on delete cascade,
    is_active  boolean          default true not null,
    created_at timestamptz      default now(),
    updated_at timestamptz      default now(),
    created_by uuid references auth.users,
    updated_by uuid references auth.users,
    unique (branch_id, product_id)
);

comment on table public.branch_products is 'Links products to branches with activation status';
comment on column public.branch_products.branch_id is 'The branch where the product is available';
comment on column public.branch_products.product_id is 'The product linked to the branch';
comment on column public.branch_products.is_active is 'Whether the product is active at this branch';

-- Enable RLS
alter table public.branch_products
    enable row level security;

-- Revoke default privileges
revoke all on public.branch_products from authenticated, service_role;

-- Grant permissions
grant select, insert, update, delete on public.branch_products to authenticated, service_role;

-- RLS Policies
create policy "Users can manage branch_products for their account" on public.branch_products
    for all to authenticated
    using (
    exists (select 1
            from public.accounts a
                     join public.branches b on b.account_id = a.id
            where b.id = branch_products.branch_id
              and public.has_role_on_account(a.id))
    )
    with check (
    exists (select 1
            from public.accounts a
                     join public.branches b on b.account_id = a.id
            where b.id = branch_products.branch_id
              and public.has_permission(auth.uid(), a.id, 'products.manage'::public.app_permissions))
    );

-- Indexes
create index idx_branch_products_branch_id on public.branch_products (branch_id);
create index idx_branch_products_product_id on public.branch_products (product_id);

-- Timestamps trigger
create trigger set_branch_products_timestamps
    before insert or update
    on public.branch_products
    for each row
execute function public.trigger_set_timestamps();

-- User tracking trigger
create trigger set_branch_products_user_tracking
    before insert or update
    on public.branch_products
    for each row
execute function public.trigger_set_user_tracking();
-- 2. Migration cho product_attributes
-- Tạo bảng để lưu thuộc tính của sản phẩm:
-- sql
-- File: 20250322100001_create_product_attributes.sql
create table if not exists public.product_attributes
(
    id             uuid primary key default extensions.uuid_generate_v4(),
    product_id     uuid not null references public.products (id) on delete cascade,
    name           text not null,              -- e.g., "Size", "Color"
    value          text not null,              -- e.g., "M", "Red"
    price_modifier numeric          default 0, -- Additional price for this attribute
    created_at     timestamptz      default now(),
    updated_at     timestamptz      default now(),
    created_by     uuid references auth.users,
    updated_by     uuid references auth.users,
    unique (product_id, name, value)
);

comment on table public.product_attributes is 'Attributes for products (e.g., size, color)';
comment on column public.product_attributes.product_id is 'The product this attribute belongs to';
comment on column public.product_attributes.name is 'The attribute name (e.g., Size)';
comment on column public.product_attributes.value is 'The attribute value (e.g., M)';
comment on column public.product_attributes.price_modifier is 'Additional price for this attribute';

-- Enable RLS
alter table public.product_attributes
    enable row level security;

-- Revoke default privileges
revoke all on public.product_attributes from authenticated, service_role;

-- Grant permissions
grant select, insert, update, delete on public.product_attributes to authenticated, service_role;

-- RLS Policies
create policy "Users can manage product_attributes for their account" on public.product_attributes
    for all to authenticated
    using (
    exists (select 1
            from public.accounts a
                     join public.products p on p.account_id = a.id
            where p.id = product_attributes.product_id
              and public.has_role_on_account(a.id))
    )
    with check (
    exists (select 1
            from public.accounts a
                     join public.products p on p.account_id = a.id
            where p.id = product_attributes.product_id
              and public.has_permission(auth.uid(), a.id, 'products.manage'::public.app_permissions))
    );

-- Indexes
create index idx_product_attributes_product_id on public.product_attributes (product_id);

-- Triggers
create trigger set_product_attributes_timestamps
    before insert or update
    on public.product_attributes
    for each row
execute function public.trigger_set_timestamps();

create trigger set_product_attributes_user_tracking
    before insert or update
    on public.product_attributes
    for each row
execute function public.trigger_set_user_tracking();
-- 3. Migration cho inventory
-- Tạo bảng để quản lý tồn kho:
-- sql
-- File: 20250322100002_create_inventory.sql
create table if not exists public.inventory
(
    id             uuid primary key default extensions.uuid_generate_v4(),
    branch_id      uuid    not null references public.branches (id) on delete cascade,
    product_id     uuid    not null references public.products (id) on delete cascade,
    attribute_id   uuid    references public.product_attributes (id) on delete set null,
    stock          integer not null default 0 check (stock >= 0),
    reserved_stock integer not null default 0 check (reserved_stock >= 0),
    created_at     timestamptz      default now(),
    updated_at     timestamptz      default now(),
    created_by     uuid references auth.users,
    updated_by     uuid references auth.users,
    unique (branch_id, product_id, attribute_id)
);

comment on table public.inventory is 'Inventory management for products by branch and attributes';
comment on column public.inventory.branch_id is 'The branch this inventory belongs to';
comment on column public.inventory.product_id is 'The product this inventory tracks';
comment on column public.inventory.attribute_id is 'The attribute variant (optional)';
comment on column public.inventory.stock is 'Available stock';
comment on column public.inventory.reserved_stock is 'Stock reserved for pending orders';

-- Enable RLS
alter table public.inventory
    enable row level security;

-- Revoke default privileges
revoke all on public.inventory from authenticated, service_role;

-- Grant permissions
grant select, insert, update, delete on public.inventory to authenticated, service_role;

-- RLS Policies
create policy "Users can manage inventory for their account" on public.inventory
    for all to authenticated
    using (
    exists (select 1
            from public.accounts a
                     join public.branches b on b.account_id = a.id
            where b.id = inventory.branch_id
              and public.has_role_on_account(a.id))
    )
    with check (
    exists (select 1
            from public.accounts a
                     join public.branches b on b.account_id = a.id
            where b.id = inventory.branch_id
              and public.has_permission(auth.uid(), a.id, 'products.manage'::public.app_permissions))
    );

-- Indexes
create index idx_inventory_branch_id on public.inventory (branch_id);
create index idx_inventory_product_id on public.inventory (product_id);
create index idx_inventory_attribute_id on public.inventory (attribute_id);

-- Triggers
create trigger set_inventory_timestamps
    before insert or update
    on public.inventory
    for each row
execute function public.trigger_set_timestamps();

create trigger set_inventory_user_tracking
    before insert or update
    on public.inventory
    for each row
execute function public.trigger_set_user_tracking();



-- Ensure customer_orders exists and add missing fields
create table if not exists public.customer_orders
(
    id                uuid primary key default gen_random_uuid(),
    account_id        uuid                               not null references public.accounts (id) on delete cascade,
    customer_id       uuid                               references auth.users (id) on delete set null,
    branch_id         uuid                               references public.branches (id) on delete set null,
    subtotal          numeric                            not null default 0,
    discount_amount   numeric                            not null default 0,
    total_amount      numeric                            not null,
    payment_method    text,
    status            text             default 'pending' not null,
    voucher_id        uuid,
    webhook_processed boolean          default false,
    webhook_data      jsonb,
    created_at        timestamptz      default now(),
    updated_at        timestamptz      default now(),
    created_by        uuid references auth.users,
    updated_by        uuid references auth.users
);

-- Enable RLS
alter table public.customer_orders
    enable row level security;

-- Policy cho SELECT - User có thể xem orders của họ hoặc có quyền orders.manage
create policy "Users can view orders" on public.customer_orders
    for select to authenticated
    using (
    customer_id = auth.uid() -- User có thể xem orders của chính họ
        OR
    (
        public.has_role_on_account(account_id) AND -- User thuộc account
        public.has_permission(auth.uid(), account_id,
                              'orders.manage'::public.app_permissions) -- Có quyền quản lý orders
        )
    );

-- Policy cho INSERT - Chỉ team members mới được tạo order
create policy "Users can create orders" on public.customer_orders
    for insert to authenticated
    with check (
    public.has_role_on_account(account_id) -- User phải là thành viên của team
    );

-- Policy cho UPDATE - Chỉ người có quyền orders.manage mới được update
create policy "Users can update orders with permission" on public.customer_orders
    for update to authenticated
    using (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'orders.manage'::public.app_permissions)
    )
    with check (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'orders.manage'::public.app_permissions)
    );

-- Policy cho DELETE - Chỉ người có quyền orders.manage mới được delete
create policy "Users can delete orders with permission" on public.customer_orders
    for delete to authenticated
    using (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'orders.manage'::public.app_permissions)
    );

-- Bảng items của đơn hàng
create table public.customer_order_items
(
    id                 uuid primary key default gen_random_uuid(),
    order_id           uuid    not null references public.customer_orders (id) on delete cascade,
    product_id         uuid    not null references public.products (id),
    attribute_id       uuid    references public.product_attributes (id) on delete set null,
    quantity           integer not null,
    price              numeric not null,
    original_price     numeric,
    flash_sale_id      uuid,
    discount_percentage numeric(5,2),
    created_at         timestamptz      default now(),
    updated_at         timestamptz      default now(),
    created_by         uuid references auth.users,
    updated_by         uuid references auth.users,
    unique (order_id, product_id, attribute_id)
);

-- RLS cho customer_order_items
alter table public.customer_order_items
    enable row level security;

-- Tạo policy mới
create policy "Users can read their own order items or as manager" on public.customer_order_items
    for select to authenticated
    using (
    exists (select 1
            from public.customer_orders
            where customer_orders.id = customer_order_items.order_id
              and (
                customer_orders.customer_id = auth.uid() -- User xem items của orders của họ
                    OR
                public.has_role_on_account(customer_orders.account_id, 'orders.manage') -- Hoặc có role orders.manage
                ))
    );


-- Thêm policy cho INSERT - user có role trên account tạo được items cho order của họ
create policy "Users can create items for their own orders" on public.customer_order_items
    for insert to authenticated
    with check (
    exists (select 1
            from public.customer_orders
            where customer_orders.id = customer_order_items.order_id
              and customer_orders.customer_id = auth.uid()
              and public.has_role_on_account(customer_orders.account_id))
    );

-- Policy cho UPDATE/DELETE - chỉ manager mới được sửa/xóa
create policy "Managers can manage order items" on public.customer_order_items
    for update using (
    exists (select 1
            from public.customer_orders
            where customer_orders.id = customer_order_items.order_id
              and public.has_role_on_account(customer_orders.account_id, 'orders.manage'))
    )
    with check (
    exists (select 1
            from public.customer_orders
            where customer_orders.id = customer_order_items.order_id
              and public.has_role_on_account(customer_orders.account_id, 'orders.manage'))
    );

create policy "Managers can delete order items" on public.customer_order_items
    for delete using (
    exists (select 1
            from public.customer_orders
            where customer_orders.id = customer_order_items.order_id
              and public.has_role_on_account(customer_orders.account_id, 'orders.manage'))
    );


-- Indexes
create index idx_customer_orders_account_id on public.customer_orders (account_id);
create index idx_customer_orders_branch_id on public.customer_orders (branch_id);
create index idx_customer_order_items_order_id on public.customer_order_items (order_id);
-- Triggers
create trigger set_customer_orders_timestamps
    before insert or update
    on public.customer_orders
    for each row
execute function public.trigger_set_timestamps();

create trigger set_customer_orders_user_tracking
    before insert or update
    on public.customer_orders
    for each row
execute function public.trigger_set_user_tracking();

-- 5. Hàm và Trigger quản lý Inventory khi đặt hàng
-- Tạo hàm để xử lý đơn hàng và cập nhật tồn kho:
-- sql
-- File: 20250322100004_inventory_order_logic.sql


-- Cập nhật hàm create_order_with_inventory (hỗ trợ Flash Sale và Voucher)
create or replace function public.create_order_with_inventory(
    p_account_id uuid,
    p_customer_id uuid,
    p_branch_id uuid,
    p_items jsonb,
    p_subtotal numeric,
    p_discount numeric,
    p_total_amount numeric,
    p_payment_method text,
    p_status text default 'pending',
    p_voucher_code text default null,
    p_voucher_id uuid default null,
    p_voucher_discount numeric default null,
    p_metadata jsonb default null,
    p_webhook_url text default null
) returns jsonb
    set search_path = public, extensions
as
$$
declare
    v_order_id uuid;
    v_item jsonb;
    v_product_id uuid;
    v_attribute_id uuid;
    v_quantity integer;
    v_price numeric;
    v_original_price numeric;
    v_flash_sale_id uuid;
    v_discount_percentage numeric;
    v_available_stock integer;
    v_default_branch_id uuid;
    v_voucher_id uuid := p_voucher_id;
    v_voucher_discount numeric := p_voucher_discount;
begin
    -- Nếu branch_id là null, lấy branch mặc định đầu tiên của account
    if p_branch_id is null then
        select id into v_default_branch_id
        from public.branches
        where account_id = p_account_id
        order by created_at asc
        limit 1;

        if v_default_branch_id is null then
            return jsonb_build_object(
                'status', 'error',
                'message', 'No branch found for this account'
            );
        end if;

        p_branch_id := v_default_branch_id;
    end if;

    -- Process voucher if code is provided but ID is not
    if p_voucher_code is not null and p_voucher_id is null then
        -- Get voucher details
        select id, discount_value into v_voucher_id, v_voucher_discount
        from public.vouchers
        where code = p_voucher_code
        and account_id = p_account_id
        and status = 'active'
        and start_date <= now()
        and end_date > now()
        and (max_uses is null or uses_count < max_uses);

        if v_voucher_id is null then
            return jsonb_build_object(
                'status', 'error',
                'message', 'Invalid or expired voucher code'
            );
        end if;

        -- Update voucher usage count
        update public.vouchers
        set uses_count = uses_count + 1
        where id = v_voucher_id;
    end if;

    -- Bỏ qua việc kiểm tra tồn kho để cho phép tạo đơn hàng ngay cả khi không đủ tồn kho
    -- Chỉ kiểm tra xem sản phẩm có active không
    for v_item in select * from jsonb_array_elements(p_items)
        loop
            v_product_id := (v_item ->> 'product_id')::uuid;

            if not exists (select 1
                           from public.branch_products
                           where branch_id = p_branch_id
                             and product_id = v_product_id
                             and is_active = true) then
                return jsonb_build_object(
                    'status', 'error',
                    'message', format('Product %s is not active at branch %s', v_product_id, p_branch_id)
                );
            end if;

            -- Bỏ qua việc kiểm tra tồn kho
            -- Chỉ lấy thông tin tồn kho để sử dụng sau này
            select stock - reserved_stock
            into v_available_stock
            from public.inventory
            where branch_id = p_branch_id
              and product_id = v_product_id
              and (attribute_id = (v_item ->> 'attribute_id')::uuid or
                   (attribute_id is null and (v_item ->> 'attribute_id') is null))
                for update;
        end loop;

    -- Tạo đơn hàng
    insert into public.customer_orders (
        account_id,
        customer_id,
        branch_id,
        subtotal,
        discount_amount,
        total_amount,
        payment_method,
        status,
        voucher_id,
        metadata,
        webhook_url
    ) values (
        p_account_id,
        p_customer_id,
        p_branch_id,
        p_subtotal,
        p_discount,
        p_total_amount,
        p_payment_method,
        p_status,
        v_voucher_id,
        p_metadata,
        p_webhook_url
    )
    returning id into v_order_id;

    -- Thêm các sản phẩm vào đơn hàng
    for v_item in select * from jsonb_array_elements(p_items)
        loop
            v_product_id := (v_item ->> 'product_id')::uuid;
            v_attribute_id := (v_item ->> 'attribute_id')::uuid;
            v_quantity := (v_item ->> 'quantity')::integer;
            v_price := (v_item ->> 'price')::numeric;
            v_original_price := (v_item ->> 'original_price')::numeric;
            v_flash_sale_id := (v_item ->> 'flash_sale_id')::uuid;
            v_discount_percentage := (v_item ->> 'discount_percentage')::numeric;

            insert into public.customer_order_items (
                order_id,
                product_id,
                attribute_id,
                quantity,
                price,
                original_price,
                flash_sale_id,
                discount_percentage
            ) values (
                v_order_id,
                v_product_id,
                v_attribute_id,
                v_quantity,
                v_price,
                v_original_price,
                v_flash_sale_id,
                v_discount_percentage
            );

            update public.inventory
            set reserved_stock = reserved_stock + v_quantity,
                updated_at = now(),
                updated_by = auth.uid()
            where branch_id = p_branch_id
              and product_id = v_product_id
              and (attribute_id = v_attribute_id or
                   (attribute_id is null and v_attribute_id is null));

            -- Update flash sale quantity sold if applicable
            if v_flash_sale_id is not null then
                update public.flash_sale_products
                set quantity_sold = coalesce(quantity_sold, 0) + v_quantity
                where flash_sale_id = v_flash_sale_id
                and product_id = v_product_id;
            end if;
        end loop;

    -- Record voucher redemption if applicable
    if v_voucher_id is not null then
        insert into public.voucher_redemptions (
            voucher_id,
            order_id,
            customer_id,
            discount_amount
        ) values (
            v_voucher_id,
            v_order_id,
            p_customer_id,
            p_discount
        );
    end if;

    return jsonb_build_object(
        'status', 'success',
        'order_id', v_order_id
    );
exception
    when others then
        return jsonb_build_object(
            'status', 'error',
            'message', SQLERRM
        );
end;
$$ language plpgsql;


-- Trigger để tự động cập nhật total_amount của order
create or replace function public.update_order_total()
    returns trigger as
$$
begin
    update public.customer_orders
    set total_amount = (select sum(price * quantity)
                        from public.customer_order_items
                        where order_id = new.order_id)
    where id = new.order_id;
    return new;
end;
$$ language plpgsql;

create trigger update_order_total_trigger
    after insert or update or delete
    on public.customer_order_items
    for each row
execute function public.update_order_total();


-- Bảng hoạt động khách hàng
create table public.customer_activities
(
    id          uuid primary key default gen_random_uuid(),
    customer_id uuid not null references auth.users (id) on delete cascade,
    account_id  uuid not null references public.accounts (id) on delete cascade,
    action      text not null,
    details     jsonb,
    created_at  timestamptz      default now()
);

alter table public.customer_activities
    enable row level security;
create policy "Owner can manage customer_activities" on public.customer_activities
    for all to authenticated
    using (public.has_role_on_account(account_id));


-- Cấp quyền cho hàm create_order_with_inventory
GRANT EXECUTE ON FUNCTION public.create_order_with_inventory(
    uuid,
    uuid,
    uuid,
    jsonb,
    numeric,
    numeric,
    numeric,
    text,
    text,
    text,
    uuid,
    numeric,
    jsonb,
    text
) TO authenticated, anon, service_role;

-- Thêm cột metadata vào bảng customer_orders
ALTER TABLE public.customer_orders ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT NULL;

-- Thêm cột webhook_url vào bảng customer_orders
ALTER TABLE public.customer_orders ADD COLUMN IF NOT EXISTS webhook_url TEXT DEFAULT NULL;

-- Cập nhật chính sách RLS cho bảng inventory
ALTER TABLE public.inventory DISABLE ROW LEVEL SECURITY;

-- Cấp quyền cho bảng inventory
GRANT ALL ON public.inventory TO authenticated, anon, service_role;

-- Cấp quyền cho hàm create_order_with_inventory
GRANT EXECUTE ON FUNCTION public.create_order_with_inventory(
    uuid,
    uuid,
    uuid,
    jsonb,
    numeric,
    numeric,
    numeric,
    text,
    text,
    text,
    uuid,
    numeric,
    jsonb,
    text
) TO authenticated, anon, service_role;

-- Thêm cột metadata vào bảng customer_orders
ALTER TABLE public.customer_orders ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT NULL;

-- Thêm cột webhook_url vào bảng customer_orders
ALTER TABLE public.customer_orders ADD COLUMN IF NOT EXISTS webhook_url TEXT DEFAULT NULL;

-- Cập nhật chính sách RLS cho bảng inventory
ALTER TABLE public.inventory DISABLE ROW LEVEL SECURITY;

-- Cấp quyền cho bảng inventory
GRANT ALL ON public.inventory TO authenticated, anon, service_role;
