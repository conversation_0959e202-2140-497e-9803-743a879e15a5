-- Fix instructors table schema to match the complete education platform
-- Add missing columns that were not created by the original migration

-- Add missing columns to instructors table
ALTER TABLE instructors 
ADD COLUMN IF NOT EXISTS role VARCHAR(50),
ADD COLUMN IF NOT EXISTS experience_years INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS hire_date DATE;

-- Update status constraint to include 'on_leave'
ALTER TABLE instructors 
DROP CONSTRAINT IF EXISTS instructors_status_check;

ALTER TABLE instructors 
ADD CONSTRAINT instructors_status_check 
CHECK (status IN ('active', 'inactive', 'on_leave'));

-- Create index for new columns
CREATE INDEX IF NOT EXISTS idx_instructors_role ON instructors(role);
CREATE INDEX IF NOT EXISTS idx_instructors_hire_date ON instructors(hire_date);

-- Update existing instructors with default values
UPDATE instructors 
SET 
  role = 'teacher',
  experience_years = 0,
  hire_date = CURRENT_DATE
WHERE role IS NULL OR experience_years IS NULL OR hire_date IS NULL;
