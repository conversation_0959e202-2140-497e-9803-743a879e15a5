-- Create media_albums table
CREATE TABLE IF NOT EXISTS media_albums (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  description TEXT,
  album_type VARCHAR(50) DEFAULT 'general' CHECK (album_type IN ('general', 'event', 'class', 'activity')),
  privacy_level VARCHAR(50) DEFAULT 'staff_only' CHECK (privacy_level IN ('public', 'parents_only', 'staff_only', 'private')),
  cover_image_id UUID,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id)
);

-- Create media_files table
CREATE TABLE IF NOT EXISTS media_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  album_id UUID REFERENCES media_albums(id) ON DELETE SET NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  file_type VARCHAR(50) NOT NULL CHECK (file_type IN ('image', 'video', 'document')),
  file_url TEXT NOT NULL,
  thumbnail_url TEXT,
  file_size BIGINT,
  mime_type VARCHAR(100),
  original_filename VARCHAR(255),
  metadata JSONB DEFAULT '{}',
  tags TEXT[],
  is_featured BOOLEAN DEFAULT FALSE,
  privacy_level VARCHAR(50) DEFAULT 'staff_only' CHECK (privacy_level IN ('public', 'parents_only', 'staff_only', 'private')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_media_albums_account_id ON media_albums(account_id);
CREATE INDEX IF NOT EXISTS idx_media_albums_album_type ON media_albums(album_type);
CREATE INDEX IF NOT EXISTS idx_media_albums_privacy_level ON media_albums(privacy_level);
CREATE INDEX IF NOT EXISTS idx_media_albums_created_at ON media_albums(created_at);

CREATE INDEX IF NOT EXISTS idx_media_files_account_id ON media_files(account_id);
CREATE INDEX IF NOT EXISTS idx_media_files_album_id ON media_files(album_id);
CREATE INDEX IF NOT EXISTS idx_media_files_file_type ON media_files(file_type);
CREATE INDEX IF NOT EXISTS idx_media_files_privacy_level ON media_files(privacy_level);
CREATE INDEX IF NOT EXISTS idx_media_files_created_at ON media_files(created_at);
CREATE INDEX IF NOT EXISTS idx_media_files_tags ON media_files USING GIN(tags);

-- Add foreign key constraint for cover_image_id
ALTER TABLE media_albums 
ADD CONSTRAINT fk_media_albums_cover_image 
FOREIGN KEY (cover_image_id) REFERENCES media_files(id) ON DELETE SET NULL;

-- Create RLS policies for media_albums
ALTER TABLE media_albums ENABLE ROW LEVEL SECURITY;

-- Policy for SELECT: Users can view albums from their account
CREATE POLICY "Users can view albums from their account" ON media_albums
  FOR SELECT USING (
    public.has_role_on_account(account_id)
  );

-- Policy for INSERT: Users can create albums in their account
CREATE POLICY "Users can create albums in their account" ON media_albums
  FOR INSERT WITH CHECK (
    public.has_role_on_account(account_id)
  );

-- Policy for UPDATE: Users can update albums in their account
CREATE POLICY "Users can update albums in their account" ON media_albums
  FOR UPDATE USING (
    public.has_role_on_account(account_id)
  );

-- Policy for DELETE: Users can delete albums in their account
CREATE POLICY "Users can delete albums in their account" ON media_albums
  FOR DELETE USING (
    public.has_role_on_account(account_id)
  );

-- Create RLS policies for media_files
ALTER TABLE media_files ENABLE ROW LEVEL SECURITY;

-- Policy for SELECT: Users can view files from their account
CREATE POLICY "Users can view files from their account" ON media_files
  FOR SELECT USING (
    public.has_role_on_account(account_id)
  );

-- Policy for INSERT: Users can create files in their account
CREATE POLICY "Users can create files in their account" ON media_files
  FOR INSERT WITH CHECK (
    public.has_role_on_account(account_id)
  );

-- Policy for UPDATE: Users can update files in their account
CREATE POLICY "Users can update files in their account" ON media_files
  FOR UPDATE USING (
    public.has_role_on_account(account_id)
  );

-- Policy for DELETE: Users can delete files in their account
CREATE POLICY "Users can delete files in their account" ON media_files
  FOR DELETE USING (
    public.has_role_on_account(account_id)
  );

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_media_albums_updated_at 
  BEFORE UPDATE ON media_albums 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_media_files_updated_at
  BEFORE UPDATE ON media_files
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create temp_media_files table for temporary uploads
CREATE TABLE IF NOT EXISTS temp_media_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  temp_path TEXT NOT NULL,
  url TEXT NOT NULL,
  thumbnail_url TEXT,
  file_type VARCHAR(50) NOT NULL CHECK (file_type IN ('image', 'video', 'document')),
  file_size BIGINT,
  mime_type VARCHAR(100),
  original_filename VARCHAR(255),
  expires_at TIMESTAMPTZ NOT NULL,
  is_moved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for temp_media_files
CREATE INDEX IF NOT EXISTS idx_temp_media_files_account_id ON temp_media_files(account_id);
CREATE INDEX IF NOT EXISTS idx_temp_media_files_expires_at ON temp_media_files(expires_at);
CREATE INDEX IF NOT EXISTS idx_temp_media_files_temp_path ON temp_media_files(temp_path);

-- Create RLS policies for temp_media_files
ALTER TABLE temp_media_files ENABLE ROW LEVEL SECURITY;

-- Policy for temp_media_files: Users can manage their own temp files
CREATE POLICY "Users can manage their own temp media files" ON temp_media_files
  FOR ALL USING (
    public.has_role_on_account(account_id)
  );
