-- <PERSON><PERSON><PERSON> tra và cài đặt pg_cron extension
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- <PERSON><PERSON><PERSON> bảo schema cron tồn tại
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_namespace WHERE nspname = 'cron') THEN
    CREATE SCHEMA cron;
  END IF;
END $$;

-- Tạo function save_temp_theme_to_account
CREATE OR REPLACE FUNCTION public.save_temp_theme_to_account(
  p_temp_theme_id UUID,
  p_account_id UUID,
  p_mini_app_id TEXT
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_theme_id UUID;
  v_temp_theme RECORD;
BEGIN
  -- Bắt đầu giao dịch
  BEGIN
    -- Kiểm tra quyền sở hữu tài khoản
    IF NOT EXISTS (
      SELECT 1 FROM public.accounts WHERE id = p_account_id
    ) THEN
      RAISE EXCEPTION 'Account % not found', p_account_id;
    END IF;

    -- Lấy temp theme
    SELECT * INTO v_temp_theme
    FROM public.temp_themes
    WHERE id = p_temp_theme_id;

    IF NOT FOUND THEN
      RAISE EXCEPTION 'Temp theme % not found', p_temp_theme_id;
    END IF;

    -- Kiểm tra quyền sở hữu temp theme
    IF v_temp_theme.account_id != p_account_id THEN
      RAISE EXCEPTION 'Account % does not have permission to access temp theme %', p_account_id, p_temp_theme_id;
    END IF;

    -- Nếu chỉnh sửa theme hiện có
    IF v_temp_theme.account_theme_id IS NOT NULL THEN
      UPDATE public.account_themes
      SET config = v_temp_theme.config,
          updated_at = NOW()
      WHERE id = v_temp_theme.account_theme_id
      RETURNING id INTO v_theme_id;

      IF NOT FOUND THEN
        RAISE EXCEPTION 'Failed to update account theme %', v_temp_theme.account_theme_id;
      END IF;
    ELSE
      -- Tạo theme mới
      INSERT INTO public.account_themes (
        account_id,
        template_id,
        name,
        config,
        is_active,
        created_at,
        updated_at
      )
      VALUES (
        p_account_id,
        (v_temp_theme.config->>'template')::UUID,
        'My Theme',
        v_temp_theme.config,
        TRUE,
        NOW(),
        NOW()
      )
      RETURNING id INTO v_theme_id;
    END IF;

    -- Cập nhật custom_app với theme mới
    UPDATE public.custom_apps
    SET theme_id = v_theme_id
    WHERE mini_app_id = p_mini_app_id
    AND account_id = p_account_id;

    IF NOT FOUND THEN
      RAISE EXCEPTION 'Custom app % not found for account %', p_mini_app_id, p_account_id;
    END IF;

    -- Xóa temp theme
    DELETE FROM public.temp_themes
    WHERE id = p_temp_theme_id;

    RETURN v_theme_id;

  EXCEPTION WHEN OTHERS THEN
    -- Log lỗi chi tiết
    RAISE NOTICE 'Error in save_temp_theme_to_account: %', SQLERRM;
    RAISE EXCEPTION 'Failed to save temp theme: %', SQLERRM;
  END;
END;
$$;

-- Tạo index cho expires_at
CREATE INDEX IF NOT EXISTS idx_temp_themes_expires_at ON public.temp_themes (expires_at);

-- Tạo function cleanup_expired_temp_themes
CREATE OR REPLACE FUNCTION public.cleanup_expired_temp_themes()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_deleted_count INTEGER := 0;
  v_batch_size INTEGER := 1000; -- Xóa theo batch 1000 bản ghi
BEGIN
  LOOP
    -- Sử dụng CTE để chọn các ID cần xóa
    WITH to_delete AS (
      SELECT id
      FROM public.temp_themes
      WHERE expires_at < NOW()
      LIMIT v_batch_size
    ),
    deleted AS (
      DELETE FROM public.temp_themes
      WHERE id IN (SELECT id FROM to_delete)
      RETURNING 1
    )
    SELECT COUNT(*) INTO v_deleted_count
    FROM deleted;

    -- Log số lượng bản ghi bị xóa
    RAISE NOTICE 'Deleted % expired temp themes in this batch', v_deleted_count;

    -- Thoát nếu không còn bản ghi nào để xóa
    EXIT WHEN v_deleted_count = 0;
  END LOOP;

  RAISE NOTICE 'Finished cleaning up expired temp themes';
END;
$$;

-- Xóa cron job cũ nếu tồn tại
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'cleanup-temp-themes') THEN
    PERFORM cron.unschedule('cleanup-temp-themes');
  END IF;
END $$;

-- Tạo cron job mới (chạy mỗi 4 giờ)
SELECT cron.schedule(
  'cleanup-temp-themes',
  '0 */4 * * *', -- Chạy mỗi 4 giờ
  $$SELECT public.cleanup_expired_temp_themes()$$
);
