-- Migration để thêm bảng analytics_events và các view liên quan

-- Bảng lưu trữ tất cả các sự kiện analytics
CREATE TABLE IF NOT EXISTS public.analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL,
  theme_id UUID,
  event_type TEXT NOT NULL,
  event_data JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID,
  visitor_id TEXT,
  device_type TEXT,
  source TEXT DEFAULT 'zalo_miniapp'
);

-- Index để tối ưu truy vấn
CREATE INDEX IF NOT EXISTS idx_analytics_events_account_id ON public.analytics_events(account_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_theme_id ON public.analytics_events(theme_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_type ON public.analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_created_at ON public.analytics_events(created_at);
CREATE INDEX IF NOT EXISTS idx_analytics_events_source ON public.analytics_events(source);

-- RLS policy
ALTER TABLE public.analytics_events ENABLE ROW LEVEL SECURITY;

-- Policy cho SELECT - Cho phép người dùng xem dữ liệu của account mà họ thuộc về
CREATE POLICY "analytics_events_select_policy" ON public.analytics_events
  FOR SELECT TO authenticated
  USING (public.has_role_on_account(account_id));

-- Policy cho INSERT - Cho phép người dùng đã xác thực chèn dữ liệu nếu họ là thành viên của account
CREATE POLICY "analytics_events_insert_policy" ON public.analytics_events
  FOR INSERT TO authenticated
  WITH CHECK (public.has_role_on_account(account_id));

-- Policy cho INSERT - Cho phép service role chèn dữ liệu không cần kiểm tra
CREATE POLICY "analytics_events_service_role_policy" ON public.analytics_events
  FOR ALL TO service_role
  USING (true)
  WITH CHECK (true);

-- Cho phép anon insert (sẽ kiểm tra bằng API)
CREATE POLICY "analytics_events_anon_insert_policy" ON public.analytics_events
  FOR INSERT TO anon
  WITH CHECK (true);

-- Tạo stored procedure để chèn dữ liệu vào bảng analytics_events
CREATE OR REPLACE FUNCTION insert_analytics_event(
  account_id UUID,
  theme_id UUID,
  event_type TEXT,
  event_data JSONB,
  visitor_id TEXT,
  user_id UUID,
  device_type TEXT,
  source TEXT
) RETURNS JSONB
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
  inserted_id UUID;
  result JSONB;
BEGIN
  INSERT INTO analytics_events (
    account_id,
    theme_id,
    event_type,
    event_data,
    visitor_id,
    user_id,
    device_type,
    source
  ) VALUES (
    account_id,
    theme_id,
    event_type,
    event_data,
    visitor_id,
    user_id,
    device_type,
    source
  )
  RETURNING id INTO inserted_id;

  result := jsonb_build_object(
    'success', true,
    'id', inserted_id
  );

  RETURN result;
END;
$$;

-- View tổng hợp lượt xem trang theo ngày
CREATE OR REPLACE VIEW public.daily_pageviews AS
SELECT
  account_id,
  theme_id,
  DATE(created_at) AS date,
  source,
  COUNT(*) AS pageviews_count,
  COUNT(DISTINCT visitor_id) AS visitors_count,
  COUNT(DISTINCT CASE WHEN device_type = 'desktop' THEN visitor_id END) AS desktop_visitors,
  COUNT(DISTINCT CASE WHEN device_type = 'mobile' THEN visitor_id END) AS mobile_visitors,
  COUNT(DISTINCT CASE WHEN device_type = 'tablet' THEN visitor_id END) AS tablet_visitors,
  COUNT(CASE WHEN device_type = 'desktop' THEN 1 END) AS desktop_pageviews,
  COUNT(CASE WHEN device_type = 'mobile' THEN 1 END) AS mobile_pageviews,
  COUNT(CASE WHEN device_type = 'tablet' THEN 1 END) AS tablet_pageviews
FROM
  public.analytics_events
WHERE
  event_type = 'pageview'
GROUP BY
  account_id, theme_id, DATE(created_at), source;

-- View tổng hợp sự kiện sản phẩm theo ngày
CREATE OR REPLACE VIEW public.daily_product_events AS
SELECT
  account_id,
  theme_id,
  DATE(created_at) AS date,
  source,
  event_data->>'productId' AS product_id,
  COUNT(CASE WHEN event_type = 'product_view' THEN 1 END) AS views,
  COUNT(CASE WHEN event_type = 'add_to_cart' THEN 1 END) AS add_to_carts,
  COUNT(CASE WHEN event_type = 'purchase' THEN 1 END) AS purchases
FROM
  public.analytics_events
WHERE
  event_type IN ('product_view', 'add_to_cart', 'purchase')
  AND event_data->>'productId' IS NOT NULL
GROUP BY
  account_id, theme_id, DATE(created_at), source, event_data->>'productId';

-- View tổng hợp đơn hàng theo ngày
CREATE OR REPLACE VIEW public.daily_orders AS
SELECT
  account_id,
  theme_id,
  DATE(created_at) AS date,
  source,
  COUNT(*) AS orders_count,
  SUM((event_data->>'amount')::numeric) AS revenue,
  AVG((event_data->>'amount')::numeric) AS average_order_value,
  COUNT(DISTINCT visitor_id) AS customers_count
FROM
  public.analytics_events
WHERE
  event_type = 'purchase'
GROUP BY
  account_id, theme_id, DATE(created_at), source;

-- Thêm dữ liệu mẫu cho analytics_events
INSERT INTO public.analytics_events (
  account_id,
  theme_id,
  event_type,
  event_data,
  created_at,
  visitor_id,
  device_type,
  source
)
SELECT
  a.id as account_id,
  t.id as theme_id,
  -- Lưu giá trị event_type vào một biến
  CASE WHEN random() < 0.7 THEN 'pageview'
       WHEN random() < 0.9 THEN 'product_view'
       ELSE 'purchase' END as event_type,
  -- Sử dụng cùng điều kiện để tạo event_data
  CASE
    WHEN random() < 0.7 THEN -- pageview
      jsonb_build_object(
        'pagePath', '/product/' || floor(random() * 100)::text,
        'pageTitle', 'Product ' || floor(random() * 100)::text
      )
    WHEN random() < 0.9 THEN -- product_view
      jsonb_build_object(
        'productId', floor(random() * 100)::text
      )
    ELSE -- purchase
      jsonb_build_object(
        'orderId', 'order_' || floor(random() * 1000)::text,
        'amount', floor(random() * 1000000)::text,
        'productId', floor(random() * 100)::text
      )
  END as event_data,
  (CURRENT_DATE - (floor(random() * 30)::int || ' days')::INTERVAL) +
  (floor(random() * 24)::int || ' hours')::INTERVAL +
  (floor(random() * 60)::int || ' minutes')::INTERVAL as created_at,
  'v_' || md5(random()::text) as visitor_id,
  CASE WHEN random() < 0.7 THEN 'mobile'
       WHEN random() < 0.9 THEN 'desktop'
       ELSE 'tablet' END as device_type,
  CASE WHEN random() < 0.5 THEN 'web' ELSE 'zalo_miniapp' END as source
FROM
  public.accounts a
  CROSS JOIN public.account_themes t
  CROSS JOIN generate_series(1, 100) i
WHERE
  t.account_id = a.id
LIMIT 1000;
