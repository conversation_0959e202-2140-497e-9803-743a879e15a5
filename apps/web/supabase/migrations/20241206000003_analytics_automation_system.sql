-- Advanced Analytics & Smart Automation System Migration
-- Phase 4: Analytics, Reports, and Automation

-- 1. Attendance Analytics table
CREATE TABLE IF NOT EXISTS attendance_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  program_id UUID NOT NULL REFERENCES programs(id) ON DELETE CASCADE,
  learner_id UUID REFERENCES learners(id) ON DELETE CASCADE,
  analysis_date DATE NOT NULL,
  analysis_type VARCHAR(50) DEFAULT 'daily' CHECK (analysis_type IN ('daily', 'weekly', 'monthly', 'term')),
  attendance_rate DECIMAL(5,2), -- Percentage 0.00 to 100.00
  total_sessions INTEGER DEFAULT 0,
  present_sessions INTEGER DEFAULT 0,
  absent_sessions INTEGER DEFAULT 0,
  late_sessions INTEGER DEFAULT 0,
  excused_sessions INTEGER DEFAULT 0,
  streak_present INTEGER DEFAULT 0, -- Consecutive present days
  streak_absent INTEGER DEFAULT 0, -- Consecutive absent days
  patterns JSONB, -- Attendance patterns analysis
  risk_score DECIMAL(3,2), -- 0.00 to 1.00 (risk of dropping out)
  recommendations JSONB, -- AI-generated recommendations
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(account_id, program_id, learner_id, analysis_date, analysis_type)
);

-- 2. Attendance Reports table
CREATE TABLE IF NOT EXISTS attendance_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  report_name VARCHAR(255) NOT NULL,
  report_type VARCHAR(50) DEFAULT 'standard' CHECK (report_type IN ('standard', 'custom', 'automated', 'scheduled')),
  report_scope VARCHAR(50) DEFAULT 'program' CHECK (report_scope IN ('account', 'program', 'learner', 'instructor')),
  scope_id UUID, -- ID of the scope (program_id, learner_id, etc.)
  date_range JSONB NOT NULL, -- {start_date, end_date, period}
  filters JSONB, -- Report filters and criteria
  report_data JSONB, -- Generated report data
  charts_config JSONB, -- Chart configurations
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'generating', 'completed', 'failed', 'expired')),
  generated_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ,
  file_url TEXT, -- URL to exported file (PDF, Excel)
  share_token VARCHAR(100) UNIQUE, -- For sharing reports
  is_public BOOLEAN DEFAULT false,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Automation Rules table
CREATE TABLE IF NOT EXISTS automation_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  rule_name VARCHAR(255) NOT NULL,
  rule_type VARCHAR(50) DEFAULT 'attendance' CHECK (rule_type IN ('attendance', 'behavior', 'performance', 'communication')),
  trigger_conditions JSONB NOT NULL, -- Conditions that trigger the rule
  trigger_frequency VARCHAR(20) DEFAULT 'immediate' CHECK (trigger_frequency IN ('immediate', 'daily', 'weekly', 'monthly')),
  actions JSONB NOT NULL, -- Actions to perform when triggered
  target_audience JSONB, -- Who receives the actions (parents, instructors, admin)
  is_active BOOLEAN DEFAULT true,
  priority INTEGER DEFAULT 1, -- 1 = highest, 5 = lowest
  last_triggered_at TIMESTAMPTZ,
  trigger_count INTEGER DEFAULT 0,
  success_count INTEGER DEFAULT 0,
  failure_count INTEGER DEFAULT 0,
  metadata JSONB,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Automation Logs table
CREATE TABLE IF NOT EXISTS automation_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  rule_id UUID NOT NULL REFERENCES automation_rules(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  trigger_data JSONB NOT NULL, -- Data that triggered the rule
  actions_performed JSONB, -- Actions that were performed
  target_users JSONB, -- Users who were targeted
  execution_status VARCHAR(20) DEFAULT 'pending' CHECK (execution_status IN ('pending', 'executing', 'completed', 'failed', 'partial')),
  execution_time_ms INTEGER, -- Execution time in milliseconds
  error_details JSONB, -- Error information if failed
  results JSONB, -- Results of the actions
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. Notification Templates table
CREATE TABLE IF NOT EXISTS notification_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  template_name VARCHAR(255) NOT NULL,
  template_type VARCHAR(50) DEFAULT 'attendance' CHECK (template_type IN ('attendance', 'behavior', 'achievement', 'reminder', 'alert')),
  delivery_method VARCHAR(20) DEFAULT 'zns' CHECK (delivery_method IN ('zns', 'email', 'sms', 'push', 'in_app')),
  subject_template TEXT,
  content_template TEXT NOT NULL,
  variables JSONB, -- Available template variables
  styling JSONB, -- Template styling options
  is_active BOOLEAN DEFAULT true,
  usage_count INTEGER DEFAULT 0,
  last_used_at TIMESTAMPTZ,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. Predictive Insights table
CREATE TABLE IF NOT EXISTS predictive_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  insight_type VARCHAR(50) DEFAULT 'attendance_risk' CHECK (insight_type IN ('attendance_risk', 'performance_trend', 'behavior_pattern', 'engagement_level')),
  target_type VARCHAR(20) DEFAULT 'learner' CHECK (target_type IN ('learner', 'program', 'instructor', 'account')),
  target_id UUID NOT NULL, -- ID of the target
  prediction_data JSONB NOT NULL, -- Prediction results and confidence
  confidence_score DECIMAL(3,2), -- 0.00 to 1.00
  risk_level VARCHAR(20) DEFAULT 'low' CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
  recommendations JSONB, -- Recommended actions
  valid_until DATE, -- When this prediction expires
  is_acknowledged BOOLEAN DEFAULT false,
  acknowledged_by UUID REFERENCES auth.users(id),
  acknowledged_at TIMESTAMPTZ,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 7. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_attendance_analytics_account_program ON attendance_analytics(account_id, program_id);
CREATE INDEX IF NOT EXISTS idx_attendance_analytics_learner_date ON attendance_analytics(learner_id, analysis_date);
CREATE INDEX IF NOT EXISTS idx_attendance_analytics_type ON attendance_analytics(analysis_type);
CREATE INDEX IF NOT EXISTS idx_attendance_analytics_risk ON attendance_analytics(risk_score) WHERE risk_score > 0.7;

CREATE INDEX IF NOT EXISTS idx_attendance_reports_account_id ON attendance_reports(account_id);
CREATE INDEX IF NOT EXISTS idx_attendance_reports_status ON attendance_reports(status);
CREATE INDEX IF NOT EXISTS idx_attendance_reports_type ON attendance_reports(report_type);
CREATE INDEX IF NOT EXISTS idx_attendance_reports_created ON attendance_reports(created_at);

CREATE INDEX IF NOT EXISTS idx_automation_rules_account_id ON automation_rules(account_id);
CREATE INDEX IF NOT EXISTS idx_automation_rules_active ON automation_rules(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_automation_rules_type ON automation_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_automation_rules_frequency ON automation_rules(trigger_frequency);

CREATE INDEX IF NOT EXISTS idx_automation_logs_rule_id ON automation_logs(rule_id);
CREATE INDEX IF NOT EXISTS idx_automation_logs_account_id ON automation_logs(account_id);
CREATE INDEX IF NOT EXISTS idx_automation_logs_status ON automation_logs(execution_status);
CREATE INDEX IF NOT EXISTS idx_automation_logs_created ON automation_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_notification_templates_account_id ON notification_templates(account_id);
CREATE INDEX IF NOT EXISTS idx_notification_templates_type ON notification_templates(template_type);
CREATE INDEX IF NOT EXISTS idx_notification_templates_active ON notification_templates(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_predictive_insights_account_id ON predictive_insights(account_id);
CREATE INDEX IF NOT EXISTS idx_predictive_insights_target ON predictive_insights(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_predictive_insights_risk ON predictive_insights(risk_level);
CREATE INDEX IF NOT EXISTS idx_predictive_insights_valid ON predictive_insights(valid_until);

-- 8. RLS Policies
ALTER TABLE attendance_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE predictive_insights ENABLE ROW LEVEL SECURITY;

-- Attendance Analytics policies
CREATE POLICY "Users can view analytics in their accounts" ON attendance_analytics
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage analytics in their accounts" ON attendance_analytics
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Attendance Reports policies
CREATE POLICY "Users can view reports in their accounts" ON attendance_reports
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    ) OR is_public = true
  );

CREATE POLICY "Users can manage reports in their accounts" ON attendance_reports
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Automation Rules policies
CREATE POLICY "Users can view automation rules in their accounts" ON automation_rules
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage automation rules in their accounts" ON automation_rules
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Automation Logs policies
CREATE POLICY "Users can view automation logs in their accounts" ON automation_logs
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

-- Notification Templates policies
CREATE POLICY "Users can view templates in their accounts" ON notification_templates
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage templates in their accounts" ON notification_templates
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Predictive Insights policies
CREATE POLICY "Users can view insights in their accounts" ON predictive_insights
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage insights in their accounts" ON predictive_insights
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships
      WHERE user_id = auth.uid()
      AND account_role IN ('owner', 'member')
    )
  );

-- 9. Analytics Functions
CREATE OR REPLACE FUNCTION calculate_attendance_analytics(
  p_account_id UUID,
  p_program_id UUID DEFAULT NULL,
  p_learner_id UUID DEFAULT NULL,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL,
  p_analysis_type VARCHAR(50) DEFAULT 'daily'
) RETURNS JSON AS $$
DECLARE
  v_start_date DATE;
  v_end_date DATE;
  v_total_sessions INTEGER := 0;
  v_present_sessions INTEGER := 0;
  v_absent_sessions INTEGER := 0;
  v_late_sessions INTEGER := 0;
  v_excused_sessions INTEGER := 0;
  v_attendance_rate DECIMAL(5,2);
  v_patterns JSONB;
  v_risk_score DECIMAL(3,2);
  v_recommendations JSONB;
BEGIN
  -- Set default date range if not provided
  v_start_date := COALESCE(p_start_date, CURRENT_DATE - INTERVAL '30 days');
  v_end_date := COALESCE(p_end_date, CURRENT_DATE);

  -- Calculate attendance statistics
  SELECT
    COUNT(*) as total,
    COUNT(*) FILTER (WHERE status = 'present') as present,
    COUNT(*) FILTER (WHERE status = 'absent') as absent,
    COUNT(*) FILTER (WHERE status = 'late') as late,
    COUNT(*) FILTER (WHERE status = 'excused') as excused
  INTO v_total_sessions, v_present_sessions, v_absent_sessions, v_late_sessions, v_excused_sessions
  FROM attendance
  WHERE account_id = p_account_id
    AND (p_program_id IS NULL OR program_id = p_program_id)
    AND (p_learner_id IS NULL OR learner_id = p_learner_id)
    AND session_date BETWEEN v_start_date AND v_end_date;

  -- Calculate attendance rate
  v_attendance_rate := CASE
    WHEN v_total_sessions > 0 THEN
      ROUND((v_present_sessions::DECIMAL / v_total_sessions::DECIMAL) * 100, 2)
    ELSE 0
  END;

  -- Analyze patterns (simplified)
  v_patterns := json_build_object(
    'most_absent_day', (
      SELECT EXTRACT(DOW FROM session_date)
      FROM attendance
      WHERE account_id = p_account_id
        AND (p_program_id IS NULL OR program_id = p_program_id)
        AND (p_learner_id IS NULL OR learner_id = p_learner_id)
        AND status = 'absent'
        AND session_date BETWEEN v_start_date AND v_end_date
      GROUP BY EXTRACT(DOW FROM session_date)
      ORDER BY COUNT(*) DESC
      LIMIT 1
    ),
    'attendance_trend', CASE
      WHEN v_attendance_rate >= 95 THEN 'excellent'
      WHEN v_attendance_rate >= 85 THEN 'good'
      WHEN v_attendance_rate >= 75 THEN 'average'
      WHEN v_attendance_rate >= 60 THEN 'poor'
      ELSE 'critical'
    END
  );

  -- Calculate risk score
  v_risk_score := CASE
    WHEN v_attendance_rate >= 95 THEN 0.1
    WHEN v_attendance_rate >= 85 THEN 0.3
    WHEN v_attendance_rate >= 75 THEN 0.5
    WHEN v_attendance_rate >= 60 THEN 0.7
    ELSE 0.9
  END;

  -- Generate recommendations
  v_recommendations := json_build_array(
    CASE
      WHEN v_attendance_rate < 75 THEN 'Liên hệ phụ huynh để thảo luận về tình hình vắng mặt'
      WHEN v_late_sessions > v_total_sessions * 0.2 THEN 'Nhắc nhở về thời gian đến lớp'
      WHEN v_attendance_rate >= 95 THEN 'Khen ngợi sự tham gia tích cực'
      ELSE 'Tiếp tục theo dõi tình hình học tập'
    END
  );

  RETURN json_build_object(
    'success', true,
    'data', json_build_object(
      'analysis_period', json_build_object(
        'start_date', v_start_date,
        'end_date', v_end_date,
        'analysis_type', p_analysis_type
      ),
      'statistics', json_build_object(
        'total_sessions', v_total_sessions,
        'present_sessions', v_present_sessions,
        'absent_sessions', v_absent_sessions,
        'late_sessions', v_late_sessions,
        'excused_sessions', v_excused_sessions,
        'attendance_rate', v_attendance_rate
      ),
      'patterns', v_patterns,
      'risk_score', v_risk_score,
      'recommendations', v_recommendations
    )
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to calculate attendance analytics'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on analytics functions
GRANT EXECUTE ON FUNCTION calculate_attendance_analytics(UUID, UUID, UUID, DATE, DATE, VARCHAR) TO authenticated, service_role;

-- 10. Function to generate attendance report
CREATE OR REPLACE FUNCTION generate_attendance_report(
  p_account_id UUID,
  p_report_name VARCHAR(255),
  p_report_scope VARCHAR(50),
  p_scope_id UUID DEFAULT NULL,
  p_start_date DATE DEFAULT NULL,
  p_end_date DATE DEFAULT NULL,
  p_filters JSONB DEFAULT NULL
) RETURNS JSON AS $$
DECLARE
  v_report_id UUID;
  v_start_date DATE;
  v_end_date DATE;
  v_report_data JSONB;
  v_charts_config JSONB;
BEGIN
  -- Set default date range
  v_start_date := COALESCE(p_start_date, CURRENT_DATE - INTERVAL '30 days');
  v_end_date := COALESCE(p_end_date, CURRENT_DATE);

  -- Generate report data based on scope
  IF p_report_scope = 'program' THEN
    SELECT json_build_object(
      'summary', json_build_object(
        'total_students', COUNT(DISTINCT learner_id),
        'total_sessions', COUNT(DISTINCT session_date),
        'overall_attendance_rate', ROUND(AVG(CASE WHEN status = 'present' THEN 100.0 ELSE 0.0 END), 2)
      ),
      'daily_breakdown', json_agg(
        json_build_object(
          'date', session_date,
          'total_students', COUNT(DISTINCT learner_id),
          'present', COUNT(*) FILTER (WHERE status = 'present'),
          'absent', COUNT(*) FILTER (WHERE status = 'absent'),
          'late', COUNT(*) FILTER (WHERE status = 'late'),
          'rate', ROUND((COUNT(*) FILTER (WHERE status = 'present')::DECIMAL / COUNT(*)::DECIMAL) * 100, 2)
        ) ORDER BY session_date
      )
    ) INTO v_report_data
    FROM attendance
    WHERE account_id = p_account_id
      AND (p_scope_id IS NULL OR program_id = p_scope_id)
      AND session_date BETWEEN v_start_date AND v_end_date
    GROUP BY session_date;
  ELSE
    -- Default report structure
    v_report_data := json_build_object(
      'message', 'Report scope not implemented yet',
      'scope', p_report_scope
    );
  END IF;

  -- Generate charts configuration
  v_charts_config := json_build_object(
    'attendance_trend', json_build_object(
      'type', 'line',
      'title', 'Xu hướng điểm danh',
      'data_key', 'daily_breakdown'
    ),
    'status_distribution', json_build_object(
      'type', 'pie',
      'title', 'Phân bố trạng thái',
      'data_key', 'summary'
    )
  );

  -- Create report record
  INSERT INTO attendance_reports (
    account_id,
    report_name,
    report_type,
    report_scope,
    scope_id,
    date_range,
    filters,
    report_data,
    charts_config,
    status,
    generated_at,
    expires_at,
    created_by
  ) VALUES (
    p_account_id,
    p_report_name,
    'automated',
    p_report_scope,
    p_scope_id,
    json_build_object('start_date', v_start_date, 'end_date', v_end_date),
    p_filters,
    v_report_data,
    v_charts_config,
    'completed',
    NOW(),
    NOW() + INTERVAL '30 days',
    auth.uid()
  ) RETURNING id INTO v_report_id;

  RETURN json_build_object(
    'success', true,
    'report_id', v_report_id,
    'report_data', v_report_data,
    'charts_config', v_charts_config,
    'message', 'Report generated successfully'
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to generate attendance report'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
