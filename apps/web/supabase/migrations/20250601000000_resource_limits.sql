-- Migration để thiết lập cơ sở dữ liệu cho việc kiểm tra giới hạn tài nguyên

-- Tạo bảng để lưu trữ thống kê sử dụng tài nguyên
CREATE TABLE IF NOT EXISTS public.account_usage_stats (
  account_id UUID REFERENCES public.accounts(id) ON DELETE CASCADE PRIMARY KEY,
  counters JSONB DEFAULT '{}'::JSONB,
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tạo materialized view để tính toán số lượng sản phẩm
CREATE MATERIALIZED VIEW IF NOT EXISTS public.account_product_counts AS
SELECT
  account_id,
  COUNT(*) AS product_count
FROM
  public.products
GROUP BY
  account_id;

-- Tạo materialized view để tính toán số lượng ZNS
CREATE MATERIALIZED VIEW IF NOT EXISTS public.account_zns_counts AS
SELECT
  account_id,
  COUNT(*) AS zns_count
FROM
  public.zns_usage
WHERE
  status = 'success'
GROUP BY
  account_id;

-- Tạo index để tối ưu truy vấn
CREATE INDEX IF NOT EXISTS idx_account_product_counts ON public.account_product_counts(account_id);
CREATE INDEX IF NOT EXISTS idx_account_zns_counts ON public.account_zns_counts(account_id);

-- Tạo function để tăng counter
CREATE OR REPLACE FUNCTION public.increment_account_counter(
  p_account_id UUID,
  p_counter_name TEXT,
  p_increment INTEGER DEFAULT 1
) RETURNS VOID AS $$
BEGIN
  -- Tạo bản ghi nếu chưa tồn tại
  BEGIN
    INSERT INTO public.account_usage_stats (account_id, counters)
    VALUES (p_account_id, jsonb_build_object(p_counter_name, p_increment))
    ON CONFLICT (account_id) DO UPDATE
    SET counters = jsonb_set(
      COALESCE(account_usage_stats.counters, '{}'::jsonb),
      ARRAY[p_counter_name],
      to_jsonb(COALESCE((account_usage_stats.counters->>p_counter_name)::integer, 0) + p_increment)
    ),
    last_updated = NOW();
  EXCEPTION WHEN OTHERS THEN
    -- Nếu có lỗi, ghi log và tiếp tục
    RAISE NOTICE 'Error updating account_usage_stats: %', SQLERRM;
  END;
END;
$$ LANGUAGE plpgsql;

-- Hàm giảm counter
CREATE OR REPLACE FUNCTION public.decrement_account_counter(
  p_account_id UUID,
  p_counter_name TEXT,
  p_decrement INTEGER DEFAULT 1
) RETURNS VOID AS $$
BEGIN
  BEGIN
    PERFORM public.increment_account_counter(p_account_id, p_counter_name, -p_decrement);
  EXCEPTION WHEN OTHERS THEN
    -- Nếu có lỗi, ghi log và tiếp tục
    RAISE NOTICE 'Error decrementing account counter: %', SQLERRM;
  END;
END;
$$ LANGUAGE plpgsql;

-- Tạo trigger cho bảng products
CREATE OR REPLACE FUNCTION public.product_counter_trigger() RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    PERFORM public.increment_account_counter(NEW.account_id, 'products');
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    PERFORM public.decrement_account_counter(OLD.account_id, 'products');
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER product_counter_trigger
AFTER INSERT OR DELETE ON public.products
FOR EACH ROW EXECUTE FUNCTION public.product_counter_trigger();

-- Tạo trigger cho bảng branches
CREATE OR REPLACE FUNCTION public.branch_counter_trigger() RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    PERFORM public.increment_account_counter(NEW.account_id, 'branches');
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    PERFORM public.decrement_account_counter(OLD.account_id, 'branches');
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER branch_counter_trigger
AFTER INSERT OR DELETE ON public.branches
FOR EACH ROW EXECUTE FUNCTION public.branch_counter_trigger();

-- Tạo trigger cho bảng zns_usage
CREATE OR REPLACE FUNCTION public.zns_counter_trigger() RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' AND NEW.status = 'success' THEN
    PERFORM public.increment_account_counter(NEW.account_id, 'zns');
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' AND OLD.status = 'success' THEN
    PERFORM public.decrement_account_counter(OLD.account_id, 'zns');
    RETURN OLD;
  ELSIF TG_OP = 'UPDATE' THEN
    IF OLD.status != 'success' AND NEW.status = 'success' THEN
      PERFORM public.increment_account_counter(NEW.account_id, 'zns');
    ELSIF OLD.status = 'success' AND NEW.status != 'success' THEN
      PERFORM public.decrement_account_counter(NEW.account_id, 'zns');
    END IF;
    RETURN NEW;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER zns_counter_trigger
AFTER INSERT OR DELETE OR UPDATE ON public.zns_usage
FOR EACH ROW EXECUTE FUNCTION public.zns_counter_trigger();

-- Tạo trigger cho bảng account_themes (miniapp)
CREATE OR REPLACE FUNCTION public.miniapp_counter_trigger() RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    PERFORM public.increment_account_counter(NEW.account_id, 'miniapps');
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    PERFORM public.decrement_account_counter(OLD.account_id, 'miniapps');
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER miniapp_counter_trigger
AFTER INSERT OR DELETE ON public.account_themes
FOR EACH ROW EXECUTE FUNCTION public.miniapp_counter_trigger();

-- Tạo function để kiểm tra giới hạn tài nguyên
CREATE OR REPLACE FUNCTION public.can_create_resource(
  p_account_id UUID,
  p_resource_type TEXT
) RETURNS BOOLEAN AS $$
DECLARE
has_sub BOOLEAN;
  current_count INTEGER;
  resource_limit INTEGER;
  limit_field TEXT;
  stats_exists BOOLEAN;
BEGIN
  -- Kiểm tra subscription
SELECT public.has_active_subscription(p_account_id) INTO has_sub;

IF NOT has_sub THEN
    RETURN FALSE;
END IF;

  -- Xác định tên trường giới hạn trong metadata
  limit_field := p_resource_type || '_limit';

  -- Lấy giới hạn từ subscription_items
SELECT (si.metadata->>limit_field)::INTEGER INTO resource_limit
FROM public.subscription_items si
         JOIN public.subscriptions s ON si.subscription_id = s.id
WHERE s.account_id = p_account_id
  AND s.active = TRUE
    LIMIT 1;

-- Ghi log giá trị giới hạn để debug
RAISE NOTICE 'Resource limit for % (account %) is %', p_resource_type, p_account_id, resource_limit;

-- Nếu không có giới hạn hoặc giới hạn là -1 (không giới hạn)
IF resource_limit IS NULL OR resource_limit = -1 THEN
    -- Nếu không có giới hạn được định nghĩa, nhưng có subscription, giả định là không giới hạn
    RETURN TRUE;
END IF;

  -- Kiểm tra xem account_usage_stats có tồn tại cho account này không
SELECT EXISTS(
    SELECT 1 FROM public.account_usage_stats
    WHERE account_id = p_account_id
) INTO stats_exists;

-- Nếu không tồn tại, tạo mới với counter là 0
IF NOT stats_exists THEN
    -- Sử dụng security definer để bỏ qua RLS
    BEGIN
        INSERT INTO public.account_usage_stats (account_id, counters, last_updated)
        VALUES (
          p_account_id,
          jsonb_build_object(p_resource_type, 0),
          NOW()
        );
    EXCEPTION WHEN OTHERS THEN
        -- Nếu có lỗi, ghi log và tiếp tục
        RAISE NOTICE 'Error inserting into account_usage_stats: %', SQLERRM;
    END;

    -- Đặt current_count = 0 vì đây là bản ghi mới
    current_count := 0;
ELSE
    -- Lấy counter hiện tại
SELECT (counters->>p_resource_type)::INTEGER INTO current_count
FROM public.account_usage_stats
WHERE account_id = p_account_id;

-- Nếu không có counter, kiểm tra từ materialized view
IF current_count IS NULL THEN
      CASE p_resource_type
        WHEN 'products' THEN
SELECT COUNT(*) INTO current_count
FROM public.products
WHERE account_id = p_account_id;
WHEN 'branches' THEN
SELECT COUNT(*) INTO current_count
FROM public.branches
WHERE account_id = p_account_id;
WHEN 'zns' THEN
SELECT COUNT(*) INTO current_count
FROM public.zns_usage
WHERE account_id = p_account_id AND status = 'success';
WHEN 'miniapp' THEN
SELECT COUNT(*) INTO current_count
FROM public.account_themes
WHERE account_id = p_account_id;
-- Thêm các loại resource khác ở đây
END CASE;

      -- Nếu vẫn không có, giả định là 0
      current_count := COALESCE(current_count, 0);

      -- Cập nhật counter trong bảng
      PERFORM public.increment_account_counter(p_account_id, p_resource_type, 0);
END IF;
END IF;

  -- Kiểm tra counter với giới hạn
RETURN current_count < resource_limit;
END;
$$ LANGUAGE plpgsql;


-- Tạo function để làm mới thống kê sử dụng tài nguyên
CREATE OR REPLACE FUNCTION public.refresh_usage_stats()
RETURNS VOID AS $$
DECLARE
  account_rec RECORD;
  product_count INTEGER;
  branch_count INTEGER;
  zns_count INTEGER;
  miniapp_count INTEGER;
BEGIN
  -- Refresh materialized views
  REFRESH MATERIALIZED VIEW public.account_product_counts;
  REFRESH MATERIALIZED VIEW public.account_zns_counts;

  -- Cập nhật thống kê cho từng tài khoản
  FOR account_rec IN SELECT id FROM public.accounts LOOP
    -- Đếm số lượng sản phẩm
    SELECT COUNT(*) INTO product_count
    FROM public.products
    WHERE account_id = account_rec.id;

    -- Đếm số lượng chi nhánh
    SELECT COUNT(*) INTO branch_count
    FROM public.branches
    WHERE account_id = account_rec.id;

    -- Đếm số lượng ZNS đã gửi thành công
    SELECT COUNT(*) INTO zns_count
    FROM public.zns_usage
    WHERE account_id = account_rec.id
    AND status = 'success';

    -- Đếm số lượng miniapp
    SELECT COUNT(*) INTO miniapp_count
    FROM public.account_themes
    WHERE account_id = account_rec.id;

    -- Cập nhật hoặc tạo mới bản ghi trong account_usage_stats
    INSERT INTO public.account_usage_stats (account_id, counters, last_updated)
    VALUES (
      account_rec.id,
      jsonb_build_object(
        'products', product_count,
        'branches', branch_count,
        'zns', zns_count,
        'miniapp', miniapp_count
      ),
      NOW()
    )
    ON CONFLICT (account_id) DO UPDATE
    SET counters = jsonb_build_object(
        'products', product_count,
        'branches', branch_count,
        'zns', zns_count,
        'miniapp', miniapp_count
      ),
        last_updated = NOW();
  END LOOP;

  RETURN;
END;
$$ LANGUAGE plpgsql;

-- Tạo function để khởi tạo account_usage_stats với security definer
CREATE OR REPLACE FUNCTION public.initialize_account_usage_stats(
  p_account_id UUID
) RETURNS BOOLEAN
SECURITY DEFINER
SET search_path = ''
AS $$
DECLARE
  stats_exists BOOLEAN;
BEGIN
  -- Kiểm tra xem account_usage_stats đã tồn tại cho account này chưa
  SELECT EXISTS(
    SELECT 1 FROM public.account_usage_stats
    WHERE account_id = p_account_id
  ) INTO stats_exists;

  -- Nếu chưa tồn tại, tạo mới với counters rỗng
  IF NOT stats_exists THEN
    INSERT INTO public.account_usage_stats (account_id, counters, last_updated)
    VALUES (p_account_id, '{}'::jsonb, NOW());
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Cấp quyền cho các function
GRANT EXECUTE ON FUNCTION public.increment_account_counter TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.decrement_account_counter TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.can_create_resource TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.refresh_usage_stats TO service_role;
GRANT EXECUTE ON FUNCTION public.initialize_account_usage_stats TO authenticated, service_role;

-- Cấp quyền cho các bảng
GRANT SELECT ON public.account_usage_stats TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.account_usage_stats TO service_role;
GRANT SELECT ON public.account_product_counts TO authenticated, service_role;
GRANT SELECT ON public.account_zns_counts TO authenticated, service_role;

-- Thiết lập RLS cho bảng account_usage_stats
ALTER TABLE public.account_usage_stats ENABLE ROW LEVEL SECURITY;

-- RLS policy cho account_usage_stats
CREATE POLICY account_usage_stats_select ON public.account_usage_stats
FOR SELECT TO authenticated
USING (
  account_id = auth.uid() OR
  public.has_role_on_account(account_id)
);

-- RLS policy cho INSERT account_usage_stats
CREATE POLICY account_usage_stats_insert ON public.account_usage_stats
FOR INSERT TO authenticated
WITH CHECK (
  account_id = auth.uid() OR
  public.has_role_on_account(account_id)
);

-- RLS policy cho UPDATE account_usage_stats
CREATE POLICY account_usage_stats_update ON public.account_usage_stats
FOR UPDATE TO authenticated
USING (
  account_id = auth.uid() OR
  public.has_role_on_account(account_id)
)
WITH CHECK (
  account_id = auth.uid() OR
  public.has_role_on_account(account_id)
);

-- RLS policy cho DELETE account_usage_stats
CREATE POLICY account_usage_stats_delete ON public.account_usage_stats
FOR DELETE TO authenticated
USING (
  account_id = auth.uid() OR
  public.has_role_on_account(account_id)
);
