-- Photo Attendance System Migration
-- Phase 2: Advanced Smart Attendance Features

-- 1. Learner Photos table for face recognition
CREATE TABLE IF NOT EXISTS learner_photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  learner_id UUID NOT NULL REFERENCES learners(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  photo_url TEXT NOT NULL,
  photo_type VARCHAR(20) DEFAULT 'profile' CHECK (photo_type IN ('profile', 'verification', 'attendance')),
  face_encoding JSONB, -- Store face encoding data for recognition
  is_primary BOOLEAN DEFAULT false,
  quality_score DECIMAL(3,2), -- 0.00 to 1.00
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 2. Photo Attendance Sessions table
CREATE TABLE IF NOT EXISTS photo_attendance_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  program_id UUID NOT NULL REFERENCES programs(id) ON DELETE CASCADE,
  session_date DATE NOT NULL,
  session_time TIME,
  instructor_id UUID REFERENCES instructors(id),
  batch_photo_url TEXT, -- URL of the group photo
  processing_status VARCHAR(20) DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
  total_faces_detected INTEGER DEFAULT 0,
  total_students_recognized INTEGER DEFAULT 0,
  confidence_threshold DECIMAL(3,2) DEFAULT 0.75,
  processing_metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 3. Photo Recognition Results table
CREATE TABLE IF NOT EXISTS photo_recognition_results (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  photo_session_id UUID NOT NULL REFERENCES photo_attendance_sessions(id) ON DELETE CASCADE,
  learner_id UUID REFERENCES learners(id) ON DELETE CASCADE,
  detected_face_box JSONB, -- Bounding box coordinates {x, y, width, height}
  confidence_score DECIMAL(4,3), -- 0.000 to 1.000
  recognition_status VARCHAR(20) DEFAULT 'detected' CHECK (recognition_status IN ('detected', 'recognized', 'verified', 'rejected')),
  manual_verification BOOLEAN DEFAULT NULL,
  verified_by UUID REFERENCES auth.users(id),
  verification_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_learner_photos_learner_id ON learner_photos(learner_id);
CREATE INDEX IF NOT EXISTS idx_learner_photos_account_id ON learner_photos(account_id);
CREATE INDEX IF NOT EXISTS idx_learner_photos_is_primary ON learner_photos(is_primary) WHERE is_primary = true;

CREATE INDEX IF NOT EXISTS idx_photo_attendance_sessions_account_program ON photo_attendance_sessions(account_id, program_id);
CREATE INDEX IF NOT EXISTS idx_photo_attendance_sessions_date ON photo_attendance_sessions(session_date);
CREATE INDEX IF NOT EXISTS idx_photo_attendance_sessions_status ON photo_attendance_sessions(processing_status);

CREATE INDEX IF NOT EXISTS idx_photo_recognition_results_session ON photo_recognition_results(photo_session_id);
CREATE INDEX IF NOT EXISTS idx_photo_recognition_results_learner ON photo_recognition_results(learner_id);
CREATE INDEX IF NOT EXISTS idx_photo_recognition_results_confidence ON photo_recognition_results(confidence_score);

-- 5. RLS Policies
ALTER TABLE learner_photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE photo_attendance_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE photo_recognition_results ENABLE ROW LEVEL SECURITY;

-- Learner Photos policies
CREATE POLICY "Users can view learner photos in their accounts" ON learner_photos
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage learner photos in their accounts" ON learner_photos
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Photo Attendance Sessions policies
CREATE POLICY "Users can view photo sessions in their accounts" ON photo_attendance_sessions
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage photo sessions in their accounts" ON photo_attendance_sessions
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Photo Recognition Results policies
CREATE POLICY "Users can view recognition results in their accounts" ON photo_recognition_results
  FOR SELECT USING (
    photo_session_id IN (
      SELECT id FROM photo_attendance_sessions 
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can manage recognition results in their accounts" ON photo_recognition_results
  FOR ALL USING (
    photo_session_id IN (
      SELECT id FROM photo_attendance_sessions 
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships 
        WHERE user_id = auth.uid() 
        AND account_role IN ('owner', 'member')
      )
    )
  );

-- 6. Functions for photo attendance
CREATE OR REPLACE FUNCTION create_photo_attendance_session(
  p_account_id UUID,
  p_program_id UUID,
  p_session_date DATE,
  p_session_time TIME DEFAULT NULL,
  p_instructor_id UUID DEFAULT NULL,
  p_batch_photo_url TEXT DEFAULT NULL
) RETURNS JSON AS $$
DECLARE
  v_session_id UUID;
  v_enrolled_count INTEGER;
BEGIN
  -- Create photo attendance session
  INSERT INTO photo_attendance_sessions (
    account_id,
    program_id,
    session_date,
    session_time,
    instructor_id,
    batch_photo_url,
    created_by
  ) VALUES (
    p_account_id,
    p_program_id,
    p_session_date,
    p_session_time,
    p_instructor_id,
    p_batch_photo_url,
    auth.uid()
  ) RETURNING id INTO v_session_id;

  -- Get enrolled students count
  SELECT COUNT(*) INTO v_enrolled_count
  FROM enrollments
  WHERE account_id = p_account_id
    AND program_id = p_program_id
    AND status = 'active';

  RETURN json_build_object(
    'success', true,
    'session_id', v_session_id,
    'enrolled_count', v_enrolled_count,
    'message', 'Photo attendance session created successfully'
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create photo attendance session'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Function to update photo session processing status
CREATE OR REPLACE FUNCTION update_photo_session_status(
  p_session_id UUID,
  p_status VARCHAR(20),
  p_total_faces INTEGER DEFAULT NULL,
  p_total_recognized INTEGER DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL
) RETURNS JSON AS $$
BEGIN
  UPDATE photo_attendance_sessions
  SET 
    processing_status = p_status,
    total_faces_detected = COALESCE(p_total_faces, total_faces_detected),
    total_students_recognized = COALESCE(p_total_recognized, total_students_recognized),
    processing_metadata = COALESCE(p_metadata, processing_metadata),
    updated_at = NOW()
  WHERE id = p_session_id;

  IF FOUND THEN
    RETURN json_build_object(
      'success', true,
      'message', 'Photo session status updated successfully'
    );
  ELSE
    RETURN json_build_object(
      'success', false,
      'message', 'Photo session not found'
    );
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to update photo session status'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
