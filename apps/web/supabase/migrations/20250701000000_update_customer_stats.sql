-- Migration để cập nhật thống kê khách hàng

-- Tạo function để cập nhật thông tin khách hàng khi có đơn hàng mới
CREATE OR REPLACE FUNCTION public.update_customer_stats()
RETURNS TRIGGER AS $$
DECLARE
    v_account_id UUID;
    v_customer_id UUID;
    v_total_orders INTEGER;
    v_total_spent NUMERIC;
    v_public_data JSONB;
BEGIN
    -- Lấy thông tin từ đơn hàng
    v_account_id := NEW.account_id;
    v_customer_id := NEW.customer_id;
    
    -- <PERSON><PERSON><PERSON> không có customer_id, không cần cập nhật
    IF v_customer_id IS NULL THEN
        RETURN NEW;
    END IF;
    
    -- Tính tổng số đơn hàng của khách hàng
    SELECT COUNT(*) INTO v_total_orders
    FROM public.customer_orders
    WHERE customer_id = v_customer_id
    AND account_id = v_account_id;
    
    -- T<PERSON>h tổng chi tiêu của khách hàng
    SELECT COALESCE(SUM(total_amount), 0) INTO v_total_spent
    FROM public.customer_orders
    WHERE customer_id = v_customer_id
    AND account_id = v_account_id
    AND status NOT IN ('cancelled', 'refunded');
    
    -- Lấy public_data hiện tại
    SELECT public_data INTO v_public_data
    FROM public.accounts
    WHERE id = v_account_id;
    
    -- Nếu public_data là NULL, tạo mới
    IF v_public_data IS NULL THEN
        v_public_data := '{}'::JSONB;
    END IF;
    
    -- Cập nhật thông tin vào public_data
    v_public_data := jsonb_set(
        v_public_data,
        '{total_orders}',
        to_jsonb(v_total_orders)
    );
    
    v_public_data := jsonb_set(
        v_public_data,
        '{total_spent}',
        to_jsonb(v_total_spent)
    );
    
    -- Cập nhật vào bảng accounts
    UPDATE public.accounts
    SET public_data = v_public_data
    WHERE id = v_account_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Tạo trigger cho bảng customer_orders
DROP TRIGGER IF EXISTS update_customer_stats_trigger ON public.customer_orders;
CREATE TRIGGER update_customer_stats_trigger
AFTER INSERT OR UPDATE OF status, total_amount
ON public.customer_orders
FOR EACH ROW
EXECUTE FUNCTION public.update_customer_stats();

-- Cập nhật thông tin khách hàng hiện có
DO $$
DECLARE
    v_account_id UUID;
    v_customer_id UUID;
    v_total_orders INTEGER;
    v_total_spent NUMERIC;
    v_public_data JSONB;
    r RECORD;
BEGIN
    -- Lấy danh sách tất cả khách hàng
    FOR r IN (
        SELECT DISTINCT customer_id, account_id
        FROM public.customer_orders
        WHERE customer_id IS NOT NULL
    ) LOOP
        v_account_id := r.account_id;
        v_customer_id := r.customer_id;
        
        -- Tính tổng số đơn hàng của khách hàng
        SELECT COUNT(*) INTO v_total_orders
        FROM public.customer_orders
        WHERE customer_id = v_customer_id
        AND account_id = v_account_id;
        
        -- Tính tổng chi tiêu của khách hàng
        SELECT COALESCE(SUM(total_amount), 0) INTO v_total_spent
        FROM public.customer_orders
        WHERE customer_id = v_customer_id
        AND account_id = v_account_id
        AND status NOT IN ('cancelled', 'refunded');
        
        -- Lấy public_data hiện tại
        SELECT public_data INTO v_public_data
        FROM public.accounts
        WHERE id = v_account_id;
        
        -- Nếu public_data là NULL, tạo mới
        IF v_public_data IS NULL THEN
            v_public_data := '{}'::JSONB;
        END IF;
        
        -- Cập nhật thông tin vào public_data
        v_public_data := jsonb_set(
            v_public_data,
            '{total_orders}',
            to_jsonb(v_total_orders)
        );
        
        v_public_data := jsonb_set(
            v_public_data,
            '{total_spent}',
            to_jsonb(v_total_spent)
        );
        
        -- Cập nhật vào bảng accounts
        UPDATE public.accounts
        SET public_data = v_public_data
        WHERE id = v_account_id;
    END LOOP;
END;
$$;
