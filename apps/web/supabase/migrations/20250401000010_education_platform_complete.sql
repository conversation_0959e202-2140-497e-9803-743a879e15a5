-- Complete Education Platform Migration
-- This single migration contains all education platform functionality
-- Replaces all previous education-related migrations for clean maintenance

-- ============================================================================
-- PART 1: ADD EDUCATION SETTINGS TO ACCOUNTS TABLE
-- ============================================================================

-- Add education_settings column to accounts table if it doesn't exist
ALTER TABLE public.accounts
ADD COLUMN IF NOT EXISTS education_settings JSONB DEFAULT '{}'::jsonb;

-- Add index for education_settings
CREATE INDEX IF NOT EXISTS idx_accounts_education_settings ON public.accounts USING GIN (education_settings);

-- ============================================================================
-- PART 2: CORE EDUCATION TABLES
-- ============================================================================

-- Programs table (main education programs)
CREATE TABLE IF NOT EXISTS public.programs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    program_type VARCHAR(50) NOT NULL CHECK (program_type IN ('nursery', 'preschool', 'kindergarten', 'primary', 'secondary')),
    age_group VARCHAR(50),
    duration_weeks INTEGER,
    capacity INTEGER NOT NULL DEFAULT 20,
    description TEXT,
    schedule JSONB DEFAULT '{}'::jsonb,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Instructors table (teachers and staff)
CREATE TABLE IF NOT EXISTS public.instructors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    employee_code VARCHAR(50),
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    role VARCHAR(50) DEFAULT 'teacher' CHECK (role IN ('teacher', 'assistant', 'principal', 'admin')),
    specialization VARCHAR(255),
    qualifications TEXT[] DEFAULT '{}',
    experience_years INTEGER DEFAULT 0,
    hire_date DATE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'on_leave')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Learners table (students)
CREATE TABLE IF NOT EXISTS public.learners (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    learner_code VARCHAR(50) UNIQUE,
    full_name VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    date_of_birth DATE NOT NULL,
    gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other')),
    address TEXT,
    health_info JSONB DEFAULT '{}'::jsonb,
    emergency_contact JSONB DEFAULT '{}'::jsonb,
    enrollment_date DATE DEFAULT CURRENT_DATE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'graduated', 'transferred')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Guardians table (parents/guardians)
CREATE TABLE IF NOT EXISTS public.guardians (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    relationship VARCHAR(50) CHECK (relationship IN ('father', 'mother', 'guardian', 'grandparent', 'other')),
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enrollments table (learner-program relationships)
CREATE TABLE IF NOT EXISTS public.enrollments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    learner_id UUID NOT NULL REFERENCES public.learners(id) ON DELETE CASCADE,
    program_id UUID NOT NULL REFERENCES public.programs(id) ON DELETE CASCADE,
    enrollment_date DATE DEFAULT CURRENT_DATE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'completed', 'withdrawn')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(learner_id, program_id)
);

-- Events table (school events)
CREATE TABLE IF NOT EXISTS public.events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    event_type VARCHAR(50) CHECK (event_type IN ('meeting', 'performance', 'field_trip', 'sports', 'ceremony', 'cultural', 'outdoor')),
    start_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    end_datetime TIMESTAMP WITH TIME ZONE,
    location VARCHAR(255),
    max_participants INTEGER,
    current_participants INTEGER DEFAULT 0,
    registration_required BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Attendance table (daily attendance)
CREATE TABLE IF NOT EXISTS public.attendance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    learner_id UUID NOT NULL REFERENCES public.learners(id) ON DELETE CASCADE,
    program_id UUID REFERENCES public.programs(id) ON DELETE CASCADE,
    session_date DATE NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('present', 'absent', 'late', 'excused')),
    check_in_time TIMESTAMP WITH TIME ZONE,
    check_out_time TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(learner_id, session_date)
);

-- Fees table (payments and fees)
CREATE TABLE IF NOT EXISTS public.fees (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    learner_id UUID NOT NULL REFERENCES public.learners(id) ON DELETE CASCADE,
    fee_type VARCHAR(50) NOT NULL,
    fee_category VARCHAR(50) NOT NULL CHECK (fee_category IN ('tuition', 'meal', 'transport', 'activity', 'material', 'other')),
    amount DECIMAL(12,2) NOT NULL,
    due_date DATE NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled')),
    payment_date DATE,
    payment_method VARCHAR(50) CHECK (payment_method IN ('cash', 'bank_transfer', 'zalopay', 'momo', 'card')),
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ============================================================================
-- PART 2: ADDITIONAL EDUCATION MODULES
-- ============================================================================

-- Curriculum table (lesson plans and curriculum)
CREATE TABLE IF NOT EXISTS public.curriculum (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    program_id UUID REFERENCES public.programs(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    subject VARCHAR(100),
    age_group VARCHAR(50),
    difficulty_level VARCHAR(50) DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    duration_minutes INTEGER DEFAULT 30,
    objectives JSONB DEFAULT '[]'::jsonb,
    materials_needed JSONB DEFAULT '[]'::jsonb,
    activities JSONB DEFAULT '[]'::jsonb,
    assessment_criteria JSONB DEFAULT '[]'::jsonb,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Health Records table (medical records)
CREATE TABLE IF NOT EXISTS public.health_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    learner_id UUID NOT NULL REFERENCES public.learners(id) ON DELETE CASCADE,
    record_type VARCHAR(50) NOT NULL CHECK (record_type IN ('checkup', 'vaccination', 'illness', 'injury', 'allergy', 'medication')),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    record_date DATE NOT NULL,
    medications JSONB DEFAULT '[]'::jsonb,
    allergies JSONB DEFAULT '[]'::jsonb,
    vaccinations JSONB DEFAULT '[]'::jsonb,
    vital_signs JSONB DEFAULT '{}'::jsonb,
    notes TEXT,
    is_emergency BOOLEAN DEFAULT false,
    guardian_notified BOOLEAN DEFAULT false,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Meal Plans table (daily meal planning)
CREATE TABLE IF NOT EXISTS public.meal_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    meal_type VARCHAR(20) NOT NULL CHECK (meal_type IN ('breakfast', 'lunch', 'snack', 'dinner')),
    menu_items JSONB NOT NULL DEFAULT '[]'::jsonb,
    special_notes TEXT,
    dietary_accommodations JSONB DEFAULT '[]'::jsonb,
    cost_per_serving DECIMAL(10,2),
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(account_id, date, meal_type)
);

-- Vehicles table (transportation)
CREATE TABLE IF NOT EXISTS public.vehicles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    vehicle_number VARCHAR(50) NOT NULL,
    vehicle_type VARCHAR(50) NOT NULL CHECK (vehicle_type IN ('bus', 'van', 'car')),
    brand VARCHAR(100),
    model VARCHAR(100),
    year INTEGER,
    capacity INTEGER NOT NULL,
    license_plate VARCHAR(20) UNIQUE,
    driver_name VARCHAR(255),
    driver_phone VARCHAR(20),
    driver_license VARCHAR(100),
    insurance_info JSONB DEFAULT '{}'::jsonb,
    maintenance_schedule JSONB DEFAULT '{}'::jsonb,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Library Items table (media library)
CREATE TABLE IF NOT EXISTS public.library_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    item_type VARCHAR(50) NOT NULL CHECK (item_type IN ('image', 'video', 'document', 'audio')),
    description TEXT,
    file_url TEXT,
    thumbnail_url TEXT,
    category VARCHAR(100),
    tags TEXT[] DEFAULT '{}',
    target_audience VARCHAR(50) CHECK (target_audience IN ('students', 'parents', 'teachers', 'all')),
    age_group VARCHAR(50),
    language VARCHAR(20) DEFAULT 'vietnamese',
    duration INTEGER, -- in seconds for video/audio
    file_size BIGINT, -- in bytes
    format VARCHAR(20),
    upload_date DATE DEFAULT CURRENT_DATE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ============================================================================
-- PART 3: INDEXES FOR PERFORMANCE
-- ============================================================================

-- Core tables indexes
CREATE INDEX IF NOT EXISTS idx_programs_account_id ON public.programs(account_id);
CREATE INDEX IF NOT EXISTS idx_programs_status ON public.programs(status);
CREATE INDEX IF NOT EXISTS idx_instructors_account_id ON public.instructors(account_id);
CREATE INDEX IF NOT EXISTS idx_instructors_employee_code ON public.instructors(employee_code);
CREATE INDEX IF NOT EXISTS idx_learners_account_id ON public.learners(account_id);
CREATE INDEX IF NOT EXISTS idx_learners_learner_code ON public.learners(learner_code);
CREATE INDEX IF NOT EXISTS idx_guardians_account_id ON public.guardians(account_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_account_id ON public.enrollments(account_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_learner_id ON public.enrollments(learner_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_program_id ON public.enrollments(program_id);
CREATE INDEX IF NOT EXISTS idx_events_account_id ON public.events(account_id);
CREATE INDEX IF NOT EXISTS idx_events_start_datetime ON public.events(start_datetime);
CREATE INDEX IF NOT EXISTS idx_attendance_account_id ON public.attendance(account_id);
CREATE INDEX IF NOT EXISTS idx_attendance_learner_id ON public.attendance(learner_id);
CREATE INDEX IF NOT EXISTS idx_attendance_session_date ON public.attendance(session_date);
CREATE INDEX IF NOT EXISTS idx_fees_account_id ON public.fees(account_id);
CREATE INDEX IF NOT EXISTS idx_fees_learner_id ON public.fees(learner_id);
CREATE INDEX IF NOT EXISTS idx_fees_due_date ON public.fees(due_date);
CREATE INDEX IF NOT EXISTS idx_fees_status ON public.fees(status);

-- Additional modules indexes
CREATE INDEX IF NOT EXISTS idx_curriculum_account_id ON public.curriculum(account_id);
CREATE INDEX IF NOT EXISTS idx_curriculum_program_id ON public.curriculum(program_id);
CREATE INDEX IF NOT EXISTS idx_health_records_account_id ON public.health_records(account_id);
CREATE INDEX IF NOT EXISTS idx_health_records_learner_id ON public.health_records(learner_id);
CREATE INDEX IF NOT EXISTS idx_meal_plans_account_id ON public.meal_plans(account_id);
CREATE INDEX IF NOT EXISTS idx_meal_plans_date ON public.meal_plans(date);
CREATE INDEX IF NOT EXISTS idx_vehicles_account_id ON public.vehicles(account_id);
CREATE INDEX IF NOT EXISTS idx_library_items_account_id ON public.library_items(account_id);

-- ============================================================================
-- PART 4: ROW LEVEL SECURITY (RLS) POLICIES
-- ============================================================================

-- Enable RLS on all education tables
ALTER TABLE public.programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.instructors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.learners ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guardians ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.curriculum ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.health_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.meal_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.library_items ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies for education tables (view access for account members)
CREATE POLICY "education_programs_read" ON public.programs
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_instructors_read" ON public.instructors
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_learners_read" ON public.learners
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_guardians_read" ON public.guardians
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_enrollments_read" ON public.enrollments
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_events_read" ON public.events
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_attendance_read" ON public.attendance
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_fees_read" ON public.fees
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_curriculum_read" ON public.curriculum
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_health_records_read" ON public.health_records
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_meal_plans_read" ON public.meal_plans
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_vehicles_read" ON public.vehicles
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "education_library_items_read" ON public.library_items
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

-- Management policies for education data (requires education.manage permission)
CREATE POLICY "education_programs_manage" ON public.programs
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_instructors_manage" ON public.instructors
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_learners_manage" ON public.learners
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_guardians_manage" ON public.guardians
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_enrollments_manage" ON public.enrollments
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_events_manage" ON public.events
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_attendance_manage" ON public.attendance
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_fees_manage" ON public.fees
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_curriculum_manage" ON public.curriculum
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_health_records_manage" ON public.health_records
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_meal_plans_manage" ON public.meal_plans
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_vehicles_manage" ON public.vehicles
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

CREATE POLICY "education_library_items_manage" ON public.library_items
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

-- ============================================================================
-- PART 5: HELPER FUNCTIONS
-- ============================================================================

-- Function to generate unique learner codes
CREATE OR REPLACE FUNCTION public.generate_unique_learner_codes(p_account_id UUID, p_count INTEGER)
RETURNS TEXT[]
LANGUAGE plpgsql
AS $$
DECLARE
  org_prefix TEXT;
  next_number INTEGER;
  learner_codes TEXT[];
  i INTEGER;
  new_code TEXT;
BEGIN
  -- Get prefix from account name (first 3 letters)
  SELECT UPPER(LEFT(REGEXP_REPLACE(name, '[^a-zA-Z]', '', 'g'), 3))
  INTO org_prefix
  FROM public.accounts
  WHERE id = p_account_id;

  -- If no account found or empty name, use default prefix
  IF org_prefix IS NULL OR org_prefix = '' THEN
    org_prefix := 'EDU';
  END IF;

  -- Get starting number
  SELECT COALESCE(MAX(CAST(SUBSTRING(l.learner_code FROM '[0-9]+$') AS integer)), 0)
  INTO next_number
  FROM public.learners l
  WHERE l.account_id = p_account_id
  AND l.learner_code ~ ('^' || org_prefix || '[0-9]+$');

  -- Generate unique codes
  learner_codes := ARRAY[]::TEXT[];
  FOR i IN 1..p_count LOOP
    next_number := next_number + 1;
    new_code := org_prefix || LPAD(next_number::text, 4, '0');
    learner_codes := array_append(learner_codes, new_code);
  END LOOP;

  RETURN learner_codes;
END;
$$;

-- ============================================================================
-- PART 6: EDUCATION SAMPLE DATA FUNCTION
-- ============================================================================

-- Main function to create complete education sample data using mockup.config.ts
CREATE OR REPLACE FUNCTION public.create_education_sample_data(
    p_account_id UUID
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_result JSONB := jsonb_build_object(
        'success', true,
        'account_id', p_account_id,
        'created_at', now(),
        'stats', jsonb_build_object()
    );
    v_stats JSONB := jsonb_build_object(
        'programs', 0,
        'instructors', 0,
        'learners', 0,
        'events', 0,
        'guardians', 0,
        'enrollments', 0,
        'attendance', 0,
        'fees', 0,
        'curriculum', 0,
        'health_records', 0,
        'transportation', 0,
        'meals', 0,
        'library_items', 0,
        'messages', 0
    );
    v_user_id UUID;
    v_program_ids UUID[];
    v_learner_ids UUID[];
    v_instructor_ids UUID[];
    v_guardian_ids UUID[];
    v_learner_codes TEXT[];
    i INTEGER;
BEGIN
    -- Get user ID for created_by fields
    SELECT primary_owner_user_id INTO v_user_id FROM public.accounts WHERE id = p_account_id;

    -- Validate account exists
    IF NOT EXISTS (SELECT 1 FROM public.accounts WHERE id = p_account_id) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Account not found',
            'account_id', p_account_id
        );
    END IF;

    -- 1. Create Programs from mockup.config.ts
    IF NOT EXISTS (SELECT 1 FROM public.programs WHERE account_id = p_account_id) THEN
        INSERT INTO public.programs (account_id, name, program_type, age_group, duration_weeks, capacity, description, schedule, status, created_at, updated_at)
        VALUES
        (p_account_id, 'Lớp Nhà trẻ (18-36 tháng)', 'nursery', '18-36 tháng', 78, 15, 'Chương trình chăm sóc và phát triển cho trẻ từ 18-36 tháng', '{"schedule": "Thứ 2 - Thứ 6, 7:30 - 16:30"}'::jsonb, 'active', now(), now()),
        (p_account_id, 'Lớp Chồi (3-4 tuổi)', 'preschool', '36-48 tháng', 52, 20, 'Chương trình giáo dục mầm non cho trẻ 3-4 tuổi', '{"schedule": "Thứ 2 - Thứ 6, 7:30 - 16:30"}'::jsonb, 'active', now(), now()),
        (p_account_id, 'Lớp Lá (4-5 tuổi)', 'preschool', '48-60 tháng', 52, 25, 'Chương trình giáo dục mầm non cho trẻ 4-5 tuổi', '{"schedule": "Thứ 2 - Thứ 6, 7:30 - 16:30"}'::jsonb, 'active', now(), now()),
        (p_account_id, 'Lớp Hoa (5-6 tuổi)', 'kindergarten', '60-72 tháng', 52, 25, 'Chương trình chuẩn bị vào lớp 1 cho trẻ 5-6 tuổi', '{"schedule": "Thứ 2 - Thứ 6, 7:30 - 16:30"}'::jsonb, 'active', now(), now());

        v_stats := jsonb_set(v_stats, ARRAY['programs'], to_jsonb(4));
    END IF;

    -- Get program IDs
    SELECT ARRAY(SELECT id FROM public.programs WHERE account_id = p_account_id ORDER BY created_at) INTO v_program_ids;

    -- 2. Create Instructors from mockup.config.ts
    IF NOT EXISTS (SELECT 1 FROM public.instructors WHERE account_id = p_account_id) THEN
        INSERT INTO public.instructors (account_id, employee_code, full_name, email, phone, specialization, qualifications, status, created_at, updated_at)
        VALUES
        (p_account_id, 'INS0001', 'Nguyễn Thị Hoa', '<EMAIL>', '**********', 'Giáo dục mầm non', '{"degree": "Cử nhân Sư phạm Mầm non", "experience": "8 years", "certifications": ["Chứng chỉ Montessori", "Chứng chỉ Anh ngữ trẻ em"]}'::jsonb, 'active', now(), now()),
        (p_account_id, 'INS0002', 'Trần Thị Mai', '<EMAIL>', '**********', 'Âm nhạc trẻ em', '{"degree": "Cử nhân Âm nhạc", "experience": "5 years", "certifications": ["Chứng chỉ Orff", "Chứng chỉ Piano"]}'::jsonb, 'active', now(), now()),
        (p_account_id, 'INS0003', 'Lê Văn Nam', '<EMAIL>', '**********', 'Thể dục trẻ em', '{"degree": "Cử nhân Thể dục thể thao", "experience": "6 years", "certifications": ["Chứng chỉ Yoga trẻ em", "Chứng chỉ Bơi lội"]}'::jsonb, 'active', now(), now());

        v_stats := jsonb_set(v_stats, ARRAY['instructors'], to_jsonb(3));
    END IF;

    -- Get instructor IDs
    SELECT ARRAY(SELECT id FROM public.instructors WHERE account_id = p_account_id ORDER BY created_at) INTO v_instructor_ids;

    -- 3. Create Guardians first
    IF NOT EXISTS (SELECT 1 FROM public.guardians WHERE account_id = p_account_id) THEN
        INSERT INTO public.guardians (account_id, full_name, phone, email, relationship, is_primary, created_at)
        VALUES
        (p_account_id, 'Nguyễn Văn Bình', '**********', '<EMAIL>', 'father', true, now()),
        (p_account_id, 'Nguyễn Thị Lan', '**********', '<EMAIL>', 'mother', false, now()),
        (p_account_id, 'Trần Văn Đức', '**********', '<EMAIL>', 'father', true, now()),
        (p_account_id, 'Trần Thị Hương', '**********', '<EMAIL>', 'mother', false, now()),
        (p_account_id, 'Lê Thị Thu', '**********', '<EMAIL>', 'mother', true, now()),
        (p_account_id, 'Lê Văn Hùng', '**********', '<EMAIL>', 'father', false, now());

        v_stats := jsonb_set(v_stats, ARRAY['guardians'], to_jsonb(6));
    END IF;

    -- Get guardian IDs
    SELECT ARRAY(SELECT id FROM public.guardians WHERE account_id = p_account_id ORDER BY created_at) INTO v_guardian_ids;

    -- 4. Generate unique learner codes
    SELECT public.generate_unique_learner_codes(p_account_id, 3) INTO v_learner_codes;

    -- 5. Create Learners from mockup.config.ts with unique learner_codes
    IF NOT EXISTS (SELECT 1 FROM public.learners WHERE account_id = p_account_id) THEN
        INSERT INTO public.learners (account_id, learner_code, full_name, nickname, date_of_birth, gender, address, medical_info, emergency_contact, status, created_at, updated_at)
        VALUES
        (p_account_id, v_learner_codes[1], 'Nguyễn Minh An', 'Bé An', '2021-03-15', 'male', '456 Đường Lê Lợi, Quận 1, TP.HCM',
         '{"allergies": ["Không có"], "medications": []}'::jsonb,
         '{"name": "Nguyễn Thị Lan", "relationship": "mother", "phone": "**********"}'::jsonb,
         'active', now(), now()),
        (p_account_id, v_learner_codes[2], 'Trần Thị Bảo Ngọc', 'Bé Ngọc', '2020-07-20', 'female', '789 Đường Nguyễn Huệ, Quận 1, TP.HCM',
         '{"allergies": ["Dị ứng đậu phộng"], "medications": []}'::jsonb,
         '{"name": "Trần Thị Hương", "relationship": "mother", "phone": "**********"}'::jsonb,
         'active', now(), now()),
        (p_account_id, v_learner_codes[3], 'Lê Hoàng Minh', 'Bé Minh', '2019-11-10', 'male', '321 Đường Võ Văn Tần, Quận 3, TP.HCM',
         '{"allergies": ["Không có"], "medications": ["Thuốc xịt hen suyễn"]}'::jsonb,
         '{"name": "Lê Văn Hùng", "relationship": "father", "phone": "**********"}'::jsonb,
         'active', now(), now());

        v_stats := jsonb_set(v_stats, ARRAY['learners'], to_jsonb(3));
    END IF;

    -- Get learner IDs
    SELECT ARRAY(SELECT id FROM public.learners WHERE account_id = p_account_id ORDER BY created_at) INTO v_learner_ids;

    -- 6. Create Enrollments (link learners to programs)
    IF NOT EXISTS (SELECT 1 FROM public.enrollments WHERE learner_id = ANY(v_learner_ids)) AND array_length(v_learner_ids, 1) > 0 AND array_length(v_program_ids, 1) > 0 THEN
        INSERT INTO public.enrollments (account_id, learner_id, program_id, enrollment_date, status, created_at)
        VALUES
        (p_account_id, v_learner_ids[1], v_program_ids[2], '2024-09-01', 'active', now()), -- Bé An -> Lớp Chồi
        (p_account_id, v_learner_ids[2], v_program_ids[3], '2024-09-01', 'active', now()), -- Bé Ngọc -> Lớp Lá
        (p_account_id, v_learner_ids[3], v_program_ids[4], '2024-09-01', 'active', now()); -- Bé Minh -> Lớp Hoa

        v_stats := jsonb_set(v_stats, ARRAY['enrollments'], to_jsonb(3));
    END IF;

    -- 7. Create Events from mockup.config.ts
    IF NOT EXISTS (SELECT 1 FROM public.events WHERE account_id = p_account_id) THEN
        INSERT INTO public.events (account_id, title, description, event_type, start_datetime, end_datetime, location, max_participants, current_participants, registration_required, created_at)
        VALUES
        (p_account_id, 'Họp phụ huynh đầu năm học', 'Họp phụ huynh để thông báo kế hoạch năm học mới và trao đổi về chương trình giáo dục', 'meeting', '2024-12-20T19:00:00+07:00'::timestamp with time zone, '2024-12-20T21:00:00+07:00'::timestamp with time zone, 'Hội trường trường', 100, 0, true, now()),
        (p_account_id, 'Biểu diễn Giáng sinh', 'Chương trình biểu diễn Giáng sinh của các bé với sự tham gia của phụ huynh', 'performance', '2024-12-24T09:00:00+07:00'::timestamp with time zone, '2024-12-24T11:00:00+07:00'::timestamp with time zone, 'Sân trường', 200, 0, true, now()),
        (p_account_id, 'Dã ngoại cuối năm', 'Chuyến dã ngoại cuối năm học tại công viên Tao Đàn', 'field_trip', '2024-12-28T08:00:00+07:00'::timestamp with time zone, '2024-12-28T16:00:00+07:00'::timestamp with time zone, 'Công viên Tao Đàn', 50, 0, true, now());

        v_stats := jsonb_set(v_stats, ARRAY['events'], to_jsonb(3));
    END IF;

    -- 8. Create Attendance records
    IF NOT EXISTS (SELECT 1 FROM public.attendance WHERE account_id = p_account_id) AND array_length(v_learner_ids, 1) > 0 AND array_length(v_program_ids, 1) > 0 THEN
        INSERT INTO public.attendance (account_id, learner_id, program_id, session_date, status, check_in_time, check_out_time, notes, created_at)
        VALUES
        (p_account_id, v_learner_ids[1], v_program_ids[2], '2024-12-02', 'present', '2024-12-02T07:45:00+07:00'::timestamp with time zone, '2024-12-02T16:20:00+07:00'::timestamp with time zone, 'Bé vui vẻ và tham gia tích cực', now()),
        (p_account_id, v_learner_ids[1], v_program_ids[2], '2024-12-01', 'present', '2024-12-01T07:50:00+07:00'::timestamp with time zone, '2024-12-01T16:15:00+07:00'::timestamp with time zone, NULL, now()),
        (p_account_id, v_learner_ids[2], v_program_ids[3], '2024-12-02', 'present', '2024-12-02T07:30:00+07:00'::timestamp with time zone, '2024-12-02T16:00:00+07:00'::timestamp with time zone, NULL, now()),
        (p_account_id, v_learner_ids[3], v_program_ids[4], '2024-12-02', 'present', '2024-12-02T07:55:00+07:00'::timestamp with time zone, '2024-12-02T16:30:00+07:00'::timestamp with time zone, NULL, now());

        v_stats := jsonb_set(v_stats, ARRAY['attendance'], to_jsonb(4));
    END IF;

    -- 9. Create Fees records
    IF NOT EXISTS (SELECT 1 FROM public.fees WHERE account_id = p_account_id) AND array_length(v_learner_ids, 1) > 0 THEN
        INSERT INTO public.fees (account_id, learner_id, fee_type, fee_category, amount, due_date, description, status, payment_date, payment_method, created_by, created_at, updated_at)
        VALUES
        (p_account_id, v_learner_ids[1], 'tuition', 'tuition', 4000000, '2024-12-05', 'Học phí tháng 12/2024', 'pending', NULL, NULL, v_user_id, now(), now()),
        (p_account_id, v_learner_ids[1], 'meal', 'meal', 800000, '2024-12-05', 'Tiền ăn tháng 12/2024', 'pending', NULL, NULL, v_user_id, now(), now()),
        (p_account_id, v_learner_ids[2], 'tuition', 'tuition', 4500000, '2024-12-05', 'Học phí tháng 12/2024', 'pending', NULL, NULL, v_user_id, now(), now()),
        (p_account_id, v_learner_ids[3], 'tuition', 'tuition', 5000000, '2024-12-05', 'Học phí tháng 12/2024', 'paid', '2024-12-01', 'zalopay', v_user_id, now(), now());

        v_stats := jsonb_set(v_stats, ARRAY['fees'], to_jsonb(4));
    END IF;

    -- Update account with education settings from mockup.config.ts
    UPDATE public.accounts
    SET education_settings = jsonb_build_object(
        'organization_type', 'kindergarten',
        'license_number', 'GD-ĐKMN-001/2010',
        'capacity', 300,
        'established_year', 2010,
        'address', '123 Đường Nguyễn Văn Cừ, Quận 5, TP.HCM',
        'phone', '028-3838-1234',
        'website', 'https://truonghoamai.edu.vn',
        'description', 'Trường mầm non uy tín với phương pháp giáo dục hiện đại, tập trung phát triển toàn diện cho trẻ em từ 18 tháng đến 6 tuổi.'
    )
    WHERE id = p_account_id;

    -- 9. Create Sample Curriculum
    IF NOT EXISTS (SELECT 1 FROM public.curriculum WHERE account_id = p_account_id) THEN
        INSERT INTO public.curriculum (account_id, title, description, subject, age_group, duration_minutes, objectives, assessment_criteria, status, created_at, updated_at)
        VALUES
        (p_account_id, 'Chương trình Toán học Mầm non', 'Chương trình toán học cơ bản cho trẻ mầm non', 'Toán học', 'Mầm non', 30,
         '["Nhận biết số từ 1-10", "Phân biệt hình dạng cơ bản", "So sánh nhiều ít"]'::jsonb,
         '["Quan sát", "Thực hành", "Trò chơi"]'::jsonb, 'active', now(), now()),
        (p_account_id, 'Chương trình Ngôn ngữ', 'Phát triển kỹ năng ngôn ngữ cho trẻ', 'Ngôn ngữ', 'Mầm non', 45,
         '["Phát âm rõ ràng", "Từ vựng cơ bản", "Kể chuyện đơn giản"]'::jsonb,
         '["Trò chuyện", "Kể chuyện", "Hát"]'::jsonb, 'active', now(), now()),
        (p_account_id, 'Chương trình Mỹ thuật', 'Phát triển khả năng sáng tạo qua mỹ thuật', 'Mỹ thuật', 'Mầm non', 60,
         '["Sử dụng màu sắc", "Vẽ hình cơ bản", "Tạo hình đơn giản"]'::jsonb,
         '["Tác phẩm", "Triển lãm", "Thuyết trình"]'::jsonb, 'active', now(), now());

        v_stats := jsonb_set(v_stats, ARRAY['curriculum'], to_jsonb(3));
    END IF;

    -- 10. Create Sample Health Records
    IF NOT EXISTS (SELECT 1 FROM public.health_records WHERE account_id = p_account_id) THEN
        INSERT INTO public.health_records (account_id, learner_id, record_type, title, description, record_date, medications, allergies, notes, is_emergency, guardian_notified, created_at, updated_at)
        SELECT
            p_account_id,
            v_learner_ids[1],
            'checkup',
            'Khám sức khỏe định kỳ',
            'Khám sức khỏe tổng quát cho bé',
            '2024-11-15'::date,
            '[]'::jsonb,
            '["Không có dị ứng"]'::jsonb,
            'Sức khỏe tốt, phát triển bình thường',
            false,
            true,
            now(),
            now()
        WHERE array_length(v_learner_ids, 1) >= 1
        UNION ALL
        SELECT
            p_account_id,
            v_learner_ids[2],
            'vaccination',
            'Tiêm chủng định kỳ',
            'Tiêm vaccine cúm mùa',
            '2024-10-20'::date,
            '[]'::jsonb,
            '["Dị ứng đậu phộng"]'::jsonb,
            'Đã tiêm vaccine cúm mùa',
            false,
            true,
            now(),
            now()
        WHERE array_length(v_learner_ids, 1) >= 2;

        v_stats := jsonb_set(v_stats, ARRAY['health_records'], to_jsonb(2));
    END IF;

    -- 11. Create Sample Transportation
    IF NOT EXISTS (SELECT 1 FROM public.vehicles WHERE account_id = p_account_id) THEN
        INSERT INTO public.vehicles (account_id, vehicle_type, vehicle_number, brand, model, year, capacity, license_plate, driver_name, driver_phone, driver_license, insurance_info, status, created_at, updated_at)
        VALUES
        (p_account_id, 'bus', 'XE01', 'Hyundai', 'County', 2020, 25, '51B-' || EXTRACT(EPOCH FROM now())::bigint || '1', 'Nguyễn Văn Tài', '**********', 'D123456789',
         '{"provider": "Bảo Việt", "policy_number": "*********", "expiry_date": "2025-12-31"}'::jsonb, 'active', now(), now()),
        (p_account_id, 'van', 'XE02', 'Ford', 'Transit', 2019, 12, '51B-' || EXTRACT(EPOCH FROM now())::bigint || '2', 'Trần Thị Hoa', '**********', 'D987654321',
         '{"provider": "Bảo Minh", "policy_number": "*********", "expiry_date": "2025-06-30"}'::jsonb, 'active', now(), now());

        v_stats := jsonb_set(v_stats, ARRAY['transportation'], to_jsonb(2));
    END IF;

    -- 12. Create Sample Meals
    IF NOT EXISTS (SELECT 1 FROM public.meal_plans WHERE account_id = p_account_id) THEN
        INSERT INTO public.meal_plans (account_id, date, meal_type, menu_items, special_notes, dietary_accommodations, cost_per_serving, created_at, updated_at)
        VALUES
        (p_account_id, CURRENT_DATE, 'lunch',
         '[{"name": "Cơm gà xối mỡ", "ingredients": ["Gạo", "Thịt gà", "Rau cải", "Canh chua"], "nutritional_info": {"calories": 450, "protein": "25g", "carbs": "60g", "fat": "12g"}}]'::jsonb,
         'Món ăn phù hợp cho trẻ từ 2-5 tuổi', '["Không có"]'::jsonb, 35000, now(), now()),
        (p_account_id, CURRENT_DATE + INTERVAL '1 day', 'lunch',
         '[{"name": "Phở bò", "ingredients": ["Bánh phở", "Thịt bò", "Hành lá", "Rau thơm"], "nutritional_info": {"calories": 380, "protein": "22g", "carbs": "55g", "fat": "8g"}}]'::jsonb,
         'Có chứa gluten', '["Gluten"]'::jsonb, 40000, now(), now()),
        (p_account_id, CURRENT_DATE + INTERVAL '2 days', 'lunch',
         '[{"name": "Cháo tôm", "ingredients": ["Gạo", "Tôm", "Rau cải", "Hành phi"], "nutritional_info": {"calories": 320, "protein": "18g", "carbs": "45g", "fat": "6g"}}]'::jsonb,
         'Có chứa hải sản', '["Hải sản"]'::jsonb, 38000, now(), now());

        v_stats := jsonb_set(v_stats, ARRAY['meals'], to_jsonb(3));
    END IF;

    -- 13. Create Sample Library Items
    IF NOT EXISTS (SELECT 1 FROM public.library_items WHERE account_id = p_account_id) THEN
        INSERT INTO public.library_items (account_id, title, item_type, description, file_url, thumbnail_url, tags, category, target_audience, age_group, language, duration, format, status, created_at, updated_at)
        VALUES
        (p_account_id, 'Video: Bài hát ABC', 'video', 'Video học bảng chữ cái tiếng Anh cho trẻ mầm non',
         'https://example.com/abc-song.mp4', 'https://example.com/abc-thumb.jpg',
         '{"alphabet", "english", "song"}', 'educational', 'students', 'Mầm non', 'vietnamese', 180, 'mp4', 'active', now(), now()),
        (p_account_id, 'Hình ảnh: Động vật rừng', 'image', 'Bộ sưu tập hình ảnh các loài động vật rừng',
         'https://example.com/animals.jpg', 'https://example.com/animals-thumb.jpg',
         '{"animals", "nature", "learning"}', 'educational', 'all', 'Tất cả', 'vietnamese', NULL, 'jpg', 'active', now(), now()),
        (p_account_id, 'Sự kiện: Ngày hội thể thao', 'video', 'Hình ảnh và video từ ngày hội thể thao năm học',
         'https://example.com/sports-day.mp4', 'https://example.com/sports-thumb.jpg',
         '{"sports", "event", "school"}', 'event', 'all', 'Tất cả', 'vietnamese', 300, 'mp4', 'active', now(), now());

        v_stats := jsonb_set(v_stats, ARRAY['library_items'], to_jsonb(3));
    END IF;

    -- Skip messages table for now as it doesn't exist

    -- Set final stats
    v_result := jsonb_set(v_result, ARRAY['stats'], v_stats);

    RETURN v_result;
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'account_id', p_account_id
        );
END;
$$;

-- ============================================================================
-- PART 7: TRIGGER FOR AUTO-CREATING EDUCATION SAMPLE DATA
-- ============================================================================

-- Function to auto-create education sample data when team account is created
CREATE OR REPLACE FUNCTION public.auto_create_education_sample_data()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
BEGIN
    -- Check if this account has education industry or education-related settings
    IF NEW.public_data IS NOT NULL AND (
        NEW.public_data->>'industry' = 'education' OR
        NEW.public_data->>'business_type' = 'education' OR
        NEW.education_settings IS NOT NULL
    ) THEN
        -- Create education sample data asynchronously
        PERFORM public.create_education_sample_data(NEW.id);
    END IF;

    RETURN NEW;
END;
$$;

-- Create trigger for auto-creating education sample data
DROP TRIGGER IF EXISTS auto_create_education_sample_data_trigger ON public.accounts;
CREATE TRIGGER auto_create_education_sample_data_trigger
    AFTER INSERT ON public.accounts
    FOR EACH ROW
    EXECUTE FUNCTION public.auto_create_education_sample_data();

-- Update the main sample data function to handle education templates
CREATE OR REPLACE FUNCTION public.create_sample_data_for_account(
    p_account_id UUID,
    p_industry_template TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_template_data JSONB;
    v_education_result JSONB;
BEGIN
    -- Validate JSON format
    BEGIN
        v_template_data := p_industry_template::jsonb;
    EXCEPTION WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Invalid JSON format: ' || SQLERRM,
            'account_id', p_account_id,
            'industry_template', p_industry_template
        );
    END;

    -- Special handling for education industry
    -- Check if this is education template by looking for education-specific fields
    IF v_template_data ? 'programs' OR v_template_data ? 'instructors' OR v_template_data ? 'learners' OR v_template_data ? 'organization' THEN
        -- This is education template data, call the education function
        SELECT public.create_education_sample_data(p_account_id) INTO v_education_result;

        -- Return the education result directly
        RETURN v_education_result;
    END IF;

    -- For non-education templates, return success (other logic can be added here)
    RETURN jsonb_build_object(
        'success', true,
        'account_id', p_account_id,
        'industry', 'non-education',
        'created_at', now()
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'account_id', p_account_id,
            'industry', p_industry_template
        );
END;
$$;

-- ============================================================================
-- PART 8: GRANT PERMISSIONS AND COMMENTS
-- ============================================================================

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.generate_unique_learner_codes(UUID, INTEGER) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.create_education_sample_data(UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.auto_create_education_sample_data() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.create_sample_data_for_account(UUID, TEXT) TO authenticated, service_role;

-- Add comments to functions
COMMENT ON FUNCTION public.generate_unique_learner_codes(UUID, INTEGER) IS 'Generates multiple unique learner codes for the given account';
COMMENT ON FUNCTION public.create_education_sample_data(UUID) IS 'Creates comprehensive education sample data using mockup.config.ts data for all education modules';
COMMENT ON FUNCTION public.auto_create_education_sample_data() IS 'Trigger function that automatically creates education sample data for new team accounts';
COMMENT ON FUNCTION public.create_sample_data_for_account(UUID, TEXT) IS 'Main sample data function that handles education and other industry templates';

-- Add comments to tables
COMMENT ON TABLE public.programs IS 'Education programs and classes offered by the institution';
COMMENT ON TABLE public.instructors IS 'Teachers and staff members in the education institution';
COMMENT ON TABLE public.learners IS 'Students enrolled in the education institution';
COMMENT ON TABLE public.guardians IS 'Parents and guardians of the learners';
COMMENT ON TABLE public.enrollments IS 'Enrollment records linking learners to programs';
COMMENT ON TABLE public.events IS 'School events, meetings, and activities';
COMMENT ON TABLE public.attendance IS 'Daily attendance records for learners';
COMMENT ON TABLE public.fees IS 'Fee payments and billing records';
COMMENT ON TABLE public.curriculum IS 'Curriculum and lesson plans for programs';
COMMENT ON TABLE public.health_records IS 'Health and medical records for learners';
COMMENT ON TABLE public.meal_plans IS 'Daily meal planning and nutrition information';
COMMENT ON TABLE public.vehicles IS 'Transportation vehicles and logistics';
COMMENT ON TABLE public.library_items IS 'Media library for educational content and resources';
