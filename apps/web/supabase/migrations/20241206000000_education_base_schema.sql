-- Education Base Schema Migration
-- Create core education tables before photo attendance

-- 1. Learners table
CREATE TABLE IF NOT EXISTS learners (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  full_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  nickname <PERSON><PERSON><PERSON><PERSON>(100),
  learner_code VARCHAR(50) UNIQUE,
  date_of_birth DATE,
  gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other')),
  address TEXT,
  phone VARCHAR(20),
  email VARCHAR(255),
  emergency_contact JSONB,
  medical_info JSONB,
  notes TEXT,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'graduated', 'transferred')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 2. Programs table
CREATE TABLE IF NOT EXISTS programs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  program_type VARCHAR(50) DEFAULT 'regular',
  age_group VARCHAR(50),
  capacity INTEGER DEFAULT 20,
  duration_weeks INTEGER,
  schedule JSONB,
  fees JSONB,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 3. Instructors table
CREATE TABLE IF NOT EXISTS instructors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  full_name VARCHAR(255) NOT NULL,
  employee_code VARCHAR(50),
  email VARCHAR(255),
  phone VARCHAR(20),
  specialization TEXT,
  qualifications JSONB,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 4. Enrollments table
CREATE TABLE IF NOT EXISTS enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  learner_id UUID NOT NULL REFERENCES learners(id) ON DELETE CASCADE,
  program_id UUID NOT NULL REFERENCES programs(id) ON DELETE CASCADE,
  enrollment_date DATE NOT NULL DEFAULT CURRENT_DATE,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'completed', 'dropped')),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  UNIQUE(learner_id, program_id)
);

-- 5. Attendance table (enhanced from Phase 1)
CREATE TABLE IF NOT EXISTS attendance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  learner_id UUID NOT NULL REFERENCES learners(id) ON DELETE CASCADE,
  program_id UUID NOT NULL REFERENCES programs(id) ON DELETE CASCADE,
  session_date DATE NOT NULL,
  session_time TIME,
  status VARCHAR(20) DEFAULT 'absent' CHECK (status IN ('present', 'absent', 'late', 'excused')),
  check_in_time TIMESTAMPTZ,
  check_out_time TIMESTAMPTZ,
  notes TEXT,
  attendance_method VARCHAR(20) DEFAULT 'manual' CHECK (attendance_method IN ('manual', 'photo', 'qr', 'nfc', 'bulk')),
  metadata JSONB,
  confidence_score DECIMAL(4,3),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 6. Guardians table
CREATE TABLE IF NOT EXISTS guardians (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id),
  full_name VARCHAR(255) NOT NULL,
  relationship VARCHAR(50),
  phone VARCHAR(20),
  email VARCHAR(255),
  address TEXT,
  is_primary BOOLEAN DEFAULT false,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 7. Learner-Guardian relationships
CREATE TABLE IF NOT EXISTS learner_guardians (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  learner_id UUID NOT NULL REFERENCES learners(id) ON DELETE CASCADE,
  guardian_id UUID NOT NULL REFERENCES guardians(id) ON DELETE CASCADE,
  relationship VARCHAR(50),
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(learner_id, guardian_id)
);

-- 8. Indexes for performance
CREATE INDEX IF NOT EXISTS idx_learners_account_id ON learners(account_id);
CREATE INDEX IF NOT EXISTS idx_learners_learner_code ON learners(learner_code);
CREATE INDEX IF NOT EXISTS idx_learners_status ON learners(status);

CREATE INDEX IF NOT EXISTS idx_programs_account_id ON programs(account_id);
CREATE INDEX IF NOT EXISTS idx_programs_status ON programs(status);
CREATE INDEX IF NOT EXISTS idx_programs_program_type ON programs(program_type);

CREATE INDEX IF NOT EXISTS idx_instructors_account_id ON instructors(account_id);
CREATE INDEX IF NOT EXISTS idx_instructors_user_id ON instructors(user_id);

CREATE INDEX IF NOT EXISTS idx_enrollments_account_id ON enrollments(account_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_learner_id ON enrollments(learner_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_program_id ON enrollments(program_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_status ON enrollments(status);

CREATE INDEX IF NOT EXISTS idx_attendance_account_program ON attendance(account_id, program_id);
CREATE INDEX IF NOT EXISTS idx_attendance_learner_date ON attendance(learner_id, session_date);
CREATE INDEX IF NOT EXISTS idx_attendance_session_date ON attendance(session_date);
CREATE INDEX IF NOT EXISTS idx_attendance_method ON attendance(attendance_method);

CREATE INDEX IF NOT EXISTS idx_guardians_account_id ON guardians(account_id);
CREATE INDEX IF NOT EXISTS idx_guardians_user_id ON guardians(user_id);

CREATE INDEX IF NOT EXISTS idx_learner_guardians_learner ON learner_guardians(learner_id);
CREATE INDEX IF NOT EXISTS idx_learner_guardians_guardian ON learner_guardians(guardian_id);

-- 9. RLS Policies
ALTER TABLE learners ENABLE ROW LEVEL SECURITY;
ALTER TABLE programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE instructors ENABLE ROW LEVEL SECURITY;
ALTER TABLE enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE guardians ENABLE ROW LEVEL SECURITY;
ALTER TABLE learner_guardians ENABLE ROW LEVEL SECURITY;

-- Learners policies
CREATE POLICY "Users can view learners in their accounts" ON learners
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage learners in their accounts" ON learners
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Programs policies
CREATE POLICY "Users can view programs in their accounts" ON programs
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage programs in their accounts" ON programs
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Instructors policies
CREATE POLICY "Users can view instructors in their accounts" ON instructors
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage instructors in their accounts" ON instructors
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Enrollments policies
CREATE POLICY "Users can view enrollments in their accounts" ON enrollments
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage enrollments in their accounts" ON enrollments
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Attendance policies
CREATE POLICY "Users can view attendance in their accounts" ON attendance
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage attendance in their accounts" ON attendance
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Guardians policies
CREATE POLICY "Users can view guardians in their accounts" ON guardians
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage guardians in their accounts" ON guardians
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Learner-Guardian relationships policies
CREATE POLICY "Users can view learner-guardian relationships" ON learner_guardians
  FOR SELECT USING (
    learner_id IN (
      SELECT id FROM learners 
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can manage learner-guardian relationships" ON learner_guardians
  FOR ALL USING (
    learner_id IN (
      SELECT id FROM learners 
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships 
        WHERE user_id = auth.uid() 
        AND account_role IN ('owner', 'member')
      )
    )
  );
