-- Seed the roles table with default roles: 'owner' (level 1), 'member' (level 2), and 'customer' (level 3)
-- Plus education-specific roles
insert into public.roles(name, hierarchy_level)
values
    ('owner', 1),
    ('member', 2),
    ('customer', 3),
    ('teacher', 5),
    ('guardian', 6)
on conflict (name) do update set hierarchy_level = excluded.hierarchy_level;

-- Seed the role_permissions table with default permissions
insert into public.role_permissions(role, permission)
values
    -- Owner permissions (full access)
    ('owner', 'teams.manage'),
    ('owner', 'roles.manage'),
    ('owner', 'billing.manage'),
    ('owner', 'settings.manage'),
    ('owner', 'members.manage'),
    ('owner', 'invites.manage'),
    ('owner', 'products.manage'),
    ('owner', 'categories.manage'),
    ('owner', 'orders.manage'),
    ('owner', 'points.manage'),
    ('owner', 'branches.manage'),
    ('owner', 'inventory.manage'),
    ('owner', 'notifications.manage'),
    -- MiniApp permissions
    ('owner', 'miniapps.manage'),
    ('owner', 'miniapps.view'),
    ('owner', 'miniapps.themes.manage'),
    -- Customer permissions
    ('owner', 'customers.manage'),
    ('owner', 'customers.view'),
     -- ZNS permissions
    ('owner', 'zns.manage'),
    ('owner', 'zns.view'),

    -- Integration permissions
    ('owner', 'integrations.manage'),
    ('owner', 'integrations.view'),
    ('owner', 'integrations.connect'),

    -- Data management permissions
    ('owner', 'data.manage'),

    -- CDP permissions
    ('owner', 'cdp.manage'),
    ('owner', 'cdp.view'),
    ('owner', 'cdp.profiles.manage'),
    ('owner', 'cdp.profiles.view'),
    ('owner', 'cdp.segments.manage'),
    ('owner', 'cdp.segments.view'),
    ('owner', 'cdp.journeys.manage'),
    ('owner', 'cdp.journeys.view'),
    ('owner', 'cdp.analytics.view'),
    ('owner', 'cdp.insights.view'),

    -- Education Platform permissions (owner has full access)
    ('owner', 'education.manage'),
    ('owner', 'education.view'),
    ('owner', 'education.learners.manage'),
    ('owner', 'education.learners.view'),
    ('owner', 'education.programs.manage'),
    ('owner', 'education.programs.view'),
    ('owner', 'education.instructors.manage'),
    ('owner', 'education.instructors.view'),
    ('owner', 'education.attendance.manage'),
    ('owner', 'education.attendance.view'),
    ('owner', 'education.fees.manage'),
    ('owner', 'education.fees.view'),
    ('owner', 'education.events.manage'),
    ('owner', 'education.events.view'),
    ('owner', 'education.messages.manage'),
    ('owner', 'education.messages.view'),
    ('owner', 'education.reports.view'),
    ('owner', 'education.analytics.view'),

    -- Member permissions (limited management access)
    ('member', 'customers.manage'),
    ('member', 'branches.manage'),
    ('member', 'miniapps.manage'),
    ('member', 'products.manage'),
    ('member', 'categories.manage'),
    ('member', 'orders.manage'),
    ('member', 'inventory.manage'),
    ('member', 'notifications.manage'),

    -- CDP permissions for members (view only)
    ('member', 'cdp.view'),
    ('member', 'cdp.profiles.view'),
    ('member', 'cdp.segments.view'),
    ('member', 'cdp.journeys.view'),
    ('member', 'cdp.analytics.view'),
    ('member', 'cdp.insights.view'),

    -- Education Platform permissions for members (limited access)
    ('member', 'education.view'),
    ('member', 'education.learners.view'),
    ('member', 'education.programs.view'),
    ('member', 'education.instructors.view'),
    ('member', 'education.attendance.manage'),
    ('member', 'education.attendance.view'),
    ('member', 'education.fees.view'),
    ('member', 'education.events.view'),
    ('member', 'education.messages.view'),
    ('member', 'education.reports.view'),

    -- Customer permissions (view only)
    ('customer', 'orders.view'),
    ('customer', 'points.view'),

    -- Education Platform permissions for customers/guardians (view only)
    ('customer', 'education.learners.view'),
    ('customer', 'education.attendance.view'),
    ('customer', 'education.fees.view'),
    ('customer', 'education.events.view'),
    ('customer', 'education.messages.view'),

    -- Teacher permissions (education-focused)
    ('teacher', 'education.view'),
    ('teacher', 'education.learners.view'),
    ('teacher', 'education.programs.view'),
    ('teacher', 'education.attendance.manage'),
    ('teacher', 'education.attendance.view'),
    ('teacher', 'education.events.view'),
    ('teacher', 'education.messages.manage'),
    ('teacher', 'education.messages.view'),
    ('teacher', 'education.reports.view'),

    -- Guardian permissions (parent-focused)
    ('guardian', 'education.learners.view'),
    ('guardian', 'education.attendance.view'),
    ('guardian', 'education.fees.view'),
    ('guardian', 'education.events.view'),
    ('guardian', 'education.messages.view')
on conflict (role, permission) do nothing;
