-- Enable RLS for all games tables
ALTER TABLE game_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE games ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboard_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_game_progress ENABLE ROW LEVEL SECURITY;

-- Game Categories Policies
CREATE POLICY "Users can view game categories from their account" ON game_categories
  FOR SELECT USING (public.has_role_on_account(account_id));

CREATE POLICY "Users can manage game categories in their account" ON game_categories
  FOR ALL USING (public.has_role_on_account(account_id));

-- Games Policies
CREATE POLICY "Users can view games from their account" ON games
  FOR SELECT USING (public.has_role_on_account(account_id));

CREATE POLICY "Users can manage games in their account" ON games
  FOR ALL USING (public.has_role_on_account(account_id));

-- Game Sessions Policies
CREATE POLICY "Users can view game sessions from their account" ON game_sessions
  FOR SELECT USING (public.has_role_on_account(account_id));

CREATE POLICY "Students can create their own game sessions" ON game_sessions
  FOR INSERT WITH CHECK (
    public.has_role_on_account(account_id) AND 
    student_id = auth.uid()
  );

CREATE POLICY "Students can update their own game sessions" ON game_sessions
  FOR UPDATE USING (
    public.has_role_on_account(account_id) AND 
    student_id = auth.uid()
  );

CREATE POLICY "Teachers can manage all game sessions in their account" ON game_sessions
  FOR ALL USING (public.has_role_on_account(account_id));

-- Achievements Policies
CREATE POLICY "Users can view achievements from their account" ON achievements
  FOR SELECT USING (public.has_role_on_account(account_id));

CREATE POLICY "Users can manage achievements in their account" ON achievements
  FOR ALL USING (public.has_role_on_account(account_id));

-- Student Achievements Policies
CREATE POLICY "Users can view student achievements from their account" ON student_achievements
  FOR SELECT USING (public.has_role_on_account(account_id));

CREATE POLICY "Students can view their own achievements" ON student_achievements
  FOR SELECT USING (
    public.has_role_on_account(account_id) AND 
    student_id = auth.uid()
  );

CREATE POLICY "System can create student achievements" ON student_achievements
  FOR INSERT WITH CHECK (public.has_role_on_account(account_id));

-- Leaderboards Policies
CREATE POLICY "Users can view leaderboards from their account" ON leaderboards
  FOR SELECT USING (public.has_role_on_account(account_id));

CREATE POLICY "Users can manage leaderboards in their account" ON leaderboards
  FOR ALL USING (public.has_role_on_account(account_id));

-- Leaderboard Entries Policies
CREATE POLICY "Users can view leaderboard entries" ON leaderboard_entries
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM leaderboards l 
      WHERE l.id = leaderboard_id 
      AND public.has_role_on_account(l.account_id)
    )
  );

CREATE POLICY "System can manage leaderboard entries" ON leaderboard_entries
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM leaderboards l 
      WHERE l.id = leaderboard_id 
      AND public.has_role_on_account(l.account_id)
    )
  );

-- Game Assignments Policies
CREATE POLICY "Users can view game assignments from their account" ON game_assignments
  FOR SELECT USING (public.has_role_on_account(account_id));

CREATE POLICY "Teachers can manage game assignments in their account" ON game_assignments
  FOR ALL USING (public.has_role_on_account(account_id));

-- Student Game Progress Policies
CREATE POLICY "Users can view student progress from their account" ON student_game_progress
  FOR SELECT USING (public.has_role_on_account(account_id));

CREATE POLICY "Students can view their own progress" ON student_game_progress
  FOR SELECT USING (
    public.has_role_on_account(account_id) AND 
    student_id = auth.uid()
  );

CREATE POLICY "System can manage student progress" ON student_game_progress
  FOR ALL USING (public.has_role_on_account(account_id));
