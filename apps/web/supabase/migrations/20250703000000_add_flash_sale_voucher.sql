-- Migration to add Flash Sale and Voucher features
set search_path to public;

-- Flash Sale Tables
CREATE TABLE public.flash_sales (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'ended', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

COMMENT ON TABLE public.flash_sales IS 'Stores flash sale campaigns with time-limited discounts';
COMMENT ON COLUMN public.flash_sales.status IS 'Status of the flash sale: draft, active, ended, or cancelled';

CREATE TABLE public.flash_sale_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    flash_sale_id UUID NOT NULL REFERENCES public.flash_sales(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    discount_percentage NUMERIC(5,2) NOT NULL CHECK (discount_percentage > 0 AND discount_percentage <= 100),
    quantity_limit INTEGER CHECK (quantity_limit IS NULL OR quantity_limit > 0),
    quantity_sold INTEGER DEFAULT 0 CHECK (quantity_sold >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    UNIQUE (flash_sale_id, product_id)
);

COMMENT ON TABLE public.flash_sale_products IS 'Products included in flash sales with their discount percentages';
COMMENT ON COLUMN public.flash_sale_products.quantity_limit IS 'Maximum quantity available for this product in the flash sale (NULL means unlimited)';
COMMENT ON COLUMN public.flash_sale_products.quantity_sold IS 'Number of units sold during the flash sale';

-- Voucher Tables
CREATE TABLE public.vouchers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    code VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    discount_type VARCHAR(20) NOT NULL CHECK (discount_type IN ('percentage', 'fixed')),
    discount_value NUMERIC(10,2) NOT NULL CHECK (discount_value > 0),
    min_order_value NUMERIC(10,2) CHECK (min_order_value IS NULL OR min_order_value > 0),
    max_discount_value NUMERIC(10,2) CHECK (max_discount_value IS NULL OR max_discount_value > 0),
    max_uses INTEGER CHECK (max_uses IS NULL OR max_uses > 0),
    uses_count INTEGER DEFAULT 0 CHECK (uses_count >= 0),
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'disabled')),
    -- Advanced restrictions for customer-specific vouchers
    is_customer_specific BOOLEAN DEFAULT FALSE,
    usage_limit_per_customer INTEGER,
    first_time_customers_only BOOLEAN DEFAULT FALSE,
    min_previous_orders INTEGER,
    excluded_product_ids UUID[] DEFAULT '{}',
    included_product_ids UUID[] DEFAULT '{}',
    excluded_category_ids UUID[] DEFAULT '{}',
    included_category_ids UUID[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    UNIQUE (account_id, code)
);

COMMENT ON TABLE public.vouchers IS 'Discount vouchers that can be applied to orders';
COMMENT ON COLUMN public.vouchers.discount_type IS 'Type of discount: percentage or fixed amount';
COMMENT ON COLUMN public.vouchers.discount_value IS 'Value of the discount (percentage or fixed amount)';
COMMENT ON COLUMN public.vouchers.min_order_value IS 'Minimum order value required to use the voucher';
COMMENT ON COLUMN public.vouchers.max_discount_value IS 'Maximum discount amount when using percentage discount';
COMMENT ON COLUMN public.vouchers.max_uses IS 'Maximum number of times this voucher can be used (NULL means unlimited)';
COMMENT ON COLUMN public.vouchers.uses_count IS 'Number of times this voucher has been used';
COMMENT ON COLUMN public.vouchers.status IS 'Status of the voucher: active, expired, or disabled';
COMMENT ON COLUMN public.vouchers.is_customer_specific IS 'If true, voucher can only be used by customers in voucher_customers table';
COMMENT ON COLUMN public.vouchers.usage_limit_per_customer IS 'Maximum number of times a single customer can use this voucher';
COMMENT ON COLUMN public.vouchers.first_time_customers_only IS 'If true, voucher can only be used by customers with no previous orders';
COMMENT ON COLUMN public.vouchers.min_previous_orders IS 'Minimum number of previous orders required to use this voucher';
COMMENT ON COLUMN public.vouchers.excluded_product_ids IS 'Products that cannot be discounted by this voucher';
COMMENT ON COLUMN public.vouchers.included_product_ids IS 'Only these products can be discounted by this voucher (empty means all products)';
COMMENT ON COLUMN public.vouchers.excluded_category_ids IS 'Categories that cannot be discounted by this voucher';
COMMENT ON COLUMN public.vouchers.included_category_ids IS 'Only these categories can be discounted by this voucher (empty means all categories)';

CREATE TABLE public.voucher_redemptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    voucher_id UUID NOT NULL REFERENCES public.vouchers(id) ON DELETE CASCADE,
    order_id UUID NOT NULL REFERENCES public.customer_orders(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES auth.users(id),
    discount_amount NUMERIC(10,2) NOT NULL,
    redeemed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (voucher_id, order_id)
);

COMMENT ON TABLE public.voucher_redemptions IS 'Records of voucher usage in orders';
COMMENT ON COLUMN public.voucher_redemptions.discount_amount IS 'Actual discount amount applied to the order';

-- Create a table to store customer phone numbers for vouchers
CREATE TABLE public.voucher_customer_phones (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    voucher_id UUID NOT NULL REFERENCES public.vouchers(id) ON DELETE CASCADE,
    phone_number VARCHAR(20) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    UNIQUE (voucher_id, phone_number)
);

COMMENT ON TABLE public.voucher_customer_phones IS 'Stores phone numbers of customers who can use specific vouchers';
COMMENT ON COLUMN public.voucher_customer_phones.phone_number IS 'Phone number of the customer';

-- Add voucher_id and discount_amount to customer_orders table
ALTER TABLE public.customer_orders
ADD COLUMN IF NOT EXISTS voucher_id UUID REFERENCES public.vouchers(id),
ADD COLUMN IF NOT EXISTS discount_amount NUMERIC(10,2) DEFAULT 0;

-- Add indexes for performance
CREATE INDEX idx_flash_sales_account_id ON public.flash_sales(account_id);
CREATE INDEX idx_flash_sales_status ON public.flash_sales(status);
CREATE INDEX idx_flash_sales_dates ON public.flash_sales(start_time, end_time);
CREATE INDEX idx_flash_sale_products_flash_sale_id ON public.flash_sale_products(flash_sale_id);
CREATE INDEX idx_flash_sale_products_product_id ON public.flash_sale_products(product_id);
CREATE INDEX idx_vouchers_account_id ON public.vouchers(account_id);
CREATE INDEX idx_vouchers_code ON public.vouchers(code);
CREATE INDEX idx_vouchers_status ON public.vouchers(status);
CREATE INDEX idx_vouchers_dates ON public.vouchers(start_date, end_date);
CREATE INDEX idx_voucher_redemptions_voucher_id ON public.voucher_redemptions(voucher_id);
CREATE INDEX idx_voucher_redemptions_order_id ON public.voucher_redemptions(order_id);
CREATE INDEX idx_voucher_redemptions_customer_id ON public.voucher_redemptions(customer_id);
CREATE INDEX idx_voucher_customer_phones_voucher_id ON public.voucher_customer_phones(voucher_id);
CREATE INDEX idx_voucher_customer_phones_phone_number ON public.voucher_customer_phones(phone_number);

-- Add triggers for updating timestamps
CREATE TRIGGER set_flash_sales_timestamp
BEFORE UPDATE ON public.flash_sales
FOR EACH ROW
EXECUTE PROCEDURE trigger_set_timestamps();

CREATE TRIGGER set_flash_sale_products_timestamp
BEFORE UPDATE ON public.flash_sale_products
FOR EACH ROW
EXECUTE PROCEDURE trigger_set_timestamps();

CREATE TRIGGER set_vouchers_timestamp
BEFORE UPDATE ON public.vouchers
FOR EACH ROW
EXECUTE PROCEDURE trigger_set_timestamps();

-- Add triggers for user tracking
CREATE TRIGGER set_flash_sales_user_tracking
BEFORE INSERT OR UPDATE ON public.flash_sales
FOR EACH ROW
EXECUTE PROCEDURE public.trigger_set_user_tracking();

CREATE TRIGGER set_flash_sale_products_user_tracking
BEFORE INSERT OR UPDATE ON public.flash_sale_products
FOR EACH ROW
EXECUTE PROCEDURE public.trigger_set_user_tracking();

CREATE TRIGGER set_vouchers_user_tracking
BEFORE INSERT OR UPDATE ON public.vouchers
FOR EACH ROW
EXECUTE PROCEDURE public.trigger_set_user_tracking();

-- Enable Row Level Security
ALTER TABLE public.flash_sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.flash_sale_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vouchers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.voucher_redemptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.voucher_customer_phones ENABLE ROW LEVEL SECURITY;

-- RLS Policies for flash_sales
CREATE POLICY "Users can view flash sales in their account" ON public.flash_sales
    FOR SELECT TO authenticated
    USING (public.has_role_on_account(account_id));

CREATE POLICY "Users can manage flash sales with permission" ON public.flash_sales
    FOR ALL USING (
        public.has_role_on_account(account_id) AND
        public.has_permission(auth.uid(), account_id, 'products.manage'::public.app_permissions)
    )
    WITH CHECK (
        public.has_role_on_account(account_id) AND
        public.has_permission(auth.uid(), account_id, 'products.manage'::public.app_permissions)
    );

-- RLS Policies for flash_sale_products
CREATE POLICY "Users can view flash sale products in their account" ON public.flash_sale_products
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.flash_sales
            WHERE flash_sales.id = flash_sale_products.flash_sale_id
            AND public.has_role_on_account(flash_sales.account_id)
        )
    );

CREATE POLICY "Users can manage flash sale products with permission" ON public.flash_sale_products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.flash_sales
            WHERE flash_sales.id = flash_sale_products.flash_sale_id
            AND public.has_role_on_account(flash_sales.account_id)
            AND public.has_permission(auth.uid(), flash_sales.account_id, 'products.manage'::public.app_permissions)
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.flash_sales
            WHERE flash_sales.id = flash_sale_products.flash_sale_id
            AND public.has_role_on_account(flash_sales.account_id)
            AND public.has_permission(auth.uid(), flash_sales.account_id, 'products.manage'::public.app_permissions)
        )
    );

-- RLS Policies for vouchers
CREATE POLICY "Users can view vouchers in their account" ON public.vouchers
    FOR SELECT TO authenticated
    USING (public.has_role_on_account(account_id));

CREATE POLICY "Users can manage vouchers with permission" ON public.vouchers
    FOR ALL USING (
        public.has_role_on_account(account_id) AND
        public.has_permission(auth.uid(), account_id, 'products.manage'::public.app_permissions)
    )
    WITH CHECK (
        public.has_role_on_account(account_id) AND
        public.has_permission(auth.uid(), account_id, 'products.manage'::public.app_permissions)
    );

-- RLS Policies for voucher_redemptions
CREATE POLICY "Users can view voucher redemptions in their account" ON public.voucher_redemptions
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.vouchers
            JOIN public.customer_orders ON voucher_redemptions.order_id = customer_orders.id
            WHERE vouchers.id = voucher_redemptions.voucher_id
            AND (
                public.has_role_on_account(vouchers.account_id)
                OR customer_orders.customer_id = auth.uid()
            )
        )
    );

CREATE POLICY "Allow customers to create voucher redemptions" ON public.voucher_redemptions
    FOR INSERT TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.vouchers
            JOIN public.customer_orders ON voucher_redemptions.order_id = customer_orders.id
            WHERE vouchers.id = voucher_redemptions.voucher_id
            AND (
                -- Allow if user has role on account
                public.has_role_on_account(vouchers.account_id)
                OR
                -- Allow if user is the customer of the order
                customer_orders.customer_id = auth.uid()
            )
        )
    );

-- Create policy for service role to manage voucher redemptions
CREATE POLICY "Service role can manage voucher redemptions" ON public.voucher_redemptions
    FOR ALL TO service_role
    USING (true)
    WITH CHECK (true);

-- RLS Policies for voucher_customer_phones
CREATE POLICY "Users can view voucher customer phones in their account" ON public.voucher_customer_phones
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.vouchers
            WHERE vouchers.id = voucher_customer_phones.voucher_id
            AND public.has_role_on_account(vouchers.account_id)
        )
    );

CREATE POLICY "Users can manage voucher customer phones with permission" ON public.voucher_customer_phones
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.vouchers
            WHERE vouchers.id = voucher_customer_phones.voucher_id
            AND public.has_role_on_account(vouchers.account_id)
            AND public.has_permission(auth.uid(), vouchers.account_id, 'products.manage'::public.app_permissions)
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.vouchers
            WHERE vouchers.id = voucher_customer_phones.voucher_id
            AND public.has_role_on_account(vouchers.account_id)
            AND public.has_permission(auth.uid(), vouchers.account_id, 'products.manage'::public.app_permissions)
        )
    );



-- Function to check if a product is on flash sale
CREATE OR REPLACE FUNCTION public.get_product_flash_sale(
    p_product_id UUID,
    p_account_id UUID
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'flash_sale_id', fs.id,
        'flash_sale_name', fs.name,
        'discount_percentage', fsp.discount_percentage,
        'start_time', fs.start_time,
        'end_time', fs.end_time,
        'quantity_limit', fsp.quantity_limit,
        'quantity_sold', fsp.quantity_sold
    ) INTO v_result
    FROM public.flash_sales fs
    JOIN public.flash_sale_products fsp ON fs.id = fsp.flash_sale_id
    WHERE fsp.product_id = p_product_id
    AND fs.account_id = p_account_id
    AND fs.status = 'active'
    AND fs.start_time <= NOW()
    AND fs.end_time > NOW()
    ORDER BY fsp.discount_percentage DESC
    LIMIT 1;

    RETURN COALESCE(v_result, '{"on_sale": false}'::JSONB);
END;
$$ LANGUAGE plpgsql;

-- Function to apply a voucher to an order
CREATE OR REPLACE FUNCTION public.apply_voucher_to_order(
    p_order_id UUID,
    p_voucher_code TEXT,
    p_customer_phone TEXT DEFAULT NULL
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_order public.customer_orders;
    v_voucher public.vouchers;
    v_discount_amount NUMERIC;
    v_order_total NUMERIC;
    v_account_id UUID;
    v_customer_id UUID;
    v_customer_usage_count INTEGER;
    v_customer_order_count INTEGER;
    v_order_items RECORD;
    v_product_id UUID;
    v_category_id UUID;
    v_is_valid_product BOOLEAN;
BEGIN
    -- Get order details
    SELECT * INTO v_order
    FROM public.customer_orders
    WHERE id = p_order_id;

    IF v_order IS NULL THEN
        RETURN jsonb_build_object('success', false, 'message', 'Order not found');
    END IF;

    v_account_id := v_order.account_id;
    v_order_total := v_order.total_amount;
    v_customer_id := v_order.customer_id;

    -- Get voucher details
    SELECT * INTO v_voucher
    FROM public.vouchers
    WHERE code = p_voucher_code
    AND account_id = v_account_id
    AND status = 'active'
    AND start_date <= NOW()
    AND end_date > NOW();

    IF v_voucher IS NULL THEN
        RETURN jsonb_build_object('success', false, 'message', 'Invalid or expired voucher');
    END IF;

    -- Check if voucher has reached max uses
    IF v_voucher.max_uses IS NOT NULL AND v_voucher.uses_count >= v_voucher.max_uses THEN
        RETURN jsonb_build_object('success', false, 'message', 'Voucher usage limit reached');
    END IF;

    -- Check minimum order value
    IF v_voucher.min_order_value IS NOT NULL AND v_order_total < v_voucher.min_order_value THEN
        RETURN jsonb_build_object(
            'success', false,
            'message', 'Order value below minimum requirement of ' || v_voucher.min_order_value
        );
    END IF;

    -- Check if customer-specific and if customer phone is allowed
    IF v_voucher.is_customer_specific AND p_customer_phone IS NOT NULL THEN
        IF NOT EXISTS (
            SELECT 1 FROM public.voucher_customer_phones
            WHERE voucher_id = v_voucher.id
            AND phone_number = p_customer_phone
        ) THEN
            RETURN jsonb_build_object('success', false, 'message', 'This voucher is not available for your phone number');
        END IF;
    END IF;

    -- Check usage limit per customer
    IF v_voucher.usage_limit_per_customer IS NOT NULL THEN
        SELECT COUNT(*) INTO v_customer_usage_count
        FROM public.voucher_redemptions
        WHERE voucher_id = v_voucher.id
        AND customer_id = v_customer_id;

        IF v_customer_usage_count >= v_voucher.usage_limit_per_customer THEN
            RETURN jsonb_build_object('success', false, 'message', 'You have reached the usage limit for this voucher');
        END IF;
    END IF;

    -- Check first-time customers only
    IF v_voucher.first_time_customers_only THEN
        SELECT COUNT(*) INTO v_customer_order_count
        FROM public.customer_orders
        WHERE customer_id = v_customer_id
        AND account_id = v_account_id
        AND id != p_order_id
        AND status IN ('completed', 'delivered');

        IF v_customer_order_count > 0 THEN
            RETURN jsonb_build_object('success', false, 'message', 'This voucher is for first-time customers only');
        END IF;
    END IF;

    -- Check minimum previous orders
    IF v_voucher.min_previous_orders IS NOT NULL THEN
        SELECT COUNT(*) INTO v_customer_order_count
        FROM public.customer_orders
        WHERE customer_id = v_customer_id
        AND account_id = v_account_id
        AND id != p_order_id
        AND status IN ('completed', 'delivered');

        IF v_customer_order_count < v_voucher.min_previous_orders THEN
            RETURN jsonb_build_object(
                'success', false,
                'message', 'You need at least ' || v_voucher.min_previous_orders || ' previous orders to use this voucher'
            );
        END IF;
    END IF;

    -- Check product/category restrictions if there are any
    IF v_voucher.included_product_ids IS NOT NULL AND array_length(v_voucher.included_product_ids, 1) > 0 THEN
        v_is_valid_product := FALSE;

        FOR v_order_items IN
            SELECT product_id FROM public.customer_order_items WHERE order_id = p_order_id
        LOOP
            IF v_order_items.product_id = ANY(v_voucher.included_product_ids) THEN
                v_is_valid_product := TRUE;
                EXIT;
            END IF;
        END LOOP;

        IF NOT v_is_valid_product THEN
            RETURN jsonb_build_object('success', false, 'message', 'This voucher can only be used for specific products');
        END IF;
    END IF;

    IF v_voucher.excluded_product_ids IS NOT NULL AND array_length(v_voucher.excluded_product_ids, 1) > 0 THEN
        FOR v_order_items IN
            SELECT product_id FROM public.customer_order_items WHERE order_id = p_order_id
        LOOP
            IF v_order_items.product_id = ANY(v_voucher.excluded_product_ids) THEN
                RETURN jsonb_build_object('success', false, 'message', 'This voucher cannot be used for some products in your cart');
            END IF;
        END LOOP;
    END IF;

    IF v_voucher.included_category_ids IS NOT NULL AND array_length(v_voucher.included_category_ids, 1) > 0 THEN
        v_is_valid_product := FALSE;

        FOR v_order_items IN
            SELECT p.category_id
            FROM public.customer_order_items oi
            JOIN public.products p ON oi.product_id = p.id
            WHERE oi.order_id = p_order_id
        LOOP
            IF v_order_items.category_id = ANY(v_voucher.included_category_ids) THEN
                v_is_valid_product := TRUE;
                EXIT;
            END IF;
        END LOOP;

        IF NOT v_is_valid_product THEN
            RETURN jsonb_build_object('success', false, 'message', 'This voucher can only be used for specific product categories');
        END IF;
    END IF;

    IF v_voucher.excluded_category_ids IS NOT NULL AND array_length(v_voucher.excluded_category_ids, 1) > 0 THEN
        FOR v_order_items IN
            SELECT p.category_id
            FROM public.customer_order_items oi
            JOIN public.products p ON oi.product_id = p.id
            WHERE oi.order_id = p_order_id
        LOOP
            IF v_order_items.category_id = ANY(v_voucher.excluded_category_ids) THEN
                RETURN jsonb_build_object('success', false, 'message', 'This voucher cannot be used for some product categories in your cart');
            END IF;
        END LOOP;
    END IF;

    -- Calculate discount amount
    IF v_voucher.discount_type = 'percentage' THEN
        v_discount_amount := (v_order_total * v_voucher.discount_value / 100);

        -- Apply max discount if set
        IF v_voucher.max_discount_value IS NOT NULL AND v_discount_amount > v_voucher.max_discount_value THEN
            v_discount_amount := v_voucher.max_discount_value;
        END IF;
    ELSE
        -- Fixed amount discount
        v_discount_amount := v_voucher.discount_value;

        -- Ensure discount doesn't exceed order total
        IF v_discount_amount > v_order_total THEN
            v_discount_amount := v_order_total;
        END IF;
    END IF;

    -- Update order with voucher
    UPDATE public.customer_orders
    SET voucher_id = v_voucher.id,
        discount_amount = v_discount_amount,
        total_amount = v_order_total - v_discount_amount,
        updated_at = NOW(),
        updated_by = auth.uid()
    WHERE id = p_order_id;

    -- Record voucher redemption
    INSERT INTO public.voucher_redemptions (
        voucher_id,
        order_id,
        customer_id,
        discount_amount
    ) VALUES (
        v_voucher.id,
        p_order_id,
        v_customer_id,
        v_discount_amount
    );

    -- Update voucher usage count
    UPDATE public.vouchers
    SET uses_count = uses_count + 1,
        updated_at = NOW(),
        updated_by = auth.uid()
    WHERE id = v_voucher.id;

    RETURN jsonb_build_object(
        'success', true,
        'message', 'Voucher applied successfully',
        'discount_amount', v_discount_amount,
        'new_total', v_order_total - v_discount_amount
    );
END;
$$ LANGUAGE plpgsql;

-- Function to create a flash sale
CREATE OR REPLACE FUNCTION public.create_flash_sale(
    p_account_id UUID,
    p_name TEXT,
    p_description TEXT,
    p_start_time TIMESTAMP WITH TIME ZONE,
    p_end_time TIMESTAMP WITH TIME ZONE,
    p_products JSONB,
    p_status TEXT DEFAULT 'draft'
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_flash_sale_id UUID;
    v_product JSONB;
    v_product_id UUID;
    v_result JSONB := jsonb_build_object('success', true);
BEGIN
    -- Validate inputs
    IF p_end_time <= p_start_time THEN
        RETURN jsonb_build_object('success', false, 'message', 'End time must be after start time');
    END IF;

    -- Insert flash sale
    INSERT INTO public.flash_sales (
        account_id,
        name,
        description,
        start_time,
        end_time,
        status,
        created_by,
        updated_by
    )
    VALUES (
        p_account_id,
        p_name,
        p_description,
        p_start_time,
        p_end_time,
        p_status,
        auth.uid(),
        auth.uid()
    )
    RETURNING id INTO v_flash_sale_id;

    -- Insert products
    FOR v_product IN SELECT * FROM jsonb_array_elements(p_products)
    LOOP
        v_product_id := (v_product->>'product_id')::UUID;

        -- Verify product exists and belongs to account
        IF NOT EXISTS (
            SELECT 1 FROM public.products
            WHERE id = v_product_id
            AND account_id = p_account_id
        ) THEN
            RETURN jsonb_build_object(
                'success', false,
                'message', 'Product ' || v_product_id || ' not found or does not belong to this account'
            );
        END IF;

        -- Insert flash sale product
        INSERT INTO public.flash_sale_products (
            flash_sale_id,
            product_id,
            discount_percentage,
            quantity_limit,
            created_by,
            updated_by
        )
        VALUES (
            v_flash_sale_id,
            v_product_id,
            (v_product->>'discount_percentage')::NUMERIC,
            (v_product->>'quantity_limit')::INTEGER,
            auth.uid(),
            auth.uid()
        );
    END LOOP;

    -- Return success with flash sale ID
    RETURN jsonb_set(v_result, '{flash_sale_id}', to_jsonb(v_flash_sale_id));
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object('success', false, 'message', SQLERRM);
END;
$$ LANGUAGE plpgsql;

-- Function to create a voucher
CREATE OR REPLACE FUNCTION public.create_voucher(
    p_account_id UUID,
    p_code TEXT,
    p_name TEXT,
    p_description TEXT,
    p_discount_type TEXT,
    p_discount_value NUMERIC,
    p_min_order_value NUMERIC,
    p_max_discount_value NUMERIC,
    p_max_uses INTEGER,
    p_start_date TIMESTAMP WITH TIME ZONE,
    p_end_date TIMESTAMP WITH TIME ZONE,
    p_is_customer_specific BOOLEAN DEFAULT FALSE,
    p_usage_limit_per_customer INTEGER DEFAULT NULL,
    p_first_time_customers_only BOOLEAN DEFAULT FALSE,
    p_min_previous_orders INTEGER DEFAULT NULL,
    p_excluded_product_ids UUID[] DEFAULT NULL,
    p_included_product_ids UUID[] DEFAULT NULL,
    p_excluded_category_ids UUID[] DEFAULT NULL,
    p_included_category_ids UUID[] DEFAULT NULL
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_voucher_id UUID;
    v_result JSONB := jsonb_build_object('success', true);
BEGIN
    -- Validate inputs
    IF p_discount_type NOT IN ('percentage', 'fixed') THEN
        RETURN jsonb_build_object('success', false, 'message', 'Invalid discount type');
    END IF;

    IF p_end_date <= p_start_date THEN
        RETURN jsonb_build_object('success', false, 'message', 'End date must be after start date');
    END IF;

    IF p_discount_type = 'percentage' AND (p_discount_value <= 0 OR p_discount_value > 100) THEN
        RETURN jsonb_build_object('success', false, 'message', 'Percentage discount must be between 0 and 100');
    END IF;

    -- Check if code already exists for this account
    IF EXISTS (
        SELECT 1 FROM public.vouchers
        WHERE account_id = p_account_id
        AND code = p_code
    ) THEN
        RETURN jsonb_build_object('success', false, 'message', 'Voucher code already exists');
    END IF;

    -- Insert voucher
    INSERT INTO public.vouchers (
        account_id,
        code,
        name,
        description,
        discount_type,
        discount_value,
        min_order_value,
        max_discount_value,
        max_uses,
        start_date,
        end_date,
        status,
        is_customer_specific,
        usage_limit_per_customer,
        first_time_customers_only,
        min_previous_orders,
        excluded_product_ids,
        included_product_ids,
        excluded_category_ids,
        included_category_ids,
        created_by,
        updated_by
    )
    VALUES (
        p_account_id,
        p_code,
        p_name,
        p_description,
        p_discount_type,
        p_discount_value,
        p_min_order_value,
        p_max_discount_value,
        p_max_uses,
        p_start_date,
        p_end_date,
        'active',
        p_is_customer_specific,
        p_usage_limit_per_customer,
        p_first_time_customers_only,
        p_min_previous_orders,
        p_excluded_product_ids,
        p_included_product_ids,
        p_excluded_category_ids,
        p_included_category_ids,
        auth.uid(),
        auth.uid()
    )
    RETURNING id INTO v_voucher_id;

    -- Return success with voucher ID
    RETURN jsonb_set(v_result, '{voucher_id}', to_jsonb(v_voucher_id));
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object('success', false, 'message', SQLERRM);
END;
$$ LANGUAGE plpgsql;

-- Function to add customer phones to a voucher
CREATE OR REPLACE FUNCTION public.add_customer_phones_to_voucher(
    p_voucher_id UUID,
    p_phone_numbers TEXT[]
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_voucher public.vouchers;
    v_phone TEXT;
    v_added_count INTEGER := 0;
BEGIN
    -- Get voucher details
    SELECT * INTO v_voucher
    FROM public.vouchers
    WHERE id = p_voucher_id;

    IF v_voucher IS NULL THEN
        RETURN jsonb_build_object('success', false, 'message', 'Voucher not found');
    END IF;

    -- Update voucher to be customer-specific if not already
    IF NOT v_voucher.is_customer_specific THEN
        UPDATE public.vouchers
        SET is_customer_specific = TRUE
        WHERE id = p_voucher_id;
    END IF;

    -- Add each phone number to the voucher
    FOREACH v_phone IN ARRAY p_phone_numbers
    LOOP
        -- Insert phone if not already added
        INSERT INTO public.voucher_customer_phones (
            voucher_id,
            phone_number,
            created_by
        )
        VALUES (
            p_voucher_id,
            v_phone,
            auth.uid()
        )
        ON CONFLICT (voucher_id, phone_number) DO NOTHING;

        v_added_count := v_added_count + 1;
    END LOOP;

    RETURN jsonb_build_object(
        'success', true,
        'message', v_added_count || ' phone numbers added to voucher',
        'added_count', v_added_count
    );
END;
$$ LANGUAGE plpgsql;

-- Function to remove customer phones from a voucher
CREATE OR REPLACE FUNCTION public.remove_customer_phones_from_voucher(
    p_voucher_id UUID,
    p_phone_numbers TEXT[]
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_voucher public.vouchers;
    v_removed_count INTEGER;
BEGIN
    -- Get voucher details
    SELECT * INTO v_voucher
    FROM public.vouchers
    WHERE id = p_voucher_id;

    IF v_voucher IS NULL THEN
        RETURN jsonb_build_object('success', false, 'message', 'Voucher not found');
    END IF;

    -- Remove phone numbers from the voucher
    DELETE FROM public.voucher_customer_phones
    WHERE voucher_id = p_voucher_id
    AND phone_number = ANY(p_phone_numbers);

    GET DIAGNOSTICS v_removed_count = ROW_COUNT;

    -- If no phone numbers left and is_customer_specific is true, update it
    IF NOT EXISTS (SELECT 1 FROM public.voucher_customer_phones WHERE voucher_id = p_voucher_id) THEN
        UPDATE public.vouchers
        SET is_customer_specific = FALSE
        WHERE id = p_voucher_id;
    END IF;

    RETURN jsonb_build_object(
        'success', true,
        'message', v_removed_count || ' phone numbers removed from voucher',
        'removed_count', v_removed_count
    );
END;
$$ LANGUAGE plpgsql;

-- Function to update a flash sale
CREATE OR REPLACE FUNCTION public.update_flash_sale(
    p_flash_sale_id UUID,
    p_account_id UUID,
    p_name TEXT,
    p_description TEXT,
    p_start_time TIMESTAMP WITH TIME ZONE,
    p_end_time TIMESTAMP WITH TIME ZONE,
    p_products JSONB,
    p_status TEXT DEFAULT 'draft'
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_flash_sale public.flash_sales;
    v_product JSONB;
    v_product_id UUID;
    v_result JSONB := jsonb_build_object('success', true);
BEGIN
    -- Get flash sale
    SELECT * INTO v_flash_sale
    FROM public.flash_sales
    WHERE id = p_flash_sale_id
    AND account_id = p_account_id;

    IF v_flash_sale IS NULL THEN
        RETURN jsonb_build_object('success', false, 'message', 'Flash sale not found');
    END IF;

    -- Validate inputs
    IF p_end_time <= p_start_time THEN
        RETURN jsonb_build_object('success', false, 'message', 'End time must be after start time');
    END IF;

    -- Update flash sale
    UPDATE public.flash_sales
    SET
        name = p_name,
        description = p_description,
        start_time = p_start_time,
        end_time = p_end_time,
        status = p_status,
        updated_at = NOW(),
        updated_by = auth.uid()
    WHERE id = p_flash_sale_id;

    -- Delete existing products
    DELETE FROM public.flash_sale_products
    WHERE flash_sale_id = p_flash_sale_id;

    -- Insert products
    FOR v_product IN SELECT * FROM jsonb_array_elements(p_products)
    LOOP
        v_product_id := (v_product->>'product_id')::UUID;

        -- Verify product exists and belongs to account
        IF NOT EXISTS (
            SELECT 1 FROM public.products
            WHERE id = v_product_id
            AND account_id = p_account_id
        ) THEN
            RETURN jsonb_build_object(
                'success', false,
                'message', 'Product ' || v_product_id || ' not found or does not belong to this account'
            );
        END IF;

        -- Insert flash sale product
        INSERT INTO public.flash_sale_products (
            flash_sale_id,
            product_id,
            discount_percentage,
            quantity_limit,
            created_by,
            updated_by
        )
        VALUES (
            p_flash_sale_id,
            v_product_id,
            (v_product->>'discount_percentage')::NUMERIC,
            (v_product->>'quantity_limit')::INTEGER,
            auth.uid(),
            auth.uid()
        );
    END LOOP;

    -- Return success
    RETURN v_result;
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object('success', false, 'message', SQLERRM);
END;
$$ LANGUAGE plpgsql;

-- Function to delete a voucher
CREATE OR REPLACE FUNCTION public.delete_voucher(
    p_voucher_id UUID,
    p_account_id UUID
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_voucher public.vouchers;
    v_result JSONB := jsonb_build_object('success', true);
BEGIN
    -- Get voucher
    SELECT * INTO v_voucher
    FROM public.vouchers
    WHERE id = p_voucher_id
    AND account_id = p_account_id;

    IF v_voucher IS NULL THEN
        RETURN jsonb_build_object('success', false, 'message', 'Voucher not found');
    END IF;

    -- Delete voucher
    DELETE FROM public.vouchers
    WHERE id = p_voucher_id;

    -- Return success
    RETURN v_result;
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object('success', false, 'message', SQLERRM);
END;
$$ LANGUAGE plpgsql;

-- Function to delete a flash sale
CREATE OR REPLACE FUNCTION public.delete_flash_sale(
    p_flash_sale_id UUID,
    p_account_id UUID
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_flash_sale public.flash_sales;
    v_result JSONB := jsonb_build_object('success', true);
BEGIN
    -- Get flash sale
    SELECT * INTO v_flash_sale
    FROM public.flash_sales
    WHERE id = p_flash_sale_id
    AND account_id = p_account_id;

    IF v_flash_sale IS NULL THEN
        RETURN jsonb_build_object('success', false, 'message', 'Flash sale not found');
    END IF;

    -- Delete flash sale
    DELETE FROM public.flash_sales
    WHERE id = p_flash_sale_id;

    -- Return success
    RETURN v_result;
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object('success', false, 'message', SQLERRM);
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_product_flash_sale(UUID, UUID) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.apply_voucher_to_order(UUID, TEXT, TEXT) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.create_flash_sale(UUID, TEXT, TEXT, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, JSONB, TEXT) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.update_flash_sale(UUID, UUID, TEXT, TEXT, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, JSONB, TEXT) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.delete_flash_sale(UUID, UUID) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.create_voucher(UUID, TEXT, TEXT, TEXT, TEXT, NUMERIC, NUMERIC, NUMERIC, INTEGER, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, BOOLEAN, INTEGER, BOOLEAN, INTEGER, UUID[], UUID[], UUID[], UUID[]) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.delete_voucher(UUID, UUID) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.add_customer_phones_to_voucher(UUID, TEXT[]) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION public.remove_customer_phones_from_voucher(UUID, TEXT[]) TO authenticated, anon, service_role;
