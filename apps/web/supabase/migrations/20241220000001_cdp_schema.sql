-- CDP (Customer Data Platform) Schema
-- This migration creates all necessary tables for the CDP module

-- Drop existing tables if they exist
DROP TABLE IF EXISTS public.customer_profiles CASCADE;
DROP TABLE IF EXISTS public.customer_segments CASCADE;

-- Customer Profiles Table (Dedicated table for CDP)
CREATE TABLE public.customer_profiles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  team_account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,

  -- Basic profile information
  email varchar(255) NOT NULL,
  first_name varchar(255),
  last_name varchar(255),
  phone varchar(50),
  avatar_url text,

  -- CDP-specific fields
  customer_status varchar(50) DEFAULT 'lead' CHECK (customer_status IN ('lead', 'active', 'inactive', 'churned')),
  value_tier varchar(50) DEFAULT 'low' CHECK (value_tier IN ('low', 'medium', 'high')),

  -- Analytics fields
  total_spent decimal(10,2) DEFAULT 0,
  total_orders integer DEFAULT 0,
  engagement_score decimal(3,2) DEFAULT 0.5 CHECK (engagement_score >= 0 AND engagement_score <= 1),
  churn_risk_score decimal(3,2) DEFAULT 0.1 CHECK (churn_risk_score >= 0 AND churn_risk_score <= 1),

  -- Timestamps
  first_order_at timestamptz,
  last_order_at timestamptz,
  last_active_at timestamptz DEFAULT now(),

  -- Flexible metadata for additional fields
  metadata jsonb DEFAULT '{}',

  -- Audit fields
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  created_by uuid REFERENCES auth.users(id),
  updated_by uuid REFERENCES auth.users(id),

  -- Optional link to real user account (for customers who signup)
  user_account_id uuid REFERENCES public.accounts(id) ON DELETE SET NULL,

  -- Constraints
  UNIQUE(team_account_id, email)
);

-- Customer Segments Table
CREATE TABLE public.customer_segments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,

  -- Basic segment information
  name varchar(255) NOT NULL,
  description text,
  type varchar(50) NOT NULL CHECK (type IN ('behavioral', 'demographic', 'value_based', 'predictive')),

  -- Segment criteria (stored as JSONB for flexibility)
  criteria jsonb NOT NULL DEFAULT '{}',

  -- Segment metrics
  customer_count integer DEFAULT 0,
  growth_rate decimal(5,2) DEFAULT 0, -- Percentage growth rate
  engagement_score decimal(3,2) DEFAULT 0.5 CHECK (engagement_score >= 0 AND engagement_score <= 1),
  avg_value decimal(12,2) DEFAULT 0, -- Average customer value in VND

  -- Segment settings
  is_active boolean DEFAULT true,
  is_auto_updating boolean DEFAULT false,

  -- Metadata
  metadata jsonb DEFAULT '{}',

  -- Audit fields
  created_at timestamptz DEFAULT now() NOT NULL,
  updated_at timestamptz DEFAULT now() NOT NULL,
  created_by uuid REFERENCES auth.users(id),
  updated_by uuid REFERENCES auth.users(id),

  -- Constraints
  UNIQUE(account_id, name)
);

-- Customer Journeys Table
CREATE TABLE IF NOT EXISTS customer_journeys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  description TEXT,
  status VARCHAR DEFAULT 'draft' CHECK (status IN ('active', 'paused', 'draft', 'completed')),
  trigger_type VARCHAR NOT NULL CHECK (trigger_type IN ('segment_entry', 'event', 'date', 'manual')),
  trigger_config JSONB DEFAULT '{}',
  steps JSONB DEFAULT '[]',
  participants INTEGER DEFAULT 0,
  completion_rate DECIMAL DEFAULT 0 CHECK (completion_rate >= 0 AND completion_rate <= 1),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  published_at TIMESTAMP WITH TIME ZONE,
  tags TEXT[] DEFAULT '{}'
);

-- Analytics Data Table
CREATE TABLE IF NOT EXISTS analytics_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  metric_name VARCHAR NOT NULL,
  metric_value DECIMAL NOT NULL,
  metric_type VARCHAR NOT NULL CHECK (metric_type IN ('count', 'percentage', 'currency', 'duration')),
  period VARCHAR DEFAULT 'current_month' CHECK (period IN ('current_day', 'current_week', 'current_month', 'current_year')),
  metadata JSONB DEFAULT '{}',
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Integration Statuses Table
CREATE TABLE IF NOT EXISTS integration_statuses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  provider VARCHAR NOT NULL,
  category VARCHAR NOT NULL,
  status VARCHAR DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error', 'pending')),
  last_sync TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  records_synced INTEGER DEFAULT 0,
  health_score DECIMAL DEFAULT 0 CHECK (health_score >= 0 AND health_score <= 1),
  config JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Insights Table
CREATE TABLE IF NOT EXISTS ai_insights (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  type VARCHAR NOT NULL CHECK (type IN ('trend', 'anomaly', 'prediction', 'recommendation')),
  title VARCHAR NOT NULL,
  description TEXT,
  confidence DECIMAL DEFAULT 0 CHECK (confidence >= 0 AND confidence <= 1),
  impact VARCHAR DEFAULT 'low' CHECK (impact IN ('high', 'medium', 'low')),
  category VARCHAR NOT NULL,
  status VARCHAR DEFAULT 'active' CHECK (status IN ('active', 'dismissed', 'resolved')),
  data JSONB DEFAULT '{}',
  recommendations TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Journey Participants Table (for tracking customer journey progress)
CREATE TABLE IF NOT EXISTS journey_participants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  journey_id UUID NOT NULL REFERENCES customer_journeys(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customer_profiles(id) ON DELETE CASCADE,
  current_step INTEGER DEFAULT 0,
  status VARCHAR DEFAULT 'active' CHECK (status IN ('active', 'completed', 'exited')),
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}'
);

-- Segment Members Table (for tracking which customers belong to which segments)
CREATE TABLE IF NOT EXISTS segment_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  segment_id UUID NOT NULL REFERENCES customer_segments(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customer_profiles(id) ON DELETE CASCADE,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(segment_id, customer_id)
);

-- Create indexes for better performance (updated for new schema)
-- Note: Dedicated indexes are created in the CDP section below

CREATE INDEX IF NOT EXISTS idx_customer_journeys_account_id ON customer_journeys(account_id);
CREATE INDEX IF NOT EXISTS idx_customer_journeys_status ON customer_journeys(status);
CREATE INDEX IF NOT EXISTS idx_customer_journeys_trigger_type ON customer_journeys(trigger_type);

CREATE INDEX IF NOT EXISTS idx_analytics_data_account_id ON analytics_data(account_id);
CREATE INDEX IF NOT EXISTS idx_analytics_data_metric_name ON analytics_data(metric_name);
CREATE INDEX IF NOT EXISTS idx_analytics_data_period ON analytics_data(period);

CREATE INDEX IF NOT EXISTS idx_integration_statuses_account_id ON integration_statuses(account_id);
CREATE INDEX IF NOT EXISTS idx_integration_statuses_status ON integration_statuses(status);

CREATE INDEX IF NOT EXISTS idx_ai_insights_account_id ON ai_insights(account_id);
CREATE INDEX IF NOT EXISTS idx_ai_insights_type ON ai_insights(type);
CREATE INDEX IF NOT EXISTS idx_ai_insights_status ON ai_insights(status);

CREATE INDEX IF NOT EXISTS idx_journey_participants_journey_id ON journey_participants(journey_id);
CREATE INDEX IF NOT EXISTS idx_journey_participants_customer_id ON journey_participants(customer_id);
CREATE INDEX IF NOT EXISTS idx_journey_participants_status ON journey_participants(status);

CREATE INDEX IF NOT EXISTS idx_segment_members_segment_id ON segment_members(segment_id);
CREATE INDEX IF NOT EXISTS idx_segment_members_customer_id ON segment_members(customer_id);

-- Enable Row Level Security (RLS)
ALTER TABLE customer_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_journeys ENABLE ROW LEVEL SECURITY;
ALTER TABLE analytics_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE integration_statuses ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE journey_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE segment_members ENABLE ROW LEVEL SECURITY;

-- Create RLS policies using standard pattern
-- Customer Profiles policies
CREATE POLICY "Users can view customer profiles in their account" ON public.customer_profiles
  FOR SELECT TO authenticated
  USING (public.has_role_on_account(team_account_id));

CREATE POLICY "Users can manage customer profiles with permission" ON public.customer_profiles
  FOR ALL USING (
    public.has_role_on_account(team_account_id) AND
    public.has_permission(auth.uid(), team_account_id, 'customers.manage'::public.app_permissions)
  )
  WITH CHECK (
    public.has_role_on_account(team_account_id) AND
    public.has_permission(auth.uid(), team_account_id, 'customers.manage'::public.app_permissions)
  );

-- Customer Segments policies
CREATE POLICY "Users can view customer segments in their account" ON public.customer_segments
  FOR SELECT TO authenticated
  USING (public.has_role_on_account(account_id));

CREATE POLICY "Users can manage customer segments with permission" ON public.customer_segments
  FOR ALL USING (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'customers.manage'::public.app_permissions)
  )
  WITH CHECK (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'customers.manage'::public.app_permissions)
  );

-- Customer Journeys policies
CREATE POLICY "Users can view customer journeys for their account" ON customer_journeys
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships
      WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin', 'member')
    )
  );

CREATE POLICY "Users can manage customer journeys for their account" ON customer_journeys
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships
      WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin')
    )
  );

-- Analytics Data policies
CREATE POLICY "Users can view analytics data for their account" ON analytics_data
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships
      WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin', 'member')
    )
  );

CREATE POLICY "Users can manage analytics data for their account" ON analytics_data
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships
      WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin')
    )
  );

-- Integration Statuses policies
CREATE POLICY "Users can view integration statuses for their account" ON integration_statuses
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships
      WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin', 'member')
    )
  );

CREATE POLICY "Users can manage integration statuses for their account" ON integration_statuses
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships
      WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin')
    )
  );

-- AI Insights policies
CREATE POLICY "Users can view AI insights for their account" ON ai_insights
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships
      WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin', 'member')
    )
  );

CREATE POLICY "Users can manage AI insights for their account" ON ai_insights
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships
      WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin')
    )
  );

-- Journey Participants policies
CREATE POLICY "Users can view journey participants for their account" ON journey_participants
  FOR SELECT USING (
    journey_id IN (
      SELECT id FROM customer_journeys
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships
        WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin', 'member')
      )
    )
  );

CREATE POLICY "Users can manage journey participants for their account" ON journey_participants
  FOR ALL USING (
    journey_id IN (
      SELECT id FROM customer_journeys
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships
        WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin')
      )
    )
  );

-- Segment Members policies
CREATE POLICY "Users can view segment members for their account" ON segment_members
  FOR SELECT USING (
    segment_id IN (
      SELECT id FROM customer_segments
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships
        WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin', 'member')
      )
    )
  );

CREATE POLICY "Users can manage segment members for their account" ON segment_members
  FOR ALL USING (
    segment_id IN (
      SELECT id FROM customer_segments
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships
        WHERE user_id = auth.uid() AND account_role IN ('owner', 'admin')
      )
    )
  );

-- Create functions for automatic updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at (skip customer_profiles and customer_segments as they have dedicated triggers)
CREATE TRIGGER update_customer_journeys_updated_at BEFORE UPDATE ON customer_journeys FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_integration_statuses_updated_at BEFORE UPDATE ON integration_statuses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ai_insights_updated_at BEFORE UPDATE ON ai_insights FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- CDP DEDICATED FUNCTIONS AND TRIGGERS
-- ========================================

-- Indexes for customer_profiles performance
CREATE INDEX idx_customer_profiles_team_account ON public.customer_profiles(team_account_id);
CREATE INDEX idx_customer_profiles_email ON public.customer_profiles(email);
CREATE INDEX idx_customer_profiles_status ON public.customer_profiles(customer_status);
CREATE INDEX idx_customer_profiles_value_tier ON public.customer_profiles(value_tier);
CREATE INDEX idx_customer_profiles_churn_risk ON public.customer_profiles(churn_risk_score);
CREATE INDEX idx_customer_profiles_total_spent ON public.customer_profiles(total_spent);
CREATE INDEX idx_customer_profiles_last_active ON public.customer_profiles(last_active_at);
CREATE INDEX idx_customer_profiles_metadata_gin ON public.customer_profiles USING gin(metadata);

-- Full-text search index for customer_profiles
CREATE INDEX idx_customer_profiles_search ON public.customer_profiles
USING gin(to_tsvector('english', coalesce(first_name, '') || ' ' || coalesce(last_name, '') || ' ' || coalesce(email, '')));

-- Additional indexes for customer_segments
CREATE INDEX idx_customer_segments_criteria_gin ON public.customer_segments USING gin(criteria);
CREATE INDEX idx_customer_segments_metadata_gin ON public.customer_segments USING gin(metadata);
CREATE INDEX idx_customer_segments_customer_count ON public.customer_segments(customer_count);
CREATE INDEX idx_customer_segments_growth_rate ON public.customer_segments(growth_rate);

-- Full-text search index for customer_segments
CREATE INDEX idx_customer_segments_search ON public.customer_segments
USING gin(to_tsvector('english', coalesce(name, '') || ' ' || coalesce(description, '')));

-- Trigger for customer_profiles updated_at
CREATE OR REPLACE FUNCTION public.update_customer_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  NEW.updated_by = auth.uid();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_customer_profiles_updated_at ON public.customer_profiles;
CREATE TRIGGER update_customer_profiles_updated_at
  BEFORE UPDATE ON public.customer_profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.update_customer_profiles_updated_at();

-- Trigger for customer_segments updated_at
CREATE OR REPLACE FUNCTION public.update_customer_segments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  NEW.updated_by = auth.uid();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_customer_segments_updated_at ON public.customer_segments;
CREATE TRIGGER update_customer_segments_updated_at
  BEFORE UPDATE ON public.customer_segments
  FOR EACH ROW
  EXECUTE FUNCTION public.update_customer_segments_updated_at();

-- Function to calculate value tier based on total_spent
CREATE OR REPLACE FUNCTION public.calculate_value_tier(spent decimal)
RETURNS varchar AS $$
BEGIN
  IF spent >= 1000 THEN
    RETURN 'high';
  ELSIF spent >= 100 THEN
    RETURN 'medium';
  ELSE
    RETURN 'low';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to update customer stats
CREATE OR REPLACE FUNCTION public.update_customer_stats(
  p_customer_id uuid,
  p_order_amount decimal DEFAULT NULL,
  p_order_date timestamptz DEFAULT now()
)
RETURNS void AS $$
BEGIN
  UPDATE public.customer_profiles
  SET
    total_spent = total_spent + COALESCE(p_order_amount, 0),
    total_orders = total_orders + CASE WHEN p_order_amount IS NOT NULL THEN 1 ELSE 0 END,
    last_order_at = CASE WHEN p_order_amount IS NOT NULL THEN p_order_date ELSE last_order_at END,
    last_active_at = p_order_date,
    value_tier = calculate_value_tier(total_spent + COALESCE(p_order_amount, 0)),
    updated_at = now()
  WHERE id = p_customer_id;
END;
$$ LANGUAGE plpgsql;

-- Function to load customer profiles with pagination
CREATE OR REPLACE FUNCTION public.load_customer_profiles_v2(
  p_team_account_id uuid,
  p_search_query text DEFAULT '',
  p_filter varchar DEFAULT 'all',
  p_page integer DEFAULT 1,
  p_limit integer DEFAULT 50
)
RETURNS TABLE (
  id uuid,
  team_account_id uuid,
  email varchar,
  first_name varchar,
  last_name varchar,
  phone varchar,
  avatar_url text,
  customer_status varchar,
  value_tier varchar,
  total_spent decimal,
  total_orders integer,
  engagement_score decimal,
  churn_risk_score decimal,
  churn_risk_level varchar,
  last_active_at timestamptz,
  created_at timestamptz,
  total_count bigint
) AS $$
DECLARE
  offset_value integer;
  search_condition text;
  filter_condition text;
BEGIN
  -- Ensure page_number is at least 1
  p_page := GREATEST(p_page, 1);
  offset_value := (p_page - 1) * p_limit;

  -- Build search condition
  IF p_search_query != '' THEN
    search_condition := format('AND (
      first_name ILIKE %L OR
      last_name ILIKE %L OR
      email ILIKE %L OR
      phone ILIKE %L
    )',
    '%' || p_search_query || '%',
    '%' || p_search_query || '%',
    '%' || p_search_query || '%',
    '%' || p_search_query || '%');
  ELSE
    search_condition := '';
  END IF;

  -- Build filter condition
  CASE p_filter
    WHEN 'highValue' THEN
      filter_condition := 'AND value_tier = ''high''';
    WHEN 'mediumValue' THEN
      filter_condition := 'AND value_tier = ''medium''';
    WHEN 'lowValue' THEN
      filter_condition := 'AND value_tier = ''low''';
    WHEN 'atRisk' THEN
      filter_condition := 'AND churn_risk_score >= 0.7';
    WHEN 'newCustomers' THEN
      filter_condition := 'AND created_at >= (now() - interval ''30 days'')';
    ELSE
      filter_condition := '';
  END CASE;

  -- Execute query with window function for total count
  RETURN QUERY EXECUTE format('
    SELECT
      cp.id,
      cp.team_account_id,
      cp.email,
      cp.first_name,
      cp.last_name,
      cp.phone,
      cp.avatar_url,
      cp.customer_status,
      cp.value_tier,
      cp.total_spent,
      cp.total_orders,
      cp.engagement_score,
      cp.churn_risk_score,
      CASE
        WHEN cp.churn_risk_score >= 0.7 THEN ''high''
        WHEN cp.churn_risk_score >= 0.4 THEN ''medium''
        ELSE ''low''
      END::varchar as churn_risk_level,
      cp.last_active_at,
      cp.created_at,
      COUNT(*) OVER() as total_count
    FROM public.customer_profiles cp
    WHERE cp.team_account_id = %L
    %s %s
    ORDER BY cp.created_at DESC
    LIMIT %s OFFSET %s
  ',
  p_team_account_id,
  search_condition,
  filter_condition,
  p_limit,
  offset_value);
END;
$$ LANGUAGE plpgsql;

-- Function to create customer profile
CREATE OR REPLACE FUNCTION public.create_customer_profile_v2(
  p_team_account_id uuid,
  p_email text,
  p_first_name text,
  p_last_name text,
  p_phone text DEFAULT NULL,
  p_avatar_url text DEFAULT NULL,
  p_metadata jsonb DEFAULT '{}'::jsonb
)
RETURNS uuid AS $$
DECLARE
  new_customer_id uuid;
BEGIN
  -- Insert new customer profile
  INSERT INTO public.customer_profiles (
    team_account_id,
    email,
    first_name,
    last_name,
    phone,
    avatar_url,
    metadata,
    created_by
  ) VALUES (
    p_team_account_id,
    p_email,
    p_first_name,
    p_last_name,
    p_phone,
    p_avatar_url,
    p_metadata,
    auth.uid()
  )
  RETURNING id INTO new_customer_id;

  RETURN new_customer_id;
END;
$$ LANGUAGE plpgsql;

-- Function to delete customer profile
CREATE OR REPLACE FUNCTION public.delete_customer_profile_v2(
  p_customer_id uuid,
  p_team_account_id uuid
)
RETURNS boolean AS $$
DECLARE
  deleted_count integer;
BEGIN
  -- Delete customer profile
  DELETE FROM public.customer_profiles
  WHERE id = p_customer_id
    AND team_account_id = p_team_account_id;

  GET DIAGNOSTICS deleted_count = ROW_COUNT;

  RETURN deleted_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Function to load customer segments with pagination
CREATE OR REPLACE FUNCTION public.load_customer_segments(
  p_account_id uuid,
  p_search_query text DEFAULT '',
  p_filter varchar DEFAULT 'all',
  p_page integer DEFAULT 1,
  p_limit integer DEFAULT 50
)
RETURNS TABLE (
  id uuid,
  account_id uuid,
  name varchar,
  description text,
  type varchar,
  criteria jsonb,
  customer_count integer,
  growth_rate decimal,
  engagement_score decimal,
  avg_value decimal,
  is_active boolean,
  is_auto_updating boolean,
  metadata jsonb,
  created_at timestamptz,
  updated_at timestamptz,
  total_count bigint
) AS $$
DECLARE
  offset_value integer;
  search_condition text;
  filter_condition text;
BEGIN
  -- Ensure page_number is at least 1
  p_page := GREATEST(p_page, 1);
  offset_value := (p_page - 1) * p_limit;

  -- Build search condition
  IF p_search_query != '' THEN
    search_condition := format('AND (
      name ILIKE %L OR
      description ILIKE %L
    )',
    '%' || p_search_query || '%',
    '%' || p_search_query || '%');
  ELSE
    search_condition := '';
  END IF;

  -- Build filter condition
  CASE p_filter
    WHEN 'behavioral' THEN
      filter_condition := 'AND type = ''behavioral''';
    WHEN 'demographic' THEN
      filter_condition := 'AND type = ''demographic''';
    WHEN 'value_based' THEN
      filter_condition := 'AND type = ''value_based''';
    WHEN 'predictive' THEN
      filter_condition := 'AND type = ''predictive''';
    WHEN 'active' THEN
      filter_condition := 'AND is_active = true';
    WHEN 'auto_updating' THEN
      filter_condition := 'AND is_auto_updating = true';
    ELSE
      filter_condition := '';
  END CASE;

  -- Execute query with window function for total count
  RETURN QUERY EXECUTE format('
    SELECT
      cs.id,
      cs.account_id,
      cs.name,
      cs.description,
      cs.type,
      cs.criteria,
      cs.customer_count,
      cs.growth_rate,
      cs.engagement_score,
      cs.avg_value,
      cs.is_active,
      cs.is_auto_updating,
      cs.metadata,
      cs.created_at,
      cs.updated_at,
      COUNT(*) OVER() as total_count
    FROM public.customer_segments cs
    WHERE cs.account_id = %L
    %s %s
    ORDER BY cs.created_at DESC
    LIMIT %s OFFSET %s
  ',
  p_account_id,
  search_condition,
  filter_condition,
  p_limit,
  offset_value);
END;
$$ LANGUAGE plpgsql;

-- Smart sync function for customer profiles when user joins team as customer
CREATE OR REPLACE FUNCTION public.sync_customer_profile_on_membership()
RETURNS TRIGGER AS $$
DECLARE
  user_account RECORD;
  existing_profile RECORD;
BEGIN
  -- Only process customer role memberships
  IF NEW.account_role != 'customer' THEN
    RETURN NEW;
  END IF;

  -- Get user account info
  SELECT a.email, a.name, a.picture_url, a.phone, a.public_data
  INTO user_account
  FROM public.accounts a
  WHERE a.id = NEW.user_id AND a.is_personal_account = true;

  IF NOT FOUND THEN
    RETURN NEW;
  END IF;

  -- Check if customer profile already exists for this team
  SELECT * INTO existing_profile
  FROM public.customer_profiles
  WHERE team_account_id = NEW.account_id
    AND email = user_account.email;

  IF FOUND THEN
    -- Update existing profile with user account link
    UPDATE public.customer_profiles
    SET
      user_account_id = NEW.user_id,
      customer_status = 'active',
      first_name = COALESCE(first_name, split_part(user_account.name, ' ', 1)),
      last_name = COALESCE(last_name,
                          CASE WHEN position(' ' in user_account.name) > 0
                               THEN substring(user_account.name from position(' ' in user_account.name) + 1)
                               ELSE '' END),
      avatar_url = COALESCE(avatar_url, user_account.picture_url),
      phone = COALESCE(phone, user_account.phone),
      metadata = metadata || jsonb_build_object(
        'linked_to_user', true,
        'linked_at', now()
      ),
      updated_at = now(),
      updated_by = NEW.user_id
    WHERE id = existing_profile.id;
  ELSE
    -- Create new customer profile
    INSERT INTO public.customer_profiles (
      team_account_id,
      email,
      first_name,
      last_name,
      phone,
      avatar_url,
      customer_status,
      user_account_id,
      metadata,
      created_by
    ) VALUES (
      NEW.account_id,
      user_account.email,
      split_part(user_account.name, ' ', 1),
      CASE WHEN position(' ' in user_account.name) > 0
           THEN substring(user_account.name from position(' ' in user_account.name) + 1)
           ELSE '' END,
      user_account.phone,
      user_account.picture_url,
      'active',
      NEW.user_id,
      jsonb_build_object(
        'source', 'user_membership',
        'created_from_membership', true,
        'is_real_user', true
      ),
      NEW.user_id
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for smart sync
CREATE TRIGGER sync_customer_profile_on_customer_membership
  AFTER INSERT ON public.accounts_memberships
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_customer_profile_on_membership();

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.calculate_value_tier(decimal) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.update_customer_stats(uuid, decimal, timestamptz) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.load_customer_profiles_v2(uuid, text, varchar, integer, integer) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.create_customer_profile_v2(uuid, text, text, text, text, text, jsonb) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.delete_customer_profile_v2(uuid, uuid) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.load_customer_segments(uuid, text, varchar, integer, integer) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.sync_customer_profile_on_membership() TO authenticated, service_role;
