-- Migration to add the create_sample_data_for_account function
-- This function creates sample data for an account based on industry templates

-- Function to create sample data for an account based on industry template
CREATE OR REPLACE FUNCTION public.create_sample_data_for_account(
    p_account_id UUID,
    p_industry_template TEXT
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_result JSONB := jsonb_build_object(
        'success', true,
        'account_id', p_account_id,
        'industry', p_industry_template,
        'created_at', now(),
        'stats', jsonb_build_object()
    );
    v_category_id UUID;
    v_product_id UUID;
    v_branch_id UUID;
    v_customer_id UUID;
    v_order_id UUID;
    v_theme_id UUID;
    v_category_map JSONB := jsonb_build_object();
    v_product_map JSONB := jsonb_build_object();
    v_branch_map JSONB := jsonb_build_object();
    v_stats JSONB := jsonb_build_object(
        'categories', 0,
        'products', 0,
        'branches', 0,
        'branch_products', 0,
        'orders', 0,
        'order_items', 0,
        'themes', 0
    );
    v_category_record RECORD;
    v_product_record RECORD;
    v_branch_record RECORD;
    v_branch_product_record RECORD;
    v_order_record RECORD;
    v_order_item_record RECORD;
    v_theme_record RECORD;
BEGIN
    -- Log input parameters for debugging
    RAISE NOTICE 'Creating sample data for account_id: %', p_account_id;
    RAISE NOTICE 'Industry template: %', p_industry_template;

    -- Validate JSON format
    BEGIN
        PERFORM p_industry_template::jsonb;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Invalid JSON format: %', SQLERRM;
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Invalid JSON format: ' || SQLERRM,
            'account_id', p_account_id,
            'industry_template', p_industry_template
        );
    END;

    -- Log JSON structure
    RAISE NOTICE 'JSON structure type: %', pg_typeof(p_industry_template::jsonb);
    RAISE NOTICE 'JSON structure length: %', length(p_industry_template);

    -- Try to parse the JSON and log its structure
    DECLARE
        v_json_valid BOOLEAN;
        v_json_keys TEXT[];
    BEGIN
        -- Check if the JSON is valid
        v_json_valid := TRUE;
        BEGIN
            PERFORM p_industry_template::jsonb;
        EXCEPTION WHEN OTHERS THEN
            v_json_valid := FALSE;
            RAISE NOTICE 'Invalid JSON: %', SQLERRM;
        END;

        IF v_json_valid THEN
            -- Get the keys of the JSON object
            SELECT array_agg(key) INTO v_json_keys
            FROM jsonb_object_keys(p_industry_template::jsonb) AS key;

            RAISE NOTICE 'JSON keys: %', v_json_keys;

            -- Log specific sections of the JSON
            RAISE NOTICE 'Account section: %', p_industry_template::jsonb->'account';
            RAISE NOTICE 'Categories section exists: %', (p_industry_template::jsonb->'categories') IS NOT NULL;
            RAISE NOTICE 'Products section exists: %', (p_industry_template::jsonb->'products') IS NOT NULL;
            RAISE NOTICE 'Branches section exists: %', (p_industry_template::jsonb->'branches') IS NOT NULL;

            -- Log array lengths if they exist
            IF (p_industry_template::jsonb->'categories') IS NOT NULL THEN
                RAISE NOTICE 'Categories count: %', jsonb_array_length(p_industry_template::jsonb->'categories');
            END IF;

            IF (p_industry_template::jsonb->'products') IS NOT NULL THEN
                RAISE NOTICE 'Products count: %', jsonb_array_length(p_industry_template::jsonb->'products');
            END IF;

            IF (p_industry_template::jsonb->'branches') IS NOT NULL THEN
                RAISE NOTICE 'Branches count: %', jsonb_array_length(p_industry_template::jsonb->'branches');
            END IF;
        ELSE
            RAISE NOTICE 'Could not parse JSON structure';
        END IF;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error analyzing JSON structure: %', SQLERRM;
    END;

    -- Log the first 1000 characters of the JSON string for debugging
    RAISE NOTICE 'JSON string preview: %', substring(p_industry_template from 1 for 1000);

    -- Validate account exists
    IF NOT EXISTS (SELECT 1 FROM public.accounts WHERE id = p_account_id) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Account not found',
            'account_id', p_account_id
        );
    END IF;

    -- Process categories
    RAISE NOTICE 'Starting to process categories...';

    -- Check if categories array exists and is not empty
    IF (p_industry_template::jsonb->'categories') IS NULL THEN
        RAISE NOTICE 'No categories found in the industry template';
    ELSIF jsonb_array_length(p_industry_template::jsonb->'categories') = 0 THEN
        RAISE NOTICE 'Categories array is empty';
    ELSE
        RAISE NOTICE 'Found % categories in the template', jsonb_array_length(p_industry_template::jsonb->'categories');

        -- Log the first category for debugging
        RAISE NOTICE 'First category: %', (p_industry_template::jsonb->'categories'->0);
    END IF;

    -- Create a temporary table to store categories for debugging
    CREATE TEMP TABLE IF NOT EXISTS temp_categories AS
    SELECT * FROM jsonb_to_recordset(p_industry_template::jsonb->'categories') AS x(
        name TEXT,
        description TEXT,
        parent_id UUID,
        image_url TEXT
    );

    -- Log the number of categories in the temp table
    DECLARE
        v_temp_count INT;
        v_temp_record RECORD;
    BEGIN
        SELECT COUNT(*) INTO v_temp_count FROM temp_categories;
        RAISE NOTICE 'Temp categories count: %', v_temp_count;

        -- Log the first few categories
        FOR v_temp_record IN
            SELECT * FROM temp_categories LIMIT 3
        LOOP
            RAISE NOTICE 'Temp category: % - %', v_temp_record.name, v_temp_record.description;
        END LOOP;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error checking temp categories: %', SQLERRM;
    END;

    FOR v_category_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'categories', '[]'::jsonb)
        ) AS x(
            name TEXT,
            description TEXT,
            parent_id UUID,
            image_url TEXT
        )
    LOOP
        BEGIN
            -- Log the category we're about to insert
            RAISE NOTICE 'Inserting category: %, description: %',
                v_category_record.name,
                v_category_record.description;

            -- Only insert if we have a valid category name
            IF v_category_record.name IS NULL OR v_category_record.name = '' THEN
                RAISE NOTICE 'Skipping category with empty name';
                CONTINUE;
            END IF;

            INSERT INTO public.categories (
                account_id,
                name,
                description,
                image_url,
                parent_id
            ) VALUES (
                p_account_id,
                v_category_record.name,
                v_category_record.description,
                v_category_record.image_url,
                NULL -- We'll update parent_id in a second pass
            )
            RETURNING id INTO v_category_id;

            RAISE NOTICE 'Category inserted successfully with ID: %', v_category_id;

            -- Store category ID mapping for later use
            v_category_map := jsonb_set(
                v_category_map,
                ARRAY[v_category_record.name],
                to_jsonb(v_category_id)
            );

            -- Log the updated category map
            RAISE NOTICE 'Updated category map: %', v_category_map;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting category %: %', v_category_record.name, SQLERRM;
            -- Continue with next category
            CONTINUE;
        END;

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['categories'],
            to_jsonb((v_stats->>'categories')::int + 1)
        );
    END LOOP;

    -- Process products
    RAISE NOTICE 'Starting to process products...';

    -- Check if products array exists and is not empty
    IF (p_industry_template::jsonb->'products') IS NULL THEN
        RAISE NOTICE 'No products found in the industry template';
    ELSIF jsonb_array_length(p_industry_template::jsonb->'products') = 0 THEN
        RAISE NOTICE 'Products array is empty';
    ELSE
        RAISE NOTICE 'Found % products in the template', jsonb_array_length(p_industry_template::jsonb->'products');

        -- Log the first product for debugging
        RAISE NOTICE 'First product: %', (p_industry_template::jsonb->'products'->0);
    END IF;

    -- Create a temporary table to store products for debugging
    CREATE TEMP TABLE IF NOT EXISTS temp_products AS
    SELECT * FROM jsonb_to_recordset(p_industry_template::jsonb->'products') AS x(
        name TEXT,
        category_name TEXT,
        type TEXT,
        price NUMERIC,
        compare_at_price NUMERIC,
        image_url TEXT,
        status TEXT,
        sku TEXT
    );

    -- Log the number of products in the temp table
    DECLARE
        v_temp_prod_count INT;
        v_temp_record RECORD;
    BEGIN
        SELECT COUNT(*) INTO v_temp_prod_count FROM temp_products;
        RAISE NOTICE 'Temp products count: %', v_temp_prod_count;

        -- Log the first few products
        FOR v_temp_record IN
            SELECT * FROM temp_products LIMIT 3
        LOOP
            RAISE NOTICE 'Temp product: % - % - %', v_temp_record.name, v_temp_record.category_name, v_temp_record.price;
        END LOOP;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error checking temp products: %', SQLERRM;
    END;

    FOR v_product_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'products', '[]'::jsonb)
        ) AS x(
            name TEXT,
            category_name TEXT,
            type TEXT,
            price NUMERIC,
            compare_at_price NUMERIC,
            image_url TEXT,
            status TEXT,
            sku TEXT
        )
    LOOP
        -- Get category ID from the mapping
        BEGIN
            -- Log the category name we're looking for
            RAISE NOTICE 'Looking for category name: %', v_product_record.category_name;
            RAISE NOTICE 'Category map: %', v_category_map;
            RAISE NOTICE 'Product record: %', row_to_json(v_product_record);

            -- Try to get the category ID from the mapping
            IF v_product_record.category_name IS NULL THEN
                RAISE NOTICE 'Category name is NULL for product %', v_product_record.name;
                v_category_id := NULL;
            ELSE
                -- Try to get the category ID from the mapping
                BEGIN
                    v_category_id := (v_category_map->>v_product_record.category_name)::UUID;
                    RAISE NOTICE 'Category ID from map: %', v_category_id;
                EXCEPTION WHEN OTHERS THEN
                    RAISE NOTICE 'Error getting category ID from map: %', SQLERRM;
                    v_category_id := NULL;
                END;

                -- If not found in the map, try to find it directly from the database
                IF v_category_id IS NULL THEN
                    RAISE NOTICE 'Category ID not found in map for %, trying direct lookup', v_product_record.category_name;

                    BEGIN
                        SELECT id INTO v_category_id
                        FROM public.categories
                        WHERE account_id = p_account_id
                        AND name = v_product_record.category_name
                        LIMIT 1;

                        RAISE NOTICE 'Category ID from direct lookup: %', v_category_id;
                    EXCEPTION WHEN OTHERS THEN
                        RAISE NOTICE 'Error in direct category lookup: %', SQLERRM;
                        v_category_id := NULL;
                    END;

                    RAISE NOTICE 'Direct lookup result: %', v_category_id;

                    -- If still not found, use the first category as a fallback
                    IF v_category_id IS NULL THEN
                        RAISE NOTICE 'Category still not found, using first category as fallback';

                        SELECT id INTO v_category_id
                        FROM public.categories
                        WHERE account_id = p_account_id
                        LIMIT 1;

                        RAISE NOTICE 'Fallback category ID: %', v_category_id;
                    END IF;
                END IF;
            END IF;

            -- Log the result
            RAISE NOTICE 'Final category ID for product %: %', v_product_record.name, v_category_id;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error getting category ID: %', SQLERRM;
            v_category_id := NULL;
        END;

        -- Log product data before insertion
        RAISE NOTICE 'Preparing to insert product: %, category_id: %', v_product_record.name, v_category_id;

        BEGIN
            -- Only insert if we have a valid product name
            IF v_product_record.name IS NULL OR v_product_record.name = '' THEN
                RAISE NOTICE 'Skipping product with empty name';
            ELSE
                -- Create a description with the product name
                DECLARE
                    v_description TEXT;
                BEGIN
                    v_description := 'Sample product: ' || v_product_record.name;
                    IF v_product_record.category_name IS NOT NULL THEN
                        v_description := v_description || ' (Category: ' || v_product_record.category_name || ')';
                    END IF;

                    -- Insert the product
                    INSERT INTO public.products (
                        account_id,
                        category_id,
                        name,
                        description,
                        type,
                        price,
                        compare_at_price,
                        image_url,
                        status,
                        sku
                    ) VALUES (
                        p_account_id,
                        v_category_id,
                        v_product_record.name,
                        v_description,
                        COALESCE(v_product_record.type, 'physical')::product_type,
                        COALESCE(v_product_record.price, 0),
                        v_product_record.compare_at_price,
                        v_product_record.image_url,
                        COALESCE(v_product_record.status, 'active'),
                        v_product_record.sku
                    )
                    RETURNING id INTO v_product_id;

                    RAISE NOTICE 'Product inserted successfully with ID: %', v_product_id;

                    -- Update stats (moved to after the exception handling)
                    -- This will be done once per product after the exception block
                END;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error inserting product %: %', v_product_record.name, SQLERRM;
            -- Continue with next product
        END;

        -- Store product ID mapping for later use
        v_product_map := jsonb_set(
            v_product_map,
            ARRAY[v_product_record.name],
            to_jsonb(v_product_id)
        );

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['products'],
            to_jsonb((v_stats->>'products')::int + 1)
        );
    END LOOP;

    -- Process branches
    FOR v_branch_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'branches', '[]'::jsonb)
        ) AS x(
            name TEXT,
            address TEXT,
            phone TEXT,
            is_active BOOLEAN
        )
    LOOP
        INSERT INTO public.branches (
            account_id,
            name,
            address,
            phone,
            is_active
        ) VALUES (
            p_account_id,
            v_branch_record.name,
            v_branch_record.address,
            v_branch_record.phone,
            v_branch_record.is_active
        )
        RETURNING id INTO v_branch_id;

        -- Store branch ID mapping for later use
        v_branch_map := jsonb_set(
            v_branch_map,
            ARRAY[v_branch_record.name],
            to_jsonb(v_branch_id)
        );

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['branches'],
            to_jsonb((v_stats->>'branches')::int + 1)
        );
    END LOOP;

    -- Process branch products
    FOR v_branch_product_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'branch_products', '[]'::jsonb)
        ) AS x(
            product_name TEXT,
            branch_name TEXT,
            is_active BOOLEAN
        )
    LOOP
        -- Get product and branch IDs from the mappings
        BEGIN
            v_product_id := (v_product_map->>v_branch_product_record.product_name)::UUID;
        EXCEPTION WHEN OTHERS THEN
            v_product_id := NULL;
        END;

        BEGIN
            v_branch_id := (v_branch_map->>v_branch_product_record.branch_name)::UUID;
        EXCEPTION WHEN OTHERS THEN
            v_branch_id := NULL;
        END;

        -- Only insert if both product and branch exist
        IF v_product_id IS NOT NULL AND v_branch_id IS NOT NULL THEN
            INSERT INTO public.branch_products (
                branch_id,
                product_id,
                is_active
            ) VALUES (
                v_branch_id,
                v_product_id,
                v_branch_product_record.is_active
            )
            ON CONFLICT (branch_id, product_id) DO NOTHING;

            -- Update stats
            v_stats := jsonb_set(
                v_stats,
                ARRAY['branch_products'],
                to_jsonb((v_stats->>'branch_products')::int + 1)
            );
        END IF;
    END LOOP;

    -- Process customer orders
    FOR v_order_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'customer_orders', '[]'::jsonb)
        ) AS x(
            customer_id TEXT,
            branch_name TEXT,
            total_amount NUMERIC,
            payment_method TEXT,
            status TEXT,
            items JSONB
        )
    LOOP
        -- Get branch ID from the mapping
        BEGIN
            v_branch_id := (v_branch_map->>v_order_record.branch_name)::UUID;
        EXCEPTION WHEN OTHERS THEN
            v_branch_id := NULL;
        END;

        -- Create a sample customer if needed
        -- For demo purposes, we'll create a new customer for each order
        INSERT INTO auth.users (
            instance_id,
            id,
            aud,
            role,
            email,
            encrypted_password,
            email_confirmed_at,
            raw_app_meta_data,
            raw_user_meta_data,
            created_at,
            updated_at
        ) VALUES (
            '00000000-0000-0000-0000-000000000000',
            gen_random_uuid(),
            'authenticated',
            'authenticated',
            'sample-' || gen_random_uuid() || '@example.com',
            '$2a$10$NaMVRrI7NyfwP.AfAVWt6O/abulGnf9BBqwa6DqdMwXMvOCGpAnVO', -- dummy password hash
            now(),
            jsonb_build_object(
                'provider', 'email',
                'providers', array['email'],
                'role', 'customer'
            ),
            jsonb_build_object(
                'name', 'Sample Customer',
                'account_id', p_account_id
            ),
            now(),
            now()
        )
        RETURNING id INTO v_customer_id;

        -- Add customer to account as member with customer role
        INSERT INTO public.accounts_memberships (
            user_id,
            account_id,
            account_role
        ) VALUES (
            v_customer_id,
            p_account_id,
            'customer'
        );

        -- Create the order
        INSERT INTO public.customer_orders (
            account_id,
            customer_id,
            branch_id,
            total_amount,
            payment_method,
            status
        ) VALUES (
            p_account_id,
            v_customer_id,
            v_branch_id,
            v_order_record.total_amount,
            v_order_record.payment_method,
            v_order_record.status
        )
        RETURNING id INTO v_order_id;

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['orders'],
            to_jsonb((v_stats->>'orders')::int + 1)
        );

        -- Process order items
        FOR v_order_item_record IN
            SELECT * FROM jsonb_to_recordset(v_order_record.items) AS x(
                product_name TEXT,
                quantity INTEGER,
                price NUMERIC
            )
        LOOP
            -- Get product ID from the mapping
            BEGIN
                v_product_id := (v_product_map->>v_order_item_record.product_name)::UUID;
            EXCEPTION WHEN OTHERS THEN
                v_product_id := NULL;
            END;

            -- Only insert if product exists
            IF v_product_id IS NOT NULL THEN
                INSERT INTO public.customer_order_items (
                    order_id,
                    product_id,
                    quantity,
                    price
                ) VALUES (
                    v_order_id,
                    v_product_id,
                    v_order_item_record.quantity,
                    v_order_item_record.price
                );

                -- Update stats
                v_stats := jsonb_set(
                    v_stats,
                    ARRAY['order_items'],
                    to_jsonb((v_stats->>'order_items')::int + 1)
                );
            END IF;
        END LOOP;
    END LOOP;

    -- Process account themes
    FOR v_theme_record IN
        SELECT * FROM jsonb_to_recordset(
            (p_industry_template::jsonb->'account_themes')
        ) AS x(
            theme_id UUID,
            theme_name TEXT,
            mini_app_id TEXT,
            primary_color TEXT,
            secondary_color TEXT,
            logo_url TEXT
        )
    LOOP
        -- First, check if we have a themes table
        IF EXISTS (
            SELECT 1
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = 'themes'
        ) THEN
            -- Check if there's a unique constraint on name column
            DECLARE
                has_unique_constraint BOOLEAN;
            BEGIN
                SELECT EXISTS (
                    SELECT 1
                    FROM pg_constraint
                    WHERE conrelid = 'public.themes'::regclass
                    AND contype = 'u'
                    AND conkey @> ARRAY[(
                        SELECT attnum
                        FROM pg_attribute
                        WHERE attrelid = 'public.themes'::regclass
                        AND attname = 'name'
                    )]
                ) INTO has_unique_constraint;

                IF has_unique_constraint THEN
                    -- If there's a unique constraint, use ON CONFLICT
                    INSERT INTO public.themes (
                        name,
                        config
                    ) VALUES (
                        v_theme_record.theme_name,
                        jsonb_build_object(
                            'primaryColor', v_theme_record.primary_color,
                            'secondaryColor', v_theme_record.secondary_color,
                            'logoUrl', v_theme_record.logo_url
                        )
                    )
                    ON CONFLICT (name) DO UPDATE
                    SET config = EXCLUDED.config
                    RETURNING id INTO v_theme_id;
                ELSE
                    -- If there's no unique constraint, use a different approach
                    -- First check if the theme exists
                    SELECT id INTO v_theme_id
                    FROM public.themes
                    WHERE name = v_theme_record.theme_name;

                    IF v_theme_id IS NULL THEN
                        -- If not exists, insert
                        INSERT INTO public.themes (
                            name,
                            config
                        ) VALUES (
                            v_theme_record.theme_name,
                            jsonb_build_object(
                                'primaryColor', v_theme_record.primary_color,
                                'secondaryColor', v_theme_record.secondary_color,
                                'logoUrl', v_theme_record.logo_url
                            )
                        )
                        RETURNING id INTO v_theme_id;
                    ELSE
                        -- If exists, update
                        UPDATE public.themes
                        SET config = jsonb_build_object(
                            'primaryColor', v_theme_record.primary_color,
                            'secondaryColor', v_theme_record.secondary_color,
                            'logoUrl', v_theme_record.logo_url
                        )
                        WHERE id = v_theme_id;
                    END IF;
                END IF;
            EXCEPTION WHEN OTHERS THEN
                -- If any error occurs, log it and continue
                RAISE NOTICE 'Error creating/updating theme: %', SQLERRM;
                v_theme_id := NULL;
            END;

            -- Get system default OA config
            DECLARE
                v_oa_config_id UUID;
            BEGIN
                -- Get system default OA config
                v_oa_config_id := NULL;

                -- Try to get a system default OA config
                BEGIN
                    -- Check if is_system_default column exists
                    IF EXISTS (
                        SELECT 1
                        FROM information_schema.columns
                        WHERE table_schema = 'public'
                        AND table_name = 'oa_configurations'
                        AND column_name = 'is_system_default'
                    ) THEN
                        -- Use is_system_default if it exists
                        SELECT id INTO v_oa_config_id
                        FROM public.oa_configurations
                        WHERE is_system_default = TRUE
                        ORDER BY created_at DESC
                        LIMIT 1;
                    ELSE
                        -- Fallback: get a shared OA config (likely system default)
                        SELECT id INTO v_oa_config_id
                        FROM public.oa_configurations
                        WHERE oa_type = 'shared'
                        ORDER BY created_at DESC
                        LIMIT 1;
                    END IF;
                EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        v_oa_config_id := NULL;
                END;

                -- Associate theme with account
                IF EXISTS (
                    SELECT 1
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = 'account_themes'
                ) THEN
                    INSERT INTO public.account_themes (
                        account_id,
                        template_id,
                        name,
                        config,
                        is_active,
                        oa_config_id,
                        mini_app_id
                    ) VALUES (
                        p_account_id,
                        COALESCE(v_theme_record.theme_id, v_theme_id),
                        v_theme_record.theme_name,
                        jsonb_build_object(
                            'primaryColor', v_theme_record.primary_color,
                            'secondaryColor', v_theme_record.secondary_color,
                            'logoUrl', v_theme_record.logo_url,
                            'template', COALESCE(v_theme_record.theme_id, v_theme_id)
                        ),
                        true,
                        v_oa_config_id,
                        v_theme_record.mini_app_id
                    )
                    ON CONFLICT (account_id, name) DO UPDATE
                    SET config = EXCLUDED.config,
                        is_active = true,
                        template_id = EXCLUDED.template_id,
                        mini_app_id = EXCLUDED.mini_app_id;

                    -- Update stats
                    v_stats := jsonb_set(
                        v_stats,
                        ARRAY['themes'],
                        to_jsonb((v_stats->>'themes')::int + 1)
                    );
                END IF;
            END;
        END IF;
    END LOOP;

    -- Update the result with stats
    v_result := jsonb_set(v_result, ARRAY['stats'], v_stats);

    -- Log final stats and result
    RAISE NOTICE 'Final stats: %', v_stats;
    RAISE NOTICE 'Final result: %', v_result;

    -- Check if any data was created
    DECLARE
        v_product_count INT;
        v_category_count INT;
    BEGIN
        -- Check products
        SELECT COUNT(*) INTO v_product_count FROM public.products WHERE account_id = p_account_id;
        RAISE NOTICE 'Product count for account %: %', p_account_id, v_product_count;

        -- Check categories
        SELECT COUNT(*) INTO v_category_count FROM public.categories WHERE account_id = p_account_id;
        RAISE NOTICE 'Category count for account %: %', p_account_id, v_category_count;

        -- Add counts to result
        v_result := jsonb_set(v_result, ARRAY['actual_counts'], jsonb_build_object(
            'products', v_product_count,
            'categories', v_category_count
        ));
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'Error checking data counts: %', SQLERRM;
    END;

    RETURN v_result;
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'account_id', p_account_id,
            'industry', p_industry_template
        );
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.create_sample_data_for_account(UUID, TEXT) TO authenticated, service_role;

-- Add comment to the function
COMMENT ON FUNCTION public.create_sample_data_for_account(UUID, TEXT) IS
'Creates sample data for an account based on industry templates defined in mockup.config.ts.
This function creates categories, products, branches, branch products, customer orders, and themes.';
