
-- Enum for OA type
create type public.oa_type as enum ('shared', 'private');

-- OA Configurations table
create table public.oa_configurations (
  id uuid default extensions.uuid_generate_v4() primary key,
  account_id uuid references public.accounts(id) on delete cascade,
  oa_type public.oa_type not null,
  oa_id text,
  user_oa_id text,
  app_id text unique,
  secret_key text,
  access_token text,
  refresh_token text,
  token_expires_at timestamptz,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  constraint valid_oa_config check (
    (oa_type = 'shared' and account_id is null) or
    (oa_type = 'private' and account_id is not null)
  ),
   constraint unique_account_oa_config unique (account_id) -- <PERSON><PERSON><PERSON> bảo mỗi account chỉ có một oa_configurations
);

-- RLS policies cho OA configurations
alter table public.oa_configurations enable row level security;

-- Indexes
create index idx_oa_configurations_account_id on public.oa_configurations(account_id);
create index idx_oa_configurations_oa_type on public.oa_configurations(oa_type);



-- Enum for theme type
create type public.theme_type as enum ('free', 'paid', 'custom', 'default');

-- Bảng themes (theme mẫu)
create table public.themes (
  id uuid primary key default extensions.uuid_generate_v4(),
  name text not null,
  description text,
  thumbnail_url text,
  preview_url text,
  type public.theme_type not null,
  category text check (category in ('ecommerce', 'restaurant', 'blog', 'portfolio')),
  config jsonb not null default '{}'::jsonb,
  version text default '1.0.0',
  author_id uuid references auth.users(id),
  oa_config_id uuid references public.oa_configurations(id) on delete set null,
  mini_app_id text unique, -- Đảm bảo mini_app_id là duy nhất
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Policies cho themes
alter table public.themes enable row level security;

create policy "Public can read themes" on public.themes
  for select to authenticated
  using (true);
create policy "Authors can manage their themes" on public.themes
  for all to authenticated
  using (author_id = auth.uid());

-- Indexes cho themes
create index idx_themes_type on public.themes(type);
create index idx_themes_category on public.themes(category);
create index idx_themes_oa_config_id on public.themes(oa_config_id);
create index idx_themes_mini_app_id on public.themes(mini_app_id);
create unique index idx_themes_default_type on public.themes (type) where type = 'default';

-- Trigger để kiểm tra oa_config_id
create or replace function public.validate_oa_config_type()
returns trigger as $$
begin
  if new.oa_config_id is not null then
    perform 1 from public.oa_configurations
    where id = new.oa_config_id
    and (
      (new.type = 'default' and oa_type = 'shared') or
      (new.type != 'default' and oa_type = 'private')
    );
    if not found then
      raise exception 'Invalid oa_config_id for theme type';
    end if;
  end if;
  return new;
end;
$$ language plpgsql;

create trigger validate_oa_config_type_trigger
before insert or update on public.themes
for each row execute function public.validate_oa_config_type();

-- Bảng account_themes (theme của mỗi tài khoản)
create table public.account_themes (
  id uuid primary key default extensions.uuid_generate_v4(),
  account_id uuid not null references public.accounts(id) on delete cascade,
  template_id uuid references public.themes(id),
  name text not null,
  config jsonb not null default '{}'::jsonb,
  is_active boolean default false,
  version text default '1.0.0',
  oa_config_id uuid references public.oa_configurations(id) on delete set null,
  mini_app_id text, -- MiniApp ID trên chợ Zalo
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  unique (account_id, name)
);

-- Policies cho account_themes
alter table public.account_themes enable row level security;


-- Policy cho SELECT - Service role và authenticated users
create policy "Enable read access for all users" on public.oa_configurations
  for select
  using (
    (auth.role() = 'service_role') or  -- Service role có thể đọc tất cả
    (
       -- Cho phép đọc OA nếu user có quyền xem theme sử dụng OA đó
           exists (
             select 1 from public.themes t
             where t.oa_config_id = oa_configurations.id
           ) or
           -- Cho phép đọc OA nếu user có quyền xem account theme sử dụng OA đó
           exists (
             select 1 from public.account_themes at
             where at.oa_config_id = oa_configurations.id
             and public.has_role_on_account(at.account_id)
           )

    )
  );

-- Policy cho INSERT/UPDATE/DELETE - Service role và authenticated users
create policy "Enable write access for all users" on public.oa_configurations
  for all
  using (
    (auth.role() = 'service_role') or  -- Service role có thể write tất cả
    (
      auth.role() = 'authenticated' and
      (
        -- Cho phép write OA nếu user là author của theme sử dụng OA đó
        exists (
          select 1 from public.themes t
          where t.oa_config_id = oa_configurations.id
          and t.author_id = auth.uid()
        ) or
        -- Cho phép write OA nếu user có quyền quản lý account theme sử dụng OA đó
        exists (
          select 1 from public.account_themes at
          where at.oa_config_id = oa_configurations.id
          and public.has_role_on_account(at.account_id)
        )
      )
    )
  )
  with check (
    (auth.role() = 'service_role') or  -- Service role có thể write tất cả
    (
      auth.role() = 'authenticated' and
      (
        exists (
          select 1 from public.themes t
          where t.oa_config_id = oa_configurations.id
          and t.author_id = auth.uid()
        ) or
        exists (
          select 1 from public.account_themes at
          where at.oa_config_id = oa_configurations.id
          and public.has_role_on_account(at.account_id)
        )
      )
    )
  );




create policy "Owner can manage account_themes" on public.account_themes
  for all to authenticated
  using (public.has_role_on_account(account_id));

-- Indexes cho account_themes
create index idx_account_themes_account_id on public.account_themes(account_id);
create index idx_account_themes_template_id on public.account_themes(template_id);
create index idx_account_themes_is_active on public.account_themes(is_active);
create index idx_account_themes_oa_config_id on public.account_themes(oa_config_id);
create index idx_account_themes_mini_app_id on public.account_themes(mini_app_id);

-- Bảng temp_themes (theme tạm thời cho preview)
create table public.temp_themes (
  id uuid primary key default extensions.uuid_generate_v4(),
  account_id uuid not null references public.accounts(id) on delete cascade,
  account_theme_id uuid references public.account_themes(id),
  theme_id uuid references public.themes(id),
  config jsonb not null default '{}'::jsonb,
  preview_token text not null unique,
  expires_at timestamptz not null,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  check (expires_at > created_at),
  constraint theme_reference_check check (
    (theme_id is not null and account_theme_id is null) or
    (theme_id is null and account_theme_id is not null)
  )
);
-- Enable realtime
alter publication supabase_realtime add table public.temp_themes;

-- Policies cho temp_themes
alter table public.temp_themes enable row level security;
create policy "Owner can manage temp_themes" on public.temp_themes
  for all to authenticated
  using (public.has_role_on_account(account_id));

-- Add REPLICA IDENTITY for temp_themes table
ALTER TABLE public.temp_themes REPLICA IDENTITY FULL;

-- Indexes cho temp_themes
create index idx_temp_themes_account_id on public.temp_themes(account_id);
create index idx_temp_themes_account_theme_id on public.temp_themes(account_theme_id);
create index idx_temp_themes_expires_at on public.temp_themes(expires_at);

-- Scheduled Job để xóa temp_themes hết hạn
create or replace function public.cleanup_expired_temp_themes()
returns void as $$
begin
  delete from public.temp_themes
  where expires_at < now();
end;
$$ language plpgsql;

-- Bảng marketplace_themes
create table public.marketplace_themes (
  id uuid primary key default extensions.uuid_generate_v4(),
  account_id uuid references public.accounts(id) on delete cascade,
  account_theme_id uuid references public.account_themes(id),
  name text not null,
  price numeric default 0 check (price >= 0),
  description text,
  category text check (category in ('ecommerce', 'restaurant', 'blog', 'portfolio')),
  tags text[],
  status text default 'draft' check (status in ('draft', 'published', 'archived')),
  downloads_count integer default 0 check (downloads_count >= 0),
  rating numeric default 0 check (rating >= 0 and rating <= 5),
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Policies cho marketplace_themes
alter table public.marketplace_themes enable row level security;
create policy "Public can view published marketplace themes" on public.marketplace_themes
  for select to authenticated
  using (status = 'published');
create policy "Account owners can manage their marketplace themes" on public.marketplace_themes
  for all to authenticated
  using (public.has_role_on_account(account_id));

-- Indexes cho marketplace_themes
create index idx_marketplace_themes_account_id on public.marketplace_themes(account_id);
create index idx_marketplace_themes_status on public.marketplace_themes(status);
create index idx_marketplace_themes_tags on public.marketplace_themes using gin(tags);

-- Bảng theme_purchases
create table public.theme_purchases (
  id uuid primary key default extensions.uuid_generate_v4(),
  buyer_account_id uuid not null references public.accounts(id),
  marketplace_theme_id uuid not null references public.marketplace_themes(id),
  price_paid numeric not null check (price_paid >= 0),
  status text default 'completed' check (status in ('pending', 'completed', 'refunded')),
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Policies cho theme_purchases
alter table public.theme_purchases enable row level security;
create policy "Buyers can view their purchases" on public.theme_purchases
  for select to authenticated
  using (public.has_role_on_account(buyer_account_id));

-- Indexes cho theme_purchases
create index idx_theme_purchases_buyer_account_id on public.theme_purchases(buyer_account_id);
create index idx_theme_purchases_marketplace_theme_id on public.theme_purchases(marketplace_theme_id);

-- Bảng theme_reviews
create table public.theme_reviews (
  id uuid primary key default extensions.uuid_generate_v4(),
  marketplace_theme_id uuid not null references public.marketplace_themes(id),
  reviewer_account_id uuid not null references public.accounts(id),
  rating integer not null check (rating between 1 and 5),
  comment text,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  unique (marketplace_theme_id, reviewer_account_id)
);

-- Policies cho theme_reviews
alter table public.theme_reviews enable row level security;
create policy "Public can read reviews" on public.theme_reviews
  for select to authenticated
  using (true);
create policy "Buyers can create reviews" on public.theme_reviews
  for insert to authenticated
  with check (
    exists (
      select 1 from public.theme_purchases
      where buyer_account_id = reviewer_account_id
      and marketplace_theme_id = theme_reviews.marketplace_theme_id
    )
  );

-- Indexes cho theme_reviews
create index idx_theme_reviews_marketplace_theme_id on public.theme_reviews(marketplace_theme_id);

-- Trigger để cập nhật rating của marketplace_themes
create or replace function public.update_marketplace_theme_rating()
returns trigger as $$
begin
  update public.marketplace_themes
  set rating = (
    select coalesce(avg(rating)::numeric(2,1), 0)
    from public.theme_reviews
    where marketplace_theme_id = new.marketplace_theme_id
  )
  where id = new.marketplace_theme_id;
  return new;
end;
$$ language plpgsql;

create trigger update_marketplace_theme_rating_trigger
after insert or update or delete on public.theme_reviews
for each row execute function public.update_marketplace_theme_rating();

-- Enum for short link types
create type public.short_link_type as enum ('theme', 'product', 'campaign', 'custom');

-- Bảng short_links cho QR code với type system
create table if not exists public.short_links (
  id uuid primary key default gen_random_uuid(),
  short_id text not null unique,
  account_id uuid not null references public.accounts(id) on delete cascade,
  type public.short_link_type not null default 'theme',
  reference_id uuid not null,
  reference_table text not null,
  url text not null,
  title text,
  description text,
  metadata jsonb default '{}',
  click_count integer default 0,
  last_clicked_at timestamptz,
  expires_at timestamptz,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  constraint valid_reference check (
    (type = 'theme' and reference_table = 'account_themes') or
    (type = 'product' and reference_table = 'products') or
    (type = 'campaign' and reference_table = 'campaigns') or
    (type = 'custom' and reference_table is not null)
  )
);

-- Indexes cho short_links
create index idx_short_links_account_id on public.short_links(account_id);
create index idx_short_links_short_id on public.short_links(short_id);
create index idx_short_links_type on public.short_links(type);
create index idx_short_links_reference on public.short_links(reference_id, reference_table);
create index idx_short_links_expires_at on public.short_links(expires_at);

-- Function to increment click count
create or replace function public.increment_short_link_clicks()
returns trigger as $$
begin
  update public.short_links
  set
    click_count = click_count + 1,
    last_clicked_at = now(),
    updated_at = now()
  where id = new.id;
  return new;
end;
$$ language plpgsql;

-- Trigger for click tracking
create trigger track_short_link_clicks
after update of click_count on public.short_links
for each row execute function public.increment_short_link_clicks();

-- Policies cho short_links
alter table public.short_links enable row level security;

create policy "Anyone can view active short links" on public.short_links
  for select using (
    (expires_at is null or expires_at > now())
  );

create policy "Owner can manage shortlinks" on public.short_links
  for all using (public.has_role_on_account(account_id));

-- ZNS Templates table
create type public.zns_event_type as enum (
  'order_created', 'order_updated', 'promotion', 'theme_activated'
);

-- Cập nhật bảng zns_templates với các trường mới
DROP TABLE IF EXISTS public.zns_templates;
CREATE TABLE public.zns_templates (
  id uuid default extensions.uuid_generate_v4() primary key,
  account_id uuid references public.accounts(id) on delete cascade,
  oa_config_id uuid references public.oa_configurations(id) on delete cascade,
  template_id text not null,
  template_name text,
  event_type text,
  enabled boolean default false,
  content text,
  status text,
  tag text,
  preview_url text,
  metadata jsonb default '{}'::jsonb,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- RLS policies
alter table public.zns_templates enable row level security;

-- Policy cho SELECT - Cho phép đọc nếu user thuộc account
create policy "Users can view ZNS templates in their account" on public.zns_templates
  for select to authenticated
  using (public.has_role_on_account(account_id));

-- Policy cho INSERT/UPDATE/DELETE - Yêu cầu quyền zns.manage
create policy "Users can manage ZNS templates with permission" on public.zns_templates
  for all using (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'zns.manage'::public.app_permissions)
  )
  with check (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'zns.manage'::public.app_permissions)
  );

-- Indexes
create index idx_zns_templates_account on public.zns_templates(account_id);
create index idx_zns_templates_oa_config_id on public.zns_templates(oa_config_id);
-- index for team_account_id removed as account_id already represents the team

-- ZNS Usage tracking table
create type public.zns_status as enum ('pending', 'success', 'failed');

-- Cập nhật bảng zns_usage với các trường mới
DROP TABLE IF EXISTS public.zns_usage;
CREATE TABLE public.zns_usage (
  id uuid primary key default gen_random_uuid(),
  account_id uuid references public.accounts(id) on delete cascade,
  oa_config_id uuid references public.oa_configurations(id) on delete cascade,
  template_id uuid references public.zns_templates(id),
  mapping_id uuid,
  recipient text,
  message_id text,
  oa_type public.oa_type not null,
  event_type text not null,
  status public.zns_status not null,
  error_message text,
  metadata jsonb,
  sent_at timestamptz default now(),
  created_at timestamptz default now()
);

-- Enable RLS
alter table public.zns_usage enable row level security;

-- RLS policies for zns_usage
-- Policy cho SELECT - Cho phép đọc nếu user thuộc account
create policy "Users can view ZNS usage in their account" on public.zns_usage
  for select to authenticated
  using (public.has_role_on_account(account_id));

-- Policy cho INSERT/UPDATE/DELETE - Yêu cầu quyền zns.manage
create policy "Users can manage ZNS usage with permission" on public.zns_usage
  for all using (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'zns.manage'::public.app_permissions)
  )
  with check (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'zns.manage'::public.app_permissions)
  );

-- Indexes
create index idx_zns_usage_account_date on public.zns_usage(account_id, sent_at);
create index idx_zns_usage_oa_type on public.zns_usage(oa_type);
create index idx_zns_usage_template_id on public.zns_usage(template_id);
create index idx_zns_usage_mapping_id on public.zns_usage(mapping_id);

-- ZNS Mappings table
CREATE TABLE public.zns_mappings (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  description text,
  template_id uuid references public.zns_templates(id),
  module text not null,
  event_type text not null,
  parameter_mapping jsonb not null,
  recipient_path text not null default 'customer.phone',
  conditions jsonb,
  enabled boolean default true,
  created_at timestamptz default now(),
  updated_at timestamptz default now(),
  created_by uuid references auth.users(id),
  account_id uuid references public.accounts(id) on delete cascade
);

-- Enable RLS
alter table public.zns_mappings enable row level security;

-- RLS policies for zns_mappings
-- Policy cho SELECT - Cho phép đọc nếu user thuộc account
create policy "Users can view ZNS mappings in their account" on public.zns_mappings
  for select to authenticated
  using (public.has_role_on_account(account_id));

-- Policy cho INSERT/UPDATE/DELETE - Yêu cầu quyền zns.manage
create policy "Users can manage ZNS mappings with permission" on public.zns_mappings
  for all using (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'zns.manage'::public.app_permissions)
  )
  with check (
    public.has_role_on_account(account_id) AND
    public.has_permission(auth.uid(), account_id, 'zns.manage'::public.app_permissions)
  );

-- Indexes
create index idx_zns_mappings_template_id on public.zns_mappings(template_id);
create index idx_zns_mappings_module on public.zns_mappings(module);
create index idx_zns_mappings_event_type on public.zns_mappings(event_type);
create index idx_zns_mappings_account_id on public.zns_mappings(account_id);
