-- Self Check-in System Migration
-- Phase 3: QR Code and NFC Check-in

-- 1. QR Check-in Sessions table
CREATE TABLE IF NOT EXISTS qr_checkin_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  program_id UUID NOT NULL REFERENCES programs(id) ON DELETE CASCADE,
  session_date DATE NOT NULL,
  session_time TIME,
  instructor_id UUID REFERENCES instructors(id),
  qr_code TEXT UNIQUE NOT NULL, -- Base64 encoded QR code
  qr_data JSONB NOT NULL, -- QR code payload data
  expires_at TIMESTAMPTZ NOT NULL,
  max_checkins INTEGER DEFAULT 50,
  current_checkins INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'disabled', 'completed')),
  checkin_window_minutes INTEGER DEFAULT 30, -- How long before/after session time checkin is allowed
  location_required BOOLEAN DEFAULT false,
  allowed_locations JSONB, -- GPS coordinates for location-based checkin
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 2. Self Check-in Records table
CREATE TABLE IF NOT EXISTS self_checkin_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  qr_session_id UUID NOT NULL REFERENCES qr_checkin_sessions(id) ON DELETE CASCADE,
  learner_id UUID NOT NULL REFERENCES learners(id) ON DELETE CASCADE,
  checkin_method VARCHAR(20) DEFAULT 'qr' CHECK (checkin_method IN ('qr', 'nfc', 'manual_code', 'guardian_app')),
  checkin_time TIMESTAMPTZ DEFAULT NOW(),
  checkin_location JSONB, -- GPS coordinates
  device_info JSONB, -- Device information
  guardian_info JSONB, -- Guardian who performed checkin
  verification_status VARCHAR(20) DEFAULT 'pending' CHECK (verification_status IN ('pending', 'verified', 'rejected', 'auto_approved')),
  verification_notes TEXT,
  verified_by UUID REFERENCES auth.users(id),
  verified_at TIMESTAMPTZ,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. NFC Tags table
CREATE TABLE IF NOT EXISTS nfc_tags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
  tag_uid VARCHAR(100) UNIQUE NOT NULL, -- NFC tag unique identifier
  tag_name VARCHAR(255) NOT NULL,
  tag_type VARCHAR(50) DEFAULT 'checkin' CHECK (tag_type IN ('checkin', 'checkout', 'location', 'emergency')),
  location_name VARCHAR(255),
  location_coordinates JSONB,
  is_active BOOLEAN DEFAULT true,
  last_used_at TIMESTAMPTZ,
  usage_count INTEGER DEFAULT 0,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- 4. Guardian Check-in Permissions table
CREATE TABLE IF NOT EXISTS guardian_checkin_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  learner_id UUID NOT NULL REFERENCES learners(id) ON DELETE CASCADE,
  guardian_id UUID NOT NULL REFERENCES guardians(id) ON DELETE CASCADE,
  permission_type VARCHAR(20) DEFAULT 'checkin' CHECK (permission_type IN ('checkin', 'checkout', 'both', 'emergency')),
  is_active BOOLEAN DEFAULT true,
  valid_from DATE DEFAULT CURRENT_DATE,
  valid_until DATE,
  time_restrictions JSONB, -- Allowed time windows
  location_restrictions JSONB, -- Allowed locations
  requires_verification BOOLEAN DEFAULT false,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  UNIQUE(learner_id, guardian_id, permission_type)
);

-- 5. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_qr_checkin_sessions_account_program ON qr_checkin_sessions(account_id, program_id);
CREATE INDEX IF NOT EXISTS idx_qr_checkin_sessions_date ON qr_checkin_sessions(session_date);
CREATE INDEX IF NOT EXISTS idx_qr_checkin_sessions_status ON qr_checkin_sessions(status);
CREATE INDEX IF NOT EXISTS idx_qr_checkin_sessions_expires ON qr_checkin_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_qr_checkin_sessions_qr_code ON qr_checkin_sessions(qr_code);

CREATE INDEX IF NOT EXISTS idx_self_checkin_records_session ON self_checkin_records(qr_session_id);
CREATE INDEX IF NOT EXISTS idx_self_checkin_records_learner ON self_checkin_records(learner_id);
CREATE INDEX IF NOT EXISTS idx_self_checkin_records_time ON self_checkin_records(checkin_time);
CREATE INDEX IF NOT EXISTS idx_self_checkin_records_method ON self_checkin_records(checkin_method);
CREATE INDEX IF NOT EXISTS idx_self_checkin_records_status ON self_checkin_records(verification_status);

CREATE INDEX IF NOT EXISTS idx_nfc_tags_account_id ON nfc_tags(account_id);
CREATE INDEX IF NOT EXISTS idx_nfc_tags_uid ON nfc_tags(tag_uid);
CREATE INDEX IF NOT EXISTS idx_nfc_tags_active ON nfc_tags(is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_guardian_checkin_permissions_learner ON guardian_checkin_permissions(learner_id);
CREATE INDEX IF NOT EXISTS idx_guardian_checkin_permissions_guardian ON guardian_checkin_permissions(guardian_id);
CREATE INDEX IF NOT EXISTS idx_guardian_checkin_permissions_active ON guardian_checkin_permissions(is_active) WHERE is_active = true;

-- 6. RLS Policies
ALTER TABLE qr_checkin_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE self_checkin_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE nfc_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE guardian_checkin_permissions ENABLE ROW LEVEL SECURITY;

-- QR Check-in Sessions policies
CREATE POLICY "Users can view QR sessions in their accounts" ON qr_checkin_sessions
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage QR sessions in their accounts" ON qr_checkin_sessions
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Self Check-in Records policies
CREATE POLICY "Users can view checkin records in their accounts" ON self_checkin_records
  FOR SELECT USING (
    qr_session_id IN (
      SELECT id FROM qr_checkin_sessions 
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can manage checkin records in their accounts" ON self_checkin_records
  FOR ALL USING (
    qr_session_id IN (
      SELECT id FROM qr_checkin_sessions 
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships 
        WHERE user_id = auth.uid() 
        AND account_role IN ('owner', 'member')
      )
    )
  );

-- NFC Tags policies
CREATE POLICY "Users can view NFC tags in their accounts" ON nfc_tags
  FOR SELECT USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage NFC tags in their accounts" ON nfc_tags
  FOR ALL USING (
    account_id IN (
      SELECT account_id FROM accounts_memberships 
      WHERE user_id = auth.uid() 
      AND account_role IN ('owner', 'member')
    )
  );

-- Guardian Check-in Permissions policies
CREATE POLICY "Users can view guardian permissions in their accounts" ON guardian_checkin_permissions
  FOR SELECT USING (
    learner_id IN (
      SELECT id FROM learners 
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can manage guardian permissions in their accounts" ON guardian_checkin_permissions
  FOR ALL USING (
    learner_id IN (
      SELECT id FROM learners 
      WHERE account_id IN (
        SELECT account_id FROM accounts_memberships 
        WHERE user_id = auth.uid() 
        AND account_role IN ('owner', 'member')
      )
    )
  );

-- 7. Functions for QR Check-in System
CREATE OR REPLACE FUNCTION create_qr_checkin_session(
  p_account_id UUID,
  p_program_id UUID,
  p_session_date DATE,
  p_session_time TIME DEFAULT NULL,
  p_instructor_id UUID DEFAULT NULL,
  p_expires_in_minutes INTEGER DEFAULT 60,
  p_max_checkins INTEGER DEFAULT 50,
  p_checkin_window_minutes INTEGER DEFAULT 30
) RETURNS JSON AS $$
DECLARE
  v_session_id UUID;
  v_qr_code TEXT;
  v_qr_data JSONB;
  v_expires_at TIMESTAMPTZ;
  v_enrolled_count INTEGER;
BEGIN
  -- Generate unique QR code
  v_qr_code := encode(gen_random_bytes(32), 'base64');
  
  -- Calculate expiration time
  v_expires_at := NOW() + (p_expires_in_minutes || ' minutes')::INTERVAL;
  
  -- Create QR data payload
  v_qr_data := json_build_object(
    'type', 'attendance_checkin',
    'account_id', p_account_id,
    'program_id', p_program_id,
    'session_date', p_session_date,
    'session_time', p_session_time,
    'created_at', NOW()
  );

  -- Create QR check-in session
  INSERT INTO qr_checkin_sessions (
    account_id,
    program_id,
    session_date,
    session_time,
    instructor_id,
    qr_code,
    qr_data,
    expires_at,
    max_checkins,
    checkin_window_minutes,
    created_by
  ) VALUES (
    p_account_id,
    p_program_id,
    p_session_date,
    p_session_time,
    p_instructor_id,
    v_qr_code,
    v_qr_data,
    v_expires_at,
    p_max_checkins,
    p_checkin_window_minutes,
    auth.uid()
  ) RETURNING id INTO v_session_id;

  -- Get enrolled students count
  SELECT COUNT(*) INTO v_enrolled_count
  FROM enrollments
  WHERE account_id = p_account_id
    AND program_id = p_program_id
    AND status = 'active';

  RETURN json_build_object(
    'success', true,
    'session_id', v_session_id,
    'qr_code', v_qr_code,
    'qr_data', v_qr_data,
    'expires_at', v_expires_at,
    'enrolled_count', v_enrolled_count,
    'message', 'QR check-in session created successfully'
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to create QR check-in session'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Function to process QR check-in
CREATE OR REPLACE FUNCTION process_qr_checkin(
  p_qr_code TEXT,
  p_learner_id UUID,
  p_checkin_location JSONB DEFAULT NULL,
  p_device_info JSONB DEFAULT NULL,
  p_guardian_info JSONB DEFAULT NULL
) RETURNS JSON AS $$
DECLARE
  v_session_record RECORD;
  v_checkin_id UUID;
  v_current_time TIMESTAMPTZ := NOW();
  v_session_start TIMESTAMPTZ;
  v_session_end TIMESTAMPTZ;
  v_is_within_window BOOLEAN := false;
BEGIN
  -- Get QR session details
  SELECT * INTO v_session_record
  FROM qr_checkin_sessions
  WHERE qr_code = p_qr_code
    AND status = 'active'
    AND expires_at > v_current_time;

  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'INVALID_QR_CODE',
      'message', 'QR code is invalid or expired'
    );
  END IF;

  -- Check if max checkins reached
  IF v_session_record.current_checkins >= v_session_record.max_checkins THEN
    RETURN json_build_object(
      'success', false,
      'error', 'MAX_CHECKINS_REACHED',
      'message', 'Maximum check-ins reached for this session'
    );
  END IF;

  -- Check if learner is enrolled in the program
  IF NOT EXISTS(
    SELECT 1 FROM enrollments
    WHERE learner_id = p_learner_id
      AND program_id = v_session_record.program_id
      AND account_id = v_session_record.account_id
      AND status = 'active'
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'NOT_ENROLLED',
      'message', 'Learner is not enrolled in this program'
    );
  END IF;

  -- Check if already checked in today
  IF EXISTS(
    SELECT 1 FROM self_checkin_records
    WHERE qr_session_id = v_session_record.id
      AND learner_id = p_learner_id
      AND DATE(checkin_time) = v_session_record.session_date
  ) THEN
    RETURN json_build_object(
      'success', false,
      'error', 'ALREADY_CHECKED_IN',
      'message', 'Learner has already checked in today'
    );
  END IF;

  -- Check time window if session time is specified
  IF v_session_record.session_time IS NOT NULL THEN
    v_session_start := (v_session_record.session_date || ' ' || v_session_record.session_time)::TIMESTAMPTZ 
                      - (v_session_record.checkin_window_minutes || ' minutes')::INTERVAL;
    v_session_end := (v_session_record.session_date || ' ' || v_session_record.session_time)::TIMESTAMPTZ 
                    + (v_session_record.checkin_window_minutes || ' minutes')::INTERVAL;
    
    v_is_within_window := v_current_time BETWEEN v_session_start AND v_session_end;
  ELSE
    v_is_within_window := true; -- No time restriction
  END IF;

  -- Create check-in record
  INSERT INTO self_checkin_records (
    qr_session_id,
    learner_id,
    checkin_method,
    checkin_time,
    checkin_location,
    device_info,
    guardian_info,
    verification_status,
    metadata
  ) VALUES (
    v_session_record.id,
    p_learner_id,
    'qr',
    v_current_time,
    p_checkin_location,
    p_device_info,
    p_guardian_info,
    CASE WHEN v_is_within_window THEN 'auto_approved' ELSE 'pending' END,
    json_build_object(
      'within_time_window', v_is_within_window,
      'session_start', v_session_start,
      'session_end', v_session_end
    )
  ) RETURNING id INTO v_checkin_id;

  -- Update session checkin count
  UPDATE qr_checkin_sessions
  SET current_checkins = current_checkins + 1,
      updated_at = v_current_time
  WHERE id = v_session_record.id;

  -- Create attendance record
  INSERT INTO attendance (
    account_id,
    learner_id,
    program_id,
    session_date,
    session_time,
    status,
    check_in_time,
    attendance_method,
    metadata
  ) VALUES (
    v_session_record.account_id,
    p_learner_id,
    v_session_record.program_id,
    v_session_record.session_date,
    v_session_record.session_time,
    CASE WHEN v_is_within_window THEN 'present' ELSE 'late' END,
    v_current_time,
    'qr',
    json_build_object(
      'qr_session_id', v_session_record.id,
      'checkin_record_id', v_checkin_id,
      'within_time_window', v_is_within_window
    )
  );

  RETURN json_build_object(
    'success', true,
    'checkin_id', v_checkin_id,
    'status', CASE WHEN v_is_within_window THEN 'present' ELSE 'late' END,
    'checkin_time', v_current_time,
    'within_time_window', v_is_within_window,
    'message', 'Check-in successful'
  );
EXCEPTION
  WHEN OTHERS THEN
    RETURN json_build_object(
      'success', false,
      'error', SQLERRM,
      'message', 'Failed to process check-in'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
