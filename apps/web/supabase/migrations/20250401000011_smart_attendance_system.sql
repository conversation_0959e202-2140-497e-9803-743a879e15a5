-- Smart Attendance System Migration
-- Adds enhanced attendance functionality with multiple modes and smart features

-- ============================================================================
-- PART 1: UPDATE ATTENDANCE TABLE SCHEMA
-- ============================================================================

-- Add new columns for smart attendance
ALTER TABLE public.attendance 
ADD COLUMN IF NOT EXISTS attendance_method VARCHAR(20) DEFAULT 'manual' CHECK (attendance_method IN ('manual', 'photo', 'qr_code', 'nfc', 'bulk', 'ai_detection', 'self_checkin')),
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS photo_url TEXT,
ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(3,2),
ADD COLUMN IF NOT EXISTS device_info JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS location_info JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT now();

-- Add unique constraint for attendance records
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'unique_attendance_learner_program_date'
    ) THEN
        ALTER TABLE public.attendance
        ADD CONSTRAINT unique_attendance_learner_program_date
        UNIQUE (learner_id, program_id, session_date);
    END IF;
END $$;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_attendance_method ON public.attendance(attendance_method);
CREATE INDEX IF NOT EXISTS idx_attendance_metadata ON public.attendance USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_attendance_session_date_status ON public.attendance(session_date, status);
CREATE INDEX IF NOT EXISTS idx_attendance_learner_date ON public.attendance(learner_id, session_date);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION public.update_attendance_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_attendance_updated_at_trigger ON public.attendance;
CREATE TRIGGER update_attendance_updated_at_trigger
    BEFORE UPDATE ON public.attendance
    FOR EACH ROW
    EXECUTE FUNCTION public.update_attendance_updated_at();

-- ============================================================================
-- PART 2: ATTENDANCE SESSIONS TABLE
-- ============================================================================

-- Create attendance sessions for batch operations
CREATE TABLE IF NOT EXISTS public.attendance_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    program_id UUID NOT NULL REFERENCES public.programs(id) ON DELETE CASCADE,
    session_date DATE NOT NULL,
    session_time TIME,
    session_type VARCHAR(50) DEFAULT 'regular' CHECK (session_type IN ('regular', 'makeup', 'exam', 'event', 'workshop')),
    instructor_id UUID REFERENCES public.instructors(id),
    total_students INTEGER DEFAULT 0,
    present_count INTEGER DEFAULT 0,
    absent_count INTEGER DEFAULT 0,
    late_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'in_progress', 'completed', 'cancelled')),
    notes TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(account_id, program_id, session_date, session_time)
);

-- Indexes for attendance sessions
CREATE INDEX IF NOT EXISTS idx_attendance_sessions_account_id ON public.attendance_sessions(account_id);
CREATE INDEX IF NOT EXISTS idx_attendance_sessions_program_id ON public.attendance_sessions(program_id);
CREATE INDEX IF NOT EXISTS idx_attendance_sessions_date ON public.attendance_sessions(session_date);
CREATE INDEX IF NOT EXISTS idx_attendance_sessions_status ON public.attendance_sessions(status);

-- ============================================================================
-- PART 3: ATTENDANCE TEMPLATES TABLE
-- ============================================================================

-- Create attendance templates for quick setup
CREATE TABLE IF NOT EXISTS public.attendance_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    program_id UUID REFERENCES public.programs(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_type VARCHAR(50) DEFAULT 'class' CHECK (template_type IN ('class', 'event', 'workshop', 'exam')),
    default_method VARCHAR(20) DEFAULT 'manual',
    settings JSONB DEFAULT '{}'::jsonb,
    learner_ids UUID[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Indexes for attendance templates
CREATE INDEX IF NOT EXISTS idx_attendance_templates_account_id ON public.attendance_templates(account_id);
CREATE INDEX IF NOT EXISTS idx_attendance_templates_program_id ON public.attendance_templates(program_id);
CREATE INDEX IF NOT EXISTS idx_attendance_templates_type ON public.attendance_templates(template_type);

-- ============================================================================
-- PART 4: RLS POLICIES FOR NEW TABLES
-- ============================================================================

-- Enable RLS
ALTER TABLE public.attendance_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.attendance_templates ENABLE ROW LEVEL SECURITY;

-- Attendance sessions policies
CREATE POLICY "attendance_sessions_read" ON public.attendance_sessions
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "attendance_sessions_manage" ON public.attendance_sessions
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

-- Attendance templates policies
CREATE POLICY "attendance_templates_read" ON public.attendance_templates
    FOR SELECT TO authenticated USING (public.has_role_on_account(account_id));

CREATE POLICY "attendance_templates_manage" ON public.attendance_templates
    FOR ALL TO authenticated USING (
        public.has_permission(auth.uid(), account_id, 'education.manage'::public.app_permissions) OR
        public.has_permission(auth.uid(), account_id, 'settings.manage'::public.app_permissions)
    );

-- ============================================================================
-- PART 5: SMART ATTENDANCE FUNCTIONS
-- ============================================================================

-- Function to get optimal attendance method for a class
CREATE OR REPLACE FUNCTION public.get_optimal_attendance_method(
    p_program_id UUID,
    p_session_type VARCHAR DEFAULT 'regular'
)
RETURNS VARCHAR
LANGUAGE plpgsql
AS $$
DECLARE
    v_student_count INTEGER;
    v_age_range VARCHAR;
    v_program_type VARCHAR;
    v_optimal_method VARCHAR;
BEGIN
    -- Get program info
    SELECT
        capacity,
        age_group,
        program_type
    INTO
        v_student_count,
        v_age_range,
        v_program_type
    FROM public.programs
    WHERE id = p_program_id;

    -- Determine optimal method based on criteria
    IF v_student_count > 50 THEN
        v_optimal_method := 'bulk';
    ELSIF v_program_type = 'nursery' OR v_age_range LIKE '%18-36%' THEN
        v_optimal_method := 'photo';
    ELSIF p_session_type = 'workshop' OR p_session_type = 'event' THEN
        v_optimal_method := 'self_checkin';
    ELSIF v_student_count > 30 THEN
        v_optimal_method := 'qr_code';
    ELSE
        v_optimal_method := 'manual';
    END IF;

    RETURN v_optimal_method;
END;
$$;

-- Function to create attendance session with enrolled students
CREATE OR REPLACE FUNCTION public.create_attendance_session(
    p_account_id UUID,
    p_program_id UUID,
    p_session_date DATE,
    p_session_time TIME DEFAULT NULL,
    p_session_type VARCHAR DEFAULT 'regular',
    p_instructor_id UUID DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_session_id UUID;
    v_enrolled_students UUID[];
    v_student_count INTEGER;
    v_optimal_method VARCHAR;
    v_result JSONB;
BEGIN
    -- Get enrolled students for the program
    SELECT ARRAY(
        SELECT learner_id 
        FROM public.enrollments 
        WHERE program_id = p_program_id 
        AND status = 'active'
    ) INTO v_enrolled_students;

    v_student_count := array_length(v_enrolled_students, 1);
    
    -- Get optimal attendance method
    SELECT public.get_optimal_attendance_method(p_program_id, p_session_type) INTO v_optimal_method;

    -- Create attendance session
    INSERT INTO public.attendance_sessions (
        account_id,
        program_id,
        session_date,
        session_time,
        session_type,
        instructor_id,
        total_students,
        status,
        metadata,
        created_by
    ) VALUES (
        p_account_id,
        p_program_id,
        p_session_date,
        p_session_time,
        p_session_type,
        p_instructor_id,
        COALESCE(v_student_count, 0),
        'draft',
        jsonb_build_object(
            'optimal_method', v_optimal_method,
            'enrolled_students', v_enrolled_students
        ),
        auth.uid()
    ) RETURNING id INTO v_session_id;

    -- Create individual attendance records for each enrolled student
    IF v_student_count > 0 THEN
        INSERT INTO public.attendance (
            account_id,
            learner_id,
            program_id,
            session_date,
            status,
            attendance_method,
            metadata,
            created_at
        )
        SELECT 
            p_account_id,
            unnest(v_enrolled_students),
            p_program_id,
            p_session_date,
            'absent', -- Default to absent, will be updated during attendance taking
            v_optimal_method,
            jsonb_build_object('session_id', v_session_id),
            now()
        ON CONFLICT (learner_id, program_id, session_date) DO NOTHING;
    END IF;

    v_result := jsonb_build_object(
        'success', true,
        'session_id', v_session_id,
        'student_count', v_student_count,
        'optimal_method', v_optimal_method,
        'enrolled_students', v_enrolled_students
    );

    RETURN v_result;
END;
$$;

-- Function to bulk update attendance
CREATE OR REPLACE FUNCTION public.bulk_update_attendance(
    p_session_id UUID,
    p_attendance_data JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_session_record RECORD;
    v_attendance_item JSONB;
    v_updated_count INTEGER := 0;
    v_present_count INTEGER := 0;
    v_absent_count INTEGER := 0;
    v_late_count INTEGER := 0;
BEGIN
    -- Get session info
    SELECT * INTO v_session_record 
    FROM public.attendance_sessions 
    WHERE id = p_session_id;

    -- Update individual attendance records
    FOR v_attendance_item IN SELECT * FROM jsonb_array_elements(p_attendance_data)
    LOOP
        UPDATE public.attendance 
        SET 
            status = (v_attendance_item->>'status')::VARCHAR,
            check_in_time = CASE 
                WHEN v_attendance_item->>'check_in_time' IS NOT NULL 
                THEN (v_attendance_item->>'check_in_time')::TIMESTAMP WITH TIME ZONE 
                ELSE check_in_time 
            END,
            check_out_time = CASE 
                WHEN v_attendance_item->>'check_out_time' IS NOT NULL 
                THEN (v_attendance_item->>'check_out_time')::TIMESTAMP WITH TIME ZONE 
                ELSE check_out_time 
            END,
            notes = COALESCE(v_attendance_item->>'notes', notes),
            metadata = metadata || COALESCE((v_attendance_item->>'metadata')::JSONB, '{}'::JSONB),
            updated_at = now()
        WHERE 
            learner_id = (v_attendance_item->>'learner_id')::UUID
            AND session_date = v_session_record.session_date
            AND program_id = v_session_record.program_id;

        v_updated_count := v_updated_count + 1;
    END LOOP;

    -- Calculate counts
    SELECT 
        COUNT(*) FILTER (WHERE status = 'present'),
        COUNT(*) FILTER (WHERE status = 'absent'),
        COUNT(*) FILTER (WHERE status = 'late')
    INTO v_present_count, v_absent_count, v_late_count
    FROM public.attendance 
    WHERE 
        session_date = v_session_record.session_date
        AND program_id = v_session_record.program_id;

    -- Update session summary
    UPDATE public.attendance_sessions 
    SET 
        present_count = v_present_count,
        absent_count = v_absent_count,
        late_count = v_late_count,
        status = 'completed',
        updated_at = now()
    WHERE id = p_session_id;

    RETURN jsonb_build_object(
        'success', true,
        'updated_count', v_updated_count,
        'present_count', v_present_count,
        'absent_count', v_absent_count,
        'late_count', v_late_count
    );
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_optimal_attendance_method(UUID, VARCHAR) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.create_attendance_session(UUID, UUID, DATE, TIME, VARCHAR, UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.bulk_update_attendance(UUID, JSONB) TO authenticated, service_role;

-- Add comments
COMMENT ON FUNCTION public.get_optimal_attendance_method(UUID, VARCHAR) IS 'Determines the optimal attendance method based on class characteristics';
COMMENT ON FUNCTION public.create_attendance_session(UUID, UUID, DATE, TIME, VARCHAR, UUID) IS 'Creates an attendance session with enrolled students';
COMMENT ON FUNCTION public.bulk_update_attendance(UUID, JSONB) IS 'Bulk updates attendance records for a session';

COMMENT ON TABLE public.attendance_sessions IS 'Attendance sessions for batch operations and tracking';
COMMENT ON TABLE public.attendance_templates IS 'Templates for quick attendance setup';
