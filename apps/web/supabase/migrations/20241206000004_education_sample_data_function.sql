-- Education Sample Data Function
-- Create dedicated function for education sample data creation

-- Function to create education sample data for an account
CREATE OR REPLACE FUNCTION public.create_education_sample_data_for_account(
    p_account_id UUID,
    p_education_template TEXT
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_result JSONB := jsonb_build_object(
        'success', true,
        'account_id', p_account_id,
        'template', p_education_template,
        'created_at', now(),
        'stats', jsonb_build_object()
    );
    v_program_id UUID;
    v_instructor_id UUID;
    v_learner_id UUID;
    v_guardian_id UUID;
    v_stats JSONB := jsonb_build_object(
        'programs', 0,
        'instructors', 0,
        'learners', 0,
        'guardians', 0,
        'enrollments', 0,
        'learner_photos', 0
    );
    v_program_record RECORD;
    v_instructor_record RECORD;
    v_learner_record RECORD;
    v_guardian_record RECORD;
    v_enrollment_record RECORD;
    v_photo_record RECORD;
    v_program_map JSONB := jsonb_build_object();
    v_instructor_map JSONB := jsonb_build_object();
    v_learner_map JSONB := jsonb_build_object();
BEGIN
    -- Validate input
    IF p_account_id IS NULL THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Account ID is required'
        );
    END IF;

    -- Process programs
    FOR v_program_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_education_template::jsonb->'programs', '[]'::jsonb)
        ) AS x(
            name TEXT,
            description TEXT,
            program_type TEXT,
            age_group TEXT,
            capacity INTEGER,
            duration_weeks INTEGER,
            schedule JSONB,
            fees JSONB,
            status TEXT
        )
    LOOP
        INSERT INTO public.programs (
            account_id,
            name,
            description,
            program_type,
            age_group,
            capacity,
            duration_weeks,
            schedule,
            fees,
            status
        ) VALUES (
            p_account_id,
            v_program_record.name,
            v_program_record.description,
            COALESCE(v_program_record.program_type, 'regular'),
            v_program_record.age_group,
            COALESCE(v_program_record.capacity, 20),
            v_program_record.duration_weeks,
            v_program_record.schedule,
            v_program_record.fees,
            COALESCE(v_program_record.status, 'active')
        )
        RETURNING id INTO v_program_id;

        -- Store program ID mapping
        v_program_map := jsonb_set(
            v_program_map,
            ARRAY[v_program_record.name],
            to_jsonb(v_program_id)
        );

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['programs'],
            to_jsonb((v_stats->>'programs')::int + 1)
        );
    END LOOP;

    -- Process instructors
    FOR v_instructor_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_education_template::jsonb->'instructors', '[]'::jsonb)
        ) AS x(
            full_name TEXT,
            employee_code TEXT,
            email TEXT,
            phone TEXT,
            specialization TEXT,
            qualifications JSONB,
            status TEXT
        )
    LOOP
        INSERT INTO public.instructors (
            account_id,
            full_name,
            employee_code,
            email,
            phone,
            specialization,
            qualifications,
            status
        ) VALUES (
            p_account_id,
            v_instructor_record.full_name,
            v_instructor_record.employee_code,
            v_instructor_record.email,
            v_instructor_record.phone,
            v_instructor_record.specialization,
            v_instructor_record.qualifications,
            COALESCE(v_instructor_record.status, 'active')
        )
        RETURNING id INTO v_instructor_id;

        -- Store instructor ID mapping
        v_instructor_map := jsonb_set(
            v_instructor_map,
            ARRAY[v_instructor_record.full_name],
            to_jsonb(v_instructor_id)
        );

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['instructors'],
            to_jsonb((v_stats->>'instructors')::int + 1)
        );
    END LOOP;

    -- Process learners
    FOR v_learner_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_education_template::jsonb->'learners', '[]'::jsonb)
        ) AS x(
            full_name TEXT,
            nickname TEXT,
            learner_code TEXT,
            date_of_birth DATE,
            gender TEXT,
            address TEXT,
            phone TEXT,
            email TEXT,
            emergency_contact JSONB,
            medical_info JSONB,
            notes TEXT,
            status TEXT
        )
    LOOP
        INSERT INTO public.learners (
            account_id,
            full_name,
            nickname,
            learner_code,
            date_of_birth,
            gender,
            address,
            phone,
            email,
            emergency_contact,
            medical_info,
            notes,
            status
        ) VALUES (
            p_account_id,
            v_learner_record.full_name,
            v_learner_record.nickname,
            v_learner_record.learner_code,
            v_learner_record.date_of_birth,
            v_learner_record.gender,
            v_learner_record.address,
            v_learner_record.phone,
            v_learner_record.email,
            v_learner_record.emergency_contact,
            v_learner_record.medical_info,
            v_learner_record.notes,
            COALESCE(v_learner_record.status, 'active')
        )
        RETURNING id INTO v_learner_id;

        -- Store learner ID mapping
        v_learner_map := jsonb_set(
            v_learner_map,
            ARRAY[v_learner_record.full_name],
            to_jsonb(v_learner_id)
        );

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['learners'],
            to_jsonb((v_stats->>'learners')::int + 1)
        );
    END LOOP;

    -- Process guardians
    FOR v_guardian_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_education_template::jsonb->'guardians', '[]'::jsonb)
        ) AS x(
            full_name TEXT,
            relationship TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            is_primary BOOLEAN,
            status TEXT
        )
    LOOP
        INSERT INTO public.guardians (
            account_id,
            full_name,
            relationship,
            phone,
            email,
            address,
            is_primary,
            status
        ) VALUES (
            p_account_id,
            v_guardian_record.full_name,
            v_guardian_record.relationship,
            v_guardian_record.phone,
            v_guardian_record.email,
            v_guardian_record.address,
            COALESCE(v_guardian_record.is_primary, false),
            COALESCE(v_guardian_record.status, 'active')
        )
        RETURNING id INTO v_guardian_id;

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['guardians'],
            to_jsonb((v_stats->>'guardians')::int + 1)
        );
    END LOOP;

    -- Process enrollments
    FOR v_enrollment_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_education_template::jsonb->'enrollments', '[]'::jsonb)
        ) AS x(
            learner_name TEXT,
            program_name TEXT,
            enrollment_date DATE,
            status TEXT,
            notes TEXT
        )
    LOOP
        -- Get learner and program IDs from mappings
        v_learner_id := (v_learner_map->>v_enrollment_record.learner_name)::UUID;
        v_program_id := (v_program_map->>v_enrollment_record.program_name)::UUID;

        IF v_learner_id IS NOT NULL AND v_program_id IS NOT NULL THEN
            INSERT INTO public.enrollments (
                account_id,
                learner_id,
                program_id,
                enrollment_date,
                status,
                notes
            ) VALUES (
                p_account_id,
                v_learner_id,
                v_program_id,
                COALESCE(v_enrollment_record.enrollment_date, CURRENT_DATE),
                COALESCE(v_enrollment_record.status, 'active'),
                v_enrollment_record.notes
            );

            -- Update stats
            v_stats := jsonb_set(
                v_stats,
                ARRAY['enrollments'],
                to_jsonb((v_stats->>'enrollments')::int + 1)
            );
        END IF;
    END LOOP;

    -- Process learner photos
    FOR v_photo_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_education_template::jsonb->'learner_photos', '[]'::jsonb)
        ) AS x(
            learner_name TEXT,
            photo_url TEXT,
            photo_type TEXT,
            is_primary BOOLEAN,
            quality_score DECIMAL,
            face_encoding JSONB
        )
    LOOP
        -- Get learner ID from mapping
        v_learner_id := (v_learner_map->>v_photo_record.learner_name)::UUID;

        IF v_learner_id IS NOT NULL THEN
            INSERT INTO public.learner_photos (
                account_id,
                learner_id,
                photo_url,
                photo_type,
                is_primary,
                quality_score,
                face_encoding
            ) VALUES (
                p_account_id,
                v_learner_id,
                v_photo_record.photo_url,
                COALESCE(v_photo_record.photo_type, 'profile'),
                COALESCE(v_photo_record.is_primary, false),
                v_photo_record.quality_score,
                v_photo_record.face_encoding
            );

            -- Update stats
            v_stats := jsonb_set(
                v_stats,
                ARRAY['learner_photos'],
                to_jsonb((v_stats->>'learner_photos')::int + 1)
            );
        END IF;
    END LOOP;

    -- Update result with stats
    v_result := jsonb_set(v_result, ARRAY['stats'], v_stats);

    RETURN v_result;
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'account_id', p_account_id,
            'template', p_education_template
        );
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.create_education_sample_data_for_account(UUID, TEXT) TO authenticated, service_role;

-- Add comment
COMMENT ON FUNCTION public.create_education_sample_data_for_account(UUID, TEXT) IS
'Creates education sample data for an account including programs, instructors, learners, guardians, enrollments, and photos.';
