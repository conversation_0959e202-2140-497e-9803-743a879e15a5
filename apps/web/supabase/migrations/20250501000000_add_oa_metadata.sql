-- Add oa_metadata column to oa_configurations table
ALTER TABLE public.oa_configurations ADD COLUMN oa_metadata JSONB DEFAULT '{}'::JSONB;

-- Create function to check if O<PERSON> token is valid
CREATE OR REPLACE FUNCTION public.is_oa_token_valid(oa_config_id UUID) RETURNS BOOLEAN AS $$
DECLARE
  token_valid BOOLEAN;
BEGIN
  SELECT 
    CASE 
      WHEN access_token IS NULL THEN FALSE
      WHEN token_expires_at IS NULL THEN FALSE
      WHEN token_expires_at < NOW() THEN FALSE
      ELSE TRUE
    END INTO token_valid
  FROM public.oa_configurations
  WHERE id = oa_config_id;
  
  RETURN COALESCE(token_valid, FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update ZNS integration status based on OA configuration
CREATE OR REPLACE FUNCTION public.update_zns_integration_status() RETURNS TRIGGER AS $$
DECLARE
  integration_id UUID;
  token_valid BOOLEAN;
BEGIN
  -- Find ZNS integration for this account
  SELECT id INTO integration_id
  FROM public.integrations
  WHERE account_id = NEW.account_id AND type = 'zalo';
  
  -- If integration exists, update its status
  IF integration_id IS NOT NULL THEN
    token_valid := public.is_oa_token_valid(NEW.id);
    
    UPDATE public.integrations
    SET 
      status = CASE WHEN token_valid THEN 'connected' ELSE 'not_connected' END,
      metadata = jsonb_set(
        COALESCE(metadata, '{}'::jsonb),
        '{oa_config_id}',
        to_jsonb(NEW.id)
      )
    WHERE id = integration_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update ZNS integration status when OA configuration changes
CREATE TRIGGER update_zns_integration_status_trigger
AFTER INSERT OR UPDATE OF access_token, token_expires_at ON public.oa_configurations
FOR EACH ROW
EXECUTE FUNCTION public.update_zns_integration_status();

-- Update existing ZNS integrations with OA configuration IDs
DO $$
DECLARE
  account_record RECORD;
  oa_config_id UUID;
  token_valid BOOLEAN;
BEGIN
  FOR account_record IN SELECT id FROM public.accounts LOOP
    -- Find OA configuration for this account
    SELECT id INTO oa_config_id
    FROM public.oa_configurations
    WHERE account_id = account_record.id;
    
    -- If OA configuration exists, update ZNS integration
    IF oa_config_id IS NOT NULL THEN
      token_valid := public.is_oa_token_valid(oa_config_id);
      
      UPDATE public.integrations
      SET 
        status = CASE WHEN token_valid THEN 'connected' ELSE 'not_connected' END,
        metadata = jsonb_set(
          COALESCE(metadata, '{}'::jsonb),
          '{oa_config_id}',
          to_jsonb(oa_config_id)
        )
      WHERE account_id = account_record.id AND type = 'zalo';
    END IF;
  END LOOP;
END;
$$;
