-- Create enum for integration types
create type public.integration_type as enum (
  'stripe',
  'zapier',
  'zalo',
  'shopee',
  'lazada',
  'tiktok',
  'facebook',
  'google',
  'custom'
);

-- Create enum for integration status
create type public.integration_status as enum (
  'not_connected',
  'connected',
  'error',
  'pending'
);

-- Create integrations table
create table public.integrations (
  id uuid default extensions.uuid_generate_v4() primary key,
  account_id uuid references public.accounts(id) on delete cascade,
  type public.integration_type not null,
  name text,
  description text,
  status public.integration_status default 'not_connected',
  enabled boolean default false,

  -- Configuration and credentials (encrypted)
  config jsonb default '{}'::jsonb,
  credentials jsonb default '{}'::jsonb,

  -- OAuth specific fields
  oauth_access_token text,
  oauth_refresh_token text,
  oauth_expires_at timestamptz,

  -- Webhook configuration
  webhook_url text,
  webhook_secret text,

  -- Metadata and timestamps
  metadata jsonb default '{}'::jsonb,
  last_sync_at timestamptz,
  error_message text,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- Create indexes
create index idx_integrations_account_id on public.integrations(account_id);
create index idx_integrations_type on public.integrations(type);
create index idx_integrations_status on public.integrations(status);
create index idx_integrations_enabled on public.integrations(enabled);



-- RLS policies
alter table public.integrations enable row level security;

-- View policy - users can view integrations for accounts they have access to
create policy "Users can view account integrations"
  on public.integrations for select
  using (
    public.has_role_on_account(account_id)
  );

-- Insert policy - users can create integrations for accounts they manage
create policy "Users can create account integrations"
  on public.integrations for insert
  with check (
    public.has_role_on_account(account_id)
  );

-- Update policy - users can update integrations for accounts they manage
create policy "Users can update account integrations"
  on public.integrations for update
  using (
    public.has_role_on_account(account_id)
  );

-- Delete policy - users can delete integrations for accounts they manage
create policy "Users can delete account integrations"
  on public.integrations for delete
  using (
    public.has_role_on_account(account_id)
  );

-- Grant permissions to authenticated users
grant select, insert, update, delete on public.integrations to authenticated;

-- Function to handle integration updates
create or replace function public.handle_integration_update()
returns trigger as $$
begin
  -- Update the updated_at timestamp
  new.updated_at := now();

  -- Clear error message when status changes to connected
  if new.status = 'connected' then
    new.error_message := null;
  end if;

  return new;
end;
$$ language plpgsql security definer;

-- Create trigger for integration updates
create trigger on_integration_update
  before update on public.integrations
  for each row
  execute procedure public.handle_integration_update();

-- Insert default integrations function
create or replace function public.create_default_integrations(p_account_id uuid)
returns void as $$
begin
  -- Insert Zalo integration
  insert into public.integrations (
    account_id,
    type,
    name,
    description,
    config
  ) values (
    p_account_id,
    'zalo',
    'Zalo Official Account',
    'Connect with Zalo Official Account for messaging and mini-apps',
    '{"icon": "/icons/zalo.svg"}'::jsonb
  );

  -- Insert Shopee integration
  insert into public.integrations (
    account_id,
    type,
    name,
    description,
    config
  ) values (
    p_account_id,
    'shopee',
    'Shopee',
    'Integrate with Shopee marketplace',
    '{"icon": "/icons/shopee.svg"}'::jsonb
  );
end;
$$ language plpgsql security definer;

-- Trigger to create default integrations for new accounts
create or replace function public.create_account_default_integrations()
returns trigger as $$
begin
  perform public.create_default_integrations(new.id);
  return new;
end;
$$ language plpgsql security definer;

create trigger on_account_created
  after insert on public.accounts
  for each row
  execute procedure public.create_account_default_integrations();

-- Comments
comment on table public.integrations is 'Stores integration configurations for accounts';
comment on column public.integrations.config is 'Public configuration for the integration';
comment on column public.integrations.credentials is 'Encrypted credentials for the integration';
comment on column public.integrations.metadata is 'Additional metadata for the integration';
