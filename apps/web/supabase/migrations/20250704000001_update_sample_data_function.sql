-- Migration to update the create_sample_data_for_account function to include Flash Sales and Vouchers
set search_path to public;

-- Update the create_sample_data_for_account function
CREATE OR REPLACE FUNCTION public.create_sample_data_for_account(
    p_account_id UUID,
    p_industry_template TEXT
)
RETURNS JSONB
SECURITY DEFINER
SET search_path = public, extensions
AS $$
DECLARE
    v_result JSONB := jsonb_build_object(
        'success', true,
        'account_id', p_account_id,
        'industry', p_industry_template,
        'created_at', now(),
        'stats', jsonb_build_object()
    );
    v_category_id UUID;
    v_product_id UUID;
    v_branch_id UUID;
    v_customer_id UUID;
    v_order_id UUID;
    v_theme_id UUID;
    v_flash_sale_id UUID;
    v_voucher_id UUID;
    v_category_map JSONB := jsonb_build_object();
    v_product_map JSONB := jsonb_build_object();
    v_branch_map JSONB := jsonb_build_object();
    v_stats JSONB := jsonb_build_object(
        'categories', 0,
        'products', 0,
        'branches', 0,
        'branch_products', 0,
        'orders', 0,
        'order_items', 0,
        'themes', 0,
        'flash_sales', 0,
        'flash_sale_products', 0,
        'vouchers', 0
    );
    v_category_record RECORD;
    v_product_record RECORD;
    v_branch_record RECORD;
    v_branch_product_record RECORD;
    v_order_record RECORD;
    v_order_item_record RECORD;
    v_theme_record RECORD;
    v_flash_sale_record RECORD;
    v_flash_sale_product_record RECORD;
    v_voucher_record RECORD;
BEGIN
    -- Validate JSON format
    BEGIN
        PERFORM p_industry_template::jsonb;
    EXCEPTION WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Invalid JSON format: ' || SQLERRM,
            'account_id', p_account_id,
            'industry_template', p_industry_template
        );
    END;

    -- Validate account exists
    IF NOT EXISTS (SELECT 1 FROM public.accounts WHERE id = p_account_id) THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Account not found',
            'account_id', p_account_id
        );
    END IF;

    -- Process categories
    FOR v_category_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'categories', '[]'::jsonb)
        ) AS x(
            name TEXT,
            description TEXT,
            parent_id UUID,
            image_url TEXT
        )
    LOOP
        BEGIN
            -- Only insert if we have a valid category name
            IF v_category_record.name IS NULL OR v_category_record.name = '' THEN
                CONTINUE;
            END IF;

            INSERT INTO public.categories (
                account_id,
                name,
                description,
                image_url,
                parent_id
            ) VALUES (
                p_account_id,
                v_category_record.name,
                v_category_record.description,
                v_category_record.image_url,
                NULL -- We'll update parent_id in a second pass
            )
            RETURNING id INTO v_category_id;

            -- Store category ID mapping for later use
            v_category_map := jsonb_set(
                v_category_map,
                ARRAY[v_category_record.name],
                to_jsonb(v_category_id)
            );
        EXCEPTION WHEN OTHERS THEN
            -- Continue with next category
            CONTINUE;
        END;

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['categories'],
            to_jsonb((v_stats->>'categories')::int + 1)
        );
    END LOOP;

    -- Process products
    FOR v_product_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'products', '[]'::jsonb)
        ) AS x(
            name TEXT,
            category_name TEXT,
            type TEXT,
            price NUMERIC,
            compare_at_price NUMERIC,
            image_url TEXT,
            status TEXT,
            sku TEXT
        )
    LOOP
        -- Get category ID from the mapping
        BEGIN
            -- Try to get the category ID from the mapping
            IF v_product_record.category_name IS NULL THEN
                v_category_id := NULL;
            ELSE
                -- Try to get the category ID from the mapping
                BEGIN
                    v_category_id := (v_category_map->>v_product_record.category_name)::UUID;
                EXCEPTION WHEN OTHERS THEN
                    v_category_id := NULL;
                END;

                -- If not found in the map, try to find it directly from the database
                IF v_category_id IS NULL THEN
                    BEGIN
                        SELECT id INTO v_category_id
                        FROM public.categories
                        WHERE account_id = p_account_id
                        AND name = v_product_record.category_name
                        LIMIT 1;
                    EXCEPTION WHEN OTHERS THEN
                        v_category_id := NULL;
                    END;

                    -- If still not found, use the first category as a fallback
                    IF v_category_id IS NULL THEN
                        SELECT id INTO v_category_id
                        FROM public.categories
                        WHERE account_id = p_account_id
                        LIMIT 1;
                    END IF;
                END IF;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            v_category_id := NULL;
        END;

        BEGIN
            -- Only insert if we have a valid product name
            IF v_product_record.name IS NULL OR v_product_record.name = '' THEN
                CONTINUE;
            ELSE
                -- Create a description with the product name
                DECLARE
                    v_description TEXT;
                BEGIN
                    v_description := 'Sample product: ' || v_product_record.name;
                    IF v_product_record.category_name IS NOT NULL THEN
                        v_description := v_description || ' (Category: ' || v_product_record.category_name || ')';
                    END IF;

                    -- Insert the product
                    INSERT INTO public.products (
                        account_id,
                        category_id,
                        name,
                        description,
                        type,
                        price,
                        compare_at_price,
                        image_url,
                        status,
                        sku
                    ) VALUES (
                        p_account_id,
                        v_category_id,
                        v_product_record.name,
                        v_description,
                        COALESCE(v_product_record.type, 'physical')::product_type,
                        COALESCE(v_product_record.price, 0),
                        v_product_record.compare_at_price,
                        v_product_record.image_url,
                        COALESCE(v_product_record.status, 'active'),
                        v_product_record.sku
                    )
                    RETURNING id INTO v_product_id;
                END;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            -- Continue with next product
            CONTINUE;
        END;

        -- Store product ID mapping for later use
        v_product_map := jsonb_set(
            v_product_map,
            ARRAY[v_product_record.name],
            to_jsonb(v_product_id)
        );

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['products'],
            to_jsonb((v_stats->>'products')::int + 1)
        );
    END LOOP;

    -- Process branches
    FOR v_branch_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'branches', '[]'::jsonb)
        ) AS x(
            name TEXT,
            address TEXT,
            phone TEXT,
            is_active BOOLEAN
        )
    LOOP
        INSERT INTO public.branches (
            account_id,
            name,
            address,
            phone,
            is_active
        ) VALUES (
            p_account_id,
            v_branch_record.name,
            v_branch_record.address,
            v_branch_record.phone,
            v_branch_record.is_active
        )
        RETURNING id INTO v_branch_id;

        -- Store branch ID mapping for later use
        v_branch_map := jsonb_set(
            v_branch_map,
            ARRAY[v_branch_record.name],
            to_jsonb(v_branch_id)
        );

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['branches'],
            to_jsonb((v_stats->>'branches')::int + 1)
        );
    END LOOP;

    -- Process branch products
    FOR v_branch_product_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'branch_products', '[]'::jsonb)
        ) AS x(
            product_name TEXT,
            branch_name TEXT,
            is_active BOOLEAN
        )
    LOOP
        -- Get product and branch IDs from the mappings
        BEGIN
            v_product_id := (v_product_map->>v_branch_product_record.product_name)::UUID;
        EXCEPTION WHEN OTHERS THEN
            v_product_id := NULL;
        END;

        BEGIN
            v_branch_id := (v_branch_map->>v_branch_product_record.branch_name)::UUID;
        EXCEPTION WHEN OTHERS THEN
            v_branch_id := NULL;
        END;

        -- Only insert if both product and branch exist
        IF v_product_id IS NOT NULL AND v_branch_id IS NOT NULL THEN
            INSERT INTO public.branch_products (
                branch_id,
                product_id,
                is_active
            ) VALUES (
                v_branch_id,
                v_product_id,
                v_branch_product_record.is_active
            )
            ON CONFLICT (branch_id, product_id) DO NOTHING;

            -- Update stats
            v_stats := jsonb_set(
                v_stats,
                ARRAY['branch_products'],
                to_jsonb((v_stats->>'branch_products')::int + 1)
            );
        END IF;
    END LOOP;

    -- Process customer orders
    FOR v_order_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'customer_orders', '[]'::jsonb)
        ) AS x(
            customer_id TEXT,
            branch_name TEXT,
            total_amount NUMERIC,
            payment_method TEXT,
            status TEXT,
            items JSONB
        )
    LOOP
        -- Get branch ID from the mapping
        BEGIN
            v_branch_id := (v_branch_map->>v_order_record.branch_name)::UUID;
        EXCEPTION WHEN OTHERS THEN
            v_branch_id := NULL;
        END;

        -- Create a sample customer if needed
        -- For demo purposes, we'll create a new customer for each order
        INSERT INTO auth.users (
            instance_id,
            id,
            aud,
            role,
            email,
            encrypted_password,
            email_confirmed_at,
            raw_app_meta_data,
            raw_user_meta_data,
            created_at,
            updated_at
        ) VALUES (
            '00000000-0000-0000-0000-000000000000',
            gen_random_uuid(),
            'authenticated',
            'authenticated',
            'sample-' || gen_random_uuid() || '@example.com',
            '$2a$10$NaMVRrI7NyfwP.AfAVWt6O/abulGnf9BBqwa6DqdMwXMvOCGpAnVO', -- dummy password hash
            now(),
            jsonb_build_object(
                'provider', 'email',
                'providers', array['email'],
                'role', 'customer'
            ),
            jsonb_build_object(
                'name', 'Sample Customer',
                'account_id', p_account_id
            ),
            now(),
            now()
        )
        RETURNING id INTO v_customer_id;

        -- Add customer to account as member with customer role
        INSERT INTO public.accounts_memberships (
            user_id,
            account_id,
            account_role
        ) VALUES (
            v_customer_id,
            p_account_id,
            'customer'
        );

        -- Create the order
        INSERT INTO public.customer_orders (
            account_id,
            customer_id,
            branch_id,
            total_amount,
            payment_method,
            status
        ) VALUES (
            p_account_id,
            v_customer_id,
            v_branch_id,
            v_order_record.total_amount,
            v_order_record.payment_method,
            v_order_record.status
        )
        RETURNING id INTO v_order_id;

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['orders'],
            to_jsonb((v_stats->>'orders')::int + 1)
        );

        -- Process order items
        FOR v_order_item_record IN
            SELECT * FROM jsonb_to_recordset(v_order_record.items) AS x(
                product_name TEXT,
                quantity INTEGER,
                price NUMERIC
            )
        LOOP
            -- Get product ID from the mapping
            BEGIN
                v_product_id := (v_product_map->>v_order_item_record.product_name)::UUID;
            EXCEPTION WHEN OTHERS THEN
                v_product_id := NULL;
            END;

            -- Only insert if product exists
            IF v_product_id IS NOT NULL THEN
                INSERT INTO public.customer_order_items (
                    order_id,
                    product_id,
                    quantity,
                    price
                ) VALUES (
                    v_order_id,
                    v_product_id,
                    v_order_item_record.quantity,
                    v_order_item_record.price
                );

                -- Update stats
                v_stats := jsonb_set(
                    v_stats,
                    ARRAY['order_items'],
                    to_jsonb((v_stats->>'order_items')::int + 1)
                );
            END IF;
        END LOOP;
    END LOOP;

    -- Process account themes
    FOR v_theme_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'account_themes', '[]'::jsonb)
        ) AS x(
            theme_id UUID,
            theme_name TEXT,
            mini_app_id TEXT,
            primary_color TEXT,
            secondary_color TEXT,
            logo_url TEXT
        )
    LOOP
        -- First, check if we have a themes table
        IF EXISTS (
            SELECT 1
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = 'themes'
        ) THEN
            -- Check if there's a unique constraint on name column
            DECLARE
                has_unique_constraint BOOLEAN;
            BEGIN
                SELECT EXISTS (
                    SELECT 1
                    FROM pg_constraint
                    WHERE conrelid = 'public.themes'::regclass
                    AND contype = 'u'
                    AND conkey @> ARRAY[(
                        SELECT attnum
                        FROM pg_attribute
                        WHERE attrelid = 'public.themes'::regclass
                        AND attname = 'name'
                    )]
                ) INTO has_unique_constraint;

                IF has_unique_constraint THEN
                    -- If there's a unique constraint, use ON CONFLICT
                    INSERT INTO public.themes (
                        name,
                        config
                    ) VALUES (
                        v_theme_record.theme_name,
                        jsonb_build_object(
                            'primaryColor', v_theme_record.primary_color,
                            'secondaryColor', v_theme_record.secondary_color,
                            'logoUrl', v_theme_record.logo_url
                        )
                    )
                    ON CONFLICT (name) DO UPDATE
                    SET config = EXCLUDED.config
                    RETURNING id INTO v_theme_id;
                ELSE
                    -- If there's no unique constraint, use a different approach
                    -- First check if the theme exists
                    SELECT id INTO v_theme_id
                    FROM public.themes
                    WHERE name = v_theme_record.theme_name;

                    IF v_theme_id IS NULL THEN
                        -- If not exists, insert
                        INSERT INTO public.themes (
                            name,
                            config
                        ) VALUES (
                            v_theme_record.theme_name,
                            jsonb_build_object(
                                'primaryColor', v_theme_record.primary_color,
                                'secondaryColor', v_theme_record.secondary_color,
                                'logoUrl', v_theme_record.logo_url
                            )
                        )
                        RETURNING id INTO v_theme_id;
                    ELSE
                        -- If exists, update
                        UPDATE public.themes
                        SET config = jsonb_build_object(
                            'primaryColor', v_theme_record.primary_color,
                            'secondaryColor', v_theme_record.secondary_color,
                            'logoUrl', v_theme_record.logo_url
                        )
                        WHERE id = v_theme_id;
                    END IF;
                END IF;
            EXCEPTION WHEN OTHERS THEN
                -- If any error occurs, log it and continue
                v_theme_id := NULL;
            END;

            -- Get system default OA config
            DECLARE
                v_oa_config_id UUID;
            BEGIN
                -- Get system default OA config
                v_oa_config_id := NULL;

                -- Try to get a system default OA config
                BEGIN
                    -- Check if is_system_default column exists
                    IF EXISTS (
                        SELECT 1
                        FROM information_schema.columns
                        WHERE table_schema = 'public'
                        AND table_name = 'oa_configurations'
                        AND column_name = 'is_system_default'
                    ) THEN
                        -- Use is_system_default if it exists
                        SELECT id INTO v_oa_config_id
                        FROM public.oa_configurations
                        WHERE is_system_default = TRUE
                        ORDER BY created_at DESC
                        LIMIT 1;
                    ELSE
                        -- Fallback: get a shared OA config (likely system default)
                        SELECT id INTO v_oa_config_id
                        FROM public.oa_configurations
                        WHERE oa_type = 'shared'
                        ORDER BY created_at DESC
                        LIMIT 1;
                    END IF;
                EXCEPTION
                    WHEN NO_DATA_FOUND THEN
                        v_oa_config_id := NULL;
                END;

                -- Associate theme with account
                IF EXISTS (
                    SELECT 1
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = 'account_themes'
                ) THEN
                    INSERT INTO public.account_themes (
                        account_id,
                        template_id,
                        name,
                        config,
                        is_active,
                        oa_config_id,
                        mini_app_id
                    ) VALUES (
                        p_account_id,
                        COALESCE(v_theme_record.theme_id, v_theme_id),
                        v_theme_record.theme_name,
                        jsonb_build_object(
                            'primaryColor', v_theme_record.primary_color,
                            'secondaryColor', v_theme_record.secondary_color,
                            'logoUrl', v_theme_record.logo_url,
                            'template', COALESCE(v_theme_record.theme_id, v_theme_id)
                        ),
                        true,
                        v_oa_config_id,
                        v_theme_record.mini_app_id
                    )
                    ON CONFLICT (account_id, name) DO UPDATE
                    SET config = EXCLUDED.config,
                        is_active = true,
                        template_id = EXCLUDED.template_id,
                        mini_app_id = EXCLUDED.mini_app_id;

                    -- Update stats
                    v_stats := jsonb_set(
                        v_stats,
                        ARRAY['themes'],
                        to_jsonb((v_stats->>'themes')::int + 1)
                    );
                END IF;
            END;
        END IF;
    END LOOP;

    -- Process flash sales
    FOR v_flash_sale_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'flash_sales', '[]'::jsonb)
        ) AS x(
            name TEXT,
            description TEXT,
            start_time TIMESTAMP WITH TIME ZONE,
            end_time TIMESTAMP WITH TIME ZONE,
            status TEXT,
            products JSONB
        )
    LOOP
        -- Insert flash sale
        INSERT INTO public.flash_sales (
            account_id,
            name,
            description,
            start_time,
            end_time,
            status
        ) VALUES (
            p_account_id,
            v_flash_sale_record.name,
            v_flash_sale_record.description,
            COALESCE(v_flash_sale_record.start_time, NOW()),
            COALESCE(v_flash_sale_record.end_time, NOW() + INTERVAL '7 days'),
            COALESCE(v_flash_sale_record.status, 'active')
        )
        RETURNING id INTO v_flash_sale_id;

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['flash_sales'],
            to_jsonb((v_stats->>'flash_sales')::int + 1)
        );

        -- Process flash sale products
        FOR v_flash_sale_product_record IN
            SELECT * FROM jsonb_to_recordset(
                COALESCE(v_flash_sale_record.products, '[]'::jsonb)
            ) AS x(
                product_name TEXT,
                discount_percentage NUMERIC,
                quantity_limit INTEGER
            )
        LOOP
            -- Get product ID from the mapping
            BEGIN
                v_product_id := (v_product_map->>v_flash_sale_product_record.product_name)::UUID;
            EXCEPTION WHEN OTHERS THEN
                v_product_id := NULL;
            END;

            -- Only insert if product exists
            IF v_product_id IS NOT NULL THEN
                INSERT INTO public.flash_sale_products (
                    flash_sale_id,
                    product_id,
                    discount_percentage,
                    quantity_limit
                ) VALUES (
                    v_flash_sale_id,
                    v_product_id,
                    COALESCE(v_flash_sale_product_record.discount_percentage, 10),
                    v_flash_sale_product_record.quantity_limit
                );

                -- Update stats
                v_stats := jsonb_set(
                    v_stats,
                    ARRAY['flash_sale_products'],
                    to_jsonb((v_stats->>'flash_sale_products')::int + 1)
                );
            END IF;
        END LOOP;
    END LOOP;

    -- Process vouchers
    FOR v_voucher_record IN
        SELECT * FROM jsonb_to_recordset(
            COALESCE(p_industry_template::jsonb->'vouchers', '[]'::jsonb)
        ) AS x(
            code TEXT,
            name TEXT,
            description TEXT,
            discount_type TEXT,
            discount_value NUMERIC,
            min_order_value NUMERIC,
            max_discount_value NUMERIC,
            max_uses INTEGER,
            start_date TIMESTAMP WITH TIME ZONE,
            end_date TIMESTAMP WITH TIME ZONE,
            status TEXT
        )
    LOOP
        -- Insert voucher
        INSERT INTO public.vouchers (
            account_id,
            code,
            name,
            description,
            discount_type,
            discount_value,
            min_order_value,
            max_discount_value,
            max_uses,
            start_date,
            end_date,
            status
        ) VALUES (
            p_account_id,
            v_voucher_record.code,
            v_voucher_record.name,
            v_voucher_record.description,
            COALESCE(v_voucher_record.discount_type, 'percentage'),
            COALESCE(v_voucher_record.discount_value, 10),
            v_voucher_record.min_order_value,
            v_voucher_record.max_discount_value,
            v_voucher_record.max_uses,
            COALESCE(v_voucher_record.start_date, NOW()),
            COALESCE(v_voucher_record.end_date, NOW() + INTERVAL '30 days'),
            COALESCE(v_voucher_record.status, 'active')
        )
        RETURNING id INTO v_voucher_id;

        -- Update stats
        v_stats := jsonb_set(
            v_stats,
            ARRAY['vouchers'],
            to_jsonb((v_stats->>'vouchers')::int + 1)
        );
    END LOOP;

    -- Update the result with stats
    v_result := jsonb_set(v_result, ARRAY['stats'], v_stats);

    -- Check if any data was created
    DECLARE
        v_product_count INT;
        v_category_count INT;
        v_flash_sale_count INT;
        v_voucher_count INT;
    BEGIN
        -- Check products
        SELECT COUNT(*) INTO v_product_count FROM public.products WHERE account_id = p_account_id;
        
        -- Check categories
        SELECT COUNT(*) INTO v_category_count FROM public.categories WHERE account_id = p_account_id;
        
        -- Check flash sales
        SELECT COUNT(*) INTO v_flash_sale_count FROM public.flash_sales WHERE account_id = p_account_id;
        
        -- Check vouchers
        SELECT COUNT(*) INTO v_voucher_count FROM public.vouchers WHERE account_id = p_account_id;

        -- Add counts to result
        v_result := jsonb_set(v_result, ARRAY['actual_counts'], jsonb_build_object(
            'products', v_product_count,
            'categories', v_category_count,
            'flash_sales', v_flash_sale_count,
            'vouchers', v_voucher_count
        ));
    EXCEPTION WHEN OTHERS THEN
        -- Ignore errors
    END;

    RETURN v_result;
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', SQLERRM,
            'account_id', p_account_id,
            'industry', p_industry_template
        );
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.create_sample_data_for_account(UUID, TEXT) TO authenticated, service_role;
