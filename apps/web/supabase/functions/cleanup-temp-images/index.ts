import { serve } from 'https://deno.land/std@0.131.0/http/server.ts';

import { getSupabaseServerClient } from '@kit/supabase/src/clients/server-client';

serve(async () => {
  try {
    const supabase = getSupabaseServerClient();

    const { data: expiredImages, error } = await supabase
      .from('temp_images')
      .select('temp_path, url')
      .lt('expires_at', new Date().toISOString())
      .eq('is_moved', false)
      .limit(100); // Giới hạn 100 hình ảnh mỗi lần để tránh tải nặng

    if (error) {
      console.error('Error fetching expired images:', error);
      return new Response(
        JSON.stringify({ error: 'Failed to fetch expired images' }),
        { status: 500 },
      );
    }

    for (const { temp_path, url } of expiredImages || []) {
      await supabase.storage.from('products').remove([temp_path]);
      await supabase.from('temp_images').delete().eq('url', url);
      console.log(`Deleted expired image: ${url}`);
    }

    return new Response(
      JSON.stringify({
        message: `Cleanup completed, deleted ${expiredImages.length} images`,
      }),
      { status: 200 },
    );
  } catch (error) {
    console.error('Cleanup error:', error);
    return new Response(JSON.stringify({ error: 'Cleanup failed' }), {
      status: 500,
    });
  }
});
