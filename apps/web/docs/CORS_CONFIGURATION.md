# CORS Configuration Guide

This guide explains how to configure CORS (Cross-Origin Resource Sharing) for the application to support multiple development ports.

## Overview

The application supports flexible CORS configuration for development environments, allowing you to work with multiple localhost ports without manually updating the configuration each time.

## Configuration Methods

### Method 1: Environment Variable (Recommended)

Add custom localhost ports via environment variable:

```bash
# In your .env.local file
CORS_LOCALHOST_PORTS=3001,3002,4000,5000,8080
```

This will allow CORS requests from:
- `http://localhost:3001`
- `http://localhost:3002` 
- `http://localhost:4000`
- `http://localhost:5000`
- `http://localhost:8080`
- Plus all default ports (3000, 3999, 2999, etc.)

### Method 2: Dynamic CORS Middleware

The application includes a dynamic CORS middleware that automatically allows all localhost ports in development mode.

**Features:**
- ✅ Automatic localhost port detection
- ✅ Support for `127.0.0.1` addresses
- ✅ Development-only (disabled in production)
- ✅ Regex pattern matching

**Usage in API routes:**

```typescript
import { createCorsResponse, handleCorsPreflightRequest } from '~/lib/cors-middleware';

// <PERSON>le preflight requests
export async function OPTIONS(request: NextRequest) {
  return handleCorsPreflightRequest(request);
}

// Add CORS headers to responses
export async function POST(request: NextRequest) {
  // ... your API logic
  
  const response = NextResponse.json(result);
  return createCorsResponse(request, response);
}
```

### Method 3: Static Configuration

Manually add ports to the CORS config:

```typescript
// In apps/web/config/cors.config.ts
const generateLocalhostOrigins = () => {
  const commonPorts = [3000, 3001, 3002, 4000, 5000]; // Add your ports here
  return commonPorts.map(port => `http://localhost:${port}`);
};
```

## Default Supported Ports

The following localhost ports are supported by default:
- `3000` - Default Next.js development server
- `3001` - Alternative Next.js port
- `3002` - Alternative Next.js port
- `3999` - Custom development port
- `2999` - Custom development port
- `4000` - Common development port
- `5000` - Common development port
- `8000` - Common development port
- `8080` - Common development port
- `9000` - Common development port

## Production Configuration

In production, only explicitly allowed origins are permitted:
- `https://h5.zdn.vn`
- `https://h5.zadn.vn`
- `https://app.minapp.vn`
- `zbrowser://h5.zdn.vn`
- Your production URL from `appConfig.url`

## Security Notes

⚠️ **Important Security Considerations:**

1. **Development Only**: Dynamic localhost support is only enabled in development mode
2. **Production Safety**: Production environments use strict origin validation
3. **Explicit Origins**: Always prefer explicit origin configuration for production
4. **Regular Review**: Periodically review and update allowed origins

## Troubleshooting

### CORS Error: "Access to fetch blocked"

1. **Check Environment**: Ensure you're in development mode
2. **Verify Port**: Confirm your port is in the allowed list
3. **Check Console**: Look for CORS-related error messages
4. **Test Preflight**: Verify OPTIONS requests are handled correctly

### Custom Port Not Working

1. **Add to Environment**: Set `CORS_LOCALHOST_PORTS` in `.env.local`
2. **Restart Server**: Restart the development server after changing environment variables
3. **Check Syntax**: Ensure comma-separated format without spaces

### Production CORS Issues

1. **Explicit Configuration**: Add your production domain to `corsConfig.allowedOrigins`
2. **HTTPS Only**: Ensure production origins use HTTPS
3. **No Wildcards**: Avoid wildcard origins in production

## Examples

### Frontend Development on Port 3001

```bash
# .env.local
CORS_LOCALHOST_PORTS=3001

# Start your frontend
npm run dev -- --port 3001
```

### Multiple Development Servers

```bash
# .env.local
CORS_LOCALHOST_PORTS=3001,3002,4000,5000

# API server on 3000 (default)
npm run dev

# Frontend on 3001
npm run dev -- --port 3001

# Storybook on 4000
npm run storybook -- --port 4000
```

### Testing with Different Tools

```bash
# Support various development tools
CORS_LOCALHOST_PORTS=3001,4000,5000,8080,9000

# Next.js: 3001
# Storybook: 4000  
# Vite: 5000
# Webpack Dev Server: 8080
# Custom tools: 9000
```

## Best Practices

1. **Use Environment Variables**: Prefer `CORS_LOCALHOST_PORTS` for flexibility
2. **Document Ports**: Document which ports your team uses
3. **Minimal Configuration**: Only add ports you actually use
4. **Regular Cleanup**: Remove unused port configurations
5. **Team Coordination**: Share CORS configuration with your team

## API Routes with CORS

When creating new API routes that need CORS support:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { createCorsResponse, handleCorsPreflightRequest } from '~/lib/cors-middleware';

export async function OPTIONS(request: NextRequest) {
  return handleCorsPreflightRequest(request);
}

export async function POST(request: NextRequest) {
  try {
    // Your API logic here
    const result = { success: true };
    
    const response = NextResponse.json(result);
    return createCorsResponse(request, response);
  } catch (error) {
    const response = NextResponse.json({ error: 'Internal error' }, { status: 500 });
    return createCorsResponse(request, response);
  }
}
```

This ensures consistent CORS handling across all API endpoints.
