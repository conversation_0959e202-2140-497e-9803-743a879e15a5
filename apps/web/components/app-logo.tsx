import Link from 'next/link';

import { cn } from '@kit/ui/utils';

function LogoImage({
  className,
  width = 105,
}: {
  className?: string;
  width?: number;
}) {
  return (
    <svg
      width={width}
      className={cn(`w-[30px] lg:w-[55px]`, className)}
      viewBox="0 0 19 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <mask id="mask0_2121_805" style={{maskType:"luminance"}} maskUnits="userSpaceOnUse" x="0" y="6" width="7" height="6">
        <path d="M0.00610352 6.16357H6.36675V11.6344H0.00610352V6.16357Z" fill="white"/>
      </mask>
      <g mask="url(#mask0_2121_805)">
        <path d="M4.75313 6.16956H1.59062L0.00610352 8.91387L1.59062 11.6524H4.75313L6.33726 8.91387L4.75313 6.16956Z" fill="#007BFF"/>
      </g>
      <mask id="mask1_2121_805" style={{maskType:"luminance"}} maskUnits="userSpaceOnUse" x="3" y="0" width="10" height="12">
        <path d="M3.14136 0.804321H12.6667V11.6343H3.14136V0.804321Z" fill="white"/>
      </mask>
      <g mask="url(#mask1_2121_805)">
        <path d="M11.6669 7.18254L11.6675 7.18234L11.5606 6.99749L11.0844 6.16941H11.0822L8.95227 2.48242L8.95187 2.48301L7.91598 0.692261H4.75327L3.16895 3.43083L4.23512 5.27403H4.23492L6.84505 9.79465L7.91598 11.6523H11.0844L12.6626 8.91372L11.6669 7.18254Z" fill="#FF0000"/>
      </g>
      <mask id="mask2_2121_805" style={{maskType:"luminance"}} maskUnits="userSpaceOnUse" x="9" y="0" width="10" height="12">
        <path d="M9.48804 0.804321H18.9936V11.6343H9.48804V0.804321Z" fill="white"/>
      </mask>
      <g mask="url(#mask2_2121_805)">
        <path d="M17.9174 7.04934L17.9219 7.04677L15.2127 2.34704L15.2065 2.3508L14.2468 0.692261H11.0843L9.5 3.43083L11.0843 6.16941H11.0853L12.6675 8.9056L12.6625 8.91372L14.2468 11.6523H17.4095L18.9937 8.91372L17.9174 7.04934Z" fill="#FFDE59"/>
      </g>
    </svg>
  );
}

export function AppLogo({
  href,
  label,
  className,
}: {
  href?: string | null;
  className?: string;
  label?: string;
}) {
  if (href === null) {
    return <LogoImage className={className} />;
  }

  return (
    <Link aria-label={label ?? 'Home Page'} href={href ?? '/'} prefetch={true}>
      <LogoImage className={className} />
    </Link>
  );
}
