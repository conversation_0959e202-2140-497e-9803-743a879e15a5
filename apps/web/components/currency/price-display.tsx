'use client';

import React from 'react';
import { useCurrency } from '../../hooks/use-currency';
import { CurrencyCode } from '../../config/currency.config';

interface PriceDisplayProps {
  amount: number;
  currencyCode?: CurrencyCode;
  className?: string;
}

/**
 * Component to display prices with proper currency formatting
 */
export function PriceDisplay({
  amount,
  currencyCode,
  className = '',
}: PriceDisplayProps) {
  const { format } = useCurrency(currencyCode);
  
  return (
    <span className={className}>
      {format(amount)}
    </span>
  );
}

/**
 * Component to display prices with proper currency formatting and additional styling
 */
export function FormattedPrice({
  amount,
  currencyCode,
  className = '',
  size = 'md',
}: PriceDisplayProps & {
  size?: 'sm' | 'md' | 'lg' | 'xl';
}) {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg font-semibold',
    xl: 'text-xl font-bold',
  };
  
  return (
    <PriceDisplay
      amount={amount}
      currencyCode={currencyCode}
      className={`${sizeClasses[size]} ${className}`}
    />
  );
}
