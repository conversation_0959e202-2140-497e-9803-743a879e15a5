'use client';

import { useCallback, useEffect } from 'react';

import { usePathname, useSearchParams } from 'next/navigation';

import { analytics } from '@kit/analytics';
import {
  AppEvent,
  AppEventType,
  ConsumerProvidedEventTypes,
  useAppEvents,
} from '@kit/shared/events';
import { isBrowser } from '@kit/shared/utils';

// <PERSON>ịnh nghĩa các sự kiện từ Zalo Mini App
interface ZaloMiniAppEvents extends ConsumerProvidedEventTypes {
  'zalo.pageview': { pagePath: string; pageTitle?: string; accountId: string; themeId: string; visitorId: string; customerId?: string; deviceType: string; };
  'zalo.product_view': { productId: string; accountId: string; themeId: string; visitorId: string; customerId?: string; deviceType: string; };
  'zalo.add_to_cart': { productId: string; quantity: number; accountId: string; themeId: string; visitorId: string; customerId?: string; deviceType: string; };
  'zalo.purchase': { orderId: string; amount: number; products: any[]; accountId: string; themeId: string; visitorId: string; customerId?: string; deviceType: string; };
}

type AnalyticsMapping<
  T extends ConsumerProvidedEventTypes = ZaloMiniAppEvents,
> = {
  [K in AppEventType<T>]?: (event: AppEvent<T, K>) => unknown;
};

/**
 * Hook to subscribe to app events and map them to analytics actions
 * @param mapping
 */
function useAnalyticsMapping<T extends ConsumerProvidedEventTypes>(
  mapping: AnalyticsMapping<T>,
) {
  const appEvents = useAppEvents<T>();

  useEffect(() => {
    const subscriptions = Object.entries(mapping).map(
      ([eventType, handler]) => {
        appEvents.on(eventType as AppEventType<T>, handler);

        return () => appEvents.off(eventType as AppEventType<T>, handler);
      },
    );

    return () => {
      subscriptions.forEach((unsubscribe) => unsubscribe());
    };
  }, [appEvents, mapping]);
}

/**
 * Define a mapping of app events to analytics actions
 * Add new mappings here to track new events in the analytics service from app events
 */
const analyticsMapping: AnalyticsMapping = {
  'user.signedIn': (event) => {
    const { userId, ...traits } = event.payload;

    if (userId) {
      return analytics.identify(userId, traits);
    }
  },
  'user.signedUp': (event) => {
    return analytics.trackEvent(event.type, event.payload);
  },
  'checkout.started': (event) => {
    return analytics.trackEvent(event.type, event.payload);
  },
  'user.updated': (event) => {
    return analytics.trackEvent(event.type, event.payload);
  },
  // Xử lý các sự kiện từ Zalo Mini App
  'zalo.pageview': (event) => {
    const { accountId, themeId, pagePath, ...properties } = event.payload;

    // Thêm provider Zalo Mini App nếu chưa có
    analytics.addProvider('zaloMiniApp', { accountId, themeId });

    return analytics.trackPageView(pagePath, properties);
  },
  'zalo.product_view': (event) => {
    const { accountId, themeId, ...properties } = event.payload;

    // Thêm provider Zalo Mini App nếu chưa có
    analytics.addProvider('zaloMiniApp', { accountId, themeId });

    return analytics.trackEvent('product_view', properties);
  },
  'zalo.add_to_cart': (event) => {
    const { accountId, themeId, ...properties } = event.payload;

    // Thêm provider Zalo Mini App nếu chưa có
    analytics.addProvider('zaloMiniApp', { accountId, themeId });

    return analytics.trackEvent('add_to_cart', properties);
  },
  'zalo.purchase': (event) => {
    const { accountId, themeId, ...properties } = event.payload;

    // Thêm provider Zalo Mini App nếu chưa có
    analytics.addProvider('zaloMiniApp', { accountId, themeId });

    return analytics.trackEvent('purchase', properties);
  },
};

function AnalyticsProviderBrowser(props: React.PropsWithChildren) {
  // Subscribe to app events and map them to analytics actions
  useAnalyticsMapping(analyticsMapping);

  // Report page views to the analytics service
  useReportPageView(useCallback((url) => analytics.trackPageView(url), []));

  // Render children
  return props.children;
}

/**
 * Provider for the analytics service
 */
export function AnalyticsProvider(props: React.PropsWithChildren) {
  if (!isBrowser()) {
    return props.children;
  }

  return <AnalyticsProviderBrowser>{props.children}</AnalyticsProviderBrowser>;
}

/**
 * Hook to report page views to the analytics service
 * @param reportAnalyticsFn
 */
function useReportPageView(reportAnalyticsFn: (url: string) => unknown) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const url = [pathname, searchParams.toString()].filter(Boolean).join('?');

    reportAnalyticsFn(url);
  }, [pathname, reportAnalyticsFn, searchParams]);
}
