# Feature Development Guide for Next-Supabase SaaS Kit

This guide outlines the standard development process for implementing new features in the Next-Supabase SaaS Kit project. Follow these steps to ensure consistent, high-quality feature implementation that aligns with the project's architecture and best practices.

## Table of Contents

1. [Development Process Overview](#development-process-overview)
2. [Understanding the Architecture](#understanding-the-architecture)
3. [Backend Development](#backend-development)
4. [Frontend Development](#frontend-development)
5. [Internationalization (i18n)](#internationalization-i18n)
6. [Testing](#testing)
7. [Feature Implementation Checklist](#feature-implementation-checklist)
8. [Best Practices](#best-practices)

## Development Process Overview

### 1. Requirement Analysis
- Thoroughly understand the feature requirements
- Identify all components that need to be developed
- Determine necessary API endpoints and database schema changes
- Define user flows and edge cases

### 2. Codebase Exploration
- Study similar existing features for implementation patterns
- Understand data structures and data flow
- Identify reusable hooks, components, and utilities
- Review existing tests for similar features

### 3. Backend Development
- Create or update database schema
- Implement API endpoints with proper validation
- Set up Supabase Row Level Security (RLS) policies
- Implement error handling and logging

### 4. Frontend Development
- Create UI components following the design system
- Implement form validation with Zod
- Ensure responsive design
- Add user feedback (loading states, toasts, etc.)

### 5. Internationalization
- Add translation keys for all text content
- Update language files for English and Vietnamese
- Use `<Trans>` component for formatted text

### 6. Testing
- Write E2E tests for all main use cases
- Add data-testid attributes to key elements
- Ensure tests are stable and reliable
- Test edge cases and error scenarios

### 7. Review and Refinement
- Run all tests and fix any issues
- Optimize performance if needed
- Ensure code quality and documentation
- Verify all requirements are met

## Understanding the Architecture

### Project Structure
```
apps/
  ├── web/                    # Main Next.js application
  │   ├── app/                # App router pages
  │   │   ├── home/           # Authenticated routes
  │   │   │   └── [account]/  # Account-specific routes
  │   ├── components/         # Shared components
  │   ├── lib/                # Utilities and helpers
  │   └── i18n/               # Internationalization
  ├── e2e/                    # End-to-end tests
  │   ├── tests/              # Playwright tests
  │   └── test-api/           # API tests
packages/
  ├── supabase/               # Supabase client and types
  ├── ui/                     # UI component library
  └── config/                 # Shared configuration
```

### Key Patterns

#### Data Flow
1. Server Components fetch data directly from Supabase
2. Client Components use React Query for data fetching
3. Forms submit data through server actions
4. Real-time updates use Supabase subscriptions

#### Authentication & Authorization
- Supabase Auth for user authentication
- Row Level Security (RLS) for data access control
- Account-based permissions system

## Backend Development

### Database Schema
- Review the existing schema in `packages/supabase/src/database.types.ts`
- Follow the established patterns for new tables
- Include standard fields: id, created_at, updated_at, account_id

### Example Schema (Categories)
```typescript
categories: {
  Row: {
    account_id: string
    created_at: string | null
    description: string | null
    id: string
    image_url: string | null
    name: string
    parent_id: string | null
    updated_at: string | null
  }
  // Insert, Update, and Relationships definitions...
}
```

### API Implementation
- Create server actions in `app/api/[feature]/actions.ts`
- Implement proper validation with Zod
- Handle errors and return appropriate responses
- Set up RLS policies in Supabase

### Example Server Action
```typescript
'use server'

import { z } from 'zod'
import { createServerActionClient } from '@/lib/supabase/server'
import { revalidatePath } from 'next/cache'

const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  parent_id: z.string().optional().nullable(),
  account_id: z.string()
})

export async function createCategory(formData: FormData) {
  try {
    const data = {
      name: formData.get('name'),
      description: formData.get('description'),
      parent_id: formData.get('parent_id'),
      account_id: formData.get('account_id')
    }
    
    const validated = schema.parse(data)
    
    const supabase = createServerActionClient()
    
    const { data: category, error } = await supabase
      .from('categories')
      .insert(validated)
      .select()
      .single()
      
    if (error) throw error
    
    revalidatePath(`/home/<USER>/categories`)
    return { success: true, data: category }
  } catch (error) {
    console.error('Error creating category:', error)
    return { success: false, error: error.message }
  }
}
```

## Frontend Development

### Page Structure
- Create feature pages in `app/home/<USER>/[feature]/page.tsx`
- Split complex UI into smaller components
- Use server components for data fetching where possible
- Implement client components for interactive elements

### Example Page Component
```tsx
// app/home/<USER>/categories/page.tsx
import { Suspense } from 'react'
import { getCategoriesByAccount } from '@/lib/api/categories'
import { CategoryList } from './components/category-list'
import { CategoryCreateButton } from './components/category-create-button'
import { PageHeader } from '@/components/page-header'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export default async function CategoriesPage({ params }) {
  return (
    <div className="container py-6">
      <PageHeader 
        title="Categories" 
        description="Manage your product categories"
        action={<CategoryCreateButton accountId={params.account} />}
      />
      
      <Suspense fallback={<LoadingSpinner />}>
        <CategoryListWrapper accountId={params.account} />
      </Suspense>
    </div>
  )
}

async function CategoryListWrapper({ accountId }) {
  const categories = await getCategoriesByAccount(accountId)
  return <CategoryList categories={categories} accountId={accountId} />
}
```

### Forms
- Use React Hook Form with Zod for validation
- Implement proper error handling and user feedback
- Create reusable form components when possible

### Example Form Component
```tsx
// components/categories/category-form.tsx
'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { createCategory } from '@/app/api/categories/actions'
import { useToast } from '@/components/ui/use-toast'
import { useTranslation } from '@/i18n/client'

const schema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  parent_id: z.string().optional().nullable()
})

export function CategoryForm({ accountId, onSuccess }) {
  const { t } = useTranslation()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      description: '',
      parent_id: null
    }
  })
  
  async function onSubmit(values) {
    setIsSubmitting(true)
    try {
      const formData = new FormData()
      formData.append('name', values.name)
      formData.append('description', values.description || '')
      formData.append('parent_id', values.parent_id || '')
      formData.append('account_id', accountId)
      
      const result = await createCategory(formData)
      
      if (result.success) {
        toast({
          title: t('categories.create_success'),
          description: t('categories.create_success_message'),
          variant: 'success'
        })
        onSuccess?.()
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      toast({
        title: t('common.error'),
        description: error.message,
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <label htmlFor="name">{t('categories.name')}</label>
        <Input
          id="name"
          data-testid="category-name-input"
          {...form.register('name')}
          error={form.formState.errors.name?.message}
        />
      </div>
      
      <div>
        <label htmlFor="description">{t('categories.description')}</label>
        <Textarea
          id="description"
          data-testid="category-description-input"
          {...form.register('description')}
          error={form.formState.errors.description?.message}
        />
      </div>
      
      <Button 
        type="submit" 
        disabled={isSubmitting}
        data-testid="category-submit-button"
      >
        {isSubmitting ? t('common.submitting') : t('common.submit')}
      </Button>
    </form>
  )
}
```

## Internationalization (i18n)

### Translation Files
- Add new translation keys to `i18n/locales/en.json` and `i18n/locales/vi.json`
- Use nested objects for organization
- Include all user-facing text

### Example Translation Keys
```json
// i18n/locales/en.json
{
  "categories": {
    "title": "Categories",
    "description": "Manage your product categories",
    "create": "Create Category",
    "edit": "Edit Category",
    "delete": "Delete Category",
    "name": "Category Name",
    "description": "Description",
    "parent_category": "Parent Category",
    "no_categories": "No categories found",
    "create_success": "Category created",
    "create_success_message": "The category has been created successfully",
    "update_success": "Category updated",
    "update_success_message": "The category has been updated successfully",
    "delete_success": "Category deleted",
    "delete_success_message": "The category has been deleted successfully",
    "delete_confirm": "Are you sure you want to delete this category?",
    "delete_confirm_message": "This action cannot be undone"
  }
}
```

### Using Translations
```tsx
// In server components
import { getTranslations } from '@/i18n/server'

export default async function Page() {
  const t = await getTranslations()
  return <h1>{t('categories.title')}</h1>
}

// In client components
import { useTranslation } from '@/i18n/client'

export function Component() {
  const { t } = useTranslation()
  return <h1>{t('categories.title')}</h1>
}
```

## Testing

### E2E Tests
- Create tests in `apps/e2e/tests/[feature]/`
- Test all main user flows
- Include edge cases and error scenarios

### Example E2E Test
```typescript
// apps/e2e/tests/categories/categories.spec.ts
import { test, expect } from '@playwright/test'
import { login } from '../helpers/auth'

test.describe('Categories', () => {
  test.beforeEach(async ({ page }) => {
    await login(page)
    await page.goto('/home/<USER>/categories')
  })
  
  test('should display categories page', async ({ page }) => {
    await expect(page.getByRole('heading', { name: 'Categories' })).toBeVisible()
  })
  
  test('should create a new category', async ({ page }) => {
    await page.getByTestId('create-category-button').click()
    await page.getByTestId('category-name-input').fill('Test Category')
    await page.getByTestId('category-description-input').fill('Test Description')
    await page.getByTestId('category-submit-button').click()
    
    await expect(page.getByText('Category created')).toBeVisible()
    await expect(page.getByText('Test Category')).toBeVisible()
  })
  
  test('should edit a category', async ({ page }) => {
    await page.getByText('Test Category').click()
    await page.getByTestId('edit-category-button').click()
    await page.getByTestId('category-name-input').fill('Updated Category')
    await page.getByTestId('category-submit-button').click()
    
    await expect(page.getByText('Category updated')).toBeVisible()
    await expect(page.getByText('Updated Category')).toBeVisible()
  })
  
  test('should delete a category', async ({ page }) => {
    await page.getByText('Updated Category').click()
    await page.getByTestId('delete-category-button').click()
    await page.getByTestId('confirm-delete-button').click()
    
    await expect(page.getByText('Category deleted')).toBeVisible()
    await expect(page.getByText('Updated Category')).not.toBeVisible()
  })
})
```

### API Tests
- Create tests in `apps/e2e/test-api/__tests__/api/[feature]/`
- Test API endpoints directly
- Include validation and error handling tests

### Example API Test
```typescript
// apps/e2e/test-api/__tests__/api/categories/categories.test.ts
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/packages/supabase/src/database.types'

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

describe('Categories API', () => {
  let accountId: string
  let categoryId: string
  
  beforeAll(async () => {
    // Setup test account
    const { data: account } = await supabase
      .from('accounts')
      .select('id')
      .limit(1)
      .single()
      
    accountId = account.id
  })
  
  it('should create a category', async () => {
    const { data, error } = await supabase
      .from('categories')
      .insert({
        name: 'Test API Category',
        description: 'Created from API test',
        account_id: accountId
      })
      .select()
      .single()
      
    expect(error).toBeNull()
    expect(data).toHaveProperty('id')
    expect(data.name).toBe('Test API Category')
    
    categoryId = data.id
  })
  
  it('should get categories by account', async () => {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('account_id', accountId)
      
    expect(error).toBeNull()
    expect(data.length).toBeGreaterThan(0)
    expect(data.some(cat => cat.id === categoryId)).toBe(true)
  })
  
  it('should update a category', async () => {
    const { data, error } = await supabase
      .from('categories')
      .update({ name: 'Updated API Category' })
      .eq('id', categoryId)
      .select()
      .single()
      
    expect(error).toBeNull()
    expect(data.name).toBe('Updated API Category')
  })
  
  it('should delete a category', async () => {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', categoryId)
      
    expect(error).toBeNull()
    
    const { data } = await supabase
      .from('categories')
      .select('*')
      .eq('id', categoryId)
      
    expect(data.length).toBe(0)
  })
})
```

## Feature Implementation Checklist

### Planning
- [ ] Understand all requirements
- [ ] Identify all components to develop
- [ ] Determine API endpoints and schema changes
- [ ] Study similar existing features

### Backend
- [ ] Create/update database schema
- [ ] Implement API endpoints
- [ ] Set up RLS policies
- [ ] Implement error handling

### Frontend
- [ ] Create page component
- [ ] Implement UI components
- [ ] Add form validation
- [ ] Ensure responsive design
- [ ] Add loading states and error handling

### Internationalization
- [ ] Add translation keys (English)
- [ ] Add translation keys (Vietnamese)
- [ ] Use translation functions in components

### Testing
- [ ] Add data-testid attributes
- [ ] Write E2E tests for main flows
- [ ] Write API tests
- [ ] Test edge cases and error scenarios
- [ ] Verify all tests pass

### Final Review
- [ ] All requirements implemented
- [ ] Code is well-organized and maintainable
- [ ] All text is internationalized
- [ ] UI is responsive and accessible
- [ ] All tests pass
- [ ] No console errors

## Best Practices

### Error Handling
- Always use try/catch blocks for async operations
- Log detailed errors for debugging
- Show user-friendly error messages
- Handle network errors and validation errors separately

### Validation
- Use Zod for form and data validation
- Validate data on both client and server
- Provide clear error messages
- Handle special cases (null, undefined, empty strings)

### Performance
- Optimize database queries
- Use React.memo, useMemo, and useCallback when appropriate
- Avoid unnecessary re-renders
- Implement pagination for large data sets

### Accessibility
- Ensure all elements have appropriate labels
- Use ARIA attributes when necessary
- Ensure keyboard navigation works
- Test with screen readers

### Security
- Always validate user input
- Use RLS policies to restrict data access
- Don't expose sensitive information to the client
- Use parameterized queries to prevent SQL injection

### File Uploads (if applicable)
- Validate file size and type
- Optimize images before upload
- Use secure file paths
- Handle upload errors gracefully

By following this guide, you'll ensure that your feature implementations are consistent, high-quality, and align with the project's architecture and best practices.

### Flow
làm xong thì phải chạy chính e2e của mình làm mục đích chạy e2e xem mình code có lỗi không có lỗi thì quay lại fix và cứ như vậy đến khi nào pass mọi test case tức là xong

cách chạy test :  cd apps/e2e && ENABLE_BILLING_TESTS=true npx playwright test tests/products/products.spec.ts:3
8 --headed --workers=1
