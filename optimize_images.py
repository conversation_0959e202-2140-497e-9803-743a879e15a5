#!/usr/bin/env python3
"""
Image Optimization Script

This script optimizes PNG images in a directory by:
1. Converting them to WebP format
2. Optimizing them for mobile use
3. Deleting the original PNG files after successful conversion

Usage:
    python optimize_images.py [directory_path]

If no directory path is provided, it defaults to 'apps/web/public/images/smockup'.
"""

import os
import sys
import shutil
from pathlib import Path
from PIL import Image
import concurrent.futures

# Default directory to process
DEFAULT_DIR = 'apps/web/public/images/mockup'

# Quality setting for WebP (0-100, higher is better quality but larger file)
WEBP_QUALITY = 80

# Maximum width for mobile optimization (in pixels)
MAX_WIDTH = 1200

def optimize_image(image_path):
    """
    Optimize a single image:
    1. Convert to WebP
    2. Resize if needed for mobile
    3. Delete original after successful conversion
    """
    try:
        # Open the image
        img = Image.open(image_path)

        # Get original size
        original_size = os.path.getsize(image_path)

        # Resize if width is greater than MAX_WIDTH
        if img.width > MAX_WIDTH:
            ratio = MAX_WIDTH / img.width
            new_height = int(img.height * ratio)
            img = img.resize((MAX_WIDTH, new_height), Image.LANCZOS)

        # Create WebP filename
        webp_path = str(image_path).replace('.png', '.webp')

        # Save as WebP
        img.save(webp_path, 'WEBP', quality=WEBP_QUALITY)

        # Get new size
        new_size = os.path.getsize(webp_path)

        # Calculate size reduction
        reduction = (1 - (new_size / original_size)) * 100

        print(f"Converted: {image_path}")
        print(f"  Original: {original_size / 1024:.1f} KB")
        print(f"  WebP: {new_size / 1024:.1f} KB")
        print(f"  Reduction: {reduction:.1f}%")

        # Delete original PNG file
        os.remove(image_path)

        return True, image_path
    except Exception as e:
        return False, f"{image_path}: {str(e)}"

def process_directory(directory):
    """Process all PNG files in the given directory and its subdirectories"""
    # Find all PNG files
    png_files = list(Path(directory).glob('**/*.png'))

    if not png_files:
        print(f"No PNG files found in {directory}")
        return

    print(f"Found {len(png_files)} PNG files to process")

    # Process images in parallel
    success_count = 0
    error_count = 0
    errors = []

    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = [executor.submit(optimize_image, str(png_file)) for png_file in png_files]

        for future in concurrent.futures.as_completed(futures):
            success, result = future.result()
            if success:
                success_count += 1
            else:
                error_count += 1
                errors.append(result)

    # Print summary
    print("\nSummary:")
    print(f"  Successfully converted: {success_count}")
    print(f"  Errors: {error_count}")

    if errors:
        print("\nErrors:")
        for error in errors:
            print(f"  {error}")

def main():
    # Get directory from command line or use default
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    else:
        directory = DEFAULT_DIR

    # Check if directory exists
    if not os.path.isdir(directory):
        print(f"Error: Directory '{directory}' not found")
        sys.exit(1)

    print(f"Processing images in: {directory}")
    process_directory(directory)

if __name__ == "__main__":
    main()
