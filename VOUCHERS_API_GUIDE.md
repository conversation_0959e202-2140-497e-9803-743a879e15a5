# Hướng dẫn sử dụng API Vouchers

Tài liệu này mô tả cách sử dụng API Vouchers để quản lý và áp dụng mã giảm giá trong Zalo Mini App.

## Tổng quan

API Vouchers cho phép bạn lấy danh sách voucher đang hoạt động, kiểm tra tính hợp lệ của voucher và áp dụng voucher vào đơn hàng. Các voucher được sử dụng để giảm giá đơn hàng theo nhiều hình thức khác nhau.

## Endpoints

### Lấy danh sách voucher đang hoạt động

```
GET /api/vouchers/active
```

### Kiểm tra tính hợp lệ của voucher

```
POST /api/vouchers/validate
```

## Xác thực

API yêu cầu xác thực bằng JWT token. Token này cần đư<PERSON><PERSON> gửi trong header `Authorization`.

```
Authorization: Bearer <your_jwt_token>
```

## Tham số

### L<PERSON>y danh sách voucher đang hoạt động

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| page | number | Không | Số trang (mặc định: 1) |
| limit | number | Không | Số lượng kết quả trên mỗi trang (mặc định: 10) |

### Kiểm tra tính hợp lệ của voucher

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| code | string | Có | Mã voucher cần kiểm tra |
| items | array | Có | Danh sách các sản phẩm trong đơn hàng |
| subtotal | number | Có | Tổng giá trị của các sản phẩm |

#### Cấu trúc của mỗi item trong mảng items

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| product_id | string | Có | ID của sản phẩm |
| quantity | number | Có | Số lượng sản phẩm |
| price | number | Có | Giá của sản phẩm (sau khi áp dụng flash sale) |
| category_id | string | Không | ID của danh mục sản phẩm |

## Phản hồi

### Thành công - Danh sách voucher đang hoạt động

```json
{
  "success": true,
  "data": {
    "vouchers": [
      {
        "id": "voucher-123",
        "code": "SUMMER10",
        "name": "Giảm giá mùa hè 10%",
        "description": "Giảm 10% cho tất cả sản phẩm, tối đa 50,000 VND",
        "discount_type": "percentage",
        "discount_value": 10,
        "min_order_value": 200000,
        "max_discount": 50000,
        "start_date": "2023-06-01T00:00:00Z",
        "end_date": "2023-06-30T23:59:59Z",
        "usage_limit": 1000,
        "usage_count": 250,
        "created_at": "2023-05-15T10:00:00Z",
        "updated_at": "2023-05-15T10:00:00Z"
      },
      {
        "id": "voucher-456",
        "code": "FREESHIP",
        "name": "Miễn phí vận chuyển",
        "description": "Miễn phí vận chuyển cho đơn hàng từ 500,000 VND",
        "discount_type": "fixed",
        "discount_value": 30000,
        "min_order_value": 500000,
        "max_discount": 30000,
        "start_date": "2023-06-01T00:00:00Z",
        "end_date": "2023-06-15T23:59:59Z",
        "usage_limit": 500,
        "usage_count": 120,
        "created_at": "2023-05-20T14:30:00Z",
        "updated_at": "2023-05-20T14:30:00Z"
      }
    ],
    "pagination": {
      "total": 5,
      "page": 1,
      "limit": 10,
      "total_pages": 1
    }
  }
}
```

### Thành công - Kiểm tra tính hợp lệ của voucher

```json
{
  "success": true,
  "data": {
    "valid": true,
    "voucher": {
      "id": "voucher-123",
      "code": "SUMMER10",
      "name": "Giảm giá mùa hè 10%",
      "description": "Giảm 10% cho tất cả sản phẩm, tối đa 50,000 VND",
      "discount_type": "percentage",
      "discount_value": 10
    },
    "discount_amount": 20000,
    "final_total": 180000,
    "message": "Voucher đã được áp dụng thành công"
  }
}
```

### Lỗi - Voucher không hợp lệ

```json
{
  "success": false,
  "error": "Voucher không hợp lệ",
  "details": "Voucher đã hết hạn sử dụng"
}
```

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| 400 | Yêu cầu không hợp lệ hoặc thiếu tham số bắt buộc |
| 401 | Không được phép truy cập |
| 404 | Không tìm thấy voucher |
| 422 | Voucher không hợp lệ (đã hết hạn, đã hết lượt sử dụng, không đủ điều kiện áp dụng) |
| 500 | Lỗi server |

## Ví dụ

### Lấy danh sách voucher đang hoạt động

```bash
curl -X GET "https://your-domain.com/api/vouchers/active?page=1&limit=10" \
  -H "Authorization: Bearer your_jwt_token"
```

### Kiểm tra tính hợp lệ của voucher

```bash
curl -X POST https://your-domain.com/api/vouchers/validate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "code": "SUMMER10",
    "items": [
      {
        "product_id": "product-123",
        "quantity": 2,
        "price": 100000,
        "category_id": "category-456"
      },
      {
        "product_id": "product-789",
        "quantity": 1,
        "price": 50000,
        "category_id": "category-012"
      }
    ],
    "subtotal": 250000
  }'
```

## Lưu ý

1. Voucher có 2 loại giảm giá:
   - `percentage`: Giảm theo phần trăm giá trị đơn hàng
   - `fixed`: Giảm một số tiền cố định
2. Trường `min_order_value` xác định giá trị đơn hàng tối thiểu để áp dụng voucher.
3. Trường `max_discount` xác định số tiền giảm giá tối đa khi áp dụng voucher loại `percentage`.
4. Trường `usage_limit` xác định số lần sử dụng tối đa của voucher.
5. Trường `usage_count` cho biết số lần voucher đã được sử dụng.
6. Voucher chỉ có thể áp dụng trong khoảng thời gian từ `start_date` đến `end_date`.

## Tích hợp với Zalo Mini App

Để tích hợp API Vouchers vào Zalo Mini App, bạn cần:

1. Lấy JWT token từ quá trình xác thực
2. Hiển thị danh sách voucher đang hoạt động cho người dùng
3. Cho phép người dùng nhập mã voucher và kiểm tra tính hợp lệ
4. Áp dụng voucher vào đơn hàng nếu hợp lệ

### Ví dụ tích hợp (JavaScript)

```javascript
// Hàm lấy danh sách voucher đang hoạt động
async function getActiveVouchers(page = 1, limit = 10) {
  try {
    const response = await fetch(`https://your-domain.com/api/vouchers/active?page=${page}&limit=${limit}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      }
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Lỗi khi lấy danh sách voucher');
    }
    
    return data.data;
  } catch (error) {
    console.error('Error fetching active vouchers:', error);
    return { vouchers: [], pagination: { total: 0, page, limit, total_pages: 0 } };
  }
}

// Hàm kiểm tra tính hợp lệ của voucher
async function validateVoucher(code, items, subtotal) {
  try {
    const response = await fetch('https://your-domain.com/api/vouchers/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      },
      body: JSON.stringify({
        code,
        items,
        subtotal
      })
    });
    
    const data = await response.json();
    
    return data;
  } catch (error) {
    console.error('Error validating voucher:', error);
    return {
      success: false,
      error: 'Lỗi khi kiểm tra voucher',
      details: error.message
    };
  }
}

// Ví dụ sử dụng
// Hiển thị danh sách voucher đang hoạt động
getActiveVouchers()
  .then(result => {
    if (result.vouchers.length === 0) {
      document.querySelector('.voucher-list').innerHTML = '<p>Không có voucher nào đang hoạt động</p>';
      return;
    }
    
    let vouchersHtml = '<div class="vouchers-container">';
    
    result.vouchers.forEach(voucher => {
      const startDate = new Date(voucher.start_date).toLocaleDateString();
      const endDate = new Date(voucher.end_date).toLocaleDateString();
      
      let discountText = '';
      if (voucher.discount_type === 'percentage') {
        discountText = `Giảm ${voucher.discount_value}%, tối đa ${voucher.max_discount.toLocaleString()} VND`;
      } else {
        discountText = `Giảm ${voucher.discount_value.toLocaleString()} VND`;
      }
      
      vouchersHtml += `
        <div class="voucher-card">
          <div class="voucher-header">
            <h3>${voucher.name}</h3>
            <span class="voucher-code">${voucher.code}</span>
          </div>
          <div class="voucher-body">
            <p>${voucher.description}</p>
            <p class="discount-text">${discountText}</p>
            <p>Đơn hàng tối thiểu: ${voucher.min_order_value.toLocaleString()} VND</p>
            <p>Hiệu lực: ${startDate} - ${endDate}</p>
            <p>Còn lại: ${voucher.usage_limit - voucher.usage_count}/${voucher.usage_limit} lượt</p>
          </div>
          <div class="voucher-footer">
            <button onclick="applyVoucher('${voucher.code}')">Áp dụng</button>
          </div>
        </div>
      `;
    });
    
    vouchersHtml += '</div>';
    document.querySelector('.voucher-list').innerHTML = vouchersHtml;
  })
  .catch(error => {
    console.error('Lỗi:', error);
  });

// Hàm áp dụng voucher
function applyVoucher(code) {
  // Lấy thông tin giỏ hàng hiện tại
  const cartItems = JSON.parse(localStorage.getItem('cart_items') || '[]');
  
  if (cartItems.length === 0) {
    alert('Giỏ hàng trống. Vui lòng thêm sản phẩm vào giỏ hàng trước khi áp dụng voucher.');
    return;
  }
  
  // Tính tổng giá trị đơn hàng
  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  
  // Chuẩn bị dữ liệu cho API
  const items = cartItems.map(item => ({
    product_id: item.productId,
    quantity: item.quantity,
    price: item.price,
    category_id: item.categoryId
  }));
  
  // Kiểm tra tính hợp lệ của voucher
  validateVoucher(code, items, subtotal)
    .then(result => {
      if (!result.success) {
        alert(`Không thể áp dụng voucher: ${result.error}`);
        return;
      }
      
      if (!result.data.valid) {
        alert(`Voucher không hợp lệ: ${result.data.message}`);
        return;
      }
      
      // Lưu thông tin voucher vào localStorage
      localStorage.setItem('applied_voucher', JSON.stringify({
        code: code,
        id: result.data.voucher.id,
        discount_amount: result.data.discount_amount
      }));
      
      // Cập nhật giao diện
      document.querySelector('.subtotal-value').textContent = `${subtotal.toLocaleString()} VND`;
      document.querySelector('.discount-value').textContent = `- ${result.data.discount_amount.toLocaleString()} VND`;
      document.querySelector('.total-value').textContent = `${result.data.final_total.toLocaleString()} VND`;
      
      document.querySelector('.applied-voucher').innerHTML = `
        <div class="applied-voucher-info">
          <span class="voucher-name">${result.data.voucher.name}</span>
          <span class="voucher-code">${code}</span>
          <span class="voucher-discount">- ${result.data.discount_amount.toLocaleString()} VND</span>
          <button onclick="removeVoucher()">Xóa</button>
        </div>
      `;
      
      alert('Áp dụng voucher thành công!');
    })
    .catch(error => {
      console.error('Lỗi khi áp dụng voucher:', error);
      alert('Đã xảy ra lỗi khi áp dụng voucher. Vui lòng thử lại sau.');
    });
}

// Hàm xóa voucher đã áp dụng
function removeVoucher() {
  localStorage.removeItem('applied_voucher');
  
  const cartItems = JSON.parse(localStorage.getItem('cart_items') || '[]');
  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  
  document.querySelector('.subtotal-value').textContent = `${subtotal.toLocaleString()} VND`;
  document.querySelector('.discount-value').textContent = '- 0 VND';
  document.querySelector('.total-value').textContent = `${subtotal.toLocaleString()} VND`;
  
  document.querySelector('.applied-voucher').innerHTML = `
    <div class="no-voucher">
      <p>Chưa áp dụng voucher</p>
      <button onclick="showVoucherList()">Chọn voucher</button>
    </div>
  `;
  
  alert('Đã xóa voucher!');
}
```
