# Hướng dẫn sử dụng API Products

Tài liệu này mô tả cách sử dụng API Products để truy vấn thông tin về sản phẩm trong Zalo Mini App.

## Tổng quan

API Products cho phép bạn lấy danh sách sản phẩm, xem chi tiết sản phẩm, tìm kiếm sản phẩm theo từ khóa và lọc sản phẩm theo danh mục. API này cũng tích hợp với các tính năng flash sale để hiển thị giá khuyến mãi nếu có.

## Endpoints

### Lấy danh sách sản phẩm

```
GET /api/products
```

### Lấy chi tiết sản phẩm

```
GET /api/products/:id
```

### Tìm kiếm sản phẩm

```
GET /api/products/search
```

## Xác thực

API yêu cầu xác thực bằng JWT token. Token này cần đượ<PERSON> gử<PERSON> trong header `Authorization`.

```
Authorization: Bearer <your_jwt_token>
```

## Tham số

### Lấy danh sách sản phẩm

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| page | number | Không | Số trang (mặc định: 1) |
| limit | number | Không | Số lượng kết quả trên mỗi trang (mặc định: 10) |
| category_id | string | Không | Lọc theo ID danh mục |
| sort | string | Không | Sắp xếp kết quả (`price_asc`, `price_desc`, `newest`, `popular`) |
| min_price | number | Không | Giá tối thiểu |
| max_price | number | Không | Giá tối đa |

### Tìm kiếm sản phẩm

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| q | string | Có | Từ khóa tìm kiếm |
| page | number | Không | Số trang (mặc định: 1) |
| limit | number | Không | Số lượng kết quả trên mỗi trang (mặc định: 10) |
| category_id | string | Không | Lọc theo ID danh mục |
| sort | string | Không | Sắp xếp kết quả (`price_asc`, `price_desc`, `newest`, `popular`) |
| min_price | number | Không | Giá tối thiểu |
| max_price | number | Không | Giá tối đa |

## Phản hồi

### Thành công - Danh sách sản phẩm

```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "product-123",
        "name": "Áo thun nam",
        "price": 150000,
        "final_price": 120000,
        "compare_at_price": 180000,
        "image_url": "/images/products/product-123.jpg",
        "image_urls": [
          "/images/products/product-123-1.jpg",
          "/images/products/product-123-2.jpg"
        ],
        "category": {
          "id": "category-456",
          "name": "Áo thun"
        },
        "flash_sale": {
          "id": "flash-sale-789",
          "name": "Flash Sale Hè 2023",
          "discount_percentage": 20
        },
        "inventory_by_branch": {
          "branch-123": 10,
          "branch-456": 5
        },
        "attributes": [
          {
            "id": "attr-123",
            "name": "Size M, Màu đỏ"
          },
          {
            "id": "attr-456",
            "name": "Size L, Màu đỏ"
          }
        ]
      },
      {
        "id": "product-456",
        "name": "Quần jean nam",
        "price": 350000,
        "final_price": 350000,
        "compare_at_price": null,
        "image_url": "/images/products/product-456.jpg",
        "image_urls": [
          "/images/products/product-456-1.jpg",
          "/images/products/product-456-2.jpg"
        ],
        "category": {
          "id": "category-789",
          "name": "Quần jean"
        },
        "flash_sale": null,
        "inventory_by_branch": {
          "branch-123": 8,
          "branch-456": 12
        },
        "attributes": [
          {
            "id": "attr-789",
            "name": "Size 30, Màu xanh đậm"
          },
          {
            "id": "attr-012",
            "name": "Size 32, Màu xanh đậm"
          }
        ]
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "limit": 10,
      "total_pages": 5
    }
  }
}
```

### Thành công - Chi tiết sản phẩm

```json
{
  "success": true,
  "data": {
    "product": {
      "id": "product-123",
      "name": "Áo thun nam",
      "description": "Áo thun nam chất liệu cotton 100%, thoáng mát, thấm hút mồ hôi tốt.",
      "price": 150000,
      "final_price": 120000,
      "compare_at_price": 180000,
      "image_url": "/images/products/product-123.jpg",
      "image_urls": [
        "/images/products/product-123-1.jpg",
        "/images/products/product-123-2.jpg",
        "/images/products/product-123-3.jpg"
      ],
      "category": {
        "id": "category-456",
        "name": "Áo thun"
      },
      "flash_sale": {
        "id": "flash-sale-789",
        "name": "Flash Sale Hè 2023",
        "discount_percentage": 20,
        "start_date": "2023-06-01T00:00:00Z",
        "end_date": "2023-06-07T23:59:59Z"
      },
      "inventory_by_branch": {
        "branch-123": {
          "id": "branch-123",
          "name": "Chi nhánh Quận 1",
          "quantity": 10
        },
        "branch-456": {
          "id": "branch-456",
          "name": "Chi nhánh Quận 2",
          "quantity": 5
        }
      },
      "attributes": [
        {
          "id": "attr-123",
          "name": "Size M, Màu đỏ",
          "price_adjustment": 0,
          "inventory": 5
        },
        {
          "id": "attr-456",
          "name": "Size L, Màu đỏ",
          "price_adjustment": 10000,
          "inventory": 3
        },
        {
          "id": "attr-789",
          "name": "Size XL, Màu đỏ",
          "price_adjustment": 20000,
          "inventory": 2
        }
      ],
      "related_products": [
        {
          "id": "product-789",
          "name": "Áo thun nam cổ tròn",
          "price": 140000,
          "final_price": 140000,
          "image_url": "/images/products/product-789.jpg"
        },
        {
          "id": "product-012",
          "name": "Áo thun nam cổ tim",
          "price": 160000,
          "final_price": 128000,
          "image_url": "/images/products/product-012.jpg",
          "flash_sale": {
            "id": "flash-sale-789",
            "discount_percentage": 20
          }
        }
      ],
      "created_at": "2023-05-15T10:00:00Z",
      "updated_at": "2023-05-30T14:30:00Z"
    }
  }
}
```

### Thành công - Tìm kiếm sản phẩm

```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "product-123",
        "name": "Áo thun nam",
        "price": 150000,
        "final_price": 120000,
        "image_url": "/images/products/product-123.jpg",
        "category": {
          "id": "category-456",
          "name": "Áo thun"
        },
        "flash_sale": {
          "id": "flash-sale-789",
          "discount_percentage": 20
        }
      },
      {
        "id": "product-789",
        "name": "Áo thun nam cổ tròn",
        "price": 140000,
        "final_price": 140000,
        "image_url": "/images/products/product-789.jpg",
        "category": {
          "id": "category-456",
          "name": "Áo thun"
        },
        "flash_sale": null
      }
    ],
    "pagination": {
      "total": 15,
      "page": 1,
      "limit": 10,
      "total_pages": 2
    }
  }
}
```

### Lỗi

```json
{
  "success": false,
  "error": "Mô tả lỗi",
  "details": "Chi tiết lỗi (nếu có)"
}
```

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| 400 | Yêu cầu không hợp lệ |
| 401 | Không được phép truy cập |
| 404 | Không tìm thấy sản phẩm |
| 500 | Lỗi server |

## Ví dụ

### Lấy danh sách sản phẩm

```bash
curl -X GET "https://your-domain.com/api/products?page=1&limit=10&category_id=category-456&sort=price_asc" \
  -H "Authorization: Bearer your_jwt_token"
```

### Lấy chi tiết sản phẩm

```bash
curl -X GET "https://your-domain.com/api/products/product-123" \
  -H "Authorization: Bearer your_jwt_token"
```

### Tìm kiếm sản phẩm

```bash
curl -X GET "https://your-domain.com/api/products/search?q=áo%20thun&page=1&limit=10" \
  -H "Authorization: Bearer your_jwt_token"
```

## Lưu ý

1. Giá cuối cùng (final_price) của sản phẩm được tính bằng cách áp dụng tỷ lệ giảm giá từ flash sale (nếu có) vào giá gốc (price).
2. Trường compare_at_price thể hiện giá so sánh (giá gốc trước khi giảm giá), có thể null nếu không có.
3. Trường flash_sale sẽ là null nếu sản phẩm không nằm trong chương trình flash sale nào hoặc flash sale đã kết thúc.
4. Trường inventory_by_branch cho biết số lượng tồn kho của sản phẩm tại mỗi chi nhánh.
5. Trường attributes chứa thông tin về các biến thể của sản phẩm (ví dụ: kích cỡ, màu sắc).

## Tích hợp với Zalo Mini App

Để tích hợp API Products vào Zalo Mini App, bạn cần:

1. Lấy JWT token từ quá trình xác thực
2. Gọi API để lấy danh sách sản phẩm hoặc chi tiết sản phẩm
3. Hiển thị thông tin sản phẩm cho người dùng

### Ví dụ tích hợp (JavaScript)

```javascript
// Hàm lấy danh sách sản phẩm
async function getProducts(page = 1, limit = 10, categoryId = null, sort = null) {
  try {
    let url = `https://your-domain.com/api/products?page=${page}&limit=${limit}`;
    
    if (categoryId) {
      url += `&category_id=${categoryId}`;
    }
    
    if (sort) {
      url += `&sort=${sort}`;
    }
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      }
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Lỗi khi lấy danh sách sản phẩm');
    }
    
    return data.data;
  } catch (error) {
    console.error('Error fetching products:', error);
    return { products: [], pagination: { total: 0, page, limit, total_pages: 0 } };
  }
}

// Hàm lấy chi tiết sản phẩm
async function getProductDetails(productId) {
  try {
    const response = await fetch(`https://your-domain.com/api/products/${productId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      }
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Lỗi khi lấy chi tiết sản phẩm');
    }
    
    return data.data.product;
  } catch (error) {
    console.error(`Error fetching product details for ID ${productId}:`, error);
    return null;
  }
}

// Hàm tìm kiếm sản phẩm
async function searchProducts(query, page = 1, limit = 10) {
  try {
    const url = `https://your-domain.com/api/products/search?q=${encodeURIComponent(query)}&page=${page}&limit=${limit}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      }
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Lỗi khi tìm kiếm sản phẩm');
    }
    
    return data.data;
  } catch (error) {
    console.error(`Error searching products with query "${query}":`, error);
    return { products: [], pagination: { total: 0, page, limit, total_pages: 0 } };
  }
}

// Ví dụ sử dụng
// Hiển thị danh sách sản phẩm
getProducts(1, 10, 'category-456', 'price_asc')
  .then(result => {
    console.log(`Tìm thấy ${result.pagination.total} sản phẩm`);
    
    result.products.forEach(product => {
      let priceDisplay = '';
      
      if (product.final_price < product.price) {
        priceDisplay = `<span class="sale-price">${product.final_price.toLocaleString()} VND</span> <span class="original-price">${product.price.toLocaleString()} VND</span>`;
      } else {
        priceDisplay = `<span class="regular-price">${product.price.toLocaleString()} VND</span>`;
      }
      
      const flashSaleBadge = product.flash_sale ? `<div class="flash-sale-badge">-${product.flash_sale.discount_percentage}%</div>` : '';
      
      const productHtml = `
        <div class="product-card">
          ${flashSaleBadge}
          <img src="${product.image_url}" alt="${product.name}" />
          <h3>${product.name}</h3>
          <div class="price">${priceDisplay}</div>
          <button onclick="viewProductDetails('${product.id}')">Xem chi tiết</button>
        </div>
      `;
      
      document.querySelector('.product-list').innerHTML += productHtml;
    });
  })
  .catch(error => {
    console.error('Lỗi:', error);
  });

// Hiển thị chi tiết sản phẩm
function viewProductDetails(productId) {
  getProductDetails(productId)
    .then(product => {
      if (!product) {
        document.querySelector('.product-detail').innerHTML = '<p>Không tìm thấy sản phẩm</p>';
        return;
      }
      
      let priceDisplay = '';
      
      if (product.final_price < product.price) {
        priceDisplay = `<span class="sale-price">${product.final_price.toLocaleString()} VND</span> <span class="original-price">${product.price.toLocaleString()} VND</span>`;
      } else {
        priceDisplay = `<span class="regular-price">${product.price.toLocaleString()} VND</span>`;
      }
      
      const flashSaleInfo = product.flash_sale ? `
        <div class="flash-sale-info">
          <h4>${product.flash_sale.name}</h4>
          <p>Giảm ${product.flash_sale.discount_percentage}%</p>
          <p>Kết thúc: ${new Date(product.flash_sale.end_date).toLocaleString()}</p>
        </div>
      ` : '';
      
      let attributesHtml = '';
      if (product.attributes && product.attributes.length > 0) {
        attributesHtml = '<div class="attributes"><h4>Tùy chọn:</h4><select>';
        product.attributes.forEach(attr => {
          const priceAdjustment = attr.price_adjustment > 0 ? ` (+${attr.price_adjustment.toLocaleString()} VND)` : '';
          attributesHtml += `<option value="${attr.id}">${attr.name}${priceAdjustment}</option>`;
        });
        attributesHtml += '</select></div>';
      }
      
      const productDetailHtml = `
        <div class="product-detail-container">
          <div class="product-images">
            <img src="${product.image_url}" alt="${product.name}" class="main-image" />
            <div class="image-thumbnails">
              ${product.image_urls.map(url => `<img src="${url}" alt="${product.name}" />`).join('')}
            </div>
          </div>
          <div class="product-info">
            <h2>${product.name}</h2>
            <div class="price">${priceDisplay}</div>
            ${flashSaleInfo}
            <div class="description">${product.description}</div>
            ${attributesHtml}
            <div class="inventory">
              <p>Tồn kho: ${Object.values(product.inventory_by_branch).reduce((total, branch) => total + branch.quantity, 0)} sản phẩm</p>
            </div>
            <button class="add-to-cart-btn">Thêm vào giỏ hàng</button>
          </div>
        </div>
        <div class="related-products">
          <h3>Sản phẩm liên quan</h3>
          <div class="related-products-list">
            ${product.related_products.map(relatedProduct => {
              const relatedPriceDisplay = relatedProduct.final_price < relatedProduct.price ? 
                `<span class="sale-price">${relatedProduct.final_price.toLocaleString()} VND</span> <span class="original-price">${relatedProduct.price.toLocaleString()} VND</span>` : 
                `<span class="regular-price">${relatedProduct.price.toLocaleString()} VND</span>`;
              
              const relatedFlashSaleBadge = relatedProduct.flash_sale ? `<div class="flash-sale-badge">-${relatedProduct.flash_sale.discount_percentage}%</div>` : '';
              
              return `
                <div class="related-product-card">
                  ${relatedFlashSaleBadge}
                  <img src="${relatedProduct.image_url}" alt="${relatedProduct.name}" />
                  <h4>${relatedProduct.name}</h4>
                  <div class="price">${relatedPriceDisplay}</div>
                  <button onclick="viewProductDetails('${relatedProduct.id}')">Xem chi tiết</button>
                </div>
              `;
            }).join('')}
          </div>
        </div>
      `;
      
      document.querySelector('.product-detail').innerHTML = productDetailHtml;
    })
    .catch(error => {
      console.error('Lỗi:', error);
    });
}
```
