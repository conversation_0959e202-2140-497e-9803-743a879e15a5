# Hướng dẫn sử dụng API Lấy Vị Trí từ Zalo

Tài liệu này mô tả cách sử dụng API để lấy thông tin vị trí của người dùng từ Zalo Mini App.

## Tổng quan

API Lấy Vị Trí cho phép bạn truy xuất thông tin vị trí hiện tại của người dùng thông qua Zalo Mini App. Thông tin vị trí bao gồm tọa độ (latitude, longitude), nguồn cung cấp (provider) và thời gian (timestamp).

## Endpoint

```
POST /api/integrations/zalo/location
```

## Xác thực

API yêu cầu xác thực bằng JWT token. Token này cần được gửi trong header `Authorization`.

```
Authorization: Bearer <your_jwt_token>
```

## Tham số

| Tham số | Kiểu dữ liệu | B<PERSON><PERSON> buộ<PERSON> | <PERSON><PERSON> tả |
|---------|--------------|----------|-------|
| code | string | Có | Token nhận được khi gọi API lấy vị trí trên Zalo |
| access_token | string | Có | Token truy cập từ Zalo SDK |
| theme_id | string (UUID) | Không | ID của theme (tùy chọn) |

## Cách lấy các tham số

### access_token

Token truy cập được lấy thông qua API `sdk.getAccessToken()` của Zalo Mini App:

```javascript
const { token } = await zmp.getAccessToken();
```

### code

Token nhận được khi gọi API lấy vị trí trên Zalo Mini App:

```javascript
const { token } = await zmp.getLocation();
```

### secret_key

Khóa bí mật của Zalo App, được lấy từ trang quản lý ứng dụng Zalo:
1. Truy cập https://developers.zalo.me/
2. Vào phần "Quản lý ứng dụng"
3. Chọn ứng dụng tương ứng
4. Tìm mục "App Secret Key"

## Phản hồi

### Thành công

```json
{
  "success": true,
  "data": {
    "location": {
      "latitude": "10.758341",
      "longitude": "106.745863",
      "provider": "gps",
      "timestamp": "*************"
    }
  }
}
```

### Lỗi

```json
{
  "success": false,
  "error": "Mô tả lỗi",
  "details": "Chi tiết lỗi (nếu có)"
}
```

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| 400 | Dữ liệu không hợp lệ hoặc thiếu tham số bắt buộc |
| 401 | Không tìm thấy account_id trong token |
| 404 | Không tìm thấy cấu hình OA |
| 500 | Lỗi server khi lấy vị trí hoặc cập nhật thông tin người dùng |

## Mã lỗi từ Zalo

| Mã lỗi | Thông báo | Ghi chú |
|--------|-----------|---------|
| -201 | User deny request permission! | Người dùng từ chối cấp quyền |
| -202 | User deny request permission! | Người dùng từ chối cấp quyền và không muốn hỏi lại |
| -1401 | User Authentication Required. Please grant User Authentication permission before requesting User Permission | Người dùng chưa cấp thông tin xác thực |
| -2002 | User denied | Người dùng đã từ chối cấp quyền trước đó và không muốn hỏi lại |
| 114 | code is empty | Tham số code rỗng |
| 115 | code is invalid | Tham số code không hợp lệ |
| 116 | secret_key is empty | Tham số secret_key rỗng |
| 117 | secret_key is invalid | Tham số secret_key không hợp lệ |
| 118 | code is invalid | Tham số code không hợp lệ, thuộc về một App khác |
| 119 | code has already been used | Code đã được sử dụng |

## Ví dụ

### Gọi API bằng curl

```bash
curl -X POST https://your-domain.com/api/integrations/zalo/location \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "code": "your_location_token",
    "access_token": "your_access_token",
    "theme_id": "123e4567-e89b-12d3-a456-426614174000"
  }'
```

### Tích hợp với Zalo Mini App (JavaScript)

```javascript
import { createZMPApp } from 'zmp-js';

const zmp = createZMPApp();

async function getUserLocation() {
  try {
    // Lấy access token
    const accessTokenResult = await zmp.getAccessToken();
    if (!accessTokenResult.token) {
      throw new Error('Failed to get access token');
    }

    // Lấy location token
    const locationResult = await zmp.getLocation();
    if (!locationResult.token) {
      throw new Error('Failed to get location token');
    }

    // Gọi API để lấy vị trí
    const response = await fetch('https://your-domain.com/api/integrations/zalo/location', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      },
      body: JSON.stringify({
        code: locationResult.token,
        access_token: accessTokenResult.token,
        theme_id: 'your-theme-id' // Tùy chọn
      })
    });

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'Unknown error');
    }

    return data.data.location;
  } catch (error) {
    console.error('Error getting user location:', error);
    throw error;
  }
}

// Sử dụng hàm
getUserLocation()
  .then(location => {
    console.log('User location:', location);
    // Xử lý vị trí người dùng
    // Ví dụ: hiển thị bản đồ, tính khoảng cách, v.v.
  })
  .catch(error => {
    console.error('Failed to get user location:', error);
    // Xử lý lỗi
  });
```

## Xử lý lỗi và quyền truy cập

### Yêu cầu quyền truy cập vị trí

Trước khi gọi API `zmp.getLocation()`, bạn nên kiểm tra và yêu cầu quyền truy cập vị trí:

```javascript
async function requestLocationPermission() {
  try {
    // Kiểm tra quyền truy cập vị trí
    const permissionStatus = await zmp.checkPermission('location');

    if (permissionStatus !== 'granted') {
      // Yêu cầu quyền truy cập vị trí
      const requestResult = await zmp.requestPermission('location');

      if (requestResult !== 'granted') {
        throw new Error('Location permission denied');
      }
    }

    return true;
  } catch (error) {
    console.error('Error requesting location permission:', error);
    return false;
  }
}

// Sử dụng hàm
async function getLocationWithPermission() {
  const hasPermission = await requestLocationPermission();

  if (hasPermission) {
    return getUserLocation();
  } else {
    throw new Error('Cannot get location without permission');
  }
}
```

### Xử lý lỗi phổ biến

```javascript
function handleLocationError(error) {
  if (error.code === -201 || error.code === -202) {
    // Người dùng từ chối cấp quyền
    return 'Vui lòng cấp quyền truy cập vị trí để sử dụng tính năng này';
  } else if (error.code === -1401) {
    // Người dùng chưa cấp thông tin xác thực
    return 'Vui lòng đăng nhập để sử dụng tính năng này';
  } else if (error.code === -2002) {
    // Người dùng đã từ chối cấp quyền trước đó
    return 'Bạn đã từ chối cấp quyền truy cập vị trí. Vui lòng vào cài đặt để cấp quyền';
  } else {
    // Lỗi khác
    return 'Không thể lấy vị trí của bạn. Vui lòng thử lại sau';
  }
}
```

## Lưu ý

1. API này yêu cầu xác thực JWT token có chứa thông tin account_id trong user_metadata.
2. Thông tin vị trí chỉ được trả về cho client, không được lưu vào user_metadata.
3. API này sử dụng cấu hình OA từ theme_id (nếu được cung cấp) hoặc cấu hình mặc định của tài khoản.
4. Để sử dụng API này, ứng dụng Zalo Mini App của bạn cần được cấp quyền truy cập vị trí.
5. API getLocation() của Zalo Mini App chỉ được hỗ trợ từ phiên bản 2.21.0 trở lên.

## Phiên bản hỗ trợ

API getLocation() của Zalo Mini App được hỗ trợ từ phiên bản 2.21.0 trở lên.
