# Hướng dẫn tích hợp Analytics cho Zalo Mini App

## 1. Tổng quan

Tài liệu này hướng dẫn cách tích hợp hệ thống phân tích (analytics) từ Zalo Mini App vào hệ thống của chúng ta. Dữ liệu phân tích sẽ được hiển thị trong Dashboard Team và có thể được sử dụng để phân tích hành vi người dùng, hiệu suất sản phẩm và doanh thu.

## 2. Luồng nghiệp vụ tích hợp

### 2.1. <PERSON><PERSON> đồ luồng dữ liệu

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Zalo Mini App  │────▶│  API Endpoint   │────▶│  Supabase       │
│  (Mobile)       │     │  /api/analytics │     │  Database       │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         │
                                                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│    Dashboard    │◀────│   App Events    │◀────│   Analytics     │
│    (Team UI)    │     │   System        │     │   Provider      │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

### 2.2. <PERSON><PERSON><PERSON> bước tích hợp

1. **<PERSON><PERSON><PERSON> nhập qua Zalo**:
   - Người dùng đăng nhập vào Zalo Mini App
   - Nhận token xác thực từ Zalo
   - Lưu token để sử dụng cho các request

2. **Triển khai mã trong Zalo Mini App**:
   - Tạo class Analytics để gửi dữ liệu
   - Theo dõi các sự kiện cần thiết
   - Gửi dữ liệu đến API endpoint kèm token

3. **Xử lý dữ liệu**:
   - API endpoint xác thực token và xử lý dữ liệu
   - Dữ liệu được lưu vào Supabase
   - Hệ thống App Events xử lý sự kiện và gửi đến Analytics Provider

## 3. Triển khai mã trong Zalo Mini App

### 3.1. Tạo class Analytics

```javascript
// analytics.js
class ZaloAnalytics {
  constructor(options) {
    this.apiUrl = options.apiUrl;
    this.token = options.token;
    this.accountId = options.accountId;
    this.themeId = options.themeId;
    this.customerId = options.customerId;
    this.visitorId = this.getVisitorId();
    this.deviceType = this.getDeviceType();
    
    // Tự động theo dõi pageview
    this.trackPageView();
  }
  
  getVisitorId() {
    let visitorId = localStorage.getItem('visitor_id');
    if (!visitorId) {
      visitorId = 'v_' + Math.random().toString(36).substring(2, 15);
      localStorage.setItem('visitor_id', visitorId);
    }
    return visitorId;
  }
  
  getDeviceType() {
    const ua = navigator.userAgent;
    if (/tablet|ipad|playbook|silk/i.test(ua)) {
      return 'tablet';
    }
    if (/mobile|iphone|ipod|android|blackberry|opera mini|iemobile/i.test(ua)) {
      return 'mobile';
    }
    return 'desktop';
  }
  
  async sendEvent(eventType, eventData = {}) {
    try {
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`,
        },
        body: JSON.stringify({
          eventType,
          accountId: this.accountId,
          themeId: this.themeId,
          visitorId: this.visitorId,
          customerId: this.customerId,
          deviceType: this.deviceType,
          ...eventData,
        }),
      });
      
      if (!response.ok) {
        console.error(`Error sending event: ${eventType}`, await response.json());
        return false;
      }
      
      return true;
    } catch (error) {
      console.error(`Error sending event: ${eventType}`, error);
      return false;
    }
  }
  
  trackPageView(pagePath, pageTitle) {
    return this.sendEvent('pageview', {
      pagePath: pagePath || window.location.pathname,
      pageTitle: pageTitle || document.title,
      referrer: document.referrer,
    });
  }
  
  trackProductView(productId) {
    return this.sendEvent('product_view', { productId });
  }
  
  trackAddToCart(productId, quantity = 1) {
    return this.sendEvent('add_to_cart', { productId, quantity });
  }
  
  trackPurchase(orderId, amount, products) {
    return this.sendEvent('purchase', { orderId, amount, products });
  }
  
  setCustomerId(customerId) {
    this.customerId = customerId;
  }
}

export default ZaloAnalytics;
```

### 3.2. Sử dụng class Analytics trong Zalo Mini App

```javascript
// index.js hoặc App.js
import ZaloAnalytics from './analytics';

// Khởi tạo analytics sau khi đăng nhập
async function initializeApp() {
  try {
    // Đăng nhập với Zalo và lấy token
    const token = await getZaloToken();
    
    // Khởi tạo analytics
    const analytics = new ZaloAnalytics({
      apiUrl: 'https://your-app.com/api/analytics/zalo',
      token: token,
      accountId: 'account_id', // ID của account trong hệ thống
      themeId: 'theme_id', // ID của theme trong hệ thống
    });
    
    // Lưu analytics vào global để sử dụng ở mọi nơi
    window.analytics = analytics;
    
    // Render ứng dụng
    renderApp();
  } catch (error) {
    console.error('Failed to initialize app:', error);
    // Xử lý lỗi khởi tạo
    renderLoginScreen();
  }
}

// Hàm lấy token Zalo
async function getZaloToken() {
  // Trong môi trường thực, bạn cần triển khai hàm này để lấy token từ Zalo SDK
  // Ở đây chúng ta giả định token đã có sẵn
  return localStorage.getItem('zalo_token');
}

// Khởi tạo ứng dụng
initializeApp();
```

### 3.3. Theo dõi sự kiện trong các trang

```javascript
// ProductPage.js
function ProductPage({ product }) {
  // Theo dõi xem sản phẩm khi trang được tải
  useEffect(() => {
    if (window.analytics) {
      window.analytics.trackProductView(product.id);
    }
  }, [product.id]);
  
  // Xử lý thêm vào giỏ hàng
  function handleAddToCart() {
    // Thêm sản phẩm vào giỏ hàng
    // ...
    
    // Theo dõi thêm vào giỏ hàng
    if (window.analytics) {
      window.analytics.trackAddToCart(product.id, 1);
    }
  }
  
  return (
    <div>
      <h1>{product.name}</h1>
      <button onClick={handleAddToCart}>Thêm vào giỏ hàng</button>
    </div>
  );
}

// CheckoutPage.js
function CheckoutPage({ order }) {
  // Xử lý hoàn tất đơn hàng
  function handleCompleteOrder() {
    // Xử lý đơn hàng
    // ...
    
    // Theo dõi mua hàng
    if (window.analytics) {
      window.analytics.trackPurchase(
        order.id,
        order.totalAmount,
        order.items.map(item => ({
          id: item.productId,
          quantity: item.quantity,
          price: item.price,
        }))
      );
    }
  }
  
  return (
    <div>
      <h1>Thanh toán</h1>
      <button onClick={handleCompleteOrder}>Hoàn tất đơn hàng</button>
    </div>
  );
}
```

## 4. Thông số kỹ thuật API

### 4.1. API Endpoint

```
POST https://your-app.com/api/analytics/zalo
```

### 4.2. Headers

```
Content-Type: application/json
Authorization: Bearer zalo_token
```

### 4.3. Request Body

```json
{
  "eventType": "pageview|product_view|add_to_cart|purchase",
  "accountId": "account_id",
  "themeId": "theme_id",
  "visitorId": "visitor_id",
  "customerId": "customer_id",
  "deviceType": "mobile|tablet|desktop",
  "pagePath": "/product/123",
  "pageTitle": "Tên sản phẩm",
  "referrer": "https://referrer.com",
  "productId": "product_id",
  "quantity": 1,
  "orderId": "order_id",
  "amount": 100000,
  "products": [
    {
      "id": "product_id_1",
      "quantity": 2,
      "price": 50000
    },
    {
      "id": "product_id_2",
      "quantity": 1,
      "price": 50000
    }
  ]
}
```

### 4.4. Response

```json
{
  "success": true
}
```

## 5. Các sự kiện cần theo dõi

### 5.1. Sự kiện cơ bản

| Sự kiện | Mô tả | Dữ liệu |
|---------|-------|---------|
| `pageview` | Xem trang | `pagePath`, `pageTitle`, `referrer` |
| `product_view` | Xem sản phẩm | `productId` |
| `add_to_cart` | Thêm vào giỏ hàng | `productId`, `quantity` |
| `purchase` | Mua hàng | `orderId`, `amount`, `products` |

### 5.2. Sự kiện nâng cao (tùy chọn)

| Sự kiện | Mô tả | Dữ liệu |
|---------|-------|---------|
| `search` | Tìm kiếm | `query`, `results_count` |
| `category_view` | Xem danh mục | `categoryId`, `categoryName` |
| `wishlist_add` | Thêm vào danh sách yêu thích | `productId` |
| `share` | Chia sẻ sản phẩm | `productId`, `channel` |

## 6. Xử lý lỗi và khắc phục sự cố

### 6.1. Lỗi thường gặp

| Lỗi | Nguyên nhân | Cách khắc phục |
|-----|-------------|----------------|
| 401 Unauthorized | Token không hợp lệ hoặc hết hạn | Đăng nhập lại để lấy token mới |
| 400 Bad Request | Thiếu thông tin bắt buộc | Kiểm tra dữ liệu gửi đi |
| Network Error | Lỗi kết nối | Kiểm tra kết nối mạng |

### 6.2. Kiểm tra logs

```javascript
// Bật chế độ debug để xem logs
console.log('Sending event:', eventType, eventData);
```

## 7. Lợi ích của hệ thống Analytics

### 7.1. Cho Zalo Mini App

- **Hiểu rõ hành vi người dùng**: Biết được người dùng xem những trang nào, sản phẩm nào, và mua những gì
- **Tối ưu hóa trải nghiệm người dùng**: Dựa trên dữ liệu để cải thiện giao diện và luồng người dùng
- **Tăng tỷ lệ chuyển đổi**: Phân tích và cải thiện các bước trong quy trình mua hàng

### 7.2. Cho Dashboard Team

- **Theo dõi hiệu suất**: Xem số lượng người dùng, lượt xem trang, đơn hàng, doanh thu
- **Phân tích xu hướng**: Xem xu hướng theo thời gian để đưa ra quyết định kinh doanh
- **So sánh hiệu suất**: So sánh hiệu suất giữa các Mini App khác nhau

## 8. Kết luận

Hệ thống Analytics cho Zalo Mini App cung cấp cách tiếp cận đơn giản và hiệu quả để thu thập dữ liệu về hành vi người dùng, hiệu suất sản phẩm và doanh thu. Bằng cách tích hợp với hệ thống App Events và Analytics Provider, dữ liệu từ Zalo Mini App được tích hợp liền mạch vào hệ thống phân tích hiện có, cung cấp cái nhìn toàn diện về hiệu suất của ứng dụng.
