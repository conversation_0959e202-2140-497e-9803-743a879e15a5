# Education Platform - Quick Start Guide

## 🚀 Quick Setup (5 minutes)

### 1. Environment Setup

```bash
# Clone repository
git clone [repository-url]
cd education-platform

# Install dependencies
pnpm install

# Copy environment file
cp .env.example .env.local
```

### 2. Configure Environment Variables

```env
# Required for basic functionality
DATABASE_URL=postgresql://postgres:password@localhost:5432/education_db
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Required for payments
ZALOPAY_APP_ID=your_zalopay_app_id
ZALOPAY_KEY1=your_zalopay_key1
ZALOPAY_KEY2=your_zalopay_key2

# Site URL
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 3. Database Setup

```bash
# Reset database with all migrations
pnpm run supabase:web:reset

# Generate TypeScript types
pnpm run supabase:web:typegen
```

### 4. Start Development Server

```bash
pnpm run dev
```

Server sẽ chạy tại: http://localhost:3000

## 🏫 Initialize Education Platform

### Create Organization

```bash
curl -X POST http://localhost:3000/api/education/initialize \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "account_id": "your-account-uuid",
    "education_type": "kindergarten",
    "custom_config": {
      "organization_name": "Mầm Non ABC",
      "director_name": "Cô Nguyễn Thị Lan",
      "address": "123 Đường ABC, Quận 1, TP.HCM",
      "phone": "**********",
      "email": "<EMAIL>"
    }
  }'
```

Lệnh này sẽ tạo:
- ✅ Organization với thông tin cơ bản
- ✅ Sample programs (3-4 chương trình học)
- ✅ Sample learners (5-10 học viên)
- ✅ Sample guardians (phụ huynh)
- ✅ Sample enrollments (đăng ký học)
- ✅ Sample fees (học phí)

## 📱 Mini App Integration

### 1. Zalo Authentication

```javascript
// Trong Zalo Mini App
const authenticateUser = async () => {
  // Lấy access token từ Zalo
  const zaloToken = await zalo.getAccessToken();
  
  // Authenticate với backend
  const response = await fetch('/api/auth/zalo', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      access_token: zaloToken,
      theme_id: 'your-organization-theme-id' // Lấy từ URL hoặc config
    })
  });
  
  const { access_token, user, organization_id } = await response.json();
  
  // Lưu token để sử dụng cho các API calls
  localStorage.setItem('auth_token', access_token);
  localStorage.setItem('organization_id', organization_id);
  
  return { user, organization_id };
};
```

### 2. Guardian Features

```javascript
// Lấy danh sách con em
const getChildren = async () => {
  const orgId = localStorage.getItem('organization_id');
  const token = localStorage.getItem('auth_token');
  
  const response = await fetch(`/api/education/guardian/children?organization_id=${orgId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  return response.json();
};

// Xem điểm danh
const getAttendance = async (learnerId, month) => {
  const orgId = localStorage.getItem('organization_id');
  const token = localStorage.getItem('auth_token');
  
  const response = await fetch(
    `/api/education/guardian/attendance?learner_id=${learnerId}&organization_id=${orgId}&month=${month}`,
    { headers: { 'Authorization': `Bearer ${token}` } }
  );
  
  return response.json();
};

// Thanh toán học phí
const payFee = async (feeId) => {
  const orgId = localStorage.getItem('organization_id');
  const token = localStorage.getItem('auth_token');
  
  const response = await fetch('/api/education/payments/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      fee_id: feeId,
      organization_id: orgId,
      return_url: 'https://miniapp.zalo.me/payment-result'
    })
  });
  
  const { data } = await response.json();
  
  // Redirect đến ZaloPay
  window.location.href = data.paymentUrl;
};
```

### 3. Instructor Features

```javascript
// Lấy danh sách lớp dạy
const getClasses = async () => {
  const orgId = localStorage.getItem('organization_id');
  const token = localStorage.getItem('auth_token');
  
  const response = await fetch(`/api/education/instructor/classes?organization_id=${orgId}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  
  return response.json();
};
```

## 💳 ZaloPay Testing

### 1. Sandbox Configuration

```env
# Sử dụng sandbox cho testing
NODE_ENV=development

# ZaloPay sandbox credentials
ZALOPAY_APP_ID=553
ZALOPAY_KEY1=9phuAOYhan4urywHTh0ndEXiV3pKHr5Q
ZALOPAY_KEY2=Iyz2habzyr7AG8SgvoBCbKwKi3UzlLi3
```

### 2. Test Payment Flow

```javascript
// Test payment creation
const testPayment = async () => {
  const payment = await fetch('/api/education/payments/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_TOKEN'
    },
    body: JSON.stringify({
      fee_id: 'test-fee-uuid',
      organization_id: 'test-org-uuid'
    })
  });
  
  const result = await payment.json();
  console.log('Payment URL:', result.data.paymentUrl);
  
  // Trong sandbox, có thể test với test cards
  // Xem ZaloPay documentation cho test card numbers
};
```

## 🔧 Common API Patterns

### Error Handling

```javascript
const apiCall = async (url, options = {}) => {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        ...options.headers
      },
      ...options
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'API call failed');
    }
    
    return data.data;
  } catch (error) {
    console.error('API Error:', error);
    // Show user-friendly error message
    showErrorToast(error.message);
    throw error;
  }
};
```

### Pagination

```javascript
const getDataWithPagination = async (endpoint, page = 1, limit = 20) => {
  const orgId = localStorage.getItem('organization_id');
  const url = `${endpoint}?organization_id=${orgId}&page=${page}&limit=${limit}`;
  
  return apiCall(url);
};
```

### Real-time Updates

```javascript
// Polling for payment status
const pollPaymentStatus = async (paymentId) => {
  const orgId = localStorage.getItem('organization_id');
  
  const checkStatus = async () => {
    const status = await apiCall(
      `/api/education/payments/status?payment_id=${paymentId}&organization_id=${orgId}`
    );
    
    if (status.status === 'succeeded') {
      showSuccessMessage('Thanh toán thành công!');
      return true;
    } else if (status.status === 'failed') {
      showErrorMessage('Thanh toán thất bại!');
      return true;
    }
    
    return false;
  };
  
  // Check every 3 seconds
  const interval = setInterval(async () => {
    const completed = await checkStatus();
    if (completed) {
      clearInterval(interval);
    }
  }, 3000);
  
  // Stop after 5 minutes
  setTimeout(() => clearInterval(interval), 300000);
};
```

## 📊 Testing & Debugging

### API Testing

```bash
# Test all education APIs
node scripts/test-education-api.js

# Test payment APIs
node scripts/test-payment-api.js
```

### Database Inspection

```bash
# Connect to local database
psql postgresql://postgres:password@localhost:5432/education_db

# Check organizations
SELECT * FROM organizations;

# Check learners
SELECT * FROM learners LIMIT 5;

# Check fees
SELECT * FROM fees WHERE status = 'pending';
```

### Logs Monitoring

```bash
# Watch application logs
tail -f logs/app.log

# Watch payment logs
tail -f logs/payments.log
```

## 🚨 Troubleshooting

### Common Issues

1. **Authentication Failed**
   ```
   Error: Invalid or expired access token
   Solution: Check Zalo token expiration, refresh if needed
   ```

2. **Payment Creation Failed**
   ```
   Error: Invalid payment amount
   Solution: Check amount >= 1000 VND and <= 500,000,000 VND
   ```

3. **Database Connection Error**
   ```
   Error: Connection refused
   Solution: Check DATABASE_URL and ensure PostgreSQL is running
   ```

4. **ZaloPay Callback Failed**
   ```
   Error: Invalid MAC signature
   Solution: Verify ZALOPAY_KEY2 is correct
   ```

### Debug Mode

```bash
# Enable debug logging
DEBUG=education:* pnpm run dev

# Enable SQL query logging
DEBUG=supabase:* pnpm run dev
```

## 📚 Next Steps

1. **Read Full Documentation**: [EDUCATION_PLATFORM_README.md](./EDUCATION_PLATFORM_README.md)
2. **API Reference**: [EDUCATION_MINI_APP_API.md](./EDUCATION_MINI_APP_API.md)
3. **Deploy to Production**: Follow deployment guide
4. **Setup Monitoring**: Configure error tracking and analytics

## 💬 Support

- **Technical Issues**: Create GitHub issue
- **Integration Help**: Contact development team
- **ZaloPay Support**: Check ZaloPay documentation

---

🎉 **Congratulations!** Bạn đã setup thành công Education Platform. Happy coding!
