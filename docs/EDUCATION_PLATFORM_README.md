# Education Platform - SaaS for Kindergartens & Talent Centers

## Overview

Nền tảng SaaS hoàn chỉnh dành cho các trường mầm non và trung tâm năng khiếu, tích hợp với Zalo Mini App để phụ huynh và giáo viên có thể tương tác dễ dàng.

## Features

### 🏫 **Organization Management**
- Multi-tenant architecture với organization isolation
- Support kindergarten và talent center templates
- Customizable organization settings và branding
- Auto-initialization với sample data

### 👨‍👩‍👧‍👦 **User Management**
- **Guardians (<PERSON><PERSON> huynh)**: <PERSON>em thông tin con em, điể<PERSON> danh, b<PERSON><PERSON> c<PERSON>o, thanh toán học phí
- **Instructors (Giáo viên)**: <PERSON><PERSON><PERSON><PERSON> lý lớ<PERSON> họ<PERSON>, đi<PERSON><PERSON> danh, tạo báo cáo
- **Education Admins**: Quản lý toàn bộ hệ thống
- **Guests**: <PERSON><PERSON><PERSON> cậ<PERSON> hạn chế, logged cho follow-up

### 👶 **Learner Management**
- Complete learner profiles với health info
- Auto-generated learner codes
- Age-based program enrollment
- Guardian relationships management

### 📚 **Program Management**
- Flexible program types (regular class, talent programs, etc.)
- Capacity management với enrollment limits
- Schedule và location tracking
- Fee structure per program

### ✅ **Attendance System**
- Daily attendance tracking
- Multiple status types (present, absent, late, sick, excused)
- Guardian notifications
- Attendance statistics và reporting

### 📊 **Progress Reports**
- Daily, weekly, monthly reports
- Rich content support (text, photos, videos)
- Guardian feedback system
- Rating system với Vietnamese text

### 💰 **Fee Management & Payments**
- Flexible fee categories (tuition, meal, materials, etc.)
- Multiple billing periods
- **ZaloPay Integration** cho thanh toán online
- Automatic receipt generation
- Payment notifications

### 🔔 **Notification System**
- Education-specific notifications
- Payment success notifications
- Real-time updates cho guardians

## Technical Architecture

### 🏗️ **Database Schema**

```
organizations (Tổ chức)
├── programs (Chương trình học)
│   ├── enrollments (Đăng ký học)
│   └── attendance (Điểm danh)
├── learners (Học viên)
│   ├── learner_guardians (Quan hệ phụ huynh)
│   ├── progress_reports (Báo cáo tiến độ)
│   └── fees (Học phí)
├── guardians (Phụ huynh)
└── events (Sự kiện)

payment_records (Thanh toán)
├── payment_receipts (Hóa đơn)
└── education_notifications (Thông báo)
```

### 🔐 **Security**
- **Row Level Security (RLS)** cho data isolation
- Role-based access control
- Guardian authorization (chỉ xem được data con mình)
- ZaloPay MAC signature verification

### 🚀 **API Architecture**

#### **Core APIs**
- `/api/education/organizations` - Organization CRUD
- `/api/education/learners` - Learner management
- `/api/education/initialize` - Platform initialization

#### **Guardian APIs**
- `/api/education/guardian/children` - Children information
- `/api/education/guardian/attendance` - Attendance history
- `/api/education/guardian/reports` - Progress reports
- `/api/education/guardian/fees` - Fee management

#### **Instructor APIs**
- `/api/education/instructor/classes` - Class management

#### **Payment APIs**
- `/api/education/payments/create` - Create ZaloPay payment
- `/api/education/payments/status` - Check payment status
- `/api/education/payments/callback` - ZaloPay webhook

### 📱 **Zalo Mini App Integration**

#### **Authentication Flow**
```
Mini App → Zalo OAuth → /api/auth/zalo → Role Detection → Session Creation
```

#### **Role Detection Logic**
1. **Invitation Check**: Existing pending invitations
2. **Guardian Check**: Phone number trong guardians table
3. **Instructor Check**: User assigned to programs
4. **Default**: Guest role với access logging

#### **Theme-based Organization Detection**
- URL pattern: `miniapp.zalo.me/app?themeId=org-theme-id`
- Theme ID maps to specific organization
- Automatic organization context setting

## ZaloPay Integration

### 💳 **Payment Features**
- **Payment Methods**: ZaloPay App, ATM, Credit Cards
- **Real-time Processing**: Instant payment confirmation
- **Secure Callbacks**: MAC signature verification
- **Receipt Generation**: Automatic Vietnamese receipts
- **Refund Support**: Full refund capabilities

### 🔧 **Technical Implementation**

```typescript
// Payment Creation
const paymentIntent = await zaloPayProvider.createPaymentIntent({
  feeId: 'uuid',
  learnerId: 'uuid',
  learnerName: 'Nguyễn Văn A',
  organizationId: 'uuid',
  organizationName: 'Mầm Non ABC',
  guardianId: 'uuid',
  guardianName: 'Nguyễn Thị B',
  feeCategory: 'tuition',
  feeCategoryText: 'Học phí',
  originalAmount: 2500000,
  paymentType: 'education_fee'
});

// Payment URL for Mini App
window.location.href = paymentIntent.paymentUrl;
```

### 🔒 **Security Features**
- MAC signature verification cho callbacks
- Encrypted payment metadata
- Secure webhook endpoints
- Payment audit trail

## Setup & Configuration

### 📋 **Prerequisites**
- Node.js 18+
- PostgreSQL 14+
- Supabase account
- ZaloPay merchant account

### ⚙️ **Environment Variables**

```env
# Database
DATABASE_URL=postgresql://...
SUPABASE_URL=https://...
SUPABASE_ANON_KEY=...
SUPABASE_SERVICE_ROLE_KEY=...

# ZaloPay
ZALOPAY_APP_ID=your_app_id
ZALOPAY_KEY1=your_key1
ZALOPAY_KEY2=your_key2

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

### 🚀 **Installation**

```bash
# Install dependencies
pnpm install

# Setup database
pnpm run supabase:web:reset

# Generate types
pnpm run supabase:web:typegen

# Start development server
pnpm run dev
```

### 🏫 **Initialize Education Platform**

```bash
# API call to initialize with sample data
POST /api/education/initialize
{
  "account_id": "uuid",
  "education_type": "kindergarten", // or "talent_center"
  "custom_config": {
    "organization_name": "Mầm Non ABC",
    "director_name": "Cô Nguyễn Thị Lan"
  }
}
```

## Usage Examples

### 👨‍👩‍👧‍👦 **Guardian Workflow**

```javascript
// 1. Login via Zalo
const auth = await zaloAuth(accessToken, themeId);

// 2. Get children list
const children = await getChildren(organizationId);

// 3. Check attendance
const attendance = await getAttendance(learnerId, month);

// 4. View reports
const reports = await getReports(learnerId);

// 5. Pay fees
const payment = await createPayment(feeId);
window.location.href = payment.paymentUrl;
```

### 👨‍🏫 **Instructor Workflow**

```javascript
// 1. Get classes
const classes = await getInstructorClasses(organizationId);

// 2. Take attendance
await takeAttendance(programId, attendanceData);

// 3. Create daily report
await createReport(learnerId, reportData);
```

## Mini App Integration Guide

### 📱 **Zalo Mini App Setup**

1. **Theme Configuration**
   - Tạo theme cho mỗi organization
   - Configure theme_id trong Mini App URL
   - Map theme_id to organization trong database

2. **Authentication Integration**
   ```javascript
   // Trong Mini App
   const authResult = await fetch('/api/auth/zalo', {
     method: 'POST',
     body: JSON.stringify({
       access_token: zaloAccessToken,
       theme_id: getThemeIdFromUrl()
     })
   });
   ```

3. **API Integration**
   - Sử dụng JWT token từ auth response
   - Handle role-based navigation
   - Implement payment flow

### 📚 **API Documentation**
Chi tiết API documentation: [EDUCATION_MINI_APP_API.md](./EDUCATION_MINI_APP_API.md)

## Testing

### 🧪 **API Testing**

```bash
# Test education APIs
node scripts/test-education-api.js

# Test payment APIs
node scripts/test-payment-api.js
```

### 💳 **ZaloPay Testing**
- Sử dụng Sandbox environment
- Test cards có sẵn trong ZaloPay docs
- Webhook testing với ngrok

## Deployment

### 🌐 **Production Checklist**

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] ZaloPay production credentials
- [ ] SSL certificates
- [ ] Webhook URLs configured
- [ ] Monitoring setup

### 📊 **Monitoring**

- Payment transaction logs
- API response times
- Error tracking
- User activity analytics

## Support & Maintenance

### 🔧 **Common Issues**

1. **Payment Failures**
   - Check ZaloPay credentials
   - Verify webhook URLs
   - Review MAC signature calculation

2. **Authentication Issues**
   - Validate Zalo access tokens
   - Check theme_id mapping
   - Review RLS policies

3. **Data Access Issues**
   - Verify guardian relationships
   - Check organization membership
   - Review role assignments

### 📞 **Support Contacts**

- Technical Support: [Contact Info]
- ZaloPay Integration: [Contact Info]
- Documentation: [Link to detailed docs]

## Roadmap

### 🎯 **Upcoming Features**

- [ ] Event management system
- [ ] In-app messaging
- [ ] Photo/video sharing
- [ ] QR code attendance
- [ ] Advanced analytics
- [ ] Mobile app support
- [ ] Multi-language support

### 🔄 **Continuous Improvements**

- Performance optimization
- Security enhancements
- User experience improvements
- API documentation updates

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**License**: Proprietary
