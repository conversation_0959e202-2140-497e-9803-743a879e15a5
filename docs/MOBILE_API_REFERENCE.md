# Education Platform Mobile API Reference

## Overview

This document provides comprehensive API documentation for mobile app integration with the Education SaaS platform. All endpoints use RESTful conventions and return JSON responses.

## Base URL

```
https://your-domain.com/api/education
```

## Authentication

All API requests require authentication using JWT tokens obtained through Zalo OAuth flow.

```bash
# Include in all requests
-H "Authorization: Bearer {access_token}"
-H "Content-Type: application/json"
```

## Quick Start

### 1. Authentication Flow

```bash
# Step 1: Authenticate with Zalo
curl -X POST "https://your-domain.com/api/auth/zalo" \
  -H "Content-Type: application/json" \
  -d '{
    "access_token": "zalo_access_token",
    "theme_id": "organization_theme_id"
  }'
```

**Enhanced Response with Roles & Permissions:**
```json
{
  "success": true,
  "user": {
    "id": "uuid",
    "name": "Nguyễn <PERSON>n <PERSON>",
    "picture": "https://avatar.url",
    "phone": "**********"
  },
  "access_token": "jwt_token",
  "refresh_token": "refresh_token",
  "account_id": "uuid",
  "organization": {
    "id": "uuid",
    "name": "Trường Mầm Non ABC",
    "logo_url": "https://logo.url",
    "settings": {}
  },
  "roles": ["guardian", "instructor"],
  "primary_role": "guardian",
  "permissions": {
    "can_view_dashboard": true,
    "can_manage_learners": false,
    "can_take_attendance": true,
    "can_create_reports": true,
    "can_view_children": true,
    "can_pay_fees": true
  },
  "guardian_info": {
    "id": "uuid",
    "full_name": "Nguyễn Văn A"
  },
  "instructor_programs": [
    {
      "id": "uuid",
      "name": "Lớp Chồi A"
    }
  ],
  "theme_id": "theme_id"
}
```

### 2. Get User's Children

```bash
curl -X GET "https://your-domain.com/api/education/guardian/children?organization_id=uuid" \
  -H "Authorization: Bearer {access_token}"
```

### 3. Get Attendance Records

```bash
curl -X GET "https://your-domain.com/api/education/guardian/attendance?learner_id=uuid&organization_id=uuid&month=2024-12" \
  -H "Authorization: Bearer {access_token}"
```

## Guardian APIs

### Children Management

#### Get Children List
```bash
curl -X GET "https://your-domain.com/api/education/guardian/children?organization_id=uuid" \
  -H "Authorization: Bearer {access_token}"
```

#### Get Child Details
```bash
curl -X POST "https://your-domain.com/api/education/guardian/children" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "learner_id": "uuid",
    "organization_id": "uuid"
  }'
```

### Attendance Tracking

#### Get Attendance History
```bash
curl -X GET "https://your-domain.com/api/education/guardian/attendance?learner_id=uuid&organization_id=uuid&month=2024-12" \
  -H "Authorization: Bearer {access_token}"
```

#### Get Attendance Summary
```bash
curl -X POST "https://your-domain.com/api/education/guardian/attendance" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "learner_id": "uuid",
    "organization_id": "uuid",
    "year": "2024"
  }'
```

### Progress Reports

#### Get Reports List
```bash
curl -X GET "https://your-domain.com/api/education/guardian/reports?learner_id=uuid&organization_id=uuid&limit=10" \
  -H "Authorization: Bearer {access_token}"
```

#### Get Report Details
```bash
curl -X POST "https://your-domain.com/api/education/guardian/reports" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "report_id": "uuid",
    "organization_id": "uuid"
  }'
```

### Fee Management

#### Get Fees List
```bash
curl -X GET "https://your-domain.com/api/education/guardian/fees?organization_id=uuid&status=pending" \
  -H "Authorization: Bearer {access_token}"
```

#### Get Fee Details
```bash
curl -X POST "https://your-domain.com/api/education/guardian/fees" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "fee_id": "uuid",
    "organization_id": "uuid"
  }'
```

### Events

#### Get Events List
```bash
curl -X GET "https://your-domain.com/api/education/guardian/events?organization_id=uuid&upcoming=true" \
  -H "Authorization: Bearer {access_token}"
```

#### Register for Event
```bash
curl -X POST "https://your-domain.com/api/education/guardian/events/register" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "event_id": "uuid",
    "organization_id": "uuid",
    "learner_ids": ["uuid1", "uuid2"],
    "notes": "Registration notes"
  }'
```

#### Cancel Event Registration
```bash
curl -X DELETE "https://your-domain.com/api/education/guardian/events/register?event_id=uuid&organization_id=uuid&learner_ids=uuid1,uuid2" \
  -H "Authorization: Bearer {access_token}"
```

## Instructor APIs

### Class Management

#### Get Classes List
```bash
curl -X GET "https://your-domain.com/api/education/instructor/classes?organization_id=uuid" \
  -H "Authorization: Bearer {access_token}"
```

#### Get Class Details
```bash
curl -X POST "https://your-domain.com/api/education/instructor/classes" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "program_id": "uuid",
    "organization_id": "uuid"
  }'
```

### Attendance Management

#### Get Attendance Template
```bash
curl -X GET "https://your-domain.com/api/education/instructor/attendance?program_id=uuid&organization_id=uuid&session_date=2024-12-01&session_time=morning" \
  -H "Authorization: Bearer {access_token}"
```

#### Submit Attendance
```bash
curl -X POST "https://your-domain.com/api/education/instructor/attendance" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "program_id": "uuid",
    "organization_id": "uuid",
    "session_date": "2024-12-01",
    "session_time": "morning",
    "attendance_records": [
      {
        "learner_id": "uuid",
        "status": "present",
        "check_in_time": "07:45",
        "check_out_time": "16:30",
        "notes": ""
      }
    ]
  }'
```

### Progress Reports

#### Create Report
```bash
curl -X POST "https://your-domain.com/api/education/instructor/reports" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "learner_id": "uuid",
    "program_id": "uuid",
    "organization_id": "uuid",
    "report_date": "2024-12-01",
    "report_type": "daily",
    "content": {
      "academic": {
        "subjects": ["Toán", "Tiếng Việt"],
        "performance": "good"
      },
      "behavior": {
        "social": "excellent",
        "discipline": "good"
      },
      "activities": ["Vẽ tranh", "Hát"],
      "notes": "Bé rất tích cực trong các hoạt động"
    },
    "overall_rating": "good",
    "media_urls": ["url1", "url2"]
  }'
```

#### Get Reports List
```bash
curl -X GET "https://your-domain.com/api/education/instructor/reports?organization_id=uuid&program_id=uuid&limit=20" \
  -H "Authorization: Bearer {access_token}"
```

## Payment APIs

### Create Payment
```bash
curl -X POST "https://your-domain.com/api/education/payments/create" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "fee_id": "uuid",
    "organization_id": "uuid",
    "return_url": "https://miniapp.zalo.me/payment-result"
  }'
```

### Check Payment Status
```bash
curl -X GET "https://your-domain.com/api/education/payments/status?payment_id=uuid&organization_id=uuid" \
  -H "Authorization: Bearer {access_token}"
```

## Notifications APIs

### Get Notifications
```bash
curl -X GET "https://your-domain.com/api/education/notifications?organization_id=uuid&limit=20" \
  -H "Authorization: Bearer {access_token}"
```

### Mark as Read
```bash
curl -X PUT "https://your-domain.com/api/education/notifications" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "notification_ids": ["uuid1", "uuid2"],
    "organization_id": "uuid"
  }'
```

### Mark All as Read
```bash
curl -X PUT "https://your-domain.com/api/education/notifications" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "mark_all_read": true,
    "organization_id": "uuid"
  }'
```

## File Upload APIs

### Upload Media
```bash
curl -X POST "https://your-domain.com/api/education/upload" \
  -H "Authorization: Bearer {access_token}" \
  -F "file=@image.jpg" \
  -F "organization_id=uuid" \
  -F "upload_type=report_media" \
  -F "learner_id=uuid" \
  -F "report_id=uuid"
```

### Get Files List
```bash
curl -X GET "https://your-domain.com/api/education/upload?organization_id=uuid&upload_type=report_media&limit=20" \
  -H "Authorization: Bearer {access_token}"
```

## Response Format

All APIs return responses in this format:

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "error": "Error message (if success is false)"
}
```

## Error Codes

- `400` - Bad Request: Invalid parameters
- `401` - Unauthorized: Invalid or missing authentication
- `403` - Forbidden: Insufficient permissions
- `404` - Not Found: Resource not found
- `500` - Internal Server Error: Server error

## Rate Limiting

- 100 requests per minute per user
- 1000 requests per hour per organization

## Data Types

### User Roles
- `guardian` - Phụ huynh/Người giám hộ
- `instructor` - Giáo viên
- `assistant` - Trợ giảng
- `education_admin` - Quản trị viên giáo dục

### Status Values
- **Fee Status**: `pending`, `processing`, `paid`, `overdue`, `cancelled`
- **Payment Status**: `pending`, `processing`, `succeeded`, `failed`, `cancelled`
- **Attendance Status**: `present`, `absent`, `late`, `sick`, `excused`
- **Report Rating**: `excellent`, `good`, `average`, `needs_improvement`

## SDK Examples

### JavaScript/React Native
```javascript
const API_BASE = 'https://your-domain.com/api/education';

class EducationAPI {
  constructor(accessToken) {
    this.accessToken = accessToken;
  }

  async request(endpoint, options = {}) {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });
    return response.json();
  }

  async getChildren(organizationId) {
    return this.request(`/guardian/children?organization_id=${organizationId}`);
  }

  async getAttendance(learnerId, organizationId, month) {
    return this.request(`/guardian/attendance?learner_id=${learnerId}&organization_id=${organizationId}&month=${month}`);
  }
}
```

## Additional Module APIs

### Curriculum Management

#### Get Curriculum List
```bash
curl -X GET "https://your-domain.com/api/education/curriculum?organization_id=uuid&subject=Math&age_group=3-4" \
  -H "Authorization: Bearer {access_token}"
```

#### Create Curriculum
```bash
curl -X POST "https://your-domain.com/api/education/curriculum" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "organization_id": "uuid",
    "title": "Toán cơ bản cho trẻ 3-4 tuổi",
    "subject": "Toán",
    "age_group": "3-4",
    "difficulty_level": "beginner",
    "duration_minutes": 30,
    "objectives": ["Nhận biết số từ 1-10", "Đếm đồ vật"]
  }'
```

### Health Management

#### Get Health Records
```bash
curl -X GET "https://your-domain.com/api/education/health?organization_id=uuid&learner_id=uuid&record_type=vaccination" \
  -H "Authorization: Bearer {access_token}"
```

#### Create Health Record
```bash
curl -X POST "https://your-domain.com/api/education/health" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "organization_id": "uuid",
    "learner_id": "uuid",
    "record_type": "vaccination",
    "title": "Tiêm chủng viêm gan B",
    "record_date": "2024-12-01",
    "doctor_name": "BS. Nguyễn Văn A"
  }'
```

### Meals Management

#### Get Meal Plans
```bash
curl -X GET "https://your-domain.com/api/education/meals?organization_id=uuid&date=2024-12-01&meal_type=lunch" \
  -H "Authorization: Bearer {access_token}"
```

#### Create Meal Plan
```bash
curl -X POST "https://your-domain.com/api/education/meals" \
  -H "Authorization: Bearer {access_token}" \
  -d '{
    "organization_id": "uuid",
    "date": "2024-12-01",
    "meal_type": "lunch",
    "menu_items": [
      {
        "name": "Cơm gà",
        "description": "Cơm trắng với thịt gà luộc",
        "ingredients": ["Gạo", "Thịt gà", "Muối"]
      }
    ]
  }'
```

### Transportation Management

#### Get Transportation Routes
```bash
curl -X GET "https://your-domain.com/api/education/transportation?organization_id=uuid&date=2024-12-01" \
  -H "Authorization: Bearer {access_token}"
```

### Library Management

#### Get Library Items
```bash
curl -X GET "https://your-domain.com/api/education/library?organization_id=uuid&item_type=book&available_only=true" \
  -H "Authorization: Bearer {access_token}"
```

### Dashboard Analytics

#### Get Dashboard Metrics
```bash
curl -X GET "https://your-domain.com/api/education/dashboard?organization_id=uuid&period=month" \
  -H "Authorization: Bearer {access_token}"
```

## Complete API Coverage

| **Module** | **APIs** | **Mobile Ready** |
|------------|----------|------------------|
| Guardian APIs | 11 | ✅ |
| Instructor APIs | 6 | ✅ |
| Payment APIs | 3 | ✅ |
| Notification APIs | 3 | ✅ |
| File Upload APIs | 2 | ✅ |
| Event APIs | 3 | ✅ |
| **Curriculum APIs** | **2** | **✅** |
| **Health APIs** | **2** | **✅** |
| **Meals APIs** | **3** | **✅** |
| **Transportation APIs** | **3** | **✅** |
| **Library APIs** | **3** | **✅** |
| **Dashboard APIs** | **1** | **✅** |
| **TOTAL** | **42** | **✅** |

## Support

For technical support and API questions:
- Email: <EMAIL>
- Documentation: https://docs.your-domain.com
- Status Page: https://status.your-domain.com
