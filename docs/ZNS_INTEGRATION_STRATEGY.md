# ZNS Integration Strategy for Education SaaS

## 🎯 Overview

Tích hợp Zalo Notification Service (ZNS) để cung cấp dịch vụ thông báo chung cho các trung tâm giáo dục không đăng ký doanh nghiệp, tối ưu hóa chi phí và hiệu quả thông báo.

## 📱 ZNS Service Architecture

### 1. **Shared ZNS Pool Strategy**

```javascript
const ZNSPoolConfig = {
  // Shared ZNS accounts for small centers
  sharedPool: {
    account1: {
      oa_id: 'shared_oa_1',
      app_id: 'shared_app_1',
      quota: 10000, // messages per month
      assignedCenters: [],
      currentUsage: 0
    },
    account2: {
      oa_id: 'shared_oa_2', 
      app_id: 'shared_app_2',
      quota: 10000,
      assignedCenters: [],
      currentUsage: 0
    }
  },
  
  // Dedicated accounts for enterprise centers
  dedicatedAccounts: {
    // Centers with their own ZNS registration
  },
  
  // Load balancing strategy
  loadBalancer: {
    algorithm: 'round_robin', // or 'least_usage'
    fallback: true,
    retryAttempts: 3
  }
};
```

### 2. **Message Template Management**

```javascript
const MessageTemplates = {
  // Attendance notifications
  attendance: {
    absent: {
      template_id: 'ATT_ABSENT_001',
      template: 'Xin chào {{parent_name}}, {{child_name}} vắng mặt ngày {{date}} tại {{school_name}}. Vui lòng liên hệ giáo viên để biết thêm chi tiết.',
      params: ['parent_name', 'child_name', 'date', 'school_name']
    },
    late: {
      template_id: 'ATT_LATE_001', 
      template: 'Xin chào {{parent_name}}, {{child_name}} đến muộn ngày {{date}} lúc {{time}} tại {{school_name}}.',
      params: ['parent_name', 'child_name', 'date', 'time', 'school_name']
    }
  },
  
  // Payment notifications
  payment: {
    reminder: {
      template_id: 'PAY_REMINDER_001',
      template: 'Xin chào {{parent_name}}, học phí tháng {{month}} của {{child_name}} sẽ đến hạn vào {{due_date}}. Số tiền: {{amount}}đ. Thanh toán tại: {{payment_link}}',
      params: ['parent_name', 'month', 'child_name', 'due_date', 'amount', 'payment_link']
    },
    success: {
      template_id: 'PAY_SUCCESS_001',
      template: 'Cảm ơn {{parent_name}}! Học phí tháng {{month}} của {{child_name}} đã được thanh toán thành công. Số tiền: {{amount}}đ.',
      params: ['parent_name', 'month', 'child_name', 'amount']
    }
  },
  
  // Health notifications
  health: {
    emergency: {
      template_id: 'HEALTH_EMERGENCY_001',
      template: 'KHẨN CẤP: {{child_name}} cần được chăm sóc y tế tại {{school_name}}. Vui lòng liên hệ ngay: {{contact_phone}}',
      params: ['child_name', 'school_name', 'contact_phone']
    },
    medication: {
      template_id: 'HEALTH_MED_001',
      template: 'Xin chào {{parent_name}}, {{child_name}} đã được uống thuốc {{medication}} lúc {{time}} theo chỉ định.',
      params: ['parent_name', 'child_name', 'medication', 'time']
    }
  },
  
  // Event notifications
  events: {
    reminder: {
      template_id: 'EVENT_REMINDER_001',
      template: 'Nhắc nhở: Sự kiện "{{event_name}}" sẽ diễn ra vào {{date}} tại {{location}}. {{child_name}} đã đăng ký tham gia.',
      params: ['event_name', 'date', 'location', 'child_name']
    },
    registration: {
      template_id: 'EVENT_REG_001',
      template: 'Đăng ký thành công! {{child_name}} đã được đăng ký tham gia "{{event_name}}" vào {{date}}.',
      params: ['child_name', 'event_name', 'date']
    }
  }
};
```

### 3. **Smart Routing System**

```javascript
class ZNSRouter {
  constructor() {
    this.pools = ZNSPoolConfig.sharedPool;
    this.dedicatedAccounts = ZNSPoolConfig.dedicatedAccounts;
  }

  async routeMessage(organizationId, messageType, recipients, templateData) {
    // Check if organization has dedicated ZNS
    const dedicatedAccount = this.dedicatedAccounts[organizationId];
    if (dedicatedAccount) {
      return this.sendViaDedicated(dedicatedAccount, messageType, recipients, templateData);
    }

    // Use shared pool with load balancing
    const selectedPool = this.selectOptimalPool(recipients.length);
    return this.sendViaSharedPool(selectedPool, organizationId, messageType, recipients, templateData);
  }

  selectOptimalPool(messageCount) {
    // Find pool with lowest usage and sufficient quota
    const availablePools = Object.values(this.pools).filter(pool => 
      (pool.quota - pool.currentUsage) >= messageCount
    );

    if (availablePools.length === 0) {
      throw new Error('No available ZNS pool with sufficient quota');
    }

    // Return pool with lowest usage percentage
    return availablePools.reduce((best, current) => 
      (current.currentUsage / current.quota) < (best.currentUsage / best.quota) ? current : best
    );
  }

  async sendViaSharedPool(pool, organizationId, messageType, recipients, templateData) {
    const template = MessageTemplates[messageType.category][messageType.type];
    
    // Add organization branding to template
    const brandedTemplate = this.addOrganizationBranding(template, organizationId);
    
    // Send messages
    const results = await Promise.allSettled(
      recipients.map(recipient => 
        this.sendZNSMessage(pool, brandedTemplate, recipient, templateData)
      )
    );

    // Update usage tracking
    pool.currentUsage += recipients.length;
    
    return this.processResults(results);
  }

  addOrganizationBranding(template, organizationId) {
    // Add school name and contact info to template
    const orgInfo = this.getOrganizationInfo(organizationId);
    return {
      ...template,
      template: template.template.replace('{{school_name}}', orgInfo.name),
      footer: `\n\n${orgInfo.name}\n📞 ${orgInfo.phone}\n📍 ${orgInfo.address}`
    };
  }
}
```

### 4. **Usage Tracking & Analytics**

```javascript
const UsageTracker = {
  // Track usage per organization
  trackUsage: async (organizationId, messageType, count, cost) => {
    await supabase.from('zns_usage').insert({
      organization_id: organizationId,
      message_type: messageType,
      message_count: count,
      cost: cost,
      sent_at: new Date().toISOString()
    });
  },

  // Generate monthly reports
  generateMonthlyReport: async (organizationId, month) => {
    const { data: usage } = await supabase
      .from('zns_usage')
      .select('*')
      .eq('organization_id', organizationId)
      .gte('sent_at', `${month}-01`)
      .lt('sent_at', `${month}-31`);

    return {
      totalMessages: usage.reduce((sum, u) => sum + u.message_count, 0),
      totalCost: usage.reduce((sum, u) => sum + u.cost, 0),
      breakdown: usage.reduce((acc, u) => {
        acc[u.message_type] = (acc[u.message_type] || 0) + u.message_count;
        return acc;
      }, {}),
      costPerMessage: usage.length > 0 ? 
        usage.reduce((sum, u) => sum + u.cost, 0) / usage.reduce((sum, u) => sum + u.message_count, 0) : 0
    };
  }
};
```

## 🔄 Integration with Education APIs

### 1. **Automatic Trigger System**

```javascript
// Integration with existing education APIs
const ZNSIntegration = {
  // Attendance notifications
  onAttendanceSubmit: async (attendanceData) => {
    const absentStudents = attendanceData.records.filter(r => r.status === 'absent');
    
    for (const student of absentStudents) {
      const guardians = await getStudentGuardians(student.learner_id);
      
      await ZNSRouter.routeMessage(
        attendanceData.organization_id,
        { category: 'attendance', type: 'absent' },
        guardians.map(g => ({ phone: g.phone, name: g.full_name })),
        {
          parent_name: guardians[0].full_name,
          child_name: student.learner.full_name,
          date: attendanceData.session_date,
          school_name: '{{school_name}}' // Will be replaced by router
        }
      );
    }
  },

  // Payment notifications
  onPaymentDue: async (feeData) => {
    const guardians = await getStudentGuardians(feeData.learner_id);
    
    await ZNSRouter.routeMessage(
      feeData.organization_id,
      { category: 'payment', type: 'reminder' },
      guardians.map(g => ({ phone: g.phone, name: g.full_name })),
      {
        parent_name: guardians[0].full_name,
        month: feeData.month,
        child_name: feeData.learner.full_name,
        due_date: feeData.due_date,
        amount: feeData.amount.toLocaleString('vi-VN'),
        payment_link: `${process.env.MINI_APP_URL}/payment/${feeData.id}`
      }
    );
  },

  // Health emergency notifications
  onHealthEmergency: async (healthRecord) => {
    if (healthRecord.is_emergency) {
      const guardians = await getStudentGuardians(healthRecord.learner_id);
      const organization = await getOrganization(healthRecord.organization_id);
      
      await ZNSRouter.routeMessage(
        healthRecord.organization_id,
        { category: 'health', type: 'emergency' },
        guardians.map(g => ({ phone: g.phone, name: g.full_name })),
        {
          child_name: healthRecord.learner.full_name,
          school_name: organization.name,
          contact_phone: organization.phone
        }
      );
    }
  }
};
```

### 2. **Scheduled Notifications**

```javascript
const ScheduledNotifications = {
  // Daily attendance reminders
  dailyAttendanceReminder: {
    schedule: '0 7 * * 1-5', // 7 AM, Monday to Friday
    handler: async () => {
      const organizations = await getActiveOrganizations();
      
      for (const org of organizations) {
        const todayAbsences = await getTodayAbsences(org.id);
        
        if (todayAbsences.length > 0) {
          // Send summary to administrators
          await sendAttendanceSummary(org.id, todayAbsences);
        }
      }
    }
  },

  // Weekly payment reminders
  weeklyPaymentReminder: {
    schedule: '0 9 * * 1', // 9 AM every Monday
    handler: async () => {
      const upcomingFees = await getUpcomingFees(7); // Next 7 days
      
      for (const fee of upcomingFees) {
        await ZNSIntegration.onPaymentDue(fee);
      }
    }
  },

  // Monthly usage reports
  monthlyUsageReport: {
    schedule: '0 8 1 * *', // 8 AM on 1st of every month
    handler: async () => {
      const organizations = await getActiveOrganizations();
      
      for (const org of organizations) {
        const lastMonth = new Date();
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        
        const report = await UsageTracker.generateMonthlyReport(
          org.id, 
          lastMonth.toISOString().slice(0, 7)
        );
        
        await sendUsageReport(org.id, report);
      }
    }
  }
};
```

## 💰 Pricing & Billing Strategy

### 1. **Tiered Pricing Model**

```javascript
const PricingTiers = {
  starter: {
    name: 'Starter',
    monthlyFee: 0,
    includedMessages: 100,
    overageRate: 500, // VND per message
    features: ['Basic notifications', 'Shared ZNS pool']
  },
  
  professional: {
    name: 'Professional', 
    monthlyFee: 200000, // 200k VND
    includedMessages: 1000,
    overageRate: 400,
    features: ['All notifications', 'Priority routing', 'Analytics']
  },
  
  enterprise: {
    name: 'Enterprise',
    monthlyFee: 500000, // 500k VND
    includedMessages: 5000,
    overageRate: 300,
    features: ['Dedicated ZNS', 'Custom templates', 'Advanced analytics']
  }
};
```

### 2. **Cost Optimization**

```javascript
const CostOptimization = {
  // Batch similar messages
  batchMessages: (messages) => {
    const batches = {};
    
    messages.forEach(msg => {
      const key = `${msg.template_id}_${msg.organization_id}`;
      if (!batches[key]) batches[key] = [];
      batches[key].push(msg);
    });
    
    return Object.values(batches);
  },

  // Smart scheduling to avoid peak hours
  scheduleOptimal: (messages) => {
    const now = new Date();
    const hour = now.getHours();
    
    // Avoid peak hours (8-9 AM, 5-6 PM)
    if ((hour >= 8 && hour <= 9) || (hour >= 17 && hour <= 18)) {
      // Schedule for next available slot
      const nextSlot = new Date(now);
      nextSlot.setHours(hour >= 17 ? 19 : 10);
      return nextSlot;
    }
    
    return now; // Send immediately
  },

  // Deduplicate messages
  deduplicate: (messages) => {
    const seen = new Set();
    return messages.filter(msg => {
      const key = `${msg.phone}_${msg.template_id}_${msg.date}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }
};
```

## 📊 Monitoring & Analytics

### 1. **Real-time Dashboard**

```javascript
const ZNSDashboard = {
  metrics: {
    // Real-time metrics
    messagesPerMinute: 'Current sending rate',
    successRate: 'Delivery success percentage',
    poolUtilization: 'Usage across shared pools',
    
    // Daily metrics
    dailyVolume: 'Messages sent today',
    dailyCost: 'Cost incurred today',
    errorRate: 'Failed message percentage',
    
    // Monthly metrics
    monthlyTrends: 'Usage trends over time',
    costAnalysis: 'Cost breakdown by message type',
    organizationRanking: 'Top organizations by usage'
  },

  alerts: {
    quotaWarning: 'Pool approaching quota limit',
    highErrorRate: 'Error rate above threshold',
    costSpike: 'Unusual cost increase detected',
    poolDown: 'ZNS pool unavailable'
  }
};
```

### 2. **Performance Optimization**

```javascript
const PerformanceOptimizer = {
  // Queue management
  messageQueue: {
    priority: ['emergency', 'urgent', 'normal', 'low'],
    batchSize: 100,
    retryPolicy: {
      maxRetries: 3,
      backoffMultiplier: 2,
      initialDelay: 1000
    }
  },

  // Rate limiting
  rateLimiter: {
    perSecond: 10,
    perMinute: 500,
    perHour: 10000,
    perDay: 100000
  },

  // Circuit breaker
  circuitBreaker: {
    failureThreshold: 5,
    timeout: 60000,
    resetTimeout: 300000
  }
};
```

## 🚀 Implementation Roadmap

### Phase 1: Core ZNS Integration (4 weeks)
- ✅ Basic ZNS API integration
- ✅ Message template system
- ✅ Shared pool routing
- ✅ Usage tracking

### Phase 2: Advanced Features (6 weeks)
- ✅ Smart scheduling
- ✅ Batch processing
- ✅ Analytics dashboard
- ✅ Cost optimization

### Phase 3: Enterprise Features (4 weeks)
- ✅ Dedicated account support
- ✅ Custom templates
- ✅ Advanced analytics
- ✅ White-label options

### Phase 4: AI Enhancement (8 weeks)
- 🔄 Smart message timing
- 🔄 Personalization engine
- 🔄 Predictive analytics
- 🔄 Auto-optimization

Đây là strategy hoàn chỉnh cho ZNS integration. Bạn muốn tôi detail hơn về phần nào, hoặc bắt đầu implement các components cụ thể?
