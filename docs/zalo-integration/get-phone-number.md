# Zalo Mini App - Get Phone Number API

This document describes how to use the Zalo Mini App getPhoneNumber API integration.

## Overview

The getPhoneNumber API allows you to retrieve a user's phone number from Zalo Mini App after they have authorized the request and update the user's profile. This is useful for user registration, verification, and other scenarios where you need the user's phone number.

## API Endpoint

```
POST /api/integrations/zalo/phone
```

## Authentication

This API requires authentication. You must include a valid JWT token in the Authorization header:

```
Authorization: Bearer <token>
```

## Request Parameters

| Parameter | Type   | Required | Description                                   |
|-----------|--------|----------|-----------------------------------------------|
| code      | string | Yes      | OAuth code from Zalo Mini App                 |
| theme_id  | string | Yes      | Theme ID to get OA configuration from         |

## Response

### Success Response

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-id",
      "name": "User Name",
      "phone": "+84123456789",
      "picture": "https://example.com/avatar.jpg"
    }
  }
}
```

### Error Response

```json
{
  "success": false,
  "error": "Error message",
  "details": { /* Additional error details */ }
}
```

## Integration Flow

1. **Frontend**: Request phone number permission from the user using Zalo Mini App SDK
2. **Frontend**: Receive OAuth code after user grants permission
3. **Frontend**: Send the code to your backend API with authentication token
4. **Backend**: Exchange the code for an access token
5. **Backend**: Use the access token to get the user's phone number
6. **Backend**: Update the user's profile with the phone number
7. **Backend**: Return the updated user information to the frontend

## Frontend Implementation

### Using the Zalo Mini App SDK

```javascript
import { requestPhoneNumber } from 'zmp-sdk';
import { getPhoneNumber } from '@/lib/zalo/mini-app';

async function getUserPhoneNumber(themeId) {
  try {
    // Request phone number permission from the user
    const { code } = await requestPhoneNumber();

    // Send the code to your backend API
    const user = await getPhoneNumber(code, themeId);

    console.log('User updated with phone number:', user.phone);
    return user;
  } catch (error) {
    console.error('Error getting phone number:', error);
    throw error;
  }
}
```

## Backend Implementation

The backend API is already implemented in the Next.js application. It handles:

1. Authenticating the user and getting the account_id from user metadata
2. Getting the OA configuration from the account_themes table using the provided theme_id
3. If theme OA configuration not found, falling back to the account OA configuration
4. Getting the phone number directly from Zalo API using the code, app_id, and secret_key
5. Updating the user's profile with the phone number
6. Returning the updated user information to the client

## Error Handling

Common errors that may occur:

- **401 Unauthorized**: Missing or invalid authentication token
- **404 Not Found**: OA configuration not found
- **400 Bad Request**: Invalid parameters or phone number not available
- **500 Internal Server Error**: Failed to get access token or phone number from Zalo

## Security Considerations

- The API requires authentication to ensure only authorized users can update their profiles
- The account_id is extracted from the authenticated user's metadata
- The code is a one-time use token and expires quickly
- The API updates the user's profile with the phone number and returns the updated user information

## Testing

You can test the API using the provided test script:

```bash
node apps/e2e/test-api/scripts/zalo/test-get-phone-auth.js <code> <theme_id>
```

## Troubleshooting

If you encounter issues:

1. Check that the user is authenticated and has a valid account_id in their metadata
2. Check that the OA configuration exists for the account
3. Verify that the app_id and secret_key are correct
4. Ensure the code is valid and not expired
5. Check the logs for detailed error messages
