# Education Platform API Documentation for Mini App

## Overview

Tài liệu này mô tả các API endpoints mà Zalo Mini App cần tích hợp để xây dựng ứng dụng giáo dục hoàn chỉnh.

## Authentication

Tất cả API endpoints yêu cầu authentication thông qua JWT token được lấy từ Zalo Auth flow.

### Zalo Authentication Flow

```
1. Mini App → POST /api/auth/zalo
   - Body: { access_token, theme_id }
   - Response: { access_token, refresh_token, user, organization_id }

2. Sử dụng access_token cho các API calls tiếp theo
   - Header: Authorization: Bearer {access_token}
```

## API Endpoints

### 1. Guardian APIs

#### 1.1 L<PERSON><PERSON> danh s<PERSON>ch con em

```bash
curl -X GET "https://your-domain.com/api/education/guardian/children?organization_id=uuid" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "guardian": {
      "id": "uuid",
      "fullName": "Nguyễn <PERSON>",
      "relationship": "mother"
    },
    "children": [
      {
        "id": "uuid",
        "learnerCode": "HMA0001",
        "fullName": "Nguyễn Minh An",
        "nickname": "Bé An",
        "dateOfBirth": "2020-03-15",
        "gender": "male",
        "avatarUrl": "https://...",
        "status": "active",
        "programs": [
          {
            "enrollmentId": "uuid",
            "enrollmentStatus": "active",
            "program": {
              "id": "uuid",
              "name": "Lớp Chồi (3-4 tuổi)",
              "type": "regular_class",
              "location": "Phòng A1"
            }
          }
        ]
      }
    ],
    "totalChildren": 1
  }
}
```

#### 1.2 Lấy lịch sử điểm danh

```bash
curl -X GET "https://your-domain.com/api/education/guardian/attendance?learner_id=uuid&organization_id=uuid&month=2024-12" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": "uuid",
        "date": "2024-12-01",
        "sessionTime": "morning",
        "checkInTime": "07:45",
        "checkOutTime": "16:30",
        "status": "present",
        "statusText": "Có mặt",
        "statusColor": "#10B981",
        "notes": "",
        "program": {
          "name": "Lớp Chồi (3-4 tuổi)"
        }
      }
    ],
    "stats": {
      "totalDays": 20,
      "presentDays": 18,
      "absentDays": 1,
      "lateDays": 1,
      "attendanceRate": 95
    }
  }
}
```

#### 1.3 Lấy báo cáo học tập

```bash
curl -X GET "https://your-domain.com/api/education/guardian/reports?learner_id=uuid&organization_id=uuid&limit=10" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "reports": [
      {
        "id": "uuid",
        "date": "2024-12-01",
        "type": "daily",
        "typeText": "Báo cáo hàng ngày",
        "content": {
          "academic": {
            "subjects": ["Toán", "Tiếng Việt"],
            "performance": "good"
          },
          "behavior": {
            "social": "excellent",
            "discipline": "good"
          },
          "activities": ["Vẽ tranh", "Hát"],
          "media": {
            "photos": ["url1", "url2"],
            "videos": []
          },
          "notes": "Bé rất tích cực trong các hoạt động"
        },
        "overallRating": "good",
        "ratingText": "Tốt",
        "ratingColor": "#3B82F6",
        "instructor": {
          "fullName": "Cô Nguyễn Thị Mai"
        }
      }
    ]
  }
}
```

#### 1.4 Lấy danh sách học phí

```bash
curl -X GET "https://your-domain.com/api/education/guardian/fees?organization_id=uuid&status=pending" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "fees": [
      {
        "id": "uuid",
        "category": "tuition",
        "categoryText": "Học phí",
        "amount": 2500000,
        "finalAmount": 2500000,
        "dueDate": "2024-12-31",
        "status": "pending",
        "statusText": "Chưa thanh toán",
        "statusColor": "#F59E0B",
        "description": "Học phí tháng 12/2024",
        "learner": {
          "id": "uuid",
          "fullName": "Nguyễn Minh An",
          "learnerCode": "HMA0001"
        }
      }
    ],
    "summary": {
      "totalAmount": 2500000,
      "pendingAmount": 2500000,
      "paidAmount": 0,
      "overdueAmount": 0
    }
  }
}
```

### 2. Payment APIs

#### 2.1 Tạo thanh toán

```bash
curl -X POST "https://your-domain.com/api/education/payments/create" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "fee_id": "uuid",
    "organization_id": "uuid",
    "return_url": "https://miniapp.zalo.me/payment-result"
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "paymentId": "uuid",
    "appTransId": "241201_123456_789012",
    "paymentUrl": "https://sbgateway.zalopay.vn/start?token=...",
    "amount": 2500000,
    "formattedAmount": "2.500.000 ₫",
    "fee": {
      "id": "uuid",
      "category": "tuition",
      "categoryText": "Học phí",
      "description": "Học phí tháng 12/2024"
    },
    "learner": {
      "id": "uuid",
      "name": "Nguyễn Minh An",
      "code": "HMA0001"
    }
  }
}
```

#### 2.2 Kiểm tra trạng thái thanh toán

```bash
curl -X GET "https://your-domain.com/api/education/payments/status?payment_id=uuid&organization_id=uuid" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "paymentId": "uuid",
    "appTransId": "241201_123456_789012",
    "zptransid": "************",
    "status": "succeeded",
    "statusText": "Thành công",
    "statusColor": "#10B981",
    "amount": 2500000,
    "formattedAmount": "2.500.000 ₫",
    "createdAt": "2024-12-01T10:00:00Z",
    "fee": {
      "id": "uuid",
      "categoryText": "Học phí"
    }
  }
}
```

### 3. Instructor APIs

#### 3.1 Lấy danh sách lớp dạy

```bash
curl -X GET "https://your-domain.com/api/education/instructor/classes?organization_id=uuid" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "programs": [
      {
        "id": "uuid",
        "name": "Lớp Chồi (3-4 tuổi)",
        "type": "regular_class",
        "typeText": "Lớp học thường",
        "capacity": 15,
        "currentEnrollments": 12,
        "enrollmentRate": 80,
        "location": "Phòng A1",
        "role": "instructor",
        "students": [
          {
            "learner": {
              "id": "uuid",
              "learnerCode": "HMA0001",
              "fullName": "Nguyễn Minh An",
              "nickname": "Bé An",
              "age": 4,
              "gender": "male"
            }
          }
        ],
        "totalStudents": 12
      }
    ],
    "summary": {
      "totalClasses": 1,
      "totalStudents": 12,
      "averageEnrollmentRate": 80
    }
  }
}
```

#### 3.2 Điểm danh học viên

```bash
curl -X POST "https://your-domain.com/api/education/instructor/attendance" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "program_id": "uuid",
    "organization_id": "uuid",
    "session_date": "2024-12-01",
    "session_time": "morning",
    "attendance_records": [
      {
        "learner_id": "uuid",
        "status": "present",
        "check_in_time": "07:45",
        "check_out_time": "16:30",
        "notes": ""
      }
    ]
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sessionId": "uuid",
    "recordsCreated": 12,
    "recordsUpdated": 0,
    "summary": {
      "present": 11,
      "absent": 1,
      "late": 0,
      "sick": 0,
      "excused": 0
    }
  }
}
```

#### 3.3 Tạo báo cáo tiến độ

```bash
curl -X POST "https://your-domain.com/api/education/instructor/reports" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "learner_id": "uuid",
    "program_id": "uuid",
    "organization_id": "uuid",
    "report_date": "2024-12-01",
    "report_type": "daily",
    "content": {
      "academic": {
        "subjects": ["Toán", "Tiếng Việt"],
        "performance": "good"
      },
      "behavior": {
        "social": "excellent",
        "discipline": "good"
      },
      "activities": ["Vẽ tranh", "Hát"],
      "notes": "Bé rất tích cực trong các hoạt động"
    },
    "overall_rating": "good",
    "media_urls": ["url1", "url2"]
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "reportId": "uuid",
    "learner": {
      "id": "uuid",
      "fullName": "Nguyễn Minh An",
      "learnerCode": "HMA0001"
    },
    "guardianNotified": true
  }
}
```

### 4. Events APIs

#### 4.1 Lấy danh sách sự kiện

```bash
curl -X GET "https://your-domain.com/api/education/guardian/events?organization_id=uuid&upcoming=true" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "events": [
      {
        "id": "uuid",
        "title": "Ngày hội thể thao",
        "description": "Ngày hội thể thao dành cho các bé",
        "eventType": "activity",
        "startDateTime": "2024-12-15T08:00:00Z",
        "endDateTime": "2024-12-15T11:00:00Z",
        "location": "Sân chơi trường",
        "targetAudience": "learners",
        "registrationRequired": true,
        "maxParticipants": 50,
        "currentParticipants": 25,
        "fee": 50000,
        "images": ["url1", "url2"],
        "isRegistered": false,
        "canRegister": true
      }
    ],
    "totalEvents": 1
  }
}
```

#### 4.2 Đăng ký sự kiện

```bash
curl -X POST "https://your-domain.com/api/education/guardian/events/register" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "event_id": "uuid",
    "organization_id": "uuid",
    "learner_ids": ["uuid1", "uuid2"],
    "notes": "Ghi chú đăng ký"
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "registrationIds": ["uuid1", "uuid2"],
    "registeredLearners": 2,
    "event": {
      "id": "uuid",
      "title": "Ngày hội thể thao"
    }
  }
}
```

### 5. Notifications APIs

#### 5.1 Lấy danh sách thông báo

```bash
curl -X GET "https://your-domain.com/api/education/notifications?organization_id=uuid&limit=20" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "notifications": [
      {
        "id": "uuid",
        "title": "Thanh toán thành công",
        "message": "Học phí tháng 12 đã được thanh toán thành công",
        "type": "payment_success",
        "isRead": false,
        "createdAt": "2024-12-01T10:00:00Z",
        "data": {
          "fee_id": "uuid",
          "learner_id": "uuid"
        }
      }
    ],
    "unreadCount": 5,
    "totalCount": 20
  }
}
```

#### 5.2 Đánh dấu đã đọc

```bash
curl -X PUT "https://your-domain.com/api/education/notifications/read" \
  -H "Authorization: Bearer {access_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "notification_ids": ["uuid1", "uuid2"],
    "organization_id": "uuid"
  }'
```

### 6. File Upload APIs

#### 6.1 Upload ảnh/video cho báo cáo

```bash
curl -X POST "https://your-domain.com/api/education/upload" \
  -H "Authorization: Bearer {access_token}" \
  -F "file=@image.jpg" \
  -F "organization_id=uuid" \
  -F "upload_type=report_media"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "fileId": "uuid",
    "fileName": "image.jpg",
    "fileUrl": "https://storage.com/path/to/image.jpg",
    "fileSize": 1024000,
    "mimeType": "image/jpeg"
  }
}
```

## Error Handling

Tất cả API endpoints trả về error theo format:

```json
{
  "success": false,
  "error": "Error message",
  "details": "Detailed error information"
}
```

### Common Error Codes

- `400` - Bad Request: Invalid parameters
- `401` - Unauthorized: Invalid or missing authentication
- `403` - Forbidden: Insufficient permissions
- `404` - Not Found: Resource not found
- `500` - Internal Server Error: Server error

## Data Types

### User Roles

- `guardian` - Phụ huynh/Người giám hộ
- `instructor` - Giáo viên
- `assistant` - Trợ giảng
- `education_admin` - Quản trị viên giáo dục
- `guest` - Khách (chưa được mời)

### Fee Status

- `pending` - Chưa thanh toán
- `processing` - Đang xử lý
- `paid` - Đã thanh toán
- `overdue` - Quá hạn
- `cancelled` - Đã hủy

### Payment Status

- `pending` - Chờ thanh toán
- `processing` - Đang xử lý
- `succeeded` - Thành công
- `failed` - Thất bại
- `cancelled` - Đã hủy

### Attendance Status

- `present` - Có mặt
- `absent` - Vắng mặt
- `late` - Đi muộn
- `sick` - Ốm
- `excused` - Có phép

## Mobile SDK Integration

### React Native Example

```javascript
import AsyncStorage from '@react-native-async-storage/async-storage';

class EducationAPI {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.accessToken = null;
  }

  async setAccessToken(token) {
    this.accessToken = token;
    await AsyncStorage.setItem('access_token', token);
  }

  async getAccessToken() {
    if (!this.accessToken) {
      this.accessToken = await AsyncStorage.getItem('access_token');
    }
    return this.accessToken;
  }

  async request(endpoint, options = {}) {
    const token = await this.getAccessToken();
    const response = await fetch(`${this.baseUrl}/api/education${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    const data = await response.json();
    if (!response.ok || !data.success) {
      throw new Error(data.error || 'API request failed');
    }
    return data.data;
  }

  // Guardian methods
  async getChildren(organizationId) {
    return this.request(`/guardian/children?organization_id=${organizationId}`);
  }

  async getAttendance(learnerId, organizationId, month) {
    return this.request(`/guardian/attendance?learner_id=${learnerId}&organization_id=${organizationId}&month=${month}`);
  }

  async getReports(learnerId, organizationId, limit = 10) {
    return this.request(`/guardian/reports?learner_id=${learnerId}&organization_id=${organizationId}&limit=${limit}`);
  }

  async getFees(organizationId, status = 'pending') {
    return this.request(`/guardian/fees?organization_id=${organizationId}&status=${status}`);
  }

  async getEvents(organizationId, upcoming = true) {
    return this.request(`/guardian/events?organization_id=${organizationId}&upcoming=${upcoming}`);
  }

  async registerForEvent(eventId, organizationId, learnerIds, notes = '') {
    return this.request('/guardian/events/register', {
      method: 'POST',
      body: JSON.stringify({
        event_id: eventId,
        organization_id: organizationId,
        learner_ids: learnerIds,
        notes
      }),
    });
  }

  // Payment methods
  async createPayment(feeId, organizationId, returnUrl) {
    return this.request('/payments/create', {
      method: 'POST',
      body: JSON.stringify({
        fee_id: feeId,
        organization_id: organizationId,
        return_url: returnUrl
      }),
    });
  }

  async checkPaymentStatus(paymentId, organizationId) {
    return this.request(`/payments/status?payment_id=${paymentId}&organization_id=${organizationId}`);
  }

  // Notification methods
  async getNotifications(organizationId, limit = 20) {
    return this.request(`/notifications?organization_id=${organizationId}&limit=${limit}`);
  }

  async markNotificationsAsRead(notificationIds, organizationId) {
    return this.request('/notifications', {
      method: 'PUT',
      body: JSON.stringify({
        notification_ids: notificationIds,
        organization_id: organizationId
      }),
    });
  }

  // File upload
  async uploadFile(file, organizationId, uploadType, learnerId = null, reportId = null) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('organization_id', organizationId);
    formData.append('upload_type', uploadType);
    if (learnerId) formData.append('learner_id', learnerId);
    if (reportId) formData.append('report_id', reportId);

    const token = await this.getAccessToken();
    const response = await fetch(`${this.baseUrl}/api/education/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    const data = await response.json();
    if (!response.ok || !data.success) {
      throw new Error(data.error || 'Upload failed');
    }
    return data.data;
  }
}

// Usage example
const api = new EducationAPI('https://your-domain.com');

// Initialize with Zalo auth
const initializeApp = async () => {
  try {
    // Get Zalo access token
    const zaloToken = await getZaloAccessToken();

    // Authenticate with your backend
    const authResponse = await fetch('/api/auth/zalo', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        access_token: zaloToken,
        theme_id: 'organization-theme-id'
      })
    });

    const authData = await authResponse.json();

    if (authData.success) {
      // Store authentication data
      await api.setAccessToken(authData.access_token);
      await AsyncStorage.setItem('user_data', JSON.stringify(authData));

      // Check user roles and permissions
      console.log('User roles:', authData.roles);
      console.log('Primary role:', authData.primary_role);
      console.log('Permissions:', authData.permissions);

      // Navigate based on user role
      if (authData.roles.includes('guardian')) {
        // Show guardian features
        const children = await api.getChildren(authData.organization.id);
        console.log('Children:', children);
      }

      if (authData.roles.includes('instructor')) {
        // Show instructor features
        const classes = await api.getClasses(authData.organization.id);
        console.log('Classes:', classes);
      }

      if (authData.permissions.can_view_dashboard) {
        // Show dashboard
        const dashboard = await api.getDashboard(authData.organization.id);
        console.log('Dashboard:', dashboard);
      }
    }

  } catch (error) {
    console.error('Initialization error:', error);
  }
};
```

## Integration Notes

### 1. Theme ID Setup

Mini App cần truyền `theme_id` trong Zalo auth để xác định organization:

```bash
curl -X POST "https://your-domain.com/api/auth/zalo" \
  -H "Content-Type: application/json" \
  -d '{
    "access_token": "zalo_access_token",
    "theme_id": "organization-theme-id"
  }'
```

**Enhanced Response with Roles & Permissions:**
```json
{
  "success": true,
  "user": {
    "id": "uuid",
    "name": "Nguyễn Văn A",
    "picture": "https://avatar.url",
    "phone": "**********"
  },
  "access_token": "jwt_token",
  "refresh_token": "refresh_token",
  "account_id": "uuid",
  "organization": {
    "id": "uuid",
    "name": "Trường Mầm Non ABC",
    "logo_url": "https://logo.url"
  },
  "roles": ["guardian", "instructor"],
  "primary_role": "guardian",
  "permissions": {
    "can_view_dashboard": true,
    "can_manage_learners": false,
    "can_take_attendance": true,
    "can_create_reports": true,
    "can_view_children": true,
    "can_pay_fees": true
  },
  "guardian_info": {
    "id": "uuid",
    "full_name": "Nguyễn Văn A"
  },
  "instructor_programs": [
    {
      "id": "uuid",
      "name": "Lớp Chồi A"
    }
  ]
}
```

### 2. Payment Flow

```javascript
// 1. Tạo payment
const payment = await createPayment(feeId);

// 2. Redirect đến ZaloPay
window.location.href = payment.paymentUrl;

// 3. Sau khi thanh toán, check status
const status = await checkPaymentStatus(payment.paymentId);
```

### 3. Real-time Updates

Sử dụng polling để check payment status:

```javascript
const pollPaymentStatus = async (paymentId) => {
  const interval = setInterval(async () => {
    const status = await checkPaymentStatus(paymentId);
    if (status.status !== 'pending') {
      clearInterval(interval);
      // Handle completion
    }
  }, 3000);
};
```

### 4. Error Handling

```javascript
const handleApiCall = async (apiCall) => {
  try {
    const response = await apiCall();
    if (!response.success) {
      throw new Error(response.error);
    }
    return response.data;
  } catch (error) {
    // Show user-friendly error message
    showError(error.message);
  }
};
```

## Environment Variables

Mini App backend cần các environment variables:

```env
# ZaloPay Configuration
ZALOPAY_APP_ID=your_app_id
ZALOPAY_KEY1=your_key1
ZALOPAY_KEY2=your_key2

# Site URL for callbacks
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

## Testing

Sử dụng ZaloPay Sandbox environment cho testing:

- Sandbox URL: `https://sb-openapi.zalopay.vn`
- Test cards và payment methods có sẵn trong ZaloPay documentation

## Support

Liên hệ team development để được hỗ trợ tích hợp API.
