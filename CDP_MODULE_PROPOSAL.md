# Customer Data Platform (CDP) Module - Đề xuất phát triển

## 🎯 Tổng quan đề xuất

### **Tại sao cần CDP Module?**
Dựa trên module Analytics hiện tại, chúng ta đã có:
- ✅ Event tracking system
- ✅ User behavior data
- ✅ Multi-source data collection
- ✅ Database infrastructure

**CDP sẽ nâng cấp lên tầm cao mới:**
- 🔄 **Unified Customer Profiles** - Hợp nhất data từ mọi touchpoint
- 🎯 **Real-time Segmentation** - Phân khúc khách hàng động
- 🤖 **Predictive Analytics** - Dự đoán hành vi khách hàng
- 📧 **Automated Marketing** - Tự động hóa chiến dịch
- 📊 **360° Customer View** - Cái nhìn toàn diện về khách hàng

## 🏗️ Kiến trúc CDP Module

### **1. Data Layer - Tầng dữ liệu**
```
📊 Analytics Events (existing)
    ↓
🔄 Data Unification Engine
    ↓
👤 Unified Customer Profiles
    ↓
🎯 Customer Segments
    ↓
📈 Predictive Models
```

### **2. Core Components**

#### **A. Customer Profile Engine**
```typescript
// packages/cdp/src/profile-engine/
├── customer-profile.ts          # Core profile model
├── profile-unification.ts       # Data merging logic
├── identity-resolution.ts       # Cross-device/session linking
├── profile-enrichment.ts        # External data enrichment
└── profile-scoring.ts           # Customer scoring algorithms
```

#### **B. Segmentation Engine**
```typescript
// packages/cdp/src/segmentation/
├── segment-builder.ts           # Dynamic segment creation
├── segment-rules.ts             # Rule engine
├── real-time-segments.ts        # Real-time evaluation
├── segment-analytics.ts         # Segment performance
└── segment-export.ts            # Export to marketing tools
```

#### **C. Journey Orchestration**
```typescript
// packages/cdp/src/journey/
├── journey-builder.ts           # Visual journey builder
├── trigger-engine.ts            # Event-based triggers
├── action-executor.ts           # Execute marketing actions
├── journey-analytics.ts         # Journey performance
└── a-b-testing.ts              # Journey optimization
```

#### **D. Predictive Analytics**
```typescript
// packages/cdp/src/predictive/
├── churn-prediction.ts          # Churn risk scoring
├── ltv-calculation.ts           # Lifetime value prediction
├── next-best-action.ts          # Recommendation engine
├── propensity-scoring.ts        # Purchase propensity
└── model-training.ts            # ML model management
```

## 📊 Database Schema Extension

### **Customer Profiles Table**
```sql
CREATE TABLE public.customer_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES public.accounts(id),
  
  -- Identity
  primary_email TEXT,
  primary_phone TEXT,
  external_ids JSONB DEFAULT '{}', -- {zalo_id, facebook_id, etc}
  
  -- Demographics
  first_name TEXT,
  last_name TEXT,
  gender TEXT,
  birth_date DATE,
  location JSONB, -- {city, country, coordinates}
  
  -- Behavioral Data
  first_seen_at TIMESTAMPTZ,
  last_seen_at TIMESTAMPTZ,
  total_sessions INTEGER DEFAULT 0,
  total_pageviews INTEGER DEFAULT 0,
  total_events INTEGER DEFAULT 0,
  
  -- Transactional Data
  total_orders INTEGER DEFAULT 0,
  total_revenue DECIMAL(15,2) DEFAULT 0,
  average_order_value DECIMAL(15,2) DEFAULT 0,
  last_order_at TIMESTAMPTZ,
  
  -- Engagement Metrics
  email_engagement_score DECIMAL(5,2) DEFAULT 0,
  website_engagement_score DECIMAL(5,2) DEFAULT 0,
  social_engagement_score DECIMAL(5,2) DEFAULT 0,
  
  -- Predictive Scores
  churn_risk_score DECIMAL(5,2) DEFAULT 0,
  lifetime_value_score DECIMAL(15,2) DEFAULT 0,
  purchase_propensity_score DECIMAL(5,2) DEFAULT 0,
  
  -- Preferences
  communication_preferences JSONB DEFAULT '{}',
  product_preferences JSONB DEFAULT '{}',
  channel_preferences JSONB DEFAULT '{}',
  
  -- Metadata
  tags TEXT[] DEFAULT '{}',
  custom_attributes JSONB DEFAULT '{}',
  computed_at TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

### **Customer Segments Table**
```sql
CREATE TABLE public.customer_segments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES public.accounts(id),
  
  name TEXT NOT NULL,
  description TEXT,
  
  -- Segment Definition
  rules JSONB NOT NULL, -- Segment criteria
  is_dynamic BOOLEAN DEFAULT true,
  
  -- Metadata
  customer_count INTEGER DEFAULT 0,
  last_calculated_at TIMESTAMPTZ,
  
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

### **Customer Journey Table**
```sql
CREATE TABLE public.customer_journeys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES public.accounts(id),
  
  name TEXT NOT NULL,
  description TEXT,
  
  -- Journey Definition
  trigger_rules JSONB NOT NULL,
  journey_steps JSONB NOT NULL,
  
  -- Status
  is_active BOOLEAN DEFAULT false,
  
  -- Analytics
  total_entries INTEGER DEFAULT 0,
  total_completions INTEGER DEFAULT 0,
  conversion_rate DECIMAL(5,2) DEFAULT 0,
  
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

## 🔧 Core Features Implementation

### **1. Real-time Profile Updates**
```typescript
// packages/cdp/src/profile-engine/real-time-updater.ts
export class RealTimeProfileUpdater {
  async updateFromEvent(event: AnalyticsEvent) {
    const profile = await this.getOrCreateProfile(event.visitor_id, event.user_id);
    
    // Update behavioral metrics
    await this.updateBehavioralMetrics(profile, event);
    
    // Update engagement scores
    await this.updateEngagementScores(profile, event);
    
    // Recalculate predictive scores
    await this.updatePredictiveScores(profile);
    
    // Check segment membership
    await this.evaluateSegmentMembership(profile);
    
    // Trigger journey events
    await this.triggerJourneyEvents(profile, event);
  }

  private async updateBehavioralMetrics(profile: CustomerProfile, event: AnalyticsEvent) {
    const updates: Partial<CustomerProfile> = {};
    
    switch (event.event_type) {
      case 'pageview':
        updates.total_pageviews = (profile.total_pageviews || 0) + 1;
        updates.last_seen_at = new Date();
        break;
        
      case 'purchase':
        const amount = event.event_data.amount || 0;
        updates.total_orders = (profile.total_orders || 0) + 1;
        updates.total_revenue = (profile.total_revenue || 0) + amount;
        updates.average_order_value = updates.total_revenue / updates.total_orders;
        updates.last_order_at = new Date();
        break;
    }
    
    await this.updateProfile(profile.id, updates);
  }
}
```

### **2. Dynamic Segmentation**
```typescript
// packages/cdp/src/segmentation/segment-evaluator.ts
export class SegmentEvaluator {
  async evaluateCustomerForSegments(customerId: string) {
    const customer = await this.getCustomerProfile(customerId);
    const segments = await this.getActiveSegments(customer.account_id);
    
    for (const segment of segments) {
      const isMatch = await this.evaluateSegmentRules(customer, segment.rules);
      await this.updateSegmentMembership(customerId, segment.id, isMatch);
    }
  }

  private async evaluateSegmentRules(customer: CustomerProfile, rules: SegmentRules): Promise<boolean> {
    // Rule engine implementation
    return this.ruleEngine.evaluate(customer, rules);
  }
}

// Example segment rules
const highValueCustomerRules = {
  operator: 'AND',
  conditions: [
    {
      field: 'total_revenue',
      operator: 'greater_than',
      value: 1000000 // 1M VND
    },
    {
      field: 'total_orders',
      operator: 'greater_than',
      value: 3
    },
    {
      field: 'last_order_at',
      operator: 'within_days',
      value: 90
    }
  ]
};
```

### **3. Predictive Analytics**
```typescript
// packages/cdp/src/predictive/churn-predictor.ts
export class ChurnPredictor {
  async calculateChurnRisk(customerId: string): Promise<number> {
    const customer = await this.getCustomerProfile(customerId);
    const features = this.extractFeatures(customer);
    
    // Simple rule-based model (can be replaced with ML model)
    let churnScore = 0;
    
    // Recency factor
    const daysSinceLastOrder = this.daysSince(customer.last_order_at);
    if (daysSinceLastOrder > 90) churnScore += 0.3;
    if (daysSinceLastOrder > 180) churnScore += 0.3;
    
    // Frequency factor
    if (customer.total_orders < 2) churnScore += 0.2;
    
    // Engagement factor
    if (customer.website_engagement_score < 0.3) churnScore += 0.2;
    
    return Math.min(churnScore, 1.0);
  }

  async calculateLifetimeValue(customerId: string): Promise<number> {
    const customer = await this.getCustomerProfile(customerId);
    
    // Simple LTV calculation: AOV * Purchase Frequency * Customer Lifespan
    const avgOrderValue = customer.average_order_value || 0;
    const orderFrequency = this.calculateOrderFrequency(customer);
    const predictedLifespan = this.predictCustomerLifespan(customer);
    
    return avgOrderValue * orderFrequency * predictedLifespan;
  }
}
```

## 🎯 Marketing Automation Features

### **1. Journey Builder**
```typescript
// packages/cdp/src/journey/journey-executor.ts
export class JourneyExecutor {
  async executeJourneyStep(customerId: string, journeyId: string, stepId: string) {
    const step = await this.getJourneyStep(journeyId, stepId);
    const customer = await this.getCustomerProfile(customerId);
    
    switch (step.type) {
      case 'email':
        await this.sendEmail(customer, step.config);
        break;
        
      case 'sms':
        await this.sendSMS(customer, step.config);
        break;
        
      case 'zns':
        await this.sendZNS(customer, step.config);
        break;
        
      case 'wait':
        await this.scheduleNextStep(customerId, journeyId, step.next_step, step.delay);
        break;
        
      case 'condition':
        const nextStep = await this.evaluateCondition(customer, step.condition);
        await this.executeJourneyStep(customerId, journeyId, nextStep);
        break;
    }
  }

  private async sendZNS(customer: CustomerProfile, config: ZNSConfig) {
    // Integration with existing ZNS system
    await sendZnsMessage(
      config.oa_config_id,
      customer.primary_phone,
      config.template_id,
      this.personalizeMessage(config.parameters, customer)
    );
  }
}
```

### **2. Personalization Engine**
```typescript
// packages/cdp/src/personalization/content-personalizer.ts
export class ContentPersonalizer {
  async personalizeContent(customerId: string, contentType: string): Promise<PersonalizedContent> {
    const customer = await this.getCustomerProfile(customerId);
    const preferences = customer.product_preferences;
    
    switch (contentType) {
      case 'product_recommendations':
        return this.getProductRecommendations(customer);
        
      case 'email_content':
        return this.personalizeEmailContent(customer);
        
      case 'website_banner':
        return this.personalizeWebsiteBanner(customer);
    }
  }

  private async getProductRecommendations(customer: CustomerProfile): Promise<Product[]> {
    // Collaborative filtering + content-based recommendations
    const behaviorBasedRecs = await this.getBehaviorBasedRecommendations(customer);
    const segmentBasedRecs = await this.getSegmentBasedRecommendations(customer);
    
    return this.mergeRecommendations(behaviorBasedRecs, segmentBasedRecs);
  }
}
```

## 📱 UI Components

### **1. Customer Profile Dashboard**
```typescript
// apps/web/app/home/<USER>/cdp/customers/[id]/page.tsx
export default function CustomerProfilePage({ params }: { params: { id: string } }) {
  const { data: customer } = useCustomerProfile(params.id);
  
  return (
    <div className="space-y-6">
      {/* Customer Overview */}
      <CustomerOverviewCard customer={customer} />
      
      {/* Engagement Timeline */}
      <CustomerTimelineCard customerId={params.id} />
      
      {/* Segments */}
      <CustomerSegmentsCard customer={customer} />
      
      {/* Predictive Scores */}
      <PredictiveScoresCard customer={customer} />
      
      {/* Journey Status */}
      <CustomerJourneysCard customerId={params.id} />
    </div>
  );
}
```

### **2. Segment Builder**
```typescript
// apps/web/app/home/<USER>/cdp/segments/builder/page.tsx
export default function SegmentBuilderPage() {
  return (
    <div className="flex h-screen">
      {/* Rule Builder */}
      <div className="w-1/2 p-6">
        <SegmentRuleBuilder />
      </div>
      
      {/* Preview */}
      <div className="w-1/2 p-6 border-l">
        <SegmentPreview />
      </div>
    </div>
  );
}
```

## 🚀 Implementation Roadmap

### **Phase 1: Foundation (4-6 weeks)**
- [ ] Customer Profile Engine
- [ ] Basic Segmentation
- [ ] Profile UI Dashboard
- [ ] Real-time profile updates from analytics events

### **Phase 2: Intelligence (6-8 weeks)**
- [ ] Predictive Analytics (Churn, LTV)
- [ ] Advanced Segmentation
- [ ] Recommendation Engine
- [ ] A/B Testing Framework

### **Phase 3: Automation (8-10 weeks)**
- [ ] Journey Builder
- [ ] Marketing Automation
- [ ] Personalization Engine
- [ ] Campaign Management

### **Phase 4: Advanced Features (6-8 weeks)**
- [ ] Machine Learning Models
- [ ] External Data Integration
- [ ] Advanced Analytics Dashboard
- [ ] API for third-party integrations

## 💰 Business Value

### **Immediate Benefits:**
- 📊 **360° Customer View**: Hiểu khách hàng toàn diện
- 🎯 **Targeted Marketing**: Tăng conversion rate 20-30%
- 🤖 **Automation**: Giảm 60% thời gian manual marketing
- 📈 **Revenue Growth**: Tăng 15-25% revenue từ personalization

### **Long-term Benefits:**
- 🔮 **Predictive Insights**: Dự đoán churn, LTV
- 🎨 **Hyper-personalization**: Cá nhân hóa từng touchpoint
- 📊 **Data-driven Decisions**: Quyết định dựa trên data
- 🚀 **Competitive Advantage**: Dẫn đầu về customer experience
