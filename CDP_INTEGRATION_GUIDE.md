# CDP Module - Integration & Extension Guide

## 🔌 Integration với Analytics Module hiện tại

### **1. Event Bridge Pattern**

```typescript
// packages/cdp/src/integrations/analytics-bridge.ts
export class AnalyticsCDPBridge {
  constructor(
    private cdpEventProcessor: CDPEventProcessor,
    private analyticsService: AnalyticsService
  ) {
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // Listen to analytics events and forward to CDP
    this.analyticsService.on('event.tracked', async (event: AnalyticsEvent) => {
      await this.cdpEventProcessor.processAnalyticsEvent(event);
    });

    // Listen to CDP events and optionally track in analytics
    this.cdpEventProcessor.on('profile.updated', async (event: ProfileUpdateEvent) => {
      await this.analyticsService.trackEvent('cdp.profile.updated', {
        customer_id: event.customerId,
        changes: event.changes
      });
    });
  }

  async migrateExistingAnalyticsData(): Promise<void> {
    // Migrate existing analytics events to build customer profiles
    const events = await this.getHistoricalAnalyticsEvents();
    
    for (const event of events) {
      await this.cdpEventProcessor.processAnalyticsEvent(event);
    }
  }
}
```

### **2. Shared Database Schema**

```sql
-- Extend existing analytics_events table
ALTER TABLE public.analytics_events 
ADD COLUMN customer_profile_id UUID REFERENCES public.customer_profiles(id);

-- Create index for performance
CREATE INDEX idx_analytics_events_customer_profile 
ON public.analytics_events(customer_profile_id);

-- Update existing events with customer profile IDs
UPDATE public.analytics_events 
SET customer_profile_id = cp.id
FROM public.customer_profiles cp
WHERE analytics_events.user_id = cp.primary_user_id
   OR analytics_events.visitor_id = ANY(cp.visitor_ids);
```

### **3. Unified API Layer**

```typescript
// apps/web/app/api/cdp/events/route.ts
import { enhanceRouteHandler } from '@kit/next/routes';
import { AnalyticsCDPBridge } from '@kit/cdp/integrations';

export const POST = enhanceRouteHandler(
  async ({ request, supabase, user }) => {
    const eventData = await request.json();
    
    // Process through both analytics and CDP
    await Promise.all([
      analytics.trackEvent(eventData.eventType, eventData),
      cdpBridge.processEvent(eventData)
    ]);
    
    return NextResponse.json({ success: true });
  },
  { auth: true }
);
```

## 🔗 External System Integrations

### **1. CRM Integration (HubSpot, Salesforce)**

```typescript
// packages/cdp/src/integrations/crm-integration.ts
export class CRMIntegration {
  constructor(
    private crmClient: CRMClient,
    private profileService: ProfileService
  ) {}

  async syncCustomerData(): Promise<void> {
    const crmContacts = await this.crmClient.getContacts();
    
    for (const contact of crmContacts) {
      await this.profileService.enrichProfile(
        contact.email,
        'crm',
        {
          company: contact.company,
          job_title: contact.jobTitle,
          lead_score: contact.leadScore,
          deal_stage: contact.dealStage
        }
      );
    }
  }

  async pushSegmentToCRM(segmentId: string): Promise<void> {
    const segment = await this.segmentService.getSegment(segmentId);
    const customers = await this.segmentService.getSegmentMembers(segmentId);
    
    // Create list in CRM
    const crmList = await this.crmClient.createList({
      name: segment.name,
      description: segment.description
    });
    
    // Add contacts to list
    for (const customer of customers) {
      await this.crmClient.addContactToList(crmList.id, customer.primary_email);
    }
  }
}
```

### **2. Email Marketing Integration (Mailchimp, SendGrid)**

```typescript
// packages/cdp/src/integrations/email-integration.ts
export class EmailMarketingIntegration {
  constructor(
    private emailClient: EmailClient,
    private segmentService: SegmentationService
  ) {}

  async syncSegmentToEmailPlatform(segmentId: string): Promise<void> {
    const segment = await this.segmentService.getSegment(segmentId);
    const customers = await this.segmentService.getSegmentMembers(segmentId);
    
    // Create audience in email platform
    const audience = await this.emailClient.createAudience({
      name: segment.name,
      description: segment.description
    });
    
    // Add subscribers
    const subscribers = customers.map(customer => ({
      email: customer.primary_email,
      merge_fields: {
        FNAME: customer.first_name,
        LNAME: customer.last_name,
        LTV: customer.lifetime_value,
        CHURN_RISK: customer.churn_risk_score
      }
    }));
    
    await this.emailClient.addSubscribers(audience.id, subscribers);
  }

  async trackEmailEngagement(emailId: string, customerId: string, action: string): Promise<void> {
    await this.profileService.updateEngagementScore(customerId, 'email', action);
    
    // Update customer journey if applicable
    await this.journeyService.processCustomerEvent(customerId, {
      type: `email.${action}`,
      data: { email_id: emailId }
    });
  }
}
```

### **3. Social Media Integration**

```typescript
// packages/cdp/src/integrations/social-integration.ts
export class SocialMediaIntegration {
  async enrichWithSocialData(customerId: string, socialProfiles: SocialProfile[]): Promise<void> {
    const socialData = {
      social_profiles: socialProfiles,
      social_engagement_score: this.calculateSocialEngagement(socialProfiles),
      interests: this.extractInterests(socialProfiles),
      demographics: this.extractDemographics(socialProfiles)
    };
    
    await this.profileService.enrichProfile(customerId, 'social', socialData);
  }

  async createLookalikeAudience(segmentId: string, platform: 'facebook' | 'google'): Promise<void> {
    const customers = await this.segmentService.getSegmentMembers(segmentId);
    const emails = customers.map(c => c.primary_email).filter(Boolean);
    
    switch (platform) {
      case 'facebook':
        await this.facebookAdsAPI.createLookalikeAudience(emails);
        break;
      case 'google':
        await this.googleAdsAPI.createSimilarAudience(emails);
        break;
    }
  }
}
```

## 🛠️ Extension Points

### **1. Custom Data Sources**

```typescript
// packages/cdp/src/extensions/data-source-extension.ts
export abstract class DataSourceExtension {
  abstract name: string;
  abstract description: string;
  
  abstract async connect(config: any): Promise<void>;
  abstract async fetchData(): Promise<any[]>;
  abstract async transformData(rawData: any[]): Promise<CustomerData[]>;
  
  async sync(): Promise<void> {
    const rawData = await this.fetchData();
    const transformedData = await this.transformData(rawData);
    
    for (const customerData of transformedData) {
      await this.profileService.enrichProfile(
        customerData.identifier,
        this.name,
        customerData.data
      );
    }
  }
}

// Example: Shopify integration
export class ShopifyDataSource extends DataSourceExtension {
  name = 'shopify';
  description = 'Shopify e-commerce data';
  
  async connect(config: ShopifyConfig): Promise<void> {
    this.shopifyClient = new ShopifyClient(config);
  }
  
  async fetchData(): Promise<ShopifyCustomer[]> {
    return this.shopifyClient.getCustomers();
  }
  
  async transformData(customers: ShopifyCustomer[]): Promise<CustomerData[]> {
    return customers.map(customer => ({
      identifier: customer.email,
      data: {
        total_spent: customer.total_spent,
        orders_count: customer.orders_count,
        tags: customer.tags,
        created_at: customer.created_at
      }
    }));
  }
}
```

### **2. Custom Scoring Models**

```typescript
// packages/cdp/src/extensions/scoring-model-extension.ts
export abstract class ScoringModelExtension {
  abstract name: string;
  abstract description: string;
  
  abstract async calculateScore(customer: CustomerProfile): Promise<number>;
  abstract async getFeatures(customer: CustomerProfile): Promise<Record<string, number>>;
  
  async updateCustomerScore(customerId: string): Promise<void> {
    const customer = await this.profileService.getProfile(customerId);
    const score = await this.calculateScore(customer);
    
    await this.profileService.updateProfile(customerId, {
      [`${this.name}_score`]: score
    });
  }
}

// Example: Custom engagement scoring
export class EngagementScoringModel extends ScoringModelExtension {
  name = 'engagement';
  description = 'Custom engagement scoring based on multiple touchpoints';
  
  async calculateScore(customer: CustomerProfile): Promise<number> {
    const features = await this.getFeatures(customer);
    
    // Weighted scoring algorithm
    const weights = {
      email_opens: 0.2,
      website_visits: 0.3,
      purchase_frequency: 0.3,
      social_interactions: 0.2
    };
    
    let score = 0;
    for (const [feature, value] of Object.entries(features)) {
      score += (weights[feature] || 0) * value;
    }
    
    return Math.min(score, 1.0);
  }
  
  async getFeatures(customer: CustomerProfile): Promise<Record<string, number>> {
    return {
      email_opens: this.normalizeEmailOpens(customer.email_engagement_score),
      website_visits: this.normalizeWebsiteVisits(customer.total_sessions),
      purchase_frequency: this.normalizePurchaseFrequency(customer.total_orders),
      social_interactions: this.normalizeSocialInteractions(customer.social_engagement_score)
    };
  }
}
```

### **3. Custom Journey Actions**

```typescript
// packages/cdp/src/extensions/journey-action-extension.ts
export abstract class JourneyActionExtension {
  abstract type: string;
  abstract name: string;
  abstract description: string;
  
  abstract async execute(customer: CustomerProfile, config: any): Promise<void>;
  abstract validateConfig(config: any): boolean;
  
  async register(): Promise<void> {
    await this.journeyService.registerActionType(this.type, this);
  }
}

// Example: Custom Slack notification action
export class SlackNotificationAction extends JourneyActionExtension {
  type = 'slack_notification';
  name = 'Slack Notification';
  description = 'Send notification to Slack channel';
  
  async execute(customer: CustomerProfile, config: SlackConfig): Promise<void> {
    const message = this.personalizeMessage(config.message, customer);
    
    await this.slackClient.sendMessage({
      channel: config.channel,
      text: message,
      attachments: [{
        color: 'good',
        fields: [
          { title: 'Customer', value: customer.primary_email, short: true },
          { title: 'LTV', value: `$${customer.lifetime_value}`, short: true }
        ]
      }]
    });
  }
  
  validateConfig(config: SlackConfig): boolean {
    return !!(config.channel && config.message);
  }
}
```

## 📦 Package Structure for Modularity

### **Core Package**
```
packages/cdp-core/
├── src/
│   ├── types/           # Core type definitions
│   ├── interfaces/      # Service interfaces
│   ├── base-classes/    # Abstract base classes
│   └── utils/          # Shared utilities
└── package.json
```

### **Service Packages**
```
packages/cdp-profile/     # Profile management
packages/cdp-segmentation/ # Segmentation engine
packages/cdp-journey/     # Journey orchestration
packages/cdp-predictive/  # Predictive analytics
packages/cdp-activation/  # Marketing activation
```

### **Integration Packages**
```
packages/cdp-integrations/
├── crm/                 # CRM integrations
├── email/              # Email platform integrations
├── social/             # Social media integrations
├── analytics/          # Analytics platform integrations
└── custom/             # Custom integration framework
```

### **Extension Packages**
```
packages/cdp-extensions/
├── data-sources/       # Custom data source extensions
├── scoring-models/     # Custom scoring models
├── journey-actions/    # Custom journey actions
└── ui-components/      # Custom UI components
```

## 🔧 Configuration Management

### **Environment-based Configuration**
```typescript
// packages/cdp/src/config/cdp-config.ts
export interface CDPConfig {
  // Core settings
  realTimeProcessing: boolean;
  batchProcessingInterval: number;
  
  // Data retention
  profileRetentionDays: number;
  eventRetentionDays: number;
  
  // Performance settings
  cacheEnabled: boolean;
  cacheTTL: number;
  maxConcurrentProcessing: number;
  
  // ML settings
  enablePredictiveAnalytics: boolean;
  modelUpdateFrequency: number;
  
  // Integration settings
  enabledIntegrations: string[];
  integrationConfigs: Record<string, any>;
}

export const getCDPConfig = (): CDPConfig => {
  return {
    realTimeProcessing: process.env.CDP_REAL_TIME_PROCESSING === 'true',
    batchProcessingInterval: parseInt(process.env.CDP_BATCH_INTERVAL || '3600'),
    profileRetentionDays: parseInt(process.env.CDP_PROFILE_RETENTION || '2555'), // 7 years
    eventRetentionDays: parseInt(process.env.CDP_EVENT_RETENTION || '365'),
    cacheEnabled: process.env.CDP_CACHE_ENABLED === 'true',
    cacheTTL: parseInt(process.env.CDP_CACHE_TTL || '3600'),
    maxConcurrentProcessing: parseInt(process.env.CDP_MAX_CONCURRENT || '10'),
    enablePredictiveAnalytics: process.env.CDP_PREDICTIVE_ENABLED === 'true',
    modelUpdateFrequency: parseInt(process.env.CDP_MODEL_UPDATE_FREQ || '86400'),
    enabledIntegrations: (process.env.CDP_ENABLED_INTEGRATIONS || '').split(','),
    integrationConfigs: JSON.parse(process.env.CDP_INTEGRATION_CONFIGS || '{}')
  };
};
```

### **Feature Flags**
```typescript
// packages/cdp/src/config/feature-flags.ts
export class CDPFeatureFlags {
  static isEnabled(feature: string, accountId?: string): boolean {
    // Check account-specific feature flags
    if (accountId) {
      const accountFlags = this.getAccountFeatureFlags(accountId);
      if (accountFlags[feature] !== undefined) {
        return accountFlags[feature];
      }
    }
    
    // Check global feature flags
    const globalFlags = this.getGlobalFeatureFlags();
    return globalFlags[feature] || false;
  }
  
  static async enableFeature(feature: string, accountId?: string): Promise<void> {
    if (accountId) {
      await this.setAccountFeatureFlag(accountId, feature, true);
    } else {
      await this.setGlobalFeatureFlag(feature, true);
    }
  }
}

// Usage in services
if (CDPFeatureFlags.isEnabled('predictive_analytics', customer.account_id)) {
  await this.predictiveService.updateScores(customer.id);
}
```
