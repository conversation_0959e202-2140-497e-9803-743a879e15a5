#!/bin/bash

# <PERSON>ript to set up a virtual environment and run the image optimization script

# Exit on error
set -e

echo "Setting up virtual environment for image optimization..."

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    python3 -m venv venv
    echo "Virtual environment created."
else
    echo "Using existing virtual environment."
fi

# Activate virtual environment
source venv/bin/activate

# Install required packages
echo "Installing required packages..."
pip install pillow

# Run the optimization script
echo "Running image optimization script..."
python optimize_images.py

# Deactivate virtual environment
deactivate

echo "Image optimization complete!"
