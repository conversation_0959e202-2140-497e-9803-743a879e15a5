# Hướng dẫn sử dụng API Flash Sales

Tài liệu này mô tả cách sử dụng API Flash Sales để quản lý và truy vấn thông tin về các chương trình khuyến mãi flash sale trong Zalo Mini App.

## Tổng quan

API Flash Sales cho phép bạn lấy danh sách các chương trình flash sale đang hoạt động, xem chi tiết một flash sale cụ thể và danh sách sản phẩm trong flash sale. Các flash sale được sử dụng để giảm giá sản phẩm trong một khoảng thời gian nhất định.

## Endpoints

### Lấy danh sách flash sales

```
GET /api/flash-sales
```

### Lấy chi tiết flash sale

```
GET /api/flash-sales/:id
```

### Lấy danh sách flash sales đang hoạt động

```
GET /api/flash-sales/active
```

## Xác thực

API yêu cầu xác thực bằng JWT token. Token này cần được gửi trong header `Authorization`.

```
Authorization: Bearer <your_jwt_token>
```

## Tham số

### Lấy danh sách flash sales

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| page | number | Không | Số trang (mặc định: 1) |
| limit | number | Không | Số lượng kết quả trên mỗi trang (mặc định: 10) |
| search | string | Không | Từ khóa tìm kiếm theo tên flash sale |
| status | string | Không | Lọc theo trạng thái (`active`, `upcoming`, `expired`) |

### Lấy chi tiết flash sale

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| id | string | Có | ID của flash sale |

## Phản hồi

### Thành công - Danh sách flash sales

```json
{
  "success": true,
  "data": {
    "flash_sales": [
      {
        "id": "flash-sale-123",
        "name": "Flash Sale Hè 2023",
        "description": "Giảm giá sốc các sản phẩm mùa hè",
        "discount_percentage": 20,
        "start_date": "2023-06-01T00:00:00Z",
        "end_date": "2023-06-07T23:59:59Z",
        "status": "active",
        "created_at": "2023-05-15T10:00:00Z",
        "updated_at": "2023-05-15T10:00:00Z"
      },
      {
        "id": "flash-sale-456",
        "name": "Flash Sale Cuối Tuần",
        "description": "Giảm giá cuối tuần cho tất cả sản phẩm",
        "discount_percentage": 15,
        "start_date": "2023-06-10T00:00:00Z",
        "end_date": "2023-06-11T23:59:59Z",
        "status": "upcoming",
        "created_at": "2023-05-20T14:30:00Z",
        "updated_at": "2023-05-20T14:30:00Z"
      }
    ],
    "pagination": {
      "total": 10,
      "page": 1,
      "limit": 10,
      "total_pages": 1
    }
  }
}
```

### Thành công - Chi tiết flash sale

```json
{
  "success": true,
  "data": {
    "flash_sale": {
      "id": "flash-sale-123",
      "name": "Flash Sale Hè 2023",
      "description": "Giảm giá sốc các sản phẩm mùa hè",
      "discount_percentage": 20,
      "start_date": "2023-06-01T00:00:00Z",
      "end_date": "2023-06-07T23:59:59Z",
      "status": "active",
      "created_at": "2023-05-15T10:00:00Z",
      "updated_at": "2023-05-15T10:00:00Z",
      "products": [
        {
          "id": "product-789",
          "name": "Áo thun mùa hè",
          "price": 200000,
          "final_price": 160000,
          "image_url": "/images/products/product-789.jpg",
          "discount_percentage": 20
        },
        {
          "id": "product-012",
          "name": "Quần short",
          "price": 300000,
          "final_price": 240000,
          "image_url": "/images/products/product-012.jpg",
          "discount_percentage": 20
        }
      ]
    }
  }
}
```

### Thành công - Flash sales đang hoạt động

```json
{
  "success": true,
  "data": {
    "flash_sales": [
      {
        "id": "flash-sale-123",
        "name": "Flash Sale Hè 2023",
        "description": "Giảm giá sốc các sản phẩm mùa hè",
        "discount_percentage": 20,
        "start_date": "2023-06-01T00:00:00Z",
        "end_date": "2023-06-07T23:59:59Z",
        "status": "active",
        "created_at": "2023-05-15T10:00:00Z",
        "updated_at": "2023-05-15T10:00:00Z"
      },
      {
        "id": "flash-sale-789",
        "name": "Flash Sale Ngày Lễ",
        "description": "Giảm giá đặc biệt ngày lễ",
        "discount_percentage": 25,
        "start_date": "2023-06-02T00:00:00Z",
        "end_date": "2023-06-03T23:59:59Z",
        "status": "active",
        "created_at": "2023-05-25T09:15:00Z",
        "updated_at": "2023-05-25T09:15:00Z"
      }
    ]
  }
}
```

### Lỗi

```json
{
  "success": false,
  "error": "Mô tả lỗi",
  "details": "Chi tiết lỗi (nếu có)"
}
```

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| 400 | Yêu cầu không hợp lệ |
| 401 | Không được phép truy cập |
| 404 | Không tìm thấy flash sale |
| 500 | Lỗi server |

## Ví dụ

### Lấy danh sách flash sales

```bash
curl -X GET "https://your-domain.com/api/flash-sales?page=1&limit=10&status=active" \
  -H "Authorization: Bearer your_jwt_token"
```

### Lấy chi tiết flash sale

```bash
curl -X GET "https://your-domain.com/api/flash-sales/flash-sale-123" \
  -H "Authorization: Bearer your_jwt_token"
```

### Lấy danh sách flash sales đang hoạt động

```bash
curl -X GET "https://your-domain.com/api/flash-sales/active" \
  -H "Authorization: Bearer your_jwt_token"
```

## Lưu ý

1. Flash sale có 3 trạng thái: `active` (đang diễn ra), `upcoming` (sắp diễn ra) và `expired` (đã kết thúc).
2. Giá cuối cùng (final_price) của sản phẩm được tính bằng cách áp dụng tỷ lệ giảm giá (discount_percentage) vào giá gốc (price).
3. Chỉ những flash sale có trạng thái `active` mới được áp dụng khi tính giá sản phẩm.

## Tích hợp với Zalo Mini App

Để tích hợp API Flash Sales vào Zalo Mini App, bạn cần:

1. Lấy JWT token từ quá trình xác thực
2. Gọi API để lấy danh sách flash sales đang hoạt động
3. Hiển thị thông tin flash sale và sản phẩm trong flash sale cho người dùng

### Ví dụ tích hợp (JavaScript)

```javascript
// Hàm lấy danh sách flash sales đang hoạt động
async function getActiveFlashSales() {
  try {
    const response = await fetch('https://your-domain.com/api/flash-sales/active', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      }
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Lỗi khi lấy danh sách flash sales');
    }
    
    return data.data.flash_sales;
  } catch (error) {
    console.error('Error fetching active flash sales:', error);
    return [];
  }
}

// Hàm lấy chi tiết flash sale
async function getFlashSaleDetails(flashSaleId) {
  try {
    const response = await fetch(`https://your-domain.com/api/flash-sales/${flashSaleId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      }
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Lỗi khi lấy chi tiết flash sale');
    }
    
    return data.data.flash_sale;
  } catch (error) {
    console.error(`Error fetching flash sale details for ID ${flashSaleId}:`, error);
    return null;
  }
}

// Ví dụ sử dụng
// Hiển thị danh sách flash sales đang hoạt động
getActiveFlashSales()
  .then(flashSales => {
    if (flashSales.length === 0) {
      console.log('Không có flash sale nào đang diễn ra');
      return;
    }
    
    console.log('Flash sales đang diễn ra:');
    flashSales.forEach(flashSale => {
      console.log(`- ${flashSale.name}: Giảm ${flashSale.discount_percentage}% đến ${new Date(flashSale.end_date).toLocaleString()}`);
    });
    
    // Lấy chi tiết flash sale đầu tiên
    return getFlashSaleDetails(flashSales[0].id);
  })
  .then(flashSaleDetails => {
    if (!flashSaleDetails) return;
    
    console.log('Chi tiết flash sale:');
    console.log(`Tên: ${flashSaleDetails.name}`);
    console.log(`Mô tả: ${flashSaleDetails.description}`);
    console.log(`Giảm giá: ${flashSaleDetails.discount_percentage}%`);
    console.log('Sản phẩm trong flash sale:');
    
    flashSaleDetails.products.forEach(product => {
      console.log(`- ${product.name}: ${product.price.toLocaleString()} VND -> ${product.final_price.toLocaleString()} VND (Giảm ${product.discount_percentage}%)`);
    });
  })
  .catch(error => {
    console.error('Lỗi:', error);
  });
```
