'use server';

import { revalidatePath } from 'next/cache';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Database } from '~/lib/database.types';
import { getLogger } from '@kit/shared/logger';

import { IPOSCredentials, createIPOSConnector, getIPOSCredentialsFromIntegration, getIPOSProductFields, getIPOSOrderFields, getIPOSCustomerFields, getIPOSCategoryFields, getIPOSBranchFields, getIPOSVoucherFields } from '../../../_lib/server/ipos-connector-server';

/**
 * <PERSON><PERSON><PERSON> danh sách trường từ iPOS và hệ thống
 */
export async function getIPOSFields(integrationId: string, resourceType: 'products' | 'orders' | 'customers' | 'categories' | 'branches' | 'vouchers') {
  const supabase = getSupabaseServerClient<Database>();
  const logger = await getLogger();

  try {
    logger.info({ integrationId, resourceType }, 'Getting iPOS fields');

    // Lấy credentials từ integration
    const credentials = await getIPOSCredentialsFromIntegration(integrationId);

    // Lấy danh sách trường từ iPOS
    let sourceFields: string[] = [];
    switch (resourceType) {
      case 'products':
        sourceFields = await getIPOSProductFields(credentials);
        break;
      case 'orders':
        sourceFields = await getIPOSOrderFields(credentials);
        break;
      case 'customers':
        sourceFields = await getIPOSCustomerFields(credentials);
        break;
      case 'categories':
        sourceFields = await getIPOSCategoryFields(credentials);
        break;
      case 'branches':
        sourceFields = await getIPOSBranchFields(credentials);
        break;
      case 'vouchers':
        sourceFields = await getIPOSVoucherFields(credentials);
        break;
    }

    // Lấy danh sách trường từ hệ thống
    let targetFields: string[] = [];
    switch (resourceType) {
      case 'products':
        targetFields = [
          'id', 'name', 'description', 'price', 'compare_at_price',
          'sku', 'barcode', 'image_url', 'category_id', 'status',
          'weight', 'dimensions', 'metadata', 'type', 'tax_rate',
          // Trường metadata mở rộng
          'external_id', 'source', 'last_synced_at', 'integration_data'
        ];
        break;
      case 'orders':
        targetFields = [
          'id', 'customer_id', 'order_code', 'total_amount',
          'discount_amount', 'status', 'payment_method',
          'payment_status', 'shipping_address', 'billing_address',
          'notes', 'metadata', 'created_at',
          // Trường metadata mở rộng
          'external_id', 'source', 'last_synced_at', 'integration_data'
        ];
        break;
      case 'customers':
        targetFields = [
          'id', 'name', 'email', 'phone', 'address',
          'city', 'province', 'country', 'postal_code',
          'metadata',
          // Trường metadata mở rộng
          'external_id', 'source', 'last_synced_at', 'integration_data'
        ];
        break;
      case 'categories':
        targetFields = [
          'id', 'name', 'description', 'parent_id', 'status',
          'metadata',
          // Trường metadata mở rộng
          'external_id', 'source', 'last_synced_at', 'integration_data'
        ];
        break;
      case 'branches':
        targetFields = [
          'id', 'name', 'address', 'city', 'district',
          'phone', 'email', 'status', 'metadata',
          // Trường metadata mở rộng
          'external_id', 'source', 'last_synced_at', 'integration_data'
        ];
        break;
      case 'vouchers':
        targetFields = [
          'id', 'name', 'type', 'amount', 'valid_from', 'valid_to',
          'status', 'description', 'conditions', 'usage_limit', 'usage_count',
          'metadata',
          // Trường metadata mở rộng
          'external_id', 'source', 'last_synced_at', 'integration_data'
        ];
        break;
    }

    // Lấy mappings hiện tại
    const { data: existingMappings } = await supabase
      .from('integration_mappings')
      .select('source_field, target_field')
      .eq('integration_id', integrationId)
      .eq('resource_type', resourceType)
      .eq('is_active', true);

    // Chuyển đổi mappings thành định dạng phù hợp
    const mappings: Record<string, string> = {};
    if (existingMappings && existingMappings.length > 0) {
      existingMappings.forEach(mapping => {
        mappings[mapping.target_field] = mapping.source_field;
      });
    }

    // Lấy dữ liệu mẫu từ iPOS
    let previewData: any[] = [];
    try {
      // Tạo connector
      const connector = await createIPOSConnector(integrationId);

      switch (resourceType) {
        case 'products':
          previewData = await connector.getProducts({ limit: 5 });
          break;
        case 'orders':
          previewData = await connector.getOrders({ limit: 5 });
          break;
        case 'customers':
          previewData = await connector.getCustomers({ limit: 5 });
          break;
        case 'categories':
          previewData = await connector.getCategories({ limit: 5 });
          break;
        case 'branches':
          previewData = await connector.getBranches({ limit: 5 });
          break;
        case 'vouchers':
          previewData = await connector.getVouchers({ limit: 5 });
          break;
      }
    } catch (error) {
      logger.warn({ error, resourceType }, 'Failed to get preview data from iPOS');
    }

    return {
      sourceFields,
      targetFields,
      mappings,
      previewData,
    };
  } catch (error: any) {
    logger.error({ error, integrationId, resourceType }, 'Error getting iPOS fields');
    throw new Error(`Failed to get iPOS fields: ${error.message}`);
  }
}

/**
 * Lưu mapping cho iPOS
 */
export async function saveIPOSMapping(
  integrationId: string,
  resourceType: 'products' | 'orders' | 'customers' | 'categories' | 'branches' | 'vouchers',
  mappings: Record<string, string>
) {
  const supabase = getSupabaseServerClient<Database>();
  const logger = await getLogger();

  try {
    logger.info({ integrationId, resourceType }, 'Saving iPOS mapping');

    // Xóa mappings hiện tại
    await supabase
      .from('integration_mappings')
      .delete()
      .eq('integration_id', integrationId)
      .eq('resource_type', resourceType as string);

    // Tạo mappings mới
    const mappingsToInsert = Object.entries(mappings).map(([targetField, sourceField]) => ({
      integration_id: integrationId,
      resource_type: resourceType as string,
      source_field: sourceField,
      target_field: targetField,
      is_active: true,
    }));

    if (mappingsToInsert.length > 0) {
      const { error } = await supabase
        .from('integration_mappings')
        .insert(mappingsToInsert);

      if (error) throw error;
    }

    // Cập nhật integration
    await supabase
      .from('integrations')
      .update({
        updated_at: new Date().toISOString(),
      })
      .eq('id', integrationId);

    logger.info({ integrationId, resourceType, mappingsCount: mappingsToInsert.length }, 'iPOS mapping saved successfully');
    return { success: true };
  } catch (error: any) {
    logger.error({ error, integrationId, resourceType }, 'Error saving iPOS mapping');
    throw new Error(`Failed to save iPOS mapping: ${error.message}`);
  }
}

/**
 * Test mapping cho iPOS
 */
export async function testIPOSMapping(
  integrationId: string,
  resourceType: 'products' | 'orders' | 'customers' | 'categories' | 'branches' | 'vouchers',
  mappings: Record<string, string>
) {
  const logger = await getLogger();

  try {
    logger.info({ integrationId, resourceType }, 'Testing iPOS mapping');

    // Tạo iPOS connector
    const connector = await createIPOSConnector(integrationId);

    // Lấy dữ liệu mẫu từ iPOS
    let data: any[] = [];
    switch (resourceType) {
      case 'products':
        data = await connector.getProducts({ limit: 5 });
        break;
      case 'orders':
        data = await connector.getOrders({ limit: 5 });
        break;
      case 'customers':
        data = await connector.getCustomers({ limit: 5 });
        break;
      case 'categories':
        data = await connector.getCategories({ limit: 5 });
        break;
      case 'branches':
        data = await connector.getBranches({ limit: 5 });
        break;
      case 'vouchers':
        data = await connector.getVouchers({ limit: 5 });
        break;
    }

    // Áp dụng mapping cho dữ liệu mẫu
    const mappedData = Array.isArray(data) && data.length > 0 ? data.map(item => {
      const result: Record<string, any> = {};

      Object.entries(mappings).forEach(([targetField, sourceField]) => {
        result[targetField] = item[sourceField];
      });

      return result;
    }) : [];

    // Đảm bảo data là một mảng
    const dataArray = Array.isArray(data) ? data : [];
    const count = dataArray.length;

    logger.info({ integrationId, resourceType, count }, 'iPOS mapping test successful');
    return {
      success: true,
      count,
      data: dataArray,
      mappedData: mappedData,
    };
  } catch (error: any) {
    logger.error({ error, integrationId, resourceType }, 'Error testing iPOS mapping');
    throw new Error(`Failed to test iPOS mapping: ${error.message}`);
  }
}

/**
 * Đồng bộ dữ liệu từ iPOS
 */
export async function syncIPOSData(
  integrationId: string,
  resourceType: 'products' | 'orders' | 'customers' | 'categories' | 'branches' | 'vouchers',
  userId: string
) {
  const supabase = getSupabaseServerClient<Database>();
  const logger = await getLogger();

  try {
    logger.info({ integrationId, resourceType }, 'Starting iPOS data sync');

    // Lấy thông tin integration
    const { data: integration, error: integrationError } = await supabase
      .from('integrations')
      .select('*')
      .eq('id', integrationId)
      .eq('type', 'ipos' as Database['public']['Enums']['integration_type'])
      .single();

    if (integrationError || !integration) {
      throw new Error('iPOS integration not found');
    }

    // Kiểm tra trạng thái integration
    if (integration.status !== 'connected' || !integration.enabled) {
      throw new Error('iPOS integration is not connected or disabled');
    }

    // Tạo sync log
    const { data: syncLog, error: syncLogError } = await supabase
      .from('integration_sync_logs')
      .insert({
        integration_id: integrationId,
        resource_type: resourceType,
        status: 'in_progress' as Database['public']['Enums']['integration_sync_status'],
        created_by: userId,
      })
      .select()
      .single();

    if (syncLogError || !syncLog) {
      throw new Error('Failed to create sync log');
    }

    // Tạo connector
    const connector = await createIPOSConnector(integrationId);

    // Lấy dữ liệu từ iPOS
    let data: any[] = [];
    switch (resourceType) {
      case 'products':
        data = await connector.getProducts();
        break;
      case 'orders':
        data = await connector.getOrders();
        break;
      case 'customers':
        data = await connector.getCustomers();
        break;
      case 'categories':
        data = await connector.getCategories();
        break;
      case 'branches':
        data = await connector.getBranches();
        break;
      case 'vouchers':
        data = await connector.getVouchers();
        break;
    }

    // Lấy mappings
    const { data: mappings } = await supabase
      .from('integration_mappings')
      .select('*')
      .eq('integration_id', integrationId)
      .eq('resource_type', resourceType as string)
      .eq('is_active', true);

    // Áp dụng mappings và lưu dữ liệu
    let successCount = 0;
    let failedCount = 0;

    for (const item of data) {
      try {
        // Áp dụng mapping
        const mappedData: Record<string, any> = {};

        mappings?.forEach(mapping => {
          const { source_field, target_field, transform_function } = mapping;

          // Lấy giá trị từ source field
          let value = item[source_field];

          // Áp dụng hàm biến đổi nếu có
          if (transform_function && value !== undefined) {
            try {
              // Thực thi hàm biến đổi (cẩn thận với eval!)
              // Trong môi trường production, nên sử dụng cách an toàn hơn
              const transformFn = new Function('value', transform_function);
              value = transformFn(value);
            } catch (error) {
              logger.error({ error, sourceField: source_field }, 'Error applying transform function');
            }
          }

          // Gán giá trị cho target field
          if (value !== undefined) {
            mappedData[target_field] = value;
          }
        });

        // Thêm account_id vào dữ liệu
        mappedData.account_id = integration.account_id;

        // Thêm trường đánh dấu nguồn dữ liệu
        mappedData.source = 'ipos';
        mappedData.external_id = item.id || item.item_id || item.tran_id || item.membership_id;
        mappedData.last_synced_at = new Date().toISOString();
        mappedData.integration_data = item; // Lưu toàn bộ dữ liệu gốc từ iPOS

        // Xác định bảng dựa trên resource type
        let table: string;
        switch (resourceType) {
          case 'products':
            table = 'products';

            // Tạo SKU tự động nếu không có
            if (!mappedData.sku) {
              const timestamp = Date.now().toString().slice(-6);
              const randomStr = Math.random().toString(36).substring(2, 5).toUpperCase();
              const productName = mappedData.name || '';
              const prefix = productName.slice(0, 3).toUpperCase().replace(/[^A-Z0-9]/g, '');

              mappedData.sku = `${prefix || 'SKU'}-${randomStr}${timestamp}`;
              logger.info({ productId: mappedData.external_id, generatedSku: mappedData.sku }, 'Generated SKU for product');
            }
            break;
          case 'orders':
            table = 'customer_orders';
            break;
          case 'customers':
            table = 'customers';
            break;
          case 'categories':
            table = 'categories';
            break;
          case 'branches':
            table = 'branches';
            break;
          case 'vouchers':
            table = 'vouchers';
            break;
          default:
            throw new Error(`Unsupported resource type: ${resourceType}`);
        }

        // Xử lý ID thông minh - tìm kiếm bản ghi dựa trên các trường định danh khác nhau
        let existingData = null;
        let fetchError = null;

        // Nếu có external_id, tìm kiếm theo external_id trước
        if (mappedData.external_id) {
          const result = await supabase
            .from(table)
            .select('id')
            .eq('external_id', mappedData.external_id)
            .eq('account_id', integration.account_id)
            .eq('source', 'ipos')
            .maybeSingle();

          existingData = result.data;
          fetchError = result.error;
        }

        // Nếu không tìm thấy theo external_id, thử tìm theo các trường khác
        if (!existingData && !fetchError) {
          // Tìm kiếm theo các trường định danh phù hợp với từng loại resource
          const identifierFields = [];

          switch (resourceType) {
            case 'products':
              if (mappedData.sku) identifierFields.push(['sku', mappedData.sku]);
              if (mappedData.barcode) identifierFields.push(['barcode', mappedData.barcode]);
              break;
            case 'orders':
              if (mappedData.order_number) identifierFields.push(['order_number', mappedData.order_number]);
              break;
            case 'customers':
              if (mappedData.email) identifierFields.push(['email', mappedData.email]);
              if (mappedData.phone) identifierFields.push(['phone', mappedData.phone]);
              break;
            case 'categories':
              if (mappedData.name) identifierFields.push(['name', mappedData.name]);
              break;
            case 'branches':
              if (mappedData.name) identifierFields.push(['name', mappedData.name]);
              if (mappedData.address) identifierFields.push(['address', mappedData.address]);
              break;
            case 'vouchers':
              if (mappedData.name) identifierFields.push(['name', mappedData.name]);
              if (mappedData.code) identifierFields.push(['code', mappedData.code]);
              break;
          }

          // Tìm kiếm theo từng trường định danh
          for (const [field, value] of identifierFields) {
            if (!value) continue;

            const result = await supabase
              .from(table)
              .select('id')
              .eq(field as string, value)
              .eq('account_id', integration.account_id)
              .maybeSingle();

            if (result.data) {
              existingData = result.data;
              break;
            }
          }
        }

        // Nếu vẫn không tìm thấy và có ID, thử tìm theo ID
        if (!existingData && mappedData.id) {
          const result = await supabase
            .from(table)
            .select('id')
            .eq('id', mappedData.id)
            .eq('account_id', integration.account_id)
            .maybeSingle();

          existingData = result.data;
          fetchError = result.error;
        }

        if (fetchError && fetchError.code !== 'PGRST116') {
          throw fetchError;
        }

        let saveResult;
        if (existingData) {
          // Cập nhật dữ liệu hiện có
          saveResult = await supabase
            .from(table)
            .update(mappedData)
            .eq('id', existingData.id)
            .eq('account_id', integration.account_id);
        } else {
          // Tạo dữ liệu mới
          saveResult = await supabase
            .from(table)
            .insert(mappedData);
        }

        if (saveResult.error) {
          // Lưu thông tin lỗi
          await supabase
            .from('integration_sync_items')
            .insert({
              sync_log_id: syncLog.id,
              external_id: item.id,
              resource_type: resourceType as string,
              status: 'error' as Database['public']['Enums']['integration_sync_status'],
              error_message: saveResult.error.message,
              raw_data: item,
              processed_data: mappedData,
            });

          failedCount++;
        } else {
          // Lưu thông tin thành công
          await supabase
            .from('integration_sync_items')
            .insert({
              sync_log_id: syncLog.id,
              external_id: item.id,
              internal_id: mappedData.id,
              resource_type: resourceType as string,
              status: 'success' as Database['public']['Enums']['integration_sync_status'],
              raw_data: item,
              processed_data: mappedData,
            });

          successCount++;
        }
      } catch (itemError: any) {
        // Lưu thông tin lỗi
        await supabase
          .from('integration_sync_items')
          .insert({
            sync_log_id: syncLog.id,
            external_id: item.id || 'unknown',
            resource_type: resourceType as string,
            status: 'error' as Database['public']['Enums']['integration_sync_status'],
            error_message: itemError.message,
            raw_data: item,
          });

        failedCount++;
      }
    }

    // Cập nhật sync log
    await supabase
      .from('integration_sync_logs')
      .update({
        status: failedCount > 0 ? (successCount > 0 ? 'partial' as Database['public']['Enums']['integration_sync_status'] : 'error' as Database['public']['Enums']['integration_sync_status']) : 'success' as Database['public']['Enums']['integration_sync_status'],
        items_processed: data.length,
        items_created: successCount,
        items_failed: failedCount,
        completed_at: new Date().toISOString(),
      })
      .eq('id', syncLog.id);

    // Cập nhật integration
    await supabase
      .from('integrations')
      .update({
        last_sync_at: new Date().toISOString(),
        error_message: failedCount > 0 ? `Failed to sync ${failedCount} items` : null,
      })
      .eq('id', integrationId);

    logger.info(
      { integrationId, resourceType, total: data.length, success: successCount, failed: failedCount },
      'iPOS data sync completed'
    );

    return {
      success: true,
      syncLogId: syncLog.id,
      total: data.length,
      created: successCount,
      failed: failedCount,
    };
  } catch (error: any) {
    logger.error({ error, integrationId, resourceType }, 'Error syncing iPOS data');

    // Cập nhật sync log nếu đã tạo
    const { data: syncLog } = await supabase
      .from('integration_sync_logs')
      .select('id')
      .eq('integration_id', integrationId)
      .eq('resource_type', resourceType)
      .eq('status', 'in_progress')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (syncLog) {
      await supabase
        .from('integration_sync_logs')
        .update({
          status: 'error',
          error_message: error.message,
          completed_at: new Date().toISOString(),
        })
        .eq('id', syncLog.id);
    }

    // Cập nhật integration
    await supabase
      .from('integrations')
      .update({
        error_message: `Sync failed: ${error.message}`,
      })
      .eq('id', integrationId);

    throw new Error(`Failed to sync iPOS data: ${error.message}`);
  }
}
