#!/usr/bin/env node

/**
 * Mobile API Testing Script
 * Tests all education platform mobile APIs to ensure they work correctly
 */

const https = require('https');
const fs = require('fs');

// Configuration
const CONFIG = {
  baseUrl: process.env.API_BASE_URL || 'http://localhost:3000',
  accessToken: process.env.TEST_ACCESS_TOKEN || 'your-test-token',
  organizationId: process.env.TEST_ORGANIZATION_ID || 'test-org-uuid',
  learnerId: process.env.TEST_LEARNER_ID || 'test-learner-uuid',
  programId: process.env.TEST_PROGRAM_ID || 'test-program-uuid',
  feeId: process.env.TEST_FEE_ID || 'test-fee-uuid',
  eventId: process.env.TEST_EVENT_ID || 'test-event-uuid',
};

// Test results
const results = {
  passed: 0,
  failed: 0,
  tests: []
};

// Helper function to make API requests
async function apiRequest(endpoint, options = {}) {
  const url = `${CONFIG.baseUrl}/api/education${endpoint}`;
  const method = options.method || 'GET';
  const headers = {
    'Authorization': `Bearer ${CONFIG.accessToken}`,
    'Content-Type': 'application/json',
    ...options.headers
  };

  console.log(`\n🔄 ${method} ${endpoint}`);

  try {
    const response = await fetch(url, {
      method,
      headers,
      body: options.body ? JSON.stringify(options.body) : undefined,
    });

    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log(`✅ Success: ${response.status}`);
      return { success: true, data: data.data, status: response.status };
    } else {
      console.log(`❌ Failed: ${response.status} - ${data.error || 'Unknown error'}`);
      return { success: false, error: data.error, status: response.status };
    }
  } catch (error) {
    console.log(`❌ Network Error: ${error.message}`);
    return { success: false, error: error.message, status: 0 };
  }
}

// Test function wrapper
async function test(name, testFn) {
  console.log(`\n📋 Testing: ${name}`);
  console.log('='.repeat(50));
  
  try {
    const result = await testFn();
    if (result.success) {
      results.passed++;
      console.log(`✅ PASSED: ${name}`);
    } else {
      results.failed++;
      console.log(`❌ FAILED: ${name} - ${result.error}`);
    }
    results.tests.push({ name, ...result });
  } catch (error) {
    results.failed++;
    console.log(`❌ ERROR: ${name} - ${error.message}`);
    results.tests.push({ name, success: false, error: error.message });
  }
}

// Guardian API Tests
async function testGuardianAPIs() {
  console.log('\n🏠 GUARDIAN APIs');
  console.log('================');

  // Test 1: Get Children
  await test('Guardian - Get Children', async () => {
    return await apiRequest(`/guardian/children?organization_id=${CONFIG.organizationId}`);
  });

  // Test 2: Get Child Details
  await test('Guardian - Get Child Details', async () => {
    return await apiRequest('/guardian/children', {
      method: 'POST',
      body: {
        learner_id: CONFIG.learnerId,
        organization_id: CONFIG.organizationId
      }
    });
  });

  // Test 3: Get Attendance
  await test('Guardian - Get Attendance', async () => {
    return await apiRequest(`/guardian/attendance?learner_id=${CONFIG.learnerId}&organization_id=${CONFIG.organizationId}&month=2024-12`);
  });

  // Test 4: Get Attendance Summary
  await test('Guardian - Get Attendance Summary', async () => {
    return await apiRequest('/guardian/attendance', {
      method: 'POST',
      body: {
        learner_id: CONFIG.learnerId,
        organization_id: CONFIG.organizationId,
        year: '2024'
      }
    });
  });

  // Test 5: Get Reports
  await test('Guardian - Get Reports', async () => {
    return await apiRequest(`/guardian/reports?learner_id=${CONFIG.learnerId}&organization_id=${CONFIG.organizationId}&limit=10`);
  });

  // Test 6: Get Report Details
  await test('Guardian - Get Report Details', async () => {
    return await apiRequest('/guardian/reports', {
      method: 'POST',
      body: {
        report_id: 'test-report-uuid',
        organization_id: CONFIG.organizationId
      }
    });
  });

  // Test 7: Get Fees
  await test('Guardian - Get Fees', async () => {
    return await apiRequest(`/guardian/fees?organization_id=${CONFIG.organizationId}&status=pending`);
  });

  // Test 8: Get Fee Details
  await test('Guardian - Get Fee Details', async () => {
    return await apiRequest('/guardian/fees', {
      method: 'POST',
      body: {
        fee_id: CONFIG.feeId,
        organization_id: CONFIG.organizationId
      }
    });
  });

  // Test 9: Get Events
  await test('Guardian - Get Events', async () => {
    return await apiRequest(`/guardian/events?organization_id=${CONFIG.organizationId}&upcoming=true`);
  });

  // Test 10: Register for Event
  await test('Guardian - Register for Event', async () => {
    return await apiRequest('/guardian/events/register', {
      method: 'POST',
      body: {
        event_id: CONFIG.eventId,
        organization_id: CONFIG.organizationId,
        learner_ids: [CONFIG.learnerId],
        notes: 'Test registration'
      }
    });
  });
}

// Instructor API Tests
async function testInstructorAPIs() {
  console.log('\n👨‍🏫 INSTRUCTOR APIs');
  console.log('==================');

  // Test 1: Get Classes
  await test('Instructor - Get Classes', async () => {
    return await apiRequest(`/instructor/classes?organization_id=${CONFIG.organizationId}`);
  });

  // Test 2: Get Class Details
  await test('Instructor - Get Class Details', async () => {
    return await apiRequest('/instructor/classes', {
      method: 'POST',
      body: {
        program_id: CONFIG.programId,
        organization_id: CONFIG.organizationId
      }
    });
  });

  // Test 3: Get Attendance Template
  await test('Instructor - Get Attendance Template', async () => {
    return await apiRequest(`/instructor/attendance?program_id=${CONFIG.programId}&organization_id=${CONFIG.organizationId}&session_date=2024-12-01&session_time=morning`);
  });

  // Test 4: Submit Attendance
  await test('Instructor - Submit Attendance', async () => {
    return await apiRequest('/instructor/attendance', {
      method: 'POST',
      body: {
        program_id: CONFIG.programId,
        organization_id: CONFIG.organizationId,
        session_date: '2024-12-01',
        session_time: 'morning',
        attendance_records: [
          {
            learner_id: CONFIG.learnerId,
            status: 'present',
            check_in_time: '07:45',
            check_out_time: '16:30',
            notes: 'Test attendance'
          }
        ]
      }
    });
  });

  // Test 5: Create Report
  await test('Instructor - Create Report', async () => {
    return await apiRequest('/instructor/reports', {
      method: 'POST',
      body: {
        learner_id: CONFIG.learnerId,
        program_id: CONFIG.programId,
        organization_id: CONFIG.organizationId,
        report_date: '2024-12-01',
        report_type: 'daily',
        content: {
          academic: {
            subjects: ['Toán', 'Tiếng Việt'],
            performance: 'good'
          },
          behavior: {
            social: 'excellent',
            discipline: 'good'
          },
          activities: ['Vẽ tranh', 'Hát'],
          notes: 'Test report'
        },
        overall_rating: 'good',
        media_urls: []
      }
    });
  });

  // Test 6: Get Reports
  await test('Instructor - Get Reports', async () => {
    return await apiRequest(`/instructor/reports?organization_id=${CONFIG.organizationId}&program_id=${CONFIG.programId}&limit=20`);
  });
}

// Payment API Tests
async function testPaymentAPIs() {
  console.log('\n💳 PAYMENT APIs');
  console.log('===============');

  // Test 1: Create Payment
  await test('Payment - Create Payment', async () => {
    return await apiRequest('/payments/create', {
      method: 'POST',
      body: {
        fee_id: CONFIG.feeId,
        organization_id: CONFIG.organizationId,
        return_url: 'https://miniapp.zalo.me/payment-result'
      }
    });
  });

  // Test 2: Check Payment Status
  await test('Payment - Check Status', async () => {
    return await apiRequest(`/payments/status?payment_id=test-payment-uuid&organization_id=${CONFIG.organizationId}`);
  });
}

// Notification API Tests
async function testNotificationAPIs() {
  console.log('\n🔔 NOTIFICATION APIs');
  console.log('===================');

  // Test 1: Get Notifications
  await test('Notifications - Get List', async () => {
    return await apiRequest(`/notifications?organization_id=${CONFIG.organizationId}&limit=20`);
  });

  // Test 2: Mark as Read
  await test('Notifications - Mark as Read', async () => {
    return await apiRequest('/notifications', {
      method: 'PUT',
      body: {
        notification_ids: ['test-notification-uuid'],
        organization_id: CONFIG.organizationId
      }
    });
  });

  // Test 3: Mark All as Read
  await test('Notifications - Mark All as Read', async () => {
    return await apiRequest('/notifications', {
      method: 'PUT',
      body: {
        mark_all_read: true,
        organization_id: CONFIG.organizationId
      }
    });
  });
}

// File Upload API Tests
async function testFileUploadAPIs() {
  console.log('\n📁 FILE UPLOAD APIs');
  console.log('===================');

  // Test 1: Get Files List
  await test('Upload - Get Files List', async () => {
    return await apiRequest(`/upload?organization_id=${CONFIG.organizationId}&upload_type=report_media&limit=20`);
  });

  // Note: File upload test requires multipart/form-data which is complex in Node.js
  // This would typically be tested with a proper test file
  console.log('📝 Note: File upload test requires actual file and multipart handling');
}

// Additional Module API Tests
async function testAdditionalModuleAPIs() {
  console.log('\n📚 ADDITIONAL MODULE APIs');
  console.log('=========================');

  // Test Curriculum APIs
  await test('Curriculum - Get List', async () => {
    return await apiRequest(`/curriculum?organization_id=${CONFIG.organizationId}&subject=Math&age_group=3-4`);
  });

  await test('Curriculum - Create', async () => {
    return await apiRequest('/curriculum', {
      method: 'POST',
      body: {
        organization_id: CONFIG.organizationId,
        title: 'Test Curriculum',
        subject: 'Math',
        age_group: '3-4',
        difficulty_level: 'beginner',
        duration_minutes: 30,
        objectives: ['Test objective'],
        activities: [{ name: 'Test activity', description: 'Test', duration_minutes: 15 }]
      }
    });
  });

  // Test Health APIs
  await test('Health - Get Records', async () => {
    return await apiRequest(`/health?organization_id=${CONFIG.organizationId}&learner_id=${CONFIG.learnerId}&record_type=vaccination`);
  });

  await test('Health - Create Record', async () => {
    return await apiRequest('/health', {
      method: 'POST',
      body: {
        organization_id: CONFIG.organizationId,
        learner_id: CONFIG.learnerId,
        record_type: 'vaccination',
        title: 'Test Vaccination',
        record_date: '2024-12-01',
        doctor_name: 'Dr. Test'
      }
    });
  });

  // Test Meals APIs
  await test('Meals - Get Plans', async () => {
    return await apiRequest(`/meals?organization_id=${CONFIG.organizationId}&date=2024-12-01&meal_type=lunch`);
  });

  await test('Meals - Create Plan', async () => {
    return await apiRequest('/meals', {
      method: 'POST',
      body: {
        organization_id: CONFIG.organizationId,
        date: '2024-12-01',
        meal_type: 'lunch',
        menu_items: [{ name: 'Test Meal', description: 'Test meal description', ingredients: ['Test ingredient'] }]
      }
    });
  });

  // Test Transportation APIs
  await test('Transportation - Get Routes', async () => {
    return await apiRequest(`/transportation?organization_id=${CONFIG.organizationId}&date=2024-12-01`);
  });

  // Test Library APIs
  await test('Library - Get Items', async () => {
    return await apiRequest(`/library?organization_id=${CONFIG.organizationId}&item_type=book&available_only=true`);
  });

  await test('Library - Create Item', async () => {
    return await apiRequest('/library', {
      method: 'POST',
      body: {
        organization_id: CONFIG.organizationId,
        item_type: 'book',
        title: 'Test Book',
        category: 'Math',
        age_group: '3-5',
        physical_copies: 1,
        available_copies: 1
      }
    });
  });

  // Test Dashboard API
  await test('Dashboard - Get Metrics', async () => {
    return await apiRequest(`/dashboard?organization_id=${CONFIG.organizationId}&period=month&include_charts=true`);
  });
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Complete Mobile API Tests');
  console.log('=====================================');
  console.log(`Base URL: ${CONFIG.baseUrl}`);
  console.log(`Organization ID: ${CONFIG.organizationId}`);

  // Check if we have required config
  if (!CONFIG.accessToken || CONFIG.accessToken === 'your-test-token') {
    console.log('⚠️  Warning: Using default test token. Set TEST_ACCESS_TOKEN environment variable for real testing.');
  }

  try {
    await testGuardianAPIs();
    await testInstructorAPIs();
    await testPaymentAPIs();
    await testNotificationAPIs();
    await testFileUploadAPIs();
    await testAdditionalModuleAPIs();

    // Print summary
    console.log('\n📊 TEST SUMMARY');
    console.log('===============');
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`📊 Total: ${results.passed + results.failed}`);
    console.log(`📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);

    // Save detailed results
    const reportPath = 'test-results.json';
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
    console.log(`\n📄 Detailed results saved to: ${reportPath}`);

    // Exit with appropriate code
    process.exit(results.failed > 0 ? 1 : 0);

  } catch (error) {
    console.error('💥 Test runner error:', error);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests, apiRequest, test };
