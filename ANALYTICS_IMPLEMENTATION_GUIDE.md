# Analytics Implementation Guide

## 🛠️ Cách triển khai Analytics trong dự án

### 1. **Setup cơ bản**

#### **Khởi tạo Analytics Provider**
```typescript
// apps/web/components/analytics-provider.tsx
import { analytics } from '@kit/analytics';
import { useAppEvents } from '@kit/shared/events';

export function AnalyticsProvider({ children }: React.PropsWithChildren) {
  // Mapping app events to analytics
  const analyticsMapping = {
    'user.signedIn': (event) => {
      analytics.identify(event.payload.userId);
    },
    'user.signedUp': (event) => {
      analytics.trackEvent('user_signup', event.payload);
    },
    'checkout.started': (event) => {
      analytics.trackEvent('checkout_started', event.payload);
    }
  };

  useAnalyticsMapping(analyticsMapping);

  return <>{children}</>;
}
```

#### **Tích hợp vào App Layout**
```typescript
// apps/web/app/layout.tsx
import { AnalyticsProvider } from '~/components/analytics-provider';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <AppEventsProvider>
          <AnalyticsProvider>
            {children}
          </AnalyticsProvider>
        </AppEventsProvider>
      </body>
    </html>
  );
}
```

### 2. **Tracking Events trong Components**

#### **Page View Tracking**
```typescript
// Automatic page view tracking
import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { analytics } from '@kit/analytics';

export function usePageViewTracking() {
  const pathname = usePathname();

  useEffect(() => {
    analytics.trackPageView(pathname);
  }, [pathname]);
}

// Sử dụng trong layout
export default function Layout({ children }) {
  usePageViewTracking();
  return <>{children}</>;
}
```

#### **Event Tracking trong Components**
```typescript
// Product component
import { analytics } from '@kit/analytics';

export function ProductCard({ product }) {
  const handleAddToCart = async () => {
    // Business logic
    await addToCart(product.id);
    
    // Analytics tracking
    await analytics.trackEvent('add_to_cart', {
      productId: product.id,
      productName: product.name,
      price: product.price,
      category: product.category
    });
  };

  const handleProductView = () => {
    analytics.trackEvent('product_view', {
      productId: product.id,
      productName: product.name,
      category: product.category
    });
  };

  return (
    <div onClick={handleProductView}>
      <h3>{product.name}</h3>
      <button onClick={handleAddToCart}>Add to Cart</button>
    </div>
  );
}
```

### 3. **Custom Analytics Provider**

#### **Tạo Provider mới**
```typescript
// packages/analytics/src/providers/google-analytics-provider.ts
import { AnalyticsService } from '../types';

export interface GoogleAnalyticsConfig {
  measurementId: string;
  apiSecret?: string;
}

export class GoogleAnalyticsService implements AnalyticsService {
  private config: GoogleAnalyticsConfig;
  private gtag: any;

  constructor(config: GoogleAnalyticsConfig) {
    this.config = config;
  }

  async initialize() {
    // Load Google Analytics script
    const script = document.createElement('script');
    script.src = `https://www.googletagmanager.com/gtag/js?id=${this.config.measurementId}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    this.gtag = function() { window.dataLayer.push(arguments); };
    this.gtag('js', new Date());
    this.gtag('config', this.config.measurementId);
  }

  async trackPageView(path: string) {
    this.gtag('config', this.config.measurementId, {
      page_path: path
    });
  }

  async trackEvent(eventName: string, eventProperties?: Record<string, any>) {
    this.gtag('event', eventName, eventProperties);
  }

  async identify(userId: string, traits?: Record<string, any>) {
    this.gtag('config', this.config.measurementId, {
      user_id: userId,
      custom_map: traits
    });
  }
}
```

#### **Register Provider**
```typescript
// packages/analytics/src/index.ts
import { GoogleAnalyticsService } from './providers/google-analytics-provider';

export const analytics: AnalyticsManager = createAnalyticsManager({
  providers: {
    null: () => NullAnalyticsService,
    zaloMiniApp: (config) => new ZaloMiniAppAnalyticsService(config),
    googleAnalytics: (config) => new GoogleAnalyticsService(config), // New provider
  },
});

// Sử dụng
analytics.addProvider('googleAnalytics', {
  measurementId: 'GA_MEASUREMENT_ID'
});
```

### 4. **Advanced Event Tracking**

#### **E-commerce Events**
```typescript
// Enhanced e-commerce tracking
export const ecommerceEvents = {
  // View item list
  viewItemList: (items: Product[], listName: string) => {
    analytics.trackEvent('view_item_list', {
      item_list_name: listName,
      items: items.map(item => ({
        item_id: item.id,
        item_name: item.name,
        category: item.category,
        price: item.price
      }))
    });
  },

  // Purchase event
  purchase: (orderId: string, items: CartItem[], total: number) => {
    analytics.trackEvent('purchase', {
      transaction_id: orderId,
      value: total,
      currency: 'VND',
      items: items.map(item => ({
        item_id: item.productId,
        item_name: item.name,
        quantity: item.quantity,
        price: item.price
      }))
    });
  },

  // Begin checkout
  beginCheckout: (items: CartItem[], total: number) => {
    analytics.trackEvent('begin_checkout', {
      value: total,
      currency: 'VND',
      items: items.map(item => ({
        item_id: item.productId,
        quantity: item.quantity,
        price: item.price
      }))
    });
  }
};
```

#### **User Journey Tracking**
```typescript
// Track user journey through funnel
export const funnelTracking = {
  // Step 1: Landing
  trackLanding: (source: string, medium: string, campaign?: string) => {
    analytics.trackEvent('funnel_landing', {
      utm_source: source,
      utm_medium: medium,
      utm_campaign: campaign
    });
  },

  // Step 2: Product interest
  trackProductInterest: (productId: string, action: 'view' | 'click' | 'hover') => {
    analytics.trackEvent('funnel_product_interest', {
      product_id: productId,
      action: action,
      funnel_step: 2
    });
  },

  // Step 3: Add to cart
  trackAddToCart: (productId: string, quantity: number) => {
    analytics.trackEvent('funnel_add_to_cart', {
      product_id: productId,
      quantity: quantity,
      funnel_step: 3
    });
  },

  // Step 4: Checkout
  trackCheckout: (step: number, option?: string) => {
    analytics.trackEvent('funnel_checkout', {
      checkout_step: step,
      checkout_option: option,
      funnel_step: 4
    });
  }
};
```

### 5. **Performance Monitoring**

#### **API Performance Tracking**
```typescript
// Track API performance
export function trackApiCall(endpoint: string, method: string) {
  const startTime = performance.now();
  
  return {
    success: (responseTime?: number) => {
      const endTime = responseTime || performance.now();
      analytics.trackEvent('api_call_success', {
        endpoint,
        method,
        response_time: endTime - startTime
      });
    },
    
    error: (errorCode: string, errorMessage: string) => {
      analytics.trackEvent('api_call_error', {
        endpoint,
        method,
        error_code: errorCode,
        error_message: errorMessage,
        response_time: performance.now() - startTime
      });
    }
  };
}

// Usage
const tracker = trackApiCall('/api/products', 'GET');
try {
  const response = await fetch('/api/products');
  tracker.success();
} catch (error) {
  tracker.error('FETCH_ERROR', error.message);
}
```

#### **User Experience Metrics**
```typescript
// Track Core Web Vitals
export function trackWebVitals() {
  // Largest Contentful Paint
  new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      analytics.trackEvent('web_vital_lcp', {
        value: entry.startTime,
        rating: entry.startTime < 2500 ? 'good' : entry.startTime < 4000 ? 'needs_improvement' : 'poor'
      });
    }
  }).observe({ entryTypes: ['largest-contentful-paint'] });

  // First Input Delay
  new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      analytics.trackEvent('web_vital_fid', {
        value: entry.processingStart - entry.startTime,
        rating: entry.processingStart - entry.startTime < 100 ? 'good' : 'poor'
      });
    }
  }).observe({ entryTypes: ['first-input'] });
}
```

### 6. **Testing Analytics**

#### **Mock Analytics Service**
```typescript
// For testing
export class MockAnalyticsService implements AnalyticsService {
  public events: Array<{ type: string; data: any }> = [];

  async initialize() {
    return Promise.resolve();
  }

  async trackPageView(path: string) {
    this.events.push({ type: 'pageview', data: { path } });
  }

  async trackEvent(eventName: string, eventProperties?: Record<string, any>) {
    this.events.push({ type: 'event', data: { eventName, eventProperties } });
  }

  async identify(userId: string, traits?: Record<string, any>) {
    this.events.push({ type: 'identify', data: { userId, traits } });
  }

  getEvents() {
    return this.events;
  }

  clearEvents() {
    this.events = [];
  }
}
```

#### **Test Cases**
```typescript
// Jest test example
describe('Analytics', () => {
  let mockService: MockAnalyticsService;

  beforeEach(() => {
    mockService = new MockAnalyticsService();
    analytics.addProvider('mock', mockService);
  });

  it('should track page views', async () => {
    await analytics.trackPageView('/products');
    
    expect(mockService.getEvents()).toHaveLength(1);
    expect(mockService.getEvents()[0]).toEqual({
      type: 'pageview',
      data: { path: '/products' }
    });
  });

  it('should track custom events', async () => {
    await analytics.trackEvent('add_to_cart', { productId: '123' });
    
    expect(mockService.getEvents()).toHaveLength(1);
    expect(mockService.getEvents()[0].data.eventName).toBe('add_to_cart');
  });
});
```
