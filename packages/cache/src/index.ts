// Cache Package Main Export
export * from './types';
export * from './redis';
export * from './utils';

import { CacheProvider, CacheConfig, CacheOptions, CacheEvent, CACHE_INVALIDATION_RULES } from './types';
import { RedisCache } from './redis';
import { EducationCacheUtils, CircuitBreaker } from './utils';

// Cache Manager - Main interface for cache operations
export class CacheManager {
  private provider: CacheProvider;
  private circuitBreaker: CircuitBreaker;
  private invalidationEnabled: boolean;

  constructor(config: CacheConfig) {
    // Initialize cache provider based on config
    if (config.redis) {
      this.provider = new RedisCache(config);
    } else {
      throw new Error('No cache provider configured');
    }

    this.circuitBreaker = new CircuitBreaker(5, 60000); // 5 failures, 1 minute recovery
    this.invalidationEnabled = true;
  }

  // Core cache operations with circuit breaker
  async get<T = any>(key: string, options?: CacheOptions): Promise<T | null> {
    try {
      return await this.circuitBreaker.execute(async () => {
        return await this.provider.get<T>(key);
      });
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set(key: string, value: any, options?: CacheOptions): Promise<void> {
    try {
      const ttl = options?.ttl || EducationCacheUtils.getTTL('default');
      
      await this.circuitBreaker.execute(async () => {
        await this.provider.set(key, value, ttl);
      });
    } catch (error) {
      console.error('Cache set error:', error);
      // Don't throw - cache failures shouldn't break the app
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.circuitBreaker.execute(async () => {
        await this.provider.del(key);
      });
    } catch (error) {
      console.error('Cache del error:', error);
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      return await this.circuitBreaker.execute(async () => {
        return await this.provider.exists(key);
      });
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  async clear(pattern?: string): Promise<void> {
    try {
      await this.circuitBreaker.execute(async () => {
        await this.provider.clear(pattern);
      });
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  // Batch operations
  async mget<T = any>(keys: string[]): Promise<(T | null)[]> {
    try {
      return await this.circuitBreaker.execute(async () => {
        return await this.provider.mget<T>(keys);
      });
    } catch (error) {
      console.error('Cache mget error:', error);
      return keys.map(() => null);
    }
  }

  async mset(keyValuePairs: Array<[string, any, number?]>): Promise<void> {
    try {
      await this.circuitBreaker.execute(async () => {
        await this.provider.mset(keyValuePairs);
      });
    } catch (error) {
      console.error('Cache mset error:', error);
    }
  }

  // Education-specific cache methods
  async cacheWithFallback<T>(
    key: string,
    fallbackFn: () => Promise<T>,
    options?: CacheOptions
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Execute fallback function
    const result = await fallbackFn();
    
    // Cache the result
    if (result !== null && result !== undefined) {
      await this.set(key, result, options);
    }

    return result;
  }

  async cacheLearnerDetails(learnerId: string, data: any): Promise<void> {
    const key = EducationCacheUtils.learnerKey(learnerId);
    const ttl = EducationCacheUtils.getTTL('learner-details');
    await this.set(key, data, { ttl });
  }

  async getLearnerDetails(learnerId: string): Promise<any | null> {
    const key = EducationCacheUtils.learnerKey(learnerId);
    return await this.get(key);
  }

  async cacheDashboardMetrics(orgId: string, period: string, data: any): Promise<void> {
    const key = EducationCacheUtils.dashboardKey(orgId, period);
    const ttl = EducationCacheUtils.getTTL('dashboard-metrics');
    await this.set(key, data, { ttl });
  }

  async getDashboardMetrics(orgId: string, period: string): Promise<any | null> {
    const key = EducationCacheUtils.dashboardKey(orgId, period);
    return await this.get(key);
  }

  async cacheUserPermissions(userId: string, orgId: string, permissions: any): Promise<void> {
    const key = `user:permissions:${userId}:${orgId}`;
    const ttl = EducationCacheUtils.getTTL('user-permissions');
    await this.set(key, permissions, { ttl });
  }

  async getUserPermissions(userId: string, orgId: string): Promise<any | null> {
    const key = `user:permissions:${userId}:${orgId}`;
    return await this.get(key);
  }

  // Cache invalidation
  async invalidateByEvent(event: CacheEvent, entityId?: string): Promise<void> {
    if (!this.invalidationEnabled) return;

    try {
      const rules = CACHE_INVALIDATION_RULES.filter(rule => 
        rule.triggers.includes(event)
      );

      for (const rule of rules) {
        let pattern = rule.pattern;
        
        // Replace placeholders with actual entity ID
        if (entityId) {
          pattern = pattern.replace('*', entityId);
        }

        await this.clear(pattern);
      }
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }

  async invalidateLearner(learnerId: string): Promise<void> {
    const patterns = [
      `learner:${learnerId}:*`,
      `*:learner:${learnerId}`,
      `attendance:${learnerId}:*`,
      `guardian:*:children`, // Invalidate guardian children cache
    ];

    for (const pattern of patterns) {
      await this.clear(pattern);
    }
  }

  async invalidateOrganization(orgId: string): Promise<void> {
    const patterns = [
      `org:${orgId}:*`,
      `dashboard:${orgId}:*`,
      `*:${orgId}:*`,
    ];

    for (const pattern of patterns) {
      await this.clear(pattern);
    }
  }

  async invalidateProgram(programId: string): Promise<void> {
    const patterns = [
      `program:${programId}:*`,
      `*:program:${programId}`,
      `attendance:*:${programId}:*`,
    ];

    for (const pattern of patterns) {
      await this.clear(pattern);
    }
  }

  // Health and monitoring
  async getHealthCheck(): Promise<any> {
    if (this.provider instanceof RedisCache) {
      return await this.provider.getHealthCheck();
    }
    
    return {
      status: 'unknown',
      provider: 'unknown',
    };
  }

  getStats(): any {
    if (this.provider instanceof RedisCache) {
      return this.provider.getStats();
    }
    
    return {};
  }

  getCircuitBreakerState(): string {
    return this.circuitBreaker.getState();
  }

  resetCircuitBreaker(): void {
    this.circuitBreaker.reset();
  }

  enableInvalidation(): void {
    this.invalidationEnabled = true;
  }

  disableInvalidation(): void {
    this.invalidationEnabled = false;
  }

  // Cleanup
  async disconnect(): Promise<void> {
    if (this.provider instanceof RedisCache) {
      await this.provider.disconnect();
    }
  }
}

// Factory function to create cache manager
export function createCacheManager(config: CacheConfig): CacheManager {
  return new CacheManager(config);
}

// Default cache instance (will be initialized by the app)
let defaultCache: CacheManager | null = null;

export function initializeCache(config: CacheConfig): CacheManager {
  defaultCache = createCacheManager(config);
  return defaultCache;
}

export function getCache(): CacheManager {
  if (!defaultCache) {
    throw new Error('Cache not initialized. Call initializeCache() first.');
  }
  return defaultCache;
}

// Utility functions for common cache operations
export const CacheUtils = {
  // Wrap any async function with caching
  withCache: <T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    keyGenerator: (...args: T) => string,
    options?: CacheOptions
  ) => {
    return async (...args: T): Promise<R> => {
      const cache = getCache();
      const key = keyGenerator(...args);
      
      return await cache.cacheWithFallback(
        key,
        () => fn(...args),
        options
      );
    };
  },

  // Create a memoized version of a function with cache
  memoize: <T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    keyGenerator: (...args: T) => string,
    ttl?: number
  ) => {
    const cache = getCache();
    
    return async (...args: T): Promise<R> => {
      const key = keyGenerator(...args);
      const cached = await cache.get<R>(key);
      
      if (cached !== null) {
        return cached;
      }
      
      const result = await fn(...args);
      await cache.set(key, result, { ttl });
      
      return result;
    };
  },

  // Batch cache operations
  batchGet: async <T>(keys: string[]): Promise<Record<string, T | null>> => {
    const cache = getCache();
    const values = await cache.mget<T>(keys);
    
    const result: Record<string, T | null> = {};
    keys.forEach((key, index) => {
      result[key] = values[index];
    });
    
    return result;
  },

  batchSet: async (items: Array<{ key: string; value: any; ttl?: number }>): Promise<void> => {
    const cache = getCache();
    const keyValuePairs: Array<[string, any, number?]> = items.map(item => [
      item.key,
      item.value,
      item.ttl,
    ]);
    
    await cache.mset(keyValuePairs);
  },
};
