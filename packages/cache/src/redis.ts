import Redis from 'ioredis';
import { <PERSON>acheProvider, CacheConfig, CacheStats, CacheHealthCheck } from './types';
import { compress, decompress } from './utils';

export class RedisCache implements CacheProvider {
  private redis: Redis;
  private config: CacheConfig;
  private stats: CacheStats;

  constructor(config: CacheConfig) {
    this.config = config;
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      hitRate: 0,
    };

    this.redis = new Redis({
      host: config.redis?.host || 'localhost',
      port: config.redis?.port || 6379,
      password: config.redis?.password,
      db: config.redis?.db || 0,
      keyPrefix: config.redis?.keyPrefix || 'edu:',
      maxRetriesPerRequest: config.redis?.maxRetriesPerRequest || 3,
      lazyConnect: true,
      enableReadyCheck: true,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.redis.on('connect', () => {
      console.log('Redis connected');
    });

    this.redis.on('error', (error) => {
      console.error('Redis error:', error);
    });

    this.redis.on('close', () => {
      console.log('Redis connection closed');
    });

    this.redis.on('reconnecting', () => {
      console.log('Redis reconnecting...');
    });
  }

  async get<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      
      if (value === null) {
        this.stats.misses++;
        this.updateHitRate();
        return null;
      }

      this.stats.hits++;
      this.updateHitRate();

      // Try to parse as JSON, handle compression
      try {
        const parsed = JSON.parse(value);
        
        // Check if value is compressed
        if (parsed._compressed) {
          return decompress(parsed.data) as T;
        }
        
        return parsed as T;
      } catch {
        // Return as string if not JSON
        return value as T;
      }
    } catch (error) {
      console.error('Redis get error:', error);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      let serializedValue: string;
      const shouldCompress = this.config.enableCompression && 
                           typeof value === 'object' && 
                           JSON.stringify(value).length > 1024; // Compress if > 1KB

      if (shouldCompress) {
        const compressed = compress(value);
        serializedValue = JSON.stringify({
          _compressed: true,
          data: compressed,
        });
      } else {
        serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      }

      const expiration = ttl || this.config.defaultTTL || 3600; // Default 1 hour

      if (expiration > 0) {
        await this.redis.setex(key, expiration, serializedValue);
      } else {
        await this.redis.set(key, serializedValue);
      }

      this.stats.sets++;
    } catch (error) {
      console.error('Redis set error:', error);
      throw error;
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.redis.del(key);
      this.stats.deletes++;
    } catch (error) {
      console.error('Redis del error:', error);
      throw error;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Redis exists error:', error);
      return false;
    }
  }

  async clear(pattern?: string): Promise<void> {
    try {
      if (pattern) {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      } else {
        await this.redis.flushdb();
      }
    } catch (error) {
      console.error('Redis clear error:', error);
      throw error;
    }
  }

  async mget<T = any>(keys: string[]): Promise<(T | null)[]> {
    try {
      const values = await this.redis.mget(...keys);
      
      return values.map((value, index) => {
        if (value === null) {
          this.stats.misses++;
          return null;
        }

        this.stats.hits++;
        
        try {
          const parsed = JSON.parse(value);
          
          if (parsed._compressed) {
            return decompress(parsed.data) as T;
          }
          
          return parsed as T;
        } catch {
          return value as T;
        }
      });
    } catch (error) {
      console.error('Redis mget error:', error);
      this.stats.misses += keys.length;
      return keys.map(() => null);
    } finally {
      this.updateHitRate();
    }
  }

  async mset(keyValuePairs: Array<[string, any, number?]>): Promise<void> {
    try {
      const pipeline = this.redis.pipeline();

      for (const [key, value, ttl] of keyValuePairs) {
        let serializedValue: string;
        const shouldCompress = this.config.enableCompression && 
                             typeof value === 'object' && 
                             JSON.stringify(value).length > 1024;

        if (shouldCompress) {
          const compressed = compress(value);
          serializedValue = JSON.stringify({
            _compressed: true,
            data: compressed,
          });
        } else {
          serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
        }

        const expiration = ttl || this.config.defaultTTL || 3600;

        if (expiration > 0) {
          pipeline.setex(key, expiration, serializedValue);
        } else {
          pipeline.set(key, serializedValue);
        }
      }

      await pipeline.exec();
      this.stats.sets += keyValuePairs.length;
    } catch (error) {
      console.error('Redis mset error:', error);
      throw error;
    }
  }

  async incr(key: string, value: number = 1): Promise<number> {
    try {
      if (value === 1) {
        return await this.redis.incr(key);
      } else {
        return await this.redis.incrby(key, value);
      }
    } catch (error) {
      console.error('Redis incr error:', error);
      throw error;
    }
  }

  async expire(key: string, ttl: number): Promise<void> {
    try {
      await this.redis.expire(key, ttl);
    } catch (error) {
      console.error('Redis expire error:', error);
      throw error;
    }
  }

  async ttl(key: string): Promise<number> {
    try {
      return await this.redis.ttl(key);
    } catch (error) {
      console.error('Redis ttl error:', error);
      return -1;
    }
  }

  // Redis-specific methods
  async pipeline() {
    return this.redis.pipeline();
  }

  async invalidateByPattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
        this.stats.deletes += keys.length;
      }
    } catch (error) {
      console.error('Redis invalidateByPattern error:', error);
      throw error;
    }
  }

  async invalidateByTags(tags: string[]): Promise<void> {
    try {
      const patterns = tags.map(tag => `*:tag:${tag}:*`);
      
      for (const pattern of patterns) {
        await this.invalidateByPattern(pattern);
      }
    } catch (error) {
      console.error('Redis invalidateByTags error:', error);
      throw error;
    }
  }

  getStats(): CacheStats {
    return { ...this.stats };
  }

  async getHealthCheck(): Promise<CacheHealthCheck> {
    const startTime = Date.now();
    
    try {
      // Test basic operations
      const testKey = 'health:check';
      await this.redis.setex(testKey, 10, 'test');
      const value = await this.redis.get(testKey);
      await this.redis.del(testKey);
      
      const latency = Date.now() - startTime;
      
      // Get memory info
      const info = await this.redis.info('memory');
      const memoryMatch = info.match(/used_memory:(\d+)/);
      const memoryUsage = memoryMatch ? parseInt(memoryMatch[1]) : 0;
      
      // Get key count
      const keyCount = await this.redis.dbsize();
      
      return {
        status: latency < 100 ? 'healthy' : latency < 500 ? 'degraded' : 'unhealthy',
        latency,
        memoryUsage,
        keyCount,
        errors: [],
        lastCheck: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        latency: Date.now() - startTime,
        memoryUsage: 0,
        keyCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        lastCheck: new Date().toISOString(),
      };
    }
  }

  async disconnect(): Promise<void> {
    await this.redis.quit();
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  // Education-specific helper methods
  async cacheUserPermissions(userId: string, orgId: string, permissions: any, ttl: number = 900): Promise<void> {
    const key = `user:permissions:${userId}:${orgId}`;
    await this.set(key, permissions, ttl);
  }

  async getUserPermissions(userId: string, orgId: string): Promise<any | null> {
    const key = `user:permissions:${userId}:${orgId}`;
    return await this.get(key);
  }

  async cacheDashboardMetrics(orgId: string, period: string, metrics: any, ttl: number = 600): Promise<void> {
    const key = `dashboard:metrics:${orgId}:${period}`;
    await this.set(key, metrics, ttl);
  }

  async getDashboardMetrics(orgId: string, period: string): Promise<any | null> {
    const key = `dashboard:metrics:${orgId}:${period}`;
    return await this.get(key);
  }

  async invalidateUserCache(userId: string): Promise<void> {
    await this.invalidateByPattern(`user:*:${userId}:*`);
    await this.invalidateByPattern(`*:user:${userId}`);
  }

  async invalidateOrganizationCache(orgId: string): Promise<void> {
    await this.invalidateByPattern(`*:${orgId}:*`);
    await this.invalidateByPattern(`org:${orgId}:*`);
    await this.invalidateByPattern(`dashboard:*:${orgId}:*`);
  }

  async invalidateLearnerCache(learnerId: string): Promise<void> {
    await this.invalidateByPattern(`learner:*:${learnerId}:*`);
    await this.invalidateByPattern(`*:learner:${learnerId}`);
  }
}
