// Cache Types and Interfaces

export interface CacheConfig {
  redis?: {
    host: string;
    port: number;
    password?: string;
    db?: number;
    keyPrefix?: string;
    maxRetriesPerRequest?: number;
  };
  memory?: {
    maxSize?: number;
    ttl?: number;
  };
  defaultTTL?: number;
  enableCompression?: boolean;
}

export interface CacheProvider {
  get<T = any>(key: string): Promise<T | null>;
  set(key: string, value: any, ttl?: number): Promise<void>;
  del(key: string): Promise<void>;
  exists(key: string): Promise<boolean>;
  clear(pattern?: string): Promise<void>;
  mget<T = any>(keys: string[]): Promise<(T | null)[]>;
  mset(keyValuePairs: Array<[string, any, number?]>): Promise<void>;
  incr(key: string, value?: number): Promise<number>;
  expire(key: string, ttl: number): Promise<void>;
  ttl(key: string): Promise<number>;
}

export interface CacheOptions {
  ttl?: number;
  compress?: boolean;
  tags?: string[];
  namespace?: string;
}

export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  hitRate: number;
  memoryUsage?: number;
  keyCount?: number;
}

export interface CacheEntry<T = any> {
  value: T;
  createdAt: number;
  expiresAt: number;
  tags?: string[];
  compressed?: boolean;
}

// Education-specific cache keys
export const CACHE_KEYS = {
  // Dashboard
  DASHBOARD_METRICS: (orgId: string, period: string) => `dashboard:metrics:${orgId}:${period}`,
  DASHBOARD_ANALYTICS: (orgId: string, metric: string, period: string) => `dashboard:analytics:${orgId}:${metric}:${period}`,
  
  // Learners
  LEARNER_DETAILS: (learnerId: string) => `learner:details:${learnerId}`,
  LEARNER_ATTENDANCE: (learnerId: string, programId?: string) => `learner:attendance:${learnerId}${programId ? `:${programId}` : ''}`,
  LEARNER_FEES: (learnerId: string) => `learner:fees:${learnerId}`,
  
  // Programs
  PROGRAM_DETAILS: (programId: string) => `program:details:${programId}`,
  PROGRAM_LEARNERS: (programId: string) => `program:learners:${programId}`,
  PROGRAM_ATTENDANCE: (programId: string, date: string) => `program:attendance:${programId}:${date}`,
  
  // Organizations
  ORG_STATS: (orgId: string) => `org:stats:${orgId}`,
  ORG_PROGRAMS: (orgId: string) => `org:programs:${orgId}`,
  ORG_LEARNERS: (orgId: string, status?: string) => `org:learners:${orgId}${status ? `:${status}` : ''}`,
  
  // Guardians
  GUARDIAN_CHILDREN: (guardianId: string) => `guardian:children:${guardianId}`,
  GUARDIAN_MESSAGES: (guardianId: string) => `guardian:messages:${guardianId}`,
  
  // Events
  EVENT_DETAILS: (eventId: string) => `event:details:${eventId}`,
  EVENT_REGISTRATIONS: (eventId: string) => `event:registrations:${eventId}`,
  ORG_EVENTS: (orgId: string, upcoming?: boolean) => `org:events:${orgId}${upcoming ? ':upcoming' : ''}`,
  
  // QR Sessions
  QR_SESSION: (sessionId: string) => `qr:session:${sessionId}`,
  QR_PROGRAM_SESSIONS: (programId: string) => `qr:program:${programId}`,
  
  // Payments
  PAYMENT_STATUS: (paymentId: string) => `payment:status:${paymentId}`,
  FEE_PAYMENTS: (feeId: string) => `fee:payments:${feeId}`,
  
  // User sessions
  USER_PERMISSIONS: (userId: string, orgId: string) => `user:permissions:${userId}:${orgId}`,
  USER_ROLE: (userId: string, accountId: string) => `user:role:${userId}:${accountId}`,
} as const;

export const CACHE_TTL = {
  SHORT: 5 * 60, // 5 minutes
  MEDIUM: 15 * 60, // 15 minutes
  LONG: 60 * 60, // 1 hour
  VERY_LONG: 24 * 60 * 60, // 24 hours
  
  // Specific TTLs
  DASHBOARD_METRICS: 10 * 60, // 10 minutes
  LEARNER_DATA: 30 * 60, // 30 minutes
  PROGRAM_DATA: 60 * 60, // 1 hour
  USER_PERMISSIONS: 15 * 60, // 15 minutes
  QR_SESSION: 5 * 60, // 5 minutes
  PAYMENT_STATUS: 2 * 60, // 2 minutes
} as const;

export const CACHE_TAGS = {
  ORGANIZATION: (orgId: string) => `org:${orgId}`,
  LEARNER: (learnerId: string) => `learner:${learnerId}`,
  PROGRAM: (programId: string) => `program:${programId}`,
  USER: (userId: string) => `user:${userId}`,
  GUARDIAN: (guardianId: string) => `guardian:${guardianId}`,
  EVENT: (eventId: string) => `event:${eventId}`,
  PAYMENT: (paymentId: string) => `payment:${paymentId}`,
  DASHBOARD: 'dashboard',
  ANALYTICS: 'analytics',
} as const;

// Cache invalidation patterns
export interface CacheInvalidationRule {
  pattern: string;
  triggers: string[];
  dependencies?: string[];
}

export const CACHE_INVALIDATION_RULES: CacheInvalidationRule[] = [
  {
    pattern: 'dashboard:*',
    triggers: ['learner:created', 'learner:updated', 'attendance:created', 'fee:paid', 'enrollment:created'],
  },
  {
    pattern: 'learner:*',
    triggers: ['learner:updated', 'attendance:created', 'fee:created', 'enrollment:created'],
  },
  {
    pattern: 'program:*',
    triggers: ['program:updated', 'enrollment:created', 'attendance:created'],
  },
  {
    pattern: 'org:*',
    triggers: ['learner:created', 'program:created', 'event:created'],
  },
  {
    pattern: 'guardian:*',
    triggers: ['learner:updated', 'attendance:created', 'message:sent'],
  },
];

export type CacheEvent = 
  | 'learner:created' | 'learner:updated' | 'learner:deleted'
  | 'program:created' | 'program:updated' | 'program:deleted'
  | 'attendance:created' | 'attendance:updated'
  | 'fee:created' | 'fee:paid' | 'fee:updated'
  | 'enrollment:created' | 'enrollment:updated'
  | 'event:created' | 'event:updated'
  | 'message:sent' | 'message:read'
  | 'payment:completed' | 'payment:failed'
  | 'qr:scanned' | 'qr:expired';

export interface CacheMetrics {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  hitRate: number;
  averageResponseTime: number;
  memoryUsage: number;
  keyCount: number;
  evictions: number;
  errors: number;
}

export interface CacheHealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency: number;
  memoryUsage: number;
  keyCount: number;
  errors: string[];
  lastCheck: string;
}
