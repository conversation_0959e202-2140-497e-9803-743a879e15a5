import { gzipSync, gunzipSync } from 'zlib';

// Compression utilities
export function compress(data: any): string {
  const jsonString = JSON.stringify(data);
  const compressed = gzipSync(Buffer.from(jsonString, 'utf8'));
  return compressed.toString('base64');
}

export function decompress(compressedData: string): any {
  const buffer = Buffer.from(compressedData, 'base64');
  const decompressed = gunzipSync(buffer);
  return JSON.parse(decompressed.toString('utf8'));
}

// Cache key utilities
export function generateCacheKey(namespace: string, ...parts: (string | number)[]): string {
  return [namespace, ...parts.map(p => String(p))].join(':');
}

export function extractNamespace(key: string): string {
  return key.split(':')[0];
}

export function matchesPattern(key: string, pattern: string): boolean {
  // Convert glob pattern to regex
  const regexPattern = pattern
    .replace(/\*/g, '.*')
    .replace(/\?/g, '.')
    .replace(/\[([^\]]+)\]/g, '[$1]');
  
  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(key);
}

// Serialization utilities
export function serialize(value: any): string {
  if (typeof value === 'string') {
    return value;
  }
  
  try {
    return JSON.stringify(value);
  } catch (error) {
    throw new Error(`Failed to serialize value: ${error}`);
  }
}

export function deserialize<T = any>(value: string): T {
  try {
    return JSON.parse(value);
  } catch {
    // Return as string if not valid JSON
    return value as T;
  }
}

// TTL utilities
export function calculateTTL(baseTime: number, jitter: boolean = true): number {
  if (!jitter) {
    return baseTime;
  }
  
  // Add random jitter of ±10% to prevent cache stampede
  const jitterRange = baseTime * 0.1;
  const jitterValue = (Math.random() - 0.5) * 2 * jitterRange;
  
  return Math.max(1, Math.floor(baseTime + jitterValue));
}

export function isExpired(expiresAt: number): boolean {
  return Date.now() > expiresAt;
}

// Hash utilities for consistent key generation
export function hashKey(input: string): string {
  let hash = 0;
  if (input.length === 0) return hash.toString();
  
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString(36);
}

// Memory size utilities
export function getObjectSize(obj: any): number {
  const jsonString = JSON.stringify(obj);
  return Buffer.byteLength(jsonString, 'utf8');
}

export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Performance monitoring utilities
export class PerformanceTimer {
  private startTime: number;
  
  constructor() {
    this.startTime = performance.now();
  }
  
  elapsed(): number {
    return performance.now() - this.startTime;
  }
  
  reset(): void {
    this.startTime = performance.now();
  }
}

export function measureAsync<T>(
  operation: () => Promise<T>
): Promise<{ result: T; duration: number }> {
  const timer = new PerformanceTimer();
  
  return operation().then(result => ({
    result,
    duration: timer.elapsed(),
  }));
}

// Cache warming utilities
export interface WarmupTask {
  key: string;
  generator: () => Promise<any>;
  ttl?: number;
  priority?: number;
}

export class CacheWarmer {
  private tasks: WarmupTask[] = [];
  
  addTask(task: WarmupTask): void {
    this.tasks.push(task);
    // Sort by priority (higher first)
    this.tasks.sort((a, b) => (b.priority || 0) - (a.priority || 0));
  }
  
  async warmup(cacheProvider: any, concurrency: number = 5): Promise<void> {
    const chunks = this.chunkArray(this.tasks, concurrency);
    
    for (const chunk of chunks) {
      await Promise.all(
        chunk.map(async (task) => {
          try {
            const value = await task.generator();
            await cacheProvider.set(task.key, value, task.ttl);
          } catch (error) {
            console.error(`Failed to warm cache for key ${task.key}:`, error);
          }
        })
      );
    }
  }
  
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}

// Rate limiting utilities
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  constructor(
    private maxRequests: number,
    private windowMs: number
  ) {}
  
  isAllowed(key: string): boolean {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    
    // Get existing requests for this key
    const requests = this.requests.get(key) || [];
    
    // Filter out old requests
    const validRequests = requests.filter(time => time > windowStart);
    
    // Check if we're under the limit
    if (validRequests.length >= this.maxRequests) {
      return false;
    }
    
    // Add current request
    validRequests.push(now);
    this.requests.set(key, validRequests);
    
    return true;
  }
  
  reset(key?: string): void {
    if (key) {
      this.requests.delete(key);
    } else {
      this.requests.clear();
    }
  }
}

// Circuit breaker for cache operations
export class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';
  
  constructor(
    private failureThreshold: number = 5,
    private recoveryTimeMs: number = 60000
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeMs) {
        this.state = 'half-open';
      } else {
        throw new Error('Circuit breaker is open');
      }
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'closed';
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.failureThreshold) {
      this.state = 'open';
    }
  }
  
  getState(): string {
    return this.state;
  }
  
  reset(): void {
    this.failures = 0;
    this.state = 'closed';
    this.lastFailureTime = 0;
  }
}

// Education-specific cache utilities
export const EducationCacheUtils = {
  // Generate cache keys for common education entities
  learnerKey: (learnerId: string, suffix?: string) => 
    generateCacheKey('learner', learnerId, suffix || 'details'),
  
  programKey: (programId: string, suffix?: string) => 
    generateCacheKey('program', programId, suffix || 'details'),
  
  organizationKey: (orgId: string, suffix?: string) => 
    generateCacheKey('org', orgId, suffix || 'stats'),
  
  dashboardKey: (orgId: string, period: string, metric?: string) => 
    generateCacheKey('dashboard', orgId, period, metric || 'overview'),
  
  attendanceKey: (learnerId: string, programId?: string, date?: string) => {
    const parts = ['attendance', learnerId];
    if (programId) parts.push(programId);
    if (date) parts.push(date);
    return parts.join(':');
  },
  
  // Calculate appropriate TTL based on data type
  getTTL: (dataType: string): number => {
    const ttlMap: Record<string, number> = {
      'user-permissions': 15 * 60, // 15 minutes
      'dashboard-metrics': 10 * 60, // 10 minutes
      'learner-details': 30 * 60, // 30 minutes
      'program-details': 60 * 60, // 1 hour
      'attendance-summary': 60 * 60, // 1 hour
      'fee-summary': 30 * 60, // 30 minutes
      'event-details': 60 * 60, // 1 hour
      'qr-session': 5 * 60, // 5 minutes
      'payment-status': 2 * 60, // 2 minutes
    };
    
    return calculateTTL(ttlMap[dataType] || 30 * 60); // Default 30 minutes with jitter
  },
  
  // Create cache tags for invalidation
  createTags: (entityType: string, entityId: string, ...additionalTags: string[]): string[] => {
    return [`${entityType}:${entityId}`, ...additionalTags];
  },
  
  // Check if data should be cached based on size and type
  shouldCache: (data: any, maxSize: number = 1024 * 1024): boolean => {
    if (!data) return false;
    
    const size = getObjectSize(data);
    return size <= maxSize;
  },
};
