{"name": "@kit/cache", "version": "0.1.0", "private": true, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext .ts,.tsx", "typecheck": "tsc --noEmit"}, "dependencies": {"@kit/shared": "workspace:*", "ioredis": "^5.3.2", "zod": "^3.24.2"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@types/node": "^22.14.0", "eslint": "^9.24.0", "typescript": "^5.8.3"}, "exports": {".": "./dist/index.js", "./redis": "./dist/redis.js", "./memory": "./dist/memory.js", "./types": "./dist/types.js"}, "typesVersions": {"*": {".": ["./dist/index.d.ts"], "redis": ["./dist/redis.d.ts"], "memory": ["./dist/memory.d.ts"], "types": ["./dist/types.d.ts"]}}}