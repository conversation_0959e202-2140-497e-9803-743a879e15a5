import { format, parseISO } from 'date-fns';
import { enUS, vi } from 'date-fns/locale';

export function formatDate(
  dateString: string | null | undefined,
  formatString: string = 'PP',
  locale: string = 'vi',
) {
  if (!dateString) {
    return '-';
  }
  try {
    const options = locale
      ? { locale: locale === 'vi' ? vi : enUS }
      : undefined;
    return format(parseISO(dateString), formatString, options);
  } catch (error) {
    console.error('Error formatting date:', error, { dateString });
    return '-';
  }
}

export function formatDateTime(
  dateString: string | null | undefined,
  locale: string = 'vi',
) {
  return formatDate(dateString, 'PPpp', locale);
}

export function formatDateFromObject(
  date: Date | null | undefined,
  formatString: string = 'PP',
  locale: string = 'vi',
) {
  if (!date) {
    return '-';
  }
  try {
    const options = locale
      ? { locale: locale === 'vi' ? vi : enUS }
      : undefined;
    return format(date, formatString, options);
  } catch (error) {
    console.error('Error formatting date:', error, { date });
    return '-';
  }
}

export function formatDateTimeFromObject(
  date: Date | null | undefined,
  locale: string = 'vi',
) {
  return formatDateFromObject(date, 'PPpp', locale);
}
