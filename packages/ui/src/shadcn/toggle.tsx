'use client';

import * as React from 'react';

import { type VariantProps, cva } from 'class-variance-authority';

import { cn } from '../lib/utils';

const toggleVariants = cva(
  'focus-visible:ring-ring data-[state=on]:bg-accent data-[state=on]:text-accent-foreground inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'hover:bg-muted hover:text-muted-foreground bg-transparent',
        outline:
          'border-input hover:bg-accent hover:text-accent-foreground border bg-transparent',
      },
      size: {
        default: 'h-10 px-3',
        sm: 'h-9 px-2.5',
        lg: 'h-11 px-5',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

export interface ToggleProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof toggleVariants> {
  pressed?: boolean;
  onPressedChange?: (pressed: boolean) => void;
}

const Toggle = React.forwardRef<HTMLButtonElement, ToggleProps>(
  (
    { className, variant, size, pressed, onPressedChange, onClick, ...props },
    ref,
  ) => {
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      onClick?.(event);
      onPressedChange?.(!pressed);
    };

    return (
      <button
        ref={ref}
        type="button"
        data-state={pressed ? 'on' : 'off'}
        onClick={handleClick}
        className={cn(toggleVariants({ variant, size, className }))}
        {...props}
      />
    );
  },
);

Toggle.displayName = 'Toggle';

export { Toggle, toggleVariants };
