'use client';

import { forwardRef, useEffect, useRef } from 'react';

import { useTranslation } from 'react-i18next';

import { cn } from '../lib/utils';
import { Input } from './input';

// Define default currency configuration
const defaultCurrencyConfig = {
  defaultCurrency: 'VND',
  currencyFormats: {
    VND: { symbol: 'đ', position: 'suffix', decimalPlaces: 0 },
    USD: { symbol: '$', position: 'prefix', decimalPlaces: 2 },
    EUR: { symbol: '€', position: 'prefix', decimalPlaces: 2 },
  },
};

// Use the default currency config
const currencyConfig = defaultCurrencyConfig;

interface PriceInputProps {
  value: string | number;
  onChange: (value: number) => void;
  placeholder?: string;
  className?: string;
  decimals?: number;
  currencyCode?: string;
}

export const PriceInput = forwardRef<HTMLInputElement, PriceInputProps>(
  (
    {
      value,
      onChange,
      placeholder = '0',
      className = '',
      decimals = 0,
      currencyCode,
      ...props
    },
    ref,
  ) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const { i18n } = useTranslation();
    const currency = currencyCode || currencyConfig?.defaultCurrency || 'VND';
    const currencyFormat = currencyConfig?.currencyFormats?.[currency] || {
      symbol: 'đ',
      position: 'suffix',
      decimalPlaces: 0,
    };

    const formatValue = (val: string | number) => {
      if (!val) return '';

      // Use the configured decimal places if available
      const decimalPlaces = currencyFormat?.decimalPlaces ?? decimals;

      // Fallback to Intl.NumberFormat for string values or if formatCurrency fails
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces,
      }).format(Number(val));
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const input = e.target;
      const rawValue = input.value.replace(/[^\d.]/g, '');
      const parts = rawValue.split('.');

      if (parts.length > 2) return;
      if (parts[1]?.length > decimals) return;

      const numValue = parseFloat(rawValue);
      if (!isNaN(numValue)) {
        onChange(numValue);
      }

      const cursorPosition = input.selectionStart;
      const formattedValue = formatValue(rawValue);
      input.value = formattedValue;

      const addedSeparators =
        (formattedValue.match(/[,\.]/g) || []).length -
        (input.value.match(/[,\.]/g) || []).length;
      const newPosition = cursorPosition! + addedSeparators;
      input.setSelectionRange(newPosition, newPosition);
    };

    useEffect(() => {
      if (inputRef.current && value) {
        inputRef.current.value = formatValue(value);
      }
    }, [value, i18n.language, decimals]);

    return (
      <div className="relative">
        <Input
          {...props}
          ref={(node) => {
            inputRef.current = node;
            if (typeof ref === 'function') {
              ref(node);
            } else if (ref) {
              ref.current = node;
            }
          }}
          type="text"
          defaultValue={formatValue(value)}
          onChange={handleChange}
          placeholder={placeholder}
          className={cn(
            currencyFormat?.position === 'prefix'
              ? 'pl-10 text-left'
              : 'pr-14 text-right',
            className,
          )}
        />
        <span
          className={`text-muted-foreground absolute top-1/2 ${currencyFormat?.position === 'prefix' ? 'left-3' : 'right-3'} -translate-y-1/2`}
        >
          {currencyFormat?.symbol || 'đ'}
        </span>
      </div>
    );
  },
);

PriceInput.displayName = 'PriceInput';
