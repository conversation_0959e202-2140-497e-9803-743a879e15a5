'use client';

import * as React from 'react';

import { Calendar as CalendarI<PERSON>, Clock } from 'lucide-react';
import { format } from 'date-fns';
import { vi, enUS } from 'date-fns/locale';

import { cn } from '../lib/utils';
import { Button } from './button';
import { Calendar } from './calendar';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';

interface DateTimePickerProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  locale?: string;
  disabled?: boolean;
  minDate?: Date;
}

export function DateTimePicker({
  date,
  setDate,
  locale = 'vi',
  disabled = false,
  minDate,
}: DateTimePickerProps) {
  const [selectedDateTime, setSelectedDateTime] = React.useState<Date | undefined>(date);
  const [isCalendarOpen, setIsCalendarOpen] = React.useState(false);
  const [formattedDate, setFormattedDate] = React.useState<string>('');

  // Update the selected date when the date prop changes
  React.useEffect(() => {
    setSelectedDateTime(date);
  }, [date]);

  // Format date only on client-side to avoid hydration mismatch
  React.useEffect(() => {
    if (date) {
      setFormattedDate(formatSelectedDate(date));
    } else {
      setFormattedDate('');
    }
  }, [date, locale]);

  // Generate hours and minutes options
  const hours = Array.from({ length: 24 }, (_, i) => i);
  const minutes = Array.from({ length: 60 }, (_, i) => i);

  // Format the selected date for display
  const formatSelectedDate = (date: Date | undefined) => {
    if (!date) return '';

    return format(date, 'PPpp', {
      locale: locale === 'vi' ? vi : enUS,
    });
  };

  // Handle date selection from calendar
  const handleSelectDate = (date: Date | undefined) => {
    if (!date) {
      setSelectedDateTime(undefined);
      return;
    }

    // Preserve the time from the previously selected date if it exists
    if (selectedDateTime) {
      date.setHours(selectedDateTime.getHours());
      date.setMinutes(selectedDateTime.getMinutes());
    }

    setSelectedDateTime(date);
  };

  // Handle hour selection
  const handleSelectHour = (hour: string) => {
    if (!selectedDateTime) return;

    const newDate = new Date(selectedDateTime);
    newDate.setHours(parseInt(hour));
    setSelectedDateTime(newDate);
  };

  // Handle minute selection
  const handleSelectMinute = (minute: string) => {
    if (!selectedDateTime) return;

    const newDate = new Date(selectedDateTime);
    newDate.setMinutes(parseInt(minute));
    setSelectedDateTime(newDate);
  };

  // Update the parent component's date when the popover is closed
  const handleOpenChange = (open: boolean) => {
    setIsCalendarOpen(open);
    if (!open) {
      setDate(selectedDateTime);
    }
  };

  return (
    <Popover open={isCalendarOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground",
            disabled && "pointer-events-none opacity-50"
          )}
          disabled={disabled}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {formattedDate || (locale === 'vi' ? "Chọn ngày và giờ" : "Select date and time")}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{locale === 'vi' ? "Ngày" : "Date"}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{locale === 'vi' ? "Giờ" : "Time"}</span>
            </div>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row">
          <Calendar
            mode="single"
            selected={selectedDateTime}
            onSelect={handleSelectDate}
            disabled={(date) => {
              if (disabled) return true;
              if (minDate && date < minDate) return true;
              return false;
            }}
            initialFocus
          />
          <div className="border-t border-border p-3 sm:border-l sm:border-t-0">
            <div className="flex flex-col space-y-3">
              <div className="space-y-1">
                <span className="text-xs text-muted-foreground">{locale === 'vi' ? "Giờ" : "Hour"}</span>
                <Select
                  value={selectedDateTime ? selectedDateTime.getHours().toString() : undefined}
                  onValueChange={handleSelectHour}
                  disabled={!selectedDateTime || disabled}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={locale === 'vi' ? "Giờ" : "Hour"} />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px]">
                    {hours.map((hour) => (
                      <SelectItem key={hour} value={hour.toString()}>
                        {hour.toString().padStart(2, '0')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-1">
                <span className="text-xs text-muted-foreground">{locale === 'vi' ? "Phút" : "Minute"}</span>
                <Select
                  value={selectedDateTime ? selectedDateTime.getMinutes().toString() : undefined}
                  onValueChange={handleSelectMinute}
                  disabled={!selectedDateTime || disabled}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={locale === 'vi' ? "Phút" : "Minute"} />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px]">
                    {minutes.map((minute) => (
                      <SelectItem key={minute} value={minute.toString()}>
                        {minute.toString().padStart(2, '0')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>
        <div className="border-t border-border p-3">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSelectedDateTime(undefined);
                setDate(undefined);
                setIsCalendarOpen(false);
              }}
            >
              {locale === 'vi' ? "Xóa" : "Clear"}
            </Button>
            <Button
              size="sm"
              onClick={() => {
                setDate(selectedDateTime);
                setIsCalendarOpen(false);
              }}
            >
              {locale === 'vi' ? "Áp dụng" : "Apply"}
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
