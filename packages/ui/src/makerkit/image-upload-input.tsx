'use client';

import type { FormEvent } from 'react';
import { useCallback, useRef, useState } from 'react';

import { UploadCloud } from 'lucide-react';

import { cn } from '../lib/utils';

type Props = Omit<React.InputHTMLAttributes<unknown>, 'value'> & {
  image?: string | null;
  onClear?: () => void;
  onValueChange?: (props: { images: string[]; files: File[] }) => void;
  visible?: boolean;
  multiple?: boolean;
} & React.ComponentPropsWithRef<'input'>;

export const ImageUploadInput: React.FC<Props> =
  function ImageUploadInputComponent({
    children,
    image,
    onClear,
    onInput,
    onValueChange,
    ref: forwardedRef,
    visible = true,
    multiple = false,
    className,
    ...props
  }) {
    const localRef = useRef<HTMLInputElement>(null);
    const dropZoneRef = useRef<HTMLDivElement>(null);
    const [isDragging, setIsDragging] = useState(false);

    const processFiles = useCallback(
      (files: FileList | File[]) => {
        if (!files.length) return;

        const processedFiles = Array.from(files);
        const imageUrls = processedFiles.map((file) =>
          URL.createObjectURL(file),
        );

        if (onValueChange) {
          onValueChange({
            images: imageUrls,
            files: processedFiles,
          });
        }

        if (onInput) {
          // Create a synthetic event
          const event = {
            currentTarget: {
              files: processedFiles,
            },
          } as unknown as FormEvent<HTMLInputElement>;
          onInput(event);
        }
      },
      [onInput, onValueChange],
    );

    const onInputChange = useCallback(
      (e: FormEvent<HTMLInputElement>) => {
        e.preventDefault();
        const files = e.currentTarget.files;
        if (files) {
          processFiles(files);
        }
      },
      [processFiles],
    );

    const handleDragEnter = useCallback((e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(true);
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);
    }, []);

    const handleDragOver = useCallback((e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
    }, []);

    const handleDrop = useCallback(
      (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);

        const files = Array.from(e.dataTransfer.files).filter((file) =>
          file.type.startsWith('image/'),
        );

        if (files.length > 0) {
          if (!multiple && files.length > 1) {
            // If multiple is false, only take the first image
            processFiles([files[0]]);
          } else {
            processFiles(files);
          }
        }
      },
      [multiple, processFiles],
    );

    const setRef = useCallback(
      (input: HTMLInputElement) => {
        localRef.current = input;
        if (typeof forwardedRef === 'function') {
          forwardedRef(localRef.current);
        }
      },
      [forwardedRef],
    );

    const Input = () => (
      <input
        {...props}
        className="hidden"
        ref={setRef}
        type="file"
        multiple={multiple}
        onChange={onInputChange}
        accept="image/*"
        aria-labelledby="image-upload-input"
      />
    );

    if (!visible) {
      return <Input />;
    }

    return (
      <div
        ref={dropZoneRef}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        className={cn(
          'relative rounded-lg border-2 border-dashed border-gray-300 p-6 transition-all',
          isDragging && 'border-primary bg-primary/5',
          className,
        )}
      >
        <Input />
        <div className="flex flex-col items-center justify-center space-y-2 text-center">
          <UploadCloud
            className={cn(
              'h-12 w-12 text-gray-400',
              isDragging && 'text-primary',
            )}
          />
          <div className="flex flex-col space-y-1">
            <span className="text-sm font-medium">
              Drop your images here, or{' '}
              <span className="text-primary cursor-pointer">browse</span>
            </span>
            <span className="text-xs text-gray-500">
              {multiple ? 'Support multiple images' : 'Only one image allowed'}
            </span>
          </div>
        </div>
        {children}
      </div>
    );
  };
