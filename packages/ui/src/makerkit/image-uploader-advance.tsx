'use client';

import React, { useCallback, useEffect, useState } from 'react';

import { ImageIcon, Upload, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import { cn } from '../lib/utils';
import { Button } from '../shadcn/button';
import { Progress } from '../shadcn/progress';
import { Trans } from './trans';

export interface ImageUploaderProps
  extends React.HTMLAttributes<HTMLDivElement> {
  value?: string[]; // Đổi thành string[] (URL của ảnh)
  onChange?: (files: File[] | null) => void | Promise<void>;
  onRemove?: (url: string) => void | Promise<void>;
  maxFiles?: number;
  accept?: string;
  bucket?: string;
  aspectRatio?: 'square' | '4/3' | '16/9';
}

export function ImageUploaderAdvance({
  value = [],
  onChange,
  onRemove,
  maxFiles = 1,
  accept = 'image/*',
  bucket,
  aspectRatio = 'square',
  className,
  ...props
}: ImageUploaderProps) {
  const { t } = useTranslation('ui');
  const [files, setFiles] = useState<File[]>([]);
  const [previews, setPreviews] = useState<string[]>(value);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number[]>([]);
  const aspectRatioClass = {
    square: '',
    '4/3': 'aspect-[4/3]',
    '16/9': 'aspect-[16/9]',
  }[aspectRatio];

  const handleFiles = useCallback(
    async (newFiles: FileList | null) => {
      if (!newFiles) {
        setFiles([]);
        setPreviews([]);
        onChange?.(null);
        return;
      }

      const updatedFiles = Array.from(newFiles).slice(
        0,
        maxFiles - files.length,
      );
      const newFilesArray = [...files, ...updatedFiles];
      setFiles(newFilesArray);

      // Simulate upload progress for each new file
      setUploadProgress(updatedFiles.map(() => 0));

      // Generate previews with simulated upload progress
      const newPreviews = await Promise.all(
        updatedFiles.map(async (file, index) => {
          for (let progress = 0; progress <= 100; progress += 10) {
            setUploadProgress((prev) => {
              const next = [...prev];
              next[index] = progress;
              return next;
            });
            await new Promise((resolve) => setTimeout(resolve, 100));
          }
          return URL.createObjectURL(file);
        }),
      );

      setPreviews((prev) => [...prev, ...newPreviews]);
      onChange?.(newFilesArray);
      setUploadProgress([]);
    },
    [maxFiles, files, onChange],
  );

  const removeFile = useCallback(
    (index: number) => {
      setFiles((prev) => {
        const next = [...prev];
        next.splice(index, 1);
        return next;
      });
      setPreviews((prev) => {
        const next = [...prev];
        next.splice(index, 1);
        return next;
      });
      const updatedFiles = files.filter((_, i) => i !== index);
      onChange?.(updatedFiles.length > 0 ? updatedFiles : null);
    },
    [files, onChange],
  );

  const handleRemove = async (index: number) => {
    const url = previews[index];

    // Call onRemove if provided
    if (onRemove) {
      await onRemove(url);
    }

    // Remove from local state
    removeFile(index);
  };

  useEffect(() => {
    return () => previews.forEach((preview) => URL.revokeObjectURL(preview));
  }, [previews]);

  useEffect(() => {
    setPreviews(value);
  }, [value]);

  return (
    <div
      className={cn(
        'group relative rounded-lg border-2 transition-all duration-200',
        isDragging
          ? 'border-primary scale-[1.02] border-dashed'
          : 'border-muted hover:border-muted-foreground/50',
        className,
      )}
      onDragOver={(e) => {
        e.preventDefault();
        setIsDragging(true);
      }}
      onDragLeave={() => setIsDragging(false)}
      onDrop={(e) => {
        e.preventDefault();
        setIsDragging(false);
        handleFiles(e.dataTransfer.files);
      }}
      {...props}
    >
      <input
        type="file"
        accept={accept}
        multiple={maxFiles > 1}
        className="absolute inset-0 z-10 h-full w-full cursor-pointer opacity-0"
        onChange={(e) => handleFiles(e.target.files)}
      />

      {previews.length > 0 ? (
        <div
          className={cn(
            'grid gap-4 p-4',
            maxFiles > 1
              ? 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-4'
              : 'grid-cols-1',
          )}
        >
          {previews.map((preview, index) => (
            <div
              key={preview}
              className={cn(
                'group/item relative overflow-hidden rounded-lg',
                aspectRatioClass,
              )}
            >
              <img
                src={preview}
                alt={`Preview ${index + 1}`}
                className="h-full w-full object-cover transition-transform group-hover/item:scale-105"
              />
              {uploadProgress[index] !== undefined && (
                <div className="bg-background/80 absolute inset-0 flex items-center justify-center">
                  <div className="w-3/4 space-y-2">
                    <Progress value={uploadProgress[index]} />
                    <p className="text-muted-foreground text-center text-xs">
                      <Trans
                        i18nKey="ui:imageUploader.uploading"
                        values={{ progress: uploadProgress[index] }}
                      >
                        Uploading... {uploadProgress[index]}%
                      </Trans>
                    </p>
                  </div>
                </div>
              )}
              <div className="absolute inset-0 bg-black/40 opacity-0 transition-opacity group-hover/item:opacity-100" />
              <Button
                size="icon"
                variant="destructive"
                className="absolute top-2 right-2 z-10 opacity-0 transition-opacity group-hover/item:opacity-100"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleRemove(index);
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
          {previews.length < maxFiles && (
            <div
              className={cn(
                'group/upload border-muted-foreground/25 hover:border-muted-foreground/50 relative overflow-hidden rounded-lg border-2 border-dashed transition-colors',
                aspectRatioClass,
              )}
            >
              <div className="text-muted-foreground absolute inset-0 flex flex-col items-center justify-center p-2">
                <Upload className="mb-1 h-6 w-6" />
                <p className="text-center text-sm font-medium">
                  <Trans
                    i18nKey={
                      previews.length === 0
                        ? 'ui:imageUploader.uploadImage'
                        : 'ui:imageUploader.addMore'
                    }
                  >
                    {previews.length === 0 ? 'Upload image' : 'Add more'}
                  </Trans>
                </p>
                {maxFiles > 1 && (
                  <p className="mt-0.5 text-center text-xs">
                    <Trans
                      i18nKey="ui:imageUploader.remaining"
                      values={{ count: maxFiles - previews.length }}
                    >
                      {maxFiles - previews.length} remaining
                    </Trans>
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div
          className={cn(
            'flex flex-col items-center justify-center p-8 text-center',
            aspectRatioClass,
          )}
        >
          <div className="bg-muted/30 mb-4 rounded-full p-3">
            <ImageIcon className="text-muted-foreground h-6 w-6" />
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium">
              <Trans i18nKey="ui:imageUploader.dropImages">
                Drop your images here or click to upload
              </Trans>
            </p>
            <p className="text-muted-foreground text-xs">
              {accept === 'image/*' && (
                <Trans i18nKey="ui:imageUploader.supportedFormats">
                  Supports JPG, PNG and GIF
                </Trans>
              )}
              {accept === 'image/*' ? ' • ' : ''}
              <Trans i18nKey="ui:imageUploader.maximumFiles" count={maxFiles}>
                Maximum {maxFiles} file{maxFiles > 1 ? 's' : ''}
              </Trans>
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
