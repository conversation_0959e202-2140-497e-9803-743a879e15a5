'use client';

import * as React from 'react';
import { ArrowUpIcon, ArrowDownIcon, ArrowRightIcon } from 'lucide-react';

import { cn } from '../lib/utils';

export interface TrendProps extends React.HTMLAttributes<HTMLSpanElement> {
  trend: 'up' | 'down' | 'neutral';
  children: React.ReactNode;
}

export function Trend({ trend, children, className, ...props }: TrendProps) {
  const trendColors = {
    up: 'text-green-600 bg-green-100',
    down: 'text-red-600 bg-red-100',
    neutral: 'text-blue-600 bg-blue-100',
  };

  const trendIcons = {
    up: <ArrowUpIcon className="h-3 w-3" />,
    down: <ArrowDownIcon className="h-3 w-3" />,
    neutral: <ArrowRightIcon className="h-3 w-3" />,
  };

  return (
    <span
      className={cn(
        'inline-flex items-center gap-1 rounded-full px-1.5 py-0.5 text-xs font-medium',
        trendColors[trend],
        className
      )}
      {...props}
    >
      {trendIcons[trend]}
      {children}
    </span>
  );
}
