'use client';

import { useCallback, useState } from 'react';
import { useIsMobile } from '../hooks/use-mobile';
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import type {
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  Row,
  SortingState,
  VisibilityState,
} from '@tanstack/react-table';

import { Button } from '../shadcn/button';
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from '../shadcn/table';
import { Card, CardContent } from '../shadcn/card';
import { Trans } from './trans';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface ReactTableProps<T extends object> {
  data: T[];
  columns: ColumnDef<T>[];
  renderSubComponent?: (props: { row: Row<T> }) => React.ReactElement;
  pageIndex?: number;
  pageSize?: number;
  pageCount?: number;
  onPaginationChange?: (pagination: PaginationState) => void;
  onSortingChange?: (sorting: SortingState) => void;
  manualPagination?: boolean;
  manualSorting?: boolean;
  sorting?: SortingState;
  tableProps?: React.ComponentProps<typeof Table> &
    Record<`data-${string}`, string>;
  rowAttributes?: (row: Row<T>) => Record<string, string>;
  // Thêm các props cho mobile view
  mobileCardFields?: string[]; // Các trường hiển thị trên mobile
  renderMobileCard?: (row: Row<T>) => React.ReactNode; // Custom render cho mobile card
}

function Pagination({ table }: { table: any }) {
  const isMobile = useIsMobile();
  const pageCount = table.getPageCount();

  // Ẩn pagination nếu chỉ có 1 trang
  if (pageCount <= 1) {
    return null;
  }

  return (
    <div className="flex items-center justify-between px-2">
      <div className="flex-1 text-sm text-muted-foreground">
        <Trans
          i18nKey="common:table.pagination"
          defaults="Page {{ page }} of {{ count }}"
          values={{
            page: table.getState().pagination.pageIndex + 1,
            count: pageCount,
          }}
        />
      </div>
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only">Go to first page</span>
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="hidden h-8 w-8 p-0 lg:flex"
            onClick={() => table.setPageIndex(pageCount - 1)}
            disabled={!table.getCanNextPage()}
          >
            <span className="sr-only">Go to last page</span>
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export function ResponsiveDataTable<T extends object>({
  data,
  columns,
  pageIndex,
  pageSize,
  pageCount,
  onPaginationChange,
  onSortingChange,
  tableProps,
  manualPagination = true,
  manualSorting = false,
  sorting: initialSorting,
  rowAttributes,
  mobileCardFields = [],
  renderMobileCard,
}: ReactTableProps<T>) {
  'use no memo';

  const isMobile = useIsMobile();

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: pageIndex ?? 0,
    pageSize: pageSize ?? 15,
  });

  const [sorting, setSorting] = useState<SortingState>(initialSorting ?? []);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination,
    manualSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    pageCount,
    state: {
      pagination,
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    onSortingChange: (updater) => {
      if (typeof updater === 'function') {
        const nextState = updater(sorting);

        setSorting(nextState);

        if (onSortingChange) {
          onSortingChange(nextState);
        }
      } else {
        setSorting(updater);

        if (onSortingChange) {
          onSortingChange(updater);
        }
      }
    },
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        const nextState = updater(pagination);

        setPagination(nextState);

        if (onPaginationChange) {
          onPaginationChange(nextState);
        }
      } else {
        setPagination(updater);

        if (onPaginationChange) {
          onPaginationChange(updater);
        }
      }
    },
  });

  // Render mobile view as cards
  if (isMobile) {
    return (
      <div className="space-y-4">
        {table.getRowModel().rows?.length ? (
          table.getRowModel().rows.map((row) => {
            // Nếu có custom render function, sử dụng nó
            if (renderMobileCard) {
              return (
                <Card key={row.id} className="overflow-hidden">
                  {renderMobileCard(row)}
                </Card>
              );
            }

            // Mặc định render dựa trên mobileCardFields
            return (
              <Card key={row.id} className="overflow-hidden border rounded-xl shadow-sm hover:shadow-md transition-all duration-200 mb-4">
                <CardContent className="p-4 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-950">
                  {columns.map((column) => {
                    const accessorKey = column.accessorKey as string;

                    // Chỉ hiển thị các trường được chỉ định trong mobileCardFields
                    if (accessorKey && mobileCardFields.includes(accessorKey)) {
                      return (
                        <div key={accessorKey} className="py-2 border-b last:border-b-0 border-gray-100 dark:border-gray-800">
                          <div className="font-medium text-xs uppercase tracking-wider text-muted-foreground">
                            {column.header ?
                              (typeof column.header === 'function' ?
                                column.header({} as any) :
                                column.header) :
                              accessorKey}
                          </div>
                          <div className="mt-1 font-medium">
                            {flexRender(column.cell, row.getVisibleCells().find(cell =>
                              cell.column.id === (column.id || accessorKey))?.getContext())}
                          </div>
                        </div>
                      );
                    }
                    return null;
                  })}
                </CardContent>
              </Card>
            );
          })
        ) : (
          <Card>
            <CardContent className="h-24 flex items-center justify-center">
              <Trans i18nKey={'common:noData'} />
            </CardContent>
          </Card>
        )}

        {table.getPageCount() > 1 && (
          <div className="rounded-lg border p-2">
            <Pagination table={table} />
          </div>
        )}
      </div>
    );
  }

  // Desktop view as table
  return (
    <div className="rounded-lg border">
      <Table {...tableProps}>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead
                  colSpan={header.colSpan}
                  style={{
                    width: header.column.getSize(),
                  }}
                  key={header.id}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>

        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <TableRow
                key={row.id}
                data-state={row.getIsSelected() && 'selected'}
                {...(rowAttributes ? rowAttributes(row) : {})}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                <Trans i18nKey={'common:noData'} />
              </TableCell>
            </TableRow>
          )}
        </TableBody>

        <TableFooter className="bg-background">
          <TableRow>
            <TableCell colSpan={columns.length}>
              <Pagination table={table} />
            </TableCell>
          </TableRow>
        </TableFooter>
      </Table>
    </div>
  );
}
