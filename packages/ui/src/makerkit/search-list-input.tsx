'use client';

import { useRouter, useSearchParams } from 'next/navigation';

import { Input } from '../shadcn/input';

interface SearchListInputProps {
  defaultValue?: string;
  placeholder?: string;
  className?: string;
}

export function SearchListInput({
  defaultValue = '',
  placeholder = 'Search...',
  className = 'max-w-sm',
}: SearchListInputProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleSearch = (value: string) => {
    const params = new URLSearchParams(searchParams);

    if (value) {
      params.set('query', value);
    } else {
      params.delete('query');
    }

    // Reset to first page when searching
    params.delete('page');

    router.push(`?${params.toString()}`);
  };

  return (
    <Input
      placeholder={placeholder}
      className={className}
      defaultValue={defaultValue}
      onChange={(e) => handleSearch(e.target.value)}
    />
  );
}
