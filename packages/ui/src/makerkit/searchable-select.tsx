'use client';

import React, { useCallback, useMemo, useState } from 'react';

import { debounce } from 'next/dist/server/utils';

import { SearchX } from 'lucide-react';

import { Button } from '../shadcn/button';
import { Input } from '../shadcn/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../shadcn/select';

export interface SearchableSelectItem {
  id: string;
  label: string;
}

interface SearchableSelectProps {
  items: SearchableSelectItem[];
  value?: string;
  onValueChange: (value: string) => void;
  onSearch: (value: string) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  fetchNextPage?: () => void;
  disabled?: boolean;
  emptyStateMessage?: string;
}

export function SearchableSelect({
  items,
  value,
  onValueChange,
  onSearch,
  placeholder = 'Select an item',
  searchPlaceholder = 'Search...',
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
  disabled,
  emptyStateMessage = 'No items found',
}: SearchableSelectProps) {
  const [searchValue, setSearchValue] = useState('');

  const debouncedSearch = useMemo(
    () => debounce((value: string) => onSearch(value), 300),
    [onSearch],
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchValue(value);
      debouncedSearch(value);
    },
    [debouncedSearch],
  );

  const renderContent = () => {
    if (items.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center p-4 text-center">
          <SearchX className="text-muted-foreground/50 h-12 w-12" />
          <p className="text-muted-foreground mt-2 text-sm">
            {emptyStateMessage}
          </p>
        </div>
      );
    }

    return (
      <>
        {items.map((item) => (
          <SelectItem key={item.id} value={item.id}>
            {item.label}
          </SelectItem>
        ))}
        {hasNextPage && (
          <div className="p-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => fetchNextPage?.()}
              disabled={isFetchingNextPage}
              className="w-full"
            >
              {isFetchingNextPage ? 'Loading...' : 'Load more'}
            </Button>
          </div>
        )}
      </>
    );
  };

  return (
    <Select onValueChange={onValueChange} value={value} disabled={disabled}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <div className="px-2 py-2">
          <Input
            placeholder={searchPlaceholder}
            onChange={handleSearchChange}
            value={searchValue}
            onKeyDown={(e) => e.stopPropagation()}
          />
        </div>
        {renderContent()}
      </SelectContent>
    </Select>
  );
}
