'use client';

import { usePathname, useRouter } from 'next/navigation';

import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';

import { Button } from '../shadcn/button';
import { Trans } from './trans';

// Tách hook ra ngoài

// Tách hook ra ngoài
function useDefaultNavigation() {
  const router = useRouter();
  const path = usePathname();

  return (page: number) => {
    const searchParams = new URLSearchParams({
      page: (page + 1).toString(),
    });
    router.push(`${path}?${searchParams.toString()}`);
  };
}

export interface PaginationProps {
  currentPage: number;
  pageSize: number;
  total: number;
  className?: string;
  onPageChange?: (page: number) => void;
  showItemsCounter?: boolean;
  showFirstLastButtons?: boolean;
  maxVisiblePages?: number;
}

export function Pagination({
  currentPage,
  pageSize,
  total,
  className = '',
  onPageChange,
  showItemsCounter = true,
  showFirstLastButtons = true,
  maxVisiblePages = 5,
}: PaginationProps) {
  // Sử dụng hook ở đây
  const defaultNavigate = useDefaultNavigation();

  const totalPages = Math.ceil(total / pageSize);
  const displayPage = currentPage + 1;

  const startItem = currentPage * pageSize + 1;
  const endItem = Math.min((currentPage + 1) * pageSize, total);

  const handlePageChange = (page: number) => {
    if (onPageChange) {
      onPageChange(page);
    } else {
      defaultNavigate(page);
    }
  };

  // Generate page numbers to show
  const getPageNumbers = () => {
    const pages = [];

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages are less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (displayPage > 3) {
        pages.push('...');
      }

      // Show pages around current page
      for (
        let i = Math.max(2, displayPage - 1);
        i <= Math.min(totalPages - 1, displayPage + 1);
        i++
      ) {
        pages.push(i);
      }

      if (displayPage < totalPages - 2) {
        pages.push('...');
      }

      // Always show last page if not already included
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }

    return pages;
  };

  return (
    <div
      className={`mt-4 flex flex-col items-center justify-between gap-4 border-t border-gray-200 px-2 py-4 sm:flex-row ${className}`}
    >
      {/* Items counter */}
      {showItemsCounter && (
        <div className="text-sm text-gray-500">
          <Trans
            i18nKey="common:pagination.showing"
            values={{
              start: startItem,
              end: endItem,
              total: total,
            }}
          >
            Showing {startItem} to {endItem} of {total}
          </Trans>
        </div>
      )}

      {/* Pagination controls */}
      <div className="flex items-center gap-1">
        {/* First page */}
        {showFirstLastButtons && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(0)}
            disabled={displayPage === 1}
            className="hidden h-8 w-8 p-0 sm:flex"
            title={<Trans i18nKey="common:pagination.first">First page</Trans>}
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>
        )}

        {/* Previous page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={displayPage === 1}
          className="h-8 w-8 p-0"
          title={
            <Trans i18nKey="common:pagination.previous">Previous page</Trans>
          }
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {/* Page numbers */}
        <div className="flex gap-1">
          {getPageNumbers().map((page, idx) =>
            typeof page === 'number' ? (
              <Button
                key={idx}
                variant={displayPage === page ? 'default' : 'outline'}
                size="sm"
                onClick={() => handlePageChange(page - 1)}
                className="hidden h-8 w-8 p-0 sm:flex"
              >
                {page}
              </Button>
            ) : (
              <Button
                key={idx}
                variant="outline"
                size="sm"
                disabled
                className="hidden h-8 w-8 p-0 sm:flex"
              >
                {page}
              </Button>
            ),
          )}
        </div>

        {/* Current page indicator for mobile */}
        <span className="text-sm text-gray-500 sm:hidden">
          <Trans
            i18nKey="common:pagination.pageOf"
            values={{ current: displayPage, total: totalPages }}
          >
            Page {displayPage} of {totalPages}
          </Trans>
        </span>

        {/* Next page */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={displayPage === totalPages}
          className="h-8 w-8 p-0"
          title={<Trans i18nKey="common:pagination.next">Next page</Trans>}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        {/* Last page */}
        {showFirstLastButtons && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(totalPages - 1)}
            disabled={displayPage === totalPages}
            className="hidden h-8 w-8 p-0 sm:flex"
            title={<Trans i18nKey="common:pagination.last">Last page</Trans>}
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
