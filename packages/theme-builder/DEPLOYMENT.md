# Theme Builder Deployment Guide

Hướng dẫn deploy Theme Builder package vào production environment.

## 🚀 Production Deployment

### 1. Package Build

```bash
# Build theme-builder package
cd packages/theme-builder
npm run build

# Verify build output
ls -la dist/
# Should contain:
# - index.js, index.mjs (main entry)
# - components/index.js, components/index.mjs
# - editor/index.js, editor/index.mjs
# - renderer/index.js, renderer/index.mjs
```

### 2. Backend Integration

```bash
# Install dependencies in web app
cd apps/web
npm install

# Build web app
npm run build

# Start production server
npm run start
```

### 3. Mobile App Integration

```bash
# Link theme-builder package
cd mobile-project
npm install file:../packages/theme-builder

# Build mobile app
npm run build

# Deploy to Zalo Mini App platform
npm run deploy
```

## 🔧 Environment Configuration

### Backend Environment Variables

```bash
# apps/web/.env.production
NEXT_PUBLIC_THEME_BUILDER_ENABLED=true
NEXT_PUBLIC_THEME_PREVIEW_URL=https://your-domain.com/preview
THEME_STORAGE_BUCKET=your-storage-bucket
THEME_CDN_URL=https://cdn.your-domain.com/themes
```

### Mobile Environment Variables

```bash
# mobile-project/.env.production
REACT_APP_THEME_API_URL=https://api.your-domain.com
REACT_APP_THEME_CACHE_ENABLED=true
REACT_APP_THEME_CACHE_TTL=3600
```

## 📦 Package Distribution

### NPM Registry (Optional)

```bash
# Publish to private NPM registry
cd packages/theme-builder
npm version patch
npm publish --registry https://your-private-registry.com
```

### Manual Distribution

```bash
# Create distribution package
cd packages/theme-builder
npm pack

# Copy to target projects
cp kit-theme-builder-*.tgz ../target-project/
cd ../target-project
npm install kit-theme-builder-*.tgz
```

## 🌐 CDN Setup

### Theme Assets CDN

```bash
# Upload built themes to CDN
aws s3 sync ./themes s3://your-theme-bucket/themes/ --acl public-read

# Configure CloudFront distribution
# Origin: your-theme-bucket.s3.amazonaws.com
# Behaviors: /themes/* -> S3 origin
```

### Component Assets CDN

```bash
# Upload component assets
aws s3 sync ./packages/theme-builder/dist s3://your-assets-bucket/theme-builder/ --acl public-read
```

## 🔒 Security Configuration

### Content Security Policy

```javascript
// next.config.js
const nextConfig = {
  async headers() {
    return [
      {
        source: '/miniapp/setup/customization',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: `
              default-src 'self';
              script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.your-domain.com;
              style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
              img-src 'self' data: https: blob:;
              font-src 'self' https://fonts.gstatic.com;
              connect-src 'self' https://api.your-domain.com;
              frame-src 'self' https://preview.your-domain.com;
            `.replace(/\s+/g, ' ').trim()
          }
        ]
      }
    ];
  }
};
```

### API Rate Limiting

```javascript
// middleware.js
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

const ratelimit = new Ratelimit({
  redis: Redis.fromEnv(),
  limiter: Ratelimit.slidingWindow(10, '1 m'), // 10 requests per minute
});

export async function middleware(request) {
  if (request.nextUrl.pathname.startsWith('/api/themes')) {
    const { success } = await ratelimit.limit(request.ip);
    
    if (!success) {
      return new Response('Rate limit exceeded', { status: 429 });
    }
  }
}
```

## 📊 Monitoring & Analytics

### Performance Monitoring

```javascript
// utils/monitoring.js
export const trackThemeBuilderPerformance = (action, duration) => {
  // Google Analytics
  gtag('event', 'theme_builder_performance', {
    action,
    duration,
    category: 'performance'
  });

  // Custom analytics
  fetch('/api/analytics/theme-builder', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ action, duration, timestamp: Date.now() })
  });
};

// Usage in components
const startTime = performance.now();
// ... theme operation
const endTime = performance.now();
trackThemeBuilderPerformance('theme_save', endTime - startTime);
```

### Error Tracking

```javascript
// utils/error-tracking.js
import * as Sentry from '@sentry/nextjs';

export const trackThemeBuilderError = (error, context) => {
  Sentry.withScope((scope) => {
    scope.setTag('component', 'theme-builder');
    scope.setContext('theme_builder', context);
    Sentry.captureException(error);
  });
};

// Usage
try {
  await saveTheme(data, theme);
} catch (error) {
  trackThemeBuilderError(error, {
    action: 'save_theme',
    themeId: theme.id,
    userId: user.id
  });
}
```

## 🔄 Database Migrations

### Theme Storage Schema

```sql
-- Migration: Add theme builder support
ALTER TABLE themes ADD COLUMN IF NOT EXISTS puck_data JSONB;
ALTER TABLE themes ADD COLUMN IF NOT EXISTS puck_config JSONB;
ALTER TABLE themes ADD COLUMN IF NOT EXISTS version VARCHAR(10) DEFAULT '1.0.0';
ALTER TABLE themes ADD COLUMN IF NOT EXISTS created_with VARCHAR(50) DEFAULT 'theme-builder';

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_themes_puck_data ON themes USING GIN (puck_data);
CREATE INDEX IF NOT EXISTS idx_themes_version ON themes (version);
CREATE INDEX IF NOT EXISTS idx_themes_created_with ON themes (created_with);
```

### Data Migration Script

```javascript
// scripts/migrate-themes.js
const { createClient } = require('@supabase/supabase-js');

async function migrateExistingThemes() {
  const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_KEY);
  
  const { data: themes } = await supabase
    .from('themes')
    .select('*')
    .is('puck_data', null);

  for (const theme of themes) {
    const puckData = convertLegacyThemeToPuck(theme);
    
    await supabase
      .from('themes')
      .update({
        puck_data: puckData.data,
        puck_config: puckData.config,
        version: '1.0.0',
        created_with: 'migration'
      })
      .eq('id', theme.id);
  }
  
  console.log(`Migrated ${themes.length} themes`);
}

function convertLegacyThemeToPuck(legacyTheme) {
  return {
    data: { content: [], root: {} },
    config: {
      name: legacyTheme.name,
      brandColor: legacyTheme.brand_color,
      colors: legacyTheme.colors || {},
      typography: legacyTheme.typography || {},
      layout: legacyTheme.layout || {},
      components: legacyTheme.components || {},
      accessibility: legacyTheme.accessibility || {}
    }
  };
}

migrateExistingThemes().catch(console.error);
```

## 🧪 Testing in Production

### Health Checks

```javascript
// pages/api/health/theme-builder.js
export default async function handler(req, res) {
  try {
    // Test theme loading
    const testTheme = await loadTheme('test-theme-id');
    
    // Test theme saving
    await saveTheme({ content: [], root: {} }, testTheme);
    
    // Test component rendering
    const componentTest = await testComponentRendering();
    
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      tests: {
        theme_loading: 'pass',
        theme_saving: 'pass',
        component_rendering: componentTest ? 'pass' : 'fail'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
```

### Load Testing

```bash
# Install artillery for load testing
npm install -g artillery

# Create load test config
cat > theme-builder-load-test.yml << EOF
config:
  target: 'https://your-domain.com'
  phases:
    - duration: 60
      arrivalRate: 10
  variables:
    theme_id: 'test-theme-123'

scenarios:
  - name: 'Theme Builder Load Test'
    flow:
      - get:
          url: '/api/themes/{{ theme_id }}'
      - post:
          url: '/api/themes/{{ theme_id }}'
          json:
            data: { content: [], root: {} }
            theme: { name: 'Test Theme' }
EOF

# Run load test
artillery run theme-builder-load-test.yml
```

## 📋 Deployment Checklist

### Pre-deployment

- [ ] Package builds successfully
- [ ] All tests pass
- [ ] Security scan completed
- [ ] Performance benchmarks met
- [ ] Database migrations ready
- [ ] CDN assets uploaded
- [ ] Environment variables configured

### Deployment

- [ ] Deploy backend changes
- [ ] Run database migrations
- [ ] Deploy mobile app updates
- [ ] Update CDN cache
- [ ] Verify health checks
- [ ] Test critical user flows

### Post-deployment

- [ ] Monitor error rates
- [ ] Check performance metrics
- [ ] Verify theme loading/saving
- [ ] Test mobile app integration
- [ ] Monitor user feedback
- [ ] Update documentation

## 🆘 Rollback Plan

### Quick Rollback

```bash
# Rollback backend
cd apps/web
git checkout previous-stable-tag
npm run build
npm run deploy

# Rollback mobile app
cd mobile-project
git checkout previous-stable-tag
npm run build
npm run deploy

# Rollback database (if needed)
npm run db:rollback
```

### Feature Flag Rollback

```javascript
// Feature flag to disable theme builder
if (!process.env.NEXT_PUBLIC_THEME_BUILDER_ENABLED) {
  return <LegacyThemeEditor {...props} />;
}

return <ThemeBuilderIntegration {...props} />;
```

## 📞 Support & Maintenance

### Monitoring Dashboards

- **Performance**: Response times, error rates, user engagement
- **Usage**: Theme creation/editing frequency, popular components
- **Errors**: Failed saves, rendering issues, validation errors

### Regular Maintenance

- **Weekly**: Review error logs, performance metrics
- **Monthly**: Update dependencies, security patches
- **Quarterly**: Performance optimization, feature usage analysis

### Emergency Contacts

- **Development Team**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **Product Team**: <EMAIL>
