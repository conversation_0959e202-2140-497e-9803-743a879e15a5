# Theme Builder Integration Guide

Hướng dẫn tích hợp Theme Builder vào Education SaaS và Zalo Mini App.

## 📋 Tổng quan

Theme Builder package cung cấp:
- **Visual Theme Editor**: Drag-and-drop interface với Puck Editor
- **ZMP-UI Components**: Pre-built components cho Zalo Mini Apps
- **Theme Management**: Comprehensive theming system
- **Backend Integration**: Seamless integration với existing backend

## 🏗️ Kiến trúc tích hợp

```
┌─────────────────────────────────────────────────────────────┐
│                    Education SaaS Backend                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           Theme Builder Integration                     │ │
│  │  /miniapp/setup/customization                          │ │
│  │  - ThemeBuilderIntegration component                   │ │
│  │  - Traditional + Visual editor tabs                    │ │
│  │  - Real-time preview                                   │ │
│  └─────────────────────────────────────────────────────────┘ │
│                            │                                │
│                            ▼                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Theme Config Storage                       │ │
│  │  - Database: themes table                              │ │
│  │  - Config: JSON structure                              │ │
│  │  - API: getConfig(), updateConfig()                    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                    Zalo Mini App                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Theme Service                              │ │
│  │  - loadThemeConfig(accountId)                          │ │
│  │  - applyThemeToDOM()                                   │ │
│  │  - CSS variables injection                             │ │
│  └─────────────────────────────────────────────────────────┘ │
│                            │                                │
│                            ▼                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           Guest Business View                           │ │
│  │  - Dynamic theme loading                               │ │
│  │  - CSS variables application                           │ │
│  │  - Real-time theme updates                             │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Backend Integration

### 1. Package Installation

```bash
# Add to apps/web/package.json
{
  "dependencies": {
    "@kit/theme-builder": "workspace:*"
  }
}
```

### 2. Theme Builder Component

```tsx
// apps/web/app/home/<USER>/miniapp/setup/customization/_components/theme-builder-integration.tsx
import { ThemeBuilder, ThemeProvider } from '@kit/theme-builder';

export function ThemeBuilderIntegration({
  initialConfig,
  onConfigChange,
  onSave,
  isSaving,
}) {
  return (
    <ThemeProvider initialTheme={puckTheme}>
      <ThemeBuilder
        data={puckData}
        theme={puckTheme}
        onSave={handlePuckSave}
        onPreview={handlePuckPreview}
        onPublish={handlePuckPublish}
        showThemePanel={true}
        showPreview={true}
      />
    </ThemeProvider>
  );
}
```

### 3. Integration vào Customization Page

```tsx
// apps/web/app/home/<USER>/miniapp/setup/customization/page.tsx
import { ThemeBuilderIntegration } from './_components/theme-builder-integration';

export default function CustomizationPage() {
  return (
    <form>
      {/* Existing form fields */}
      
      <ThemeBuilderIntegration
        initialConfig={configValues}
        onConfigChange={handleConfigChange}
        onSave={handleSave}
        isSaving={isSaving}
        tempTheme={tempTheme}
        previewQrCode={previewQrCode}
      />
    </form>
  );
}
```

## 📱 Mobile Integration

### 1. Theme Service

```typescript
// src/services/themeService.ts
import { supabaseService } from './supabaseService';

class ThemeService {
  async loadThemeConfig(accountId: string): Promise<ThemeConfig> {
    const config = await supabaseService.getConfig(accountId);
    const themeConfig = this.parseBackendConfig(config.config);
    this.setTheme(themeConfig);
    return themeConfig;
  }

  setTheme(theme: ThemeConfig) {
    this.applyThemeToDOM(theme);
    this.notifyListeners(theme);
  }

  private applyThemeToDOM(theme: ThemeConfig) {
    const root = document.documentElement;
    root.style.setProperty('--color-primary', theme.colors.primary.main);
    // ... more CSS variables
  }
}

export const themeService = new ThemeService();
```

### 2. Guest Business View Integration

```tsx
// src/pages/business/home/<USER>/guest-business-view.tsx
import { themeService, type ThemeConfig } from '../../../../services/themeService';

const GuestBusinessView: React.FC = () => {
  const [themeConfig, setThemeConfig] = useState<ThemeConfig | null>(null);

  useEffect(() => {
    const loadData = async () => {
      // Load business data
      const result = await educationService.getGuestBusinessInfo(accountId);
      
      // Load theme config
      const theme = await themeService.loadThemeConfig(accountId);
      setThemeConfig(theme);
    };

    loadData();
  }, []);

  const themeStyles = themeConfig ? themeService.getCSSVariables(themeConfig) : {};

  return (
    <div style={themeStyles}>
      {/* Component content with theme variables */}
    </div>
  );
};
```

## 🎨 Theme Configuration

### Backend Config Structure

```typescript
interface BackendThemeConfig {
  name: string;
  brandColor: string;
  colors: {
    primary: { main: string; light: string; dark: string };
    secondary: { main: string; light: string; dark: string };
    // ... more colors
  };
  typography: {
    fontFamily: string;
    fontSize: { base: string; lg: string; xl: string };
    // ... more typography
  };
  layout: {
    maxWidth: string;
    containerPadding: string;
    header: { height: string; background: string };
    // ... more layout
  };
  components: {
    button: { borderRadius: string; padding: string };
    card: { borderRadius: string; shadow: string };
    // ... more components
  };
  accessibility: {
    highContrast: boolean;
    autoplayVideos: boolean;
    // ... more accessibility
  };
  logoUrl?: string;
}
```

### CSS Variables Applied

```css
:root {
  --color-primary: #1877F2;
  --color-primary-light: #42A5F5;
  --color-primary-dark: #1565C0;
  --color-secondary: #6C757D;
  --color-background: #FFFFFF;
  --color-text-primary: #212529;
  --font-family: 'Inter, sans-serif';
  --font-size-base: 1rem;
  --layout-max-width: 1200px;
  --header-height: 64px;
  --button-border-radius: 0.5rem;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
```

## 🔄 Data Flow

### 1. Theme Creation/Update (Backend)
```
User edits theme → ThemeBuilder → onConfigChange → Backend API → Database
```

### 2. Theme Loading (Mobile)
```
App loads → themeService.loadThemeConfig() → Backend API → Apply CSS variables
```

### 3. Real-time Preview
```
Theme changes → Preview URL → QR Code → Mobile preview
```

## 🧪 Testing

### Backend Testing
```bash
# Test theme builder integration
cd apps/web
npm run dev
# Navigate to /miniapp/setup/customization
# Test visual editor tab
# Verify theme changes are saved
```

### Mobile Testing
```bash
# Test theme loading
cd mobile-project
npm run dev
# Check console for theme loading logs
# Verify CSS variables are applied
# Test with different theme configs
```

## 🐛 Troubleshooting

### Common Issues

1. **Theme not loading in mobile**
   - Check console for API errors
   - Verify accountId is correct
   - Check supabaseService.getConfig() response

2. **CSS variables not applied**
   - Check themeService.applyThemeToDOM() is called
   - Verify CSS variable names match
   - Check browser developer tools

3. **Theme Builder not saving**
   - Check onConfigChange callback
   - Verify backend API endpoints
   - Check form validation

### Debug Commands

```typescript
// Check current theme
console.log('Current theme:', themeService.getCurrentTheme());

// Check CSS variables
console.log('CSS variables:', themeService.getCSSVariables());

// Check theme loading
themeService.loadThemeConfig('account-id').then(theme => {
  console.log('Loaded theme:', theme);
});
```

## 📚 Next Steps

1. **Extend Components**: Add Form, Navigation, Media components
2. **Theme Marketplace**: Create pre-built themes
3. **A/B Testing**: Test theme performance
4. **Advanced Features**: Animations, transitions, responsive breakpoints
5. **Documentation**: Video tutorials, best practices

## 🤝 Support

For issues and questions:
- Check console logs for errors
- Verify package versions compatibility
- Test with default theme first
- Contact development team for complex issues
