{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/editor/*": ["./src/editor/*"], "@/renderer/*": ["./src/renderer/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"]}, "declaration": true, "declarationMap": true, "outDir": "dist"}, "include": ["src/**/*", "*.ts", "*.tsx"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}