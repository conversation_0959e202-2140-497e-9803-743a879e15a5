/* Tailwind CSS */
@import 'tailwindcss';

/* Puck Editor Styles */
@import '@measured/puck/puck.css';

/* ZMP-UI Styles */
@import 'zmp-ui/zaui.css';

/* Safelist for dynamic classes - Tailwind v4.0 */
@source "./safelist.txt";

/* Theme Builder Custom Styles */
@layer base {
  :root {
    /* ZMP Theme Variables */
    --zmp-primary: #2563eb;
    --zmp-primary-light: #3b82f6;
    --zmp-primary-dark: #1d4ed8;
    --zmp-secondary: #4f46e5;
    --zmp-secondary-light: #6366f1;
    --zmp-secondary-dark: #4338ca;
    --zmp-accent: #f59e0b;
    --zmp-accent-light: #fbbf24;
    --zmp-accent-dark: #d97706;
    --zmp-background: #ffffff;
    --zmp-paper-background: #f3f4f6;
    --zmp-text: #111827;
    --zmp-secondary-text: #4b5563;
    --zmp-border-radius: 8px;
    --zmp-spacing: 16px;
  }
}

@layer components {
  /* ZMP Component Base Styles */
  .zmp-component {
    font-family: var(--zmp-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
  }

  /* Dynamic Button Variants */
  .zmp-button {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .zmp-button-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .zmp-button-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  .zmp-button-accent {
    @apply bg-amber-500 text-white hover:bg-amber-600 focus:ring-amber-500;
  }

  .zmp-button-outline {
    @apply border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }

  .zmp-button-ghost {
    @apply bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
  }

  /* Dynamic Card Variants */
  .zmp-card {
    @apply rounded-lg border bg-white shadow-sm;
  }

  .zmp-card-elevated {
    @apply shadow-md;
  }

  .zmp-card-outlined {
    @apply border-2;
  }

  .zmp-card-flat {
    @apply shadow-none border-0;
  }

  /* Dynamic Text Variants */
  .zmp-text {
    @apply text-gray-900;
  }

  .zmp-text-secondary {
    @apply text-gray-600;
  }

  .zmp-text-muted {
    @apply text-gray-500;
  }

  /* Dynamic Grid Variants */
  .zmp-grid {
    @apply grid gap-4;
  }

  .zmp-grid-1 {
    @apply grid-cols-1;
  }

  .zmp-grid-2 {
    @apply grid-cols-2;
  }

  .zmp-grid-3 {
    @apply grid-cols-3;
  }

  .zmp-grid-4 {
    @apply grid-cols-4;
  }

  .zmp-grid-6 {
    @apply grid-cols-6;
  }

  .zmp-grid-12 {
    @apply grid-cols-12;
  }
}

@layer utilities {
  /* Dynamic spacing utilities */
  .zmp-spacing-xs {
    @apply p-2;
  }

  .zmp-spacing-sm {
    @apply p-3;
  }

  .zmp-spacing-md {
    @apply p-4;
  }

  .zmp-spacing-lg {
    @apply p-6;
  }

  .zmp-spacing-xl {
    @apply p-8;
  }

  /* Dynamic border radius utilities */
  .zmp-rounded-none {
    @apply rounded-none;
  }

  .zmp-rounded-sm {
    @apply rounded-sm;
  }

  .zmp-rounded-md {
    @apply rounded-md;
  }

  .zmp-rounded-lg {
    @apply rounded-lg;
  }

  .zmp-rounded-xl {
    @apply rounded-xl;
  }

  .zmp-rounded-full {
    @apply rounded-full;
  }
}
