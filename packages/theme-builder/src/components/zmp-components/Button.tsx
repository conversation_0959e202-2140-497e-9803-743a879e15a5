import React from 'react';
import { Button } from 'zmp-ui';
import { ComponentConfig } from '@measured/puck';

import { cn, generateDynamicClasses, TAILWIND_OPTIONS } from '../../utils/tailwind-utils';

export interface ZMPButtonProps {
  text: string;
  variant?: 'primary' | 'secondary' | 'tertiary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  disabled?: boolean;
  loading?: boolean;
  prefixIcon?: string;
  suffixIcon?: string;
  onClick?: string; // Action as string for Puck
  className?: string;
  // Tailwind dynamic styling
  useTailwind?: boolean;
  tailwindVariant?: 'filled' | 'outlined' | 'ghost';
  tailwindColor?: keyof typeof TAILWIND_OPTIONS.colors;
  tailwindSize?: keyof typeof TAILWIND_OPTIONS.sizes;
  tailwindBorderRadius?: keyof typeof TAILWIND_OPTIONS.borderRadius;
  tailwindShadow?: keyof typeof TAILWIND_OPTIONS.shadows;
  customClasses?: string;
}

export const ZMPButtonComponent: React.FC<ZMPButtonProps> = ({
  text,
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  disabled = false,
  loading = false,
  prefixIcon,
  suffixIcon,
  onClick,
  className,
  useTailwind = false,
  tailwindVariant = 'filled',
  tailwindColor = 'primary',
  tailwindSize = 'md',
  tailwindBorderRadius = 'md',
  tailwindShadow = 'sm',
  customClasses,
}) => {
  const handleClick = () => {
    if (onClick) {
      // In a real implementation, you might want to parse and execute the action
      console.log('Button clicked:', onClick);
    }
  };

  // Generate dynamic Tailwind classes
  const tailwindClasses = useTailwind
    ? generateDynamicClasses({
        variant: tailwindVariant,
        size: tailwindSize,
        color: tailwindColor,
        borderRadius: tailwindBorderRadius,
        shadow: tailwindShadow,
        customClasses: cn(
          'zmp-button',
          'transition-all duration-200 ease-in-out',
          'focus:outline-none focus:ring-2 focus:ring-offset-2',
          fullWidth && 'w-full',
          disabled && 'opacity-50 cursor-not-allowed',
          customClasses
        ),
      })
    : undefined;

  // Use Tailwind styling or ZMP-UI styling
  if (useTailwind) {
    return (
      <button
        className={cn(tailwindClasses, className)}
        disabled={disabled || loading}
        onClick={handleClick}
        type="button"
      >
        {prefixIcon && <span className="mr-2">{prefixIcon}</span>}
        {loading ? (
          <span className="inline-flex items-center">
            <svg
              className="animate-spin -ml-1 mr-2 h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            Loading...
          </span>
        ) : (
          text
        )}
        {suffixIcon && <span className="ml-2">{suffixIcon}</span>}
      </button>
    );
  }

  // Fallback to ZMP-UI Button
  return (
    <Button
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      disabled={disabled}
      loading={loading}
      prefixIcon={prefixIcon}
      suffixIcon={suffixIcon}
      onClick={handleClick}
      className={cn('zmp-button', className)}
    >
      {text}
    </Button>
  );
};

// Puck component configuration
export const ZMPButton: ComponentConfig<ZMPButtonProps> = {
  fields: {
    text: {
      type: 'text',
      label: 'Button Text',
    },
    variant: {
      type: 'select',
      label: 'Variant',
      options: [
        { label: 'Primary', value: 'primary' },
        { label: 'Secondary', value: 'secondary' },
        { label: 'Tertiary', value: 'tertiary' },
        { label: 'Danger', value: 'danger' },
      ],
    },
    size: {
      type: 'select',
      label: 'Size',
      options: [
        { label: 'Small', value: 'small' },
        { label: 'Medium', value: 'medium' },
        { label: 'Large', value: 'large' },
      ],
    },
    fullWidth: {
      type: 'radio',
      label: 'Full Width',
      options: [
        { label: 'Yes', value: true },
        { label: 'No', value: false },
      ],
    },
    disabled: {
      type: 'radio',
      label: 'Disabled',
      options: [
        { label: 'Yes', value: true },
        { label: 'No', value: false },
      ],
    },
    loading: {
      type: 'radio',
      label: 'Loading',
      options: [
        { label: 'Yes', value: true },
        { label: 'No', value: false },
      ],
    },
    prefixIcon: {
      type: 'text',
      label: 'Prefix Icon',
    },
    suffixIcon: {
      type: 'text',
      label: 'Suffix Icon',
    },
    onClick: {
      type: 'text',
      label: 'On Click Action',
    },
    className: {
      type: 'text',
      label: 'CSS Class',
    },
    // Tailwind dynamic styling fields
    useTailwind: {
      type: 'radio',
      label: 'Use Tailwind Styling',
      options: [
        { label: 'Yes', value: true },
        { label: 'No', value: false },
      ],
    },
    tailwindVariant: {
      type: 'select',
      label: 'Tailwind Variant',
      options: [
        { label: 'Filled', value: 'filled' },
        { label: 'Outlined', value: 'outlined' },
        { label: 'Ghost', value: 'ghost' },
      ],
    },
    tailwindColor: {
      type: 'select',
      label: 'Tailwind Color',
      options: Object.entries(TAILWIND_OPTIONS.colors).map(([key, value]) => ({
        label: value.label,
        value: key,
      })),
    },
    tailwindSize: {
      type: 'select',
      label: 'Tailwind Size',
      options: Object.entries(TAILWIND_OPTIONS.sizes).map(([key, value]) => ({
        label: value.label,
        value: key,
      })),
    },
    tailwindBorderRadius: {
      type: 'select',
      label: 'Border Radius',
      options: Object.entries(TAILWIND_OPTIONS.borderRadius).map(([key, value]) => ({
        label: value.label,
        value: key,
      })),
    },
    tailwindShadow: {
      type: 'select',
      label: 'Shadow',
      options: Object.entries(TAILWIND_OPTIONS.shadows).map(([key, value]) => ({
        label: value.label,
        value: key,
      })),
    },
    customClasses: {
      type: 'text',
      label: 'Custom Tailwind Classes',
      placeholder: 'e.g., hover:scale-105 active:scale-95',
    },
  },
  defaultProps: {
    text: 'Click me',
    variant: 'primary',
    size: 'medium',
    fullWidth: false,
    disabled: false,
    loading: false,
    useTailwind: false,
    tailwindVariant: 'filled',
    tailwindColor: 'primary',
    tailwindSize: 'md',
    tailwindBorderRadius: 'md',
    tailwindShadow: 'sm',
  },
  render: ZMPButtonComponent,
};
