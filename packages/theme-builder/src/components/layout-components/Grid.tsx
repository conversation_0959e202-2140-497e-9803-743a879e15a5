import React from 'react';
import { Box } from 'zmp-ui';
import { ComponentConfig } from '@measured/puck';

import { cn, generateGridClasses, TAILWIND_OPTIONS } from '../../utils/tailwind-utils';

export interface ZMPGridProps {
  children?: React.ReactNode;
  columns?: 1 | 2 | 3 | 4 | 6 | 12;
  gap?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
  // Tailwind dynamic styling
  useTailwind?: boolean;
  tailwindGap?: keyof typeof TAILWIND_OPTIONS.gaps;
  tailwindColumns?: keyof typeof TAILWIND_OPTIONS.gridColumns;
  customClasses?: string;
}

export const ZMPGridComponent: React.FC<ZMPGridProps> = ({
  children,
  columns = 2,
  gap = 'md',
  className,
  useTailwind = true,
  tailwindGap,
  tailwindColumns,
  customClasses,
}) => {
  // Legacy grid classes (for backward compatibility)
  const getGridClass = (cols: number) => {
    switch (cols) {
      case 1: return 'grid-cols-1';
      case 2: return 'grid-cols-2';
      case 3: return 'grid-cols-3';
      case 4: return 'grid-cols-4';
      case 6: return 'grid-cols-6';
      case 12: return 'grid-cols-12';
      default: return 'grid-cols-2';
    }
  };

  const getGapClass = (gapSize: string) => {
    switch (gapSize) {
      case 'none': return 'gap-0';
      case 'sm': return 'gap-2';
      case 'md': return 'gap-4';
      case 'lg': return 'gap-6';
      default: return 'gap-4';
    }
  };

  // Use Tailwind dynamic styling if enabled
  const gridClasses = useTailwind
    ? generateGridClasses({
        columns: tailwindColumns || (columns as keyof typeof TAILWIND_OPTIONS.gridColumns),
        gap: tailwindGap || (gap as keyof typeof TAILWIND_OPTIONS.gaps),
        customClasses: cn(className, customClasses),
      })
    : cn(
        'grid',
        getGridClass(columns),
        getGapClass(gap),
        className,
        customClasses
      );

  return (
    <Box className={cn('zmp-grid', gridClasses)}>
      {children}
    </Box>
  );
};

// Puck component configuration
export const ZMPGrid: ComponentConfig<ZMPGridProps> = {
  fields: {
    columns: {
      type: 'select',
      label: 'Columns (Legacy)',
      options: [
        { label: '1 Column', value: 1 },
        { label: '2 Columns', value: 2 },
        { label: '3 Columns', value: 3 },
        { label: '4 Columns', value: 4 },
        { label: '6 Columns', value: 6 },
        { label: '12 Columns', value: 12 },
      ],
    },
    gap: {
      type: 'select',
      label: 'Gap (Legacy)',
      options: [
        { label: 'None', value: 'none' },
        { label: 'Small', value: 'sm' },
        { label: 'Medium', value: 'md' },
        { label: 'Large', value: 'lg' },
      ],
    },
    useTailwind: {
      type: 'radio',
      label: 'Use Tailwind Styling',
      options: [
        { label: 'Yes', value: true },
        { label: 'No', value: false },
      ],
    },
    tailwindColumns: {
      type: 'select',
      label: 'Tailwind Columns',
      options: Object.entries(TAILWIND_OPTIONS.gridColumns).map(([key, value]) => ({
        label: value.label,
        value: Number(key),
      })),
    },
    tailwindGap: {
      type: 'select',
      label: 'Tailwind Gap',
      options: Object.entries(TAILWIND_OPTIONS.gaps).map(([key, value]) => ({
        label: value.label,
        value: key,
      })),
    },
    className: {
      type: 'text',
      label: 'CSS Class',
    },
    customClasses: {
      type: 'text',
      label: 'Custom Tailwind Classes',
      placeholder: 'e.g., lg:grid-cols-4 md:gap-6',
    },
  },
  defaultProps: {
    columns: 2,
    gap: 'md',
    useTailwind: true,
    tailwindColumns: 2,
    tailwindGap: 'md',
  },
  render: ZMPGridComponent,
};
