import { clsx, type ClassValue } from 'clsx';

/**
 * Utility function to combine class names
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

/**
 * Dynamic styling options for components
 */
export const TAILWIND_OPTIONS = {
  // Color variants
  colors: {
    primary: {
      label: 'Primary',
      bg: 'bg-blue-600',
      text: 'text-blue-600',
      border: 'border-blue-600',
      hover: 'hover:bg-blue-700',
    },
    secondary: {
      label: 'Secondary',
      bg: 'bg-gray-600',
      text: 'text-gray-600',
      border: 'border-gray-600',
      hover: 'hover:bg-gray-700',
    },
    accent: {
      label: 'Accent',
      bg: 'bg-amber-500',
      text: 'text-amber-500',
      border: 'border-amber-500',
      hover: 'hover:bg-amber-600',
    },
    success: {
      label: 'Success',
      bg: 'bg-green-600',
      text: 'text-green-600',
      border: 'border-green-600',
      hover: 'hover:bg-green-700',
    },
    warning: {
      label: 'Warning',
      bg: 'bg-yellow-500',
      text: 'text-yellow-500',
      border: 'border-yellow-500',
      hover: 'hover:bg-yellow-600',
    },
    danger: {
      label: 'Danger',
      bg: 'bg-red-600',
      text: 'text-red-600',
      border: 'border-red-600',
      hover: 'hover:bg-red-700',
    },
  },

  // Size variants
  sizes: {
    xs: {
      label: 'Extra Small',
      padding: 'px-2 py-1',
      text: 'text-xs',
      spacing: 'gap-1',
    },
    sm: {
      label: 'Small',
      padding: 'px-3 py-2',
      text: 'text-sm',
      spacing: 'gap-2',
    },
    md: {
      label: 'Medium',
      padding: 'px-4 py-2',
      text: 'text-base',
      spacing: 'gap-3',
    },
    lg: {
      label: 'Large',
      padding: 'px-6 py-3',
      text: 'text-lg',
      spacing: 'gap-4',
    },
    xl: {
      label: 'Extra Large',
      padding: 'px-8 py-4',
      text: 'text-xl',
      spacing: 'gap-6',
    },
  },

  // Border radius variants
  borderRadius: {
    none: { label: 'None', class: 'rounded-none' },
    sm: { label: 'Small', class: 'rounded-sm' },
    md: { label: 'Medium', class: 'rounded-md' },
    lg: { label: 'Large', class: 'rounded-lg' },
    xl: { label: 'Extra Large', class: 'rounded-xl' },
    full: { label: 'Full', class: 'rounded-full' },
  },

  // Shadow variants
  shadows: {
    none: { label: 'None', class: 'shadow-none' },
    sm: { label: 'Small', class: 'shadow-sm' },
    md: { label: 'Medium', class: 'shadow-md' },
    lg: { label: 'Large', class: 'shadow-lg' },
    xl: { label: 'Extra Large', class: 'shadow-xl' },
  },

  // Grid columns
  gridColumns: {
    1: { label: '1 Column', class: 'grid-cols-1' },
    2: { label: '2 Columns', class: 'grid-cols-2' },
    3: { label: '3 Columns', class: 'grid-cols-3' },
    4: { label: '4 Columns', class: 'grid-cols-4' },
    6: { label: '6 Columns', class: 'grid-cols-6' },
    12: { label: '12 Columns', class: 'grid-cols-12' },
  },

  // Gap sizes
  gaps: {
    none: { label: 'None', class: 'gap-0' },
    sm: { label: 'Small', class: 'gap-2' },
    md: { label: 'Medium', class: 'gap-4' },
    lg: { label: 'Large', class: 'gap-6' },
    xl: { label: 'Extra Large', class: 'gap-8' },
  },
} as const;

/**
 * Generate dynamic class names for components
 */
export function generateDynamicClasses({
  variant,
  size,
  color,
  borderRadius,
  shadow,
  customClasses,
}: {
  variant?: string;
  size?: keyof typeof TAILWIND_OPTIONS.sizes;
  color?: keyof typeof TAILWIND_OPTIONS.colors;
  borderRadius?: keyof typeof TAILWIND_OPTIONS.borderRadius;
  shadow?: keyof typeof TAILWIND_OPTIONS.shadows;
  customClasses?: string;
}) {
  const classes: string[] = [];

  // Add size classes
  if (size && TAILWIND_OPTIONS.sizes[size]) {
    const sizeConfig = TAILWIND_OPTIONS.sizes[size];
    classes.push(sizeConfig.padding, sizeConfig.text);
  }

  // Add color classes based on variant
  if (color && TAILWIND_OPTIONS.colors[color]) {
    const colorConfig = TAILWIND_OPTIONS.colors[color];
    switch (variant) {
      case 'filled':
        classes.push(colorConfig.bg, 'text-white', colorConfig.hover);
        break;
      case 'outlined':
        classes.push(
          'bg-transparent',
          colorConfig.text,
          colorConfig.border,
          'border',
          `hover:${colorConfig.bg}`,
          'hover:text-white'
        );
        break;
      case 'ghost':
        classes.push(
          'bg-transparent',
          colorConfig.text,
          `hover:${colorConfig.bg.replace('bg-', 'bg-').replace('-600', '-50')}`
        );
        break;
      default:
        classes.push(colorConfig.bg, 'text-white', colorConfig.hover);
    }
  }

  // Add border radius
  if (borderRadius && TAILWIND_OPTIONS.borderRadius[borderRadius]) {
    classes.push(TAILWIND_OPTIONS.borderRadius[borderRadius].class);
  }

  // Add shadow
  if (shadow && TAILWIND_OPTIONS.shadows[shadow]) {
    classes.push(TAILWIND_OPTIONS.shadows[shadow].class);
  }

  // Add custom classes
  if (customClasses) {
    classes.push(customClasses);
  }

  return cn(classes);
}

/**
 * Generate grid classes dynamically
 */
export function generateGridClasses({
  columns,
  gap,
  customClasses,
}: {
  columns?: keyof typeof TAILWIND_OPTIONS.gridColumns;
  gap?: keyof typeof TAILWIND_OPTIONS.gaps;
  customClasses?: string;
}) {
  const classes = ['grid'];

  if (columns && TAILWIND_OPTIONS.gridColumns[columns]) {
    classes.push(TAILWIND_OPTIONS.gridColumns[columns].class);
  }

  if (gap && TAILWIND_OPTIONS.gaps[gap]) {
    classes.push(TAILWIND_OPTIONS.gaps[gap].class);
  }

  if (customClasses) {
    classes.push(customClasses);
  }

  return cn(classes);
}

/**
 * Convert theme config to CSS custom properties
 */
export function generateCSSVariables(theme: {
  colors?: {
    primary?: { main: string; light: string; dark: string };
    secondary?: { main: string; light: string; dark: string };
    accent?: { main: string; light: string; dark: string };
    background?: { default: string; paper: string };
    text?: { primary: string; secondary: string };
  };
  typography?: {
    fontFamily?: string;
    headings?: { fontFamily: string; fontWeight: string };
    body?: { fontFamily: string; fontWeight: string };
  };
}) {
  const cssVars: Record<string, string> = {};

  // Colors
  if (theme.colors?.primary) {
    cssVars['--zmp-primary'] = theme.colors.primary.main;
    cssVars['--zmp-primary-light'] = theme.colors.primary.light;
    cssVars['--zmp-primary-dark'] = theme.colors.primary.dark;
  }

  if (theme.colors?.secondary) {
    cssVars['--zmp-secondary'] = theme.colors.secondary.main;
    cssVars['--zmp-secondary-light'] = theme.colors.secondary.light;
    cssVars['--zmp-secondary-dark'] = theme.colors.secondary.dark;
  }

  if (theme.colors?.accent) {
    cssVars['--zmp-accent'] = theme.colors.accent.main;
    cssVars['--zmp-accent-light'] = theme.colors.accent.light;
    cssVars['--zmp-accent-dark'] = theme.colors.accent.dark;
  }

  if (theme.colors?.background) {
    cssVars['--zmp-background'] = theme.colors.background.default;
    cssVars['--zmp-paper-background'] = theme.colors.background.paper;
  }

  if (theme.colors?.text) {
    cssVars['--zmp-text'] = theme.colors.text.primary;
    cssVars['--zmp-secondary-text'] = theme.colors.text.secondary;
  }

  // Typography
  if (theme.typography?.fontFamily) {
    cssVars['--zmp-font-family'] = theme.typography.fontFamily;
  }

  if (theme.typography?.headings) {
    cssVars['--zmp-headings-font-family'] = theme.typography.headings.fontFamily;
    cssVars['--zmp-headings-font-weight'] = theme.typography.headings.fontWeight;
  }

  if (theme.typography?.body) {
    cssVars['--zmp-body-font-family'] = theme.typography.body.fontFamily;
    cssVars['--zmp-body-font-weight'] = theme.typography.body.fontWeight;
  }

  return cssVars;
}
