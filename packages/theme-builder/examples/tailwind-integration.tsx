import React from 'react';
import { Render } from '@measured/puck';

// Import theme-builder with Tailwind integration
import '@kit/theme-builder/dist/styles/globals.css';
import { config } from '@kit/theme-builder';

/**
 * Example: Tailwind CSS v4.0 Integration with Theme Builder
 * 
 * This example demonstrates how to use the enhanced theme-builder
 * with Tailwind CSS v4.0 dynamic styling capabilities.
 */

// Example Puck data with Tailwind styling
const exampleData = {
  content: [
    {
      type: 'ZMPRoot',
      props: {
        title: 'Education Platform Demo',
        // Enable Tailwind styling
        enableTailwindStyling: true,
        tailwindTheme: 'primary',
        containerClasses: 'max-w-6xl mx-auto px-4',
        // Theme colors
        primaryColor: '#2563eb',
        primaryLightColor: '#3b82f6',
        primaryDarkColor: '#1d4ed8',
        secondaryColor: '#4f46e5',
        accentColor: '#f59e0b',
        backgroundColor: '#ffffff',
        textColor: '#111827',
      },
      children: [
        {
          type: 'ZMPGrid',
          props: {
            useTailwind: true,
            tailwindColumns: 3,
            tailwindGap: 'lg',
            customClasses: 'lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1',
          },
          children: [
            {
              type: 'ZMPButton',
              props: {
                text: 'Primary Button',
                useTailwind: true,
                tailwindVariant: 'filled',
                tailwindColor: 'primary',
                tailwindSize: 'lg',
                tailwindBorderRadius: 'lg',
                tailwindShadow: 'md',
                customClasses: 'hover:scale-105 transition-transform duration-200',
              },
            },
            {
              type: 'ZMPButton',
              props: {
                text: 'Secondary Button',
                useTailwind: true,
                tailwindVariant: 'outlined',
                tailwindColor: 'secondary',
                tailwindSize: 'md',
                tailwindBorderRadius: 'md',
                customClasses: 'hover:shadow-lg transition-shadow',
              },
            },
            {
              type: 'ZMPButton',
              props: {
                text: 'Accent Button',
                useTailwind: true,
                tailwindVariant: 'ghost',
                tailwindColor: 'accent',
                tailwindSize: 'sm',
                tailwindBorderRadius: 'full',
                customClasses: 'hover:bg-amber-100',
              },
            },
            {
              type: 'ZMPCard',
              props: {
                title: 'Feature Card',
                content: 'This card uses Tailwind dynamic styling for beautiful, responsive design.',
                useTailwind: true,
                tailwindVariant: 'elevated',
                tailwindColor: 'primary',
                customClasses: 'hover:shadow-xl transition-all duration-300',
              },
            },
          ],
        },
        {
          type: 'ZMPGrid',
          props: {
            useTailwind: true,
            tailwindColumns: 2,
            tailwindGap: 'xl',
            customClasses: 'mt-12',
          },
          children: [
            {
              type: 'ZMPText',
              props: {
                text: 'Dynamic Typography',
                variant: 'h2',
                useTailwind: true,
                tailwindSize: 'xl',
                tailwindColor: 'primary',
                customClasses: 'font-bold text-center',
              },
            },
            {
              type: 'ZMPText',
              props: {
                text: 'This text component demonstrates Tailwind CSS v4.0 integration with predefined classes and custom styling options.',
                variant: 'body',
                useTailwind: true,
                tailwindSize: 'md',
                tailwindColor: 'secondary',
                customClasses: 'text-center leading-relaxed',
              },
            },
          ],
        },
      ],
    },
  ],
  root: {
    props: {
      title: 'Tailwind Integration Demo',
    },
  },
};

// Example component demonstrating Tailwind integration
export const TailwindIntegrationExample: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Theme Builder + Tailwind CSS v4.0
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience the power of dynamic styling with Tailwind CSS v4.0 integration.
            This example showcases predefined classes, custom styling, and responsive design.
          </p>
        </div>

        {/* Render Puck content with Tailwind styling */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <Render config={config} data={exampleData} />
        </div>

        {/* Feature highlights */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white rounded-lg p-6 shadow-md">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a4 4 0 004-4V5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Dynamic Classes</h3>
            <p className="text-gray-600">
              Predefined Tailwind classes with safelist support for dynamic styling without runtime generation.
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-md">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Component Variants</h3>
            <p className="text-gray-600">
              Multiple styling variants for each component with consistent design patterns and accessibility.
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-md">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Performance Optimized</h3>
            <p className="text-gray-600">
              Optimized CSS bundle with tree-shaking and minimal runtime overhead for production applications.
            </p>
          </div>
        </div>

        {/* Code example */}
        <div className="mt-12 bg-gray-900 rounded-xl p-6 overflow-x-auto">
          <h3 className="text-lg font-semibold text-white mb-4">Usage Example</h3>
          <pre className="text-sm text-gray-300">
            <code>{`// Import Tailwind styles
import '@kit/theme-builder/dist/styles/globals.css';
import { ZMPButton, ZMPGrid } from '@kit/theme-builder';

// Use Tailwind dynamic styling
<ZMPButton
  text="Dynamic Button"
  useTailwind={true}
  tailwindVariant="filled"
  tailwindColor="primary"
  tailwindSize="lg"
  customClasses="hover:scale-105 transition-transform"
/>

<ZMPGrid
  useTailwind={true}
  tailwindColumns={3}
  tailwindGap="lg"
  customClasses="lg:grid-cols-4 md:grid-cols-2"
/>`}</code>
          </pre>
        </div>
      </div>
    </div>
  );
};

export default TailwindIntegrationExample;
