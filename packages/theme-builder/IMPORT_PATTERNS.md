# Import Patterns - Learning from @kit/zns

This document outlines the correct import patterns for the theme-builder package, following the established patterns from the @kit/zns package.

## 📦 Package Structure Analysis

### ZNS Package Structure
```
packages/zns/
├── package.json
├── src/
│   ├── index.ts          # Main entry point
│   ├── lib/              # Core functionality
│   ├── hooks/            # React hooks
│   ├── types/            # TypeScript types
│   └── context/          # React contexts
```

### ZNS Package.json Exports
```json
{
  "name": "@kit/zns",
  "exports": {
    ".": "./src/index.ts",
    "./bill-zns": "./src/lib/bill-zns.ts",
    "./get-templates": "./src/lib/get-templates.ts",
    "./send-zns": "./src/lib/send-zns.ts",
    "./types": "./src/types/index.ts",
    "./utils": "./src/lib/utils.ts"
  },
  "typesVersions": {
    "*": {
      "*": ["src/*"]
    }
  }
}
```

## 🎯 Theme Builder Package Structure

### Applied Structure
```
packages/theme-builder/
├── package.json
├── src/
│   ├── index.ts              # Main entry point
│   ├── components/           # ZMP-UI components
│   │   ├── index.ts
│   │   ├── zmp-components/
│   │   ├── layout-components/
│   │   └── content-components/
│   ├── editor/               # Theme editor components
│   │   ├── index.ts
│   │   ├── ThemeBuilder.tsx
│   │   ├── ThemeRenderer.tsx
│   │   └── ThemePanel.tsx
│   ├── hooks/                # React hooks
│   │   └── index.tsx
│   ├── types/                # TypeScript definitions
│   │   └── index.ts
│   ├── utils/                # Utility functions
│   │   └── index.ts
│   └── renderer/             # Renderer exports
│       └── index.ts
```

### Theme Builder Package.json Exports
```json
{
  "name": "@kit/theme-builder",
  "exports": {
    ".": {
      "import": "./dist/index.mjs",
      "require": "./dist/index.js",
      "types": "./dist/index.d.ts"
    },
    "./components": {
      "import": "./dist/components/index.mjs",
      "require": "./dist/components/index.js",
      "types": "./dist/components/index.d.ts"
    },
    "./editor": {
      "import": "./dist/editor/index.mjs",
      "require": "./dist/editor/index.js",
      "types": "./dist/editor/index.d.ts"
    },
    "./renderer": {
      "import": "./dist/renderer/index.mjs",
      "require": "./dist/renderer/index.js",
      "types": "./dist/renderer/index.d.ts"
    }
  }
}
```

## 🔧 Correct Import Patterns

### 1. Main Package Import
```typescript
// ✅ Correct - Main package import
import { ThemeBuilder, ThemeProvider, DEFAULT_THEME } from '@kit/theme-builder';

// ✅ Correct - Specific module import
import { ThemeBuilder } from '@kit/theme-builder/editor';
import { ZMPButton, ZMPText } from '@kit/theme-builder/components';
```

### 2. Submodule Imports
```typescript
// ✅ Correct - Editor components
import { 
  ThemeBuilder, 
  ThemeRenderer, 
  ThemePanel 
} from '@kit/theme-builder/editor';

// ✅ Correct - ZMP Components
import { 
  ZMPButton, 
  ZMPText, 
  ZMPHero 
} from '@kit/theme-builder/components';

// ✅ Correct - Hooks
import { 
  useTheme, 
  useThemeBuilder, 
  ThemeProvider 
} from '@kit/theme-builder/hooks';

// ✅ Correct - Types
import type { 
  ThemeConfig, 
  ComponentConfig, 
  PuckConfig 
} from '@kit/theme-builder/types';
```

### 3. Development vs Production Imports

#### Development (Source)
```typescript
// When working in development with source files
import { ThemeBuilder } from '@kit/theme-builder';
// Resolves to: packages/theme-builder/src/index.ts
```

#### Production (Built)
```typescript
// When using built package
import { ThemeBuilder } from '@kit/theme-builder';
// Resolves to: packages/theme-builder/dist/index.js
```

## 🚫 Incorrect Patterns to Avoid

### 1. Direct File Imports
```typescript
// ❌ Incorrect - Direct file import
import { ThemeBuilder } from '@kit/theme-builder/src/editor/ThemeBuilder';

// ❌ Incorrect - Deep import without proper export
import { ThemeBuilder } from '@kit/theme-builder/dist/editor/ThemeBuilder';
```

### 2. Missing Package Exports
```typescript
// ❌ Incorrect - Importing non-exported module
import { InternalComponent } from '@kit/theme-builder/internal';
```

## 📝 Implementation Guidelines

### 1. Package.json Configuration
- Use `exports` field for module resolution
- Include `typesVersions` for TypeScript support
- Provide both ESM and CJS formats
- Include source files in development

### 2. Index File Structure
```typescript
// src/index.ts - Main entry point
export * from './components';
export * from './editor';
export * from './hooks';
export * from './types';
export * from './utils';

// Re-export commonly used items
export { DEFAULT_THEME } from './utils';
```

### 3. Module Index Files
```typescript
// src/components/index.ts
export * from './zmp-components';
export * from './layout-components';
export * from './content-components';
export * from './config';

// src/editor/index.ts
export * from './ThemeBuilder';
export * from './ThemeRenderer';
export * from './ThemePanel';
export * from './ComponentPanel';
```

## 🔄 Migration from Incorrect Imports

### Before (Incorrect)
```typescript
// ❌ This was causing module resolution errors
import { ThemeBuilderIntegration } from '@kit/theme-builder';
```

### After (Correct)
```typescript
// ✅ Fixed - Use proper Zalo Mini App integration
import { ThemeProvider, ThemeConfigurator, WithTheme } from '../components/theme';
```

## 🎯 Key Learnings from ZNS Package

1. **Consistent Export Structure**: All @kit packages follow the same export pattern
2. **TypeScript Support**: Use `typesVersions` for proper type resolution
3. **Development Workflow**: Source files are accessible during development
4. **Build Output**: Dist files are used in production
5. **Module Boundaries**: Clear separation between different functionality areas

## 📚 Best Practices

1. **Follow Established Patterns**: Use the same structure as other @kit packages
2. **Clear Module Boundaries**: Separate components, hooks, types, and utilities
3. **Proper Exports**: Export everything through index files
4. **TypeScript First**: Ensure proper type definitions and exports
5. **Development Experience**: Make imports intuitive and discoverable

## 🔍 Verification

To verify imports work correctly:

```typescript
// Test file: src/test-imports.ts
import { ThemeBuilder } from '@kit/theme-builder';
import { ZMPButton } from '@kit/theme-builder/components';
import { useTheme } from '@kit/theme-builder/hooks';
import type { ThemeConfig } from '@kit/theme-builder/types';

console.log('✅ All imports working correctly');
```

This pattern ensures compatibility with the existing @kit ecosystem and provides a consistent developer experience.
