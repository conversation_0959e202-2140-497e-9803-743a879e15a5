import { defineConfig } from 'tsup';

export default defineConfig({
  entry: {
    index: 'src/index.ts',
    'components/index': 'src/components/index.ts',
    'editor/index': 'src/editor/index.ts',
    'renderer/index': 'src/renderer/index.ts',
  },
  format: ['cjs', 'esm'],
  dts: false, // Temporarily disable DTS generation due to complex Puck types
  splitting: false,
  sourcemap: true,
  clean: true,
  // Copy CSS files to dist
  publicDir: false,
  loader: {
    '.css': 'copy',
  },
  external: [
    'react',
    'react-dom',
    'zmp-ui',
    'zmp-sdk',
    '@measured/puck',
    '@supabase/supabase-js',
    'framer-motion',
    'lucide-react',
    'tailwindcss',
    'clsx',
    '@tailwindcss/postcss',
    'postcss'
  ],
  esbuildOptions(options) {
    options.jsx = 'automatic';
    // Handle CSS imports
    options.loader = {
      ...options.loader,
      '.css': 'css',
    };
  },
});
