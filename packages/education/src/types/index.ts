// Education Platform Types

export interface Organization {
  id: string;
  account_id: string;
  name: string;
  organization_type: string;
  address?: string;
  phone?: string;
  email?: string;
  director_name?: string;
  license_number?: string;
  business_hours?: Record<string, any>;
  settings?: Record<string, any>;
  logo_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Program {
  id: string;
  organization_id: string;
  name: string;
  program_type?: string;
  description?: string;
  age_range?: string;
  duration_months?: number;
  capacity: number;
  current_enrollments: number;
  instructor_id?: string;
  assistant_id?: string;
  location?: string;
  schedule?: Record<string, any>;
  fee_structure?: Record<string, any>;
  status: 'active' | 'inactive' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
}

export interface Learner {
  id: string;
  organization_id: string;
  learner_code: string;
  full_name: string;
  nickname?: string;
  date_of_birth?: string;
  gender?: 'male' | 'female' | 'other';
  address?: string;
  health_info?: Record<string, any>;
  emergency_contact?: Record<string, any>;
  enrollment_date: string;
  status: 'active' | 'inactive' | 'graduated' | 'transferred';
  avatar_url?: string;
  notes?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface Guardian {
  id: string;
  user_id?: string;
  organization_id: string;
  full_name: string;
  phone: string;
  email?: string;
  relationship: string;
  occupation?: string;
  zalo_id?: string;
  is_primary: boolean;
  notification_preferences?: Record<string, any>;
  created_at: string;
}

export interface Enrollment {
  id: string;
  learner_id: string;
  program_id: string;
  enrollment_date: string;
  completion_date?: string;
  status: 'active' | 'completed' | 'dropped' | 'transferred';
  progress_data?: Record<string, any>;
  final_assessment?: Record<string, any>;
  created_at: string;
}

export interface Attendance {
  id: string;
  learner_id: string;
  program_id: string;
  session_date: string;
  session_time?: string;
  check_in_time?: string;
  check_out_time?: string;
  status: 'present' | 'absent' | 'late' | 'excused' | 'sick';
  notes?: string;
  checked_by?: string;
  guardian_notified: boolean;
  created_at: string;
}

export interface ProgressReport {
  id: string;
  learner_id: string;
  program_id: string;
  report_date: string;
  instructor_id: string;
  report_type: 'daily' | 'weekly' | 'monthly' | 'assessment';
  content: Record<string, any>;
  overall_rating?: 'excellent' | 'good' | 'average' | 'needs_improvement';
  guardian_notified: boolean;
  guardian_feedback?: string;
  created_at: string;
}

export interface Fee {
  id: string;
  organization_id: string;
  learner_id: string;
  program_id?: string;
  fee_category: string;
  amount: number;
  currency: string;
  billing_period?: string;
  due_date: string;
  description?: string;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled' | 'refunded';
  payment_method?: string;
  payment_date?: string;
  payment_reference?: string;
  discount_amount?: number;
  late_fee?: number;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface Event {
  id: string;
  organization_id: string;
  title: string;
  description?: string;
  event_type?: string;
  start_datetime: string;
  end_datetime?: string;
  location?: string;
  target_audience: 'all' | 'guardians' | 'instructors' | 'learners' | 'specific_programs';
  program_ids?: string[];
  registration_required: boolean;
  max_participants?: number;
  current_participants: number;
  fee?: number;
  images?: string[];
  metadata?: Record<string, any>;
  created_by?: string;
  created_at: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages?: number;
  };
}

// Guardian API Types
export interface GuardianChildrenResponse {
  guardian: {
    id: string;
    fullName: string;
    relationship: string;
  };
  children: Array<{
    id: string;
    learnerCode: string;
    fullName: string;
    nickname?: string;
    dateOfBirth?: string;
    gender?: string;
    avatarUrl?: string;
    status: string;
    relationship: string;
    isEmergencyContact: boolean;
    canPickup: boolean;
    programs: Array<{
      enrollmentId: string;
      enrollmentStatus: string;
      enrollmentDate: string;
      program: {
        id: string;
        name: string;
        type: string;
        ageRange?: string;
        location?: string;
        schedule?: Record<string, any>;
      };
    }>;
  }>;
  totalChildren: number;
}

export interface AttendanceStats {
  totalDays: number;
  presentDays: number;
  absentDays: number;
  lateDays: number;
  sickDays: number;
  excusedDays: number;
  attendanceRate: number;
}

export interface AttendanceResponse {
  records: Array<{
    id: string;
    date: string;
    sessionTime?: string;
    checkInTime?: string;
    checkOutTime?: string;
    status: string;
    statusText: string;
    statusColor: string;
    notes?: string;
    guardianNotified: boolean;
    program?: any;
    checkedBy?: string;
    createdAt: string;
  }>;
  stats: AttendanceStats;
  period: {
    fromDate: string;
    toDate: string;
    month?: string;
  };
}

// Instructor API Types
export interface InstructorClassesResponse {
  programs: Array<{
    id: string;
    name: string;
    type: string;
    typeText: string;
    description?: string;
    ageRange?: string;
    capacity: number;
    currentEnrollments: number;
    enrollmentRate: number;
    location?: string;
    schedule?: Record<string, any>;
    status: string;
    role: 'instructor' | 'assistant';
    instructor?: any;
    assistant?: any;
    students: Array<{
      enrollmentId: string;
      enrollmentDate: string;
      learner: {
        id: string;
        learnerCode: string;
        fullName: string;
        nickname?: string;
        dateOfBirth?: string;
        gender?: string;
        avatarUrl?: string;
        age: number;
        healthInfo?: Record<string, any>;
      };
    }>;
    totalStudents: number;
    createdAt: string;
  }>;
  summary: {
    totalClasses: number;
    totalStudents: number;
    averageEnrollmentRate: number;
    instructorClasses: number;
    assistantClasses: number;
  };
  instructor: {
    id: string;
    name: string;
  };
}

// Education User Context
export interface EducationUserContext {
  userId: string;
  name: string;
  phone: string;
  picture: string;
  email: string;
  accountRole: string;
  organizationId?: string;
  relationshipData?: any;
}
