// Education Utility Functions

export function calculateAge(dateOfBirth: string): number {
  if (!dateOfBirth) return 0;
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}

export function formatVietnameseDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('vi-VN', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

export function formatVietnameseTime(timeString: string): string {
  const time = new Date(`2000-01-01T${timeString}`);
  return time.toLocaleTimeString('vi-VN', {
    hour: '2-digit',
    minute: '2-digit',
  });
}

export function getAttendanceStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    present: '#10B981', // green
    absent: '#EF4444',  // red
    late: '#F59E0B',    // yellow
    sick: '#8B5CF6',    // purple
    excused: '#6B7280', // gray
  };
  return colorMap[status] || '#6B7280';
}

export function getAttendanceStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    present: 'Có mặt',
    absent: 'Vắng mặt',
    late: 'Đi muộn',
    sick: 'Ốm',
    excused: 'Có phép',
  };
  return statusMap[status] || status;
}

export function getRatingColor(rating: string): string {
  const colorMap: Record<string, string> = {
    excellent: '#10B981',     // green
    good: '#3B82F6',          // blue
    average: '#F59E0B',       // yellow
    needs_improvement: '#EF4444', // red
  };
  return colorMap[rating] || '#6B7280';
}

export function getRatingText(rating: string): string {
  const ratingMap: Record<string, string> = {
    excellent: 'Xuất sắc',
    good: 'Tốt',
    average: 'Trung bình',
    needs_improvement: 'Cần cải thiện',
  };
  return ratingMap[rating] || rating;
}

export function getFeeStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    pending: '#F59E0B',    // yellow
    paid: '#10B981',       // green
    overdue: '#EF4444',    // red
    cancelled: '#6B7280',  // gray
    refunded: '#8B5CF6',   // purple
  };
  return colorMap[status] || '#6B7280';
}

export function getFeeStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    pending: 'Chưa thanh toán',
    paid: 'Đã thanh toán',
    overdue: 'Quá hạn',
    cancelled: 'Đã hủy',
    refunded: 'Đã hoàn tiền',
  };
  return statusMap[status] || status;
}

export function getFeeCategoryText(category: string): string {
  const categoryMap: Record<string, string> = {
    tuition: 'Học phí',
    meal: 'Tiền ăn',
    material: 'Đồ dùng học tập',
    activity: 'Hoạt động ngoại khóa',
    transport: 'Xe đưa đón',
    registration: 'Lệ phí đăng ký',
  };
  return categoryMap[category] || category;
}

export function getBillingPeriodText(period: string): string {
  const periodMap: Record<string, string> = {
    monthly: 'Hàng tháng',
    quarterly: 'Hàng quý',
    semester: 'Học kỳ',
    annual: 'Hàng năm',
    one_time: 'Một lần',
  };
  return periodMap[period] || period;
}

export function getProgramTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    regular_class: 'Lớp học thường',
    talent_program: 'Chương trình năng khiếu',
    special_course: 'Khóa học đặc biệt',
    music_program: 'Chương trình âm nhạc',
    art_program: 'Chương trình mỹ thuật',
    dance_program: 'Chương trình múa',
    language_program: 'Chương trình ngoại ngữ',
  };
  return typeMap[type] || type;
}

export function getReportTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    daily: 'Báo cáo hàng ngày',
    weekly: 'Báo cáo tuần',
    monthly: 'Báo cáo tháng',
    assessment: 'Đánh giá',
  };
  return typeMap[type] || type;
}

export function formatCurrency(amount: number, currency: string = 'VND'): string {
  if (currency === 'VND') {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  }
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount);
}

export function isOverdue(dueDate: string): boolean {
  const today = new Date().toISOString().split('T')[0];
  return dueDate < today;
}

export function getDaysUntilDue(dueDate: string): number {
  const today = new Date();
  const due = new Date(dueDate);
  const diffTime = due.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

export function getMonthName(month: number): string {
  const monthNames = [
    'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
    'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
  ];
  return monthNames[month - 1] || `Tháng ${month}`;
}

export function getWeekdayName(dayIndex: number): string {
  const weekdays = [
    'Chủ nhật', 'Thứ hai', 'Thứ ba', 'Thứ tư', 'Thứ năm', 'Thứ sáu', 'Thứ bảy'
  ];
  return weekdays[dayIndex] || '';
}

export function generateLearnerCode(organizationName: string, sequence: number): string {
  // Lấy 3 ký tự đầu của tên tổ chức
  const prefix = organizationName
    .replace(/[^a-zA-Z]/g, '')
    .toUpperCase()
    .substring(0, 3)
    .padEnd(3, 'X');
  
  // Tạo mã với 4 chữ số
  const code = sequence.toString().padStart(4, '0');
  
  return `${prefix}${code}`;
}

export function validatePhone(phone: string): boolean {
  // Vietnamese phone number validation
  const phoneRegex = /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

export function normalizePhone(phone: string): string {
  // Remove all non-digits and replace leading 0 with 84
  return phone.replace(/\D/g, '').replace(/^0/, '84');
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function getAgeGroup(dateOfBirth: string): string {
  const age = calculateAge(dateOfBirth);
  
  if (age < 3) return 'Dưới 3 tuổi';
  if (age <= 4) return '3-4 tuổi';
  if (age <= 5) return '4-5 tuổi';
  if (age <= 6) return '5-6 tuổi';
  if (age <= 12) return '6-12 tuổi';
  if (age <= 18) return '12-18 tuổi';
  
  return 'Trên 18 tuổi';
}

export function calculateAttendanceRate(present: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((present / total) * 100);
}

export function getAttendanceRateColor(rate: number): string {
  if (rate >= 90) return '#10B981'; // green
  if (rate >= 80) return '#3B82F6'; // blue
  if (rate >= 70) return '#F59E0B'; // yellow
  return '#EF4444'; // red
}

export function formatSchedule(schedule: Record<string, any>): string {
  if (!schedule || typeof schedule !== 'object') return '';
  
  const days = Object.keys(schedule);
  if (days.length === 0) return '';
  
  const dayNames: Record<string, string> = {
    monday: 'T2',
    tuesday: 'T3',
    wednesday: 'T4',
    thursday: 'T5',
    friday: 'T6',
    saturday: 'T7',
    sunday: 'CN',
  };
  
  const formattedDays = days
    .map(day => dayNames[day] || day)
    .join(', ');
  
  const firstDay = schedule[days[0]];
  if (firstDay && firstDay.start && firstDay.end) {
    return `${formattedDays} (${firstDay.start} - ${firstDay.end})`;
  }
  
  return formattedDays;
}
