export interface NavigationItem {
  id: string;
  title: string;
  path: string;
  icon: string;
  description?: string;
  badge?: string | number;
  children?: NavigationItem[];
}

export interface UserContext {
  id: string;
  role: string;
  organizationId?: string;
  isEducation: boolean;
}

export function getNavigationByRole(
  userContext: UserContext,
  organizationId: string
): NavigationItem[] {
  const baseNavigation: NavigationItem[] = [
    {
      id: 'home',
      title: 'Trang chủ',
      path: '/',
      icon: 'home',
      description: 'Trang chủ chính',
    },
  ];

  if (!userContext.isEducation) {
    return baseNavigation;
  }

  switch (userContext.role) {
    case 'guardian':
      return [
        ...baseNavigation,
        {
          id: 'children',
          title: 'Con em',
          path: '/children',
          icon: 'users',
          description: 'Thông tin con em',
          children: [
            {
              id: 'children-list',
              title: 'Danh sách con em',
              path: '/children',
              icon: 'list',
            },
            {
              id: 'children-profile',
              title: '<PERSON><PERSON> sơ chi tiết',
              path: '/children/profile',
              icon: 'user',
            },
          ],
        },
        {
          id: 'attendance',
          title: 'Điểm danh',
          path: '/attendance',
          icon: 'check-circle',
          description: '<PERSON><PERSON><PERSON> sử điểm danh',
          children: [
            {
              id: 'attendance-daily',
              title: 'Hàng ngày',
              path: '/attendance/daily',
              icon: 'calendar-day',
            },
            {
              id: 'attendance-monthly',
              title: 'Theo tháng',
              path: '/attendance/monthly',
              icon: 'calendar',
            },
            {
              id: 'attendance-summary',
              title: 'Tổng hợp',
              path: '/attendance/summary',
              icon: 'chart-bar',
            },
          ],
        },
        {
          id: 'reports',
          title: 'Báo cáo',
          path: '/reports',
          icon: 'file-text',
          description: 'Báo cáo học tập',
          children: [
            {
              id: 'reports-daily',
              title: 'Báo cáo hàng ngày',
              path: '/reports/daily',
              icon: 'file',
            },
            {
              id: 'reports-progress',
              title: 'Tiến độ học tập',
              path: '/reports/progress',
              icon: 'trending-up',
            },
            {
              id: 'reports-assessment',
              title: 'Đánh giá',
              path: '/reports/assessment',
              icon: 'star',
            },
          ],
        },
        {
          id: 'fees',
          title: 'Học phí',
          path: '/fees',
          icon: 'credit-card',
          description: 'Quản lý học phí',
          children: [
            {
              id: 'fees-pending',
              title: 'Chưa thanh toán',
              path: '/fees/pending',
              icon: 'clock',
            },
            {
              id: 'fees-paid',
              title: 'Đã thanh toán',
              path: '/fees/paid',
              icon: 'check',
            },
            {
              id: 'fees-history',
              title: 'Lịch sử',
              path: '/fees/history',
              icon: 'history',
            },
          ],
        },
        {
          id: 'events',
          title: 'Sự kiện',
          path: '/events',
          icon: 'calendar',
          description: 'Sự kiện và hoạt động',
        },
        {
          id: 'contact',
          title: 'Liên hệ',
          path: '/contact',
          icon: 'message-circle',
          description: 'Liên hệ với giáo viên',
          children: [
            {
              id: 'contact-teachers',
              title: 'Giáo viên',
              path: '/contact/teachers',
              icon: 'user-check',
            },
            {
              id: 'contact-admin',
              title: 'Ban giám hiệu',
              path: '/contact/admin',
              icon: 'shield',
            },
          ],
        },
      ];

    case 'instructor':
      return [
        ...baseNavigation,
        {
          id: 'classes',
          title: 'Lớp học',
          path: '/classes',
          icon: 'users',
          description: 'Quản lý lớp học',
          children: [
            {
              id: 'classes-list',
              title: 'Danh sách lớp',
              path: '/classes',
              icon: 'list',
            },
            {
              id: 'classes-schedule',
              title: 'Lịch dạy',
              path: '/classes/schedule',
              icon: 'calendar',
            },
          ],
        },
        {
          id: 'attendance-manage',
          title: 'Điểm danh',
          path: '/attendance/manage',
          icon: 'check-circle',
          description: 'Quản lý điểm danh',
          children: [
            {
              id: 'attendance-take',
              title: 'Điểm danh',
              path: '/attendance/take',
              icon: 'check',
            },
            {
              id: 'attendance-qr',
              title: 'QR Code',
              path: '/attendance/qr',
              icon: 'qr-code',
            },
            {
              id: 'attendance-history',
              title: 'Lịch sử',
              path: '/attendance/history',
              icon: 'history',
            },
          ],
        },
        {
          id: 'reports-create',
          title: 'Báo cáo',
          path: '/reports/create',
          icon: 'edit',
          description: 'Tạo báo cáo',
          children: [
            {
              id: 'reports-daily-create',
              title: 'Báo cáo hàng ngày',
              path: '/reports/create/daily',
              icon: 'file-plus',
            },
            {
              id: 'reports-assessment-create',
              title: 'Đánh giá học viên',
              path: '/reports/create/assessment',
              icon: 'star',
            },
            {
              id: 'reports-templates',
              title: 'Mẫu báo cáo',
              path: '/reports/templates',
              icon: 'template',
            },
          ],
        },
        {
          id: 'students',
          title: 'Học viên',
          path: '/students',
          icon: 'graduation-cap',
          description: 'Quản lý học viên',
          children: [
            {
              id: 'students-list',
              title: 'Danh sách',
              path: '/students',
              icon: 'list',
            },
            {
              id: 'students-progress',
              title: 'Tiến độ',
              path: '/students/progress',
              icon: 'trending-up',
            },
          ],
        },
        {
          id: 'parents',
          title: 'Phụ huynh',
          path: '/parents',
          icon: 'users',
          description: 'Liên hệ phụ huynh',
          children: [
            {
              id: 'parents-contact',
              title: 'Danh bạ',
              path: '/parents/contact',
              icon: 'phone',
            },
            {
              id: 'parents-messages',
              title: 'Tin nhắn',
              path: '/parents/messages',
              icon: 'message-square',
            },
          ],
        },
      ];

    case 'education_admin':
      return [
        ...baseNavigation,
        {
          id: 'dashboard',
          title: 'Dashboard',
          path: '/admin',
          icon: 'bar-chart',
          description: 'Tổng quan hệ thống',
        },
        {
          id: 'learners-admin',
          title: 'Học viên',
          path: '/admin/learners',
          icon: 'users',
          description: 'Quản lý học viên',
          children: [
            {
              id: 'learners-list',
              title: 'Danh sách',
              path: '/admin/learners',
              icon: 'list',
            },
            {
              id: 'learners-add',
              title: 'Thêm mới',
              path: '/admin/learners/add',
              icon: 'user-plus',
            },
            {
              id: 'learners-import',
              title: 'Import',
              path: '/admin/learners/import',
              icon: 'upload',
            },
          ],
        },
        {
          id: 'instructors-admin',
          title: 'Giáo viên',
          path: '/admin/instructors',
          icon: 'user-check',
          description: 'Quản lý giáo viên',
          children: [
            {
              id: 'instructors-list',
              title: 'Danh sách',
              path: '/admin/instructors',
              icon: 'list',
            },
            {
              id: 'instructors-schedule',
              title: 'Lịch dạy',
              path: '/admin/instructors/schedule',
              icon: 'calendar',
            },
            {
              id: 'instructors-performance',
              title: 'Hiệu suất',
              path: '/admin/instructors/performance',
              icon: 'trending-up',
            },
          ],
        },
        {
          id: 'programs-admin',
          title: 'Chương trình',
          path: '/admin/programs',
          icon: 'book',
          description: 'Quản lý chương trình học',
          children: [
            {
              id: 'programs-list',
              title: 'Danh sách',
              path: '/admin/programs',
              icon: 'list',
            },
            {
              id: 'programs-add',
              title: 'Thêm mới',
              path: '/admin/programs/add',
              icon: 'plus',
            },
            {
              id: 'programs-schedule',
              title: 'Lịch học',
              path: '/admin/programs/schedule',
              icon: 'calendar',
            },
          ],
        },
        {
          id: 'fees-admin',
          title: 'Học phí',
          path: '/admin/fees',
          icon: 'dollar-sign',
          description: 'Quản lý học phí',
          children: [
            {
              id: 'fees-overview',
              title: 'Tổng quan',
              path: '/admin/fees',
              icon: 'pie-chart',
            },
            {
              id: 'fees-create',
              title: 'Tạo học phí',
              path: '/admin/fees/create',
              icon: 'plus',
            },
            {
              id: 'fees-reports',
              title: 'Báo cáo',
              path: '/admin/fees/reports',
              icon: 'file-text',
            },
          ],
        },
        {
          id: 'reports-admin',
          title: 'Báo cáo',
          path: '/admin/reports',
          icon: 'file-text',
          description: 'Báo cáo tổng hợp',
          children: [
            {
              id: 'reports-attendance',
              title: 'Điểm danh',
              path: '/admin/reports/attendance',
              icon: 'check-circle',
            },
            {
              id: 'reports-academic',
              title: 'Học tập',
              path: '/admin/reports/academic',
              icon: 'book-open',
            },
            {
              id: 'reports-financial',
              title: 'Tài chính',
              path: '/admin/reports/financial',
              icon: 'dollar-sign',
            },
          ],
        },
        {
          id: 'settings',
          title: 'Cài đặt',
          path: '/admin/settings',
          icon: 'settings',
          description: 'Cài đặt hệ thống',
        },
      ];

    case 'guest':
      return [
        ...baseNavigation,
        {
          id: 'about',
          title: 'Giới thiệu',
          path: '/about',
          icon: 'info',
          description: 'Thông tin trường học',
        },
        {
          id: 'programs-info',
          title: 'Chương trình',
          path: '/programs',
          icon: 'book',
          description: 'Các chương trình học',
        },
        {
          id: 'contact-info',
          title: 'Liên hệ',
          path: '/contact',
          icon: 'phone',
          description: 'Thông tin liên hệ',
        },
        {
          id: 'register',
          title: 'Đăng ký',
          path: '/register',
          icon: 'user-plus',
          description: 'Đăng ký học',
        },
      ];

    default:
      return baseNavigation;
  }
}

export function getQuickActions(userContext: UserContext): NavigationItem[] {
  if (!userContext.isEducation) {
    return [];
  }

  switch (userContext.role) {
    case 'guardian':
      return [
        {
          id: 'check-attendance',
          title: 'Xem điểm danh',
          path: '/attendance',
          icon: 'check-circle',
        },
        {
          id: 'view-reports',
          title: 'Báo cáo mới nhất',
          path: '/reports',
          icon: 'file-text',
        },
        {
          id: 'pay-fees',
          title: 'Thanh toán học phí',
          path: '/fees/pending',
          icon: 'credit-card',
        },
        {
          id: 'contact-teacher',
          title: 'Liên hệ giáo viên',
          path: '/contact/teachers',
          icon: 'message-circle',
        },
      ];

    case 'instructor':
      return [
        {
          id: 'take-attendance',
          title: 'Điểm danh',
          path: '/attendance/take',
          icon: 'check',
        },
        {
          id: 'create-report',
          title: 'Tạo báo cáo',
          path: '/reports/create/daily',
          icon: 'edit',
        },
        {
          id: 'view-schedule',
          title: 'Lịch dạy',
          path: '/classes/schedule',
          icon: 'calendar',
        },
        {
          id: 'student-list',
          title: 'Danh sách học viên',
          path: '/students',
          icon: 'users',
        },
      ];

    case 'education_admin':
      return [
        {
          id: 'dashboard',
          title: 'Dashboard',
          path: '/admin',
          icon: 'bar-chart',
        },
        {
          id: 'add-learner',
          title: 'Thêm học viên',
          path: '/admin/learners/add',
          icon: 'user-plus',
        },
        {
          id: 'create-fees',
          title: 'Tạo học phí',
          path: '/admin/fees/create',
          icon: 'dollar-sign',
        },
        {
          id: 'view-reports',
          title: 'Báo cáo tổng hợp',
          path: '/admin/reports',
          icon: 'file-text',
        },
      ];

    default:
      return [];
  }
}
