{"name": "@kit/education", "version": "0.1.0", "private": true, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext .ts,.tsx", "typecheck": "tsc --noEmit"}, "dependencies": {"@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "zod": "^3.24.2"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/node": "^22.14.0", "eslint": "^9.24.0", "typescript": "^5.8.3"}, "exports": {"./navigation": "./dist/navigation/index.js", "./types": "./dist/types/index.js", "./utils": "./dist/utils/index.js"}, "typesVersions": {"*": {"navigation": ["./dist/navigation/index.d.ts"], "types": ["./dist/types/index.d.ts"], "utils": ["./dist/utils/index.d.ts"]}}}