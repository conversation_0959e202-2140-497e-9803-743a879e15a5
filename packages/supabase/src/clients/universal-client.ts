// File này an toàn để sử dụng trong cả client và server
// KHÔNG import 'server-only' hoặc 'next/headers'

import { createClient } from '@supabase/supabase-js';

import { Database } from '../database.types';
import { getSupabaseClientKeys } from '../get-supabase-client-keys';

/**
 * @name getUniversalSupabaseClient
 * @description Creates a Supabase client that is safe to use in both client and server
 */
export function getUniversalSupabaseClient<GenericSchema = Database>() {
  const keys = getSupabaseClientKeys();

  return createClient<GenericSchema>(keys.url, keys.anonKey, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false,
    },
  });
}
