// File này an toàn để sử dụng trong client components
// KHÔNG import 'server-only' hoặc 'next/headers'

import { createClient } from '@supabase/supabase-js';

import { Database } from '../database.types';
import { getSupabaseClientKeys } from '../get-supabase-client-keys';

/**
 * @name getClientSafeSupabaseClient
 * @description Creates a Supabase client that is safe to use in client components
 */
export function getClientSafeSupabaseClient<GenericSchema = Database>() {
  const keys = getSupabaseClientKeys();

  return createClient<GenericSchema>(keys.url, keys.anonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
  });
}
