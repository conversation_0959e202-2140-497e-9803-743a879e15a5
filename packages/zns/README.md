# ZNS (Zalo Notification Service) Package

Gói ZNS cung cấp tích hợp hoàn chỉnh với Zalo Notification Service, bao gồm quản lý template, mapping sự kiện, và gửi tin nhắn tự động.

## 📋 Mục lục

- [<PERSON><PERSON><PERSON> đặt](#cài-đặt)
- [<PERSON><PERSON><PERSON> hình](#cấu-hình)
- [Sử dụng cơ bản](#sử-dụng-cơ-bản)
- [Hooks](#hooks)
- [Event System](#event-system)
- [Template Management](#template-management)
- [Mapping System](#mapping-system)
- [Mở rộng](#mở-rộng)
- [API Reference](#api-reference)

## 🚀 Cài đặt

```bash
# Package đã được cài đặt sẵn trong monorepo
# Import trong ứng dụng:
import { useZnsEventHandler, ZNS_EVENT_TYPES } from '@kit/zns';
```

## ⚙️ Cấu hình

### 1. Database Setup

Đảm bảo các bảng sau đã được tạo:

```sql
-- ZNS Templates
CREATE TABLE zns_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_id TEXT NOT NULL,
  template_name TEXT NOT NULL,
  event_type TEXT,
  metadata JSONB,
  enabled BOOLEAN DEFAULT true,
  account_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ZNS Mappings
CREATE TABLE zns_mappings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  module TEXT NOT NULL,
  event_type TEXT NOT NULL,
  template_id UUID REFERENCES zns_templates(id),
  parameter_mapping JSONB DEFAULT '{}',
  recipient_path TEXT DEFAULT 'customer.phone',
  enabled BOOLEAN DEFAULT true,
  account_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ZNS Usage Tracking
CREATE TABLE zns_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL,
  mapping_id UUID REFERENCES zns_mappings(id),
  template_id UUID REFERENCES zns_templates(id),
  recipient TEXT NOT NULL,
  status TEXT NOT NULL,
  event_type TEXT,
  metadata JSONB,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. Environment Variables

```env
# Zalo OA Configuration
ZALO_APP_ID=your_app_id
ZALO_APP_SECRET=your_app_secret
```

## 🎯 Sử dụng cơ bản

### 1. Gửi ZNS thủ công

```typescript
import { sendZnsMessage } from '@kit/zns';
import { createClient } from '@kit/supabase/server';

const supabase = createClient();

const result = await sendZnsMessage({
  supabase,
  templateId: 'your-template-id',
  recipient: '***********',
  parameters: {
    customer_name: 'Nguyễn Văn A',
    order_id: 'ORD-12345',
    order_total: '500,000 VND'
  },
  oaConfigId: 'your-oa-config-id',
  trackingId: 'unique-tracking-id'
});
```

### 2. Trigger Event tự động

```typescript
import { handleZnsEvent } from '@kit/zns';
import { createClient } from '@kit/supabase/server';

const supabase = createClient();

// Trigger khi có đơn hàng mới
await handleZnsEvent(supabase, {
  module: 'orders',
  event: 'order_confirmation',
  data: {
    customer: {
      name: 'Nguyễn Văn A',
      phone: '**********',
      email: '<EMAIL>'
    },
    order: {
      id: 'ORD-12345',
      total: '500000',
      created_at: new Date().toISOString()
    }
  },
  metadata: {
    accountId: 'account-uuid',
    timestamp: new Date().toISOString()
  }
});
```

## 🎣 Hooks

### useZnsEventHandler

Hook để xử lý ZNS events trong React components:

```typescript
import { useZnsEventHandler } from '@kit/zns';

function OrderComponent() {
  const { triggerZnsEvent, isLoading, error } = useZnsEventHandler();

  const handleOrderCreated = async (orderData) => {
    await triggerZnsEvent({
      module: 'orders',
      event: 'order_confirmation',
      data: {
        customer: orderData.customer,
        order: orderData.order
      }
    });
  };

  return (
    <button
      onClick={() => handleOrderCreated(orderData)}
      disabled={isLoading}
    >
      {isLoading ? 'Đang gửi...' : 'Xác nhận đơn hàng'}
    </button>
  );
}
```

### useZnsTemplates

Hook để quản lý ZNS templates:

```typescript
import { useZnsTemplates } from '@kit/zns';

function TemplateManager() {
  const {
    templates,
    isLoading,
    createTemplate,
    updateTemplate,
    deleteTemplate
  } = useZnsTemplates();

  const handleCreateTemplate = async () => {
    await createTemplate({
      template_name: 'Order Confirmation',
      event_type: 'order_confirmation',
      metadata: {
        params: [
          { name: 'customer_name', type: '1' },
          { name: 'order_id', type: '4' }
        ]
      }
    });
  };

  return (
    <div>
      {templates?.map(template => (
        <div key={template.id}>
          {template.template_name}
        </div>
      ))}
    </div>
  );
}
```

### useZnsMappings

Hook để quản lý ZNS mappings:

```typescript
import { useZnsMappings } from '@kit/zns';

function MappingManager() {
  const {
    mappings,
    isLoading,
    createMapping,
    updateMapping,
    toggleMapping
  } = useZnsMappings();

  const handleCreateMapping = async () => {
    await createMapping({
      name: 'Order Confirmation Notification',
      module: 'orders',
      event_type: 'order_confirmation',
      template_id: 'template-uuid',
      parameter_mapping: {
        customer_name: 'customer.name',
        order_id: 'order.id'
      }
    });
  };

  return (
    <div>
      {mappings?.map(mapping => (
        <div key={mapping.id}>
          <span>{mapping.name}</span>
          <button onClick={() => toggleMapping(mapping.id, !mapping.enabled)}>
            {mapping.enabled ? 'Tắt' : 'Bật'}
          </button>
        </div>
      ))}
    </div>
  );
}
```

## 🎪 Event System

### Supported Event Types

```typescript
// packages/zns/src/config/event-types.ts
export const ZNS_EVENT_TYPES = [
  {
    id: 'order_confirmation',
    name: 'Xác nhận đơn hàng',
    description: 'Gửi khi đơn hàng được tạo thành công',
    icon: '📦',
    category: 'orders'
  },
  {
    id: 'payment_confirmation',
    name: 'Xác nhận thanh toán',
    description: 'Gửi khi thanh toán thành công',
    icon: '💳',
    category: 'orders'
  },
  // ... more events
];
```

### Default Parameter Mapping

```typescript
// packages/zns/src/config/default-keys.ts
export const ZNS_DEFAULT_KEYS = [
  {
    key: 'customer_name',
    path: 'customer.name',
    paramType: '1',
    description: 'Tên khách hàng',
    category: 'customer',
    example: 'Nguyễn Văn A'
  },
  {
    key: 'order_id',
    path: 'order.id',
    paramType: '4',
    description: 'Mã đơn hàng',
    category: 'order',
    example: 'ORD-12345'
  },
  // ... more keys
];
```

## 📝 Template Management

### Tạo Template

```typescript
import { createZnsTemplate } from '@kit/zns';

const template = await createZnsTemplate({
  template_name: 'Order Confirmation',
  event_type: 'order_confirmation',
  content: 'Xin chào {{customer_name}}, đơn hàng {{order_id}} đã được xác nhận.',
  metadata: {
    params: [
      { name: 'customer_name', type: '1' },
      { name: 'order_id', type: '4' }
    ]
  }
});
```

### Template với Parameters

```typescript
// Template metadata structure
{
  "params": [
    {
      "name": "customer_name",
      "type": "1",        // Text parameter
      "required": true
    },
    {
      "name": "order_total",
      "type": "5",        // Currency parameter
      "required": true
    }
  ],
  "design": {
    "header": {
      "type": "LOGO",
      "content": "logo_url"
    },
    "body": [
      {
        "type": "TITLE",
        "content": "Xác nhận đơn hàng"
      },
      {
        "type": "PARAGRAPH",
        "content": "Đơn hàng {{order_id}} của bạn đã được xác nhận."
      }
    ]
  }
}
```

## 🗺️ Mapping System

### Auto vs Manual Parameters

```typescript
import { categorizeTemplateParams } from '@kit/zns';

const templateParams = [
  { name: 'customer_name', type: '1' },  // Auto (có trong ZNS_DEFAULT_KEYS)
  { name: 'order_id', type: '4' },       // Auto (có trong ZNS_DEFAULT_KEYS)
  { name: 'bank_name', type: '15' }      // Manual (không có trong ZNS_DEFAULT_KEYS)
];

const { autoParams, manualParams } = categorizeTemplateParams(templateParams);

// autoParams: [{ param: {...}, config: {...} }]
// manualParams: [{ name: 'bank_name', type: '15' }]
```

### Parameter Mapping

```typescript
// Auto mapping
const autoMapping = generateAutoMapping(templateParams);
// Result: { customer_name: 'customer.name', order_id: 'order.id' }

// Manual mapping (user input required)
const manualMapping = {
  bank_name: 'Vietcombank',
  custom_message: 'Cảm ơn bạn đã mua hàng!'
};

// Final mapping
const finalMapping = { ...autoMapping, ...manualMapping };
```

## 🔧 Mở rộng

### Thêm Event Type mới

1. **Cập nhật ZNS_EVENT_TYPES:**

```typescript
// packages/zns/src/config/event-types.ts
export const ZNS_EVENT_TYPES = [
  // ... existing events
  {
    id: 'appointment_reminder',
    name: 'Nhắc hẹn',
    description: 'Gửi nhắc nhở cuộc hẹn',
    icon: '📅',
    category: 'appointments',
    defaultKeys: ['customer_name', 'appointment_date', 'appointment_time']
  }
];
```

2. **Thêm Default Keys (nếu cần):**

```typescript
// packages/zns/src/config/default-keys.ts
export const ZNS_DEFAULT_KEYS = [
  // ... existing keys
  {
    key: 'appointment_date',
    path: 'appointment.date',
    paramType: '6',
    description: 'Ngày hẹn',
    category: 'appointment',
    example: '25/12/2024'
  },
  {
    key: 'appointment_time',
    path: 'appointment.time',
    paramType: '7',
    description: 'Giờ hẹn',
    category: 'appointment',
    example: '14:30'
  }
];
```

3. **Cập nhật Module Structure:**

```typescript
// Trong UI components (CreateMappingDialog, MappingsPage)
const eventTypesByModule = {
  // ... existing modules
  appointments: [
    ZNS_EVENT_TYPES.find(t => t.id === 'appointment_reminder')!,
    ZNS_EVENT_TYPES.find(t => t.id === 'appointment_confirmation')!,
  ],
};

const modules = [
  // ... existing modules
  {
    id: 'appointments',
    name: 'Lịch hẹn',
    description: 'Sự kiện liên quan đến lịch hẹn',
    icon: '📅',
    events: eventTypesByModule.appointments,
  },
];
```

### Thêm Module mới

1. **Định nghĩa Module Events:**

```typescript
// Ví dụ: Module Education
const educationEvents = [
  {
    id: 'course_enrollment',
    name: 'Đăng ký khóa học',
    description: 'Gửi khi học viên đăng ký khóa học',
    icon: '📚',
    category: 'education'
  },
  {
    id: 'class_reminder',
    name: 'Nhắc nhở lớp học',
    description: 'Gửi nhắc nhở trước giờ học',
    icon: '🔔',
    category: 'education'
  }
];
```

2. **Thêm vào ZNS_EVENT_TYPES:**

```typescript
export const ZNS_EVENT_TYPES = [
  // ... existing events
  ...educationEvents
];
```

3. **Cập nhật Default Keys cho Module:**

```typescript
export const ZNS_DEFAULT_KEYS = [
  // ... existing keys
  {
    key: 'student_name',
    path: 'student.name',
    paramType: '1',
    description: 'Tên học viên',
    category: 'education',
    example: 'Nguyễn Văn A'
  },
  {
    key: 'course_name',
    path: 'course.name',
    paramType: '13',
    description: 'Tên khóa học',
    category: 'education',
    example: 'Lập trình React'
  }
];
```

4. **Trigger Events trong Code:**

```typescript
// Trong education service
import { handleZnsEvent } from '@kit/zns';

export async function enrollStudent(studentData, courseData) {
  // ... business logic

  // Trigger ZNS event
  await handleZnsEvent(supabase, {
    module: 'education',
    event: 'course_enrollment',
    data: {
      student: {
        name: studentData.name,
        phone: studentData.phone,
        email: studentData.email
      },
      course: {
        name: courseData.name,
        start_date: courseData.start_date,
        instructor: courseData.instructor
      }
    },
    metadata: {
      accountId: studentData.account_id,
      timestamp: new Date().toISOString()
    }
  });
}
```

### Custom Parameter Types

```typescript
// Thêm parameter type mới
export const ZNS_PARAM_TYPES = {
  // ... existing types
  CUSTOM_URL: '16',
  CUSTOM_CODE: '17',
  CUSTOM_RATING: '18'
};

// Sử dụng trong template
const customTemplate = {
  params: [
    { name: 'verification_code', type: ZNS_PARAM_TYPES.CUSTOM_CODE },
    { name: 'rating_url', type: ZNS_PARAM_TYPES.CUSTOM_URL }
  ]
};
```

## 📚 API Reference

### Core Functions

#### `handleZnsEvent(supabase, eventData)`

Xử lý ZNS event và gửi tin nhắn tự động.

**Parameters:**
- `supabase`: Supabase client instance
- `eventData`: Object chứa thông tin event

```typescript
interface ZnsEventData {
  module: string;           // 'orders', 'education', etc.
  event: string;            // 'order_confirmation', etc.
  data: Record<string, any>; // Event data
  metadata: {
    accountId: string;
    timestamp?: string;
    [key: string]: any;
  };
}
```

#### `sendZnsMessage(options)`

Gửi ZNS message trực tiếp.

**Parameters:**

```typescript
interface SendZnsOptions {
  supabase: SupabaseClient;
  templateId: string;
  recipient: string;
  parameters: Record<string, string>;
  oaConfigId: string;
  trackingId?: string;
}
```

#### `categorizeTemplateParams(params)`

Phân loại template parameters thành auto và manual.

**Parameters:**
- `params`: Array of template parameters

**Returns:**
```typescript
{
  autoParams: Array<{ param: any, config: any }>;
  manualParams: Array<any>;
}
```

#### `generateAutoMapping(params)`

Tạo auto parameter mapping.

**Parameters:**
- `params`: Array of template parameters

**Returns:**
- `Record<string, string>`: Parameter mapping object

### Utility Functions

#### `getZnsKeyConfig(key)`

Lấy config cho một parameter key.

```typescript
const config = getZnsKeyConfig('customer_name');
// Returns: { path: 'customer.name', paramType: '1', ... }
```

#### `validateZnsTemplate(template)`

Validate ZNS template structure.

```typescript
const validation = validateZnsTemplate(templateData);
// Returns: { isValid: boolean, errors: string[] }
```

#### `formatPhoneNumber(phone)`

Chuẩn hóa số điện thoại cho Zalo.

```typescript
const formatted = formatPhoneNumber('**********');
// Returns: '***********'
```

## 🧪 Testing

### Unit Tests

```typescript
import { categorizeTemplateParams, generateAutoMapping } from '@kit/zns';

describe('ZNS Parameter Mapping', () => {
  test('should categorize parameters correctly', () => {
    const params = [
      { name: 'customer_name', type: '1' },
      { name: 'custom_field', type: '13' }
    ];

    const { autoParams, manualParams } = categorizeTemplateParams(params);

    expect(autoParams).toHaveLength(1);
    expect(manualParams).toHaveLength(1);
    expect(autoParams[0].param.name).toBe('customer_name');
  });
});
```

### Integration Tests

```typescript
import { handleZnsEvent } from '@kit/zns';
import { createClient } from '@kit/supabase/server';

describe('ZNS Event Handler', () => {
  test('should process order confirmation event', async () => {
    const supabase = createClient();

    const result = await handleZnsEvent(supabase, {
      module: 'orders',
      event: 'order_confirmation',
      data: {
        customer: { name: 'Test User', phone: '**********' },
        order: { id: 'TEST-001', total: '100000' }
      },
      metadata: { accountId: 'test-account-id' }
    });

    expect(result.success).toBe(true);
  });
});
```

## 🐛 Troubleshooting

### Common Issues

1. **"No mapping found for event"**
   - Kiểm tra mapping đã được tạo và enabled
   - Verify module và event_type match chính xác

2. **"Template parameters missing"**
   - Kiểm tra parameter_mapping trong mapping record
   - Verify template có đúng parameters

3. **"Invalid phone number"**
   - Sử dụng `formatPhoneNumber()` để chuẩn hóa
   - Đảm bảo format: 84xxxxxxxxx

4. **"Access token expired"**
   - Refresh Zalo access token
   - Kiểm tra OA configuration

### Debug Mode

```typescript
// Enable debug logs
process.env.ZNS_DEBUG = 'true';

// Logs sẽ hiển thị:
// 🔍 ZNS Event received: {...}
// 📋 Found mapping: {...}
// 🔗 Parameter mapping: {...}
// 📤 Sending ZNS: {...}
// ✅ ZNS sent successfully: {...}
```

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/new-event-type`
3. Commit changes: `git commit -am 'Add new event type'`
4. Push to branch: `git push origin feature/new-event-type`
5. Submit pull request

## 📞 Support

- 📧 Email: <EMAIL>
- 📖 Documentation: [ZNS Docs](https://docs.example.com/zns)
- 🐛 Issues: [GitHub Issues](https://github.com/example/zns/issues)