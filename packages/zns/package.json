{"name": "@kit/zns", "private": true, "version": "0.1.0", "exports": {".": "./src/index.ts", "./bill-zns": "./src/lib/bill-zns.ts", "./get-templates": "./src/lib/get-templates.ts", "./request-template": "./src/lib/request-template.ts", "./send-zns": "./src/lib/send-zns.ts", "./send-zns-message": "./src/lib/send-zns-message.ts", "./get-zns-info": "./src/lib/get-zns-info.ts", "./create-template": "./src/lib/create-template.ts", "./edit-template": "./src/lib/edit-template.ts", "./upload-image": "./src/lib/upload-image.ts", "./webhook-handlers": "./src/lib/webhook-handlers.ts", "./zns-queue": "./src/lib/zns-queue.ts", "./types": "./src/types/index.ts", "./utils": "./src/lib/utils.ts"}, "typesVersions": {"*": {"*": ["src/*"]}}, "license": "MIT", "scripts": {"clean": "rm -rf .turbo node_modules", "lint": "eslint .", "format": "prettier --check \"**/*.{mjs,ts,md,json}\"", "typecheck": "tsc --noEmit"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/supabase": "workspace:*", "@kit/team-accounts": "workspace:*", "@kit/tsconfig": "workspace:*", "@supabase/supabase-js": "2.49.4", "axios": "^1.6.0", "bullmq": "^5.0.0", "form-data": "^4.0.2", "redis": "^4.6.0", "zod": "^3.24.2"}, "prettier": "@kit/prettier-config"}