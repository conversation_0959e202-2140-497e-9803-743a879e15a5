'use client';

import { useEffect, useRef, useState } from 'react';

import { createClient } from '@supabase/supabase-js';

import { Database } from '@kit/supabase/database';

import {
  initZnsEventHandler,
  shutdownZnsEventHandler,
} from '../lib/event-handler';

// Lưu trữ các handler đã khởi tạo theo ID
const initializedHandlers = new Map<string, string>();

/**
 * Hook để quản lý ZNS Event Handler
 *
 * @param supabase Supabase client
 * @param accountId ID của account
 * @param options Tùy chọn bổ sung
 * @returns Trạng thái của ZNS Event Handler
 *
 * Sử dụng:
 * ```tsx
 * const supabase = useSupabase();
 * const { account } = useTeamAccountWorkspace();
 * const { isInitialized } = useZnsEventHandler(supabase, account?.id);
 * ```
 */
export function useZnsEventHandler(
  supabase: ReturnType<typeof createClient<Database>>,
  accountId?: string,
  options: {
    autoInitialize?: boolean;
    dependencyArray?: any[];
  } = {},
) {
  const { autoInitialize = true, dependencyArray = [] } = options;

  const [isInitialized, setIsInitialized] = useState(false);
  const handlerIdRef = useRef<string | null>(null);

  // Khởi tạo handler
  const initialize = () => {
    if (!accountId || isInitialized || handlerIdRef.current) return false;

    // Kiểm tra xem đã có handler cho account này chưa
    if (initializedHandlers.has(accountId)) {
      handlerIdRef.current = initializedHandlers.get(accountId) || null;
      setIsInitialized(true);
      return true;
    }

    // Khởi tạo handler mới
    try {
      const handlerId = initZnsEventHandler(
        supabase,
        `zns_handler_${accountId}`,
      );
      handlerIdRef.current = handlerId;
      initializedHandlers.set(accountId, handlerId);
      setIsInitialized(true);
      return true;
    } catch (error) {
      console.error('Error initializing ZNS Event Handler:', error);
      return false;
    }
  };

  // Hủy handler
  const shutdown = () => {
    if (!handlerIdRef.current) return false;

    try {
      shutdownZnsEventHandler(handlerIdRef.current);
      initializedHandlers.delete(accountId || '');
      handlerIdRef.current = null;
      setIsInitialized(false);
      return true;
    } catch (error) {
      console.error('Error shutting down ZNS Event Handler:', error);
      return false;
    }
  };

  // Tự động khởi tạo nếu cần
  useEffect(() => {
    if (autoInitialize && accountId && !isInitialized) {
      initialize();
    }

    // Cleanup khi component unmount
    return () => {
      if (handlerIdRef.current) {
        // Không hủy handler khi unmount để tránh mất kết nối
        // Chỉ đánh dấu là component không còn sử dụng nữa
        setIsInitialized(false);
      }
    };
  }, [accountId, autoInitialize, isInitialized, ...dependencyArray]);

  return {
    isInitialized,
    initialize,
    shutdown,
    handlerId: handlerIdRef.current,
  };
}
