export type ZnsEventType = 'order_created' | 'order_updated' | 'promotion' | 'theme_activated';
export type OaType = 'shared' | 'private';
export type ZnsStatus = 'pending' | 'success' | 'failed';

export interface ZnsTemplateParams {
  [key: string]: string;
}

export interface ZnsNotificationParams {
  customer_phone: string;
  [key: string]: any;
}

export interface OaConfiguration {
  id: string;
  access_token: string;
  refresh_token: string;
  token_expires_at: string;
  app_id: string;
  secret_key: string;
  oa_type: OaType;
}
