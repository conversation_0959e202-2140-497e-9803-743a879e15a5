# ZNS API Library

Thư viện này cung cấp các hàm để tương tác với Zalo Notification Service (ZNS) API.

## Các API được hỗ trợ

1. **L<PERSON>y danh sách template ZNS**
   - `getZnsTemplates`: L<PERSON><PERSON> danh sách template ZNS của một OA
   - `getZnsTemplateById`: L<PERSON><PERSON> thông tin chi tiết của một template ZNS theo ID

2. **Lấy thông tin người dùng Zalo**
   - `getZaloUserDetail`: Lấy thông tin người dùng Zalo từ access token

3. **<PERSON><PERSON><PERSON> thực OAuth**
   - `getAccessToken`: Lấy access token từ authorization code
   - `refreshAccessToken`: Làm mới access token từ refresh token

4. **Tạo template ZNS**
   - `createZnsTemplate`: Tạo template ZNS mới
   - `createCustomZnsTemplate`: Tạo template ZNS tùy chỉnh (helper function)

5. **Chỉnh sửa template ZNS**
   - `editZnsTemplate`: Chỉnh sửa template ZNS đã tồn tại
   - `editCustomZnsTemplate`: Chỉnh sửa template ZNS tùy chỉnh (helper function)

## Cách sử dụng API tạo template ZNS

### Tạo template ZNS cơ bản

```typescript
import { createZnsTemplate, ZnsTemplateType, ZnsTemplateTag } from '@kit/zns';

// Tạo template ZNS
const templateData = {
  template_name: 'Mẫu chăm sóc khách hàng',
  template_type: ZnsTemplateType.CUSTOM, // hoặc 1
  tag: ZnsTemplateTag.CUSTOMER_CARE, // hoặc 2
  layout: {
    header: {
      components: [
        {
          LOGO: {
            light: {
              type: 'IMAGE',
              media_id: 'your_media_id'
            },
            dark: {
              type: 'IMAGE',
              media_id: 'your_media_id'
            }
          }
        }
      ]
    },
    body: {
      components: [
        {
          TITLE: {
            value: 'Xác nhận đơn hàng'
          }
        },
        {
          PARAGRAPH: {
            value: 'Cảm ơn <name> đã mua hàng tại cửa hàng. Đơn hàng của bạn đã được xác nhận.'
          }
        }
      ]
    },
    footer: {
      components: [
        {
          BUTTONS: {
            items: [
              {
                content: 'https://example.com/order',
                type: 1,
                title: 'Xem đơn hàng'
              }
            ]
          }
        }
      ]
    }
  },
  params: [
    {
      type: '1', // hoặc ZnsParamType.CUSTOMER_NAME
      name: 'name',
      sample_value: 'Nguyễn Văn A'
    }
  ],
  note: 'Ghi chú kiểm duyệt',
  tracking_id: 'your_tracking_id'
};

// Gọi API tạo template
const result = await createZnsTemplate(supabase, oaConfigId, templateData);
console.log('Template created:', result);
```

### Sử dụng helper function để tạo template tùy chỉnh

```typescript
import { createCustomZnsTemplate, ZnsTemplateTag } from '@kit/zns';

// Tạo template ZNS tùy chỉnh
const result = await createCustomZnsTemplate(
  supabase,
  oaConfigId,
  'Mẫu chăm sóc khách hàng',
  ZnsTemplateTag.CUSTOMER_CARE,
  {
    body: {
      components: [
        {
          TITLE: {
            value: 'Xác nhận đơn hàng'
          }
        },
        {
          PARAGRAPH: {
            value: 'Cảm ơn <name> đã mua hàng tại cửa hàng.'
          }
        }
      ]
    }
  },
  [
    {
      type: '1',
      name: 'name',
      sample_value: 'Nguyễn Văn A'
    }
  ],
  'Ghi chú kiểm duyệt'
);

console.log('Template created:', result);
```

## Cách sử dụng API chỉnh sửa template ZNS

### Lấy thông tin template ZNS cần chỉnh sửa

```typescript
import { getZnsTemplateById } from '@kit/zns';

// Lấy thông tin template ZNS theo ID
const templateInfo = await getZnsTemplateById(supabase, oaConfigId, templateId);
console.log('Template info:', templateInfo);
```

### Chỉnh sửa template ZNS cơ bản

```typescript
import { editZnsTemplate, ZnsTemplateType, ZnsTemplateTag } from '@kit/zns';

// Chỉnh sửa template ZNS
const templateData = {
  template_id: 'your_template_id', // ID của template cần chỉnh sửa
  template_name: 'Mẫu chăm sóc khách hàng (updated)',
  template_type: ZnsTemplateType.CUSTOM, // hoặc 1
  tag: ZnsTemplateTag.CUSTOMER_CARE, // hoặc 2
  layout: {
    header: {
      components: [
        {
          LOGO: {
            light: {
              type: 'IMAGE',
              media_id: 'your_media_id'
            },
            dark: {
              type: 'IMAGE',
              media_id: 'your_media_id'
            }
          }
        }
      ]
    },
    body: {
      components: [
        {
          TITLE: {
            value: 'Xác nhận đơn hàng (updated)'
          }
        },
        {
          PARAGRAPH: {
            value: 'Cảm ơn <name> đã mua hàng tại cửa hàng. Đơn hàng của bạn đã được xác nhận.'
          }
        }
      ]
    },
    footer: {
      components: [
        {
          BUTTONS: {
            items: [
              {
                content: 'https://example.com/order',
                type: 1,
                title: 'Xem đơn hàng'
              }
            ]
          }
        }
      ]
    }
  },
  params: [
    {
      type: '1', // hoặc ZnsParamType.CUSTOMER_NAME
      name: 'name',
      sample_value: 'Nguyễn Văn A'
    }
  ],
  note: 'Ghi chú kiểm duyệt',
  tracking_id: 'your_tracking_id'
};

// Gọi API chỉnh sửa template
const result = await editZnsTemplate(supabase, oaConfigId, templateData);
console.log('Template updated:', result);
```

### Sử dụng helper function để chỉnh sửa template tùy chỉnh

```typescript
import { editCustomZnsTemplate, ZnsTemplateTag } from '@kit/zns';

// Chỉnh sửa template ZNS tùy chỉnh
const result = await editCustomZnsTemplate(
  supabase,
  oaConfigId,
  'your_template_id',
  'Mẫu chăm sóc khách hàng (updated)',
  ZnsTemplateTag.CUSTOMER_CARE,
  {
    body: {
      components: [
        {
          TITLE: {
            value: 'Xác nhận đơn hàng (updated)'
          }
        },
        {
          PARAGRAPH: {
            value: 'Cảm ơn <name> đã mua hàng tại cửa hàng.'
          }
        }
      ]
    }
  },
  [
    {
      type: '1',
      name: 'name',
      sample_value: 'Nguyễn Văn A'
    }
  ],
  'Ghi chú kiểm duyệt'
);

console.log('Template updated:', result);
```

## Các loại template ZNS

ZNS hỗ trợ các loại template sau:

1. **ZNS tùy chỉnh** (`ZnsTemplateType.CUSTOM` hoặc `1`)
2. **ZNS xác thực** (`ZnsTemplateType.AUTHENTICATION` hoặc `2`)
3. **ZNS yêu cầu thanh toán** (`ZnsTemplateType.PAYMENT_REQUEST` hoặc `3`)
4. **ZNS voucher** (`ZnsTemplateType.VOUCHER` hoặc `4`)
5. **ZNS đánh giá dịch vụ** (`ZnsTemplateType.SERVICE_RATING` hoặc `5`)

## Các tag template ZNS

ZNS hỗ trợ các tag sau:

1. **Transaction** (`ZnsTemplateTag.TRANSACTION` hoặc `1`)
2. **Customer Care** (`ZnsTemplateTag.CUSTOMER_CARE` hoặc `2`)
3. **Promotion** (`ZnsTemplateTag.PROMOTION` hoặc `3`)

## Các loại param trong template ZNS

ZNS hỗ trợ các loại param sau:

1. **Tên khách hàng** (`ZnsParamType.CUSTOMER_NAME` hoặc `1`)
2. **Số điện thoại** (`ZnsParamType.PHONE_NUMBER` hoặc `2`)
3. **Địa chỉ** (`ZnsParamType.ADDRESS` hoặc `3`)
4. **Mã số** (`ZnsParamType.CODE` hoặc `4`)
5. **Nhãn tùy chỉnh** (`ZnsParamType.CUSTOM_LABEL` hoặc `5`)
6. **Trạng thái giao dịch** (`ZnsParamType.TRANSACTION_STATUS` hoặc `6`)
7. **Thông tin liên hệ** (`ZnsParamType.CONTACT_INFO` hoặc `7`)
8. **Giới tính / Danh xưng** (`ZnsParamType.GENDER` hoặc `8`)
9. **Tên sản phẩm / Thương hiệu** (`ZnsParamType.PRODUCT_NAME` hoặc `9`)
10. **Số lượng / Số tiền** (`ZnsParamType.QUANTITY_AMOUNT` hoặc `10`)
11. **Thời gian** (`ZnsParamType.TIME` hoặc `11`)
12. **OTP** (`ZnsParamType.OTP` hoặc `12`)
13. **URL** (`ZnsParamType.URL` hoặc `13`)
14. **Tiền tệ (VNĐ)** (`ZnsParamType.CURRENCY` hoặc `14`)
15. **Bank transfer note** (`ZnsParamType.BANK_TRANSFER_NOTE` hoặc `15`)

## Lưu ý

- Template ZNS cần được kiểm duyệt bởi đội ngũ Zalo trước khi có thể sử dụng.
- Mỗi loại template có các quy định khác nhau về các trường thông tin.
- Daily quota của API tạo template: 100 requests/ngày.
