// Re-export các hàm từ get-zns-info.ts để đảm bảo tính tương thích ngược
import { getZnsMessageStatus as getZnsMessageStatusOriginal, getZnsQuality as getZnsQualityOriginal, getZnsQuota as getZnsQuotaOriginal, getZnsRating as getZnsRatingOriginal, getZnsTemplateDetail as getZnsTemplateDetailOriginal, getZnsTemplateList as getZnsTemplateListOriginal, getZnsTemplateSampleData as getZnsTemplateSampleDataOriginal, getZnsTemplateTag as getZnsTemplateTagOriginal } from './get-zns-info';





/**
 * Enum for ZNS template status
 */
export enum ZnsTemplateStatus {
  PENDING_REVIEW = 1,
  APPROVED = 2,
  REJECTED = 3,
  DELETED = 4,
}

/**
 * Interface for ZNS template list response
 */
export interface ZnsTemplateListResponse {
  data: Array<{
    templateId: string;
    templateName: string;
    templateType: number;
    status: string;
    tag: number;
    appId: string;
    oaId: string;
    price: string;
    timeout: number;
    previewUrl: string;
    createdTime: number;
    updatedTime: number;
  }>;
  metadata: {
    total: number;
  };
}

/**
 * Interface for ZNS message status response
 */
export interface ZnsMessageStatusResponse {
  messageId: string;
  status: string;
  statusDetail: string;
  statusCode: number;
  statusTime: number;
  deliveryTime: number;
  readTime: number;
  phone: string;
  templateId: string;
  templateName: string;
  templateType: number;
  tag: number;
  price: string;
  appId: string;
  oaId: string;
}

/**
 * Interface for ZNS quota response
 */
export interface ZnsQuotaResponse {
  quota: number;
  usedQuota: number;
  remainingQuota: number;
  quotaExpiredTime: number;
}

/**
 * Interface for ZNS template detail response
 */
export interface ZnsTemplateDetailResponse {
  templateId: string;
  templateName: string;
  templateType: number;
  status: string;
  tag: number;
  appId: string;
  oaId: string;
  price: string;
  timeout: number;
  previewUrl: string;
  createdTime: number;
  updatedTime: number;
  layout: any; // Using any for flexibility, can be typed more specifically if needed
  params: Array<{
    name: string;
    type: string;
    sample_value: string;
  }>;
}

/**
 * Interface for ZNS template sample data response
 */
export interface ZnsTemplateSampleDataResponse {
  html: string;
  data: any; // Using any for flexibility, can be typed more specifically if needed
}

/**
 * Interface for ZNS rating response
 */
export interface ZnsRatingResponse {
  ratings: Array<{
    messageId: string;
    phone: string;
    templateId: string;
    templateName: string;
    rating: number;
    comment: string;
    ratingTime: number;
  }>;
  total: number;
}

/**
 * Interface for ZNS quality response
 */
export interface ZnsQualityResponse {
  quality: number;
  qualityLevel: string;
  qualityDescription: string;
  qualityTime: number;
}

// Các hàm đã được chuyển sang get-zns-info.ts
// File này được giữ lại để đảm bảo tính tương thích ngược

// Re-export các hàm để đảm bảo tính tương thích ngược
export const getZnsMessageStatus = getZnsMessageStatusOriginal;
export const getZnsQuota = getZnsQuotaOriginal;
export const getZnsTemplateTag = getZnsTemplateTagOriginal;
export const getZnsTemplateList = getZnsTemplateListOriginal;
export const getZnsTemplateDetail = getZnsTemplateDetailOriginal;
export const getZnsTemplateSampleData = getZnsTemplateSampleDataOriginal;
export const getZnsRating = getZnsRatingOriginal;
export const getZnsQuality = getZnsQualityOriginal;

// Các hàm đã được chuyển sang get-zns-info.ts
// File này được giữ lại để đảm bảo tính tương thích ngược
