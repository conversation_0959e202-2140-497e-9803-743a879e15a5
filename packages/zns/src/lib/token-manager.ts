import { createClient } from '@supabase/supabase-js';
import axios from 'axios';
import { Database } from '@kit/supabase/database';
import { refreshAccessToken } from './oauth-utils';

// Cache lưu trữ token theo oaConfigId
interface TokenCache {
  [oaConfigId: string]: {
    accessToken: string;
    expiresAt: Date;
    refreshToken: string;
    lastRefreshed: Date;
    refreshAttempts: number;
  };
}

// Cache toàn cục
const tokenCache: TokenCache = {};

// Thời gian tối đa giữa các lần refresh token (5 phút)
const MIN_REFRESH_INTERVAL = 5 * 60 * 1000;

// Số lần thử refresh token tối đa
const MAX_REFRESH_ATTEMPTS = 3;

// Thời gian reset số lần thử refresh token (30 phút)
const REFRESH_ATTEMPTS_RESET_INTERVAL = 30 * 60 * 1000;

/**
 * Lấy access token từ cache hoặc database
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param forceRefresh Có bắt buộc refresh token không
 * @returns Access token
 */
export async function getAccessToken(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  forceRefresh = false
): Promise<string> {
  // Kiểm tra cache
  const cachedToken = tokenCache[oaConfigId];
  const now = new Date();

  // Nếu có token trong cache và token còn hạn và không bắt buộc refresh
  if (
    cachedToken &&
    cachedToken.expiresAt > now &&
    !forceRefresh
  ) {
    console.log(`Using cached token for OA config ${oaConfigId}`);
    return cachedToken.accessToken;
  }

  // Nếu có token trong cache nhưng đã hết hạn hoặc bắt buộc refresh
  if (cachedToken) {
    // Kiểm tra xem đã refresh quá nhiều lần chưa
    if (cachedToken.refreshAttempts >= MAX_REFRESH_ATTEMPTS) {
      // Kiểm tra xem đã đủ thời gian để reset số lần thử chưa
      if (now.getTime() - cachedToken.lastRefreshed.getTime() > REFRESH_ATTEMPTS_RESET_INTERVAL) {
        // Reset số lần thử
        cachedToken.refreshAttempts = 0;
      } else {
        // Nếu chưa đủ thời gian và đã thử quá nhiều lần, báo lỗi
        throw new Error('Too many refresh attempts. Please reconnect your Zalo account.');
      }
    }

    // Kiểm tra xem đã đủ thời gian giữa các lần refresh chưa
    if (now.getTime() - cachedToken.lastRefreshed.getTime() < MIN_REFRESH_INTERVAL) {
      console.log(`Last refresh was too recent for OA config ${oaConfigId}. Using cached token.`);
      return cachedToken.accessToken;
    }
  }

  // Lấy thông tin OA configuration từ database
  console.log(`Fetching OA config ${oaConfigId} from database`);
  const { data: oaConfig, error } = await supabase
    .from('oa_configurations')
    .select('*')
    .eq('id', oaConfigId)
    .single();

  if (error || !oaConfig) {
    console.error(`Failed to fetch OA config: ${error ? JSON.stringify(error, null, 2) : 'No config found'}`);
    throw new Error(`Failed to fetch OA config: ${error?.message || 'No config found'}`);
  }

  // Kiểm tra access token
  if (!oaConfig.access_token) {
    console.error('Access token is missing in OA config');
    throw new Error('Access token is missing in OA config');
  }

  // Kiểm tra xem token đã hết hạn chưa
  const tokenExpiresAt = new Date(oaConfig.token_expires_at);
  if (tokenExpiresAt <= now || forceRefresh) {
    console.log(`Token expired or force refresh for OA config ${oaConfigId}. Refreshing...`);
    
    try {
      // Refresh token
      const { accessToken, refreshToken, expiresIn } = await refreshAccessToken(
        oaConfig.app_id,
        oaConfig.secret_key,
        oaConfig.refresh_token
      );

      // Tính thời gian hết hạn mới
      const newExpiresAt = new Date(Date.now() + expiresIn * 1000);

      // Cập nhật token trong database
      await updateTokenInDatabase(supabase, oaConfig.id, accessToken, refreshToken, newExpiresAt);

      // Cập nhật cache
      tokenCache[oaConfigId] = {
        accessToken,
        expiresAt: newExpiresAt,
        refreshToken,
        lastRefreshed: now,
        refreshAttempts: 0
      };

      console.log(`Token refreshed successfully for OA config ${oaConfigId}`);
      return accessToken;
    } catch (error: any) {
      // Tăng số lần thử refresh token
      if (cachedToken) {
        cachedToken.refreshAttempts += 1;
        cachedToken.lastRefreshed = now;
      } else {
        // Tạo cache mới nếu chưa có
        tokenCache[oaConfigId] = {
          accessToken: oaConfig.access_token,
          expiresAt: tokenExpiresAt,
          refreshToken: oaConfig.refresh_token,
          lastRefreshed: now,
          refreshAttempts: 1
        };
      }

      // Kiểm tra mã lỗi từ Zalo API
      if (error.response && error.response.data) {
        const errorCode = error.response.data.error;
        const errorMessage = error.response.data.message;
        
        // Xử lý các mã lỗi cụ thể
        if (errorCode === -124) {
          console.error('Access token invalid. Please reconnect your Zalo account.');
          throw new Error('Access token invalid. Please reconnect your Zalo account.');
        } else if (errorCode === -104) {
          console.error('App secret key invalid. Please check your app configuration.');
          throw new Error('App secret key invalid. Please check your app configuration.');
        } else {
          console.error(`Failed to refresh token: ${errorCode} - ${errorMessage}`);
          throw new Error(`Failed to refresh token: ${errorMessage}`);
        }
      }

      console.error('Failed to refresh token:', error instanceof Error ? error.message : JSON.stringify(error, null, 2));
      throw new Error('Failed to refresh access token. Please reconnect your Zalo account.');
    }
  }

  // Cập nhật cache
  tokenCache[oaConfigId] = {
    accessToken: oaConfig.access_token,
    expiresAt: tokenExpiresAt,
    refreshToken: oaConfig.refresh_token,
    lastRefreshed: now,
    refreshAttempts: 0
  };

  console.log(`Token cached for OA config ${oaConfigId}`);
  return oaConfig.access_token;
}

/**
 * Cập nhật token trong database
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param accessToken Access token mới
 * @param refreshToken Refresh token mới
 * @param expiresAt Thời gian hết hạn mới
 */
async function updateTokenInDatabase(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  accessToken: string,
  refreshToken: string,
  expiresAt: Date
): Promise<void> {
  try {
    // Lấy thông tin OA configuration hiện tại
    const { data: oaConfig, error: fetchError } = await supabase
      .from('oa_configurations')
      .select('oa_metadata')
      .eq('id', oaConfigId)
      .single();

    if (fetchError) {
      console.error(`Failed to fetch OA config for update: ${fetchError.message}`);
      throw fetchError;
    }

    // Cập nhật token và metadata
    const { error: updateError } = await supabase
      .from('oa_configurations')
      .update({
        access_token: accessToken,
        refresh_token: refreshToken,
        token_expires_at: expiresAt.toISOString(),
        oa_metadata: {
          ...oaConfig?.oa_metadata,
          last_refreshed: new Date().toISOString(),
          token_info: {
            ...(oaConfig?.oa_metadata?.token_info || {}),
            expires_at: expiresAt.toISOString(),
            refreshed_at: new Date().toISOString(),
          },
        },
      })
      .eq('id', oaConfigId);

    if (updateError) {
      console.error(`Failed to update token in database: ${updateError.message}`);
      throw updateError;
    }

    // Cập nhật trạng thái integration
    await supabase
      .from('integrations')
      .update({
        status: 'connected',
        metadata: {
          oa_config_id: oaConfigId,
          last_connected: new Date().toISOString(),
        },
      })
      .eq('metadata->oa_config_id', oaConfigId);

    console.log(`Token updated in database for OA config ${oaConfigId}`);
  } catch (error) {
    console.error('Error updating token in database:', error);
    throw error;
  }
}

/**
 * Xóa token khỏi cache
 * @param oaConfigId ID của OA configuration
 */
export function clearTokenCache(oaConfigId: string): void {
  delete tokenCache[oaConfigId];
  console.log(`Token cache cleared for OA config ${oaConfigId}`);
}

/**
 * Đánh dấu token là không hợp lệ trong database
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param errorMessage Thông báo lỗi
 */
export async function markTokenAsInvalid(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  errorMessage: string
): Promise<void> {
  try {
    // Lấy thông tin OA configuration hiện tại
    const { data: oaConfig, error: fetchError } = await supabase
      .from('oa_configurations')
      .select('oa_metadata')
      .eq('id', oaConfigId)
      .single();

    if (fetchError) {
      console.error(`Failed to fetch OA config for marking as invalid: ${fetchError.message}`);
      return;
    }

    // Cập nhật token và metadata
    await supabase
      .from('oa_configurations')
      .update({
        token_expires_at: new Date(0).toISOString(), // Đặt ngày hết hạn về quá khứ
        oa_metadata: {
          ...oaConfig?.oa_metadata,
          token_error: errorMessage,
          token_error_time: new Date().toISOString(),
        },
      })
      .eq('id', oaConfigId);

    // Cập nhật trạng thái integration
    await supabase
      .from('integrations')
      .update({
        status: 'error',
        metadata: {
          oa_config_id: oaConfigId,
          last_error: new Date().toISOString(),
          error_message: errorMessage,
        },
      })
      .eq('metadata->oa_config_id', oaConfigId);

    // Xóa cache
    clearTokenCache(oaConfigId);

    console.log(`Token marked as invalid in database for OA config ${oaConfigId}`);
  } catch (error) {
    console.error('Error marking token as invalid:', error);
  }
}
