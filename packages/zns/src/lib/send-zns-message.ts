import { createClient } from '@supabase/supabase-js';

import axios from 'axios';
import crypto from 'crypto';

import { Database } from '@kit/supabase/database';

import { getValidZnsToken } from './utils';

/**
 * Enum cho chế độ gửi ZNS
 */
export enum ZnsSendingMode {
  NORMAL = '1',
  EXCEED_QUOTA = '3',
}

/**
 * Enum cho chế độ phát triển ZNS
 */
export enum ZnsDevelopmentMode {
  DEVELOPMENT = 'development',
}

/**
 * Enum cho loại Journey Token
 */
export enum ZnsJourneyTokenType {
  LOGISTICS_7 = 'token_logistics_7',
  LOGISTICS_30 = 'token_logistics_30',
  COACH_BUS_7 = 'token_coach_bus_7',
  COACH_BUS_30 = 'token_coach_bus_30',
}

/**
 * Interface cho response khi gửi ZNS
 */
export interface SendZnsResponse {
  msg_id: string;
  sent_time: string;
  sending_mode?: string;
  quota?: {
    dailyQuota: string;
    remainingQuota: string;
  };
}

/**
 * Interface cho response khi lấy Journey Token
 */
export interface GetZnsJourneyTokenResponse {
  token: string;
  journey_id: string;
}

/**
 * Interface cho response khi kiểm tra Journey Token
 */
export interface CheckZnsJourneyTokenResponse {
  appId: string;
  oaId: string;
  tokenType: string;
  createdAt: string;
  expiredAt: string;
}

/**
 * Gửi ZNS thông thường
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param phone Số điện thoại người nhận (định dạng chuẩn hóa, ví dụ: ***********)
 * @param templateId ID của template ZNS
 * @param templateData Dữ liệu template
 * @param trackingId ID theo dõi (tùy chọn)
 * @param sendingMode Chế độ gửi (tùy chọn)
 * @returns Thông tin kết quả gửi ZNS
 */
export async function sendZnsMessage(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  phone: string,
  templateId: string,
  templateData: Record<string, any>,
  trackingId?: string,
  sendingMode?: ZnsSendingMode,
): Promise<SendZnsResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(
    supabase,
    oaConfigId,
  );

  // Chuẩn hóa số điện thoại
  const normalizedPhone = normalizePhone(phone);

  // Tạo tracking ID nếu không được cung cấp
  const finalTrackingId = trackingId || `zns_${Date.now()}`;

  try {
    // Tạo request data
    const requestData: Record<string, any> = {
      phone: normalizedPhone,
      template_id: templateId,
      template_data: templateData,
      tracking_id: finalTrackingId,
    };

    // Thêm sending_mode nếu được cung cấp
    if (sendingMode) {
      requestData.sending_mode = sendingMode;
    }

    // Gọi API gửi ZNS
    const response = await axios.post(
      'https://business.openapi.zalo.me/message/template',
      requestData,
      {
        headers: {
          'Content-Type': 'application/json',
          access_token: accessToken,
        },
      },
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to send ZNS: ${response.data.message}`);
    }

    // Trả về thông tin kết quả gửi ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(
        `Failed to send ZNS: ${error.response.data.message || error.message}`,
      );
    }
    throw error;
  }
}

/**
 * Gửi ZNS sử dụng hash phone
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param phone Số điện thoại người nhận (định dạng chuẩn hóa, ví dụ: ***********)
 * @param templateId ID của template ZNS
 * @param templateData Dữ liệu template
 * @param trackingId ID theo dõi (tùy chọn)
 * @returns Thông tin kết quả gửi ZNS
 */
export async function sendZnsMessageWithHashPhone(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  phone: string,
  templateId: string,
  templateData: Record<string, any>,
  trackingId?: string,
): Promise<SendZnsResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(
    supabase,
    oaConfigId,
  );

  // Chuẩn hóa số điện thoại
  const normalizedPhone = normalizePhone(phone);

  // Hash số điện thoại
  const hashedPhone = hashPhone(normalizedPhone);

  // Tạo tracking ID nếu không được cung cấp
  const finalTrackingId = trackingId || `zns_hash_${Date.now()}`;

  try {
    // Gọi API gửi ZNS với hash phone
    const response = await axios.post(
      'https://business.openapi.zalo.me/message/template/hashphone',
      {
        hash_phone: hashedPhone,
        template_id: templateId,
        template_data: templateData,
        tracking_id: finalTrackingId,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          access_token: accessToken,
        },
      },
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(
        `Failed to send ZNS with hash phone: ${response.data.message}`,
      );
    }

    // Trả về thông tin kết quả gửi ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(
        `Failed to send ZNS with hash phone: ${error.response.data.message || error.message}`,
      );
    }
    throw error;
  }
}

/**
 * Gửi ZNS ở chế độ phát triển
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param phone Số điện thoại người nhận (định dạng chuẩn hóa, ví dụ: ***********)
 * @param templateId ID của template ZNS
 * @param templateData Dữ liệu template
 * @param trackingId ID theo dõi (tùy chọn)
 * @returns Thông tin kết quả gửi ZNS
 */
export async function sendZnsMessageInDevelopmentMode(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  phone: string,
  templateId: string,
  templateData: Record<string, any>,
  trackingId?: string,
): Promise<SendZnsResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(
    supabase,
    oaConfigId,
  );

  // Chuẩn hóa số điện thoại
  const normalizedPhone = normalizePhone(phone);

  // Tạo tracking ID nếu không được cung cấp
  const finalTrackingId = trackingId || `zns_dev_${Date.now()}`;

  try {
    // Gọi API gửi ZNS ở chế độ phát triển
    const response = await axios.post(
      'https://business.openapi.zalo.me/message/template',
      {
        mode: ZnsDevelopmentMode.DEVELOPMENT,
        phone: normalizedPhone,
        template_id: templateId,
        template_data: templateData,
        tracking_id: finalTrackingId,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          access_token: accessToken,
        },
      },
    );
    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(
        `Failed to send ZNS in development mode: ${response.data.message}`,
      );
    }

    // Trả về thông tin kết quả gửi ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(
        `Failed to send ZNS in development mode: ${error.response.data.message || error.message}`,
      );
    }
    throw error;
  }
}

/**
 * Lấy Journey Token
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param phone Số điện thoại người nhận (định dạng chuẩn hóa, ví dụ: ***********)
 * @param tokenType Loại Journey Token
 * @returns Thông tin Journey Token
 */
export async function getZnsJourneyToken(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  phone: string,
  tokenType: ZnsJourneyTokenType = ZnsJourneyTokenType.LOGISTICS_7,
): Promise<GetZnsJourneyTokenResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(
    supabase,
    oaConfigId,
  );

  // Chuẩn hóa số điện thoại
  const normalizedPhone = normalizePhone(phone);

  try {
    // Gọi API lấy Journey Token
    const response = await axios.post(
      'https://business.openapi.zalo.me/journey/get-token',
      {
        phone: normalizedPhone,
        token_type: tokenType,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          access_token: accessToken,
        },
      },
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(
        `Failed to get ZNS Journey Token: ${response.data.message}`,
      );
    }

    // Trả về thông tin Journey Token
    return {
      token: response.data.token,
      journey_id: response.data.journey_id,
    };
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(
        `Failed to get ZNS Journey Token: ${error.response.data.message || error.message}`,
      );
    }
    throw error;
  }
}

/**
 * Gửi ZNS với Journey Token
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param journeyToken Journey Token
 * @param phone Số điện thoại người nhận (định dạng chuẩn hóa, ví dụ: ***********)
 * @param templateId ID của template ZNS
 * @param templateData Dữ liệu template
 * @returns Thông tin kết quả gửi ZNS
 */
export async function sendZnsMessageWithJourneyToken(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  journeyToken: string,
  phone: string,
  templateId: string,
  templateData: Record<string, any>,
): Promise<SendZnsResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(
    supabase,
    oaConfigId,
  );

  // Chuẩn hóa số điện thoại
  const normalizedPhone = normalizePhone(phone);

  try {
    // Gọi API gửi ZNS với Journey Token
    const response = await axios.post(
      'https://business.openapi.zalo.me/message/template',
      {
        phone: normalizedPhone,
        template_id: templateId,
        template_data: templateData,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          access_token: accessToken,
          journey_token: journeyToken,
        },
      },
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(
        `Failed to send ZNS with Journey Token: ${response.data.message}`,
      );
    }

    // Trả về thông tin kết quả gửi ZNS
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(
        `Failed to send ZNS with Journey Token: ${error.response.data.message || error.message}`,
      );
    }
    throw error;
  }
}

/**
 * Kiểm tra Journey Token
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param journeyToken Journey Token
 * @returns Thông tin Journey Token
 */
export async function checkZnsJourneyToken(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  journeyToken: string,
): Promise<CheckZnsJourneyTokenResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(
    supabase,
    oaConfigId,
  );

  try {
    // Gọi API kiểm tra Journey Token
    const response = await axios.get(
      'https://business.openapi.zalo.me/journey/check-token',
      {
        headers: {
          'Content-Type': 'application/json',
          access_token: accessToken,
          journey_token: journeyToken,
        },
      },
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(
        `Failed to check ZNS Journey Token: ${response.data.message}`,
      );
    }

    // Trả về thông tin Journey Token
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(
        `Failed to check ZNS Journey Token: ${error.response.data.message || error.message}`,
      );
    }
    throw error;
  }
}

/**
 * Chuẩn hóa số điện thoại
 * @param phone Số điện thoại
 * @returns Số điện thoại đã chuẩn hóa
 */
function normalizePhone(phone: string): string {
  // Loại bỏ dấu + nếu có
  let normalizedPhone = phone.replace(/^\+/, '');

  // Nếu số điện thoại bắt đầu bằng 0, thay thế bằng 84
  if (normalizedPhone.startsWith('0')) {
    normalizedPhone = `84${normalizedPhone.substring(1)}`;
  }

  // Nếu số điện thoại chưa có mã quốc gia, thêm 84 vào đầu
  if (!normalizedPhone.startsWith('84')) {
    normalizedPhone = `84${normalizedPhone}`;
  }

  return normalizedPhone;
}

/**
 * Hash số điện thoại bằng SHA-256
 * @param phone Số điện thoại
 * @returns Số điện thoại đã hash
 */
function hashPhone(phone: string): string {
  return crypto.createHash('sha256').update(phone).digest('hex');
}
