import { createClient } from '@supabase/supabase-js';

import axios from 'axios';

import { Database } from '@kit/supabase/database';

/**
 * Lấy access token từ authorization code
 *
 * API: https://oauth.zaloapp.com/v4/oa/access_token
 *
 * @param appId ID của ứng dụng Zalo
 * @param secretKey Khóa bí mật của ứng dụng
 * @param code Authorization code nhận được từ Zalo
 * @param codeVerifier Code verifier (nếu có sử dụng PKCE)
 * @returns Thông tin access token và refresh token
 */
export async function getAccessTokenFromCode(
  appId: string,
  secretKey: string,
  code: string,
  codeVerifier?: string,
) {
  try {
    const params = new URLSearchParams();
    params.append('code', code);
    params.append('app_id', appId);
    params.append('grant_type', 'authorization_code');

    if (codeVerifier) {
      params.append('code_verifier', codeVerifier);
    }

    const response = await axios.post(
      'https://oauth.zaloapp.com/v4/oa/access_token',
      params,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          secret_key: secretKey,
        },
      },
    );

    return {
      accessToken: response.data.access_token,
      refreshToken: response.data.refresh_token,
      expiresIn: parseInt(response.data.expires_in),
    };
  } catch (error: any) {
    console.error('Failed to get access token from code:', error.message);
    throw error;
  }
}

/**
 * Làm mới access token từ refresh token
 *
 * API: https://oauth.zaloapp.com/v4/oa/access_token
 *
 * @param appId ID của ứng dụng Zalo
 * @param secretKey Khóa bí mật của ứng dụng
 * @param refreshToken Refresh token
 * @returns Thông tin access token và refresh token mới
 */
export async function refreshAccessToken(
  appId: string,
  secretKey: string,
  refreshToken: string,
) {
  try {
    const params = new URLSearchParams();
    params.append('refresh_token', refreshToken);
    params.append('app_id', appId);
    params.append('grant_type', 'refresh_token');

    const response = await axios.post(
      'https://oauth.zaloapp.com/v4/oa/access_token',
      params,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          secret_key: secretKey,
        },
      },
    );

    // Kiểm tra lỗi từ API Zalo
    if (response.data.error) {
      // Các lỗi khác
      throw new Error(
        `Zalo API error: ${response.data.error_description || response.data.error_name || response.data.error}`,
      );
    }

    // Kiểm tra dữ liệu trả về
    if (!response.data.access_token) {
      throw new Error('Failed to refresh token: No access token returned');
    }

    return {
      accessToken: response.data.access_token,
      refreshToken: response.data.refresh_token,
      expiresIn: response.data.expires_in
        ? parseInt(response.data.expires_in)
        : null,
    };
  } catch (error: any) {
    throw error;
  }
}

/**
 * Cập nhật thông tin token trong database
 *
 * @param supabase Supabase client
 * @param oaConfigId ID của cấu hình OA
 * @param accessToken Access token mới
 * @param refreshToken Refresh token mới
 * @param expiresIn Thời gian hết hạn (giây)
 */
export async function updateTokenInDatabase(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  accessToken: string,
  refreshToken: string,
  expiresIn: number,
) {
  const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString();

  // Lấy thông tin cấu hình OA hiện tại
  const { data: oaConfig, error } = await supabase
    .from('oa_configurations')
    .select('*')
    .eq('id', oaConfigId)
    .single();

  if (error || !oaConfig) {
    throw new Error(`Failed to fetch OA config: ${error?.message}`);
  }

  // Cập nhật token và metadata
  await supabase
    .from('oa_configurations')
    .update({
      access_token: accessToken,
      refresh_token: refreshToken,
      token_expires_at: expiresAt,
      oa_metadata: {
        ...oaConfig.oa_metadata,
        last_refreshed: new Date().toISOString(),
        token_info: {
          ...oaConfig.oa_metadata?.token_info,
          expires_in: expiresIn,
          refreshed_at: new Date().toISOString(),
          expires_at: expiresAt,
        },
      },
    })
    .eq('id', oaConfigId);

  // Cập nhật trạng thái integration
  if (oaConfig.account_id) {
    await supabase
      .from('integrations')
      .update({
        status: 'connected',
      })
      .eq('account_id', oaConfig.account_id)
      .eq('type', 'zalo');
  }

  return {
    accessToken,
    refreshToken,
    expiresAt,
  };
}
