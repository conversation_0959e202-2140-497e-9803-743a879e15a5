import { createClient } from '@supabase/supabase-js';

import axios from 'axios';

import { Database } from '@kit/supabase/database';

import {
  CreateZnsTemplateResponse,
  ZnsTemplateLayout,
  ZnsTemplateParam,
  ZnsTemplateTag,
  ZnsTemplateType,
} from './create-template';
import { getValidZnsToken } from './utils';
import { validateZnsTemplate } from './validate-template-unified';

/**
 * Interface cho request chỉnh sửa template ZNS
 */
export interface EditZnsTemplateRequest {
  template_id: string;
  template_name: string;
  template_type: ZnsTemplateType | number;
  tag: ZnsTemplateTag | number;
  layout: ZnsTemplateLayout;
  params?: ZnsTemplateParam[];
  note?: string;
  tracking_id: string;
}

/**
 * Chỉnh sửa template ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateData Dữ liệu template cần chỉnh sửa
 * @returns Thông tin template đã chỉnh sửa
 */
export async function editZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateData: EditZnsTemplateRequest,
  accountId: string,
): Promise<CreateZnsTemplateResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(
    supabase,
    oaConfigId,
  );

  // Validate template
  const validationResult = validateZnsTemplate(
    templateData.template_type,
    templateData.layout,
    templateData.params,
  );

  // Return error if validation failed
  if (!validationResult.valid) {
    return {
      success: false,
      error: validationResult.error,
    };
  }

  // Retry configuration
  const maxRetries = 3;
  const baseDelay = 1000; // 1 second
  let lastError = null;
  let response = null;

  // Log the template data for debugging
  console.log(
    'Edit template data being sent to API:',
    JSON.stringify(templateData, null, 2),
  );

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`=== ZNS Edit API Call Attempt ${attempt}/${maxRetries} ===`);

      // Gọi API chỉnh sửa template
      response = await axios.post(
        'https://business.openapi.zalo.me/template/edit',
        templateData,
        {
          headers: {
            'Content-Type': 'application/json',
            access_token: accessToken,
          },
          timeout: 30000, // 30 seconds timeout
        },
      );

      // Log the response for debugging
      console.log(
        `Edit API response (attempt ${attempt}):`,
        JSON.stringify(response.data, null, 2),
      );

      // Kiểm tra kết quả
      if (response.data.error !== 0) {
        const errorCode = response.data.error;
        const errorMessage = response.data.message;

        // Check if this is a retryable error
        if (isRetryableError(errorCode) && attempt < maxRetries) {
          console.log(
            `Retryable error ${errorCode}: ${errorMessage}. Retrying in ${baseDelay * attempt}ms...`,
          );
          lastError = {
            error: errorCode,
            message: errorMessage,
            details: getDetailedErrorMessage(errorCode),
            code: `ZNS_ERROR_${errorCode}`,
          };

          // Wait before retry with exponential backoff
          await new Promise((resolve) =>
            setTimeout(resolve, baseDelay * attempt),
          );
          continue;
        }

        // Non-retryable error or max retries reached
        return {
          success: false,
          error: {
            error: errorCode,
            message: errorMessage,
            details: getDetailedErrorMessage(errorCode),
            code: `ZNS_ERROR_${errorCode}`,
          },
        };
      }

      // Success - break out of retry loop
      console.log(`Edit API call successful on attempt ${attempt}`);
      break;
    } catch (networkError: any) {
      console.log(`Network error on attempt ${attempt}:`, networkError.message);
      lastError = {
        error: -1,
        message: networkError.message || 'Network error',
        details:
          'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.',
        code: 'ZNS_ERROR_NETWORK',
      };

      if (attempt < maxRetries) {
        console.log(`Retrying network error in ${baseDelay * attempt}ms...`);
        await new Promise((resolve) =>
          setTimeout(resolve, baseDelay * attempt),
        );
        continue;
      }

      // Max retries reached for network error
      return {
        success: false,
        error: lastError,
      };
    }
  }

  // If we get here and response is null, return the last error
  if (!response) {
    return {
      success: false,
      error: lastError || {
        error: -1,
        message: 'Unknown error',
        details: 'Lỗi không xác định khi gọi API Zalo.',
        code: 'ZNS_ERROR_UNKNOWN',
      },
    };
  }

  try {
    // Lưu template vào database sau khi gọi API thành công
    const templateResult = response.data.data;

    // Cập nhật bản ghi trong bảng zns_templates
    try {
      // Kiểm tra xem template đã tồn tại trong database chưa
      const { data: existingTemplate, error: findError } = await supabase
        .from('zns_templates')
        .select('id')
        .eq('template_id', templateData.template_id)
        .eq('oa_config_id', oaConfigId)
        .single();

      if (findError && findError.code !== 'PGRST116') {
        // PGRST116 là lỗi "not found"
        console.error('Error finding template in database:', findError);
      }

      // Nếu template đã tồn tại, cập nhật nó
      if (existingTemplate) {
        const { error: updateError } = await supabase
          .from('zns_templates')
          .update({
            template_name: templateResult.template_name || templateData.template_name,
            status: templateResult.template_status,
            tag: String(templateResult.tag),
            preview_url: templateResult.preview_url,
            metadata: {
              // Dữ liệu từ Zalo API response
              oa_id: templateResult.oa_id,
              app_id: templateResult.app_id,
              template_type: templateResult.template_type,
              price: templateResult.price,
              timeout: templateResult.timeout,

              // Dữ liệu từ request gốc (serialize để tương thích với Json type)
              original_request: {
                template_name: templateData.template_name,
                tag: templateData.tag,
                layout: JSON.parse(JSON.stringify(templateData.layout)),
                params: templateData.params ? JSON.parse(JSON.stringify(templateData.params)) : null,
                note: templateData.note,
                tracking_id: templateData.tracking_id,
              },

              // Metadata
              updated_at: new Date().toISOString(),
              api_response: JSON.parse(JSON.stringify(templateResult)), // Serialize response
            },
            updated_at: new Date().toISOString(),
          })
          .eq('id', existingTemplate.id);

        if (updateError) {
          console.error('Error updating template in database:', updateError);
        }
      } else {
        // Nếu template chưa tồn tại, tạo mới nó
        const { error: insertError } = await supabase
          .from('zns_templates')
          .insert({
            account_id: accountId,
            oa_config_id: oaConfigId,
            template_id: String(templateResult.template_id),
            template_name: templateResult.template_name || templateData.template_name,
            // Không set event_type mặc định, để null cho đến khi user mapping
            event_type: null,
            enabled: false, // Mặc định disabled cho đến khi user enable
            status: templateResult.template_status,
            tag: String(templateResult.tag),
            preview_url: templateResult.preview_url,
            metadata: {
              // Dữ liệu từ Zalo API response
              oa_id: templateResult.oa_id,
              app_id: templateResult.app_id,
              template_type: templateResult.template_type,
              price: templateResult.price,
              timeout: templateResult.timeout,

              // Dữ liệu từ request gốc (serialize để tương thích với Json type)
              original_request: {
                template_name: templateData.template_name,
                tag: templateData.tag,
                layout: JSON.parse(JSON.stringify(templateData.layout)),
                params: templateData.params ? JSON.parse(JSON.stringify(templateData.params)) : null,
                note: templateData.note,
                tracking_id: templateData.tracking_id,
              },

              // Metadata
              created_at: new Date().toISOString(),
              api_response: JSON.parse(JSON.stringify(templateResult)), // Serialize response
            },
          });

        if (insertError) {
          console.error('Error inserting template to database:', insertError);
        }
      }
    } catch (dbError) {
      console.error('Error saving template to database:', dbError);
      // Không throw error ở đây vì API Zalo đã thành công
      // Chỉ log lỗi để debug
    }

    // Trả về kết quả thành công
    return {
      success: true,
      data: response.data.data,
    };
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response && error.response.data) {
      // Trả về lỗi chi tiết từ API
      return {
        success: false,
        error: {
          error: error.response.data.error || -1,
          message: error.response.data.message || 'Unknown error',
          details: getDetailedErrorMessage(error.response.data.error || -1),
          code: `ZNS_ERROR_${error.response.data.error || 'UNKNOWN'}`,
        },
      };
    }

    // Trả về lỗi chung
    return {
      success: false,
      error: {
        error: -1,
        message: error.message || 'Unknown error',
        details:
          'Lỗi không xác định khi chỉnh sửa template. Vui lòng thử lại sau.',
        code: 'ZNS_ERROR_EDIT_FAILED',
      },
    };
  }
}

/**
 * Chỉnh sửa template ZNS tùy chỉnh
 * Hàm helper để chỉnh sửa template ZNS tùy chỉnh dễ dàng hơn
 */
export async function editCustomZnsTemplate(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  accountId: string,
  templateId: string,
  templateName: string,
  tag: ZnsTemplateTag,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
  note?: string,
  trackingId?: string,
): Promise<CreateZnsTemplateResponse> {
  // Tạo tracking ID nếu không được cung cấp
  const finalTrackingId = trackingId || `edit_template_${Date.now()}`;

  // Tạo request data
  const templateData: EditZnsTemplateRequest = {
    template_id: templateId,
    template_name: templateName,
    template_type: ZnsTemplateType.CUSTOM,
    tag,
    layout,
    params,
    note,
    tracking_id: finalTrackingId,
  };

  // Gọi hàm chỉnh sửa template
  return editZnsTemplate(supabase, oaConfigId, templateData, accountId);
}

/**
 * Kiểm tra xem lỗi có thể retry được không
 * @param errorCode Mã lỗi từ Zalo API
 * @returns true nếu có thể retry, false nếu không
 */
function isRetryableError(errorCode: number): boolean {
  const retryableErrors = [
    -100, // Data is invalid | Cannot create template. Plz try again
    -153, // Data is invalid | Cannot create template. Plz try again
    -500, // Internal server error
    -503, // Service unavailable
    -504, // Gateway timeout
    -429, // Too many requests
  ];

  return retryableErrors.includes(errorCode);
}

/**
 * Hàm lấy thông báo lỗi chi tiết dựa trên mã lỗi
 * @param errorCode Mã lỗi từ Zalo API
 * @returns Thông báo lỗi chi tiết
 */
function getDetailedErrorMessage(errorCode: number): string {
  switch (errorCode) {
    case -153:
      return 'Dữ liệu không hợp lệ hoặc server Zalo đang bận. Hệ thống sẽ tự động thử lại.';
    case -1131:
      return 'Nội dung nút không hợp lệ. URL phải bắt đầu bằng http:// hoặc https:// và không chứa ký tự đặc biệt.';
    case -1132:
      return 'Tiêu đề template không hợp lệ. Tiêu đề phải có độ dài từ 3-36 ký tự.';
    case -1133:
      return 'Loại tham số không hợp lệ. Loại tham số phải là số từ 1-15.';
    case -1134:
      return 'Tên tham số không hợp lệ. Tên tham số chỉ được chứa chữ cái, số và dấu gạch dưới.';
    case -1135:
      return 'Giá trị mẫu của tham số không hợp lệ. Giá trị mẫu không được để trống.';
    case -1136:
      return 'Cấu trúc template không hợp lệ. Template phải có ít nhất một tiêu đề và một đoạn văn bản.';
    case -1137:
      return 'Template phải có ít nhất một logo hoặc hình ảnh trong phần header.';
    case -1138:
      return 'Số lượng nút trong template vượt quá giới hạn. Mỗi template chỉ được có tối đa 3 nút.';
    case -1139:
      return 'Tiêu đề nút không hợp lệ. Tiêu đề nút phải có độ dài từ 1-20 ký tự.';
    case -1140:
      return 'Loại nút không hợp lệ. Loại nút phải là số từ 1-5.';
    case -1141:
      return 'Số lượng tham số vượt quá giới hạn. Mỗi template chỉ được có tối đa 20 tham số.';
    case -1142:
      return 'Số lượng hàng trong bảng vượt quá giới hạn. Mỗi bảng chỉ được có tối đa 4 hàng.';
    case -1143:
      return 'Tiêu đề hàng trong bảng không hợp lệ. Tiêu đề hàng phải có độ dài từ 1-20 ký tự.';
    case -1144:
      return 'Giá trị hàng trong bảng không hợp lệ. Giá trị hàng không được để trống.';
    case -1145:
      return 'Loại hàng trong bảng không hợp lệ. Loại hàng phải là số từ 1-3.';
    case -1146:
      return 'Media ID không hợp lệ. Media ID phải được tạo trước bằng API upload ảnh.';
    case -1147:
      return 'Loại media không hợp lệ. Loại media phải là "IMAGE".';
    case -1148:
      return 'Tag template không hợp lệ. Tag phải là số từ 1-1000.';
    case -1149:
      return 'Loại template không hợp lệ. Loại template phải là số từ 1-3.';
    case -1150:
      return 'Ghi chú template không hợp lệ. Ghi chú không được vượt quá 200 ký tự.';
    case -1151:
      return 'Tracking ID không hợp lệ. Tracking ID không được vượt quá 50 ký tự.';
    case -124:
      return 'Access token không hợp lệ hoặc đã hết hạn. Vui lòng kết nối lại với Zalo OA.';
    case -500:
      return 'Lỗi server nội bộ của Zalo. Hệ thống sẽ tự động thử lại.';
    case -503:
      return 'Dịch vụ Zalo tạm thời không khả dụng. Hệ thống sẽ tự động thử lại.';
    case -504:
      return 'Timeout khi kết nối với server Zalo. Hệ thống sẽ tự động thử lại.';
    case -429:
      return 'Quá nhiều yêu cầu gửi đến Zalo API. Hệ thống sẽ tự động thử lại sau ít phút.';
    default:
      return `Lỗi không xác định từ Zalo API (mã lỗi: ${errorCode}). Vui lòng thử lại sau hoặc liên hệ hỗ trợ.`;
  }
}

/**
 * Lấy thông tin template ZNS theo ID
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param templateId ID của template cần lấy thông tin
 * @returns Thông tin chi tiết của template
 */
export async function getZnsTemplateById(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  templateId: string,
): Promise<any> {
  // Lấy thông tin OA configuration
  const { data: oaConfig, error } = await supabase
    .from('oa_configurations')
    .select('*')
    .eq('id', oaConfigId)
    .single();

  if (error || !oaConfig) {
    throw new Error(`Failed to fetch OA config: ${error?.message}`);
  }

  // Kiểm tra và refresh token nếu cần
  let accessToken = oaConfig.access_token;
  if (new Date(oaConfig.token_expires_at) < new Date()) {
    accessToken = await refreshZnsToken(supabase, oaConfig);
  }

  try {
    // Gọi API lấy thông tin template
    const response = await axios.get(
      'https://openapi.zalo.me/v2.0/oa/template/info',
      {
        params: {
          template_id: templateId,
        },
        headers: {
          access_token: accessToken,
        },
      },
    );

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      return {
        success: false,
        error: {
          error: response.data.error,
          message: response.data.message,
          details: getDetailedErrorMessage(response.data.error),
          code: `ZNS_ERROR_${response.data.error}`,
        },
      };
    }

    // Trả về thông tin template
    return {
      success: true,
      data: response.data.data,
    };
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response && error.response.data) {
      // Trả về lỗi chi tiết từ API
      return {
        success: false,
        error: {
          error: error.response.data.error || -1,
          message: error.response.data.message || 'Unknown error',
          details: getDetailedErrorMessage(error.response.data.error || -1),
          code: `ZNS_ERROR_${error.response.data.error || 'UNKNOWN'}`,
        },
      };
    }

    // Trả về lỗi chung
    return {
      success: false,
      error: {
        error: -1,
        message: error.message || 'Unknown error',
        details:
          'Lỗi không xác định khi lấy thông tin template. Vui lòng thử lại sau.',
        code: 'ZNS_ERROR_GET_TEMPLATE_FAILED',
      },
    };
  }
}
