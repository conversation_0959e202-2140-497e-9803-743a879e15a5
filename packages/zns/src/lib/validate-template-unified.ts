import {
  ZnsTemplateLayout,
  ZnsTemplateParam,
  ZnsTemplateType,
} from './create-template';

export interface ZnsTemplateError {
  error: number;
  message: string;
  details?: string;
  code?: string;
}

export interface ValidationResult {
  valid: boolean;
  error?: ZnsTemplateError;
}

/**
 * Validate template structure and parameters
 * @param templateType Template type
 * @param layout Template layout
 * @param params Template parameters
 * @returns Validation result
 */
export function validateZnsTemplate(
  templateType: ZnsTemplateType | number,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
): ValidationResult {
  try {
    // Validate header
    const headerResult = validateHeader(layout);
    if (!headerResult.valid) return headerResult;

    // Validate body
    const bodyResult = validateBody(layout);
    if (!bodyResult.valid) return bodyResult;

    // Validate footer
    if (layout.footer) {
      const footerResult = validateFooter(layout);
      if (!footerResult.valid) return footerResult;
    }

    // Validate parameters
    if (params && params.length > 0) {
      const paramsResult = validateParameters(params);
      if (!paramsResult.valid) return paramsResult;
    }

    // Validate template type specific constraints
    const templateTypeResult = validateTemplateTypeConstraints(
      templateType,
      layout,
      params,
    );
    if (!templateTypeResult.valid) return templateTypeResult;

    // All validations passed
    return { valid: true };
  } catch (error) {
    // Catch any unexpected errors
    return {
      valid: false,
      error: {
        error: -1,
        message:
          error instanceof Error ? error.message : 'Unknown validation error',
        details:
          'Lỗi không xác định khi kiểm tra template. Vui lòng thử lại sau.',
        code: 'ZNS_ERROR_VALIDATION_FAILED',
      },
    };
  }
}

/**
 * Validate header structure
 * @param layout Template layout
 * @returns Validation result
 */
function validateHeader(layout: ZnsTemplateLayout): ValidationResult {
  // Check if header exists and has components array
  if (!layout.header || !Array.isArray(layout.header.components)) {
    return {
      valid: false,
      error: {
        error: -1136,
        message: 'Header must have components array',
        details:
          'Cấu trúc template không hợp lệ. Header phải có mảng components.',
        code: 'ZNS_ERROR_INVALID_HEADER',
      },
    };
  }

  // Check if header has at least one logo or image
  // Note: We're not validating media_id here because it might be a temporary value
  // that will be replaced after image upload
  const hasLogoOrImage = layout.header.components.some(
    (component) => component.LOGO || component.IMAGE,
  );

  if (!hasLogoOrImage) {
    return {
      valid: false,
      error: {
        error: -1137,
        message: 'Header must have at least one LOGO or IMAGE component',
        details:
          'Template phải có ít nhất một logo hoặc hình ảnh trong phần header.',
        code: 'ZNS_ERROR_MISSING_MEDIA',
      },
    };
  }

  return { valid: true };
}

/**
 * Validate body structure
 * @param layout Template layout
 * @returns Validation result
 */
function validateBody(layout: ZnsTemplateLayout): ValidationResult {
  // Check if body exists and has components array
  if (!layout.body || !Array.isArray(layout.body.components)) {
    return {
      valid: false,
      error: {
        error: -1136,
        message: 'Body must have components array',
        details:
          'Cấu trúc template không hợp lệ. Body phải có mảng components.',
        code: 'ZNS_ERROR_INVALID_BODY',
      },
    };
  }

  // Check if body has at least one component
  if (layout.body.components.length === 0) {
    return {
      valid: false,
      error: {
        error: -1136,
        message: 'Body must have at least one component',
        details: 'Body phải có ít nhất một thành phần.',
        code: 'ZNS_ERROR_EMPTY_BODY',
      },
    };
  }

  // Check if body has a TITLE component
  const hasTitleComponent = layout.body.components.some((comp) => comp.TITLE);
  if (!hasTitleComponent) {
    return {
      valid: false,
      error: {
        error: -1136,
        message: 'Body must have a TITLE component',
        details: 'Body phải có một thành phần TITLE.',
        code: 'ZNS_ERROR_MISSING_TITLE',
      },
    };
  }

  // Validate PARAGRAPH components
  const paragraphs = layout.body.components.filter((comp) => comp.PARAGRAPH);
  if (paragraphs.length > 4) {
    return {
      valid: false,
      error: {
        error: -1136,
        message: 'Body cannot have more than 4 PARAGRAPH components',
        details: 'Body không được có quá 4 thành phần PARAGRAPH.',
        code: 'ZNS_ERROR_TOO_MANY_PARAGRAPHS',
      },
    };
  }

  // Validate TABLE components
  const tables = layout.body.components.filter((comp) => comp.TABLE);
  for (const table of tables) {
    if (!table.TABLE?.rows || !Array.isArray(table.TABLE.rows)) {
      return {
        valid: false,
        error: {
          error: -1136,
          message: 'TABLE must have rows array',
          details: 'Thành phần TABLE phải có mảng rows.',
          code: 'ZNS_ERROR_INVALID_TABLE',
        },
      };
    }

    // if (table.TABLE.rows.length > 4) {
    //   return {
    //     valid: false,
    //     error: {
    //       error: -1142,
    //       message: 'TABLE cannot have more than 4 rows',
    //       details: 'Thành phần TABLE không được có quá 4 hàng.',
    //       code: 'ZNS_ERROR_TOO_MANY_TABLE_ROWS',
    //     },
    //   };
    // }

    // Validate each row
    for (const row of table.TABLE.rows) {
      if (!row.title) {
        return {
          valid: false,
          error: {
            error: -1143,
            message: 'TABLE row must have title',
            details: 'Mỗi hàng trong TABLE phải có tiêu đề.',
            code: 'ZNS_ERROR_MISSING_ROW_TITLE',
          },
        };
      }

      if (row.type && ![1, 2, 3].includes(row.type)) {
        return {
          valid: false,
          error: {
            error: -1145,
            message: 'TABLE row_type must be 1, 2, or 3',
            details:
              'Loại hàng trong bảng không hợp lệ. Loại hàng phải là số từ 1-3.',
            code: 'ZNS_ERROR_INVALID_TABLE_ROW_TYPE',
          },
        };
      }
    }
  }

  return { valid: true };
}

/**
 * Validate footer structure
 * @param layout Template layout
 * @returns Validation result
 */
function validateFooter(layout: ZnsTemplateLayout): ValidationResult {
  // Check if footer has components array
  if (!Array.isArray(layout.footer?.components)) {
    return {
      valid: false,
      error: {
        error: -1136,
        message: 'Footer must have components array',
        details:
          'Cấu trúc template không hợp lệ. Footer phải có mảng components.',
        code: 'ZNS_ERROR_INVALID_FOOTER',
      },
    };
  }

  // Validate BUTTONS components
  const buttons = layout.footer.components.filter((comp) => comp.BUTTONS);
  let totalButtons = 0;

  for (const buttonComponent of buttons) {
    if (
      !buttonComponent.BUTTONS?.items ||
      !Array.isArray(buttonComponent.BUTTONS.items)
    ) {
      return {
        valid: false,
        error: {
          error: -1138,
          message: 'BUTTONS component must have items array',
          details: 'Thành phần BUTTONS phải có mảng items.',
          code: 'ZNS_ERROR_INVALID_BUTTONS',
        },
      };
    }

    totalButtons += buttonComponent.BUTTONS.items.length;

    // Validate each button
    for (const button of buttonComponent.BUTTONS.items) {
      if (!button.title) {
        return {
          valid: false,
          error: {
            error: -1139,
            message: 'Button must have title',
            details: 'Mỗi nút phải có tiêu đề.',
            code: 'ZNS_ERROR_MISSING_BUTTON_TITLE',
          },
        };
      }

      if (![1, 2, 3, 4, 5].includes(button.type)) {
        return {
          valid: false,
          error: {
            error: -1140,
            message: 'Button type must be 1, 2, 3, 4, or 5',
            details: 'Loại nút không hợp lệ. Loại nút phải là số từ 1-5.',
            code: 'ZNS_ERROR_INVALID_BUTTON_TYPE',
          },
        };
      }

      // Validate button content based on type
      if (button.type === 1 && !button.content) {
        return {
          valid: false,
          error: {
            error: -1131,
            message: 'Button with type 1 must have content',
            details: 'Nút loại 1 (URL) phải có nội dung.',
            code: 'ZNS_ERROR_MISSING_BUTTON_CONTENT',
          },
        };
      }
    }
  }

  if (totalButtons > 3) {
    return {
      valid: false,
      error: {
        error: -1138,
        message: 'Footer cannot have more than 3 buttons in total',
        details: 'Footer không được có quá 3 nút.',
        code: 'ZNS_ERROR_TOO_MANY_BUTTONS',
      },
    };
  }

  return { valid: true };
}

/**
 * Validate parameters
 * @param params Template parameters
 * @returns Validation result
 */
function validateParameters(params: ZnsTemplateParam[]): ValidationResult {
  // Check if there are too many parameters
  if (params.length > 20) {
    return {
      valid: false,
      error: {
        error: -1141,
        message: 'Template cannot have more than 20 parameters',
        details: 'Template không được có quá 20 tham số.',
        code: 'ZNS_ERROR_TOO_MANY_PARAMS',
      },
    };
  }

  // Validate each parameter
  for (const param of params) {
    // Check parameter type
    const paramType = Number(param.type);
    if (isNaN(paramType) || paramType < 1 || paramType > 15) {
      return {
        valid: false,
        error: {
          error: -1133,
          message: `Parameter ${param.name} has invalid type: ${param.type}`,
          details:
            'Loại tham số không hợp lệ. Loại tham số phải là số từ 1-15.',
          code: 'ZNS_ERROR_INVALID_PARAM_TYPE',
        },
      };
    }

    // Check parameter name
    if (!/^[a-zA-Z0-9_]+$/.test(param.name)) {
      return {
        valid: false,
        error: {
          error: -1134,
          message: `Parameter name ${param.name} is invalid. Only letters, numbers, and underscore are allowed.`,
          details:
            'Tên tham số không hợp lệ. Tên tham số chỉ được chứa chữ cái, số và dấu gạch dưới.',
          code: 'ZNS_ERROR_INVALID_PARAM_NAME',
        },
      };
    }

    // Check sample value
    if (!param.sample_value) {
      return {
        valid: false,
        error: {
          error: -1135,
          message: `Parameter ${param.name} must have sample_value`,
          details:
            'Giá trị mẫu của tham số không hợp lệ. Giá trị mẫu không được để trống.',
          code: 'ZNS_ERROR_INVALID_PARAM_SAMPLE',
        },
      };
    }
  }

  return { valid: true };
}

/**
 * Validate template type specific constraints
 * @param templateType Template type
 * @param layout Template layout
 * @param params Template parameters
 * @returns Validation result
 */
function validateTemplateTypeConstraints(
  templateType: ZnsTemplateType | number,
  layout: ZnsTemplateLayout,
  params?: ZnsTemplateParam[],
): ValidationResult {
  const type = Number(templateType);

  switch (type) {
    case ZnsTemplateType.CUSTOM: {
      // Custom template specific validations
      return validateCustomTemplate(layout);
    }
    // Add other template types as needed
    default:
      return { valid: true };
  }
}

/**
 * Validate custom template
 * @param layout Template layout
 * @returns Validation result
 */
function validateCustomTemplate(layout: ZnsTemplateLayout): ValidationResult {
  // Custom template specific validations
  // For example, custom templates might have specific requirements for header, body, and footer

  // Header: 1 LOGO or 1 IMAGES
  if (layout.header) {
    if (layout.header.components.length !== 1) {
      return {
        valid: false,
        error: {
          error: -1136,
          message:
            'Custom template header must contain exactly 1 component (LOGO or IMAGES)',
          details:
            'Header của template tùy chỉnh phải chứa đúng 1 thành phần (LOGO hoặc IMAGES).',
          code: 'ZNS_ERROR_INVALID_HEADER',
        },
      };
    }
  }

  return { valid: true };
}
