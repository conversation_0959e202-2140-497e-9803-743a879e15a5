import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@kit/supabase/database';
import {
  handleZnsWebhook,
  ZnsWebhookEventType,
  ZnsUserFeedbackEvent,
  ZnsChangeOaDailyQuotaEvent,
  ZnsChangeTemplateStatusEvent,
  ZnsUserReceivedMessageEvent,
  handleUserFeedbackEvent,
  handleChangeOaDailyQuotaEvent,
  handleChangeTemplateStatusEvent,
  handleUserReceivedMessageEvent
} from './webhook-handlers';

/**
 * V<PERSON> dụ về cách xử lý webhook ZNS trong Next.js API route
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Chỉ chấp nhận phương thức POST
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Khởi tạo Supabase client
  const supabase = createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );

  try {
    // Lấy secret key từ cấu hình
    const { data: oaConfig } = await supabase
      .from('oa_configurations')
      .select('secret_key')
      .eq('id', req.query.oaConfigId as string)
      .single();

    if (!oaConfig) {
      return res.status(404).json({ error: 'OA configuration not found' });
    }

    // Xử lý webhook
    const event = handleZnsWebhook(
      req.headers as Record<string, string>,
      req.body,
      oaConfig.secret_key
    );

    if (!event) {
      return res.status(400).json({ error: 'Unknown event type' });
    }

    // Xử lý các loại sự kiện
    switch (event.event_name) {
      case ZnsWebhookEventType.USER_FEEDBACK:
        await handleUserFeedback(supabase, event as ZnsUserFeedbackEvent);
        break;
      case ZnsWebhookEventType.CHANGE_OA_DAILY_QUOTA:
        await handleChangeOaDailyQuota(supabase, event as ZnsChangeOaDailyQuotaEvent);
        break;
      case ZnsWebhookEventType.CHANGE_TEMPLATE_STATUS:
        await handleChangeTemplateStatus(supabase, event as ZnsChangeTemplateStatusEvent);
        break;
      case ZnsWebhookEventType.USER_RECEIVED_MESSAGE:
        await handleUserReceivedMessage(supabase, event as ZnsUserReceivedMessageEvent);
        break;
      // Xử lý các loại sự kiện khác tương tự
    }

    // Hoặc sử dụng các hàm helper
    handleUserFeedbackEvent(event, async (feedbackEvent) => {
      await handleUserFeedback(supabase, feedbackEvent);
    });

    handleChangeOaDailyQuotaEvent(event, async (quotaEvent) => {
      await handleChangeOaDailyQuota(supabase, quotaEvent);
    });

    // Trả về thành công
    return res.status(200).json({ success: true });
  } catch (error: any) {
    console.error('Error handling ZNS webhook:', error);
    return res.status(500).json({ error: error.message });
  }
}

/**
 * Xử lý sự kiện người dùng phản hồi template đánh giá dịch vụ
 */
async function handleUserFeedback(
  supabase: ReturnType<typeof createClient<Database>>,
  event: ZnsUserFeedbackEvent
) {
  // Lưu thông tin đánh giá vào database
  await supabase.from('zns_ratings').insert({
    oa_id: event.oa_id,
    app_id: event.app_id,
    msg_id: event.message.msg_id,
    tracking_id: event.message.tracking_id,
    rate: event.message.rate,
    note: event.message.note,
    feedbacks: event.message.feedbacks,
    submit_time: new Date(parseInt(event.message.submit_time)).toISOString(),
    metadata: event
  });
}

/**
 * Xử lý sự kiện thay đổi về hạn mức gửi ZNS
 */
async function handleChangeOaDailyQuota(
  supabase: ReturnType<typeof createClient<Database>>,
  event: ZnsChangeOaDailyQuotaEvent
) {
  // Cập nhật thông tin quota trong database
  const { data: oaConfig } = await supabase
    .from('oa_configurations')
    .select('id')
    .eq('oa_id', event.oa_id)
    .single();

  if (oaConfig) {
    await supabase
      .from('oa_configurations')
      .update({
        oa_metadata: {
          daily_quota: event.quota.new_value,
          quota_updated_at: new Date().toISOString()
        }
      })
      .eq('id', oaConfig.id);
  }
}

/**
 * Xử lý sự kiện thay đổi trạng thái Template ZNS
 */
async function handleChangeTemplateStatus(
  supabase: ReturnType<typeof createClient<Database>>,
  event: ZnsChangeTemplateStatusEvent
) {
  // Cập nhật trạng thái template trong database
  await supabase
    .from('zns_templates')
    .update({
      status: event.status.new_status,
      status_reason: event.reason,
      updated_at: new Date().toISOString()
    })
    .eq('template_id', event.template_id);
}

/**
 * Xử lý sự kiện người dùng nhận thông báo ZNS
 */
async function handleUserReceivedMessage(
  supabase: ReturnType<typeof createClient<Database>>,
  event: ZnsUserReceivedMessageEvent
) {
  // Cập nhật trạng thái gửi tin trong database
  await supabase
    .from('zns_messages')
    .update({
      status: 'delivered',
      delivered_at: new Date(parseInt(event.message.delivery_time)).toISOString(),
      metadata: {
        ...event
      }
    })
    .eq('msg_id', event.message.msg_id);
}
