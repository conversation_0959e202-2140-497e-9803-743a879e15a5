import { EventEmitter } from 'events';

/**
 * ZNS Event Bus - Xử lý các sự kiện liên quan đến ZNS
 *
 * Sử dụng:
 * ```
 * import { znsEventBus } from '@kit/zns/lib/event-bus';
 *
 * // Ph<PERSON>t sự kiện
 * znsEventBus.emit('order.created', {
 *   module: 'orders',
 *   event: 'created',
 *   data: orderData,
 *   metadata: { accountId: '123', timestamp: new Date().toISOString() }
 * });
 *
 * // Lắng nghe sự kiện
 * znsEventBus.on('order.created', (eventData) => {
 *   // Xử lý sự kiện
 * });
 *
 * // Lắng nghe tất cả sự kiện
 * znsEventBus.on('*', (eventName, eventData) => {
 *   // Xử lý tất cả sự kiện
 * });
 * ```
 */
class ZnsEventBus extends EventEmitter {
  /**
   * <PERSON><PERSON><PERSON> sự kiện
   * @param event Tên sự kiện
   * @param data Dữ liệu sự kiện
   * @returns boolean
   */
  emit(event: string, data: any): boolean {
    super.emit(event, data);
    super.emit('*', event, data); // Phát sự kiện wildcard
    return true;
  }
}

// Singleton instance
export const znsEventBus = new ZnsEventBus();

// Các loại sự kiện
export type ZnsEventModule = 'orders' | 'education' | 'marketing' | 'system';
export type ZnsEventType =
  // Orders
  | 'created'
  | 'updated'
  | 'status_updated'
  | 'payment_received'
  // Education
  | 'tuition_created'
  | 'class_scheduled'
  // Marketing
  | 'promotion_created'
  | 'birthday_reminder'
  // System
  | 'user_registered'
  | 'subscription_updated';

/**
 * Dữ liệu sự kiện ZNS
 */
export interface ZnsEventData<T = any> {
  module: ZnsEventModule;
  event: ZnsEventType;
  data: T;
  metadata: {
    accountId: string;
    timestamp: string;
    [key: string]: any;
  };
}

/**
 * Hàm tiện ích để phát sự kiện ZNS
 * @param module Module phát sự kiện
 * @param event Loại sự kiện
 * @param data Dữ liệu sự kiện
 * @param metadata Metadata
 */
export function emitZnsEvent<T = any>(
  module: ZnsEventModule,
  event: ZnsEventType,
  data: T,
  metadata: Omit<ZnsEventData['metadata'], 'timestamp'>
): void {
  znsEventBus.emit(`${module}.${event}`, {
    module,
    event,
    data,
    metadata: {
      ...metadata,
      timestamp: new Date().toISOString()
    }
  });
}
