import crypto from 'crypto';

/**
 * Enum cho các loại sự kiện webhook ZNS
 */
export enum ZnsWebhookEventType {
  USER_FEEDBACK = 'user_feedback',
  CHANGE_OA_DAILY_QUOTA = 'change_oa_daily_quota',
  CHANGE_OA_TEMPLATE_TAGS = 'change_oa_template_tags',
  CHANGE_OA_TEMPLATE_TAGS_V2 = 'change_oa_template_tags_v2',
  CHANGE_TEMPLATE_QUALITY = 'change_template_quality',
  EVENT_JOURNEY_TIME_OUT = 'event_journey_time_out',
  EVENT_JOURNEY_ACKNOWLEDGED = 'event_journey_acknowledged',
  USER_RECEIVED_MESSAGE = 'user_received_message',
  CHANGE_TEMPLATE_STATUS = 'change_template_status'
}

/**
 * Interface cho sự kiện webhook ZNS cơ bản
 */
export interface ZnsWebhookEvent {
  event_name: ZnsWebhookEventType;
  timestamp: string;
  [key: string]: any;
}

/**
 * Interface cho sự kiện người dùng phản hồi template đánh giá dịch vụ
 */
export interface ZnsUserFeedbackEvent extends ZnsWebhookEvent {
  event_name: ZnsWebhookEventType.USER_FEEDBACK;
  message: {
    note: string;
    rate: number;
    submit_time: string;
    msg_id: string;
    feedbacks: string[];
    tracking_id: string;
  };
  app_id: string;
  oa_id: string;
}

/**
 * Interface cho sự kiện thay đổi về hạn mức gửi ZNS
 */
export interface ZnsChangeOaDailyQuotaEvent extends ZnsWebhookEvent {
  event_name: ZnsWebhookEventType.CHANGE_OA_DAILY_QUOTA;
  oa_id: string;
  quota: {
    prev_value: number;
    new_value: number;
  };
}

/**
 * Interface cho sự kiện thay đổi về loại nội dung ZNS có thể gửi (v1)
 */
export interface ZnsChangeOaTemplateTagsEvent extends ZnsWebhookEvent {
  event_name: ZnsWebhookEventType.CHANGE_OA_TEMPLATE_TAGS;
  oa_id: string;
  tag_level: {
    prev_value: number;
    new_value: number;
  };
}

/**
 * Interface cho sự kiện thay đổi về loại nội dung ZNS có thể gửi (v2)
 */
export interface ZnsChangeOaTemplateTagsV2Event extends ZnsWebhookEvent {
  event_name: ZnsWebhookEventType.CHANGE_OA_TEMPLATE_TAGS_V2;
  oa_id: string;
  tag_level: {
    prev_value: string[];
    new_value: string[];
  };
}

/**
 * Interface cho sự kiện thay đổi về chất lượng gửi của mẫu tin ZNS
 */
export interface ZnsChangeTemplateQualityEvent extends ZnsWebhookEvent {
  event_name: ZnsWebhookEventType.CHANGE_TEMPLATE_QUALITY;
  oa_id: string;
  template_id: string;
  quality: string;
}

/**
 * Interface cho sự kiện journey hết hạn
 */
export interface ZnsJourneyTimeOutEvent extends ZnsWebhookEvent {
  event_name: ZnsWebhookEventType.EVENT_JOURNEY_TIME_OUT;
  journey_id: string;
  app_id: string;
  oa_id: string;
}

/**
 * Interface cho sự kiện journey được tính phí
 */
export interface ZnsJourneyAcknowledgedEvent extends ZnsWebhookEvent {
  event_name: ZnsWebhookEventType.EVENT_JOURNEY_ACKNOWLEDGED;
  journey_id: string;
  msg_id: string;
  app_id: string;
  oa_id: string;
}

/**
 * Interface cho sự kiện người dùng nhận thông báo ZNS
 */
export interface ZnsUserReceivedMessageEvent extends ZnsWebhookEvent {
  event_name: ZnsWebhookEventType.USER_RECEIVED_MESSAGE;
  sender: {
    id: string;
  };
  recipient: {
    id: string;
  };
  message: {
    delivery_time: string;
    msg_id: string;
    tracking_id: string;
  };
  app_id: string;
}

/**
 * Interface cho sự kiện thay đổi trạng thái Template ZNS
 */
export interface ZnsChangeTemplateStatusEvent extends ZnsWebhookEvent {
  event_name: ZnsWebhookEventType.CHANGE_TEMPLATE_STATUS;
  oa_id: string;
  app_id: string;
  template_id: string;
  status: {
    prev_status: string;
    new_status: string;
  };
  reason: string;
}

/**
 * Type union cho tất cả các loại sự kiện webhook ZNS
 */
export type ZnsWebhookEventUnion =
  | ZnsUserFeedbackEvent
  | ZnsChangeOaDailyQuotaEvent
  | ZnsChangeOaTemplateTagsEvent
  | ZnsChangeOaTemplateTagsV2Event
  | ZnsChangeTemplateQualityEvent
  | ZnsJourneyTimeOutEvent
  | ZnsJourneyAcknowledgedEvent
  | ZnsUserReceivedMessageEvent
  | ZnsChangeTemplateStatusEvent;

/**
 * Xác thực chữ ký webhook ZNS
 * @param signature Chữ ký từ header X-ZEvent-Signature
 * @param appId ID của ứng dụng
 * @param data Dữ liệu webhook (JSON string)
 * @param timestamp Thời gian từ dữ liệu webhook
 * @param secretKey Secret key của ứng dụng
 * @returns Kết quả xác thực
 */
export function verifyZnsWebhookSignature(
  signature: string,
  appId: string,
  data: string,
  timestamp: string,
  secretKey: string
): boolean {
  const payload = appId + data + timestamp + secretKey;
  const calculatedSignature = crypto.createHash('sha256').update(payload).digest('hex');
  return signature === calculatedSignature;
}

/**
 * Xử lý sự kiện webhook ZNS
 * @param headers Headers của request
 * @param body Body của request
 * @param secretKey Secret key của ứng dụng
 * @returns Sự kiện webhook đã được xác thực và phân loại
 */
export function handleZnsWebhook(
  headers: Record<string, string>,
  body: any,
  secretKey: string
): ZnsWebhookEventUnion | null {
  // Lấy chữ ký từ header
  const signature = headers['x-zevent-signature'];
  if (!signature) {
    throw new Error('Missing X-ZEvent-Signature header');
  }

  // Chuyển đổi body thành chuỗi JSON nếu cần
  const bodyString = typeof body === 'string' ? body : JSON.stringify(body);
  const bodyObject = typeof body === 'string' ? JSON.parse(body) : body;

  // Xác thực chữ ký
  const isValid = verifyZnsWebhookSignature(
    signature,
    bodyObject.app_id,
    bodyString,
    bodyObject.timestamp,
    secretKey
  );

  if (!isValid) {
    throw new Error('Invalid webhook signature');
  }

  // Phân loại sự kiện
  switch (bodyObject.event_name) {
    case ZnsWebhookEventType.USER_FEEDBACK:
      return bodyObject as ZnsUserFeedbackEvent;
    case ZnsWebhookEventType.CHANGE_OA_DAILY_QUOTA:
      return bodyObject as ZnsChangeOaDailyQuotaEvent;
    case ZnsWebhookEventType.CHANGE_OA_TEMPLATE_TAGS:
      return bodyObject as ZnsChangeOaTemplateTagsEvent;
    case ZnsWebhookEventType.CHANGE_OA_TEMPLATE_TAGS_V2:
      return bodyObject as ZnsChangeOaTemplateTagsV2Event;
    case ZnsWebhookEventType.CHANGE_TEMPLATE_QUALITY:
      return bodyObject as ZnsChangeTemplateQualityEvent;
    case ZnsWebhookEventType.EVENT_JOURNEY_TIME_OUT:
      return bodyObject as ZnsJourneyTimeOutEvent;
    case ZnsWebhookEventType.EVENT_JOURNEY_ACKNOWLEDGED:
      return bodyObject as ZnsJourneyAcknowledgedEvent;
    case ZnsWebhookEventType.USER_RECEIVED_MESSAGE:
      return bodyObject as ZnsUserReceivedMessageEvent;
    case ZnsWebhookEventType.CHANGE_TEMPLATE_STATUS:
      return bodyObject as ZnsChangeTemplateStatusEvent;
    default:
      return null;
  }
}

/**
 * Xử lý sự kiện người dùng phản hồi template đánh giá dịch vụ
 * @param event Sự kiện webhook
 * @param handler Hàm xử lý
 */
export function handleUserFeedbackEvent(
  event: ZnsWebhookEventUnion,
  handler: (event: ZnsUserFeedbackEvent) => Promise<void> | void
): void {
  if (event.event_name === ZnsWebhookEventType.USER_FEEDBACK) {
    handler(event as ZnsUserFeedbackEvent);
  }
}

/**
 * Xử lý sự kiện thay đổi về hạn mức gửi ZNS
 * @param event Sự kiện webhook
 * @param handler Hàm xử lý
 */
export function handleChangeOaDailyQuotaEvent(
  event: ZnsWebhookEventUnion,
  handler: (event: ZnsChangeOaDailyQuotaEvent) => Promise<void> | void
): void {
  if (event.event_name === ZnsWebhookEventType.CHANGE_OA_DAILY_QUOTA) {
    handler(event as ZnsChangeOaDailyQuotaEvent);
  }
}

/**
 * Xử lý sự kiện thay đổi về loại nội dung ZNS có thể gửi (v1)
 * @param event Sự kiện webhook
 * @param handler Hàm xử lý
 */
export function handleChangeOaTemplateTagsEvent(
  event: ZnsWebhookEventUnion,
  handler: (event: ZnsChangeOaTemplateTagsEvent) => Promise<void> | void
): void {
  if (event.event_name === ZnsWebhookEventType.CHANGE_OA_TEMPLATE_TAGS) {
    handler(event as ZnsChangeOaTemplateTagsEvent);
  }
}

/**
 * Xử lý sự kiện thay đổi về loại nội dung ZNS có thể gửi (v2)
 * @param event Sự kiện webhook
 * @param handler Hàm xử lý
 */
export function handleChangeOaTemplateTagsV2Event(
  event: ZnsWebhookEventUnion,
  handler: (event: ZnsChangeOaTemplateTagsV2Event) => Promise<void> | void
): void {
  if (event.event_name === ZnsWebhookEventType.CHANGE_OA_TEMPLATE_TAGS_V2) {
    handler(event as ZnsChangeOaTemplateTagsV2Event);
  }
}

/**
 * Xử lý sự kiện thay đổi về chất lượng gửi của mẫu tin ZNS
 * @param event Sự kiện webhook
 * @param handler Hàm xử lý
 */
export function handleChangeTemplateQualityEvent(
  event: ZnsWebhookEventUnion,
  handler: (event: ZnsChangeTemplateQualityEvent) => Promise<void> | void
): void {
  if (event.event_name === ZnsWebhookEventType.CHANGE_TEMPLATE_QUALITY) {
    handler(event as ZnsChangeTemplateQualityEvent);
  }
}

/**
 * Xử lý sự kiện journey hết hạn
 * @param event Sự kiện webhook
 * @param handler Hàm xử lý
 */
export function handleJourneyTimeOutEvent(
  event: ZnsWebhookEventUnion,
  handler: (event: ZnsJourneyTimeOutEvent) => Promise<void> | void
): void {
  if (event.event_name === ZnsWebhookEventType.EVENT_JOURNEY_TIME_OUT) {
    handler(event as ZnsJourneyTimeOutEvent);
  }
}

/**
 * Xử lý sự kiện journey được tính phí
 * @param event Sự kiện webhook
 * @param handler Hàm xử lý
 */
export function handleJourneyAcknowledgedEvent(
  event: ZnsWebhookEventUnion,
  handler: (event: ZnsJourneyAcknowledgedEvent) => Promise<void> | void
): void {
  if (event.event_name === ZnsWebhookEventType.EVENT_JOURNEY_ACKNOWLEDGED) {
    handler(event as ZnsJourneyAcknowledgedEvent);
  }
}

/**
 * Xử lý sự kiện người dùng nhận thông báo ZNS
 * @param event Sự kiện webhook
 * @param handler Hàm xử lý
 */
export function handleUserReceivedMessageEvent(
  event: ZnsWebhookEventUnion,
  handler: (event: ZnsUserReceivedMessageEvent) => Promise<void> | void
): void {
  if (event.event_name === ZnsWebhookEventType.USER_RECEIVED_MESSAGE) {
    handler(event as ZnsUserReceivedMessageEvent);
  }
}

/**
 * Xử lý sự kiện thay đổi trạng thái Template ZNS
 * @param event Sự kiện webhook
 * @param handler Hàm xử lý
 */
export function handleChangeTemplateStatusEvent(
  event: ZnsWebhookEventUnion,
  handler: (event: ZnsChangeTemplateStatusEvent) => Promise<void> | void
): void {
  if (event.event_name === ZnsWebhookEventType.CHANGE_TEMPLATE_STATUS) {
    handler(event as ZnsChangeTemplateStatusEvent);
  }
}
