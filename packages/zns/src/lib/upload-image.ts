import { createClient } from '@supabase/supabase-js';

import axios from 'axios';
import FormData from 'form-data';

import { Database } from '@kit/supabase/database';

import { getValidZnsToken, isBrowser } from './utils';

/**
 * Interface cho response khi upload ảnh
 */
export interface UploadZnsImageResponse {
  media_id: string;
}

/**
 * Upload ảnh cho template ZNS
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param file File ảnh cần upload (Buffer hoặc ReadStream)
 * @param filename Tên file (tùy chọn)
 * @returns Thông tin media_id của ảnh đã upload
 */
export async function uploadZnsImage(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  file: Buffer | NodeJS.ReadableStream,
  filename?: string,
): Promise<UploadZnsImageResponse> {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken, oaConfig } = await getValidZnsToken(
    supabase,
    oaConfigId,
  );

  try {
    let response;

    // Xử lý khác nhau giữa môi trường browser và Node.js
    if (isBrowser()) {
      // Browser environment - use our server-side proxy API
      const formData = new window.FormData();
      formData.append('file', new Blob([file]), filename || 'image.png');
      formData.append('oaConfigId', oaConfigId);

      // Gọi API proxy của chúng ta
      response = await axios.post('/api/zns/upload-image', formData);
    } else {
      // Node.js environment - call Zalo API directly
      const formData = new FormData();
      formData.append('file', file, filename || 'image.png');
      const headers = {
        access_token: accessToken,
        ...formData.getHeaders(),
      };

      // Gọi API upload ảnh trực tiếp
      response = await axios.post(
        'https://business.openapi.zalo.me/upload/image',
        formData,
        { headers },
      );
    }

    // Kiểm tra kết quả
    if (response.data.error !== 0) {
      throw new Error(`Failed to upload ZNS image: ${response.data.message}`);
    }

    // Trả về thông tin media_id
    return response.data.data;
  } catch (error: any) {
    // Xử lý lỗi
    if (error.response) {
      throw new Error(
        `Failed to upload ZNS image: ${error.response.data.message || error.message}`,
      );
    }
    throw error;
  }
}

/**
 * Upload ảnh cho template ZNS từ URL
 * @param supabase Supabase client
 * @param oaConfigId ID của OA configuration
 * @param imageUrl URL của ảnh cần upload
 * @returns Thông tin media_id của ảnh đã upload
 */
export async function uploadZnsImageFromUrl(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  imageUrl: string,
): Promise<UploadZnsImageResponse> {
  try {
    // Xử lý khác nhau giữa môi trường browser và Node.js
    if (isBrowser()) {
      // Browser environment - use our server-side proxy API
      const response = await axios.post('/api/zns/upload-image', {
        imageUrl,
        oaConfigId,
        type: 'url'
      });

      // Kiểm tra kết quả
      if (response.data.error !== 0) {
        throw new Error(`Failed to upload ZNS image: ${response.data.message}`);
      }

      // Trả về thông tin media_id
      return response.data.data;
    } else {
      // Node.js environment - process directly
      // Xử lý base64 data URL
      if (imageUrl.startsWith('data:')) {
        // Extract base64 data from data URL
        const matches = imageUrl.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);

        if (!matches || matches.length !== 3) {
          throw new Error('Invalid data URL format');
        }

        const mimeType = matches[1];
        const base64Data = matches[2];
        const extension = mimeType.split('/')[1] || 'png';
        const filename = `image.${extension}`;

        // Convert base64 to buffer
        const buffer = Buffer.from(base64Data, 'base64');

        // Upload image
        return uploadZnsImage(supabase, oaConfigId, buffer, filename);
      } else {
        // Tải ảnh từ URL
        const imageResponse = await axios.get(imageUrl, {
          responseType: 'arraybuffer',
        });

        // Lấy tên file từ URL
        const urlParts = imageUrl.split('/');
        const filename = urlParts[urlParts.length - 1];

        // Upload ảnh
        return uploadZnsImage(
          supabase,
          oaConfigId,
          Buffer.from(imageResponse.data),
          filename,
        );
      }
    }
  } catch (error: any) {
    throw new Error(`Failed to upload ZNS image from URL: ${error.message}`);
  }
}
