/**
 * ZNS Configuration - Đ<PERSON><PERSON> nghĩa các key mặc định cho logic gửi tin
 * Các key này sẽ được tự động map từ dữ liệu sự kiện
 */

export interface ZnsDataPath {
  key: string;
  path: string;
  description: string;
  example: string;
  category: 'customer' | 'order' | 'payment' | 'system' | 'product';
  paramType: string; // ZNS param type (1-15)
}

/**
 * <PERSON>h sách các key mặc định được hỗ trợ bởi logic gửi ZNS
 * Khi user sử dụng các key này trong template, sẽ được tự động map
 */
export const ZNS_DEFAULT_KEYS: ZnsDataPath[] = [
  // Customer data
  {
    key: 'customer_name',
    path: 'customer.name',
    description: 'Tên khách hàng',
    example: 'Nguyễn Văn A',
    category: 'customer',
    paramType: '1', // CUSTOMER_NAME
  },
  {
    key: 'customer_phone',
    path: 'customer.phone',
    description: '<PERSON><PERSON> điện thoại khách hàng',
    example: '0901234567',
    category: 'customer',
    paramType: '2', // PHONE_NUMBER
  },
  {
    key: 'customer_email',
    path: 'customer.email',
    description: 'Email khách hàng',
    example: '<EMAIL>',
    category: 'customer',
    paramType: '3', // EMAIL
  },
  {
    key: 'customer_address',
    path: 'customer.address',
    description: 'Địa chỉ khách hàng',
    example: '123 Đường ABC, Quận 1, TP.HCM',
    category: 'customer',
    paramType: '11', // ADDRESS
  },

  // Order data
  {
    key: 'order_id',
    path: 'order.id',
    description: 'Mã đơn hàng',
    example: 'ORD-2024-001',
    category: 'order',
    paramType: '4', // ORDER_CODE
  },
  {
    key: 'order_code',
    path: 'order.code',
    description: 'Mã đơn hàng (code)',
    example: 'DH001234',
    category: 'order',
    paramType: '4', // ORDER_CODE
  },
  {
    key: 'order_total',
    path: 'order.total',
    description: 'Tổng tiền đơn hàng',
    example: '500,000 VNĐ',
    category: 'order',
    paramType: '5', // PRICE
  },
  {
    key: 'order_date',
    path: 'order.created_at',
    description: 'Ngày đặt hàng',
    example: '15/12/2024',
    category: 'order',
    paramType: '6', // DATE
  },
  {
    key: 'delivery_date',
    path: 'order.delivery_date',
    description: 'Ngày giao hàng dự kiến',
    example: '20/12/2024',
    category: 'order',
    paramType: '6', // DATE
  },
  {
    key: 'order_status',
    path: 'order.status',
    description: 'Trạng thái đơn hàng',
    example: 'Đã xác nhận',
    category: 'order',
    paramType: '13', // CUSTOM_LABEL
  },

  // Payment data
  {
    key: 'payment_method',
    path: 'payment.method',
    description: 'Phương thức thanh toán',
    example: 'Chuyển khoản',
    category: 'payment',
    paramType: '7', // PAYMENT_METHOD
  },
  {
    key: 'payment_status',
    path: 'payment.status',
    description: 'Trạng thái thanh toán',
    example: 'Đã thanh toán',
    category: 'payment',
    paramType: '13', // CUSTOM_LABEL
  },
  {
    key: 'transaction_id',
    path: 'payment.transaction_id',
    description: 'Mã giao dịch',
    example: 'TXN123456789',
    category: 'payment',
    paramType: '8', // TRANSACTION_ID
  },
  {
    key: 'payment_amount',
    path: 'payment.amount',
    description: 'Số tiền thanh toán',
    example: '500,000 VNĐ',
    category: 'payment',
    paramType: '5', // PRICE
  },

  // Product data
  {
    key: 'product_name',
    path: 'product.name',
    description: 'Tên sản phẩm',
    example: 'iPhone 15 Pro',
    category: 'product',
    paramType: '13', // CUSTOM_LABEL
  },
  {
    key: 'product_code',
    path: 'product.code',
    description: 'Mã sản phẩm',
    example: 'IP15P-256GB',
    category: 'product',
    paramType: '13', // CUSTOM_LABEL
  },
  {
    key: 'product_price',
    path: 'product.price',
    description: 'Giá sản phẩm',
    example: '25,000,000 VNĐ',
    category: 'product',
    paramType: '5', // PRICE
  },

  // System data
  {
    key: 'company_name',
    path: 'system.company_name',
    description: 'Tên công ty',
    example: 'Công ty ABC',
    category: 'system',
    paramType: '9', // COMPANY_NAME
  },
  {
    key: 'company_phone',
    path: 'system.company_phone',
    description: 'Số điện thoại công ty',
    example: '1900-1234',
    category: 'system',
    paramType: '2', // PHONE_NUMBER
  },
  {
    key: 'company_email',
    path: 'system.company_email',
    description: 'Email công ty',
    example: '<EMAIL>',
    category: 'system',
    paramType: '3', // EMAIL
  },
  {
    key: 'company_address',
    path: 'system.company_address',
    description: 'Địa chỉ công ty',
    example: '456 Đường XYZ, Quận 3, TP.HCM',
    category: 'system',
    paramType: '11', // ADDRESS
  },
  {
    key: 'website',
    path: 'system.website',
    description: 'Website công ty',
    example: 'https://company.com',
    category: 'system',
    paramType: '10', // WEBSITE
  },
];

/**
 * Lấy config cho một key cụ thể
 */
export function getZnsKeyConfig(key: string): ZnsDataPath | null {
  return ZNS_DEFAULT_KEYS.find(config => config.key === key) || null;
}

/**
 * Kiểm tra xem key có phải là key mặc định không
 */
export function isDefaultKey(key: string): boolean {
  return ZNS_DEFAULT_KEYS.some(config => config.key === key);
}

/**
 * Lấy danh sách key theo category
 */
export function getKeysByCategory(category: string): ZnsDataPath[] {
  return ZNS_DEFAULT_KEYS.filter(config => config.category === category);
}

/**
 * Tạo auto mapping cho các key mặc định
 */
export function generateAutoMapping(templateParams: any[]): Record<string, string> {
  const mapping: Record<string, string> = {};

  templateParams.forEach(param => {
    const config = getZnsKeyConfig(param.name);
    if (config) {
      mapping[param.name] = config.path;
    }
  });

  return mapping;
}

/**
 * Phân loại parameters thành auto và manual
 */
export function categorizeTemplateParams(templateParams: any[]): {
  autoParams: Array<{ param: any; config: ZnsDataPath }>;
  manualParams: any[];
} {
  const autoParams: Array<{ param: any; config: ZnsDataPath }> = [];
  const manualParams: any[] = [];

  templateParams.forEach(param => {
    const config = getZnsKeyConfig(param.name);
    if (config) {
      autoParams.push({ param, config });
    } else {
      manualParams.push(param);
    }
  });

  return { autoParams, manualParams };
}

/**
 * Validate parameter mapping
 */
export function validateParameterMapping(
  templateParams: any[],
  parameterMapping: Record<string, string>
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const { manualParams } = categorizeTemplateParams(templateParams);

  // Chỉ validate manual parameters
  manualParams.forEach(param => {
    if (!parameterMapping[param.name] || parameterMapping[param.name].trim() === '') {
      errors.push(`Tham số "${param.name}" cần được cấu hình`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Event types cho ZNS templates
 */
export interface ZnsEventType {
  id: string;
  name: string;
  description: string;
  icon: string;
  categories: string[]; // Các category key liên quan
  suggestedKeys: string[]; // Key được khuyến nghị cho event type này
  example: string;
  recipientPath: string; // Đường dẫn mặc định đến số điện thoại người nhận
}

export const ZNS_EVENT_TYPES: ZnsEventType[] = [
  {
    id: 'order_confirmation',
    name: 'Xác nhận đơn hàng',
    description: 'Thông báo xác nhận đơn hàng cho khách hàng',
    icon: '📦',
    categories: ['customer', 'order', 'system'],
    suggestedKeys: ['customer_name', 'customer_phone', 'order_id', 'order_total', 'order_date', 'company_name'],
    example: 'Xin chào <customer_name>, đơn hàng <order_id> của bạn đã được xác nhận với tổng giá trị <order_total>.',
    recipientPath: 'customer.phone',
  },
  {
    id: 'payment_confirmation',
    name: 'Xác nhận thanh toán',
    description: 'Thông báo xác nhận thanh toán thành công',
    icon: '💳',
    categories: ['customer', 'payment', 'order', 'system'],
    suggestedKeys: ['customer_name', 'payment_amount', 'payment_method', 'transaction_id', 'order_id', 'company_name'],
    example: 'Thanh toán <payment_amount> cho đơn hàng <order_id> đã thành công qua <payment_method>.',
    recipientPath: 'customer.phone',
  },
  {
    id: 'shipping_notification',
    name: 'Thông báo giao hàng',
    description: 'Thông báo về tình trạng giao hàng',
    icon: '🚚',
    categories: ['customer', 'order', 'system'],
    suggestedKeys: ['customer_name', 'order_id', 'delivery_date', 'customer_address', 'company_phone'],
    example: 'Đơn hàng <order_id> sẽ được giao đến <customer_address> vào ngày <delivery_date>.',
    recipientPath: 'customer.phone',
  },
  {
    id: 'product_promotion',
    name: 'Khuyến mãi sản phẩm',
    description: 'Thông báo khuyến mãi và ưu đãi sản phẩm',
    icon: '🎉',
    categories: ['customer', 'product', 'system'],
    suggestedKeys: ['customer_name', 'product_name', 'product_price', 'company_name', 'website'],
    example: 'Khuyến mãi đặc biệt cho <product_name> chỉ còn <product_price>. Truy cập <website> để đặt hàng.',
    recipientPath: 'customer.phone',
  },
  {
    id: 'customer_support',
    name: 'Hỗ trợ khách hàng',
    description: 'Thông báo hỗ trợ và chăm sóc khách hàng',
    icon: '🎧',
    categories: ['customer', 'system'],
    suggestedKeys: ['customer_name', 'company_name', 'company_phone', 'company_email', 'website'],
    example: 'Xin chào <customer_name>, liên hệ <company_phone> hoặc <company_email> để được hỗ trợ.',
    recipientPath: 'customer.phone',
  },
  {
    id: 'appointment_reminder',
    name: 'Nhắc hẹn',
    description: 'Thông báo nhắc nhở cuộc hẹn hoặc lịch trình',
    icon: '📅',
    categories: ['customer', 'system'],
    suggestedKeys: ['customer_name', 'order_date', 'company_name', 'company_address', 'company_phone'],
    example: 'Nhắc nhở: Bạn có lịch hẹn vào <order_date> tại <company_address>.',
    recipientPath: 'customer.phone',
  },
  {
    id: 'general_notification',
    name: 'Thông báo chung',
    description: 'Thông báo chung cho nhiều mục đích khác nhau',
    icon: '📢',
    categories: ['customer', 'system'],
    suggestedKeys: ['customer_name', 'company_name', 'company_phone', 'website'],
    example: 'Thông báo từ <company_name>: Nội dung thông báo tùy chỉnh.',
    recipientPath: 'customer.phone',
  },
];

/**
 * Lấy event type theo ID
 */
export function getEventType(id: string): ZnsEventType | null {
  return ZNS_EVENT_TYPES.find(type => type.id === id) || null;
}

/**
 * Lấy key suggestions cho event type
 */
export function getKeySuggestionsForEventType(eventTypeId: string): ZnsDataPath[] {
  const eventType = getEventType(eventTypeId);
  if (!eventType) return [];

  return eventType.suggestedKeys
    .map(keyName => ZNS_DEFAULT_KEYS.find(key => key.key === keyName))
    .filter((key): key is ZnsDataPath => key !== undefined);
}

/**
 * Lấy recipient path mặc định cho event type
 */
export function getRecipientPathForEventType(eventTypeId: string): string {
  const eventType = getEventType(eventTypeId);
  return eventType?.recipientPath || 'customer.phone'; // Default fallback
}

/**
 * Tạo chú thích cho template creation
 */
export function generateKeyDocumentation(): string {
  const categories = ['customer', 'order', 'payment', 'product', 'system'];

  let doc = '# Danh sách key mặc định được hỗ trợ\n\n';
  doc += 'Sử dụng các key sau để tự động lấy dữ liệu từ hệ thống:\n\n';

  categories.forEach(category => {
    const keys = getKeysByCategory(category);
    if (keys.length > 0) {
      doc += `## ${category.toUpperCase()}\n`;
      keys.forEach(key => {
        doc += `- **${key.key}**: ${key.description} (ví dụ: ${key.example})\n`;
      });
      doc += '\n';
    }
  });

  doc += '## Lưu ý\n';
  doc += '- Các key trên sẽ được tự động map khi tạo mapping\n';
  doc += '- Key khác sẽ cần cấu hình thủ công\n';
  doc += '- Khuyến khích sử dụng key mặc định để đảm bảo tính nhất quán\n';

  return doc;
}
