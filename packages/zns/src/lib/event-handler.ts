import { createClient } from '@supabase/supabase-js';
import { znsEventBus, ZnsEventData } from './event-bus';
import { sendZnsMessage } from './send-zns-message';
import { Database } from '@kit/supabase/database';

/**
 * <PERSON><PERSON><PERSON> gi<PERSON> điều kiện
 * @param conditions Đi<PERSON><PERSON> kiện
 * @param data Dữ liệu
 * @returns boolean
 */
export function evaluateConditions(conditions: any, data: any): boolean {
  if (!conditions) return true;

  try {
    // Điều kiện đơn giản
    if (conditions.operator === 'equals') {
      const value = getValueByPath(data, conditions.field);
      return value === conditions.value;
    }

    if (conditions.operator === 'not_equals') {
      const value = getValueByPath(data, conditions.field);
      return value !== conditions.value;
    }

    if (conditions.operator === 'greater_than') {
      const value = getValueByPath(data, conditions.field);
      return value > conditions.value;
    }

    if (conditions.operator === 'less_than') {
      const value = getValueByPath(data, conditions.field);
      return value < conditions.value;
    }

    if (conditions.operator === 'contains') {
      const value = getValueByPath(data, conditions.field);
      return value?.includes(conditions.value);
    }

    // Điều kiện AND
    if (conditions.operator === 'and' && Array.isArray(conditions.conditions)) {
      return conditions.conditions.every((condition: any) =>
        evaluateConditions(condition, data)
      );
    }

    // Điều kiện OR
    if (conditions.operator === 'or' && Array.isArray(conditions.conditions)) {
      return conditions.conditions.some((condition: any) =>
        evaluateConditions(condition, data)
      );
    }

    return true;
  } catch (error) {
    console.error('Error evaluating conditions:', error);
    return false;
  }
}

/**
 * Lấy giá trị từ đường dẫn
 * @param obj Đối tượng
 * @param path Đường dẫn (ví dụ: 'customer.name')
 * @returns any
 */
export function getValueByPath(obj: any, path: string): any {
  if (!obj || !path) return undefined;

  const keys = path.split('.');
  let value = obj;

  for (const key of keys) {
    if (value === null || value === undefined) return undefined;
    value = value[key];
  }

  return value;
}

// Lưu trữ các handler đã khởi tạo
const initializedHandlers = new Set<string>();

/**
 * Khởi tạo ZNS Event Handler
 * @param supabase Supabase client
 * @param handlerId ID duy nhất cho handler (optional)
 * @returns ID của handler đã khởi tạo
 */
export function initZnsEventHandler(
  supabase: ReturnType<typeof createClient<Database>>,
  handlerId?: string
): string {
  // Tạo ID nếu không được cung cấp
  const id = handlerId || `zns_handler_${Date.now()}`;

  // Kiểm tra xem handler đã được khởi tạo chưa
  if (initializedHandlers.has(id)) {
    console.log(`ZNS Event Handler ${id} already initialized`);
    return id;
  }

  // Đăng ký handler mới
  const handler = async (eventName: string, eventData: ZnsEventData) => {
    const { module, event, data, metadata } = eventData;

    console.log(`ZNS Event: ${module}.${event}`, metadata);

    try {
      // Tìm tất cả mapping phù hợp
      const { data: mappings, error } = await supabase
        .from('zns_mappings')
        .select(`
          *,
          template:zns_templates(*)
        `)
        .eq('module', module)
        .eq('event_type', event)
        .eq('enabled', true)
        .eq('account_id', metadata.accountId);

      if (error) {
        console.error('Error fetching ZNS mappings:', error);
        return;
      }

      if (!mappings || mappings.length === 0) {
        console.log(`No ZNS mappings found for ${module}.${event}`);
        return;
      }

      // Xử lý từng mapping
      for (const mapping of mappings) {
        // Kiểm tra điều kiện
        if (evaluateConditions(mapping.conditions, data)) {
          // Áp dụng mapping và gửi thông báo
          await sendZnsWithMapping(supabase, mapping, data, metadata.accountId);
        } else {
          console.log(`Conditions not met for mapping ${mapping.id}`);
        }
      }
    } catch (error) {
      console.error('Error processing ZNS event:', error);
    }
  };

  // Thêm handler vào danh sách đã khởi tạo
  initializedHandlers.add(id);

  // Đăng ký handler với event bus
  znsEventBus.on('*', handler);

  console.log(`ZNS Event Handler ${id} initialized`);

  return id;
}

/**
 * Hủy khởi tạo ZNS Event Handler
 * @param handlerId ID của handler cần hủy
 * @returns boolean - true nếu hủy thành công, false nếu không tìm thấy handler
 */
export function shutdownZnsEventHandler(handlerId: string): boolean {
  if (!initializedHandlers.has(handlerId)) {
    console.log(`ZNS Event Handler ${handlerId} not found`);
    return false;
  }

  // Xóa handler khỏi danh sách đã khởi tạo
  initializedHandlers.delete(handlerId);

  // Lưu ý: Event Emitter trong Node.js không hỗ trợ việc xóa một listener cụ thể
  // Vì vậy chúng ta cần xóa tất cả các listener và khởi tạo lại các listener khác
  // Đây là một giải pháp tạm thời, trong tương lai có thể cần cách tiếp cận tốt hơn

  console.log(`ZNS Event Handler ${handlerId} shutdown`);
  return true;
}

/**
 * Gửi ZNS với mapping
 * @param supabase Supabase client
 * @param mapping Mapping
 * @param data Dữ liệu
 * @param accountId ID của account
 */
async function sendZnsWithMapping(
  supabase: ReturnType<typeof createClient<Database>>,
  mapping: any,
  data: any,
  accountId: string
) {
  // Lấy thông tin template
  const template = mapping.template;

  if (!template || !template.template_id) {
    console.error('Template not found or invalid');
    return;
  }

  // Áp dụng parameter mapping
  const parameters: Record<string, string> = {};
  for (const [paramName, paramPath] of Object.entries(mapping.parameter_mapping)) {
    const value = getValueByPath(data, paramPath as string);
    if (value !== undefined) {
      parameters[paramName] = String(value);
    }
  }

  // Lấy số điện thoại người nhận
  const recipientPath = mapping.recipient_path || 'customer.phone';
  const recipient = getValueByPath(data, recipientPath);

  if (!recipient) {
    console.error('Recipient phone number not found');
    return;
  }

  // Gửi thông báo ZNS
  try {
    const result = await sendZnsMessage({
      supabase,
      templateId: template.template_id,
      recipient,
      parameters,
      oaConfigId: template.oa_config_id,
      trackingId: `${mapping.id}_${Date.now()}`
    });

    // Lưu log
    await supabase.from('zns_usage').insert({
      template_id: template.id,
      mapping_id: mapping.id,
      recipient,
      message_id: result.message_id,
      status: 'success',
      metadata: {
        parameters,
        source_data: data,
        source_event: `${mapping.module}.${mapping.event_type}`
      },
      account_id: accountId,
      oa_config_id: template.oa_config_id,
      oa_type: 'official', // Mặc định
      event_type: mapping.event_type,
      sent_at: new Date().toISOString()
    });

    console.log(`ZNS sent successfully for mapping ${mapping.id}`);
    return result;
  } catch (error: any) {
    console.error(`Error sending ZNS for mapping ${mapping.id}:`, error);

    // Lưu log lỗi
    await supabase.from('zns_usage').insert({
      template_id: template.id,
      mapping_id: mapping.id,
      recipient,
      status: 'failed',
      error_message: error.message,
      metadata: {
        parameters,
        source_data: data,
        source_event: `${mapping.module}.${mapping.event_type}`,
        error: error.toString()
      },
      account_id: accountId,
      oa_config_id: template.oa_config_id,
      oa_type: 'official', // Mặc định
      event_type: mapping.event_type,
      sent_at: new Date().toISOString()
    });

    throw error;
  }
}
