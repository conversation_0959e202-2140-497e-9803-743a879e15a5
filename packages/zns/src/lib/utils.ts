import { createClient } from '@supabase/supabase-js';

import { Database } from '@kit/supabase/database';

import { refreshAccessToken } from './oauth-utils';

/**
 * Check if the code is running in a browser environment
 */
export const isBrowser = () => typeof window !== 'undefined';

/**
 * Interface for OA configuration
 */
export interface OaConfig {
  id: string;
  app_id: string;
  secret_key: string;
  access_token: string;
  refresh_token: string;
  token_expires_at: string;
  account_id?: string;
  oa_metadata?: any;
  [key: string]: any;
}

/**
 * Interface for the result of getValidZnsToken
 */
export interface ZnsTokenResult {
  accessToken: string;
  oaConfig: OaConfig;
}

/**
 * Get a valid ZNS token, refreshing if necessary
 * @param supabase Supabase client
 * @param oaConfigId ID of the OA configuration
 * @returns Object containing the valid access token and OA configuration
 */
export async function getValidZnsToken(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
): Promise<ZnsTokenResult> {
  // Fetch OA configuration
  const { data: oaConfig, error } = await supabase
    .from('oa_configurations')
    .select('*')
    .eq('id', oaConfigId)
    .single();

  if (error || !oaConfig) {
    throw new Error(`Failed to fetch OA config: ${error?.message}`);
  }

  // Check if token is expired
  let accessToken = oaConfig.access_token;
  if (new Date(oaConfig.token_expires_at) < new Date()) {
    // Token is expired, refresh it
    accessToken = await refreshZnsToken(supabase, oaConfig);
  }

  return { accessToken, oaConfig: oaConfig as OaConfig };
}

/**
 * Refresh ZNS token
 * @param supabase Supabase client
 * @param oaConfig OA configuration
 * @returns New access token
 */
export async function refreshZnsToken(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfig: OaConfig,
) {
  try {
    if (!oaConfig.refresh_token) {
      return null;
    }
    // Sử dụng hàm refreshAccessToken từ oauth-utils.ts
    const { accessToken, refreshToken, expiresIn } = await refreshAccessToken(
      oaConfig.app_id,
      oaConfig.secret_key,
      oaConfig.refresh_token,
    );

    const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString();

    // Cập nhật token và metadata
    await supabase
      .from('oa_configurations')
      .update({
        access_token: accessToken,
        refresh_token: refreshToken,
        token_expires_at: expiresAt,
        oa_metadata: {
          ...oaConfig.oa_metadata,
          last_refreshed: new Date().toISOString(),
          token_info: {
            ...oaConfig.oa_metadata?.token_info,
            expires_in: expiresIn,
            refreshed_at: new Date().toISOString(),
            expires_at: expiresAt,
          },
        },
      })
      .eq('id', oaConfig.id);

    // Cập nhật trạng thái integration
    if (oaConfig.account_id) {
      await supabase
        .from('integrations')
        .update({
          status: 'connected',
        })
        .eq('account_id', oaConfig.account_id)
        .eq('type', 'zalo');
    }

    return accessToken;
  } catch (error: any) {
    console.warn('Failed to refresh Zalo OA token:', error.message);

    // Đánh dấu token hết hạn
    await supabase
      .from('oa_configurations')
      .update({
        token_expires_at: new Date(0).toISOString(), // Đặt ngày hết hạn về quá khứ
        oa_metadata: {
          ...oaConfig.oa_metadata,
          refresh_error: error.message,
          refresh_error_time: new Date().toISOString(),
        },
      })
      .eq('id', oaConfig.id);

    throw error;
  }
}
