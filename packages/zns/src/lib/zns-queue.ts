import { createClient } from '@supabase/supabase-js';

import { Queue, Worker } from 'bullmq';

import { Database } from '@kit/supabase/database';

import { ZnsEventType, ZnsNotificationParams } from '../types';
import { sendZns } from './send-zns';

interface JobData {
  accountId: string;
  eventType: ZnsEventType;
  params: ZnsNotificationParams;
}

const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';

export const znsQueue = new Queue<JobData>('zns-queue', {
  connection: { url: REDIS_URL },
});

export async function addZnsJob(
  accountId: string,
  eventType: ZnsEventType,
  params: ZnsNotificationParams,
) {
  await znsQueue.add('send-zns', { accountId, eventType, params });
}

export function initializeZnsWorker(
  supabase: ReturnType<typeof createClient<Database>>,
) {
  const worker = new Worker<JobData>(
    'zns-queue',
    async (job) => {
      const { accountId, eventType, params } = job.data;
      await sendZns(supabase, accountId, eventType, params);
    },
    { connection: { url: REDIS_URL } },
  );

  worker.on('completed', (job) => {
    console.log(`ZNS job ${job.id} completed`);
  });

  worker.on('failed', (job, err) => {
    console.error(`ZNS job ${job.id} failed:`, err);
  });

  return worker;
}
