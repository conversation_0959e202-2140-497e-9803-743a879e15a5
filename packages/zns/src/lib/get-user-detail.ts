import { createClient } from '@supabase/supabase-js';

import axios from 'axios';

import { Database } from '@kit/supabase/database';

import { getValidZnsToken } from './utils';

/**
 * Lấy thông tin người dùng Zalo sử dụng Supabase client và oaConfigId
 *
 * API: https://graph.zalo.me/v2.0/me
 *
 * @param supabase Supabase client
 * @param oaConfigId ID của cấu hình OA
 * @param fields Các trường thông tin muốn lấy (mặc định: id,name,picture)
 * @returns Thông tin người dùng
 */
export async function getZaloUserDetail(
  supabase: ReturnType<typeof createClient<Database>>,
  oaConfigId: string,
  fields: string = 'id,name,picture',
) {
  // Lấy token hợp lệ và thông tin OA configuration
  const { accessToken } = await getValidZnsToken(supabase, oaConfigId);

  return getZaloUserDetailWithToken(accessToken, fields);
}

/**
 * L<PERSON>y thông tin người dùng Zalo sử dụng trực tiếp access token
 *
 * API: https://graph.zalo.me/v2.0/me
 *
 * @param accessToken Access token của Zalo
 * @param fields Các trường thông tin muốn lấy (mặc định: id,name,picture)
 * @returns Thông tin người dùng
 */
export async function getZaloUserDetailWithToken(
  accessToken: string,
  fields: string = 'id,name,picture',
) {
  // Gọi API lấy thông tin người dùng
  try {
    const response = await axios.get(
      `https://graph.zalo.me/v2.0/me?fields=${encodeURIComponent(fields)}`,
      {
        headers: { access_token: accessToken },
      },
    );

    // Kiểm tra lỗi từ API
    if (response.data.error && response.data.error !== 0) {
      // Xử lý lỗi 452: Session key invalid
      if (response.data.error === 452) {
        throw new Error(
          `Zalo API error: Session key (access token) invalid or revoked`,
        );
      } else {
        throw new Error(`Zalo API error: ${response.data.message}`);
      }
    }

    return response.data;
  } catch (error: any) {
    console.error('Failed to get Zalo user detail:', error.message);
    throw error;
  }
}

/**
 * Lấy thông tin người dùng Zalo sử dụng trực tiếp access token
 *
 * API: https://graph.zalo.me/v2.0/me
 *
 * @param accessToken Access token của Zalo
 * @param fields Các trường thông tin muốn lấy (mặc định: id,name,picture)
 * @returns Thông tin người dùng
 */
export async function getZaloOfficialAccount(accessToken: string) {
  // Gọi API lấy thông tin người dùng
  try {
    const response = await axios.get(`https://openapi.zalo.me/v2.0/oa/getoa`, {
      headers: { access_token: accessToken },
    });

    // Kiểm tra lỗi từ API
    if (response.data.error && response.data.error !== 0) {
      // Xử lý lỗi 452: Session key invalid
      if (response.data.error === 452) {
        throw new Error(
          `Zalo API error: Session key (access token) invalid or revoked`,
        );
      } else {
        throw new Error(`Zalo API error: ${response.data.message}`);
      }
    }

    return response.data;
  } catch (error: any) {
    console.error('Failed to get Zalo user detail:', error.message);
    throw error;
  }
}
