{"name": "@kit/resend", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/mailers-shared": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@types/node": "^22.14.0", "zod": "^3.24.2"}, "typesVersions": {"*": {"*": ["src/*"]}}}