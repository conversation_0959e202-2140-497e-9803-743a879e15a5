{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}