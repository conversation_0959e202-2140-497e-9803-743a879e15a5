// ZaloPay Integration Types

export interface ZaloPayConfig {
  appId: string;
  key1: string;
  key2: string;
  endpoint: string;
  callbackUrl: string;
  redirectUrl: string;
  isProduction: boolean;
}

export interface CreateOrderRequest {
  appId: string;
  appUser: string;
  appTime: number;
  amount: number;
  appTransId: string;
  embedData: string;
  item: string;
  description: string;
  bankCode?: string;
  mac: string;
}

export interface CreateOrderResponse {
  returnCode: number;
  returnMessage: string;
  subReturnCode: number;
  subReturnMessage: string;
  zptranstoken?: string;
  orderUrl?: string;
  orderId?: string;
}

export interface QueryOrderRequest {
  appId: string;
  appTransId: string;
  mac: string;
}

export interface QueryOrderResponse {
  returnCode: number;
  returnMessage: string;
  subReturnCode: number;
  subReturnMessage: string;
  isProcessing: boolean;
  amount: number;
  discountAmount: number;
  zptransid: string;
}

export interface ZaloPayCallbackData {
  appId: string;
  appTransId: string;
  appUser: string;
  amount: number;
  appTime: number;
  embedData: string;
  item: string;
  zptransid: string;
  serverTime: number;
  channel: number;
  merchantUserId: string;
  userFeeAmount: number;
  discountAmount: number;
  mac: string;
}

export interface RefundRequest {
  appId: string;
  zptransid: string;
  amount: number;
  description: string;
  timestamp: number;
  mac: string;
}

export interface RefundResponse {
  returnCode: number;
  returnMessage: string;
  subReturnCode: number;
  subReturnMessage: string;
  refundId: string;
}

// Education-specific payment metadata
export interface EducationPaymentMetadata {
  feeId: string;
  learnerId: string;
  learnerName: string;
  organizationId: string;
  organizationName: string;
  guardianId: string;
  guardianName: string;
  feeCategory: string;
  feeCategoryText: string;
  billingPeriod?: string;
  dueDate: string;
  originalAmount: number;
  discountAmount?: number;
  lateFee?: number;
  paymentType: 'education_fee';
}

// ZaloPay Error Codes
export enum ZaloPayReturnCode {
  SUCCESS = 1,
  PROCESSING = 2,
  FAILED = -1,
  INVALID_AMOUNT = -2,
  INVALID_DESCRIPTION = -3,
  INVALID_ITEM = -4,
  INVALID_EMBED_DATA = -5,
  INVALID_BANK_CODE = -6,
  INVALID_MAC = -7,
  INVALID_APP_ID = -8,
  INVALID_APP_TRANS_ID = -9,
  ORDER_NOT_FOUND = -49,
  ORDER_EXPIRED = -53,
  INSUFFICIENT_BALANCE = -117,
  TRANSACTION_LIMIT_EXCEEDED = -118,
}

export const ZaloPayReturnMessages: Record<number, string> = {
  [ZaloPayReturnCode.SUCCESS]: 'Giao dịch thành công',
  [ZaloPayReturnCode.PROCESSING]: 'Giao dịch đang xử lý',
  [ZaloPayReturnCode.FAILED]: 'Giao dịch thất bại',
  [ZaloPayReturnCode.INVALID_AMOUNT]: 'Số tiền không hợp lệ',
  [ZaloPayReturnCode.INVALID_DESCRIPTION]: 'Mô tả không hợp lệ',
  [ZaloPayReturnCode.INVALID_ITEM]: 'Thông tin sản phẩm không hợp lệ',
  [ZaloPayReturnCode.INVALID_EMBED_DATA]: 'Dữ liệu nhúng không hợp lệ',
  [ZaloPayReturnCode.INVALID_BANK_CODE]: 'Mã ngân hàng không hợp lệ',
  [ZaloPayReturnCode.INVALID_MAC]: 'Chữ ký không hợp lệ',
  [ZaloPayReturnCode.INVALID_APP_ID]: 'App ID không hợp lệ',
  [ZaloPayReturnCode.INVALID_APP_TRANS_ID]: 'Mã giao dịch không hợp lệ',
  [ZaloPayReturnCode.ORDER_NOT_FOUND]: 'Không tìm thấy đơn hàng',
  [ZaloPayReturnCode.ORDER_EXPIRED]: 'Đơn hàng đã hết hạn',
  [ZaloPayReturnCode.INSUFFICIENT_BALANCE]: 'Số dư không đủ',
  [ZaloPayReturnCode.TRANSACTION_LIMIT_EXCEEDED]: 'Vượt quá giới hạn giao dịch',
};

// Bank codes for ZaloPay
export const ZaloPayBankCodes = {
  ZALOPAYAPP: 'zalopayapp', // ZaloPay App
  ATM: 'ATM',               // Thẻ ATM
  CC: 'CC',                 // Thẻ tín dụng
  VISA: 'VISA',             // Visa
  MASTERCARD: 'MASTERCARD', // MasterCard
  JCB: 'JCB',               // JCB
} as const;

export type ZaloPayBankCode = typeof ZaloPayBankCodes[keyof typeof ZaloPayBankCodes];

export interface ZaloPayPaymentIntent {
  id: string;
  amount: number;
  currency: string;
  description: string;
  metadata: EducationPaymentMetadata;
  appTransId: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'cancelled';
  paymentUrl?: string;
  zptranstoken?: string;
  zptransid?: string;
  createdAt: string;
  updatedAt: string;
}
