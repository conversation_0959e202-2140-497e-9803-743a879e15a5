import * as crypto from 'crypto';
import { EducationPaymentMetadata } from './types';

// ZaloPay Utility Functions

export function generateAppTransId(appId: string): string {
  const date = new Date();
  const yymmdd = date.toISOString().slice(2, 10).replace(/-/g, '');
  const timestamp = Date.now().toString().slice(-6);
  return `${yymmdd}_${appId}_${timestamp}`;
}

export function generateMac(data: string, key: string): string {
  return crypto.createHmac('sha256', key).update(data).digest('hex');
}

export function createOrderMac(params: {
  appId: string;
  appTransId: string;
  appUser: string;
  amount: number;
  appTime: number;
  embedData: string;
  item: string;
}, key1: string): string {
  const data = `${params.appId}|${params.appTransId}|${params.appUser}|${params.amount}|${params.appTime}|${params.embedData}|${params.item}`;
  return generateMac(data, key1);
}

export function createQueryMac(params: {
  appId: string;
  appTransId: string;
}, key1: string): string {
  const data = `${params.appId}|${params.appTransId}|${key1}`;
  return generateMac(data, key1);
}

export function verifyCallbackMac(callbackData: any, key2: string): boolean {
  const { mac, ...dataWithoutMac } = callbackData;
  
  // ZaloPay callback MAC calculation
  const dataStr = [
    dataWithoutMac.appId,
    dataWithoutMac.appTransId,
    dataWithoutMac.appUser,
    dataWithoutMac.amount,
    dataWithoutMac.appTime,
    dataWithoutMac.embedData,
    dataWithoutMac.item,
    dataWithoutMac.zptransid,
    dataWithoutMac.serverTime,
    dataWithoutMac.channel,
    dataWithoutMac.merchantUserId,
    dataWithoutMac.userFeeAmount,
    dataWithoutMac.discountAmount
  ].join('|');
  
  const expectedMac = generateMac(dataStr, key2);
  return mac === expectedMac;
}

export function formatAmount(amount: number): number {
  // ZaloPay requires amount in VND (integer)
  return Math.round(amount);
}

export function validateAmount(amount: number): boolean {
  // ZaloPay minimum: 1,000 VND, maximum: 500,000,000 VND
  return amount >= 1000 && amount <= 500000000;
}

export function createEmbedData(metadata: EducationPaymentMetadata): string {
  return JSON.stringify(metadata);
}

export function parseEmbedData(embedData: string): EducationPaymentMetadata | null {
  try {
    return JSON.parse(embedData);
  } catch {
    return null;
  }
}

export function createItemDescription(metadata: EducationPaymentMetadata): string {
  return JSON.stringify([{
    name: `${metadata.feeCategoryText} - ${metadata.learnerName}`,
    quantity: 1,
    price: metadata.originalAmount,
    category: metadata.feeCategory,
    period: metadata.billingPeriod,
  }]);
}

export function createPaymentDescription(metadata: EducationPaymentMetadata): string {
  let description = `${metadata.feeCategoryText} - ${metadata.learnerName}`;
  if (metadata.billingPeriod) {
    description += ` (${metadata.billingPeriod})`;
  }
  description += ` - ${metadata.organizationName}`;
  
  // Limit to 255 characters
  if (description.length > 255) {
    description = description.substring(0, 252) + '...';
  }
  
  return description;
}

export function createAppUser(guardianId: string, guardianName: string): string {
  // Create a safe app user identifier
  const sanitizedName = guardianName.replace(/[^a-zA-Z0-9]/g, '');
  const shortId = guardianId.substring(0, 8);
  return `${sanitizedName}_${shortId}`;
}

export function createPaymentUrl(zptranstoken: string, isProduction: boolean = false): string {
  const baseUrl = isProduction 
    ? 'https://zalopay.vn/start'
    : 'https://sbgateway.zalopay.vn/start';
  
  return `${baseUrl}?token=${zptranstoken}`;
}

export function getOrderStatus(returnCode: number): 'pending' | 'processing' | 'succeeded' | 'failed' {
  switch (returnCode) {
    case 1:
      return 'succeeded';
    case 2:
      return 'processing';
    case -1:
    case -49:
    case -53:
      return 'failed';
    default:
      return 'pending';
  }
}

export function formatVietnameseCurrency(amount: number): string {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount);
}

export function calculateTotalAmount(
  baseAmount: number,
  discountAmount: number = 0,
  lateFee: number = 0
): number {
  const total = baseAmount - discountAmount + lateFee;
  return Math.max(total, 0); // Ensure non-negative
}

export function isOrderExpired(appTime: number, expirationMinutes: number = 15): boolean {
  const now = Date.now();
  const orderTime = appTime;
  const expirationTime = orderTime + (expirationMinutes * 60 * 1000);
  
  return now > expirationTime;
}

export function createCallbackResponse(success: boolean, message: string = ''): string {
  return JSON.stringify({
    return_code: success ? 1 : -1,
    return_message: message || (success ? 'success' : 'fail'),
  });
}

export function sanitizeUserInput(input: string): string {
  // Remove special characters that might cause issues
  return input
    .replace(/[<>\"'&]/g, '')
    .trim()
    .substring(0, 255);
}

export function createPaymentReference(feeId: string, learnerId: string): string {
  const timestamp = Date.now().toString(36);
  const hash = crypto.createHash('md5')
    .update(`${feeId}_${learnerId}_${timestamp}`)
    .digest('hex')
    .substring(0, 8);
  
  return `EDU_${hash}_${timestamp}`.toUpperCase();
}

export function extractTransactionInfo(appTransId: string): {
  date: string;
  appId: string;
  sequence: string;
} | null {
  const match = appTransId.match(/^(\d{6})_(\d+)_(\d{6})$/);
  if (!match) return null;
  
  const [, date, appId, sequence] = match;
  return { date, appId, sequence };
}

export function createReceiptData(
  paymentData: any, 
  metadata: EducationPaymentMetadata
): Record<string, any> {
  return {
    transactionId: paymentData.zptransid,
    appTransId: paymentData.appTransId,
    amount: paymentData.amount,
    discountAmount: paymentData.discountAmount || 0,
    finalAmount: paymentData.amount - (paymentData.discountAmount || 0),
    paymentTime: new Date().toISOString(),
    paymentMethod: 'ZaloPay',
    
    // Education specific
    feeId: metadata.feeId,
    learnerName: metadata.learnerName,
    feeCategory: metadata.feeCategoryText,
    billingPeriod: metadata.billingPeriod,
    organizationName: metadata.organizationName,
    guardianName: metadata.guardianName,
    
    // Receipt formatting
    formattedAmount: formatVietnameseCurrency(paymentData.amount),
    formattedDate: new Date().toLocaleDateString('vi-VN'),
    formattedTime: new Date().toLocaleTimeString('vi-VN'),
  };
}

export function validateZaloPayConfig(config: any): boolean {
  const required = ['appId', 'key1', 'key2', 'endpoint', 'callbackUrl'];
  return required.every(field => config[field] && typeof config[field] === 'string');
}

export function maskSensitiveData(data: string): string {
  if (data.length <= 8) {
    return '*'.repeat(data.length);
  }
  return data.substring(0, 4) + '*'.repeat(data.length - 8) + data.substring(data.length - 4);
}

export function generateSecureRandomString(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

export function createWebhookSignature(payload: string, secret: string): string {
  return crypto.createHmac('sha256', secret).update(payload).digest('hex');
}

export function verifyWebhookSignature(
  payload: string, 
  signature: string, 
  secret: string
): boolean {
  const expectedSignature = createWebhookSignature(payload, secret);
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
}
