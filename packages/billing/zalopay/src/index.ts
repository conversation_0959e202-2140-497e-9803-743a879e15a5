// ZaloPay Billing Provider
export * from './types';
export * from './utils';
export { ZaloPayProvider } from './zalopay-provider';

// Re-export commonly used functions
export {
  generateAppTransId,
  createOrderMac,
  verifyCallbackMac,
  formatAmount,
  validateAmount,
  createEmbedData,
  parseEmbedData,
  createPaymentDescription,
  createPaymentUrl,
  getOrderStatus,
  calculateTotalAmount,
  formatVietnameseCurrency,
  createCallbackResponse,
  createPaymentReference,
  createReceiptData,
} from './utils';

// Configuration helper
export function createZaloPayConfig(config: {
  appId: string;
  key1: string;
  key2: string;
  callbackUrl: string;
  redirectUrl?: string;
  isProduction?: boolean;
}): import('./types').ZaloPayConfig {
  return {
    appId: config.appId,
    key1: config.key1,
    key2: config.key2,
    endpoint: config.isProduction 
      ? 'https://openapi.zalopay.vn'
      : 'https://sb-openapi.zalopay.vn',
    callbackUrl: config.callbackUrl,
    redirectUrl: config.redirectUrl || config.callbackUrl,
    isProduction: config.isProduction || false,
  };
}
