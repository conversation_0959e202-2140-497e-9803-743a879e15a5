import axios from 'axios';
import { 
  ZaloPayConfig, 
  CreateOrderRequest, 
  CreateOrderResponse,
  QueryOrderRequest,
  QueryOrderResponse,
  ZaloPayCallbackData,
  EducationPaymentMetadata,
  ZaloPayPaymentIntent,
  ZaloPayReturnCode
} from './types';
import {
  generateAppTransId,
  createOrderMac,
  createQueryMac,
  verifyCallbackMac,
  formatAmount,
  validateAmount,
  createEmbedData,
  createItemDescription,
  createPaymentDescription,
  createAppUser,
  createPaymentUrl,
  getOrderStatus,
  calculateTotalAmount,
  createCallbackResponse,
  sanitizeUserInput
} from './utils';

export class ZaloPayProvider {
  private config: ZaloPayConfig;

  constructor(config: ZaloPayConfig) {
    this.config = config;
  }

  async createPaymentIntent(
    metadata: EducationPaymentMetadata,
    returnUrl?: string
  ): Promise<ZaloPayPaymentIntent> {
    try {
      // Validate amount
      const totalAmount = calculateTotalAmount(
        metadata.originalAmount,
        metadata.discountAmount,
        metadata.lateFee
      );

      if (!validateAmount(totalAmount)) {
        throw new Error('Invalid payment amount');
      }

      // Generate transaction ID
      const appTransId = generateAppTransId(this.config.appId);
      const appTime = Date.now();

      // Create order data
      const embedData = createEmbedData(metadata);
      const item = createItemDescription(metadata);
      const description = createPaymentDescription(metadata);
      const appUser = createAppUser(metadata.guardianId, metadata.guardianName);

      // Create MAC
      const orderParams = {
        appId: this.config.appId,
        appTransId,
        appUser: sanitizeUserInput(appUser),
        amount: formatAmount(totalAmount),
        appTime,
        embedData,
        item,
      };

      const mac = createOrderMac(orderParams, this.config.key1);

      // Prepare request
      const orderRequest: CreateOrderRequest = {
        ...orderParams,
        description: sanitizeUserInput(description),
        mac,
      };

      // Call ZaloPay API
      const response = await axios.post<CreateOrderResponse>(
        `${this.config.endpoint}/v2/create`,
        orderRequest,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }
      );

      const { data } = response;

      if (data.returnCode !== ZaloPayReturnCode.SUCCESS) {
        throw new Error(`ZaloPay API Error: ${data.returnMessage}`);
      }

      // Create payment intent
      const paymentIntent: ZaloPayPaymentIntent = {
        id: appTransId,
        amount: totalAmount,
        currency: 'VND',
        description,
        metadata,
        appTransId,
        status: 'pending',
        paymentUrl: data.zptranstoken ? createPaymentUrl(data.zptranstoken, this.config.isProduction) : undefined,
        zptranstoken: data.zptranstoken,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return paymentIntent;

    } catch (error: any) {
      throw new Error(`Failed to create payment intent: ${error.message}`);
    }
  }

  async queryPaymentStatus(appTransId: string): Promise<{
    status: 'pending' | 'processing' | 'succeeded' | 'failed';
    amount?: number;
    zptransid?: string;
    discountAmount?: number;
  }> {
    try {
      const mac = createQueryMac({
        appId: this.config.appId,
        appTransId,
      }, this.config.key1);

      const queryRequest: QueryOrderRequest = {
        appId: this.config.appId,
        appTransId,
        mac,
      };

      const response = await axios.post<QueryOrderResponse>(
        `${this.config.endpoint}/v2/query`,
        queryRequest,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }
      );

      const { data } = response;
      const status = getOrderStatus(data.returnCode);

      return {
        status,
        amount: data.amount,
        zptransid: data.zptransid,
        discountAmount: data.discountAmount,
      };

    } catch (error: any) {
      throw new Error(`Failed to query payment status: ${error.message}`);
    }
  }

  verifyCallback(callbackData: ZaloPayCallbackData): boolean {
    return verifyCallbackMac(callbackData, this.config.key2);
  }

  processCallback(callbackData: ZaloPayCallbackData): {
    isValid: boolean;
    appTransId: string;
    zptransid: string;
    amount: number;
    metadata: EducationPaymentMetadata | null;
    response: string;
  } {
    const isValid = this.verifyCallback(callbackData);
    
    if (!isValid) {
      return {
        isValid: false,
        appTransId: callbackData.appTransId,
        zptransid: callbackData.zptransid,
        amount: callbackData.amount,
        metadata: null,
        response: createCallbackResponse(false, 'Invalid MAC'),
      };
    }

    // Parse metadata from embedData
    let metadata: EducationPaymentMetadata | null = null;
    try {
      metadata = JSON.parse(callbackData.embedData);
    } catch (error) {
      console.error('Failed to parse callback metadata:', error);
    }

    return {
      isValid: true,
      appTransId: callbackData.appTransId,
      zptransid: callbackData.zptransid,
      amount: callbackData.amount,
      metadata,
      response: createCallbackResponse(true, 'success'),
    };
  }

  async refundPayment(
    zptransid: string,
    amount: number,
    description: string
  ): Promise<{
    success: boolean;
    refundId?: string;
    message: string;
  }> {
    try {
      const timestamp = Date.now();
      const mac = createOrderMac({
        appId: this.config.appId,
        appTransId: zptransid, // For refund, we use zptransid
        appUser: '',
        amount: formatAmount(amount),
        appTime: timestamp,
        embedData: '',
        item: description,
      }, this.config.key1);

      const refundRequest = {
        appId: this.config.appId,
        zptransid,
        amount: formatAmount(amount),
        description: sanitizeUserInput(description),
        timestamp,
        mac,
      };

      const response = await axios.post(
        `${this.config.endpoint}/v2/refund`,
        refundRequest,
        {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }
      );

      const { data } = response;

      return {
        success: data.returnCode === ZaloPayReturnCode.SUCCESS,
        refundId: data.refundId,
        message: data.returnMessage,
      };

    } catch (error: any) {
      return {
        success: false,
        message: `Refund failed: ${error.message}`,
      };
    }
  }

  // Utility methods
  formatAmount(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  }

  validateConfig(): boolean {
    const required = ['appId', 'key1', 'key2', 'endpoint', 'callbackUrl'];
    return required.every(field => 
      this.config[field as keyof ZaloPayConfig] && 
      typeof this.config[field as keyof ZaloPayConfig] === 'string'
    );
  }

  getPaymentMethods(): Array<{
    id: string;
    name: string;
    description: string;
    icon?: string;
  }> {
    return [
      {
        id: 'zalopayapp',
        name: 'ZaloPay App',
        description: 'Thanh toán qua ứng dụng ZaloPay',
        icon: 'zalopay',
      },
      {
        id: 'ATM',
        name: 'Thẻ ATM',
        description: 'Thanh toán bằng thẻ ATM nội địa',
        icon: 'atm',
      },
      {
        id: 'CC',
        name: 'Thẻ tín dụng',
        description: 'Thanh toán bằng thẻ tín dụng',
        icon: 'credit-card',
      },
      {
        id: 'VISA',
        name: 'Visa',
        description: 'Thanh toán bằng thẻ Visa',
        icon: 'visa',
      },
      {
        id: 'MASTERCARD',
        name: 'MasterCard',
        description: 'Thanh toán bằng thẻ MasterCard',
        icon: 'mastercard',
      },
    ];
  }

  createPaymentSummary(metadata: EducationPaymentMetadata): {
    title: string;
    description: string;
    items: Array<{
      label: string;
      value: string;
      amount?: number;
    }>;
    total: number;
    formattedTotal: string;
  } {
    const items = [
      {
        label: metadata.feeCategoryText,
        value: this.formatAmount(metadata.originalAmount),
        amount: metadata.originalAmount,
      },
    ];

    if (metadata.discountAmount && metadata.discountAmount > 0) {
      items.push({
        label: 'Giảm giá',
        value: `-${this.formatAmount(metadata.discountAmount)}`,
        amount: -metadata.discountAmount,
      });
    }

    if (metadata.lateFee && metadata.lateFee > 0) {
      items.push({
        label: 'Phí trễ hạn',
        value: this.formatAmount(metadata.lateFee),
        amount: metadata.lateFee,
      });
    }

    const total = calculateTotalAmount(
      metadata.originalAmount,
      metadata.discountAmount,
      metadata.lateFee
    );

    return {
      title: `Thanh toán học phí - ${metadata.learnerName}`,
      description: `${metadata.feeCategoryText} ${metadata.billingPeriod ? `(${metadata.billingPeriod})` : ''}`,
      items,
      total,
      formattedTotal: this.formatAmount(total),
    };
  }
}
