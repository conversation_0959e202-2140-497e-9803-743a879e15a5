{"name": "@kit/billing-zalopay", "version": "0.1.0", "private": true, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext .ts,.tsx", "typecheck": "tsc --noEmit"}, "dependencies": {"@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "axios": "^1.6.0", "zod": "^3.24.2"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@types/node": "^22.14.0", "eslint": "^9.24.0", "typescript": "^5.8.3"}, "exports": {".": "./src/index.ts", "./types": "./src/types.ts", "./utils": "./src/utils.ts"}}