{"name": "@kit/cdp", "version": "0.1.0", "description": "Customer Data Platform module for unified customer profiles and marketing automation", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist"}, "dependencies": {"@supabase/supabase-js": "2.49.4", "date-fns": "^4.1.0", "ioredis": "^5.3.2", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.14.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.24.0", "jest": "^29.7.0", "ts-jest": "^29.3.1", "typescript": "^5.8.3"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "import": "./dist/types/index.js", "require": "./dist/types/index.js"}, "./services": {"types": "./dist/services/index.d.ts", "import": "./dist/services/index.js", "require": "./dist/services/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "import": "./dist/utils/index.js", "require": "./dist/utils/index.js"}}, "files": ["dist/**/*", "README.md"], "keywords": ["cdp", "customer-data-platform", "marketing-automation", "customer-profiles", "segmentation", "personalization"], "author": "Makerkit Team", "license": "MIT"}