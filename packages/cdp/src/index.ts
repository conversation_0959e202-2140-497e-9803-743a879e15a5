// Core exports
export { CDP<PERSON>anager, create<PERSON><PERSON>anager, defaultCDPConfig } from './core/cdp-manager';

// Service exports
export { ProfileService } from './services/profile-service';
export { SegmentationService } from './services/segmentation-service';
export { JourneyService } from './services/journey-service';
export { SimpleEventProcessor } from './services/simple-event-processor';
export { SimpleAnalytics } from './services/simple-analytics';
export { SimpleMonitor } from './services/simple-monitor';
export { SimpleQueue } from './services/simple-queue';

// Phase 4B: AI/ML Engine exports
export { PredictiveAnalytics } from './services/predictive-analytics';
export { RecommendationEngine } from './services/recommendation-engine';
export { AutoSegmentation } from './services/auto-segmentation';
export { ContentPersonalization } from './services/content-personalization';

// Phase 4C: Advanced Analytics exports
export { JourneyAnalytics } from './services/journey-analytics';
export { AdvancedCohortAnalysis } from './services/advanced-cohort-analysis';
export { AttributionModeling } from './services/attribution-modeling';
export { FunnelOptimization } from './services/funnel-optimization';

// Phase 4D: Integration Hub exports
export { IntegrationManager } from './services/integration-manager';
export { BaseIntegration } from './integrations/base-integration';
export { SendGridIntegration } from './integrations/email/sendgrid-integration';
export { HubSpotIntegration } from './integrations/crm/hubspot-integration';
export { GoogleAnalyticsIntegration } from './integrations/analytics/google-analytics-integration';

// Type exports
export * from './types';

// Utility exports
export * from './utils';

// Re-export commonly used types for convenience
export type {
  CustomerProfile,
  CustomerIdentity,
  CustomerSegment,
  CustomerJourney,
  CDPConfig,
  CDPService,
  CDPEvent
} from './types';
