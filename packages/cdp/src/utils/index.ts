import { CustomerIdentity, CustomerProfile } from '../types';

/**
 * Utility functions for CDP operations
 */

/**
 * Extract identities from various data sources
 */
export function extractIdentities(data: Record<string, any>): CustomerIdentity[] {
  const identities: CustomerIdentity[] = [];
  const now = new Date();

  // Email identity
  if (data.email) {
    identities.push({
      type: 'email',
      value: data.email,
      verified: data.email_verified || false,
      source: data.source || 'unknown',
      first_seen_at: now,
      created_at: now
    });
  }

  // Phone identity
  if (data.phone) {
    identities.push({
      type: 'phone',
      value: data.phone,
      verified: data.phone_verified || false,
      source: data.source || 'unknown',
      first_seen_at: now,
      created_at: now
    });
  }

  // User ID identity
  if (data.user_id) {
    identities.push({
      type: 'user_id',
      value: data.user_id,
      verified: true,
      source: data.source || 'unknown',
      first_seen_at: now,
      created_at: now
    });
  }

  // Visitor ID identity
  if (data.visitor_id) {
    identities.push({
      type: 'visitor_id',
      value: data.visitor_id,
      verified: false,
      source: data.source || 'unknown',
      first_seen_at: now,
      created_at: now
    });
  }

  // Social ID identity
  if (data.social_id) {
    identities.push({
      type: 'social_id',
      value: data.social_id,
      verified: data.social_verified || false,
      source: data.source || 'unknown',
      first_seen_at: now,
      created_at: now
    });
  }

  // External ID identity
  if (data.external_id) {
    identities.push({
      type: 'external_id',
      value: data.external_id,
      verified: false,
      source: data.source || 'unknown',
      first_seen_at: now,
      created_at: now
    });
  }

  return identities;
}

/**
 * Normalize email address
 */
export function normalizeEmail(email: string): string {
  return email.toLowerCase().trim();
}

/**
 * Normalize phone number (basic implementation)
 */
export function normalizePhone(phone: string): string {
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');

  // Add country code if missing (assuming Vietnam +84)
  if (digits.length === 10 && digits.startsWith('0')) {
    return '+84' + digits.substring(1);
  }

  if (digits.length === 9) {
    return '+84' + digits;
  }

  if (digits.length === 11 && digits.startsWith('84')) {
    return '+' + digits;
  }

  return phone; // Return original if can't normalize
}

/**
 * Calculate customer lifetime value
 */
export function calculateLifetimeValue(profile: CustomerProfile): number {
  const { behavior } = profile;

  if (behavior.total_purchases === 0) {
    return 0;
  }

  // Simple LTV calculation: total revenue
  // More sophisticated models would consider:
  // - Customer lifespan
  // - Churn probability
  // - Future purchase predictions
  return behavior.total_revenue;
}

/**
 * Calculate customer value tier based on LTV and engagement
 */
export function calculateCustomerValueTier(profile: CustomerProfile): 'low' | 'medium' | 'high' | 'vip' {
  const ltv = calculateLifetimeValue(profile);
  const engagement = profile.engagement_scores.overall_engagement_score;
  const purchases = profile.behavior.total_purchases;

  // VIP: High LTV + High engagement + Multiple purchases
  if (ltv >= 10000000 && engagement >= 0.8 && purchases >= 5) {
    return 'vip';
  }

  // High: Good LTV + Good engagement
  if (ltv >= 5000000 && engagement >= 0.6 && purchases >= 3) {
    return 'high';
  }

  // Medium: Some purchases + Decent engagement
  if (ltv >= 1000000 && engagement >= 0.4 && purchases >= 1) {
    return 'medium';
  }

  return 'low';
}

/**
 * Calculate churn risk score based on behavioral patterns
 */
export function calculateChurnRisk(profile: CustomerProfile): number {
  const { behavior } = profile;

  if (!behavior.last_activity_at) {
    return 0.9; // High risk if no activity recorded
  }

  const daysSinceLastActivity = Math.floor(
    (Date.now() - behavior.last_activity_at.getTime()) / (1000 * 60 * 60 * 24)
  );

  // Risk factors
  let riskScore = 0;

  // Days since last activity (0-0.4 points)
  if (daysSinceLastActivity > 90) riskScore += 0.4;
  else if (daysSinceLastActivity > 60) riskScore += 0.3;
  else if (daysSinceLastActivity > 30) riskScore += 0.2;
  else if (daysSinceLastActivity > 14) riskScore += 0.1;

  // Low engagement (0-0.3 points)
  const engagement = profile.engagement_scores.overall_engagement_score;
  if (engagement < 0.2) riskScore += 0.3;
  else if (engagement < 0.4) riskScore += 0.2;
  else if (engagement < 0.6) riskScore += 0.1;

  // Purchase behavior (0-0.3 points)
  if (behavior.total_purchases === 0) riskScore += 0.3;
  else if (behavior.last_order_at) {
    const daysSinceLastOrder = Math.floor(
      (Date.now() - behavior.last_order_at.getTime()) / (1000 * 60 * 60 * 24)
    );
    if (daysSinceLastOrder > 180) riskScore += 0.2;
    else if (daysSinceLastOrder > 90) riskScore += 0.1;
  }

  return Math.min(riskScore, 1.0);
}

/**
 * Calculate engagement score based on multiple factors
 */
export function calculateEngagementScore(profile: CustomerProfile): number {
  const { behavior } = profile;

  let score = 0;
  let factors = 0;

  // Session frequency (0-0.3 points)
  if (behavior.total_sessions > 0) {
    const daysSinceFirstSeen = Math.floor(
      (Date.now() - behavior.first_seen_at.getTime()) / (1000 * 60 * 60 * 24)
    );
    const sessionsPerDay = behavior.total_sessions / Math.max(daysSinceFirstSeen, 1);

    if (sessionsPerDay >= 1) score += 0.3;
    else if (sessionsPerDay >= 0.5) score += 0.2;
    else if (sessionsPerDay >= 0.1) score += 0.1;

    factors++;
  }

  // Session duration (0-0.2 points)
  if (behavior.avg_session_duration > 0) {
    const avgMinutes = behavior.avg_session_duration / 60;
    if (avgMinutes >= 10) score += 0.2;
    else if (avgMinutes >= 5) score += 0.15;
    else if (avgMinutes >= 2) score += 0.1;
    else if (avgMinutes >= 1) score += 0.05;

    factors++;
  }

  // Purchase behavior (0-0.3 points)
  if (behavior.total_purchases > 0) {
    if (behavior.total_purchases >= 10) score += 0.3;
    else if (behavior.total_purchases >= 5) score += 0.2;
    else if (behavior.total_purchases >= 2) score += 0.15;
    else score += 0.1;

    factors++;
  }

  // Recency (0-0.2 points)
  if (behavior.last_activity_at) {
    const daysSinceLastActivity = Math.floor(
      (Date.now() - behavior.last_activity_at.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysSinceLastActivity <= 1) score += 0.2;
    else if (daysSinceLastActivity <= 7) score += 0.15;
    else if (daysSinceLastActivity <= 30) score += 0.1;
    else if (daysSinceLastActivity <= 90) score += 0.05;

    factors++;
  }

  return factors > 0 ? Math.min(score, 1.0) : 0;
}

/**
 * Determine lifecycle stage based on behavior
 */
export function determineLifecycleStage(profile: CustomerProfile): 'prospect' | 'lead' | 'customer' | 'advocate' | 'churned' {
  const { behavior } = profile;
  const churnRisk = calculateChurnRisk(profile);

  // Churned: High churn risk + No recent activity
  if (churnRisk > 0.8 && behavior.total_purchases > 0) {
    return 'churned';
  }

  // Advocate: Multiple purchases + High engagement + Low churn risk
  if (behavior.total_purchases >= 3 &&
      profile.engagement_scores.overall_engagement_score >= 0.7 &&
      churnRisk < 0.3) {
    return 'advocate';
  }

  // Customer: Has made purchases
  if (behavior.total_purchases > 0) {
    return 'customer';
  }

  // Lead: High engagement but no purchases
  if (behavior.total_sessions >= 3 &&
      profile.engagement_scores.overall_engagement_score >= 0.5) {
    return 'lead';
  }

  // Prospect: Default for new/low-engagement users
  return 'prospect';
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone format (basic)
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

/**
 * Generate a unique visitor ID
 */
export function generateVisitorId(): string {
  return 'visitor_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * Deep merge two objects
 */
export function deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
  const result = { ...target };

  for (const key in source) {
    if (source[key] !== undefined) {
      if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
        result[key] = deepMerge(result[key] || {} as any, source[key] as any);
      } else {
        result[key] = source[key] as T[Extract<keyof T, string>];
      }
    }
  }

  return result;
}

/**
 * Sanitize data for logging (remove sensitive information)
 */
export function sanitizeForLogging(data: any): any {
  if (typeof data !== 'object' || data === null) {
    return data;
  }

  const sensitiveFields = ['email', 'phone', 'password', 'token', 'key', 'secret'];
  const sanitized = { ...data };

  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
}
