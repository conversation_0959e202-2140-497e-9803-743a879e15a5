import { BaseIntegration, IntegrationConfig, SyncConfig, SyncR<PERSON>ult, WebhookPayload } from '../base-integration';
import { CDPError } from '../../types';

interface HubSpotContact {
  id?: string;
  properties: {
    email?: string;
    firstname?: string;
    lastname?: string;
    phone?: string;
    company?: string;
    website?: string;
    lifecyclestage?: string;
    lead_status?: string;
    createdate?: string;
    lastmodifieddate?: string;
    [key: string]: any;
  };
  createdAt?: string;
  updatedAt?: string;
  archived?: boolean;
}

interface HubSpotCompany {
  id?: string;
  properties: {
    name?: string;
    domain?: string;
    industry?: string;
    phone?: string;
    city?: string;
    state?: string;
    country?: string;
    numberofemployees?: string;
    annualrevenue?: string;
    createdate?: string;
    hs_lastmodifieddate?: string;
    [key: string]: any;
  };
  createdAt?: string;
  updatedAt?: string;
}

interface HubSpotDeal {
  id?: string;
  properties: {
    dealname?: string;
    amount?: string;
    dealstage?: string;
    pipeline?: string;
    closedate?: string;
    createdate?: string;
    hs_lastmodifieddate?: string;
    [key: string]: any;
  };
  associations?: {
    contacts?: string[];
    companies?: string[];
  };
}

interface HubSpotEngagement {
  id?: string;
  engagement: {
    type: 'EMAIL' | 'CALL' | 'MEETING' | 'TASK' | 'NOTE';
    timestamp: number;
    source: string;
  };
  associations: {
    contactIds?: number[];
    companyIds?: number[];
    dealIds?: number[];
  };
  metadata?: any;
}

/**
 * HubSpot CRM Integration
 * Handles contacts, companies, deals, and engagement sync
 */
export class HubSpotIntegration extends BaseIntegration {
  private accessToken: string;
  private baseUrl = 'https://api.hubapi.com';

  constructor(config: IntegrationConfig, syncConfig: SyncConfig) {
    super(config, syncConfig);
    this.accessToken = config.credentials.access_token;
  }

  async validateCredentials(): Promise<void> {
    if (!this.accessToken) {
      throw new Error('HubSpot access token is required');
    }

    // Test access token by getting account info
    const response = await this.makeRequest('GET', '/account-info/v3/api-usage');
    if (!response.ok) {
      throw new Error('Invalid HubSpot access token');
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await this.makeRequest('GET', '/account-info/v3/api-usage');
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async syncData(direction: 'import' | 'export' = 'import'): Promise<SyncResult> {
    const startTime = Date.now();
    let recordsProcessed = 0;
    let recordsCreated = 0;
    let recordsUpdated = 0;
    let recordsFailed = 0;
    const errors: string[] = [];

    try {
      if (direction === 'import') {
        // Import contacts
        const contacts = await this.getAllContacts();
        for (const contact of contacts) {
          try {
            const transformedContact = this.transformData(contact, this.syncConfig.field_mappings);
            // Save to CDP database
            recordsProcessed++;
            recordsUpdated++;
          } catch (error) {
            recordsFailed++;
            errors.push(`Failed to process contact ${contact.id}: ${error}`);
          }
        }

        // Import companies
        const companies = await this.getAllCompanies();
        for (const company of companies) {
          try {
            const transformedCompany = this.transformCompanyData(company);
            // Save to CDP database
            recordsProcessed++;
            recordsUpdated++;
          } catch (error) {
            recordsFailed++;
            errors.push(`Failed to process company ${company.id}: ${error}`);
          }
        }

        // Import deals
        const deals = await this.getAllDeals();
        for (const deal of deals) {
          try {
            const transformedDeal = this.transformDealData(deal);
            // Save to CDP database
            recordsProcessed++;
            recordsUpdated++;
          } catch (error) {
            recordsFailed++;
            errors.push(`Failed to process deal ${deal.id}: ${error}`);
          }
        }

        // Import engagements
        const engagements = await this.getRecentEngagements();
        for (const engagement of engagements) {
          try {
            const transformedEngagement = this.transformEngagementData(engagement);
            // Save to CDP database
            recordsProcessed++;
            recordsCreated++;
          } catch (error) {
            recordsFailed++;
            errors.push(`Failed to process engagement ${engagement.id}: ${error}`);
          }
        }
      } else {
        // Export contacts to HubSpot
        const cdpContacts = await this.getCDPContacts();

        for (const contact of cdpContacts) {
          try {
            const hubspotContact = this.transformToHubSpotContact(contact);
            await this.createOrUpdateContact(hubspotContact);
            recordsProcessed++;
            recordsUpdated++;
          } catch (error) {
            recordsFailed++;
            errors.push(`Failed to export contact ${contact.email}: ${error}`);
          }
        }
      }

      return {
        success: recordsFailed === 0,
        records_processed: recordsProcessed,
        records_created: recordsCreated,
        records_updated: recordsUpdated,
        records_failed: recordsFailed,
        errors,
        duration_ms: Date.now() - startTime
      };
    } catch (error) {
      throw new CDPError(
        'HubSpot sync failed',
        'HUBSPOT_SYNC_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async handleWebhook(payload: WebhookPayload): Promise<void> {
    try {
      // HubSpot webhook payload structure
      const events = payload.data;

      for (const event of events) {
        switch (event.subscriptionType) {
          case 'contact.creation':
          case 'contact.propertyChange':
            await this.processContactEvent(event);
            break;
          case 'company.creation':
          case 'company.propertyChange':
            await this.processCompanyEvent(event);
            break;
          case 'deal.creation':
          case 'deal.propertyChange':
            await this.processDealEvent(event);
            break;
          default:
            console.log('Unknown HubSpot event type:', event.subscriptionType);
        }
      }
    } catch (error) {
      throw new CDPError(
        'Failed to process HubSpot webhook',
        'HUBSPOT_WEBHOOK_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get all contacts from HubSpot
   */
  private async getAllContacts(): Promise<HubSpotContact[]> {
    const contacts: HubSpotContact[] = [];
    let after: string | undefined;
    const limit = 100;

    while (true) {
      const params = new URLSearchParams({
        limit: limit.toString(),
        properties: 'email,firstname,lastname,phone,company,website,lifecyclestage,createdate,lastmodifieddate'
      });

      if (after) {
        params.append('after', after);
      }

      const response = await this.makeRequest('GET', `/crm/v3/objects/contacts?${params}`);
      const data: any = await response.json();

      if (!data.results || data.results.length === 0) {
        break;
      }

      contacts.push(...data.results);

      if (!data.paging?.next?.after) {
        break;
      }

      after = data.paging.next.after;
    }

    return contacts;
  }

  /**
   * Get all companies from HubSpot
   */
  private async getAllCompanies(): Promise<HubSpotCompany[]> {
    const companies: HubSpotCompany[] = [];
    let after: string | undefined;
    const limit = 100;

    while (true) {
      const params = new URLSearchParams({
        limit: limit.toString(),
        properties: 'name,domain,industry,phone,city,state,country,numberofemployees,annualrevenue'
      });

      if (after) {
        params.append('after', after);
      }

      const response = await this.makeRequest('GET', `/crm/v3/objects/companies?${params}`);
      const data: any = await response.json();

      if (!data.results || data.results.length === 0) {
        break;
      }

      companies.push(...data.results);

      if (!data.paging?.next?.after) {
        break;
      }

      after = data.paging.next.after;
    }

    return companies;
  }

  /**
   * Get all deals from HubSpot
   */
  private async getAllDeals(): Promise<HubSpotDeal[]> {
    const deals: HubSpotDeal[] = [];
    let after: string | undefined;
    const limit = 100;

    while (true) {
      const params = new URLSearchParams({
        limit: limit.toString(),
        properties: 'dealname,amount,dealstage,pipeline,closedate,createdate'
      });

      if (after) {
        params.append('after', after);
      }

      const response = await this.makeRequest('GET', `/crm/v3/objects/deals?${params}`);
      const data: any = await response.json();

      if (!data.results || data.results.length === 0) {
        break;
      }

      deals.push(...data.results);

      if (!data.paging?.next?.after) {
        break;
      }

      after = data.paging.next.after;
    }

    return deals;
  }

  /**
   * Get recent engagements from HubSpot
   */
  private async getRecentEngagements(): Promise<HubSpotEngagement[]> {
    const engagements: HubSpotEngagement[] = [];
    const since = this.syncConfig.last_sync_timestamp || new Date(Date.now() - 24 * 60 * 60 * 1000);

    // Get recent engagements (simplified)
    const response = await this.makeRequest('GET', `/engagements/v1/engagements/recent/modified?since=${since.getTime()}`);

    if (response.ok) {
      const data: any = await response.json();
      engagements.push(...(data.results || []));
    }

    return engagements;
  }

  /**
   * Create or update contact in HubSpot
   */
  private async createOrUpdateContact(contact: HubSpotContact): Promise<void> {
    const response = await this.makeRequest('POST', '/crm/v3/objects/contacts', contact);

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to create/update contact: ${error}`);
    }
  }

  /**
   * Transform company data to CDP format
   */
  private transformCompanyData(company: HubSpotCompany): any {
    return {
      external_id: company.id,
      name: company.properties.name,
      domain: company.properties.domain,
      industry: company.properties.industry,
      phone: company.properties.phone,
      location: {
        city: company.properties.city,
        state: company.properties.state,
        country: company.properties.country
      },
      employees: company.properties.numberofemployees ? parseInt(company.properties.numberofemployees) : null,
      annual_revenue: company.properties.annualrevenue ? parseFloat(company.properties.annualrevenue) : null,
      source: 'hubspot',
      created_at: company.properties.createdate ? new Date(company.properties.createdate) : new Date(),
      updated_at: company.properties.hs_lastmodifieddate ? new Date(company.properties.hs_lastmodifieddate) : new Date()
    };
  }

  /**
   * Transform deal data to CDP format
   */
  private transformDealData(deal: HubSpotDeal): any {
    return {
      external_id: deal.id,
      name: deal.properties.dealname,
      amount: deal.properties.amount ? parseFloat(deal.properties.amount) : null,
      stage: deal.properties.dealstage,
      pipeline: deal.properties.pipeline,
      close_date: deal.properties.closedate ? new Date(deal.properties.closedate) : null,
      source: 'hubspot',
      created_at: deal.properties.createdate ? new Date(deal.properties.createdate) : new Date(),
      updated_at: deal.properties.hs_lastmodifieddate ? new Date(deal.properties.hs_lastmodifieddate) : new Date()
    };
  }

  /**
   * Transform engagement data to CDP format
   */
  private transformEngagementData(engagement: HubSpotEngagement): any {
    return {
      external_id: engagement.id,
      type: engagement.engagement.type.toLowerCase(),
      timestamp: new Date(engagement.engagement.timestamp),
      source: 'hubspot',
      metadata: engagement.metadata || {},
      contact_ids: engagement.associations?.contactIds || [],
      company_ids: engagement.associations?.companyIds || [],
      deal_ids: engagement.associations?.dealIds || []
    };
  }

  /**
   * Transform CDP contact to HubSpot format
   */
  private transformToHubSpotContact(contact: any): HubSpotContact {
    return {
      properties: {
        email: contact.email,
        firstname: contact.first_name,
        lastname: contact.last_name,
        phone: contact.phone,
        company: contact.company,
        website: contact.website,
        lifecyclestage: contact.lifecycle_stage || 'lead'
      }
    };
  }

  /**
   * Process contact event from webhook
   */
  private async processContactEvent(event: any): Promise<void> {
    // Process contact creation or update event
    console.log('Processing HubSpot contact event:', event);
  }

  /**
   * Process company event from webhook
   */
  private async processCompanyEvent(event: any): Promise<void> {
    // Process company creation or update event
    console.log('Processing HubSpot company event:', event);
  }

  /**
   * Process deal event from webhook
   */
  private async processDealEvent(event: any): Promise<void> {
    // Process deal creation or update event
    console.log('Processing HubSpot deal event:', event);
  }

  /**
   * Get contacts from CDP database (placeholder)
   */
  private async getCDPContacts(): Promise<any[]> {
    // This would query your CDP database
    return [];
  }

  /**
   * Make HTTP request to HubSpot API
   */
  private async makeRequest(method: string, endpoint: string, body?: any): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`;

    const options: RequestInit = {
      method,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json'
      }
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    return await this.retryOperation(
      () => fetch(url, options),
      3,
      1000
    );
  }

  /**
   * Get contact by email
   */
  async getContactByEmail(email: string): Promise<HubSpotContact | null> {
    const response = await this.makeRequest('GET', `/crm/v3/objects/contacts/${email}?idProperty=email`);

    if (!response.ok) {
      return null;
    }

    return await response.json() as HubSpotContact;
  }

  /**
   * Create contact
   */
  async createContact(contact: HubSpotContact): Promise<HubSpotContact> {
    const response = await this.makeRequest('POST', '/crm/v3/objects/contacts', contact);

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to create contact: ${error}`);
    }

    return await response.json() as HubSpotContact;
  }

  /**
   * Update contact
   */
  async updateContact(contactId: string, updates: Partial<HubSpotContact>): Promise<HubSpotContact> {
    const response = await this.makeRequest('PATCH', `/crm/v3/objects/contacts/${contactId}`, updates);

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to update contact: ${error}`);
    }

    return await response.json() as HubSpotContact;
  }

  /**
   * Get deals for contact
   */
  async getContactDeals(contactId: string): Promise<HubSpotDeal[]> {
    const response = await this.makeRequest('GET', `/crm/v3/objects/contacts/${contactId}/associations/deals`);

    if (!response.ok) {
      return [];
    }

    const data: any = await response.json();
    return data.results || [];
  }
}
