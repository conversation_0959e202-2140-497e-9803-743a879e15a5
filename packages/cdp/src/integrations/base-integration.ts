import { CDPService, CDPError } from '../types';

export interface IntegrationConfig {
  id: string;
  name: string;
  type: 'email' | 'crm' | 'analytics' | 'ecommerce' | 'social' | 'advertising';
  provider: string;
  enabled: boolean;
  credentials: Record<string, any>;
  settings: Record<string, any>;
  webhook_url?: string;
  created_at: Date;
  updated_at: Date;
}

export interface IntegrationStatus {
  connected: boolean;
  last_sync: Date | null;
  last_error: string | null;
  sync_status: 'idle' | 'syncing' | 'error' | 'success';
  records_synced: number;
  health_score: number; // 0-100
}

export interface SyncResult {
  success: boolean;
  records_processed: number;
  records_created: number;
  records_updated: number;
  records_failed: number;
  errors: string[];
  duration_ms: number;
}

export interface WebhookPayload {
  integration_id: string;
  event_type: string;
  data: any;
  timestamp: Date;
  signature?: string;
}

export interface DataMapping {
  source_field: string;
  target_field: string;
  transformation?: 'lowercase' | 'uppercase' | 'email_normalize' | 'phone_normalize' | 'date_format';
  default_value?: any;
  required: boolean;
}

export interface SyncConfig {
  direction: 'import' | 'export' | 'bidirectional';
  frequency: 'realtime' | 'hourly' | 'daily' | 'weekly' | 'manual';
  batch_size: number;
  field_mappings: DataMapping[];
  filters?: Record<string, any>;
  last_sync_timestamp?: Date;
}

/**
 * Base Integration Class
 * Provides common functionality for all integrations
 */
export abstract class BaseIntegration implements CDPService {
  protected config: IntegrationConfig;
  protected status: IntegrationStatus;
  protected syncConfig: SyncConfig;

  constructor(config: IntegrationConfig, syncConfig: SyncConfig) {
    this.config = config;
    this.syncConfig = syncConfig;
    this.status = {
      connected: false,
      last_sync: null,
      last_error: null,
      sync_status: 'idle',
      records_synced: 0,
      health_score: 0
    };
  }

  async initialize(): Promise<void> {
    try {
      await this.validateCredentials();
      await this.testConnection();
      this.status.connected = true;
      this.status.health_score = 100;
      console.log(`${this.config.name} integration initialized successfully`);
    } catch (error) {
      this.status.connected = false;
      this.status.last_error = error instanceof Error ? error.message : String(error);
      this.status.health_score = 0;
      throw new CDPError(
        `Failed to initialize ${this.config.name} integration`,
        'INTEGRATION_INIT_FAILED',
        { config: this.config, error: this.status.last_error }
      );
    }
  }

  async destroy(): Promise<void> {
    this.status.connected = false;
    console.log(`${this.config.name} integration destroyed`);
  }

  /**
   * Abstract methods that must be implemented by specific integrations
   */
  abstract validateCredentials(): Promise<void>;
  abstract testConnection(): Promise<boolean>;
  abstract syncData(direction?: 'import' | 'export'): Promise<SyncResult>;
  abstract handleWebhook(payload: WebhookPayload): Promise<void>;

  /**
   * Get integration status
   */
  getStatus(): IntegrationStatus {
    return { ...this.status };
  }

  /**
   * Get integration config
   */
  getConfig(): IntegrationConfig {
    return { ...this.config };
  }

  /**
   * Update integration config
   */
  async updateConfig(updates: Partial<IntegrationConfig>): Promise<void> {
    this.config = { ...this.config, ...updates, updated_at: new Date() };
    
    if (updates.credentials || updates.settings) {
      await this.initialize(); // Re-initialize if credentials or settings changed
    }
  }

  /**
   * Enable/disable integration
   */
  async setEnabled(enabled: boolean): Promise<void> {
    this.config.enabled = enabled;
    this.config.updated_at = new Date();
    
    if (enabled && !this.status.connected) {
      await this.initialize();
    }
  }

  /**
   * Start data sync
   */
  async startSync(direction?: 'import' | 'export'): Promise<SyncResult> {
    if (!this.config.enabled) {
      throw new CDPError('Integration is disabled', 'INTEGRATION_DISABLED');
    }

    if (!this.status.connected) {
      throw new CDPError('Integration is not connected', 'INTEGRATION_NOT_CONNECTED');
    }

    if (this.status.sync_status === 'syncing') {
      throw new CDPError('Sync already in progress', 'SYNC_IN_PROGRESS');
    }

    try {
      this.status.sync_status = 'syncing';
      const startTime = Date.now();
      
      const result = await this.syncData(direction);
      
      this.status.sync_status = 'success';
      this.status.last_sync = new Date();
      this.status.records_synced += result.records_processed;
      this.status.last_error = null;
      
      // Update health score based on success rate
      const successRate = (result.records_processed - result.records_failed) / result.records_processed;
      this.status.health_score = Math.round(successRate * 100);
      
      return result;
    } catch (error) {
      this.status.sync_status = 'error';
      this.status.last_error = error instanceof Error ? error.message : String(error);
      this.status.health_score = Math.max(0, this.status.health_score - 20);
      
      throw new CDPError(
        `Sync failed for ${this.config.name}`,
        'SYNC_FAILED',
        { error: this.status.last_error }
      );
    }
  }

  /**
   * Transform data based on field mappings
   */
  protected transformData(data: any, mappings: DataMapping[]): any {
    const transformed: any = {};
    
    for (const mapping of mappings) {
      let value = this.getNestedValue(data, mapping.source_field);
      
      // Apply default value if source is empty
      if (value === undefined || value === null || value === '') {
        if (mapping.default_value !== undefined) {
          value = mapping.default_value;
        } else if (mapping.required) {
          throw new Error(`Required field ${mapping.source_field} is missing`);
        } else {
          continue;
        }
      }
      
      // Apply transformation
      if (mapping.transformation && value) {
        value = this.applyTransformation(value, mapping.transformation);
      }
      
      this.setNestedValue(transformed, mapping.target_field, value);
    }
    
    return transformed;
  }

  /**
   * Get nested object value using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Set nested object value using dot notation
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  /**
   * Apply data transformation
   */
  private applyTransformation(value: any, transformation: string): any {
    switch (transformation) {
      case 'lowercase':
        return String(value).toLowerCase();
      case 'uppercase':
        return String(value).toUpperCase();
      case 'email_normalize':
        return String(value).toLowerCase().trim();
      case 'phone_normalize':
        return String(value).replace(/\D/g, ''); // Remove non-digits
      case 'date_format':
        return new Date(value).toISOString();
      default:
        return value;
    }
  }

  /**
   * Validate webhook signature
   */
  protected validateWebhookSignature(payload: string, signature: string, secret: string): boolean {
    // Implementation depends on the specific integration's signature method
    // This is a placeholder - each integration should override this method
    return true;
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const connectionTest = await this.testConnection();
      
      return {
        status: this.status.connected && connectionTest ? 'healthy' : 'unhealthy',
        connected: this.status.connected,
        last_sync: this.status.last_sync,
        last_error: this.status.last_error,
        sync_status: this.status.sync_status,
        records_synced: this.status.records_synced,
        health_score: this.status.health_score,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        connected: false,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Retry mechanism for failed operations
   */
  protected async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delayMs: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === maxRetries) {
          throw lastError;
        }
        
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delayMs * Math.pow(2, attempt - 1)));
      }
    }
    
    throw lastError!;
  }

  /**
   * Rate limiting helper
   */
  protected async rateLimitedRequest<T>(
    request: () => Promise<T>,
    rateLimit: { requests: number; windowMs: number }
  ): Promise<T> {
    // Simple rate limiting implementation
    // In production, you might want to use a more sophisticated rate limiter
    return await request();
  }

  /**
   * Batch processing helper
   */
  protected async processBatch<T, R>(
    items: T[],
    processor: (batch: T[]) => Promise<R[]>,
    batchSize: number = 100
  ): Promise<R[]> {
    const results: R[] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await processor(batch);
      results.push(...batchResults);
    }
    
    return results;
  }
}
