import { BaseIntegration, IntegrationConfig, SyncConfig, SyncR<PERSON>ult, WebhookPayload } from '../base-integration';
import { CDPError } from '../../types';

interface SendGridContact {
  id?: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone_number?: string;
  custom_fields?: Record<string, any>;
  list_ids?: string[];
  created_at?: string;
  updated_at?: string;
}

interface SendGridList {
  id: string;
  name: string;
  contact_count: number;
  created_at: string;
  updated_at: string;
}

interface SendGridCampaign {
  id: string;
  name: string;
  subject: string;
  status: 'draft' | 'scheduled' | 'sent';
  send_at?: string;
  created_at: string;
  updated_at: string;
}

interface SendGridEvent {
  email: string;
  timestamp: number;
  event: 'delivered' | 'open' | 'click' | 'bounce' | 'dropped' | 'spamreport' | 'unsubscribe';
  campaign_id?: string;
  url?: string;
  reason?: string;
  sg_event_id: string;
  sg_message_id: string;
}

/**
 * SendGrid Email Integration
 * Handles contact sync, campaign tracking, and email events
 */
export class SendGridIntegration extends BaseIntegration {
  private apiKey: string;
  private baseUrl = 'https://api.sendgrid.com/v3';

  constructor(config: IntegrationConfig, syncConfig: SyncConfig) {
    super(config, syncConfig);
    this.apiKey = config.credentials.api_key;
  }

  async validateCredentials(): Promise<void> {
    if (!this.apiKey) {
      throw new Error('SendGrid API key is required');
    }

    // Test API key by making a simple request
    const response = await this.makeRequest('GET', '/user/profile');
    if (!response.ok) {
      throw new Error('Invalid SendGrid API key');
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await this.makeRequest('GET', '/user/profile');
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async syncData(direction: 'import' | 'export' = 'import'): Promise<SyncResult> {
    const startTime = Date.now();
    let recordsProcessed = 0;
    let recordsCreated = 0;
    let recordsUpdated = 0;
    let recordsFailed = 0;
    const errors: string[] = [];

    try {
      if (direction === 'import') {
        // Import contacts from SendGrid
        const contacts = await this.getAllContacts();

        for (const contact of contacts) {
          try {
            const transformedContact = this.transformData(contact, this.syncConfig.field_mappings);

            // Here you would save to your CDP database
            // For now, we'll simulate the process
            recordsProcessed++;

            if (contact.id) {
              recordsUpdated++;
            } else {
              recordsCreated++;
            }
          } catch (error) {
            recordsFailed++;
            errors.push(`Failed to process contact ${contact.email}: ${error}`);
          }
        }

        // Import email events
        const events = await this.getEmailEvents();
        for (const event of events) {
          try {
            const transformedEvent = this.transformEmailEvent(event);
            // Save event to CDP database
            recordsProcessed++;
            recordsCreated++;
          } catch (error) {
            recordsFailed++;
            errors.push(`Failed to process event ${event.sg_event_id}: ${error}`);
          }
        }
      } else {
        // Export contacts to SendGrid
        // This would get contacts from your CDP database and send to SendGrid
        const cdpContacts = await this.getCDPContacts();

        for (const contact of cdpContacts) {
          try {
            const sendGridContact = this.transformToSendGridContact(contact);
            await this.createOrUpdateContact(sendGridContact);
            recordsProcessed++;
            recordsUpdated++;
          } catch (error) {
            recordsFailed++;
            errors.push(`Failed to export contact ${contact.email}: ${error}`);
          }
        }
      }

      return {
        success: recordsFailed === 0,
        records_processed: recordsProcessed,
        records_created: recordsCreated,
        records_updated: recordsUpdated,
        records_failed: recordsFailed,
        errors,
        duration_ms: Date.now() - startTime
      };
    } catch (error) {
      throw new CDPError(
        'SendGrid sync failed',
        'SENDGRID_SYNC_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async handleWebhook(payload: WebhookPayload): Promise<void> {
    try {
      // SendGrid sends events as an array
      const events = Array.isArray(payload.data) ? payload.data : [payload.data];

      for (const event of events) {
        const transformedEvent = this.transformEmailEvent(event);

        // Process the event (save to database, trigger automations, etc.)
        await this.processEmailEvent(transformedEvent);
      }
    } catch (error) {
      throw new CDPError(
        'Failed to process SendGrid webhook',
        'SENDGRID_WEBHOOK_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get all contacts from SendGrid
   */
  private async getAllContacts(): Promise<SendGridContact[]> {
    const contacts: SendGridContact[] = [];
    let offset = 0;
    const limit = 1000;

    while (true) {
      const response = await this.makeRequest('GET', `/marketing/contacts?limit=${limit}&offset=${offset}`);
      const data: any = await response.json();

      if (!data.result || data.result.length === 0) {
        break;
      }

      contacts.push(...data.result);
      offset += limit;

      if (data.result.length < limit) {
        break;
      }
    }

    return contacts;
  }

  /**
   * Get email events from SendGrid
   */
  private async getEmailEvents(): Promise<SendGridEvent[]> {
    const events: SendGridEvent[] = [];
    const startTime = this.syncConfig.last_sync_timestamp || new Date(Date.now() - 24 * 60 * 60 * 1000);

    // SendGrid Event Webhook API
    const response = await this.makeRequest('GET', `/user/webhooks/event/settings`);

    if (response.ok) {
      // In a real implementation, you would get events from your webhook endpoint storage
      // For now, we'll return empty array as events come via webhooks
      return events;
    }

    return events;
  }

  /**
   * Create or update contact in SendGrid
   */
  private async createOrUpdateContact(contact: SendGridContact): Promise<void> {
    const response = await this.makeRequest('PUT', '/marketing/contacts', {
      contacts: [contact]
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to create/update contact: ${error}`);
    }
  }

  /**
   * Get contacts from CDP database (placeholder)
   */
  private async getCDPContacts(): Promise<any[]> {
    // This would query your CDP database
    // For now, return empty array
    return [];
  }

  /**
   * Transform email event to CDP format
   */
  private transformEmailEvent(event: SendGridEvent): any {
    return {
      customer_email: event.email,
      event_type: `email_${event.event}`,
      event_data: {
        campaign_id: event.campaign_id,
        url: event.url,
        reason: event.reason,
        provider: 'sendgrid',
        message_id: event.sg_message_id,
        event_id: event.sg_event_id
      },
      timestamp: new Date(event.timestamp * 1000),
      source: 'sendgrid_webhook'
    };
  }

  /**
   * Transform CDP contact to SendGrid format
   */
  private transformToSendGridContact(contact: any): SendGridContact {
    return {
      email: contact.email,
      first_name: contact.first_name,
      last_name: contact.last_name,
      phone_number: contact.phone,
      custom_fields: contact.custom_fields || {}
    };
  }

  /**
   * Process email event (save to database, trigger automations)
   */
  private async processEmailEvent(event: any): Promise<void> {
    // This would:
    // 1. Save event to analytics_events table
    // 2. Update customer engagement metrics
    // 3. Trigger any automation rules
    // 4. Update email campaign performance metrics

    console.log('Processing email event:', event);
  }

  /**
   * Make HTTP request to SendGrid API
   */
  private async makeRequest(method: string, endpoint: string, body?: any): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`;

    const options: RequestInit = {
      method,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    return await this.retryOperation(
      () => fetch(url, options),
      3,
      1000
    );
  }

  /**
   * Get SendGrid lists
   */
  async getLists(): Promise<SendGridList[]> {
    const response = await this.makeRequest('GET', '/marketing/lists');

    if (!response.ok) {
      throw new Error('Failed to get SendGrid lists');
    }

    const data: any = await response.json();
    return data.result || [];
  }

  /**
   * Get SendGrid campaigns
   */
  async getCampaigns(): Promise<SendGridCampaign[]> {
    const response = await this.makeRequest('GET', '/marketing/campaigns');

    if (!response.ok) {
      throw new Error('Failed to get SendGrid campaigns');
    }

    const data: any = await response.json();
    return data.result || [];
  }

  /**
   * Create email campaign
   */
  async createCampaign(campaign: Partial<SendGridCampaign>): Promise<SendGridCampaign> {
    const response = await this.makeRequest('POST', '/marketing/campaigns', campaign);

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to create campaign: ${error}`);
    }

    return await response.json() as SendGridCampaign;
  }

  /**
   * Send campaign
   */
  async sendCampaign(campaignId: string, sendAt?: Date): Promise<void> {
    const body = sendAt ? { send_at: sendAt.toISOString() } : {};

    const response = await this.makeRequest('POST', `/marketing/campaigns/${campaignId}/schedules/now`, body);

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to send campaign: ${error}`);
    }
  }

  /**
   * Get campaign stats
   */
  async getCampaignStats(campaignId: string): Promise<any> {
    const response = await this.makeRequest('GET', `/marketing/campaigns/${campaignId}/stats`);

    if (!response.ok) {
      throw new Error('Failed to get campaign stats');
    }

    return await response.json() as any;
  }
}
