// Customer Types
export * from './customer';
export * from './segment';
export * from './journey';

// Common Types
export interface CDPConfig {
  // Core settings
  realTimeProcessing: boolean;
  batchProcessingInterval: number;

  // Data retention
  profileRetentionDays: number;
  eventRetentionDays: number;

  // Performance settings
  cacheEnabled: boolean;
  cacheTTL: number;
  maxConcurrentProcessing: number;

  // ML settings
  enablePredictiveAnalytics: boolean;
  modelUpdateFrequency: number;

  // Integration settings
  enabledIntegrations: string[];
  integrationConfigs: Record<string, any>;
}

// Service Interfaces
export interface CDPService {
  initialize(): Promise<void>;
  destroy(): Promise<void>;
}

// Event Types
export interface CDPEvent {
  type: string;
  payload: Record<string, any>;
  timestamp: Date;
  source: string;
}

// Error Types
export class CDPError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'CDPError';
  }
}

export class ProfileNotFoundError extends CDPError {
  constructor(profileId: string) {
    super(`Profile not found: ${profileId}`, 'PROFILE_NOT_FOUND', { profileId });
  }
}

export class SegmentNotFoundError extends CDPError {
  constructor(segmentId: string) {
    super(`Segment not found: ${segmentId}`, 'SEGMENT_NOT_FOUND', { segmentId });
  }
}

export class JourneyNotFoundError extends CDPError {
  constructor(journeyId: string) {
    super(`Journey not found: ${journeyId}`, 'JOURNEY_NOT_FOUND', { journeyId });
  }
}

export class IdentityResolutionError extends CDPError {
  constructor(identities: any[]) {
    super('Failed to resolve customer identity', 'IDENTITY_RESOLUTION_FAILED', { identities });
  }
}

export class SegmentEvaluationError extends CDPError {
  constructor(segmentId: string, error: string) {
    super(`Segment evaluation failed: ${error}`, 'SEGMENT_EVALUATION_FAILED', { segmentId, error });
  }
}

export class JourneyExecutionError extends CDPError {
  constructor(journeyId: string, customerId: string, error: string) {
    super(`Journey execution failed: ${error}`, 'JOURNEY_EXECUTION_FAILED', { journeyId, customerId, error });
  }
}

// Analytics Event Integration Types
export interface AnalyticsEvent {
  id: string;
  account_id: string;
  theme_id?: string;
  event_type: string;
  event_data: Record<string, any>;
  created_at: string;
  user_id?: string;
  visitor_id?: string;
  device_type?: string;
  source: string;
}

// Cache Types
export interface CacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  del(key: string): Promise<void>;
  exists(key: string): Promise<boolean>;
  flush(): Promise<void>;
}

// Database Types
export interface DatabaseService {
  query<T>(sql: string, params?: any[]): Promise<T[]>;
  queryOne<T>(sql: string, params?: any[]): Promise<T | null>;
  transaction<T>(callback: (tx: any) => Promise<T>): Promise<T>;
}

// Event Bus Types
export interface EventBus {
  emit(event: string, payload: any): Promise<void>;
  on(event: string, handler: (payload: any) => Promise<void>): void;
  off(event: string, handler: (payload: any) => Promise<void>): void;
}

// ML Service Types
export interface MLService {
  predict(modelName: string, features: Record<string, any>): Promise<number>;
  trainModel(modelName: string, trainingData: any[]): Promise<void>;
  hasModel(modelName: string): boolean;
  getModelMetrics(modelName: string): Promise<Record<string, any>>;
}

// External Integration Types
export interface ExternalIntegration {
  name: string;
  type: 'crm' | 'email' | 'ads' | 'social' | 'analytics' | 'custom';
  connect(config: Record<string, any>): Promise<void>;
  disconnect(): Promise<void>;
  sync(): Promise<void>;
  isConnected(): boolean;
}

// Activation Types
export interface ActivationChannel {
  type: 'email' | 'sms' | 'push' | 'zns' | 'webhook';
  send(customerId: string, config: Record<string, any>): Promise<void>;
  validateConfig(config: Record<string, any>): boolean;
}

// Personalization Types
export interface PersonalizationEngine {
  personalizeContent(customerId: string, contentType: string): Promise<any>;
  getRecommendations(customerId: string, type: string, limit?: number): Promise<any[]>;
  getNextBestAction(customerId: string): Promise<any>;
}

// Scoring Types
export interface ScoringModel {
  name: string;
  calculateScore(profile: any): Promise<number>;
  getFeatures(profile: any): Promise<Record<string, number>>;
  updateScore(customerId: string): Promise<void>;
}

// Query Builder Types
export interface QueryBuilder {
  select(fields: string[]): QueryBuilder;
  from(table: string): QueryBuilder;
  where(condition: string, params?: any[]): QueryBuilder;
  join(table: string, on: string, type?: 'INNER' | 'LEFT' | 'RIGHT'): QueryBuilder;
  orderBy(field: string, direction?: 'ASC' | 'DESC'): QueryBuilder;
  limit(count: number): QueryBuilder;
  offset(count: number): QueryBuilder;
  build(): { sql: string; params: any[] };
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequireAtLeastOne<T, Keys extends keyof T = keyof T> =
  Pick<T, Exclude<keyof T, Keys>> &
  { [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>> }[Keys];

export type Timestamp = {
  created_at: Date;
  updated_at: Date;
};

export type WithTimestamp<T> = T & Timestamp;

// Phase 4 Advanced Types
export interface EventProcessingResult {
  success: boolean;
  eventId: string;
  processingTime: number;
  result?: any;
  error?: string;
}

// Enhanced Customer Event for real-time processing
export interface CustomerEvent {
  id?: string;
  customer_profile_id?: string;
  anonymous_id?: string;
  session_id?: string;
  event_type: string;
  event_data?: Record<string, any>;
  timestamp: Date;
  processed_at?: Date;
  ip_address?: string;
  user_agent?: string;
  referrer?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  retry_count?: number;
}
