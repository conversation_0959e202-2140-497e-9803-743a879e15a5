import { z } from 'zod';

// Segment Condition Types
export const SegmentConditionSchema = z.object({
  id: z.string().uuid().optional(),
  field: z.string(),
  operator: z.enum([
    'equals',
    'not_equals',
    'greater_than',
    'greater_than_or_equal',
    'less_than',
    'less_than_or_equal',
    'contains',
    'not_contains',
    'starts_with',
    'ends_with',
    'in',
    'not_in',
    'is_null',
    'is_not_null',
    'within_days',
    'more_than_days_ago',
    'between',
    'regex_match'
  ]),
  value: z.any(),
  secondary_value: z.any().optional(), // For 'between' operator
  timeframe: z.object({
    period: z.enum(['days', 'weeks', 'months', 'years']),
    value: z.number().positive(),
  }).optional(),
  case_sensitive: z.boolean().default(false),
});

export type SegmentCondition = z.infer<typeof SegmentConditionSchema>;

// Segment Criteria Types
export const SegmentCriteriaSchema: z.ZodType<any> = z.object({
  operator: z.enum(['AND', 'OR']),
  conditions: z.array(SegmentConditionSchema),
  nested_criteria: z.array(z.lazy((): z.ZodType<any> => SegmentCriteriaSchema)).optional(),
});

export type SegmentCriteria = z.infer<typeof SegmentCriteriaSchema>;

// Segment Activation Types
export const SegmentActivationSchema = z.object({
  id: z.string().uuid(),
  type: z.enum(['email_platform', 'crm', 'ads_platform', 'webhook', 'journey']),
  platform: z.string(), // 'mailchimp', 'hubspot', 'facebook', etc.
  config: z.record(z.any()),
  is_active: z.boolean().default(true),
  last_sync_at: z.date().optional(),
  sync_frequency: z.enum(['real_time', 'hourly', 'daily', 'weekly']).default('daily'),
  created_at: z.date().default(() => new Date()),
});

export type SegmentActivation = z.infer<typeof SegmentActivationSchema>;

// Segment Performance Types
export const SegmentPerformanceSchema = z.object({
  segment_id: z.string().uuid(),
  date: z.date(),
  customer_count: z.number().default(0),
  new_customers: z.number().default(0),
  churned_customers: z.number().default(0),
  growth_rate: z.number().default(0),
  engagement_rate: z.number().default(0),
  conversion_rate: z.number().default(0),
  revenue_generated: z.number().default(0),
  computed_at: z.date().default(() => new Date()),
});

export type SegmentPerformance = z.infer<typeof SegmentPerformanceSchema>;

// Customer Segment Types
export const CustomerSegmentSchema = z.object({
  id: z.string().uuid(),
  account_id: z.string().uuid(),

  // Basic Information
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  color: z.string().optional(), // Hex color for UI
  icon: z.string().optional(), // Icon name for UI

  // Segment Definition
  criteria: SegmentCriteriaSchema,
  type: z.enum(['static', 'dynamic']).default('dynamic'),

  // Metadata
  customer_count: z.number().default(0),
  estimated_size: z.number().optional(), // For preview before creation

  // Status
  is_active: z.boolean().default(true),
  is_computing: z.boolean().default(false),

  // Activations
  activations: z.array(SegmentActivationSchema).default([]),

  // Performance tracking
  performance_history: z.array(SegmentPerformanceSchema).default([]),

  // Timestamps
  created_at: z.date().default(() => new Date()),
  updated_at: z.date().default(() => new Date()),
  last_computed_at: z.date().optional(),

  // Metadata
  created_by: z.string().uuid().optional(),
  tags: z.array(z.string()).default([]),
  custom_attributes: z.record(z.any()).default({}),
});

export type CustomerSegment = z.infer<typeof CustomerSegmentSchema>;

// Segment Membership Types
export const SegmentMembershipSchema = z.object({
  customer_profile_id: z.string().uuid(),
  segment_id: z.string().uuid(),
  account_id: z.string().uuid(),
  added_at: z.date().default(() => new Date()),
  removed_at: z.date().optional(),
  is_active: z.boolean().default(true),
  entry_reason: z.string().optional(), // Which condition caused entry
  exit_reason: z.string().optional(), // Which condition caused exit
});

export type SegmentMembership = z.infer<typeof SegmentMembershipSchema>;

// Segment Definition for Creation
export const SegmentDefinitionSchema = CustomerSegmentSchema.omit({
  id: true,
  customer_count: true,
  is_computing: true,
  performance_history: true,
  created_at: true,
  updated_at: true,
  last_computed_at: true,
});

export type SegmentDefinition = z.infer<typeof SegmentDefinitionSchema>;

// Segment Query Builder Types
export const SegmentQuerySchema = z.object({
  select: z.array(z.string()).default(['customer_profile_id']),
  from: z.string().default('customer_profiles'),
  where: z.string(),
  joins: z.array(z.object({
    table: z.string(),
    on: z.string(),
    type: z.enum(['INNER', 'LEFT', 'RIGHT', 'FULL']).default('LEFT'),
  })).default([]),
  order_by: z.array(z.object({
    field: z.string(),
    direction: z.enum(['ASC', 'DESC']).default('ASC'),
  })).default([]),
  limit: z.number().optional(),
  offset: z.number().optional(),
});

export type SegmentQuery = z.infer<typeof SegmentQuerySchema>;

// Segment Template Types
export const SegmentTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  category: z.enum(['behavioral', 'demographic', 'transactional', 'engagement', 'lifecycle']),
  criteria: SegmentCriteriaSchema,
  variables: z.array(z.object({
    name: z.string(),
    type: z.enum(['number', 'string', 'date', 'boolean']),
    default_value: z.any(),
    description: z.string(),
    required: z.boolean().default(true),
  })).default([]),
  tags: z.array(z.string()).default([]),
});

export type SegmentTemplate = z.infer<typeof SegmentTemplateSchema>;

// Segment Overlap Analysis Types
export const SegmentOverlapSchema = z.object({
  segment_a_id: z.string().uuid(),
  segment_b_id: z.string().uuid(),
  overlap_count: z.number(),
  segment_a_count: z.number(),
  segment_b_count: z.number(),
  overlap_percentage: z.number(),
  jaccard_index: z.number(), // Intersection / Union
  computed_at: z.date().default(() => new Date()),
});

export type SegmentOverlap = z.infer<typeof SegmentOverlapSchema>;
