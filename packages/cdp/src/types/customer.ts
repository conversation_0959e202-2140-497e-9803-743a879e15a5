import { z } from 'zod';

// Customer Identity Types
export const CustomerIdentitySchema = z.object({
  type: z.enum(['email', 'phone', 'user_id', 'visitor_id', 'social_id', 'external_id']),
  value: z.string().min(1),
  verified: z.boolean().default(false),
  source: z.string(),
  first_seen_at: z.date(),
  created_at: z.date().default(() => new Date()),
});

export type CustomerIdentity = z.infer<typeof CustomerIdentitySchema>;

// Location Types
export const LocationSchema = z.object({
  country: z.string().optional(),
  country_code: z.string().optional(),
  city: z.string().optional(),
  region: z.string().optional(),
  postal_code: z.string().optional(),
  coordinates: z.object({
    latitude: z.number(),
    longitude: z.number(),
  }).optional(),
});

export type Location = z.infer<typeof LocationSchema>;

// Demographics Types
export const DemographicsSchema = z.object({
  age: z.number().min(0).max(150).optional(),
  gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say']).optional(),
  location: LocationSchema.optional(),
  language: z.string().optional(),
  timezone: z.string().optional(),
  birth_date: z.date().optional(),
});

export type Demographics = z.infer<typeof DemographicsSchema>;

// Behavioral Data Types
export const BehavioralDataSchema = z.object({
  total_sessions: z.number().default(0),
  total_pageviews: z.number().default(0),
  total_events: z.number().default(0),
  total_purchases: z.number().default(0),
  total_revenue: z.number().default(0),
  average_order_value: z.number().default(0),
  avg_session_duration: z.number().default(0),
  last_activity_at: z.date().optional(),
  last_order_at: z.date().optional(),
  first_seen_at: z.date(),
  last_seen_at: z.date().optional(),
});

export type BehavioralData = z.infer<typeof BehavioralDataSchema>;

// Preferences Types
export const PreferencesSchema = z.object({
  communication_channels: z.array(z.enum(['email', 'sms', 'push', 'zns'])).default([]),
  product_categories: z.array(z.string()).default([]),
  brands: z.array(z.string()).default([]),
  price_sensitivity: z.enum(['low', 'medium', 'high']).optional(),
  frequency_preference: z.enum(['daily', 'weekly', 'monthly', 'rarely']).optional(),
  content_types: z.array(z.string()).default([]),
});

export type Preferences = z.infer<typeof PreferencesSchema>;

// Engagement Scores Types
export const EngagementScoresSchema = z.object({
  email_engagement_score: z.number().min(0).max(1).default(0),
  website_engagement_score: z.number().min(0).max(1).default(0),
  social_engagement_score: z.number().min(0).max(1).default(0),
  overall_engagement_score: z.number().min(0).max(1).default(0),
});

export type EngagementScores = z.infer<typeof EngagementScoresSchema>;

// Predictive Scores Types
export const PredictiveScoresSchema = z.object({
  churn_risk_score: z.number().min(0).max(1).default(0),
  lifetime_value_score: z.number().min(0).default(0),
  purchase_propensity_score: z.number().min(0).max(1).default(0),
  engagement_propensity_score: z.number().min(0).max(1).default(0),
  upsell_propensity_score: z.number().min(0).max(1).default(0),
});

export type PredictiveScores = z.infer<typeof PredictiveScoresSchema>;

// Customer Profile Types
export const CustomerProfileSchema = z.object({
  id: z.string().uuid(),
  account_id: z.string().uuid(),
  
  // Identity
  identities: z.array(CustomerIdentitySchema).default([]),
  primary_email: z.string().email().optional(),
  primary_phone: z.string().optional(),
  primary_user_id: z.string().optional(),
  
  // Personal Information
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  full_name: z.string().optional(),
  demographics: DemographicsSchema.default({}),
  
  // Behavioral Data
  behavior: BehavioralDataSchema,
  
  // Preferences
  preferences: PreferencesSchema.default({}),
  
  // Scores
  engagement_scores: EngagementScoresSchema.default({}),
  predictive_scores: PredictiveScoresSchema.default({}),
  
  // Lifecycle
  lifecycle_stage: z.enum(['prospect', 'lead', 'customer', 'advocate', 'churned']).default('prospect'),
  customer_value_tier: z.enum(['low', 'medium', 'high', 'vip']).default('low'),
  
  // Segments
  segments: z.array(z.string()).default([]),
  
  // Custom attributes
  custom_attributes: z.record(z.any()).default({}),
  tags: z.array(z.string()).default([]),
  
  // Metadata
  created_at: z.date().default(() => new Date()),
  updated_at: z.date().default(() => new Date()),
  computed_at: z.date().default(() => new Date()),
});

export type CustomerProfile = z.infer<typeof CustomerProfileSchema>;

// Profile Update Data Types
export const ProfileUpdateDataSchema = CustomerProfileSchema.partial().extend({
  identities: z.array(CustomerIdentitySchema).optional(),
});

export type ProfileUpdateData = z.infer<typeof ProfileUpdateDataSchema>;

// Customer Event Types
export const CustomerEventSchema = z.object({
  type: z.string(),
  customer_id: z.string().uuid(),
  data: z.record(z.any()).default({}),
  timestamp: z.date().default(() => new Date()),
  source: z.string().default('cdp'),
  session_id: z.string().optional(),
  device_type: z.enum(['mobile', 'desktop', 'tablet']).optional(),
});

export type CustomerEvent = z.infer<typeof CustomerEventSchema>;

// Profile Merge Result Types
export const ProfileMergeResultSchema = z.object({
  primary_profile_id: z.string().uuid(),
  merged_profile_ids: z.array(z.string().uuid()),
  conflicts_resolved: z.array(z.object({
    field: z.string(),
    primary_value: z.any(),
    merged_value: z.any(),
    resolution_strategy: z.string(),
  })),
  created_at: z.date().default(() => new Date()),
});

export type ProfileMergeResult = z.infer<typeof ProfileMergeResultSchema>;
