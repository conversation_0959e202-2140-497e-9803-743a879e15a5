import { z } from 'zod';

// Journey Trigger Types
export const JourneyTriggerSchema = z.object({
  id: z.string().uuid().optional(),
  type: z.enum([
    'event',
    'segment_entry',
    'segment_exit',
    'date_based',
    'api_trigger',
    'manual'
  ]),
  event_type: z.string().optional(), // For event triggers
  segment_id: z.string().uuid().optional(), // For segment triggers
  schedule: z.object({
    type: z.enum(['once', 'recurring']),
    date: z.date().optional(),
    cron_expression: z.string().optional(),
    timezone: z.string().default('UTC'),
  }).optional(),
  conditions: z.array(z.object({
    field: z.string(),
    operator: z.string(),
    value: z.any(),
  })).default([]),
  delay: z.object({
    amount: z.number(),
    unit: z.enum(['minutes', 'hours', 'days', 'weeks']),
  }).optional(),
});

export type JourneyTrigger = z.infer<typeof JourneyTriggerSchema>;

// Journey Action Types
export const JourneyActionSchema: z.ZodType<any> = z.object({
  type: z.enum([
    'send_email',
    'send_sms',
    'send_push',
    'send_zns',
    'add_to_segment',
    'remove_from_segment',
    'update_profile',
    'wait',
    'webhook',
    'condition',
    'a_b_test',
    'custom'
  ]),
  config: z.record(z.any()).default({}),

  // Email action config
  email_template_id: z.string().optional(),
  email_subject: z.string().optional(),
  email_content: z.string().optional(),

  // SMS/ZNS action config
  message_template_id: z.string().optional(),
  message_content: z.string().optional(),

  // Segment action config
  segment_id: z.string().uuid().optional(),

  // Profile update config
  profile_updates: z.record(z.any()).optional(),

  // Wait action config
  wait_duration: z.object({
    amount: z.number(),
    unit: z.enum(['minutes', 'hours', 'days', 'weeks']),
  }).optional(),

  // Webhook config
  webhook_url: z.string().url().optional(),
  webhook_method: z.enum(['GET', 'POST', 'PUT', 'PATCH']).optional(),
  webhook_headers: z.record(z.string()).optional(),
  webhook_payload: z.record(z.any()).optional(),

  // Condition config
  condition_rules: z.array(z.object({
    field: z.string(),
    operator: z.string(),
    value: z.any(),
  })).optional(),

  // A/B test config
  variants: z.array(z.object({
    name: z.string(),
    percentage: z.number(),
    action: z.lazy((): z.ZodType<any> => JourneyActionSchema),
  })).optional(),
});

export type JourneyAction = z.infer<typeof JourneyActionSchema>;

// Journey Step Types
export const JourneyStepSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  description: z.string().optional(),

  // Step configuration
  action: JourneyActionSchema,

  // Flow control
  next_steps: z.array(z.object({
    step_id: z.string().uuid(),
    condition: z.object({
      type: z.enum(['always', 'conditional', 'time_based']),
      rules: z.array(z.object({
        field: z.string(),
        operator: z.string(),
        value: z.any(),
      })).optional(),
      delay: z.object({
        amount: z.number(),
        unit: z.enum(['minutes', 'hours', 'days', 'weeks']),
      }).optional(),
    }).optional(),
  })).default([]),

  // Exit conditions
  exit_conditions: z.array(z.object({
    field: z.string(),
    operator: z.string(),
    value: z.any(),
  })).default([]),

  // Timing
  max_wait_time: z.object({
    amount: z.number(),
    unit: z.enum(['minutes', 'hours', 'days', 'weeks']),
  }).optional(),

  // Position for UI
  position: z.object({
    x: z.number(),
    y: z.number(),
  }).optional(),

  // Metadata
  tags: z.array(z.string()).default([]),
  custom_attributes: z.record(z.any()).default({}),
});

export type JourneyStep = z.infer<typeof JourneyStepSchema>;

// Customer Journey Types
export const CustomerJourneySchema = z.object({
  id: z.string().uuid(),
  account_id: z.string().uuid(),

  // Basic Information
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  category: z.enum(['onboarding', 'nurturing', 'retention', 'winback', 'upsell', 'custom']).optional(),

  // Journey Configuration
  trigger: JourneyTriggerSchema,
  steps: z.array(JourneyStepSchema).default([]),

  // Settings
  is_active: z.boolean().default(false),
  allow_re_entry: z.boolean().default(false),
  max_entries_per_customer: z.number().default(1),

  // Goal tracking
  goal_event: z.string().optional(),
  goal_value: z.number().optional(),

  // Analytics
  total_entries: z.number().default(0),
  total_completions: z.number().default(0),
  total_exits: z.number().default(0),
  conversion_rate: z.number().default(0),
  average_completion_time: z.number().default(0), // in minutes

  // Timestamps
  created_at: z.date().default(() => new Date()),
  updated_at: z.date().default(() => new Date()),
  activated_at: z.date().optional(),
  deactivated_at: z.date().optional(),

  // Metadata
  created_by: z.string().uuid().optional(),
  tags: z.array(z.string()).default([]),
  custom_attributes: z.record(z.any()).default({}),
});

export type CustomerJourney = z.infer<typeof CustomerJourneySchema>;

// Journey Instance Types (Customer's progress through a journey)
export const JourneyInstanceSchema = z.object({
  id: z.string().uuid(),
  journey_id: z.string().uuid(),
  customer_profile_id: z.string().uuid(),
  account_id: z.string().uuid(),

  // Progress tracking
  current_step_id: z.string().uuid().optional(),
  completed_steps: z.array(z.string().uuid()).default([]),

  // Status
  status: z.enum(['active', 'completed', 'exited', 'paused', 'failed']).default('active'),
  exit_reason: z.string().optional(),

  // Timing
  started_at: z.date().default(() => new Date()),
  completed_at: z.date().optional(),
  last_activity_at: z.date().default(() => new Date()),

  // Context data
  context_data: z.record(z.any()).default({}),

  // Goal tracking
  goal_achieved: z.boolean().default(false),
  goal_achieved_at: z.date().optional(),
  goal_value: z.number().optional(),
});

export type JourneyInstance = z.infer<typeof JourneyInstanceSchema>;

// Journey Step Execution Types
export const JourneyStepExecutionSchema = z.object({
  id: z.string().uuid(),
  journey_instance_id: z.string().uuid(),
  step_id: z.string().uuid(),
  customer_profile_id: z.string().uuid(),

  // Execution details
  status: z.enum(['pending', 'executing', 'completed', 'failed', 'skipped']).default('pending'),
  started_at: z.date().default(() => new Date()),
  completed_at: z.date().optional(),

  // Results
  result_data: z.record(z.any()).default({}),
  error_message: z.string().optional(),
  retry_count: z.number().default(0),

  // Next step determination
  next_step_id: z.string().uuid().optional(),
  next_step_scheduled_at: z.date().optional(),
});

export type JourneyStepExecution = z.infer<typeof JourneyStepExecutionSchema>;

// Journey Analytics Types
export const JourneyAnalyticsSchema = z.object({
  journey_id: z.string().uuid(),
  date: z.date(),

  // Entry metrics
  entries: z.number().default(0),
  unique_entries: z.number().default(0),

  // Completion metrics
  completions: z.number().default(0),
  completion_rate: z.number().default(0),
  average_completion_time: z.number().default(0),

  // Exit metrics
  exits: z.number().default(0),
  exit_rate: z.number().default(0),

  // Step performance
  step_performance: z.array(z.object({
    step_id: z.string().uuid(),
    executions: z.number(),
    completions: z.number(),
    failures: z.number(),
    average_execution_time: z.number(),
  })).default([]),

  // Goal metrics
  goal_achievements: z.number().default(0),
  goal_achievement_rate: z.number().default(0),
  total_goal_value: z.number().default(0),

  computed_at: z.date().default(() => new Date()),
});

export type JourneyAnalytics = z.infer<typeof JourneyAnalyticsSchema>;

// Journey Definition for Creation
export const JourneyDefinitionSchema = CustomerJourneySchema.omit({
  id: true,
  total_entries: true,
  total_completions: true,
  total_exits: true,
  conversion_rate: true,
  average_completion_time: true,
  created_at: true,
  updated_at: true,
  activated_at: true,
  deactivated_at: true,
});

export type JourneyDefinition = z.infer<typeof JourneyDefinitionSchema>;
