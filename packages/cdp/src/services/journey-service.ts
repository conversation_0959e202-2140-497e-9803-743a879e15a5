import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { v4 as uuidv4 } from 'uuid';
import {
  Customer<PERSON>ourney,
  JourneyDefinition,
  JourneyStep,
  CDPService,
  CDPError,
  JourneyNotFoundError,
  JourneyExecutionError
} from '../types';

// Temporary interfaces until we update the main types file
interface JourneyExecution {
  id: string;
  journey_id: string;
  customer_profile_id: string;
  account_id: string;
  status: string;
  current_step_index: number;
  current_step_id?: string;
  entry_trigger: string;
  entry_data: Record<string, any>;
  steps_completed: number;
  steps_failed: number;
  started_at: Date;
  completed_at?: Date;
  failed_at?: Date;
  last_activity_at: Date;
  execution_data: Record<string, any>;
  error_details: Record<string, any>;
}

interface JourneyTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  trigger_type: string;
  trigger_config: Record<string, any>;
  steps: JourneyStep[];
  tags: string[];
}

/**
 * Journey Service handles customer journey orchestration and automation
 */
export class JourneyService implements CDPService {
  private readonly CACHE_PREFIX = 'cdp:journey:';
  private readonly CACHE_TTL = 1800; // 30 minutes

  constructor(
    private supabase: SupabaseClient,
    private redis?: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('customer_journeys')
        .select('id')
        .limit(1);

      if (error && error.code !== 'PGRST116') {
        throw new Error(`Database connection failed: ${error.message}`);
      }
    } catch (error) {
      throw new CDPError(
        'Failed to initialize JourneyService',
        'JOURNEY_SERVICE_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    if (this.redis) {
      const keys = await this.redis.keys(`${this.CACHE_PREFIX}*`);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    }
  }

  /**
   * Create a new customer journey
   */
  async createJourney(
    accountId: string,
    definition: JourneyDefinition
  ): Promise<CustomerJourney> {
    try {
      const journeyId = uuidv4();
      const now = new Date();

      // Validate journey steps
      this.validateJourneySteps(definition.steps);

      const journeyData = {
        id: journeyId,
        account_id: accountId,
        name: definition.name,
        description: definition.description,
        trigger_type: definition.trigger.type,
        trigger_config: definition.trigger,
        steps: definition.steps,
        is_active: definition.is_active || false,
        is_draft: true, // Always start as draft
        tags: definition.tags || [],
        created_at: now,
        updated_at: now
      };

      const { data, error } = await this.supabase
        .from('customer_journeys')
        .insert(journeyData)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      return this.mapDatabaseToJourney(data);
    } catch (error) {
      throw new CDPError(
        'Failed to create journey',
        'JOURNEY_CREATE_FAILED',
        {
          accountId,
          definition,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Get a journey by ID
   */
  async getJourney(journeyId: string): Promise<CustomerJourney> {
    try {
      // Try cache first
      if (this.redis) {
        const cached = await this.redis.get(`${this.CACHE_PREFIX}${journeyId}`);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      const { data, error } = await this.supabase
        .from('customer_journeys')
        .select('*')
        .eq('id', journeyId)
        .single();

      if (error || !data) {
        throw new JourneyNotFoundError(journeyId);
      }

      const journey = this.mapDatabaseToJourney(data);

      // Cache the result
      if (this.redis) {
        await this.redis.setex(
          `${this.CACHE_PREFIX}${journeyId}`,
          this.CACHE_TTL,
          JSON.stringify(journey)
        );
      }

      return journey;
    } catch (error) {
      if (error instanceof JourneyNotFoundError) {
        throw error;
      }
      throw new CDPError(
        'Failed to get journey',
        'JOURNEY_GET_FAILED',
        { journeyId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Update a journey
   */
  async updateJourney(
    journeyId: string,
    updates: Partial<JourneyDefinition>
  ): Promise<CustomerJourney> {
    try {
      if (updates.steps) {
        this.validateJourneySteps(updates.steps);
      }

      const { data, error } = await this.supabase
        .from('customer_journeys')
        .update({
          ...updates,
          updated_at: new Date()
        })
        .eq('id', journeyId)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      // Clear cache
      if (this.redis) {
        await this.redis.del(`${this.CACHE_PREFIX}${journeyId}`);
      }

      return this.getJourney(journeyId);
    } catch (error) {
      throw new CDPError(
        'Failed to update journey',
        'JOURNEY_UPDATE_FAILED',
        {
          journeyId,
          updates,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Delete a journey
   */
  async deleteJourney(journeyId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('customer_journeys')
        .delete()
        .eq('id', journeyId);

      if (error) {
        throw new Error(error.message);
      }

      // Clear cache
      if (this.redis) {
        await this.redis.del(`${this.CACHE_PREFIX}${journeyId}`);
      }
    } catch (error) {
      throw new CDPError(
        'Failed to delete journey',
        'JOURNEY_DELETE_FAILED',
        { journeyId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * List journeys for an account
   */
  async listJourneys(
    accountId: string,
    options: {
      limit?: number;
      offset?: number;
      isActive?: boolean;
      isDraft?: boolean;
    } = {}
  ): Promise<{ journeys: CustomerJourney[]; total: number }> {
    try {
      let query = this.supabase
        .from('customer_journeys')
        .select('*', { count: 'exact' })
        .eq('account_id', accountId);

      if (options.isActive !== undefined) {
        query = query.eq('is_active', options.isActive);
      }

      if (options.isDraft !== undefined) {
        query = query.eq('is_draft', options.isDraft);
      }

      const { data, error, count } = await query
        .range(options.offset || 0, (options.offset || 0) + (options.limit || 50) - 1)
        .order('updated_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      const journeys = (data || []).map(this.mapDatabaseToJourney);

      return {
        journeys,
        total: count || 0
      };
    } catch (error) {
      throw new CDPError(
        'Failed to list journeys',
        'JOURNEY_LIST_FAILED',
        {
          accountId,
          options,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Start a journey for a customer
   */
  async startJourneyForCustomer(
    journeyId: string,
    customerProfileId: string,
    triggerData: Record<string, any> = {}
  ): Promise<JourneyExecution> {
    try {
      const journey = await this.getJourney(journeyId);

      if (!journey.is_active) {
        throw new Error('Journey is not active');
      }

      const executionId = uuidv4();
      const now = new Date();

      const executionData = {
        id: executionId,
        journey_id: journeyId,
        customer_profile_id: customerProfileId,
        account_id: journey.account_id,
        entry_trigger: 'manual',
        entry_data: triggerData,
        status: 'active',
        current_step_index: 0,
        current_step_id: journey.steps[0]?.id,
        started_at: now,
        last_activity_at: now
      };

      const { data, error } = await this.supabase
        .from('journey_executions')
        .insert(executionData)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      // Schedule first step
      if (journey.steps.length > 0) {
        await this.scheduleNextStep(executionId, journey.steps[0]);
      }

      return this.mapDatabaseToExecution(data);
    } catch (error) {
      throw new JourneyExecutionError(
        journeyId,
        customerProfileId,
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Get journey templates
   */
  async getJourneyTemplates(category?: string): Promise<JourneyTemplate[]> {
    try {
      let query = this.supabase
        .from('journey_templates')
        .select('*')
        .eq('is_active', true);

      if (category) {
        query = query.eq('category', category);
      }

      const { data, error } = await query.order('name');

      if (error) {
        throw new Error(error.message);
      }

      return (data || []).map(this.mapDatabaseToTemplate);
    } catch (error) {
      throw new CDPError(
        'Failed to get journey templates',
        'JOURNEY_TEMPLATES_GET_FAILED',
        { category, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Validate journey steps
   */
  private validateJourneySteps(steps: any[]): void {
    if (!steps || steps.length === 0) {
      throw new Error('Journey must have at least one step');
    }

    for (const step of steps) {
      if (!step.id || !step.name) {
        throw new Error('Each step must have id and name');
      }
    }
  }

  /**
   * Schedule next step in journey
   */
  private async scheduleNextStep(executionId: string, step: any): Promise<void> {
    try {
      const stepExecutionData = {
        id: uuidv4(),
        journey_execution_id: executionId,
        step_id: step.id,
        step_type: step.type || 'email',
        step_index: 0, // Will be updated based on journey flow
        step_config: step.config || {},
        status: 'pending',
        scheduled_at: new Date(),
        created_at: new Date()
      };

      const { error } = await this.supabase
        .from('journey_step_executions')
        .insert(stepExecutionData);

      if (error) {
        throw new Error(error.message);
      }
    } catch (error) {
      console.error('Failed to schedule step:', error);
    }
  }

  /**
   * Map database row to CustomerJourney
   */
  private mapDatabaseToJourney(data: any): CustomerJourney {
    return {
      id: data.id,
      account_id: data.account_id,
      name: data.name,
      description: data.description,
      trigger: data.trigger_config || { type: data.trigger_type || 'manual' },
      steps: data.steps || [],
      is_active: data.is_active || false,
      allow_re_entry: true, // Default value
      max_entries_per_customer: 1, // Default value
      total_entries: data.total_entries || 0,
      total_completions: data.total_completions || 0,
      total_exits: data.total_exits || 0,
      conversion_rate: data.conversion_rate || 0,
      average_completion_time: 0,
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at),
      activated_at: data.published_at ? new Date(data.published_at) : undefined,
      created_by: data.created_by,
      tags: data.tags || [],
      custom_attributes: data.custom_attributes || {}
    };
  }

  /**
   * Map database row to JourneyExecution
   */
  private mapDatabaseToExecution(data: any): JourneyExecution {
    return {
      id: data.id,
      journey_id: data.journey_id,
      customer_profile_id: data.customer_profile_id,
      account_id: data.account_id,
      status: data.status,
      current_step_index: data.current_step_index || 0,
      current_step_id: data.current_step_id,
      entry_trigger: data.entry_trigger,
      entry_data: data.entry_data || {},
      steps_completed: data.steps_completed || 0,
      steps_failed: data.steps_failed || 0,
      started_at: new Date(data.started_at),
      completed_at: data.completed_at ? new Date(data.completed_at) : undefined,
      failed_at: data.failed_at ? new Date(data.failed_at) : undefined,
      last_activity_at: new Date(data.last_activity_at),
      execution_data: data.execution_data || {},
      error_details: data.error_details || {}
    };
  }

  /**
   * Map database row to JourneyTemplate
   */
  private mapDatabaseToTemplate(data: any): JourneyTemplate {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      category: data.category,
      trigger_type: data.trigger_type,
      trigger_config: data.trigger_config || {},
      steps: data.steps || [],
      tags: data.tags || []
    };
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const { error: dbError } = await this.supabase
        .from('customer_journeys')
        .select('id')
        .limit(1);

      let cacheStatus = 'disabled';
      if (this.redis) {
        try {
          await this.redis.ping();
          cacheStatus = 'healthy';
        } catch {
          cacheStatus = 'unhealthy';
        }
      }

      return {
        status: dbError ? 'unhealthy' : 'healthy',
        database: dbError ? 'unhealthy' : 'healthy',
        cache: cacheStatus,
        error: dbError?.message
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}
