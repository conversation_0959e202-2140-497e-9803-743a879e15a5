import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { v4 as uuidv4 } from 'uuid';
import { CustomerEvent, EventProcessingResult, CDPService, CDPError } from '../types';

/**
 * Simplified Event Processing Engine for Phase 4A
 */
export class SimpleEventProcessor implements CDPService {
  constructor(
    private supabase: SupabaseClient,
    private redis: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();
      console.log('SimpleEventProcessor initialized successfully');
    } catch (error) {
      throw new CDPError(
        'Failed to initialize SimpleEventProcessor',
        'EVENT_PROCESSOR_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    console.log('SimpleEventProcessor destroyed');
  }

  /**
   * Process a customer event
   */
  async processEvent(event: CustomerEvent): Promise<EventProcessingResult> {
    const startTime = Date.now();
    const eventId = uuidv4();

    try {
      // Basic validation
      if (!event.event_type) {
        throw new Error('Event type is required');
      }

      // Store event in database
      await this.storeEvent({ ...event, id: eventId });

      // Update real-time metrics
      await this.updateMetrics(event);

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        eventId,
        processingTime,
        result: { stored: true, metrics_updated: true }
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      return {
        success: false,
        eventId,
        processingTime,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Process multiple events
   */
  async processBatchEvents(events: CustomerEvent[]): Promise<EventProcessingResult[]> {
    return Promise.all(events.map(event => this.processEvent(event)));
  }

  /**
   * Store event in database
   */
  private async storeEvent(event: CustomerEvent): Promise<void> {
    const { error } = await this.supabase
      .from('analytics_events')
      .insert({
        id: event.id,
        customer_profile_id: event.customer_profile_id,
        anonymous_id: event.anonymous_id,
        session_id: event.session_id,
        event_type: event.event_type,
        event_data: event.event_data || {},
        timestamp: event.timestamp,
        ip_address: event.ip_address,
        user_agent: event.user_agent
      });

    if (error) {
      throw new Error(`Failed to store event: ${error.message}`);
    }
  }

  /**
   * Update real-time metrics
   */
  private async updateMetrics(event: CustomerEvent): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    
    // Update daily counters
    await this.redis.hincrby(`cdp:metrics:daily:${today}`, event.event_type, 1);
    await this.redis.hincrby(`cdp:metrics:daily:${today}`, 'total_events', 1);
    
    // Update customer metrics if available
    if (event.customer_profile_id) {
      await this.redis.hincrby(`cdp:metrics:daily:${today}`, 'active_customers', 1);
    }
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const dbHealth = await this.supabase.from('analytics_events').select('id').limit(1);
      const redisHealth = await this.redis.ping();
      
      return {
        status: 'healthy',
        database: dbHealth.error ? 'unhealthy' : 'healthy',
        redis: redisHealth === 'PONG' ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
