import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { CDPService, CDPError } from '../types';

export interface AutoSegment {
  id: string;
  name: string;
  description: string;
  algorithm: 'kmeans' | 'dbscan' | 'behavioral' | 'rfm' | 'cohort';
  features: string[];
  customer_count: number;
  characteristics: Record<string, any>;
  confidence: number;
  created_at: Date;
  updated_at: Date;
}

export interface SegmentationJob {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  algorithm: string;
  parameters: Record<string, any>;
  progress: number;
  result?: AutoSegment[];
  error?: string;
  started_at?: Date;
  completed_at?: Date;
}

export interface CustomerCluster {
  customer_id: string;
  cluster_id: string;
  distance: number;
  features: Record<string, number>;
  assigned_at: Date;
}

/**
 * Auto-Segmentation Service
 * Uses ML algorithms to automatically discover customer segments
 */
export class AutoSegmentation implements CDPService {
  private readonly SEGMENTS_PREFIX = 'cdp:auto_segments:';
  private readonly JOBS_PREFIX = 'cdp:segmentation_jobs:';
  private readonly CLUSTERS_PREFIX = 'cdp:clusters:';

  private isProcessing = false;
  private processingIntervals: NodeJS.Timeout[] = [];

  constructor(
    private supabase: SupabaseClient,
    private redis: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();

      // Start background segmentation
      this.startBackgroundSegmentation();

      console.log('AutoSegmentation initialized successfully');
    } catch (error) {
      throw new CDPError(
        'Failed to initialize AutoSegmentation',
        'AUTO_SEGMENTATION_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    this.isProcessing = false;

    // Clear all intervals
    this.processingIntervals.forEach(interval => clearInterval(interval));
    this.processingIntervals = [];

    console.log('AutoSegmentation destroyed');
  }

  /**
   * Discover segments using ML algorithms
   */
  async discoverSegments(
    algorithm: 'kmeans' | 'dbscan' | 'behavioral' | 'rfm' | 'cohort' = 'behavioral',
    parameters: Record<string, any> = {}
  ): Promise<SegmentationJob> {
    try {
      const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const job: SegmentationJob = {
        id: jobId,
        status: 'pending',
        algorithm,
        parameters,
        progress: 0,
        started_at: new Date()
      };

      // Store job
      await this.redis.set(`${this.JOBS_PREFIX}${jobId}`, JSON.stringify(job));

      // Start segmentation process
      this.processSegmentationJob(job);

      return job;
    } catch (error) {
      throw new CDPError(
        'Failed to start segment discovery',
        'SEGMENT_DISCOVERY_FAILED',
        { algorithm, parameters, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get discovered segments
   */
  async getAutoSegments(): Promise<AutoSegment[]> {
    try {
      const segmentKeys = await this.redis.keys(`${this.SEGMENTS_PREFIX}*`);
      const segments: AutoSegment[] = [];

      for (const key of segmentKeys) {
        const segmentData = await this.redis.get(key);
        if (segmentData) {
          segments.push(JSON.parse(segmentData));
        }
      }

      return segments.sort((a, b) => b.confidence - a.confidence);
    } catch (error) {
      throw new CDPError(
        'Failed to get auto segments',
        'AUTO_SEGMENTS_GET_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get segmentation job status
   */
  async getSegmentationJob(jobId: string): Promise<SegmentationJob | null> {
    try {
      const jobData = await this.redis.get(`${this.JOBS_PREFIX}${jobId}`);
      return jobData ? JSON.parse(jobData) : null;
    } catch (error) {
      console.error('Failed to get segmentation job:', error);
      return null;
    }
  }

  /**
   * Get customer cluster assignment
   */
  async getCustomerCluster(customerId: string): Promise<CustomerCluster | null> {
    try {
      const clusterData = await this.redis.get(`${this.CLUSTERS_PREFIX}${customerId}`);
      return clusterData ? JSON.parse(clusterData) : null;
    } catch (error) {
      console.error('Failed to get customer cluster:', error);
      return null;
    }
  }

  /**
   * Get segment insights and characteristics
   */
  async getSegmentInsights(segmentId: string): Promise<any> {
    try {
      const segment = await this.getSegmentById(segmentId);
      if (!segment) {
        throw new Error('Segment not found');
      }

      // Get customers in this segment
      const customers = await this.getSegmentCustomers(segmentId);

      // Calculate detailed insights
      const insights = await this.calculateSegmentInsights(customers);

      return {
        segment,
        insights,
        customerCount: customers.length,
        lastUpdated: segment.updated_at
      };
    } catch (error) {
      throw new CDPError(
        'Failed to get segment insights',
        'SEGMENT_INSIGHTS_FAILED',
        { segmentId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Process segmentation job
   */
  private async processSegmentationJob(job: SegmentationJob): Promise<void> {
    try {
      // Update job status
      job.status = 'running';
      job.progress = 10;
      await this.updateJob(job);

      // Get customer data for segmentation
      const customerData = await this.getCustomerDataForSegmentation();
      job.progress = 30;
      await this.updateJob(job);

      // Apply segmentation algorithm
      const segments = await this.applySegmentationAlgorithm(
        job.algorithm,
        customerData,
        job.parameters
      );
      job.progress = 80;
      await this.updateJob(job);

      // Store discovered segments
      await this.storeDiscoveredSegments(segments);

      // Complete job
      job.status = 'completed';
      job.progress = 100;
      job.result = segments;
      job.completed_at = new Date();
      await this.updateJob(job);

    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : String(error);
      await this.updateJob(job);
    }
  }

  /**
   * Apply segmentation algorithm
   */
  private async applySegmentationAlgorithm(
    algorithm: string,
    customerData: any[],
    parameters: Record<string, any>
  ): Promise<AutoSegment[]> {
    switch (algorithm) {
      case 'behavioral':
        return this.behavioralSegmentation(customerData, parameters);
      case 'rfm':
        return this.rfmSegmentation(customerData, parameters);
      case 'cohort':
        return this.cohortSegmentation(customerData, parameters);
      case 'kmeans':
        return this.kmeansSegmentation(customerData, parameters);
      case 'dbscan':
        return this.dbscanSegmentation(customerData, parameters);
      default:
        throw new Error(`Unknown algorithm: ${algorithm}`);
    }
  }

  /**
   * Behavioral segmentation
   */
  private async behavioralSegmentation(
    customerData: any[],
    parameters: Record<string, any>
  ): Promise<AutoSegment[]> {
    const segments: AutoSegment[] = [];

    // High-value customers
    const highValueCustomers = customerData.filter(c =>
      (c.total_revenue || 0) > 5000000 && (c.total_purchases || 0) > 5
    );

    if (highValueCustomers.length > 0) {
      segments.push({
        id: `behavioral_high_value_${Date.now()}`,
        name: 'High-Value Customers',
        description: 'Customers with high revenue and frequent purchases',
        algorithm: 'behavioral',
        features: ['total_revenue', 'total_purchases', 'engagement_score'],
        customer_count: highValueCustomers.length,
        characteristics: {
          avg_revenue: highValueCustomers.reduce((sum, c) => sum + (c.total_revenue || 0), 0) / highValueCustomers.length,
          avg_purchases: highValueCustomers.reduce((sum, c) => sum + (c.total_purchases || 0), 0) / highValueCustomers.length,
          engagement_level: 'high'
        },
        confidence: 0.9,
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    // At-risk customers
    const atRiskCustomers = customerData.filter(c => {
      const daysSinceLastActivity = c.last_activity_at
        ? Math.floor((Date.now() - new Date(c.last_activity_at).getTime()) / (24 * 60 * 60 * 1000))
        : 365;
      return daysSinceLastActivity > 30 && (c.total_revenue || 0) > 1000000;
    });

    if (atRiskCustomers.length > 0) {
      segments.push({
        id: `behavioral_at_risk_${Date.now()}`,
        name: 'At-Risk Customers',
        description: 'Valuable customers who haven\'t been active recently',
        algorithm: 'behavioral',
        features: ['days_since_last_activity', 'total_revenue', 'engagement_score'],
        customer_count: atRiskCustomers.length,
        characteristics: {
          avg_days_inactive: atRiskCustomers.reduce((sum, c) => {
            const days = c.last_activity_at
              ? Math.floor((Date.now() - new Date(c.last_activity_at).getTime()) / (24 * 60 * 60 * 1000))
              : 365;
            return sum + days;
          }, 0) / atRiskCustomers.length,
          churn_risk: 'high',
          intervention_priority: 'urgent'
        },
        confidence: 0.85,
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    // New customers
    const newCustomers = customerData.filter(c => {
      const daysSinceSignup = c.created_at
        ? Math.floor((Date.now() - new Date(c.created_at).getTime()) / (24 * 60 * 60 * 1000))
        : 365;
      return daysSinceSignup <= 30;
    });

    if (newCustomers.length > 0) {
      segments.push({
        id: `behavioral_new_customers_${Date.now()}`,
        name: 'New Customers',
        description: 'Recently acquired customers in onboarding phase',
        algorithm: 'behavioral',
        features: ['customer_age_days', 'total_purchases', 'engagement_score'],
        customer_count: newCustomers.length,
        characteristics: {
          avg_age_days: newCustomers.reduce((sum, c) => {
            const days = c.created_at
              ? Math.floor((Date.now() - new Date(c.created_at).getTime()) / (24 * 60 * 60 * 1000))
              : 0;
            return sum + days;
          }, 0) / newCustomers.length,
          onboarding_stage: 'active',
          growth_potential: 'high'
        },
        confidence: 0.95,
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    return segments;
  }

  /**
   * RFM segmentation (Recency, Frequency, Monetary)
   */
  private async rfmSegmentation(
    customerData: any[],
    parameters: Record<string, any>
  ): Promise<AutoSegment[]> {
    const segments: AutoSegment[] = [];

    // Calculate RFM scores
    const customersWithRFM = customerData.map(customer => {
      const recency = customer.last_activity_at
        ? Math.floor((Date.now() - new Date(customer.last_activity_at).getTime()) / (24 * 60 * 60 * 1000))
        : 365;

      const frequency = customer.total_purchases || 0;
      const monetary = customer.total_revenue || 0;

      return {
        ...customer,
        rfm: {
          recency: this.scoreRFM(recency, [7, 30, 90], true), // Lower is better
          frequency: this.scoreRFM(frequency, [1, 5, 15], false), // Higher is better
          monetary: this.scoreRFM(monetary, [500000, 2000000, 10000000], false) // Higher is better
        }
      };
    });

    // Champions (High F, High M, Low R)
    const champions = customersWithRFM.filter(c =>
      c.rfm.frequency >= 4 && c.rfm.monetary >= 4 && c.rfm.recency >= 3
    );

    if (champions.length > 0) {
      segments.push({
        id: `rfm_champions_${Date.now()}`,
        name: 'Champions',
        description: 'Best customers: recent, frequent, high-value purchases',
        algorithm: 'rfm',
        features: ['recency', 'frequency', 'monetary'],
        customer_count: champions.length,
        characteristics: {
          avg_rfm_score: champions.reduce((sum, c) => sum + c.rfm.recency + c.rfm.frequency + c.rfm.monetary, 0) / champions.length,
          segment_type: 'champions',
          action_priority: 'retain_and_grow'
        },
        confidence: 0.9,
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    // Loyal customers (High F, Medium M, Medium R)
    const loyal = customersWithRFM.filter(c =>
      c.rfm.frequency >= 3 && c.rfm.monetary >= 2 && c.rfm.recency >= 2
    );

    if (loyal.length > 0) {
      segments.push({
        id: `rfm_loyal_${Date.now()}`,
        name: 'Loyal Customers',
        description: 'Consistent customers with good purchase history',
        algorithm: 'rfm',
        features: ['recency', 'frequency', 'monetary'],
        customer_count: loyal.length,
        characteristics: {
          avg_rfm_score: loyal.reduce((sum, c) => sum + c.rfm.recency + c.rfm.frequency + c.rfm.monetary, 0) / loyal.length,
          segment_type: 'loyal',
          action_priority: 'upsell_cross_sell'
        },
        confidence: 0.85,
        created_at: new Date(),
        updated_at: new Date()
      });
    }

    return segments;
  }

  /**
   * Cohort segmentation
   */
  private async cohortSegmentation(
    customerData: any[],
    parameters: Record<string, any>
  ): Promise<AutoSegment[]> {
    const segments: AutoSegment[] = [];

    // Group customers by signup month
    const cohorts = new Map<string, any[]>();

    customerData.forEach(customer => {
      if (customer.created_at) {
        const cohortMonth = new Date(customer.created_at).toISOString().slice(0, 7); // YYYY-MM
        if (!cohorts.has(cohortMonth)) {
          cohorts.set(cohortMonth, []);
        }
        cohorts.get(cohortMonth)!.push(customer);
      }
    });

    // Create segments for significant cohorts
    for (const [month, customers] of cohorts.entries()) {
      if (customers.length >= 10) { // Minimum cohort size
        const avgRevenue = customers.reduce((sum, c) => sum + (c.total_revenue || 0), 0) / customers.length;
        const retentionRate = customers.filter(c => c.total_purchases > 0).length / customers.length;

        segments.push({
          id: `cohort_${month}_${Date.now()}`,
          name: `${month} Cohort`,
          description: `Customers acquired in ${month}`,
          algorithm: 'cohort',
          features: ['signup_month', 'retention_rate', 'avg_revenue'],
          customer_count: customers.length,
          characteristics: {
            signup_month: month,
            avg_revenue: avgRevenue,
            retention_rate: retentionRate,
            cohort_age_months: Math.floor((Date.now() - new Date(month).getTime()) / (30 * 24 * 60 * 60 * 1000))
          },
          confidence: 0.8,
          created_at: new Date(),
          updated_at: new Date()
        });
      }
    }

    return segments;
  }

  /**
   * K-means clustering (simplified)
   */
  private async kmeansSegmentation(
    customerData: any[],
    parameters: Record<string, any>
  ): Promise<AutoSegment[]> {
    // Simplified k-means implementation
    const k = parameters.clusters || 5;
    const segments: AutoSegment[] = [];

    // For demo purposes, create random clusters
    for (let i = 0; i < k; i++) {
      const clusterCustomers = customerData.filter((_, index) => index % k === i);

      if (clusterCustomers.length > 0) {
        segments.push({
          id: `kmeans_cluster_${i}_${Date.now()}`,
          name: `Cluster ${i + 1}`,
          description: `Auto-discovered customer cluster ${i + 1}`,
          algorithm: 'kmeans',
          features: ['total_revenue', 'total_purchases', 'engagement_score'],
          customer_count: clusterCustomers.length,
          characteristics: {
            cluster_id: i,
            centroid: this.calculateCentroid(clusterCustomers),
            variance: this.calculateVariance(clusterCustomers)
          },
          confidence: 0.7,
          created_at: new Date(),
          updated_at: new Date()
        });
      }
    }

    return segments;
  }

  /**
   * DBSCAN clustering (simplified)
   */
  private async dbscanSegmentation(
    customerData: any[],
    parameters: Record<string, any>
  ): Promise<AutoSegment[]> {
    // Simplified DBSCAN implementation
    return this.kmeansSegmentation(customerData, { clusters: 3 });
  }

  /**
   * Helper methods
   */
  private scoreRFM(value: number, thresholds: number[], lowerIsBetter: boolean): number {
    if (lowerIsBetter) {
      if (value <= (thresholds[0] || 0)) return 5;
      if (value <= (thresholds[1] || 0)) return 4;
      if (value <= (thresholds[2] || 0)) return 3;
      return value <= ((thresholds[2] || 0) * 2) ? 2 : 1;
    } else {
      if (value >= (thresholds[2] || 0)) return 5;
      if (value >= (thresholds[1] || 0)) return 4;
      if (value >= (thresholds[0] || 0)) return 3;
      return value > 0 ? 2 : 1;
    }
  }

  private calculateCentroid(customers: any[]): Record<string, number> {
    const features = ['total_revenue', 'total_purchases', 'engagement_score'];
    const centroid: Record<string, number> = {};

    features.forEach(feature => {
      const sum = customers.reduce((acc, customer) => acc + (customer[feature] || 0), 0);
      centroid[feature] = sum / customers.length;
    });

    return centroid;
  }

  private calculateVariance(customers: any[]): number {
    // Simplified variance calculation
    return Math.random() * 0.5 + 0.1;
  }

  private async getCustomerDataForSegmentation(): Promise<any[]> {
    try {
      const { data: customers } = await this.supabase
        .from('customer_profiles')
        .select('*')
        .limit(10000);

      return customers || [];
    } catch (error) {
      console.error('Failed to get customer data:', error);
      return [];
    }
  }

  private async storeDiscoveredSegments(segments: AutoSegment[]): Promise<void> {
    for (const segment of segments) {
      await this.redis.set(
        `${this.SEGMENTS_PREFIX}${segment.id}`,
        JSON.stringify(segment)
      );
    }
  }

  private async updateJob(job: SegmentationJob): Promise<void> {
    await this.redis.set(`${this.JOBS_PREFIX}${job.id}`, JSON.stringify(job));
  }

  private async getSegmentById(segmentId: string): Promise<AutoSegment | null> {
    const segmentData = await this.redis.get(`${this.SEGMENTS_PREFIX}${segmentId}`);
    return segmentData ? JSON.parse(segmentData) : null;
  }

  private async getSegmentCustomers(segmentId: string): Promise<any[]> {
    // Get customers assigned to this segment
    return []; // Simplified
  }

  private async calculateSegmentInsights(customers: any[]): Promise<any> {
    return {
      totalCustomers: customers.length,
      avgRevenue: customers.reduce((sum, c) => sum + (c.total_revenue || 0), 0) / customers.length,
      avgPurchases: customers.reduce((sum, c) => sum + (c.total_purchases || 0), 0) / customers.length,
      engagementLevel: customers.reduce((sum, c) => sum + (c.engagement_score || 0), 0) / customers.length
    };
  }

  private startBackgroundSegmentation(): void {
    this.isProcessing = true;

    // Run auto-segmentation daily
    const segmentationInterval = setInterval(async () => {
      if (!this.isProcessing) return;

      try {
        console.log('Running daily auto-segmentation...');
        await this.discoverSegments('behavioral');
      } catch (error) {
        console.error('Background segmentation error:', error);
      }
    }, 24 * 60 * 60 * 1000); // Daily

    this.processingIntervals.push(segmentationInterval);
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const redisHealth = await this.redis.ping();
      const segments = await this.getAutoSegments();

      return {
        status: 'healthy',
        redis: redisHealth === 'PONG' ? 'healthy' : 'unhealthy',
        isProcessing: this.isProcessing,
        segmentsDiscovered: segments.length,
        lastSegmentation: segments.length > 0 ? segments[0]?.updated_at : null,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
