import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { v4 as uuidv4 } from 'uuid';
import {
  CustomerProfile,
  CustomerIdentity,
  ProfileUpdateData,
  CDPService,
  CDPError,
  ProfileNotFoundError,
  IdentityResolutionError
} from '../types';

/**
 * Profile Service handles customer profile management and identity resolution
 */
export class ProfileService implements CDPService {
  private readonly CACHE_PREFIX = 'cdp:profile:';
  private readonly CACHE_TTL = 3600; // 1 hour

  constructor(
    private supabase: SupabaseClient,
    private redis?: Redis
  ) {}

  async initialize(): Promise<void> {
    // Verify database connection and required tables
    try {
      const { error } = await this.supabase
        .from('customer_profiles')
        .select('id')
        .limit(1);

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw new Error(`Database connection failed: ${error.message}`);
      }
    } catch (error) {
      throw new CDPError(
        'Failed to initialize ProfileService',
        'PROFILE_SERVICE_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    // Cleanup resources if needed
    if (this.redis) {
      // Clear all profile caches
      const keys = await this.redis.keys(`${this.CACHE_PREFIX}*`);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    }
  }

  /**
   * Create or update a customer profile
   */
  async createOrUpdateProfile(
    accountId: string,
    identities: CustomerIdentity[],
    data: Partial<ProfileUpdateData> = {}
  ): Promise<CustomerProfile> {
    try {
      // 1. Try to resolve existing profile
      const existingProfile = await this.resolveIdentity(accountId, identities);

      if (existingProfile) {
        // Update existing profile
        return this.updateProfile(existingProfile.id, data, identities);
      } else {
        // Create new profile
        return this.createProfile(accountId, data, identities);
      }
    } catch (error) {
      throw new CDPError(
        'Failed to create or update profile',
        'PROFILE_CREATE_UPDATE_FAILED',
        {
          accountId,
          identities: identities.map(i => ({ type: i.type, value: i.value })),
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Get a customer profile by ID
   */
  async getProfile(profileId: string): Promise<CustomerProfile> {
    try {
      // Try cache first
      if (this.redis) {
        const cached = await this.redis.get(`${this.CACHE_PREFIX}${profileId}`);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      // Fetch from database
      const { data, error } = await this.supabase
        .from('customer_profiles')
        .select(`
          *,
          customer_identities (*)
        `)
        .eq('id', profileId)
        .single();

      if (error || !data) {
        throw new ProfileNotFoundError(profileId);
      }

      const profile = this.mapDatabaseToProfile(data);

      // Cache the result
      if (this.redis) {
        await this.redis.setex(
          `${this.CACHE_PREFIX}${profileId}`,
          this.CACHE_TTL,
          JSON.stringify(profile)
        );
      }

      return profile;
    } catch (error) {
      if (error instanceof ProfileNotFoundError) {
        throw error;
      }
      throw new CDPError(
        'Failed to get profile',
        'PROFILE_GET_FAILED',
        { profileId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Update a customer profile
   */
  async updateProfile(
    profileId: string,
    data: Partial<ProfileUpdateData>,
    newIdentities?: CustomerIdentity[]
  ): Promise<CustomerProfile> {
    try {
      // Start transaction
      const { data: updatedProfile, error } = await this.supabase.rpc(
        'update_customer_profile_with_identities',
        {
          profile_id: profileId,
          profile_data: this.mapProfileToDatabase(data),
          new_identities: newIdentities || []
        }
      );

      if (error) {
        throw new Error(error.message);
      }

      // Clear cache
      if (this.redis) {
        await this.redis.del(`${this.CACHE_PREFIX}${profileId}`);
      }

      // Return updated profile
      return this.getProfile(profileId);
    } catch (error) {
      throw new CDPError(
        'Failed to update profile',
        'PROFILE_UPDATE_FAILED',
        {
          profileId,
          data,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Delete a customer profile
   */
  async deleteProfile(profileId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('customer_profiles')
        .delete()
        .eq('id', profileId);

      if (error) {
        throw new Error(error.message);
      }

      // Clear cache
      if (this.redis) {
        await this.redis.del(`${this.CACHE_PREFIX}${profileId}`);
      }
    } catch (error) {
      throw new CDPError(
        'Failed to delete profile',
        'PROFILE_DELETE_FAILED',
        { profileId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Search profiles by criteria
   */
  async searchProfiles(
    accountId: string,
    criteria: Record<string, any>,
    limit = 50,
    offset = 0
  ): Promise<{ profiles: CustomerProfile[]; total: number }> {
    try {
      let query = this.supabase
        .from('customer_profiles')
        .select(`
          *,
          customer_identities (*)
        `, { count: 'exact' })
        .eq('account_id', accountId);

      // Apply search criteria
      for (const [field, value] of Object.entries(criteria)) {
        if (value !== undefined && value !== null) {
          query = query.eq(field, value);
        }
      }

      const { data, error, count } = await query
        .range(offset, offset + limit - 1)
        .order('updated_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      const profiles = (data || []).map(this.mapDatabaseToProfile);

      return {
        profiles,
        total: count || 0
      };
    } catch (error) {
      throw new CDPError(
        'Failed to search profiles',
        'PROFILE_SEARCH_FAILED',
        {
          accountId,
          criteria,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Resolve customer identity to find existing profile
   */
  async resolveIdentity(
    accountId: string,
    identities: CustomerIdentity[]
  ): Promise<CustomerProfile | null> {
    try {
      // Try each identity to find existing profile
      for (const identity of identities) {
        const { data, error } = await this.supabase
          .from('customer_identities')
          .select(`
            customer_profile_id,
            customer_profiles!inner (
              *,
              customer_identities (*)
            )
          `)
          .eq('account_id', accountId)
          .eq('identity_type', identity.type)
          .eq('identity_value', identity.value)
          .single();

        if (!error && data) {
          return this.mapDatabaseToProfile(data.customer_profiles);
        }
      }

      return null;
    } catch (error) {
      throw new IdentityResolutionError(identities);
    }
  }

  /**
   * Merge duplicate profiles
   */
  async mergeProfiles(
    primaryProfileId: string,
    duplicateProfileId: string
  ): Promise<CustomerProfile> {
    try {
      // Use stored procedure for complex merge operation
      const { data, error } = await this.supabase.rpc(
        'merge_customer_profiles',
        {
          primary_profile_id: primaryProfileId,
          duplicate_profile_id: duplicateProfileId
        }
      );

      if (error) {
        throw new Error(error.message);
      }

      // Clear caches
      if (this.redis) {
        await Promise.all([
          this.redis.del(`${this.CACHE_PREFIX}${primaryProfileId}`),
          this.redis.del(`${this.CACHE_PREFIX}${duplicateProfileId}`)
        ]);
      }

      return this.getProfile(primaryProfileId);
    } catch (error) {
      throw new CDPError(
        'Failed to merge profiles',
        'PROFILE_MERGE_FAILED',
        {
          primaryProfileId,
          duplicateProfileId,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Create a new customer profile
   */
  private async createProfile(
    accountId: string,
    data: Partial<ProfileUpdateData>,
    identities: CustomerIdentity[]
  ): Promise<CustomerProfile> {
    const profileId = uuidv4();
    const now = new Date();

    // Prepare profile data
    const profileData: Record<string, any> = {
      id: profileId,
      account_id: accountId,
      ...this.mapProfileToDatabase(data),
      created_at: now,
      updated_at: now,
      computed_at: now
    };

    // Set primary identities
    const emailIdentity = identities.find(i => i.type === 'email');
    const phoneIdentity = identities.find(i => i.type === 'phone');
    const userIdIdentity = identities.find(i => i.type === 'user_id');

    if (emailIdentity) profileData.primary_email = emailIdentity.value;
    if (phoneIdentity) profileData.primary_phone = phoneIdentity.value;
    if (userIdIdentity) profileData.primary_user_id = userIdIdentity.value;

    // Insert profile and identities in transaction
    const { data: createdProfile, error } = await this.supabase.rpc(
      'create_customer_profile_with_identities',
      {
        profile_data: profileData,
        identities_data: identities.map(identity => ({
          ...identity,
          customer_profile_id: profileId,
          account_id: accountId,
          created_at: now
        }))
      }
    );

    if (error) {
      throw new Error(error.message);
    }

    return this.getProfile(profileId);
  }

  /**
   * Map database row to CustomerProfile type
   */
  private mapDatabaseToProfile(data: any): CustomerProfile {
    return {
      id: data.id,
      account_id: data.account_id,
      identities: data.customer_identities || [],
      primary_email: data.primary_email,
      primary_phone: data.primary_phone,
      primary_user_id: data.primary_user_id,
      first_name: data.first_name,
      last_name: data.last_name,
      full_name: data.full_name,
      demographics: data.demographics || {},
      behavior: {
        total_sessions: data.total_sessions || 0,
        total_pageviews: data.total_pageviews || 0,
        total_events: data.total_events || 0,
        total_purchases: data.total_purchases || 0,
        total_revenue: parseFloat(data.total_revenue || '0'),
        average_order_value: parseFloat(data.average_order_value || '0'),
        avg_session_duration: data.avg_session_duration || 0,
        last_activity_at: data.last_activity_at ? new Date(data.last_activity_at) : undefined,
        last_order_at: data.last_order_at ? new Date(data.last_order_at) : undefined,
        first_seen_at: new Date(data.first_seen_at || data.created_at),
        last_seen_at: data.last_seen_at ? new Date(data.last_seen_at) : undefined
      },
      preferences: data.preferences || {},
      engagement_scores: {
        email_engagement_score: parseFloat(data.email_engagement_score || '0'),
        website_engagement_score: parseFloat(data.website_engagement_score || '0'),
        social_engagement_score: parseFloat(data.social_engagement_score || '0'),
        overall_engagement_score: parseFloat(data.overall_engagement_score || '0')
      },
      predictive_scores: {
        churn_risk_score: parseFloat(data.churn_risk_score || '0'),
        lifetime_value_score: parseFloat(data.lifetime_value_score || '0'),
        purchase_propensity_score: parseFloat(data.purchase_propensity_score || '0'),
        engagement_propensity_score: parseFloat(data.engagement_propensity_score || '0'),
        upsell_propensity_score: parseFloat(data.upsell_propensity_score || '0')
      },
      lifecycle_stage: data.lifecycle_stage || 'prospect',
      customer_value_tier: data.customer_value_tier || 'low',
      segments: [], // TODO: Load from segment memberships
      custom_attributes: data.custom_attributes || {},
      tags: data.tags || [],
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at),
      computed_at: new Date(data.computed_at)
    };
  }

  /**
   * Map CustomerProfile to database format
   */
  private mapProfileToDatabase(data: Partial<ProfileUpdateData>): Record<string, any> {
    const dbData: Record<string, any> = {};

    if (data.first_name !== undefined) dbData.first_name = data.first_name;
    if (data.last_name !== undefined) dbData.last_name = data.last_name;
    if (data.demographics !== undefined) dbData.demographics = data.demographics;
    if (data.preferences !== undefined) dbData.preferences = data.preferences;
    if (data.custom_attributes !== undefined) dbData.custom_attributes = data.custom_attributes;
    if (data.tags !== undefined) dbData.tags = data.tags;
    if (data.lifecycle_stage !== undefined) dbData.lifecycle_stage = data.lifecycle_stage;
    if (data.customer_value_tier !== undefined) dbData.customer_value_tier = data.customer_value_tier;

    // Behavioral data
    if (data.behavior) {
      const behavior = data.behavior;
      if (behavior.total_sessions !== undefined) dbData.total_sessions = behavior.total_sessions;
      if (behavior.total_pageviews !== undefined) dbData.total_pageviews = behavior.total_pageviews;
      if (behavior.total_events !== undefined) dbData.total_events = behavior.total_events;
      if (behavior.total_purchases !== undefined) dbData.total_purchases = behavior.total_purchases;
      if (behavior.total_revenue !== undefined) dbData.total_revenue = behavior.total_revenue;
      if (behavior.avg_session_duration !== undefined) dbData.avg_session_duration = behavior.avg_session_duration;
      if (behavior.last_activity_at !== undefined) dbData.last_activity_at = behavior.last_activity_at;
      if (behavior.last_order_at !== undefined) dbData.last_order_at = behavior.last_order_at;
      if (behavior.first_seen_at !== undefined) dbData.first_seen_at = behavior.first_seen_at;
      if (behavior.last_seen_at !== undefined) dbData.last_seen_at = behavior.last_seen_at;
    }

    // Engagement scores
    if (data.engagement_scores) {
      const scores = data.engagement_scores;
      if (scores.email_engagement_score !== undefined) dbData.email_engagement_score = scores.email_engagement_score;
      if (scores.website_engagement_score !== undefined) dbData.website_engagement_score = scores.website_engagement_score;
      if (scores.social_engagement_score !== undefined) dbData.social_engagement_score = scores.social_engagement_score;
    }

    // Predictive scores
    if (data.predictive_scores) {
      const scores = data.predictive_scores;
      if (scores.churn_risk_score !== undefined) dbData.churn_risk_score = scores.churn_risk_score;
      if (scores.lifetime_value_score !== undefined) dbData.lifetime_value_score = scores.lifetime_value_score;
      if (scores.purchase_propensity_score !== undefined) dbData.purchase_propensity_score = scores.purchase_propensity_score;
      if (scores.engagement_propensity_score !== undefined) dbData.engagement_propensity_score = scores.engagement_propensity_score;
      if (scores.upsell_propensity_score !== undefined) dbData.upsell_propensity_score = scores.upsell_propensity_score;
    }

    return dbData;
  }

  /**
   * Get health status of the service
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      // Test database connection
      const { error: dbError } = await this.supabase
        .from('customer_profiles')
        .select('id')
        .limit(1);

      // Test cache connection
      let cacheStatus = 'disabled';
      if (this.redis) {
        try {
          await this.redis.ping();
          cacheStatus = 'healthy';
        } catch {
          cacheStatus = 'unhealthy';
        }
      }

      return {
        status: dbError ? 'unhealthy' : 'healthy',
        database: dbError ? 'unhealthy' : 'healthy',
        cache: cacheStatus,
        error: dbError?.message
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}
