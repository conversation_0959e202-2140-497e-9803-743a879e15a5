import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { CDPService, CDPError } from '../types';

export interface Recommendation {
  id: string;
  customer_id: string;
  type: 'product' | 'content' | 'action' | 'journey' | 'offer';
  title: string;
  description: string;
  confidence: number;
  priority: number;
  data: Record<string, any>;
  reasoning: string[];
  expires_at?: Date;
  created_at: Date;
}

export interface RecommendationRequest {
  customer_id: string;
  context: 'homepage' | 'product_page' | 'cart' | 'email' | 'mobile_app';
  limit?: number;
  types?: string[];
  filters?: Record<string, any>;
}

export interface NextBestAction {
  action: string;
  priority: number;
  confidence: number;
  expected_value: number;
  description: string;
  parameters: Record<string, any>;
  timing: 'immediate' | 'within_hour' | 'within_day' | 'within_week';
}

/**
 * AI-Powered Recommendation Engine
 * Provides personalized recommendations and next best actions
 */
export class RecommendationEngine implements CDPService {
  private readonly RECOMMENDATIONS_PREFIX = 'cdp:recommendations:';
  private readonly MODELS_PREFIX = 'cdp:rec_models:';
  private readonly INTERACTIONS_PREFIX = 'cdp:interactions:';
  
  private isProcessing = false;
  private processingIntervals: NodeJS.Timeout[] = [];

  constructor(
    private supabase: SupabaseClient,
    private redis: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();
      
      // Initialize recommendation models
      await this.initializeRecommendationModels();
      
      // Start background processing
      this.startBackgroundProcessing();
      
      console.log('RecommendationEngine initialized successfully');
    } catch (error) {
      throw new CDPError(
        'Failed to initialize RecommendationEngine',
        'RECOMMENDATION_ENGINE_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    this.isProcessing = false;
    
    // Clear all intervals
    this.processingIntervals.forEach(interval => clearInterval(interval));
    this.processingIntervals = [];
    
    console.log('RecommendationEngine destroyed');
  }

  /**
   * Get personalized recommendations for a customer
   */
  async getRecommendations(request: RecommendationRequest): Promise<Recommendation[]> {
    try {
      const { customer_id, context, limit = 5, types, filters } = request;
      
      // Get customer profile and behavior
      const customerData = await this.getCustomerData(customer_id);
      
      // Generate recommendations based on different algorithms
      const recommendations = await Promise.all([
        this.getCollaborativeFilteringRecommendations(customerData, context),
        this.getContentBasedRecommendations(customerData, context),
        this.getBehavioralRecommendations(customerData, context),
        this.getContextualRecommendations(customerData, context)
      ]);

      // Merge and rank recommendations
      const mergedRecommendations = this.mergeAndRankRecommendations(
        recommendations.flat(),
        types,
        filters
      );

      // Apply business rules and filters
      const filteredRecommendations = this.applyBusinessRules(
        mergedRecommendations,
        customerData,
        context
      );

      // Cache recommendations
      await this.cacheRecommendations(customer_id, filteredRecommendations);

      return filteredRecommendations.slice(0, limit);
    } catch (error) {
      throw new CDPError(
        'Failed to get recommendations',
        'RECOMMENDATIONS_GET_FAILED',
        { request, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get next best action for a customer
   */
  async getNextBestAction(customerId: string): Promise<NextBestAction[]> {
    try {
      const customerData = await this.getCustomerData(customerId);
      
      // Analyze customer state and behavior
      const customerState = this.analyzeCustomerState(customerData);
      
      // Generate potential actions
      const actions = await this.generatePotentialActions(customerData, customerState);
      
      // Score and rank actions
      const rankedActions = this.scoreAndRankActions(actions, customerData);
      
      return rankedActions.slice(0, 3); // Return top 3 actions
    } catch (error) {
      throw new CDPError(
        'Failed to get next best action',
        'NEXT_BEST_ACTION_FAILED',
        { customerId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Record customer interaction with recommendation
   */
  async recordInteraction(
    customerId: string,
    recommendationId: string,
    interactionType: 'view' | 'click' | 'purchase' | 'dismiss',
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const interaction = {
        customer_id: customerId,
        recommendation_id: recommendationId,
        interaction_type: interactionType,
        metadata: metadata || {},
        timestamp: new Date()
      };

      // Store interaction
      await this.redis.lpush(
        `${this.INTERACTIONS_PREFIX}${customerId}`,
        JSON.stringify(interaction)
      );

      // Update recommendation performance
      await this.updateRecommendationPerformance(recommendationId, interactionType);

      // Trigger model updates if needed
      await this.triggerModelUpdate(customerId, interaction);
    } catch (error) {
      console.error('Failed to record interaction:', error);
    }
  }

  /**
   * Get recommendation performance analytics
   */
  async getRecommendationAnalytics(timeRange: { start: Date; end: Date }): Promise<any> {
    try {
      // Get interaction data
      const interactions = await this.getInteractionData(timeRange);
      
      // Calculate metrics
      const metrics = this.calculateRecommendationMetrics(interactions);
      
      return {
        totalRecommendations: metrics.total,
        clickThroughRate: metrics.ctr,
        conversionRate: metrics.conversionRate,
        revenueGenerated: metrics.revenue,
        topPerformingTypes: metrics.topTypes,
        customerEngagement: metrics.engagement,
        timeRange
      };
    } catch (error) {
      throw new CDPError(
        'Failed to get recommendation analytics',
        'RECOMMENDATION_ANALYTICS_FAILED',
        { timeRange, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get collaborative filtering recommendations
   */
  private async getCollaborativeFilteringRecommendations(
    customerData: any,
    context: string
  ): Promise<Recommendation[]> {
    // Find similar customers based on behavior
    const similarCustomers = await this.findSimilarCustomers(customerData);
    
    // Get items liked by similar customers
    const recommendations: Recommendation[] = [];
    
    for (const similarCustomer of similarCustomers.slice(0, 10)) {
      const items = await this.getCustomerPreferences(similarCustomer.id);
      
      for (const item of items.slice(0, 3)) {
        recommendations.push({
          id: `collab_${item.id}_${Date.now()}`,
          customer_id: customerData.id,
          type: 'product',
          title: `Recommended: ${item.name}`,
          description: `Customers like you also liked this`,
          confidence: similarCustomer.similarity * 0.8,
          priority: 2,
          data: { item, algorithm: 'collaborative_filtering' },
          reasoning: [`Similar customers with ${(similarCustomer.similarity * 100).toFixed(1)}% similarity liked this`],
          created_at: new Date()
        });
      }
    }
    
    return recommendations;
  }

  /**
   * Get content-based recommendations
   */
  private async getContentBasedRecommendations(
    customerData: any,
    context: string
  ): Promise<Recommendation[]> {
    // Analyze customer's past preferences
    const preferences = await this.analyzeCustomerPreferences(customerData);
    
    // Find items matching preferences
    const matchingItems = await this.findMatchingContent(preferences);
    
    const recommendations: Recommendation[] = matchingItems.map(item => ({
      id: `content_${item.id}_${Date.now()}`,
      customer_id: customerData.id,
      type: 'product',
      title: `You might like: ${item.name}`,
      description: `Based on your interests in ${preferences.topCategories.join(', ')}`,
      confidence: item.matchScore,
      priority: 1,
      data: { item, algorithm: 'content_based' },
      reasoning: [`Matches your interest in ${item.matchedFeatures.join(', ')}`],
      created_at: new Date()
    }));
    
    return recommendations;
  }

  /**
   * Get behavioral recommendations
   */
  private async getBehavioralRecommendations(
    customerData: any,
    context: string
  ): Promise<Recommendation[]> {
    // Analyze recent behavior patterns
    const behaviorPatterns = await this.analyzeBehaviorPatterns(customerData);
    
    const recommendations: Recommendation[] = [];
    
    // Recommend based on browsing behavior
    if (behaviorPatterns.abandonedCart.length > 0) {
      recommendations.push({
        id: `behavior_cart_${Date.now()}`,
        customer_id: customerData.id,
        type: 'action',
        title: 'Complete your purchase',
        description: 'You have items waiting in your cart',
        confidence: 0.9,
        priority: 1,
        data: { 
          action: 'complete_cart',
          items: behaviorPatterns.abandonedCart,
          algorithm: 'behavioral'
        },
        reasoning: ['You have items in your cart that you viewed recently'],
        created_at: new Date()
      });
    }
    
    // Recommend based on browsing history
    if (behaviorPatterns.frequentCategories.length > 0) {
      const category = behaviorPatterns.frequentCategories[0];
      recommendations.push({
        id: `behavior_category_${Date.now()}`,
        customer_id: customerData.id,
        type: 'content',
        title: `New in ${category.name}`,
        description: `Fresh arrivals in your favorite category`,
        confidence: 0.7,
        priority: 2,
        data: { 
          category,
          algorithm: 'behavioral'
        },
        reasoning: [`You frequently browse ${category.name} category`],
        created_at: new Date()
      });
    }
    
    return recommendations;
  }

  /**
   * Get contextual recommendations
   */
  private async getContextualRecommendations(
    customerData: any,
    context: string
  ): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = [];
    
    // Context-specific recommendations
    switch (context) {
      case 'homepage':
        recommendations.push(...await this.getHomepageRecommendations(customerData));
        break;
      case 'product_page':
        recommendations.push(...await this.getProductPageRecommendations(customerData));
        break;
      case 'cart':
        recommendations.push(...await this.getCartRecommendations(customerData));
        break;
      case 'email':
        recommendations.push(...await this.getEmailRecommendations(customerData));
        break;
    }
    
    return recommendations;
  }

  /**
   * Get customer data for recommendations
   */
  private async getCustomerData(customerId: string): Promise<any> {
    try {
      // Get customer profile
      const { data: profile } = await this.supabase
        .from('customer_profiles')
        .select('*')
        .eq('id', customerId)
        .single();

      // Get recent events
      const { data: events } = await this.supabase
        .from('analytics_events')
        .select('*')
        .eq('customer_profile_id', customerId)
        .order('timestamp', { ascending: false })
        .limit(500);

      return {
        id: customerId,
        profile: profile || {},
        events: events || [],
        preferences: await this.getStoredPreferences(customerId)
      };
    } catch (error) {
      console.error('Failed to get customer data:', error);
      return {
        id: customerId,
        profile: {},
        events: [],
        preferences: {}
      };
    }
  }

  /**
   * Analyze customer state
   */
  private analyzeCustomerState(customerData: any): any {
    const recentEvents = customerData.events.slice(0, 50);
    const profile = customerData.profile;
    
    return {
      lifecycle_stage: this.determineLifecycleStage(profile, recentEvents),
      engagement_level: this.calculateEngagementLevel(recentEvents),
      purchase_intent: this.calculatePurchaseIntent(recentEvents),
      churn_risk: this.calculateChurnRisk(profile, recentEvents),
      value_tier: this.determineValueTier(profile),
      recent_behavior: this.analyzeRecentBehavior(recentEvents)
    };
  }

  /**
   * Generate potential actions
   */
  private async generatePotentialActions(customerData: any, customerState: any): Promise<NextBestAction[]> {
    const actions: NextBestAction[] = [];
    
    // Churn prevention actions
    if (customerState.churn_risk > 0.6) {
      actions.push({
        action: 'send_retention_offer',
        priority: 1,
        confidence: 0.8,
        expected_value: customerData.profile.total_revenue * 0.3,
        description: 'Send personalized retention offer to prevent churn',
        parameters: {
          offer_type: 'discount',
          discount_percentage: 20,
          valid_days: 7
        },
        timing: 'immediate'
      });
    }
    
    // Upsell actions
    if (customerState.value_tier >= 2 && customerState.engagement_level > 0.7) {
      actions.push({
        action: 'recommend_premium_upgrade',
        priority: 2,
        confidence: 0.7,
        expected_value: 1000000, // 1M VND
        description: 'Recommend premium features or products',
        parameters: {
          upgrade_type: 'premium',
          trial_period: 14
        },
        timing: 'within_day'
      });
    }
    
    // Re-engagement actions
    if (customerState.engagement_level < 0.3) {
      actions.push({
        action: 'send_reengagement_campaign',
        priority: 3,
        confidence: 0.6,
        expected_value: customerData.profile.avg_order_value || 500000,
        description: 'Send re-engagement email with personalized content',
        parameters: {
          campaign_type: 'reengagement',
          content_type: 'personalized'
        },
        timing: 'within_hour'
      });
    }
    
    return actions;
  }

  /**
   * Score and rank actions
   */
  private scoreAndRankActions(actions: NextBestAction[], customerData: any): NextBestAction[] {
    return actions
      .map(action => ({
        ...action,
        score: action.confidence * action.expected_value * action.priority
      }))
      .sort((a, b) => (b as any).score - (a as any).score);
  }

  /**
   * Merge and rank recommendations
   */
  private mergeAndRankRecommendations(
    recommendations: Recommendation[],
    types?: string[],
    filters?: Record<string, any>
  ): Recommendation[] {
    let filtered = recommendations;
    
    // Apply type filter
    if (types && types.length > 0) {
      filtered = filtered.filter(rec => types.includes(rec.type));
    }
    
    // Apply additional filters
    if (filters) {
      // Implementation for custom filters
    }
    
    // Remove duplicates and rank by confidence * priority
    const unique = new Map<string, Recommendation>();
    
    for (const rec of filtered) {
      const key = `${rec.type}_${rec.data?.item?.id || rec.id}`;
      if (!unique.has(key) || unique.get(key)!.confidence < rec.confidence) {
        unique.set(key, rec);
      }
    }
    
    return Array.from(unique.values())
      .sort((a, b) => (b.confidence * b.priority) - (a.confidence * a.priority));
  }

  /**
   * Apply business rules
   */
  private applyBusinessRules(
    recommendations: Recommendation[],
    customerData: any,
    context: string
  ): Recommendation[] {
    return recommendations.filter(rec => {
      // Don't recommend items customer already owns
      if (rec.type === 'product' && this.customerOwnsItem(customerData, rec.data?.item?.id)) {
        return false;
      }
      
      // Don't recommend expired offers
      if (rec.expires_at && rec.expires_at < new Date()) {
        return false;
      }
      
      // Apply minimum confidence threshold
      if (rec.confidence < 0.3) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * Helper methods (simplified implementations)
   */
  private async findSimilarCustomers(customerData: any): Promise<any[]> {
    // Simplified similarity calculation
    return [
      { id: 'customer_2', similarity: 0.8 },
      { id: 'customer_3', similarity: 0.7 }
    ];
  }

  private async getCustomerPreferences(customerId: string): Promise<any[]> {
    return [
      { id: 'item_1', name: 'Product A' },
      { id: 'item_2', name: 'Product B' }
    ];
  }

  private async analyzeCustomerPreferences(customerData: any): Promise<any> {
    return {
      topCategories: ['electronics', 'books'],
      priceRange: { min: 100000, max: 1000000 }
    };
  }

  private async findMatchingContent(preferences: any): Promise<any[]> {
    return [
      { 
        id: 'item_3', 
        name: 'Product C', 
        matchScore: 0.8,
        matchedFeatures: ['electronics', 'high-rating']
      }
    ];
  }

  private async analyzeBehaviorPatterns(customerData: any): Promise<any> {
    return {
      abandonedCart: [],
      frequentCategories: [{ name: 'electronics' }],
      browsingHistory: []
    };
  }

  private async getHomepageRecommendations(customerData: any): Promise<Recommendation[]> {
    return [];
  }

  private async getProductPageRecommendations(customerData: any): Promise<Recommendation[]> {
    return [];
  }

  private async getCartRecommendations(customerData: any): Promise<Recommendation[]> {
    return [];
  }

  private async getEmailRecommendations(customerData: any): Promise<Recommendation[]> {
    return [];
  }

  private async getStoredPreferences(customerId: string): Promise<any> {
    return {};
  }

  private determineLifecycleStage(profile: any, events: any[]): string {
    if (!profile.created_at) return 'unknown';
    
    const daysSinceSignup = Math.floor(
      (Date.now() - new Date(profile.created_at).getTime()) / (24 * 60 * 60 * 1000)
    );
    
    if (daysSinceSignup < 7) return 'new';
    if (daysSinceSignup < 30) return 'onboarding';
    if (profile.total_purchases > 0) return 'active';
    return 'inactive';
  }

  private calculateEngagementLevel(events: any[]): number {
    if (events.length === 0) return 0;
    
    const recentEvents = events.filter(e => 
      new Date(e.timestamp) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    );
    
    return Math.min(1, recentEvents.length / 10);
  }

  private calculatePurchaseIntent(events: any[]): number {
    const purchaseSignals = events.filter(e => 
      ['product_view', 'add_to_cart', 'checkout_start'].includes(e.event_type)
    );
    
    return Math.min(1, purchaseSignals.length / 5);
  }

  private calculateChurnRisk(profile: any, events: any[]): number {
    if (!profile.last_activity_at) return 0.8;
    
    const daysSinceLastActivity = Math.floor(
      (Date.now() - new Date(profile.last_activity_at).getTime()) / (24 * 60 * 60 * 1000)
    );
    
    return Math.min(1, daysSinceLastActivity / 30);
  }

  private determineValueTier(profile: any): number {
    const revenue = profile.total_revenue || 0;
    if (revenue >= 10000000) return 4;
    if (revenue >= 5000000) return 3;
    if (revenue >= 1000000) return 2;
    if (revenue > 0) return 1;
    return 0;
  }

  private analyzeRecentBehavior(events: any[]): any {
    return {
      mostFrequentEvents: events.slice(0, 10).map(e => e.event_type),
      sessionCount: new Set(events.map(e => e.session_id)).size
    };
  }

  private customerOwnsItem(customerData: any, itemId: string): boolean {
    return false; // Simplified
  }

  private async cacheRecommendations(customerId: string, recommendations: Recommendation[]): Promise<void> {
    const key = `${this.RECOMMENDATIONS_PREFIX}${customerId}`;
    await this.redis.setex(key, 1800, JSON.stringify(recommendations)); // Cache for 30 minutes
  }

  private async updateRecommendationPerformance(recommendationId: string, interactionType: string): Promise<void> {
    // Update performance metrics
  }

  private async triggerModelUpdate(customerId: string, interaction: any): Promise<void> {
    // Trigger model retraining if needed
  }

  private async getInteractionData(timeRange: { start: Date; end: Date }): Promise<any[]> {
    return []; // Simplified
  }

  private calculateRecommendationMetrics(interactions: any[]): any {
    return {
      total: interactions.length,
      ctr: 0.15,
      conversionRate: 0.05,
      revenue: 5000000,
      topTypes: ['product', 'content'],
      engagement: 0.7
    };
  }

  private async initializeRecommendationModels(): Promise<void> {
    console.log('Recommendation models initialized');
  }

  private startBackgroundProcessing(): void {
    this.isProcessing = true;
    console.log('Background recommendation processing started');
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const redisHealth = await this.redis.ping();
      
      return {
        status: 'healthy',
        redis: redisHealth === 'PONG' ? 'healthy' : 'unhealthy',
        isProcessing: this.isProcessing,
        modelsLoaded: true,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
