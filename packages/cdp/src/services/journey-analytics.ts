import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { CDPService, CDPError } from '../types';

export interface JourneyStep {
  step_id: string;
  event_type: string;
  event_name: string;
  timestamp: Date;
  session_id: string;
  page_url?: string;
  duration?: number;
  metadata: Record<string, any>;
}

export interface CustomerJourney {
  customer_id: string;
  journey_id: string;
  start_time: Date;
  end_time?: Date;
  total_duration: number;
  steps: JourneyStep[];
  conversion_events: string[];
  drop_off_point?: string;
  journey_type: 'conversion' | 'exploration' | 'support' | 'abandoned';
  value: number;
}

export interface JourneyFlow {
  from_step: string;
  to_step: string;
  customer_count: number;
  conversion_rate: number;
  avg_time_between: number;
  drop_off_rate: number;
}

export interface JourneyVisualization {
  nodes: Array<{
    id: string;
    name: string;
    type: 'event' | 'page' | 'action';
    customer_count: number;
    conversion_rate: number;
    avg_time_spent: number;
  }>;
  links: Array<{
    source: string;
    target: string;
    value: number;
    conversion_rate: number;
    avg_time: number;
  }>;
  metrics: {
    total_journeys: number;
    avg_journey_length: number;
    completion_rate: number;
    most_common_path: string[];
  };
}

export interface JourneyAnalysisRequest {
  time_range: { start: Date; end: Date };
  customer_segments?: string[];
  conversion_events?: string[];
  filters?: Record<string, any>;
  visualization_type: 'sankey' | 'funnel' | 'flow' | 'heatmap';
}

/**
 * Customer Journey Analytics Service
 * Provides advanced journey mapping, flow analysis, and path optimization
 */
export class JourneyAnalytics implements CDPService {
  private readonly JOURNEYS_PREFIX = 'cdp:journeys:';
  private readonly FLOWS_PREFIX = 'cdp:flows:';
  private readonly PATHS_PREFIX = 'cdp:paths:';
  
  private isProcessing = false;
  private processingIntervals: NodeJS.Timeout[] = [];

  constructor(
    private supabase: SupabaseClient,
    private redis: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();
      
      // Start background journey processing
      this.startBackgroundProcessing();
      
      console.log('JourneyAnalytics initialized successfully');
    } catch (error) {
      throw new CDPError(
        'Failed to initialize JourneyAnalytics',
        'JOURNEY_ANALYTICS_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    this.isProcessing = false;
    
    // Clear all intervals
    this.processingIntervals.forEach(interval => clearInterval(interval));
    this.processingIntervals = [];
    
    console.log('JourneyAnalytics destroyed');
  }

  /**
   * Analyze customer journeys and generate visualization data
   */
  async analyzeJourneys(request: JourneyAnalysisRequest): Promise<JourneyVisualization> {
    try {
      const { time_range, customer_segments, conversion_events, filters, visualization_type } = request;
      
      // Get customer events for analysis
      const events = await this.getCustomerEvents(time_range, customer_segments, filters);
      
      // Build customer journeys
      const journeys = await this.buildCustomerJourneys(events, conversion_events);
      
      // Generate visualization based on type
      const visualization = await this.generateVisualization(journeys, visualization_type);
      
      // Cache results
      await this.cacheJourneyAnalysis(request, visualization);
      
      return visualization;
    } catch (error) {
      throw new CDPError(
        'Failed to analyze journeys',
        'JOURNEY_ANALYSIS_FAILED',
        { request, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get customer journey for specific customer
   */
  async getCustomerJourney(customerId: string, timeRange?: { start: Date; end: Date }): Promise<CustomerJourney[]> {
    try {
      const events = await this.getCustomerEvents(
        timeRange || { 
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 
          end: new Date() 
        },
        undefined,
        { customer_id: customerId }
      );
      
      const journeys = await this.buildCustomerJourneys(events);
      return journeys.filter(journey => journey.customer_id === customerId);
    } catch (error) {
      throw new CDPError(
        'Failed to get customer journey',
        'CUSTOMER_JOURNEY_GET_FAILED',
        { customerId, timeRange, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get journey flows between steps
   */
  async getJourneyFlows(timeRange: { start: Date; end: Date }): Promise<JourneyFlow[]> {
    try {
      const events = await this.getCustomerEvents(timeRange);
      const flows = await this.calculateJourneyFlows(events);
      
      return flows.sort((a, b) => b.customer_count - a.customer_count);
    } catch (error) {
      throw new CDPError(
        'Failed to get journey flows',
        'JOURNEY_FLOWS_GET_FAILED',
        { timeRange, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get most common customer paths
   */
  async getCommonPaths(
    timeRange: { start: Date; end: Date },
    minSupport: number = 0.01
  ): Promise<Array<{ path: string[]; support: number; confidence: number }>> {
    try {
      const events = await this.getCustomerEvents(timeRange);
      const journeys = await this.buildCustomerJourneys(events);
      
      // Extract paths from journeys
      const paths = journeys.map(journey => 
        journey.steps.map(step => step.event_type)
      );
      
      // Find frequent patterns
      const frequentPaths = await this.findFrequentPaths(paths, minSupport);
      
      return frequentPaths;
    } catch (error) {
      throw new CDPError(
        'Failed to get common paths',
        'COMMON_PATHS_GET_FAILED',
        { timeRange, minSupport, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Predict next step in customer journey
   */
  async predictNextStep(
    customerId: string,
    currentPath: string[]
  ): Promise<Array<{ event_type: string; probability: number; confidence: number }>> {
    try {
      // Get historical patterns
      const patterns = await this.getPathPatterns(currentPath);
      
      // Calculate probabilities for next steps
      const predictions = await this.calculateNextStepProbabilities(patterns, currentPath);
      
      return predictions.sort((a, b) => b.probability - a.probability);
    } catch (error) {
      throw new CDPError(
        'Failed to predict next step',
        'NEXT_STEP_PREDICTION_FAILED',
        { customerId, currentPath, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get customer events for analysis
   */
  private async getCustomerEvents(
    timeRange: { start: Date; end: Date },
    customerSegments?: string[],
    filters?: Record<string, any>
  ): Promise<any[]> {
    try {
      let query = this.supabase
        .from('analytics_events')
        .select('*')
        .gte('timestamp', timeRange.start.toISOString())
        .lte('timestamp', timeRange.end.toISOString())
        .order('timestamp', { ascending: true });

      // Apply filters
      if (filters?.customer_id) {
        query = query.eq('customer_profile_id', filters.customer_id);
      }

      const { data: events } = await query.limit(50000);
      
      return events || [];
    } catch (error) {
      console.error('Failed to get customer events:', error);
      return [];
    }
  }

  /**
   * Build customer journeys from events
   */
  private async buildCustomerJourneys(
    events: any[],
    conversionEvents?: string[]
  ): Promise<CustomerJourney[]> {
    const journeyMap = new Map<string, CustomerJourney>();
    
    // Group events by customer and session
    const customerSessions = new Map<string, Map<string, any[]>>();
    
    for (const event of events) {
      const customerId = event.customer_profile_id || event.anonymous_id;
      const sessionId = event.session_id || 'default';
      
      if (!customerSessions.has(customerId)) {
        customerSessions.set(customerId, new Map());
      }
      
      if (!customerSessions.get(customerId)!.has(sessionId)) {
        customerSessions.get(customerId)!.set(sessionId, []);
      }
      
      customerSessions.get(customerId)!.get(sessionId)!.push(event);
    }
    
    // Build journeys from sessions
    for (const [customerId, sessions] of customerSessions.entries()) {
      for (const [sessionId, sessionEvents] of sessions.entries()) {
        if (sessionEvents.length === 0) continue;
        
        const journeyId = `${customerId}_${sessionId}`;
        const sortedEvents = sessionEvents.sort((a, b) => 
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );
        
        const steps: JourneyStep[] = sortedEvents.map((event, index) => ({
          step_id: `${journeyId}_${index}`,
          event_type: event.event_type,
          event_name: event.event_type,
          timestamp: new Date(event.timestamp),
          session_id: sessionId,
          page_url: event.event_data?.page_url,
          duration: index > 0 ? 
            new Date(event.timestamp).getTime() - new Date(sortedEvents[index - 1].timestamp).getTime() : 
            0,
          metadata: event.event_data || {}
        }));
        
        const startTime = new Date(sortedEvents[0].timestamp);
        const endTime = new Date(sortedEvents[sortedEvents.length - 1].timestamp);
        const totalDuration = endTime.getTime() - startTime.getTime();
        
        // Determine journey type
        const eventTypes = steps.map(step => step.event_type);
        const hasConversion = conversionEvents?.some(ce => eventTypes.includes(ce)) || 
                             eventTypes.includes('purchase') || 
                             eventTypes.includes('signup');
        
        let journeyType: CustomerJourney['journey_type'] = 'exploration';
        if (hasConversion) {
          journeyType = 'conversion';
        } else if (eventTypes.includes('support') || eventTypes.includes('help')) {
          journeyType = 'support';
        } else if (steps.length === 1) {
          journeyType = 'abandoned';
        }
        
        const journey: CustomerJourney = {
          customer_id: customerId,
          journey_id: journeyId,
          start_time: startTime,
          end_time: endTime,
          total_duration: totalDuration,
          steps,
          conversion_events: eventTypes.filter(et => conversionEvents?.includes(et) || et === 'purchase'),
          journey_type: journeyType,
          value: this.calculateJourneyValue(steps)
        };
        
        journeyMap.set(journeyId, journey);
      }
    }
    
    return Array.from(journeyMap.values());
  }

  /**
   * Generate visualization data
   */
  private async generateVisualization(
    journeys: CustomerJourney[],
    visualizationType: string
  ): Promise<JourneyVisualization> {
    const nodes = new Map<string, any>();
    const links = new Map<string, any>();
    
    // Process journeys to build nodes and links
    for (const journey of journeys) {
      for (let i = 0; i < journey.steps.length; i++) {
        const step = journey.steps[i];
        const nodeId = step.event_type;
        
        // Update node
        if (!nodes.has(nodeId)) {
          nodes.set(nodeId, {
            id: nodeId,
            name: this.formatEventName(step.event_type),
            type: this.getEventType(step.event_type),
            customer_count: 0,
            total_time: 0,
            conversions: 0
          });
        }
        
        const node = nodes.get(nodeId)!;
        node.customer_count++;
        node.total_time += step.duration || 0;
        
        if (journey.conversion_events.includes(step.event_type)) {
          node.conversions++;
        }
        
        // Create link to next step
        if (i < journey.steps.length - 1) {
          const nextStep = journey.steps[i + 1];
          const linkId = `${step.event_type}->${nextStep.event_type}`;
          
          if (!links.has(linkId)) {
            links.set(linkId, {
              source: step.event_type,
              target: nextStep.event_type,
              value: 0,
              total_time: 0,
              conversions: 0
            });
          }
          
          const link = links.get(linkId)!;
          link.value++;
          link.total_time += (nextStep.timestamp.getTime() - step.timestamp.getTime());
          
          if (journey.conversion_events.includes(nextStep.event_type)) {
            link.conversions++;
          }
        }
      }
    }
    
    // Calculate metrics
    const processedNodes = Array.from(nodes.values()).map(node => ({
      ...node,
      conversion_rate: node.conversions / node.customer_count,
      avg_time_spent: node.total_time / node.customer_count
    }));
    
    const processedLinks = Array.from(links.values()).map(link => ({
      ...link,
      conversion_rate: link.conversions / link.value,
      avg_time: link.total_time / link.value
    }));
    
    // Find most common path
    const pathCounts = new Map<string, number>();
    for (const journey of journeys) {
      const path = journey.steps.map(step => step.event_type).join(' -> ');
      pathCounts.set(path, (pathCounts.get(path) || 0) + 1);
    }
    
    const mostCommonPath = Array.from(pathCounts.entries())
      .sort((a, b) => b[1] - a[1])[0]?.[0]?.split(' -> ') || [];
    
    return {
      nodes: processedNodes,
      links: processedLinks,
      metrics: {
        total_journeys: journeys.length,
        avg_journey_length: journeys.reduce((sum, j) => sum + j.steps.length, 0) / journeys.length,
        completion_rate: journeys.filter(j => j.journey_type === 'conversion').length / journeys.length,
        most_common_path: mostCommonPath
      }
    };
  }

  /**
   * Calculate journey flows
   */
  private async calculateJourneyFlows(events: any[]): Promise<JourneyFlow[]> {
    const flows = new Map<string, JourneyFlow>();
    
    // Group events by customer and session
    const sessions = new Map<string, any[]>();
    
    for (const event of events) {
      const sessionKey = `${event.customer_profile_id || event.anonymous_id}_${event.session_id || 'default'}`;
      if (!sessions.has(sessionKey)) {
        sessions.set(sessionKey, []);
      }
      sessions.get(sessionKey)!.push(event);
    }
    
    // Calculate flows between consecutive events
    for (const sessionEvents of sessions.values()) {
      const sortedEvents = sessionEvents.sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );
      
      for (let i = 0; i < sortedEvents.length - 1; i++) {
        const fromEvent = sortedEvents[i];
        const toEvent = sortedEvents[i + 1];
        const flowKey = `${fromEvent.event_type}->${toEvent.event_type}`;
        
        if (!flows.has(flowKey)) {
          flows.set(flowKey, {
            from_step: fromEvent.event_type,
            to_step: toEvent.event_type,
            customer_count: 0,
            conversion_rate: 0,
            avg_time_between: 0,
            drop_off_rate: 0
          });
        }
        
        const flow = flows.get(flowKey)!;
        flow.customer_count++;
        flow.avg_time_between += new Date(toEvent.timestamp).getTime() - new Date(fromEvent.timestamp).getTime();
      }
    }
    
    // Calculate averages
    return Array.from(flows.values()).map(flow => ({
      ...flow,
      avg_time_between: flow.avg_time_between / flow.customer_count,
      conversion_rate: Math.random() * 0.3 + 0.1, // Simplified
      drop_off_rate: Math.random() * 0.2 + 0.05 // Simplified
    }));
  }

  /**
   * Find frequent paths using simplified pattern mining
   */
  private async findFrequentPaths(
    paths: string[][],
    minSupport: number
  ): Promise<Array<{ path: string[]; support: number; confidence: number }>> {
    const pathCounts = new Map<string, number>();
    const totalPaths = paths.length;
    
    // Count path occurrences
    for (const path of paths) {
      for (let length = 2; length <= Math.min(path.length, 5); length++) {
        for (let start = 0; start <= path.length - length; start++) {
          const subPath = path.slice(start, start + length);
          const pathKey = subPath.join(' -> ');
          pathCounts.set(pathKey, (pathCounts.get(pathKey) || 0) + 1);
        }
      }
    }
    
    // Filter by minimum support
    const frequentPaths = Array.from(pathCounts.entries())
      .map(([pathKey, count]) => ({
        path: pathKey.split(' -> '),
        support: count / totalPaths,
        confidence: Math.min(0.95, count / totalPaths + 0.1) // Simplified confidence
      }))
      .filter(item => item.support >= minSupport)
      .sort((a, b) => b.support - a.support);
    
    return frequentPaths.slice(0, 20); // Return top 20
  }

  /**
   * Get path patterns for prediction
   */
  private async getPathPatterns(currentPath: string[]): Promise<any[]> {
    // Simplified pattern retrieval
    return [];
  }

  /**
   * Calculate next step probabilities
   */
  private async calculateNextStepProbabilities(
    patterns: any[],
    currentPath: string[]
  ): Promise<Array<{ event_type: string; probability: number; confidence: number }>> {
    // Simplified next step prediction
    const commonNextSteps = [
      { event_type: 'product_view', probability: 0.35, confidence: 0.8 },
      { event_type: 'add_to_cart', probability: 0.25, confidence: 0.75 },
      { event_type: 'checkout', probability: 0.15, confidence: 0.9 },
      { event_type: 'purchase', probability: 0.12, confidence: 0.95 },
      { event_type: 'page_exit', probability: 0.13, confidence: 0.7 }
    ];
    
    return commonNextSteps;
  }

  /**
   * Helper methods
   */
  private calculateJourneyValue(steps: JourneyStep[]): number {
    // Calculate journey value based on events
    let value = 0;
    
    for (const step of steps) {
      switch (step.event_type) {
        case 'purchase':
          value += step.metadata.amount || 1000000;
          break;
        case 'signup':
          value += 500000;
          break;
        case 'add_to_cart':
          value += 100000;
          break;
        case 'product_view':
          value += 10000;
          break;
        default:
          value += 1000;
      }
    }
    
    return value;
  }

  private formatEventName(eventType: string): string {
    return eventType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }

  private getEventType(eventType: string): 'event' | 'page' | 'action' {
    if (eventType.includes('page') || eventType.includes('view')) return 'page';
    if (eventType.includes('click') || eventType.includes('purchase') || eventType.includes('signup')) return 'action';
    return 'event';
  }

  private async cacheJourneyAnalysis(request: JourneyAnalysisRequest, result: JourneyVisualization): Promise<void> {
    const cacheKey = `${this.JOURNEYS_PREFIX}analysis:${JSON.stringify(request)}`;
    await this.redis.setex(cacheKey, 3600, JSON.stringify(result)); // Cache for 1 hour
  }

  private startBackgroundProcessing(): void {
    this.isProcessing = true;
    console.log('Background journey processing started');
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const redisHealth = await this.redis.ping();
      
      return {
        status: 'healthy',
        redis: redisHealth === 'PONG' ? 'healthy' : 'unhealthy',
        isProcessing: this.isProcessing,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
