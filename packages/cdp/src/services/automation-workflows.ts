import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { CDPService, CDPError } from '../types';

export interface WorkflowTrigger {
  id: string;
  type: 'event' | 'schedule' | 'condition' | 'manual';
  name: string;
  description: string;
  config: {
    event_type?: string;
    schedule?: string; // cron expression
    conditions?: Array<{
      field: string;
      operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
      value: any;
    }>;
  };
  is_active: boolean;
  created_at: Date;
}

export interface WorkflowAction {
  id: string;
  type: 'email' | 'sms' | 'webhook' | 'update_profile' | 'add_to_segment' | 'create_task';
  name: string;
  description: string;
  config: {
    template_id?: string;
    recipient?: string;
    subject?: string;
    content?: string;
    webhook_url?: string;
    field_updates?: Record<string, any>;
    segment_id?: string;
    task_details?: Record<string, any>;
  };
  delay?: number; // seconds
  conditions?: Array<{
    field: string;
    operator: string;
    value: any;
  }>;
  created_at: Date;
}

export interface Workflow {
  id: string;
  name: string;
  description: string;
  trigger: WorkflowTrigger;
  actions: WorkflowAction[];
  is_active: boolean;
  execution_count: number;
  success_rate: number;
  last_executed: Date | null;
  created_at: Date;
  updated_at: Date;
  tags: string[];
  folder?: string;
}

export interface WorkflowExecution {
  id: string;
  workflow_id: string;
  customer_id?: string;
  trigger_data: Record<string, any>;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  started_at: Date;
  completed_at?: Date;
  error_message?: string;
  actions_executed: Array<{
    action_id: string;
    status: 'pending' | 'completed' | 'failed' | 'skipped';
    started_at: Date;
    completed_at?: Date;
    error_message?: string;
    result?: Record<string, any>;
  }>;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: 'onboarding' | 'retention' | 'upsell' | 'winback' | 'nurture';
  template_data: Workflow;
  usage_count: number;
  rating: number;
  created_at: Date;
}

/**
 * Automation Workflows Service
 * Advanced workflow automation and customer journey orchestration
 */
export class AutomationWorkflows implements CDPService {
  private readonly WORKFLOWS_PREFIX = 'cdp:workflows:';
  private readonly EXECUTIONS_PREFIX = 'cdp:executions:';
  private readonly TEMPLATES_PREFIX = 'cdp:templates:';
  
  private workflows: Map<string, Workflow> = new Map();
  private executions: Map<string, WorkflowExecution> = new Map();
  private templates: Map<string, WorkflowTemplate> = new Map();
  private isProcessing = false;
  private executionInterval?: NodeJS.Timeout;

  constructor(
    private supabase: SupabaseClient,
    private redis: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();
      
      // Load existing workflows and templates
      await this.loadWorkflows();
      await this.loadTemplates();
      await this.initializeDefaultTemplates();
      
      // Start workflow execution engine
      this.startWorkflowEngine();
      
      this.isProcessing = true;
      console.log('AutomationWorkflows initialized successfully');
    } catch (error) {
      throw new CDPError(
        'Failed to initialize AutomationWorkflows',
        'AUTOMATION_WORKFLOWS_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    this.isProcessing = false;
    
    if (this.executionInterval) {
      clearInterval(this.executionInterval);
    }
    
    console.log('AutomationWorkflows destroyed');
  }

  /**
   * Create a new workflow
   */
  async createWorkflow(workflowData: Omit<Workflow, 'id' | 'execution_count' | 'success_rate' | 'last_executed' | 'created_at' | 'updated_at'>): Promise<Workflow> {
    try {
      const workflow: Workflow = {
        id: `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...workflowData,
        execution_count: 0,
        success_rate: 0,
        last_executed: null,
        created_at: new Date(),
        updated_at: new Date()
      };
      
      this.workflows.set(workflow.id, workflow);
      await this.redis.set(`${this.WORKFLOWS_PREFIX}${workflow.id}`, JSON.stringify(workflow));
      
      return workflow;
    } catch (error) {
      throw new CDPError(
        'Failed to create workflow',
        'WORKFLOW_CREATION_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Execute workflow manually
   */
  async executeWorkflow(workflowId: string, customerId?: string, triggerData: Record<string, any> = {}): Promise<WorkflowExecution> {
    try {
      const workflow = this.workflows.get(workflowId);
      if (!workflow) {
        throw new Error(`Workflow ${workflowId} not found`);
      }

      if (!workflow.is_active) {
        throw new Error(`Workflow ${workflowId} is not active`);
      }

      const execution: WorkflowExecution = {
        id: `execution_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        workflow_id: workflowId,
        customer_id: customerId,
        trigger_data: triggerData,
        status: 'pending',
        started_at: new Date(),
        actions_executed: workflow.actions.map(action => ({
          action_id: action.id,
          status: 'pending',
          started_at: new Date()
        }))
      };

      this.executions.set(execution.id, execution);
      await this.redis.set(`${this.EXECUTIONS_PREFIX}${execution.id}`, JSON.stringify(execution));

      // Start execution
      await this.processExecution(execution);

      return execution;
    } catch (error) {
      throw new CDPError(
        'Failed to execute workflow',
        'WORKFLOW_EXECUTION_FAILED',
        { workflowId, customerId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Trigger workflows based on events
   */
  async triggerWorkflows(eventType: string, eventData: Record<string, any>): Promise<WorkflowExecution[]> {
    try {
      const executions: WorkflowExecution[] = [];
      
      // Find workflows with matching triggers
      const matchingWorkflows = Array.from(this.workflows.values()).filter(workflow => 
        workflow.is_active && 
        workflow.trigger.type === 'event' && 
        workflow.trigger.config.event_type === eventType
      );

      for (const workflow of matchingWorkflows) {
        try {
          const execution = await this.executeWorkflow(workflow.id, eventData.customer_id, eventData);
          executions.push(execution);
        } catch (error) {
          console.error(`Failed to execute workflow ${workflow.id}:`, error);
        }
      }

      return executions;
    } catch (error) {
      throw new CDPError(
        'Failed to trigger workflows',
        'WORKFLOW_TRIGGER_FAILED',
        { eventType, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Create workflow from template
   */
  async createFromTemplate(templateId: string, customizations: Partial<Workflow> = {}): Promise<Workflow> {
    try {
      const template = this.templates.get(templateId);
      if (!template) {
        throw new Error(`Template ${templateId} not found`);
      }

      const workflowData = {
        ...template.template_data,
        ...customizations,
        name: customizations.name || `${template.template_data.name} (Copy)`,
        id: undefined, // Will be generated
        execution_count: undefined,
        success_rate: undefined,
        last_executed: undefined,
        created_at: undefined,
        updated_at: undefined
      };

      const workflow = await this.createWorkflow(workflowData as any);

      // Update template usage
      template.usage_count++;
      this.templates.set(templateId, template);
      await this.redis.set(`${this.TEMPLATES_PREFIX}${templateId}`, JSON.stringify(template));

      return workflow;
    } catch (error) {
      throw new CDPError(
        'Failed to create workflow from template',
        'WORKFLOW_TEMPLATE_CREATION_FAILED',
        { templateId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get workflow analytics
   */
  getWorkflowAnalytics(workflowId: string): Record<string, any> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) return {};

    const executions = Array.from(this.executions.values()).filter(e => e.workflow_id === workflowId);
    const completedExecutions = executions.filter(e => e.status === 'completed');
    const failedExecutions = executions.filter(e => e.status === 'failed');

    return {
      workflow_id: workflowId,
      total_executions: executions.length,
      completed_executions: completedExecutions.length,
      failed_executions: failedExecutions.length,
      success_rate: executions.length > 0 ? (completedExecutions.length / executions.length) * 100 : 0,
      avg_execution_time: this.calculateAverageExecutionTime(completedExecutions),
      last_execution: executions.length > 0 ? executions[executions.length - 1].started_at : null,
      performance_trend: this.calculatePerformanceTrend(executions)
    };
  }

  /**
   * Get all workflows
   */
  getWorkflows(): Workflow[] {
    return Array.from(this.workflows.values());
  }

  /**
   * Get workflow templates
   */
  getTemplates(): WorkflowTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Get workflow executions
   */
  getExecutions(workflowId?: string): WorkflowExecution[] {
    const executions = Array.from(this.executions.values());
    return workflowId ? executions.filter(e => e.workflow_id === workflowId) : executions;
  }

  /**
   * Private helper methods
   */
  private async loadWorkflows(): Promise<void> {
    const workflowKeys = await this.redis.keys(`${this.WORKFLOWS_PREFIX}*`);
    
    for (const key of workflowKeys) {
      try {
        const workflowData = await this.redis.get(key);
        if (workflowData) {
          const workflow: Workflow = JSON.parse(workflowData);
          this.workflows.set(workflow.id, workflow);
        }
      } catch (error) {
        console.error(`Failed to load workflow ${key}:`, error);
      }
    }
  }

  private async loadTemplates(): Promise<void> {
    const templateKeys = await this.redis.keys(`${this.TEMPLATES_PREFIX}*`);
    
    for (const key of templateKeys) {
      try {
        const templateData = await this.redis.get(key);
        if (templateData) {
          const template: WorkflowTemplate = JSON.parse(templateData);
          this.templates.set(template.id, template);
        }
      } catch (error) {
        console.error(`Failed to load template ${key}:`, error);
      }
    }
  }

  private async initializeDefaultTemplates(): Promise<void> {
    const defaultTemplates: WorkflowTemplate[] = [
      {
        id: 'welcome_series',
        name: 'Welcome Email Series',
        description: 'Automated welcome email sequence for new customers',
        category: 'onboarding',
        template_data: {
          id: '',
          name: 'Welcome Email Series',
          description: 'Send a series of welcome emails to new customers',
          trigger: {
            id: 'trigger_1',
            type: 'event',
            name: 'Customer Registration',
            description: 'Triggered when a new customer registers',
            config: { event_type: 'customer_registered' },
            is_active: true,
            created_at: new Date()
          },
          actions: [
            {
              id: 'action_1',
              type: 'email',
              name: 'Welcome Email',
              description: 'Send welcome email immediately',
              config: {
                template_id: 'welcome_template',
                subject: 'Welcome to our platform!',
                content: 'Thank you for joining us...'
              },
              delay: 0,
              created_at: new Date()
            },
            {
              id: 'action_2',
              type: 'email',
              name: 'Getting Started Guide',
              description: 'Send getting started guide after 1 day',
              config: {
                template_id: 'getting_started_template',
                subject: 'Get started with our platform',
                content: 'Here\'s how to get the most out of our platform...'
              },
              delay: 86400, // 1 day
              created_at: new Date()
            }
          ],
          is_active: true,
          execution_count: 0,
          success_rate: 0,
          last_executed: null,
          created_at: new Date(),
          updated_at: new Date(),
          tags: ['onboarding', 'email', 'welcome']
        },
        usage_count: 0,
        rating: 4.8,
        created_at: new Date()
      },
      {
        id: 'churn_prevention',
        name: 'Churn Prevention Campaign',
        description: 'Automated campaign to prevent customer churn',
        category: 'retention',
        template_data: {
          id: '',
          name: 'Churn Prevention Campaign',
          description: 'Engage customers at risk of churning',
          trigger: {
            id: 'trigger_2',
            type: 'condition',
            name: 'High Churn Risk',
            description: 'Triggered when customer has high churn risk',
            config: {
              conditions: [
                { field: 'churn_risk_score', operator: 'greater_than', value: 0.7 }
              ]
            },
            is_active: true,
            created_at: new Date()
          },
          actions: [
            {
              id: 'action_3',
              type: 'email',
              name: 'Re-engagement Email',
              description: 'Send personalized re-engagement email',
              config: {
                template_id: 'reengagement_template',
                subject: 'We miss you! Here\'s a special offer',
                content: 'We noticed you haven\'t been active lately...'
              },
              delay: 0,
              created_at: new Date()
            },
            {
              id: 'action_4',
              type: 'create_task',
              name: 'Customer Success Follow-up',
              description: 'Create task for customer success team',
              config: {
                task_details: {
                  title: 'Follow up with at-risk customer',
                  priority: 'high',
                  assigned_to: 'customer_success_team'
                }
              },
              delay: 3600, // 1 hour
              created_at: new Date()
            }
          ],
          is_active: true,
          execution_count: 0,
          success_rate: 0,
          last_executed: null,
          created_at: new Date(),
          updated_at: new Date(),
          tags: ['retention', 'churn', 'email', 'task']
        },
        usage_count: 0,
        rating: 4.6,
        created_at: new Date()
      }
    ];

    for (const template of defaultTemplates) {
      if (!this.templates.has(template.id)) {
        this.templates.set(template.id, template);
        await this.redis.set(`${this.TEMPLATES_PREFIX}${template.id}`, JSON.stringify(template));
      }
    }
  }

  private startWorkflowEngine(): void {
    this.executionInterval = setInterval(async () => {
      try {
        // Process scheduled workflows
        await this.processScheduledWorkflows();
        
        // Process condition-based workflows
        await this.processConditionWorkflows();
        
        // Clean up old executions
        await this.cleanupExecutions();
      } catch (error) {
        console.error('Failed to process workflows:', error);
      }
    }, 60 * 1000); // Every minute
  }

  private async processExecution(execution: WorkflowExecution): Promise<void> {
    try {
      execution.status = 'running';
      
      const workflow = this.workflows.get(execution.workflow_id);
      if (!workflow) {
        throw new Error(`Workflow ${execution.workflow_id} not found`);
      }

      // Execute actions sequentially
      for (let i = 0; i < workflow.actions.length; i++) {
        const action = workflow.actions[i];
        const actionExecution = execution.actions_executed[i];

        try {
          // Apply delay if specified
          if (action.delay && action.delay > 0) {
            await new Promise(resolve => setTimeout(resolve, action.delay * 1000));
          }

          // Execute action
          const result = await this.executeAction(action, execution);
          
          actionExecution.status = 'completed';
          actionExecution.completed_at = new Date();
          actionExecution.result = result;
        } catch (error) {
          actionExecution.status = 'failed';
          actionExecution.completed_at = new Date();
          actionExecution.error_message = error instanceof Error ? error.message : String(error);
        }
      }

      // Update execution status
      const failedActions = execution.actions_executed.filter(a => a.status === 'failed');
      execution.status = failedActions.length === 0 ? 'completed' : 'failed';
      execution.completed_at = new Date();

      // Update workflow statistics
      workflow.execution_count++;
      workflow.last_executed = new Date();
      
      const allExecutions = Array.from(this.executions.values()).filter(e => e.workflow_id === workflow.id);
      const successfulExecutions = allExecutions.filter(e => e.status === 'completed');
      workflow.success_rate = (successfulExecutions.length / allExecutions.length) * 100;

      // Save updates
      this.workflows.set(workflow.id, workflow);
      this.executions.set(execution.id, execution);
      
      await this.redis.set(`${this.WORKFLOWS_PREFIX}${workflow.id}`, JSON.stringify(workflow));
      await this.redis.set(`${this.EXECUTIONS_PREFIX}${execution.id}`, JSON.stringify(execution));

    } catch (error) {
      execution.status = 'failed';
      execution.completed_at = new Date();
      execution.error_message = error instanceof Error ? error.message : String(error);
      
      this.executions.set(execution.id, execution);
      await this.redis.set(`${this.EXECUTIONS_PREFIX}${execution.id}`, JSON.stringify(execution));
    }
  }

  private async executeAction(action: WorkflowAction, execution: WorkflowExecution): Promise<Record<string, any>> {
    // Simulate action execution
    switch (action.type) {
      case 'email':
        return { sent: true, message_id: `msg_${Date.now()}` };
      case 'sms':
        return { sent: true, message_id: `sms_${Date.now()}` };
      case 'webhook':
        return { called: true, response_code: 200 };
      case 'update_profile':
        return { updated: true, fields: Object.keys(action.config.field_updates || {}) };
      case 'add_to_segment':
        return { added: true, segment_id: action.config.segment_id };
      case 'create_task':
        return { created: true, task_id: `task_${Date.now()}` };
      default:
        return { executed: true };
    }
  }

  private async processScheduledWorkflows(): Promise<void> {
    // Process workflows with schedule triggers
    const scheduledWorkflows = Array.from(this.workflows.values()).filter(w => 
      w.is_active && w.trigger.type === 'schedule'
    );

    for (const workflow of scheduledWorkflows) {
      // Check if workflow should run based on schedule
      // This is a simplified implementation
      const shouldRun = Math.random() < 0.1; // 10% chance for demo
      
      if (shouldRun) {
        try {
          await this.executeWorkflow(workflow.id, undefined, { scheduled: true });
        } catch (error) {
          console.error(`Failed to execute scheduled workflow ${workflow.id}:`, error);
        }
      }
    }
  }

  private async processConditionWorkflows(): Promise<void> {
    // Process workflows with condition triggers
    const conditionWorkflows = Array.from(this.workflows.values()).filter(w => 
      w.is_active && w.trigger.type === 'condition'
    );

    for (const workflow of conditionWorkflows) {
      // Check conditions against customer data
      // This is a simplified implementation
      const conditionsMet = Math.random() < 0.05; // 5% chance for demo
      
      if (conditionsMet) {
        try {
          await this.executeWorkflow(workflow.id, 'customer_123', { condition_triggered: true });
        } catch (error) {
          console.error(`Failed to execute condition workflow ${workflow.id}:`, error);
        }
      }
    }
  }

  private async cleanupExecutions(): Promise<void> {
    // Remove executions older than 30 days
    const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    for (const [id, execution] of this.executions.entries()) {
      if (execution.started_at < cutoffDate) {
        this.executions.delete(id);
        await this.redis.del(`${this.EXECUTIONS_PREFIX}${id}`);
      }
    }
  }

  private calculateAverageExecutionTime(executions: WorkflowExecution[]): number {
    if (executions.length === 0) return 0;
    
    const totalTime = executions.reduce((sum, execution) => {
      if (execution.completed_at) {
        return sum + (execution.completed_at.getTime() - execution.started_at.getTime());
      }
      return sum;
    }, 0);
    
    return totalTime / executions.length / 1000; // Return in seconds
  }

  private calculatePerformanceTrend(executions: WorkflowExecution[]): 'improving' | 'declining' | 'stable' {
    if (executions.length < 10) return 'stable';
    
    const recent = executions.slice(-5);
    const previous = executions.slice(-10, -5);
    
    const recentSuccessRate = recent.filter(e => e.status === 'completed').length / recent.length;
    const previousSuccessRate = previous.filter(e => e.status === 'completed').length / previous.length;
    
    if (recentSuccessRate > previousSuccessRate + 0.1) return 'improving';
    if (recentSuccessRate < previousSuccessRate - 0.1) return 'declining';
    return 'stable';
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const redisHealth = await this.redis.ping();
      
      return {
        status: 'healthy',
        redis: redisHealth === 'PONG' ? 'healthy' : 'unhealthy',
        workflows_count: this.workflows.size,
        active_workflows: Array.from(this.workflows.values()).filter(w => w.is_active).length,
        executions_count: this.executions.size,
        templates_count: this.templates.size,
        is_processing: this.isProcessing,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
