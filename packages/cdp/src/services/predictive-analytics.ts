import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { CDPService, CDPError } from '../types';

export interface PredictiveModel {
  id: string;
  name: string;
  type: 'churn_prediction' | 'ltv_prediction' | 'engagement_prediction' | 'conversion_prediction';
  version: string;
  accuracy: number;
  features: string[];
  created_at: Date;
  updated_at: Date;
}

export interface PredictionResult {
  customer_id: string;
  model_type: string;
  prediction: number;
  confidence: number;
  features_used: Record<string, number>;
  explanation: string[];
  created_at: Date;
}

export interface ModelTrainingData {
  customer_id: string;
  features: Record<string, number>;
  target: number;
  timestamp: Date;
}

/**
 * Predictive Analytics Service
 * Provides ML-powered predictions for customer behavior
 */
export class PredictiveAnalytics implements CDPService {
  private readonly MODELS_PREFIX = 'cdp:models:';
  private readonly PREDICTIONS_PREFIX = 'cdp:predictions:';
  private readonly TRAINING_PREFIX = 'cdp:training:';

  private models: Map<string, PredictiveModel> = new Map();
  private isTraining = false;

  constructor(
    private supabase: SupabaseClient,
    private redis: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();

      // Load existing models
      await this.loadModels();

      // Initialize default models
      await this.initializeDefaultModels();

      // Start background training
      this.startBackgroundTraining();

      console.log('PredictiveAnalytics initialized successfully');
    } catch (error) {
      throw new CDPError(
        'Failed to initialize PredictiveAnalytics',
        'PREDICTIVE_ANALYTICS_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    this.isTraining = false;
    console.log('PredictiveAnalytics destroyed');
  }

  /**
   * Predict customer churn risk
   */
  async predictChurnRisk(customerId: string): Promise<PredictionResult> {
    try {
      // Get customer features
      const features = await this.extractCustomerFeatures(customerId);

      // Apply churn prediction model
      const prediction = await this.applyChurnModel(features);

      // Generate explanation
      const explanation = this.generateChurnExplanation(features, prediction);

      const result: PredictionResult = {
        customer_id: customerId,
        model_type: 'churn_prediction',
        prediction: prediction.score,
        confidence: prediction.confidence,
        features_used: features,
        explanation,
        created_at: new Date()
      };

      // Cache prediction
      await this.cachePrediction(result);

      return result;
    } catch (error) {
      throw new CDPError(
        'Failed to predict churn risk',
        'CHURN_PREDICTION_FAILED',
        { customerId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Predict customer lifetime value
   */
  async predictLifetimeValue(customerId: string): Promise<PredictionResult> {
    try {
      const features = await this.extractCustomerFeatures(customerId);
      const prediction = await this.applyLTVModel(features);
      const explanation = this.generateLTVExplanation(features, prediction);

      const result: PredictionResult = {
        customer_id: customerId,
        model_type: 'ltv_prediction',
        prediction: prediction.score,
        confidence: prediction.confidence,
        features_used: features,
        explanation,
        created_at: new Date()
      };

      await this.cachePrediction(result);
      return result;
    } catch (error) {
      throw new CDPError(
        'Failed to predict lifetime value',
        'LTV_PREDICTION_FAILED',
        { customerId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Predict engagement likelihood
   */
  async predictEngagement(customerId: string): Promise<PredictionResult> {
    try {
      const features = await this.extractCustomerFeatures(customerId);
      const prediction = await this.applyEngagementModel(features);
      const explanation = this.generateEngagementExplanation(features, prediction);

      const result: PredictionResult = {
        customer_id: customerId,
        model_type: 'engagement_prediction',
        prediction: prediction.score,
        confidence: prediction.confidence,
        features_used: features,
        explanation,
        created_at: new Date()
      };

      await this.cachePrediction(result);
      return result;
    } catch (error) {
      throw new CDPError(
        'Failed to predict engagement',
        'ENGAGEMENT_PREDICTION_FAILED',
        { customerId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get batch predictions for multiple customers
   */
  async getBatchPredictions(
    customerIds: string[],
    modelType: string
  ): Promise<PredictionResult[]> {
    const results: PredictionResult[] = [];

    // Process in chunks to avoid overwhelming the system
    const chunkSize = 50;
    for (let i = 0; i < customerIds.length; i += chunkSize) {
      const chunk = customerIds.slice(i, i + chunkSize);

      const chunkResults = await Promise.all(
        chunk.map(async (customerId) => {
          try {
            switch (modelType) {
              case 'churn_prediction':
                return await this.predictChurnRisk(customerId);
              case 'ltv_prediction':
                return await this.predictLifetimeValue(customerId);
              case 'engagement_prediction':
                return await this.predictEngagement(customerId);
              default:
                throw new Error(`Unknown model type: ${modelType}`);
            }
          } catch (error) {
            console.error(`Prediction failed for customer ${customerId}:`, error);
            return null;
          }
        })
      );

      results.push(...chunkResults.filter(result => result !== null) as PredictionResult[]);
    }

    return results;
  }

  /**
   * Train model with new data
   */
  async trainModel(modelType: string, trainingData: ModelTrainingData[]): Promise<PredictiveModel> {
    try {
      this.isTraining = true;

      // Prepare training dataset
      const dataset = this.prepareTrainingDataset(trainingData);

      // Train model (simplified implementation)
      const model = await this.performModelTraining(modelType, dataset);

      // Validate model performance
      const validation = await this.validateModel(model, dataset);

      // Update model if performance is good
      if (validation.accuracy > 0.7) {
        this.models.set(modelType, model);
        await this.saveModel(model);
      }

      this.isTraining = false;
      return model;
    } catch (error) {
      this.isTraining = false;
      throw new CDPError(
        'Failed to train model',
        'MODEL_TRAINING_FAILED',
        { modelType, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get model performance metrics
   */
  async getModelPerformance(modelType: string): Promise<any> {
    const model = this.models.get(modelType);
    if (!model) {
      throw new Error(`Model not found: ${modelType}`);
    }

    // Get recent predictions for evaluation
    const recentPredictions = await this.getRecentPredictions(modelType, 1000);

    // Calculate performance metrics
    const metrics = this.calculatePerformanceMetrics(recentPredictions);

    return {
      model: {
        name: model.name,
        type: model.type,
        version: model.version,
        accuracy: model.accuracy,
        features: model.features
      },
      performance: metrics,
      lastUpdated: model.updated_at
    };
  }

  /**
   * Extract customer features for ML models
   */
  private async extractCustomerFeatures(customerId: string): Promise<Record<string, number>> {
    try {
      // Get customer profile data
      const { data: profile } = await this.supabase
        .from('customer_profiles')
        .select('*')
        .eq('id', customerId)
        .single();

      if (!profile) {
        throw new Error('Customer profile not found');
      }

      // Get customer events for behavioral features
      const { data: events } = await this.supabase
        .from('analytics_events')
        .select('event_type, event_data, timestamp')
        .eq('customer_profile_id', customerId)
        .order('timestamp', { ascending: false })
        .limit(1000);

      // Extract features
      const features = this.calculateFeatures(profile, events || []);

      return features;
    } catch (error) {
      console.error('Feature extraction failed:', error);
      return this.getDefaultFeatures();
    }
  }

  /**
   * Calculate features from customer data
   */
  private calculateFeatures(profile: any, events: any[]): Record<string, number> {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);

    // Recent events (last 30 days)
    const recentEvents = events.filter(e => new Date(e.timestamp) >= thirtyDaysAgo);
    const oldEvents = events.filter(e => new Date(e.timestamp) >= ninetyDaysAgo && new Date(e.timestamp) < thirtyDaysAgo);

    // Calculate behavioral features
    const features: Record<string, number> = {
      // Recency features
      days_since_last_activity: profile.last_activity_at
        ? Math.floor((now.getTime() - new Date(profile.last_activity_at).getTime()) / (24 * 60 * 60 * 1000))
        : 365,

      // Frequency features
      total_events_30d: recentEvents.length,
      total_events_90d: events.length,
      avg_events_per_day: recentEvents.length / 30,

      // Monetary features
      total_revenue: profile.total_revenue || 0,
      avg_order_value: profile.total_purchases > 0 ? (profile.total_revenue || 0) / profile.total_purchases : 0,
      total_purchases: profile.total_purchases || 0,

      // Engagement features
      email_open_rate: profile.email_engagement?.open_rate || 0,
      email_click_rate: profile.email_engagement?.click_rate || 0,
      website_sessions: profile.total_sessions || 0,

      // Lifecycle features
      customer_age_days: profile.created_at
        ? Math.floor((now.getTime() - new Date(profile.created_at).getTime()) / (24 * 60 * 60 * 1000))
        : 0,

      // Trend features
      activity_trend: recentEvents.length > 0 && oldEvents.length > 0
        ? recentEvents.length / oldEvents.length
        : recentEvents.length > 0 ? 2 : 0,

      // Behavioral diversity
      unique_event_types: new Set(recentEvents.map(e => e.event_type)).size,

      // Value tier
      value_tier: this.calculateValueTier(profile.total_revenue || 0),

      // Engagement score
      engagement_score: profile.engagement_score || 0.5
    };

    return features;
  }

  /**
   * Apply churn prediction model
   */
  private async applyChurnModel(features: Record<string, number>): Promise<{ score: number; confidence: number }> {
    // Simplified churn prediction logic
    // In production, this would use a trained ML model

    const weights = {
      days_since_last_activity: -0.05,
      total_events_30d: 0.02,
      activity_trend: 0.3,
      engagement_score: 0.4,
      email_open_rate: 0.2,
      total_revenue: 0.0001
    };

    let score = 0.5; // Base churn risk
    let confidence = 0.8;

    for (const [feature, weight] of Object.entries(weights)) {
      if (features[feature] !== undefined) {
        score += features[feature] * weight;
      }
    }

    // Normalize score to 0-1 range
    score = Math.max(0, Math.min(1, score));

    // Adjust confidence based on data quality
    const dataQuality = Object.keys(features).length / 10; // Assuming 10 ideal features
    confidence *= Math.min(1, dataQuality);

    return { score, confidence };
  }

  /**
   * Apply LTV prediction model
   */
  private async applyLTVModel(features: Record<string, number>): Promise<{ score: number; confidence: number }> {
    // Simplified LTV prediction
    const weights = {
      total_revenue: 1.5,
      avg_order_value: 2.0,
      total_purchases: 0.1,
      customer_age_days: 0.001,
      engagement_score: 1000000,
      activity_trend: 500000
    };

    let score = features.total_revenue || 0; // Base LTV is current revenue
    let confidence = 0.7;

    for (const [feature, weight] of Object.entries(weights)) {
      if (features[feature] !== undefined && feature !== 'total_revenue') {
        score += features[feature] * weight;
      }
    }

    // Ensure minimum LTV
    score = Math.max(0, score);

    const dataQuality = Object.keys(features).length / 10;
    confidence *= Math.min(1, dataQuality);

    return { score, confidence };
  }

  /**
   * Apply engagement prediction model
   */
  private async applyEngagementModel(features: Record<string, number>): Promise<{ score: number; confidence: number }> {
    // Simplified engagement prediction
    const weights = {
      total_events_30d: 0.05,
      email_open_rate: 0.4,
      email_click_rate: 0.3,
      activity_trend: 0.2,
      unique_event_types: 0.05
    };

    let score = 0.5; // Base engagement
    let confidence = 0.75;

    for (const [feature, weight] of Object.entries(weights)) {
      if (features[feature] !== undefined) {
        score += features[feature] * weight;
      }
    }

    score = Math.max(0, Math.min(1, score));

    const dataQuality = Object.keys(features).length / 10;
    confidence *= Math.min(1, dataQuality);

    return { score, confidence };
  }

  /**
   * Generate explanation for churn prediction
   */
  private generateChurnExplanation(features: Record<string, number>, prediction: { score: number; confidence: number }): string[] {
    const explanations: string[] = [];

    if ((features.days_since_last_activity || 0) > 30) {
      explanations.push(`Customer hasn't been active for ${features.days_since_last_activity || 0} days`);
    }

    if ((features.activity_trend || 0) < 0.5) {
      explanations.push('Recent activity has decreased compared to previous period');
    }

    if ((features.engagement_score || 0) < 0.3) {
      explanations.push('Low engagement score indicates reduced interest');
    }

    if ((features.email_open_rate || 0) < 0.2) {
      explanations.push('Low email engagement suggests disconnection');
    }

    if (prediction.score > 0.7) {
      explanations.push('High churn risk - immediate intervention recommended');
    } else if (prediction.score > 0.4) {
      explanations.push('Moderate churn risk - monitor closely');
    } else {
      explanations.push('Low churn risk - customer appears stable');
    }

    return explanations;
  }

  /**
   * Generate explanation for LTV prediction
   */
  private generateLTVExplanation(features: Record<string, number>, prediction: { score: number; confidence: number }): string[] {
    const explanations: string[] = [];

    if ((features.total_revenue || 0) > 1000000) {
      explanations.push('High historical revenue indicates strong value potential');
    }

    if ((features.avg_order_value || 0) > 500000) {
      explanations.push('High average order value suggests premium customer');
    }

    if ((features.engagement_score || 0) > 0.7) {
      explanations.push('High engagement score indicates continued value potential');
    }

    if ((features.activity_trend || 0) > 1.2) {
      explanations.push('Increasing activity trend suggests growing value');
    }

    explanations.push(`Predicted lifetime value: ${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(prediction.score)}`);

    return explanations;
  }

  /**
   * Generate explanation for engagement prediction
   */
  private generateEngagementExplanation(features: Record<string, number>, prediction: { score: number; confidence: number }): string[] {
    const explanations: string[] = [];

    if ((features.email_open_rate || 0) > 0.5) {
      explanations.push('High email open rate indicates strong engagement');
    }

    if ((features.total_events_30d || 0) > 50) {
      explanations.push('High activity level shows active engagement');
    }

    if ((features.unique_event_types || 0) > 5) {
      explanations.push('Diverse interaction types indicate deep engagement');
    }

    if (prediction.score > 0.7) {
      explanations.push('High engagement likelihood - excellent for campaigns');
    } else if (prediction.score > 0.4) {
      explanations.push('Moderate engagement likelihood - targeted approach recommended');
    } else {
      explanations.push('Low engagement likelihood - re-engagement strategy needed');
    }

    return explanations;
  }

  /**
   * Calculate value tier
   */
  private calculateValueTier(revenue: number): number {
    if (revenue >= 10000000) return 4; // VIP
    if (revenue >= 5000000) return 3;  // High
    if (revenue >= 1000000) return 2;  // Medium
    if (revenue > 0) return 1;         // Low
    return 0; // No revenue
  }

  /**
   * Get default features when extraction fails
   */
  private getDefaultFeatures(): Record<string, number> {
    return {
      days_since_last_activity: 30,
      total_events_30d: 0,
      total_events_90d: 0,
      avg_events_per_day: 0,
      total_revenue: 0,
      avg_order_value: 0,
      total_purchases: 0,
      email_open_rate: 0,
      email_click_rate: 0,
      website_sessions: 0,
      customer_age_days: 0,
      activity_trend: 0,
      unique_event_types: 0,
      value_tier: 0,
      engagement_score: 0.5
    };
  }

  /**
   * Cache prediction result
   */
  private async cachePrediction(result: PredictionResult): Promise<void> {
    const key = `${this.PREDICTIONS_PREFIX}${result.model_type}:${result.customer_id}`;
    await this.redis.setex(key, 3600, JSON.stringify(result)); // Cache for 1 hour
  }

  /**
   * Load existing models
   */
  private async loadModels(): Promise<void> {
    // Load models from Redis or initialize defaults
    console.log('Loading ML models...');
  }

  /**
   * Initialize default models
   */
  private async initializeDefaultModels(): Promise<void> {
    const defaultModels: PredictiveModel[] = [
      {
        id: 'churn_v1',
        name: 'Churn Prediction Model v1',
        type: 'churn_prediction',
        version: '1.0.0',
        accuracy: 0.82,
        features: ['days_since_last_activity', 'activity_trend', 'engagement_score'],
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 'ltv_v1',
        name: 'Lifetime Value Prediction Model v1',
        type: 'ltv_prediction',
        version: '1.0.0',
        accuracy: 0.75,
        features: ['total_revenue', 'avg_order_value', 'engagement_score'],
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 'engagement_v1',
        name: 'Engagement Prediction Model v1',
        type: 'engagement_prediction',
        version: '1.0.0',
        accuracy: 0.78,
        features: ['email_open_rate', 'total_events_30d', 'activity_trend'],
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    for (const model of defaultModels) {
      this.models.set(model.type, model);
    }
  }

  /**
   * Start background training
   */
  private startBackgroundTraining(): void {
    // Placeholder for background model training
    console.log('Background model training started');
  }

  /**
   * Prepare training dataset
   */
  private prepareTrainingDataset(data: ModelTrainingData[]): any {
    return data; // Simplified
  }

  /**
   * Perform model training
   */
  private async performModelTraining(modelType: string, dataset: any): Promise<PredictiveModel> {
    // Simplified training - in production would use actual ML libraries
    const existingModel = this.models.get(modelType);
    if (existingModel) {
      return existingModel;
    }

    const firstModel = this.models.values().next().value;
    if (!firstModel) {
      throw new Error('No models available');
    }

    return firstModel;
  }

  /**
   * Validate model performance
   */
  private async validateModel(model: PredictiveModel, dataset: any): Promise<{ accuracy: number }> {
    return { accuracy: 0.8 }; // Simplified
  }

  /**
   * Save model
   */
  private async saveModel(model: PredictiveModel): Promise<void> {
    const key = `${this.MODELS_PREFIX}${model.type}`;
    await this.redis.set(key, JSON.stringify(model));
  }

  /**
   * Get recent predictions
   */
  private async getRecentPredictions(modelType: string, limit: number): Promise<PredictionResult[]> {
    // Simplified - would query actual prediction history
    return [];
  }

  /**
   * Calculate performance metrics
   */
  private calculatePerformanceMetrics(predictions: PredictionResult[]): any {
    return {
      accuracy: 0.8,
      precision: 0.75,
      recall: 0.82,
      f1Score: 0.78
    };
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const redisHealth = await this.redis.ping();

      return {
        status: 'healthy',
        redis: redisHealth === 'PONG' ? 'healthy' : 'unhealthy',
        modelsLoaded: this.models.size,
        isTraining: this.isTraining,
        availableModels: Array.from(this.models.keys()),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
