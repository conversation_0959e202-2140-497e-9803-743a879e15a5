import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { CDPService, CDPError } from '../types';
import { BaseIntegration, IntegrationConfig, SyncConfig, SyncResult, WebhookPayload } from '../integrations/base-integration';
import { SendGridIntegration } from '../integrations/email/sendgrid-integration';
import { HubSpotIntegration } from '../integrations/crm/hubspot-integration';
import { GoogleAnalyticsIntegration } from '../integrations/analytics/google-analytics-integration';

export interface IntegrationRegistry {
  [key: string]: {
    name: string;
    type: 'email' | 'crm' | 'analytics' | 'ecommerce' | 'social' | 'advertising';
    description: string;
    provider: string;
    class: new (config: IntegrationConfig, syncConfig: SyncConfig) => BaseIntegration;
    required_credentials: string[];
    optional_credentials: string[];
    supported_sync_directions: ('import' | 'export' | 'bidirectional')[];
    webhook_supported: boolean;
  };
}

export interface IntegrationHealth {
  integration_id: string;
  status: 'healthy' | 'warning' | 'error';
  last_check: Date;
  response_time_ms: number;
  error_count_24h: number;
  success_rate_24h: number;
  last_sync: Date | null;
  next_sync: Date | null;
}

export interface SyncSchedule {
  integration_id: string;
  frequency: 'realtime' | 'hourly' | 'daily' | 'weekly' | 'manual';
  enabled: boolean;
  last_run: Date | null;
  next_run: Date | null;
  cron_expression?: string;
}

/**
 * Integration Manager Service
 * Manages all third-party integrations, sync schedules, and health monitoring
 */
export class IntegrationManager implements CDPService {
  private readonly INTEGRATIONS_PREFIX = 'cdp:integrations:';
  private readonly HEALTH_PREFIX = 'cdp:integration_health:';
  private readonly SCHEDULES_PREFIX = 'cdp:sync_schedules:';

  private integrations: Map<string, BaseIntegration> = new Map();
  private healthChecks: Map<string, IntegrationHealth> = new Map();
  private syncSchedules: Map<string, SyncSchedule> = new Map();
  private isProcessing = false;
  private healthCheckInterval?: NodeJS.Timeout;
  private syncInterval?: NodeJS.Timeout;

  // Integration registry
  private readonly registry: IntegrationRegistry = {
    sendgrid: {
      name: 'SendGrid',
      type: 'email',
      description: 'Email marketing and transactional email service',
      provider: 'sendgrid',
      class: SendGridIntegration as any,
      required_credentials: ['api_key'],
      optional_credentials: ['webhook_secret'],
      supported_sync_directions: ['import', 'export', 'bidirectional'],
      webhook_supported: true
    },
    hubspot: {
      name: 'HubSpot',
      type: 'crm',
      description: 'Customer relationship management platform',
      provider: 'hubspot',
      class: HubSpotIntegration as any,
      required_credentials: ['access_token'],
      optional_credentials: ['refresh_token', 'webhook_secret'],
      supported_sync_directions: ['import', 'export', 'bidirectional'],
      webhook_supported: true
    },
    google_analytics: {
      name: 'Google Analytics',
      type: 'analytics',
      description: 'Web analytics and measurement platform',
      provider: 'google',
      class: GoogleAnalyticsIntegration as any,
      required_credentials: ['measurement_id', 'api_secret'],
      optional_credentials: ['service_account_key', 'property_id'],
      supported_sync_directions: ['export'],
      webhook_supported: false
    }
  };

  constructor(
    private supabase: SupabaseClient,
    private redis: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();

      // Load existing integrations
      await this.loadIntegrations();

      // Start health monitoring
      this.startHealthMonitoring();

      // Start sync scheduler
      this.startSyncScheduler();

      this.isProcessing = true;
      console.log('IntegrationManager initialized successfully');
    } catch (error) {
      throw new CDPError(
        'Failed to initialize IntegrationManager',
        'INTEGRATION_MANAGER_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    this.isProcessing = false;

    // Stop intervals
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    // Destroy all integrations
    for (const integration of this.integrations.values()) {
      await integration.destroy();
    }

    console.log('IntegrationManager destroyed');
  }

  /**
   * Get available integration types
   */
  getAvailableIntegrations(): IntegrationRegistry {
    return { ...this.registry };
  }

  /**
   * Create new integration
   */
  async createIntegration(
    type: string,
    config: Omit<IntegrationConfig, 'id' | 'type' | 'provider' | 'created_at' | 'updated_at'>,
    syncConfig: SyncConfig
  ): Promise<IntegrationConfig> {
    try {
      const registryEntry = this.registry[type];
      if (!registryEntry) {
        throw new Error(`Integration type '${type}' not supported`);
      }

      // Validate required credentials
      for (const credential of registryEntry.required_credentials) {
        if (!config.credentials[credential]) {
          throw new Error(`Required credential '${credential}' is missing`);
        }
      }

      const integrationConfig: IntegrationConfig = {
        ...config,
        id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: registryEntry.type,
        provider: registryEntry.provider,
        created_at: new Date(),
        updated_at: new Date()
      };

      // Create integration instance
      const IntegrationClass = registryEntry.class;
      const integration = new IntegrationClass(integrationConfig, syncConfig);

      // Initialize integration
      await integration.initialize();

      // Store integration
      this.integrations.set(integrationConfig.id, integration);
      await this.saveIntegrationConfig(integrationConfig);
      await this.saveSyncConfig(integrationConfig.id, syncConfig);

      // Create sync schedule
      await this.createSyncSchedule(integrationConfig.id, syncConfig.frequency);

      return integrationConfig;
    } catch (error) {
      throw new CDPError(
        'Failed to create integration',
        'INTEGRATION_CREATE_FAILED',
        { type, config, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Update integration
   */
  async updateIntegration(
    integrationId: string,
    updates: Partial<IntegrationConfig>
  ): Promise<IntegrationConfig> {
    try {
      const integration = this.integrations.get(integrationId);
      if (!integration) {
        throw new Error('Integration not found');
      }

      await integration.updateConfig(updates);
      const updatedConfig = integration.getConfig();

      await this.saveIntegrationConfig(updatedConfig);

      return updatedConfig;
    } catch (error) {
      throw new CDPError(
        'Failed to update integration',
        'INTEGRATION_UPDATE_FAILED',
        { integrationId, updates, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Delete integration
   */
  async deleteIntegration(integrationId: string): Promise<void> {
    try {
      const integration = this.integrations.get(integrationId);
      if (integration) {
        await integration.destroy();
        this.integrations.delete(integrationId);
      }

      // Remove from storage
      await this.redis.del(`${this.INTEGRATIONS_PREFIX}${integrationId}`);
      await this.redis.del(`${this.HEALTH_PREFIX}${integrationId}`);
      await this.redis.del(`${this.SCHEDULES_PREFIX}${integrationId}`);

      // Remove from health checks and schedules
      this.healthChecks.delete(integrationId);
      this.syncSchedules.delete(integrationId);
    } catch (error) {
      throw new CDPError(
        'Failed to delete integration',
        'INTEGRATION_DELETE_FAILED',
        { integrationId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get integration by ID
   */
  getIntegration(integrationId: string): BaseIntegration | null {
    return this.integrations.get(integrationId) || null;
  }

  /**
   * Get all integrations
   */
  getAllIntegrations(): BaseIntegration[] {
    return Array.from(this.integrations.values());
  }

  /**
   * Start data sync for integration
   */
  async syncIntegration(
    integrationId: string,
    direction?: 'import' | 'export'
  ): Promise<SyncResult> {
    try {
      const integration = this.integrations.get(integrationId);
      if (!integration) {
        throw new Error('Integration not found');
      }

      const result = await integration.startSync(direction);

      // Update sync schedule
      const schedule = this.syncSchedules.get(integrationId);
      if (schedule) {
        schedule.last_run = new Date();
        schedule.next_run = this.calculateNextRun(schedule.frequency);
        await this.saveSyncSchedule(schedule);
      }

      return result;
    } catch (error) {
      throw new CDPError(
        'Failed to sync integration',
        'INTEGRATION_SYNC_FAILED',
        { integrationId, direction, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Handle webhook from integration
   */
  async handleWebhook(integrationId: string, payload: WebhookPayload): Promise<void> {
    try {
      const integration = this.integrations.get(integrationId);
      if (!integration) {
        throw new Error('Integration not found');
      }

      await integration.handleWebhook(payload);
    } catch (error) {
      throw new CDPError(
        'Failed to handle webhook',
        'WEBHOOK_HANDLE_FAILED',
        { integrationId, payload, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get integration health status
   */
  getIntegrationHealth(integrationId: string): IntegrationHealth | null {
    return this.healthChecks.get(integrationId) || null;
  }

  /**
   * Get all integration health statuses
   */
  getAllIntegrationHealth(): IntegrationHealth[] {
    return Array.from(this.healthChecks.values());
  }

  /**
   * Get sync schedule for integration
   */
  getSyncSchedule(integrationId: string): SyncSchedule | null {
    return this.syncSchedules.get(integrationId) || null;
  }

  /**
   * Update sync schedule
   */
  async updateSyncSchedule(
    integrationId: string,
    updates: Partial<SyncSchedule>
  ): Promise<SyncSchedule> {
    try {
      const schedule = this.syncSchedules.get(integrationId);
      if (!schedule) {
        throw new Error('Sync schedule not found');
      }

      const updatedSchedule = { ...schedule, ...updates };

      if (updates.frequency) {
        updatedSchedule.next_run = this.calculateNextRun(updates.frequency);
      }

      this.syncSchedules.set(integrationId, updatedSchedule);
      await this.saveSyncSchedule(updatedSchedule);

      return updatedSchedule;
    } catch (error) {
      throw new CDPError(
        'Failed to update sync schedule',
        'SYNC_SCHEDULE_UPDATE_FAILED',
        { integrationId, updates, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Private helper methods
   */
  private async loadIntegrations(): Promise<void> {
    const integrationKeys = await this.redis.keys(`${this.INTEGRATIONS_PREFIX}*`);

    for (const key of integrationKeys) {
      try {
        const configData = await this.redis.get(key);
        if (!configData) continue;

        const config: IntegrationConfig = JSON.parse(configData);
        const syncConfigData = await this.redis.get(`sync_config:${config.id}`);
        const syncConfig: SyncConfig = syncConfigData ? JSON.parse(syncConfigData) : this.getDefaultSyncConfig();

        const registryEntry = this.registry[config.provider];
        if (!registryEntry) continue;

        const IntegrationClass = registryEntry.class;
        const integration = new IntegrationClass(config, syncConfig);

        if (config.enabled) {
          await integration.initialize();
        }

        this.integrations.set(config.id, integration);
      } catch (error) {
        console.error(`Failed to load integration ${key}:`, error);
      }
    }
  }

  private async saveIntegrationConfig(config: IntegrationConfig): Promise<void> {
    await this.redis.set(`${this.INTEGRATIONS_PREFIX}${config.id}`, JSON.stringify(config));
  }

  private async saveSyncConfig(integrationId: string, syncConfig: SyncConfig): Promise<void> {
    await this.redis.set(`sync_config:${integrationId}`, JSON.stringify(syncConfig));
  }

  private async createSyncSchedule(integrationId: string, frequency: string): Promise<void> {
    const schedule: SyncSchedule = {
      integration_id: integrationId,
      frequency: frequency as any,
      enabled: true,
      last_run: null,
      next_run: this.calculateNextRun(frequency as any)
    };

    this.syncSchedules.set(integrationId, schedule);
    await this.saveSyncSchedule(schedule);
  }

  private async saveSyncSchedule(schedule: SyncSchedule): Promise<void> {
    await this.redis.set(`${this.SCHEDULES_PREFIX}${schedule.integration_id}`, JSON.stringify(schedule));
  }

  private calculateNextRun(frequency: 'realtime' | 'hourly' | 'daily' | 'weekly' | 'manual'): Date | null {
    if (frequency === 'manual' || frequency === 'realtime') {
      return null;
    }

    const now = new Date();

    switch (frequency) {
      case 'hourly':
        return new Date(now.getTime() + 60 * 60 * 1000);
      case 'daily':
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
      case 'weekly':
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      default:
        return null;
    }
  }

  private getDefaultSyncConfig(): SyncConfig {
    return {
      direction: 'import',
      frequency: 'daily',
      batch_size: 1000,
      field_mappings: []
    };
  }

  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(async () => {
      for (const [id, integration] of this.integrations.entries()) {
        try {
          const startTime = Date.now();
          const health = await integration.getHealthStatus();
          const responseTime = Date.now() - startTime;

          const healthStatus: IntegrationHealth = {
            integration_id: id,
            status: health.status === 'healthy' ? 'healthy' : 'error',
            last_check: new Date(),
            response_time_ms: responseTime,
            error_count_24h: 0, // Would be calculated from logs
            success_rate_24h: 95, // Would be calculated from logs
            last_sync: health.last_sync ? new Date(health.last_sync) : null,
            next_sync: null // Would be calculated from schedule
          };

          this.healthChecks.set(id, healthStatus);
          await this.redis.set(`${this.HEALTH_PREFIX}${id}`, JSON.stringify(healthStatus));
        } catch (error) {
          console.error(`Health check failed for integration ${id}:`, error);
        }
      }
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  private startSyncScheduler(): void {
    this.syncInterval = setInterval(async () => {
      const now = new Date();

      for (const schedule of this.syncSchedules.values()) {
        if (!schedule.enabled || !schedule.next_run) continue;

        if (schedule.next_run <= now) {
          try {
            await this.syncIntegration(schedule.integration_id);
          } catch (error) {
            console.error(`Scheduled sync failed for integration ${schedule.integration_id}:`, error);
          }
        }
      }
    }, 60 * 1000); // Every minute
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const redisHealth = await this.redis.ping();

      return {
        status: 'healthy',
        redis: redisHealth === 'PONG' ? 'healthy' : 'unhealthy',
        integrations_count: this.integrations.size,
        active_integrations: Array.from(this.integrations.values()).filter(i => i.getConfig().enabled).length,
        health_checks_count: this.healthChecks.size,
        sync_schedules_count: this.syncSchedules.size,
        is_processing: this.isProcessing,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
