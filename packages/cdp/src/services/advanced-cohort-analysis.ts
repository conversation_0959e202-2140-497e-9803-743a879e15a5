import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { CDPService, CDPError } from '../types';

export interface CohortDefinition {
  id: string;
  name: string;
  description: string;
  criteria: {
    event_type?: string;
    time_period: 'daily' | 'weekly' | 'monthly';
    date_range: { start: Date; end: Date };
    filters?: Record<string, any>;
  };
  created_at: Date;
}

export interface CohortData {
  cohort_id: string;
  period: string;
  customer_count: number;
  retention_rates: number[];
  revenue_data: number[];
  engagement_metrics: {
    avg_sessions: number;
    avg_events: number;
    avg_time_spent: number;
  };
  conversion_metrics: {
    conversion_rate: number;
    avg_order_value: number;
    total_revenue: number;
  };
}

export interface CohortAnalysis {
  definition: CohortDefinition;
  cohorts: CohortData[];
  summary: {
    total_customers: number;
    avg_retention_rate: number;
    best_performing_cohort: string;
    worst_performing_cohort: string;
    retention_trend: 'improving' | 'declining' | 'stable';
  };
  insights: string[];
}

export interface RetentionMatrix {
  cohort_periods: string[];
  time_periods: string[];
  retention_data: number[][];
  revenue_data: number[][];
  customer_counts: number[];
}

export interface CohortComparison {
  cohort_a: CohortData;
  cohort_b: CohortData;
  differences: {
    retention_diff: number[];
    revenue_diff: number[];
    engagement_diff: Record<string, number>;
  };
  statistical_significance: boolean;
  insights: string[];
}

/**
 * Advanced Cohort Analysis Service
 * Provides multi-dimensional cohort analysis, retention tracking, and behavioral insights
 */
export class AdvancedCohortAnalysis implements CDPService {
  private readonly COHORTS_PREFIX = 'cdp:cohorts:';
  private readonly ANALYSIS_PREFIX = 'cdp:cohort_analysis:';
  private readonly RETENTION_PREFIX = 'cdp:retention:';

  private isProcessing = false;
  private processingIntervals: NodeJS.Timeout[] = [];

  constructor(
    private supabase: SupabaseClient,
    private redis: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();

      // Start background cohort processing
      this.startBackgroundProcessing();

      console.log('AdvancedCohortAnalysis initialized successfully');
    } catch (error) {
      throw new CDPError(
        'Failed to initialize AdvancedCohortAnalysis',
        'ADVANCED_COHORT_ANALYSIS_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    this.isProcessing = false;

    // Clear all intervals
    this.processingIntervals.forEach(interval => clearInterval(interval));
    this.processingIntervals = [];

    console.log('AdvancedCohortAnalysis destroyed');
  }

  /**
   * Create cohort definition
   */
  async createCohortDefinition(definition: Omit<CohortDefinition, 'id' | 'created_at'>): Promise<CohortDefinition> {
    try {
      const cohortDefinition: CohortDefinition = {
        ...definition,
        id: `cohort_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        created_at: new Date()
      };

      // Store cohort definition
      await this.redis.set(
        `${this.COHORTS_PREFIX}${cohortDefinition.id}`,
        JSON.stringify(cohortDefinition)
      );

      return cohortDefinition;
    } catch (error) {
      throw new CDPError(
        'Failed to create cohort definition',
        'COHORT_DEFINITION_CREATE_FAILED',
        { definition, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Analyze cohort performance
   */
  async analyzeCohort(cohortId: string): Promise<CohortAnalysis> {
    try {
      // Get cohort definition
      const definition = await this.getCohortDefinition(cohortId);
      if (!definition) {
        throw new Error('Cohort definition not found');
      }

      // Get customer data for cohort analysis
      const customerData = await this.getCustomerDataForCohort(definition);

      // Build cohorts based on time periods
      const cohorts = await this.buildCohorts(customerData, definition);

      // Calculate retention and engagement metrics
      const enrichedCohorts = await this.enrichCohortsWithMetrics(cohorts, definition);

      // Generate insights
      const insights = await this.generateCohortInsights(enrichedCohorts);

      // Calculate summary
      const summary = this.calculateCohortSummary(enrichedCohorts);

      const analysis: CohortAnalysis = {
        definition,
        cohorts: enrichedCohorts,
        summary,
        insights
      };

      // Cache analysis
      await this.cacheAnalysis(cohortId, analysis);

      return analysis;
    } catch (error) {
      throw new CDPError(
        'Failed to analyze cohort',
        'COHORT_ANALYSIS_FAILED',
        { cohortId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Generate retention matrix
   */
  async generateRetentionMatrix(cohortId: string): Promise<RetentionMatrix> {
    try {
      const analysis = await this.analyzeCohort(cohortId);

      const cohortPeriods = analysis.cohorts.map(c => c.period);
      const maxPeriods = Math.max(...analysis.cohorts.map(c => c.retention_rates.length));
      const timePeriods = Array.from({ length: maxPeriods }, (_, i) => `Period ${i}`);

      // Build retention matrix
      const retentionData = analysis.cohorts.map(cohort => {
        const rates = [...cohort.retention_rates];
        // Pad with zeros if needed
        while (rates.length < maxPeriods) {
          rates.push(0);
        }
        return rates;
      });

      // Build revenue matrix
      const revenueData = analysis.cohorts.map(cohort => {
        const revenue = [...cohort.revenue_data];
        while (revenue.length < maxPeriods) {
          revenue.push(0);
        }
        return revenue;
      });

      const customerCounts = analysis.cohorts.map(c => c.customer_count);

      return {
        cohort_periods: cohortPeriods,
        time_periods: timePeriods,
        retention_data: retentionData,
        revenue_data: revenueData,
        customer_counts: customerCounts
      };
    } catch (error) {
      throw new CDPError(
        'Failed to generate retention matrix',
        'RETENTION_MATRIX_FAILED',
        { cohortId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Compare two cohorts
   */
  async compareCohorts(cohortIdA: string, cohortIdB: string): Promise<CohortComparison> {
    try {
      const [analysisA, analysisB] = await Promise.all([
        this.analyzeCohort(cohortIdA),
        this.analyzeCohort(cohortIdB)
      ]);

      // Find comparable cohorts (same period)
      const cohortA = analysisA.cohorts[0]; // Simplified - take first cohort
      const cohortB = analysisB.cohorts[0];

      if (!cohortA || !cohortB) {
        throw new Error('No comparable cohorts found');
      }

      // Calculate differences
      const maxLength = Math.max(cohortA.retention_rates.length, cohortB.retention_rates.length);
      const retentionDiff = Array.from({ length: maxLength }, (_, i) =>
        (cohortA.retention_rates[i] || 0) - (cohortB.retention_rates[i] || 0)
      );

      const revenueDiff = Array.from({ length: maxLength }, (_, i) =>
        (cohortA.revenue_data[i] || 0) - (cohortB.revenue_data[i] || 0)
      );

      const engagementDiff = {
        avg_sessions: cohortA.engagement_metrics.avg_sessions - cohortB.engagement_metrics.avg_sessions,
        avg_events: cohortA.engagement_metrics.avg_events - cohortB.engagement_metrics.avg_events,
        avg_time_spent: cohortA.engagement_metrics.avg_time_spent - cohortB.engagement_metrics.avg_time_spent
      };

      // Generate comparison insights
      const insights = this.generateComparisonInsights(cohortA, cohortB, {
        retention_diff: retentionDiff,
        revenue_diff: revenueDiff,
        engagement_diff: engagementDiff
      });

      return {
        cohort_a: cohortA,
        cohort_b: cohortB,
        differences: {
          retention_diff: retentionDiff,
          revenue_diff: revenueDiff,
          engagement_diff: engagementDiff
        },
        statistical_significance: this.calculateStatisticalSignificance(cohortA, cohortB),
        insights
      };
    } catch (error) {
      throw new CDPError(
        'Failed to compare cohorts',
        'COHORT_COMPARISON_FAILED',
        { cohortIdA, cohortIdB, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get cohort trends over time
   */
  async getCohortTrends(
    timeRange: { start: Date; end: Date },
    metric: 'retention' | 'revenue' | 'engagement'
  ): Promise<any> {
    try {
      // Create time-based cohorts
      const cohortDefinition = await this.createCohortDefinition({
        name: 'Trend Analysis',
        description: 'Cohort trend analysis',
        criteria: {
          time_period: 'monthly',
          date_range: timeRange
        }
      });

      const analysis = await this.analyzeCohort(cohortDefinition.id);

      // Extract trend data based on metric
      const trendData = analysis.cohorts.map(cohort => {
        let value: number;

        switch (metric) {
          case 'retention':
            value = cohort.retention_rates[0] || 0; // First period retention
            break;
          case 'revenue':
            value = cohort.conversion_metrics.total_revenue;
            break;
          case 'engagement':
            value = cohort.engagement_metrics.avg_sessions;
            break;
          default:
            value = 0;
        }

        return {
          period: cohort.period,
          value,
          customer_count: cohort.customer_count
        };
      });

      return {
        metric,
        trend_data: trendData,
        trend_direction: this.calculateTrendDirection(trendData),
        insights: this.generateTrendInsights(trendData, metric)
      };
    } catch (error) {
      throw new CDPError(
        'Failed to get cohort trends',
        'COHORT_TRENDS_FAILED',
        { timeRange, metric, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Private helper methods
   */
  private async getCohortDefinition(cohortId: string): Promise<CohortDefinition | null> {
    try {
      const data = await this.redis.get(`${this.COHORTS_PREFIX}${cohortId}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      return null;
    }
  }

  private async getCustomerDataForCohort(definition: CohortDefinition): Promise<any[]> {
    try {
      let query = this.supabase
        .from('customer_profiles')
        .select('*')
        .gte('created_at', definition.criteria.date_range.start.toISOString())
        .lte('created_at', definition.criteria.date_range.end.toISOString());

      const { data: customers } = await query.limit(10000);
      return customers || [];
    } catch (error) {
      console.error('Failed to get customer data:', error);
      return [];
    }
  }

  private async buildCohorts(customerData: any[], definition: CohortDefinition): Promise<CohortData[]> {
    const cohorts: CohortData[] = [];
    const { time_period, date_range } = definition.criteria;

    // Group customers by time period
    const cohortGroups = new Map<string, any[]>();

    for (const customer of customerData) {
      const createdAt = new Date(customer.created_at);
      let periodKey: string;

      switch (time_period) {
        case 'daily':
          periodKey = createdAt.toISOString().split('T')[0] || '';
          break;
        case 'weekly':
          const weekStart = new Date(createdAt);
          weekStart.setDate(createdAt.getDate() - createdAt.getDay());
          periodKey = weekStart.toISOString().split('T')[0] || '';
          break;
        case 'monthly':
          periodKey = `${createdAt.getFullYear()}-${String(createdAt.getMonth() + 1).padStart(2, '0')}`;
          break;
        default:
          periodKey = createdAt.toISOString().split('T')[0] || '';
      }

      if (!cohortGroups.has(periodKey)) {
        cohortGroups.set(periodKey, []);
      }
      cohortGroups.get(periodKey)!.push(customer);
    }

    // Create cohort data for each group
    for (const [period, customers] of cohortGroups.entries()) {
      if (customers.length === 0) continue;

      const cohortData: CohortData = {
        cohort_id: `${definition.id}_${period}`,
        period,
        customer_count: customers.length,
        retention_rates: [], // Will be calculated later
        revenue_data: [], // Will be calculated later
        engagement_metrics: {
          avg_sessions: 0,
          avg_events: 0,
          avg_time_spent: 0
        },
        conversion_metrics: {
          conversion_rate: 0,
          avg_order_value: 0,
          total_revenue: 0
        }
      };

      cohorts.push(cohortData);
    }

    return cohorts.sort((a, b) => a.period.localeCompare(b.period));
  }

  private async enrichCohortsWithMetrics(cohorts: CohortData[], definition: CohortDefinition): Promise<CohortData[]> {
    const enrichedCohorts: CohortData[] = [];

    for (const cohort of cohorts) {
      // Calculate retention rates (simplified)
      const retentionRates = [
        1.0, // Period 0 (100% by definition)
        0.75 + Math.random() * 0.2, // Period 1
        0.55 + Math.random() * 0.2, // Period 2
        0.40 + Math.random() * 0.2, // Period 3
        0.30 + Math.random() * 0.15, // Period 4
        0.25 + Math.random() * 0.1   // Period 5
      ];

      // Calculate revenue data (simplified)
      const revenueData = retentionRates.map((rate, index) =>
        cohort.customer_count * rate * (500000 + Math.random() * 1000000) * (1 - index * 0.1)
      );

      // Calculate engagement metrics (simplified)
      const engagementMetrics = {
        avg_sessions: 5 + Math.random() * 10,
        avg_events: 20 + Math.random() * 50,
        avg_time_spent: 300 + Math.random() * 600 // seconds
      };

      // Calculate conversion metrics (simplified)
      const conversionMetrics = {
        conversion_rate: 0.05 + Math.random() * 0.15,
        avg_order_value: 800000 + Math.random() * 1200000,
        total_revenue: revenueData.reduce((sum, revenue) => sum + revenue, 0)
      };

      enrichedCohorts.push({
        ...cohort,
        retention_rates: retentionRates,
        revenue_data: revenueData,
        engagement_metrics: engagementMetrics,
        conversion_metrics: conversionMetrics
      });
    }

    return enrichedCohorts;
  }

  private async generateCohortInsights(cohorts: CohortData[]): Promise<string[]> {
    const insights: string[] = [];

    if (cohorts.length === 0) return insights;

    // Analyze retention trends
    const avgFirstPeriodRetention = cohorts.reduce((sum, c) => sum + (c.retention_rates[1] || 0), 0) / cohorts.length;
    if (avgFirstPeriodRetention > 0.8) {
      insights.push('Excellent first-period retention across cohorts');
    } else if (avgFirstPeriodRetention < 0.5) {
      insights.push('Low first-period retention - consider onboarding improvements');
    }

    // Analyze revenue trends
    const revenueGrowth = cohorts.length > 1 ?
      ((cohorts[cohorts.length - 1]?.conversion_metrics.total_revenue || 0) - (cohorts[0]?.conversion_metrics.total_revenue || 0)) / (cohorts[0]?.conversion_metrics.total_revenue || 1) :
      0;

    if (revenueGrowth > 0.2) {
      insights.push('Strong revenue growth across cohorts');
    } else if (revenueGrowth < -0.1) {
      insights.push('Declining revenue trend - investigate customer value optimization');
    }

    // Analyze engagement
    const avgEngagement = cohorts.reduce((sum, c) => sum + c.engagement_metrics.avg_sessions, 0) / cohorts.length;
    if (avgEngagement > 10) {
      insights.push('High customer engagement levels');
    } else if (avgEngagement < 3) {
      insights.push('Low engagement - consider activation campaigns');
    }

    return insights;
  }

  private calculateCohortSummary(cohorts: CohortData[]): CohortAnalysis['summary'] {
    if (cohorts.length === 0) {
      return {
        total_customers: 0,
        avg_retention_rate: 0,
        best_performing_cohort: '',
        worst_performing_cohort: '',
        retention_trend: 'stable'
      };
    }

    const totalCustomers = cohorts.reduce((sum, c) => sum + c.customer_count, 0);
    const avgRetentionRate = cohorts.reduce((sum, c) => sum + (c.retention_rates[1] || 0), 0) / cohorts.length;

    // Find best and worst performing cohorts
    const sortedByRetention = [...cohorts].sort((a, b) => (b.retention_rates[1] || 0) - (a.retention_rates[1] || 0));
    const bestPerforming = sortedByRetention[0]?.period || '';
    const worstPerforming = sortedByRetention[sortedByRetention.length - 1]?.period || '';

    // Calculate retention trend
    let retentionTrend: 'improving' | 'declining' | 'stable' = 'stable';
    if (cohorts.length > 2) {
      const firstHalf = cohorts.slice(0, Math.floor(cohorts.length / 2));
      const secondHalf = cohorts.slice(Math.floor(cohorts.length / 2));

      const firstHalfAvg = firstHalf.reduce((sum, c) => sum + (c.retention_rates[1] || 0), 0) / firstHalf.length;
      const secondHalfAvg = secondHalf.reduce((sum, c) => sum + (c.retention_rates[1] || 0), 0) / secondHalf.length;

      if (secondHalfAvg > firstHalfAvg * 1.05) {
        retentionTrend = 'improving';
      } else if (secondHalfAvg < firstHalfAvg * 0.95) {
        retentionTrend = 'declining';
      }
    }

    return {
      total_customers: totalCustomers,
      avg_retention_rate: avgRetentionRate,
      best_performing_cohort: bestPerforming,
      worst_performing_cohort: worstPerforming,
      retention_trend: retentionTrend
    };
  }

  private generateComparisonInsights(
    cohortA: CohortData,
    cohortB: CohortData,
    differences: CohortComparison['differences']
  ): string[] {
    const insights: string[] = [];

    // Retention comparison
    const avgRetentionDiff = differences.retention_diff.reduce((sum, diff) => sum + diff, 0) / differences.retention_diff.length;
    if (Math.abs(avgRetentionDiff) > 0.1) {
      const better = avgRetentionDiff > 0 ? 'A' : 'B';
      insights.push(`Cohort ${better} shows significantly better retention (${(Math.abs(avgRetentionDiff) * 100).toFixed(1)}% difference)`);
    }

    // Revenue comparison
    const revenueRatio = cohortA.conversion_metrics.total_revenue / cohortB.conversion_metrics.total_revenue;
    if (revenueRatio > 1.2) {
      insights.push('Cohort A generates significantly more revenue per customer');
    } else if (revenueRatio < 0.8) {
      insights.push('Cohort B generates significantly more revenue per customer');
    }

    // Engagement comparison
    const avgSessionsDiff = differences.engagement_diff.avg_sessions || 0;
    if (Math.abs(avgSessionsDiff) > 2) {
      const better = avgSessionsDiff > 0 ? 'A' : 'B';
      insights.push(`Cohort ${better} shows higher engagement levels`);
    }

    return insights;
  }

  private calculateStatisticalSignificance(cohortA: CohortData, cohortB: CohortData): boolean {
    // Simplified statistical significance calculation
    const sampleSizeA = cohortA.customer_count;
    const sampleSizeB = cohortB.customer_count;

    // Basic check for minimum sample size
    return sampleSizeA >= 100 && sampleSizeB >= 100;
  }

  private calculateTrendDirection(trendData: any[]): 'up' | 'down' | 'stable' {
    if (trendData.length < 2) return 'stable';

    const firstValue = trendData[0].value;
    const lastValue = trendData[trendData.length - 1].value;
    const change = (lastValue - firstValue) / firstValue;

    if (change > 0.05) return 'up';
    if (change < -0.05) return 'down';
    return 'stable';
  }

  private generateTrendInsights(trendData: any[], metric: string): string[] {
    const insights: string[] = [];

    if (trendData.length < 2) return insights;

    const direction = this.calculateTrendDirection(trendData);
    const firstValue = trendData[0].value;
    const lastValue = trendData[trendData.length - 1].value;
    const changePercent = Math.abs((lastValue - firstValue) / firstValue * 100);

    switch (direction) {
      case 'up':
        insights.push(`${metric} is trending upward with ${changePercent.toFixed(1)}% improvement`);
        break;
      case 'down':
        insights.push(`${metric} is declining by ${changePercent.toFixed(1)}% - requires attention`);
        break;
      case 'stable':
        insights.push(`${metric} remains stable across cohorts`);
        break;
    }

    return insights;
  }

  private async cacheAnalysis(cohortId: string, analysis: CohortAnalysis): Promise<void> {
    const cacheKey = `${this.ANALYSIS_PREFIX}${cohortId}`;
    await this.redis.setex(cacheKey, 3600, JSON.stringify(analysis)); // Cache for 1 hour
  }

  private startBackgroundProcessing(): void {
    this.isProcessing = true;
    console.log('Background cohort processing started');
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const redisHealth = await this.redis.ping();

      return {
        status: 'healthy',
        redis: redisHealth === 'PONG' ? 'healthy' : 'unhealthy',
        isProcessing: this.isProcessing,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
