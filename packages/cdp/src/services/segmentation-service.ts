import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { v4 as uuidv4 } from 'uuid';
import {
  CustomerSegment,
  SegmentDefinition,
  SegmentMembership,
  SegmentCriteria,
  SegmentCondition,
  CDPService,
  CDPError,
  SegmentNotFoundError
} from '../types';

/**
 * Segmentation Service handles dynamic customer segmentation
 */
export class SegmentationService implements CDPService {
  private readonly CACHE_PREFIX = 'cdp:segment:';
  private readonly CACHE_TTL = 1800; // 30 minutes

  constructor(
    private supabase: SupabaseClient,
    private redis?: Redis
  ) {}

  async initialize(): Promise<void> {
    // Verify database connection
    try {
      const { error } = await this.supabase
        .from('customer_segments')
        .select('id')
        .limit(1);

      if (error && error.code !== 'PGRST116') {
        throw new Error(`Database connection failed: ${error.message}`);
      }
    } catch (error) {
      throw new CDPError(
        'Failed to initialize SegmentationService',
        'SEGMENTATION_SERVICE_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    if (this.redis) {
      const keys = await this.redis.keys(`${this.CACHE_PREFIX}*`);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    }
  }

  /**
   * Create a new customer segment
   */
  async createSegment(
    accountId: string,
    definition: Omit<SegmentDefinition, 'account_id'>
  ): Promise<CustomerSegment> {
    try {
      const segmentId = uuidv4();
      const now = new Date();

      // Validate segment criteria
      await this.validateSegmentCriteria(definition.criteria);

      // Create segment
      const segmentData = {
        id: segmentId,
        account_id: accountId,
        ...definition,
        created_at: now,
        updated_at: now,
      };

      const { data, error } = await this.supabase
        .from('customer_segments')
        .insert(segmentData)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      // Initial computation for dynamic segments
      if (definition.type === 'dynamic') {
        await this.computeSegmentMembership(segmentId);
      }

      return this.mapDatabaseToSegment(data);
    } catch (error) {
      throw new CDPError(
        'Failed to create segment',
        'SEGMENT_CREATE_FAILED',
        {
          accountId,
          definition,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Get segment by ID
   */
  async getSegment(segmentId: string): Promise<CustomerSegment> {
    try {
      // Try cache first
      if (this.redis) {
        const cached = await this.redis.get(`${this.CACHE_PREFIX}${segmentId}`);
        if (cached) {
          return JSON.parse(cached);
        }
      }

      const { data, error } = await this.supabase
        .from('customer_segments')
        .select('*')
        .eq('id', segmentId)
        .single();

      if (error || !data) {
        throw new SegmentNotFoundError(segmentId);
      }

      const segment = this.mapDatabaseToSegment(data);

      // Cache the result
      if (this.redis) {
        await this.redis.setex(
          `${this.CACHE_PREFIX}${segmentId}`,
          this.CACHE_TTL,
          JSON.stringify(segment)
        );
      }

      return segment;
    } catch (error) {
      if (error instanceof SegmentNotFoundError) {
        throw error;
      }
      throw new CDPError(
        'Failed to get segment',
        'SEGMENT_GET_FAILED',
        { segmentId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * List segments for an account
   */
  async listSegments(
    accountId: string,
    limit = 50,
    offset = 0
  ): Promise<{ segments: CustomerSegment[]; total: number }> {
    try {
      const { data, error, count } = await this.supabase
        .from('customer_segments')
        .select('*', { count: 'exact' })
        .eq('account_id', accountId)
        .order('updated_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw new Error(error.message);
      }

      const segments = (data || []).map(this.mapDatabaseToSegment);

      return {
        segments,
        total: count || 0
      };
    } catch (error) {
      throw new CDPError(
        'Failed to list segments',
        'SEGMENT_LIST_FAILED',
        {
          accountId,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Update segment
   */
  async updateSegment(
    segmentId: string,
    updates: Partial<SegmentDefinition>
  ): Promise<CustomerSegment> {
    try {
      if (updates.criteria) {
        await this.validateSegmentCriteria(updates.criteria);
      }

      const { data, error } = await this.supabase
        .from('customer_segments')
        .update({
          ...updates,
          updated_at: new Date(),
        })
        .eq('id', segmentId)
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      // Clear cache
      if (this.redis) {
        await this.redis.del(`${this.CACHE_PREFIX}${segmentId}`);
      }

      // Recompute if criteria changed
      if (updates.criteria) {
        await this.computeSegmentMembership(segmentId);
      }

      return this.mapDatabaseToSegment(data);
    } catch (error) {
      throw new CDPError(
        'Failed to update segment',
        'SEGMENT_UPDATE_FAILED',
        {
          segmentId,
          updates,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Delete segment
   */
  async deleteSegment(segmentId: string): Promise<void> {
    try {
      // Delete segment memberships first
      await this.supabase
        .from('segment_memberships')
        .delete()
        .eq('segment_id', segmentId);

      // Delete segment
      const { error } = await this.supabase
        .from('customer_segments')
        .delete()
        .eq('id', segmentId);

      if (error) {
        throw new Error(error.message);
      }

      // Clear cache
      if (this.redis) {
        await this.redis.del(`${this.CACHE_PREFIX}${segmentId}`);
      }
    } catch (error) {
      throw new CDPError(
        'Failed to delete segment',
        'SEGMENT_DELETE_FAILED',
        { segmentId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Compute segment membership
   */
  async computeSegmentMembership(segmentId: string): Promise<void> {
    try {
      const segment = await this.getSegment(segmentId);

      if (segment.type === 'static') {
        return; // Static segments don't auto-compute
      }

      // Build SQL query from criteria
      const query = this.buildSegmentQuery(segment.criteria);

      // Execute query to get matching customers
      const { data: customers, error } = await this.supabase.rpc(
        'evaluate_segment_criteria',
        {
          segment_id: segmentId,
          account_id: segment.account_id,
          criteria_sql: query
        }
      );

      if (error) {
        throw new Error(error.message);
      }

      // Update segment size
      await this.supabase
        .from('customer_segments')
        .update({
          customer_count: customers?.length || 0,
          last_computed_at: new Date(),
        })
        .eq('id', segmentId);

      // Clear cache to force refresh
      if (this.redis) {
        await this.redis.del(`${this.CACHE_PREFIX}${segmentId}`);
      }

    } catch (error) {
      throw new CDPError(
        'Failed to compute segment membership',
        'SEGMENT_COMPUTATION_FAILED',
        { segmentId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get segment members
   */
  async getSegmentMembers(
    segmentId: string,
    limit = 100,
    offset = 0
  ): Promise<{ customerIds: string[]; total: number }> {
    try {
      const { data, error, count } = await this.supabase
        .from('segment_memberships')
        .select('customer_profile_id', { count: 'exact' })
        .eq('segment_id', segmentId)
        .eq('is_active', true)
        .range(offset, offset + limit - 1);

      if (error) {
        throw new Error(error.message);
      }

      return {
        customerIds: (data || []).map(row => row.customer_profile_id),
        total: count || 0
      };
    } catch (error) {
      throw new CDPError(
        'Failed to get segment members',
        'SEGMENT_MEMBERS_FAILED',
        { segmentId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Create pre-built segment templates
   */
  async createHighValueCustomers(accountId: string): Promise<CustomerSegment> {
    return this.createSegment(accountId, {
      name: 'High Value Customers',
      description: 'Customers with high lifetime value and recent activity',
      criteria: {
        operator: 'AND',
        conditions: [
          {
            id: uuidv4(),
            field: 'lifetime_value_score',
            operator: 'greater_than',
            value: 5000000
          },
          {
            id: uuidv4(),
            field: 'total_purchases',
            operator: 'greater_than',
            value: 3
          },
          {
            id: uuidv4(),
            field: 'last_activity_at',
            operator: 'within_days',
            value: 90
          }
        ]
      },
      type: 'dynamic',
      is_active: true,
      activations: [],
      custom_attributes: {},
      tags: ['high-value', 'template']
    });
  }

  async createChurnRiskCustomers(accountId: string): Promise<CustomerSegment> {
    return this.createSegment(accountId, {
      name: 'Churn Risk Customers',
      description: 'Customers at risk of churning',
      criteria: {
        operator: 'AND',
        conditions: [
          {
            id: uuidv4(),
            field: 'churn_risk_score',
            operator: 'greater_than',
            value: 0.7
          },
          {
            id: uuidv4(),
            field: 'last_activity_at',
            operator: 'more_than_days_ago',
            value: 30
          },
          {
            id: uuidv4(),
            field: 'total_purchases',
            operator: 'greater_than',
            value: 0
          }
        ]
      },
      type: 'dynamic',
      is_active: true,
      activations: [],
      custom_attributes: {},
      tags: ['churn-risk', 'template']
    });
  }

  /**
   * Validate segment criteria
   */
  private async validateSegmentCriteria(criteria: SegmentCriteria): Promise<void> {
    if (!criteria.conditions || criteria.conditions.length === 0) {
      throw new Error('Segment must have at least one condition');
    }

    for (const condition of criteria.conditions) {
      if (!condition.field || !condition.operator) {
        throw new Error('Each condition must have field and operator');
      }
    }
  }

  /**
   * Build SQL query from segment criteria
   */
  private buildSegmentQuery(criteria: SegmentCriteria): string {
    const conditions = criteria.conditions.map((condition: SegmentCondition) => {
      return this.buildConditionSQL(condition);
    });

    return conditions.join(` ${criteria.operator} `);
  }

  /**
   * Build SQL for individual condition
   */
  private buildConditionSQL(condition: SegmentCondition): string {
    const { field, operator, value } = condition;

    switch (operator) {
      case 'equals':
        return `${field} = '${value}'`;
      case 'not_equals':
        return `${field} != '${value}'`;
      case 'greater_than':
        return `${field} > ${value}`;
      case 'greater_than_or_equal':
        return `${field} >= ${value}`;
      case 'less_than':
        return `${field} < ${value}`;
      case 'less_than_or_equal':
        return `${field} <= ${value}`;
      case 'contains':
        return `${field} ILIKE '%${value}%'`;
      case 'not_contains':
        return `${field} NOT ILIKE '%${value}%'`;
      case 'within_days':
        return `${field} >= NOW() - INTERVAL '${value} days'`;
      case 'more_than_days_ago':
        return `${field} < NOW() - INTERVAL '${value} days'`;
      case 'is_null':
        return `${field} IS NULL`;
      case 'is_not_null':
        return `${field} IS NOT NULL`;
      case 'in':
        const values = Array.isArray(value) ? value : [value];
        return `${field} IN (${values.map(v => `'${v}'`).join(', ')})`;
      default:
        throw new Error(`Unsupported operator: ${operator}`);
    }
  }

  /**
   * Map database row to CustomerSegment
   */
  private mapDatabaseToSegment(data: any): CustomerSegment {
    return {
      id: data.id,
      account_id: data.account_id,
      name: data.name,
      description: data.description,
      color: data.color,
      icon: data.icon,
      criteria: data.criteria || { operator: 'AND', conditions: [] },
      type: data.type || 'dynamic',
      customer_count: data.customer_count || 0,
      estimated_size: data.estimated_size,
      is_active: data.is_active || false,
      is_computing: data.is_computing || false,
      activations: data.activations || [],
      performance_history: data.performance_history || [],
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at),
      last_computed_at: data.last_computed_at ? new Date(data.last_computed_at) : undefined,
      created_by: data.created_by,
      tags: data.tags || [],
      custom_attributes: data.custom_attributes || {}
    };
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const { error: dbError } = await this.supabase
        .from('customer_segments')
        .select('id')
        .limit(1);

      let cacheStatus = 'disabled';
      if (this.redis) {
        try {
          await this.redis.ping();
          cacheStatus = 'healthy';
        } catch {
          cacheStatus = 'unhealthy';
        }
      }

      return {
        status: dbError ? 'unhealthy' : 'healthy',
        database: dbError ? 'unhealthy' : 'healthy',
        cache: cacheStatus,
        error: dbError?.message
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}
