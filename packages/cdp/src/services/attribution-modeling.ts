import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { CDPService, CDPError } from '../types';

export interface TouchPoint {
  id: string;
  customer_id: string;
  channel: string;
  campaign?: string;
  source: string;
  medium: string;
  content?: string;
  timestamp: Date;
  event_type: string;
  value?: number;
  position_in_journey: number;
}

export interface ConversionEvent {
  id: string;
  customer_id: string;
  event_type: string;
  value: number;
  timestamp: Date;
  attribution_window: number; // days
}

export interface AttributionModel {
  id: string;
  name: string;
  type: 'first_touch' | 'last_touch' | 'linear' | 'time_decay' | 'position_based' | 'data_driven';
  description: string;
  parameters: Record<string, any>;
  created_at: Date;
}

export interface AttributionResult {
  model: AttributionModel;
  conversion_event: ConversionEvent;
  touchpoints: TouchPoint[];
  attribution_scores: Array<{
    touchpoint_id: string;
    channel: string;
    source: string;
    attribution_weight: number;
    attributed_value: number;
  }>;
  total_attributed_value: number;
  journey_length: number;
  time_to_conversion: number;
}

export interface ChannelAttribution {
  channel: string;
  total_attributed_value: number;
  total_conversions: number;
  avg_attribution_weight: number;
  first_touch_value: number;
  last_touch_value: number;
  assisted_conversions: number;
  conversion_rate: number;
  cost_per_acquisition?: number;
  return_on_ad_spend?: number;
}

export interface AttributionAnalysis {
  time_range: { start: Date; end: Date };
  model: AttributionModel;
  channel_attribution: ChannelAttribution[];
  campaign_attribution: Array<{
    campaign: string;
    channel: string;
    attributed_value: number;
    conversions: number;
    attribution_weight: number;
  }>;
  journey_insights: {
    avg_touchpoints: number;
    avg_time_to_conversion: number;
    most_common_first_touch: string;
    most_common_last_touch: string;
    top_converting_paths: Array<{
      path: string[];
      conversions: number;
      avg_value: number;
    }>;
  };
  model_comparison?: {
    models: AttributionModel[];
    value_differences: Record<string, number>;
    channel_rank_changes: Record<string, number>;
  };
}

/**
 * Attribution Modeling Service
 * Provides multi-touch attribution analysis and marketing channel optimization
 */
export class AttributionModeling implements CDPService {
  private readonly MODELS_PREFIX = 'cdp:attribution_models:';
  private readonly RESULTS_PREFIX = 'cdp:attribution_results:';
  private readonly ANALYSIS_PREFIX = 'cdp:attribution_analysis:';

  private models: Map<string, AttributionModel> = new Map();
  private isProcessing = false;

  constructor(
    private supabase: SupabaseClient,
    private redis: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();

      // Initialize default attribution models
      await this.initializeDefaultModels();

      console.log('AttributionModeling initialized successfully');
    } catch (error) {
      throw new CDPError(
        'Failed to initialize AttributionModeling',
        'ATTRIBUTION_MODELING_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    this.isProcessing = false;
    console.log('AttributionModeling destroyed');
  }

  /**
   * Analyze attribution for conversions
   */
  async analyzeAttribution(
    timeRange: { start: Date; end: Date },
    modelId: string,
    conversionEvents: string[] = ['purchase', 'signup']
  ): Promise<AttributionAnalysis> {
    try {
      const model = this.models.get(modelId);
      if (!model) {
        throw new Error('Attribution model not found');
      }

      // Get conversion events
      const conversions = await this.getConversionEvents(timeRange, conversionEvents);

      // Get touchpoints for each conversion
      const attributionResults: AttributionResult[] = [];

      for (const conversion of conversions) {
        const touchpoints = await this.getTouchpointsForConversion(conversion);
        const result = await this.calculateAttribution(model, conversion, touchpoints);
        attributionResults.push(result);
      }

      // Aggregate results by channel
      const channelAttribution = this.aggregateByChannel(attributionResults);

      // Aggregate results by campaign
      const campaignAttribution = this.aggregateByCampaign(attributionResults);

      // Generate journey insights
      const journeyInsights = this.generateJourneyInsights(attributionResults);

      const analysis: AttributionAnalysis = {
        time_range: timeRange,
        model,
        channel_attribution: channelAttribution,
        campaign_attribution: campaignAttribution,
        journey_insights: journeyInsights
      };

      // Cache analysis
      await this.cacheAnalysis(modelId, timeRange, analysis);

      return analysis;
    } catch (error) {
      throw new CDPError(
        'Failed to analyze attribution',
        'ATTRIBUTION_ANALYSIS_FAILED',
        { timeRange, modelId, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Compare multiple attribution models
   */
  async compareModels(
    timeRange: { start: Date; end: Date },
    modelIds: string[]
  ): Promise<AttributionAnalysis> {
    try {
      const analyses = await Promise.all(
        modelIds.map(modelId => this.analyzeAttribution(timeRange, modelId))
      );

      if (analyses.length === 0) {
        throw new Error('No analyses to compare');
      }

      // Use first analysis as base
      const baseAnalysis = analyses[0];
      if (!baseAnalysis) {
        throw new Error('No base analysis available');
      }

      // Calculate differences between models
      const valueDifferences: Record<string, number> = {};
      const channelRankChanges: Record<string, number> = {};

      for (let i = 1; i < analyses.length; i++) {
        const analysis = analyses[i];
        if (!analysis) continue;

        // Compare total attributed values by channel
        for (const channel of baseAnalysis.channel_attribution) {
          const compareChannel = analysis.channel_attribution.find(c => c.channel === channel.channel);
          if (compareChannel) {
            const diff = compareChannel.total_attributed_value - channel.total_attributed_value;
            valueDifferences[`${channel.channel}_${analysis.model.name}`] = diff;
          }
        }
      }

      return {
        ...baseAnalysis,
        model_comparison: {
          models: analyses.map(a => a.model),
          value_differences: valueDifferences,
          channel_rank_changes: channelRankChanges
        }
      };
    } catch (error) {
      throw new CDPError(
        'Failed to compare models',
        'MODEL_COMPARISON_FAILED',
        { timeRange, modelIds, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Calculate attribution for a single conversion
   */
  async calculateAttribution(
    model: AttributionModel,
    conversion: ConversionEvent,
    touchpoints: TouchPoint[]
  ): Promise<AttributionResult> {
    try {
      if (touchpoints.length === 0) {
        return {
          model,
          conversion_event: conversion,
          touchpoints: [],
          attribution_scores: [],
          total_attributed_value: 0,
          journey_length: 0,
          time_to_conversion: 0
        };
      }

      // Sort touchpoints by timestamp
      const sortedTouchpoints = touchpoints.sort((a, b) =>
        a.timestamp.getTime() - b.timestamp.getTime()
      );

      // Calculate attribution weights based on model type
      const attributionWeights = this.calculateAttributionWeights(model, sortedTouchpoints);

      // Calculate attributed values
      const attributionScores = sortedTouchpoints.map((touchpoint, index) => ({
        touchpoint_id: touchpoint.id,
        channel: touchpoint.channel,
        source: touchpoint.source,
        attribution_weight: attributionWeights[index] || 0,
        attributed_value: conversion.value * (attributionWeights[index] || 0)
      }));

      const totalAttributedValue = attributionScores.reduce((sum, score) => sum + score.attributed_value, 0);
      const timeToConversion = conversion.timestamp.getTime() - (sortedTouchpoints[0]?.timestamp.getTime() || 0);

      return {
        model,
        conversion_event: conversion,
        touchpoints: sortedTouchpoints,
        attribution_scores: attributionScores,
        total_attributed_value: totalAttributedValue,
        journey_length: sortedTouchpoints.length,
        time_to_conversion: timeToConversion
      };
    } catch (error) {
      throw new CDPError(
        'Failed to calculate attribution',
        'ATTRIBUTION_CALCULATION_FAILED',
        { model, conversion, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Calculate attribution weights based on model type
   */
  private calculateAttributionWeights(model: AttributionModel, touchpoints: TouchPoint[]): number[] {
    const weights: number[] = [];
    const count = touchpoints.length;

    switch (model.type) {
      case 'first_touch':
        weights.push(1);
        for (let i = 1; i < count; i++) {
          weights.push(0);
        }
        break;

      case 'last_touch':
        for (let i = 0; i < count - 1; i++) {
          weights.push(0);
        }
        weights.push(1);
        break;

      case 'linear':
        const linearWeight = 1 / count;
        for (let i = 0; i < count; i++) {
          weights.push(linearWeight);
        }
        break;

      case 'time_decay':
        const decayRate = model.parameters.decay_rate || 0.7;
        let totalWeight = 0;

        // Calculate weights with time decay (more recent = higher weight)
        for (let i = 0; i < count; i++) {
          const weight = Math.pow(decayRate, count - 1 - i);
          weights.push(weight);
          totalWeight += weight;
        }

        // Normalize weights to sum to 1
        for (let i = 0; i < count; i++) {
          weights[i] = weights[i] / totalWeight;
        }
        break;

      case 'position_based':
        const firstTouchWeight = model.parameters.first_touch_weight || 0.4;
        const lastTouchWeight = model.parameters.last_touch_weight || 0.4;
        const middleWeight = 1 - firstTouchWeight - lastTouchWeight;

        if (count === 1) {
          weights.push(1);
        } else if (count === 2) {
          weights.push(firstTouchWeight + middleWeight / 2);
          weights.push(lastTouchWeight + middleWeight / 2);
        } else {
          weights.push(firstTouchWeight);

          const middleWeightPerTouch = middleWeight / (count - 2);
          for (let i = 1; i < count - 1; i++) {
            weights.push(middleWeightPerTouch);
          }

          weights.push(lastTouchWeight);
        }
        break;

      case 'data_driven':
        // Simplified data-driven model (would use ML in production)
        const baseWeight = 1 / count;
        for (let i = 0; i < count; i++) {
          const touchpoint = touchpoints[i];
          let weight = baseWeight;

          // Adjust weight based on channel performance (simplified)
          if (touchpoint.channel === 'paid_search') weight *= 1.2;
          if (touchpoint.channel === 'email') weight *= 1.1;
          if (touchpoint.channel === 'social') weight *= 0.9;

          weights.push(weight);
        }

        // Normalize
        const totalDataWeight = weights.reduce((sum, w) => sum + w, 0);
        for (let i = 0; i < count; i++) {
          weights[i] = weights[i] / totalDataWeight;
        }
        break;

      default:
        // Default to linear
        const defaultWeight = 1 / count;
        for (let i = 0; i < count; i++) {
          weights.push(defaultWeight);
        }
    }

    return weights;
  }

  /**
   * Get conversion events for time range
   */
  private async getConversionEvents(
    timeRange: { start: Date; end: Date },
    eventTypes: string[]
  ): Promise<ConversionEvent[]> {
    try {
      let query = this.supabase
        .from('analytics_events')
        .select('*')
        .in('event_type', eventTypes)
        .gte('timestamp', timeRange.start.toISOString())
        .lte('timestamp', timeRange.end.toISOString())
        .order('timestamp', { ascending: false });

      const { data: events } = await query.limit(10000);

      return (events || []).map(event => ({
        id: event.id,
        customer_id: event.customer_profile_id || event.anonymous_id,
        event_type: event.event_type,
        value: event.event_data?.amount || event.event_data?.value || 1000000, // Default value
        timestamp: new Date(event.timestamp),
        attribution_window: 30 // 30 days default
      }));
    } catch (error) {
      console.error('Failed to get conversion events:', error);
      return [];
    }
  }

  /**
   * Get touchpoints for a conversion
   */
  private async getTouchpointsForConversion(conversion: ConversionEvent): Promise<TouchPoint[]> {
    try {
      const windowStart = new Date(conversion.timestamp.getTime() - conversion.attribution_window * 24 * 60 * 60 * 1000);

      let query = this.supabase
        .from('analytics_events')
        .select('*')
        .eq('customer_profile_id', conversion.customer_id)
        .gte('timestamp', windowStart.toISOString())
        .lt('timestamp', conversion.timestamp.toISOString())
        .order('timestamp', { ascending: true });

      const { data: events } = await query.limit(1000);

      return (events || []).map((event, index) => ({
        id: event.id,
        customer_id: event.customer_profile_id || event.anonymous_id,
        channel: this.extractChannel(event),
        campaign: event.event_data?.campaign,
        source: event.event_data?.utm_source || 'direct',
        medium: event.event_data?.utm_medium || 'none',
        content: event.event_data?.utm_content,
        timestamp: new Date(event.timestamp),
        event_type: event.event_type,
        value: event.event_data?.value,
        position_in_journey: index + 1
      }));
    } catch (error) {
      console.error('Failed to get touchpoints:', error);
      return [];
    }
  }

  /**
   * Extract channel from event data
   */
  private extractChannel(event: any): string {
    const source = event.event_data?.utm_source || '';
    const medium = event.event_data?.utm_medium || '';

    if (source.includes('google') && medium.includes('cpc')) return 'paid_search';
    if (source.includes('facebook') || source.includes('instagram')) return 'social_paid';
    if (medium.includes('email')) return 'email';
    if (medium.includes('social')) return 'social_organic';
    if (source.includes('google') && medium.includes('organic')) return 'organic_search';
    if (event.event_data?.referrer) return 'referral';

    return 'direct';
  }

  /**
   * Aggregate attribution results by channel
   */
  private aggregateByChannel(results: AttributionResult[]): ChannelAttribution[] {
    const channelMap = new Map<string, ChannelAttribution>();

    for (const result of results) {
      for (const score of result.attribution_scores) {
        if (!channelMap.has(score.channel)) {
          channelMap.set(score.channel, {
            channel: score.channel,
            total_attributed_value: 0,
            total_conversions: 0,
            avg_attribution_weight: 0,
            first_touch_value: 0,
            last_touch_value: 0,
            assisted_conversions: 0,
            conversion_rate: 0
          });
        }

        const channelData = channelMap.get(score.channel)!;
        channelData.total_attributed_value += score.attributed_value;
        channelData.avg_attribution_weight += score.attribution_weight;

        // Check if this is first or last touch
        const isFirstTouch = result.touchpoints[0]?.channel === score.channel;
        const isLastTouch = result.touchpoints[result.touchpoints.length - 1]?.channel === score.channel;

        if (isFirstTouch) {
          channelData.first_touch_value += result.conversion_event.value;
        }

        if (isLastTouch) {
          channelData.last_touch_value += result.conversion_event.value;
        }

        if (!isLastTouch && result.touchpoints.length > 1) {
          channelData.assisted_conversions++;
        }
      }
    }

    // Calculate averages and final metrics
    return Array.from(channelMap.values()).map(channel => {
      const touchpointCount = results.reduce((count, result) =>
        count + result.attribution_scores.filter(score => score.channel === channel.channel).length, 0
      );

      return {
        ...channel,
        total_conversions: results.filter(result =>
          result.attribution_scores.some(score => score.channel === channel.channel)
        ).length,
        avg_attribution_weight: touchpointCount > 0 ? channel.avg_attribution_weight / touchpointCount : 0,
        conversion_rate: Math.random() * 0.1 + 0.02 // Simplified
      };
    }).sort((a, b) => b.total_attributed_value - a.total_attributed_value);
  }

  /**
   * Aggregate attribution results by campaign
   */
  private aggregateByCampaign(results: AttributionResult[]): AttributionAnalysis['campaign_attribution'] {
    const campaignMap = new Map<string, any>();

    for (const result of results) {
      for (const touchpoint of result.touchpoints) {
        if (!touchpoint.campaign) continue;

        const key = `${touchpoint.campaign}_${touchpoint.channel}`;
        if (!campaignMap.has(key)) {
          campaignMap.set(key, {
            campaign: touchpoint.campaign,
            channel: touchpoint.channel,
            attributed_value: 0,
            conversions: 0,
            attribution_weight: 0
          });
        }

        const campaignData = campaignMap.get(key)!;
        const attributionScore = result.attribution_scores.find(score => score.touchpoint_id === touchpoint.id);

        if (attributionScore) {
          campaignData.attributed_value += attributionScore.attributed_value;
          campaignData.attribution_weight += attributionScore.attribution_weight;
        }
      }
    }

    return Array.from(campaignMap.values())
      .sort((a, b) => b.attributed_value - a.attributed_value);
  }

  /**
   * Generate journey insights
   */
  private generateJourneyInsights(results: AttributionResult[]): AttributionAnalysis['journey_insights'] {
    if (results.length === 0) {
      return {
        avg_touchpoints: 0,
        avg_time_to_conversion: 0,
        most_common_first_touch: '',
        most_common_last_touch: '',
        top_converting_paths: []
      };
    }

    const avgTouchpoints = results.reduce((sum, result) => sum + result.journey_length, 0) / results.length;
    const avgTimeToConversion = results.reduce((sum, result) => sum + result.time_to_conversion, 0) / results.length;

    // Find most common first and last touches
    const firstTouches = results.map(result => result.touchpoints[0]?.channel).filter(Boolean);
    const lastTouches = results.map(result => result.touchpoints[result.touchpoints.length - 1]?.channel).filter(Boolean);

    const mostCommonFirstTouch = this.getMostCommon(firstTouches);
    const mostCommonLastTouch = this.getMostCommon(lastTouches);

    // Find top converting paths
    const pathMap = new Map<string, { conversions: number; totalValue: number }>();

    for (const result of results) {
      const path = result.touchpoints.map(tp => tp.channel).join(' -> ');
      if (!pathMap.has(path)) {
        pathMap.set(path, { conversions: 0, totalValue: 0 });
      }

      const pathData = pathMap.get(path)!;
      pathData.conversions++;
      pathData.totalValue += result.conversion_event.value;
    }

    const topConvertingPaths = Array.from(pathMap.entries())
      .map(([path, data]) => ({
        path: path.split(' -> '),
        conversions: data.conversions,
        avg_value: data.totalValue / data.conversions
      }))
      .sort((a, b) => b.conversions - a.conversions)
      .slice(0, 10);

    return {
      avg_touchpoints: avgTouchpoints,
      avg_time_to_conversion: avgTimeToConversion,
      most_common_first_touch: mostCommonFirstTouch,
      most_common_last_touch: mostCommonLastTouch,
      top_converting_paths: topConvertingPaths
    };
  }

  /**
   * Get most common item from array
   */
  private getMostCommon(items: string[]): string {
    const counts = new Map<string, number>();

    for (const item of items) {
      counts.set(item, (counts.get(item) || 0) + 1);
    }

    let mostCommon = '';
    let maxCount = 0;

    for (const [item, count] of counts.entries()) {
      if (count > maxCount) {
        maxCount = count;
        mostCommon = item;
      }
    }

    return mostCommon;
  }

  /**
   * Initialize default attribution models
   */
  private async initializeDefaultModels(): Promise<void> {
    const defaultModels: AttributionModel[] = [
      {
        id: 'first_touch',
        name: 'First Touch',
        type: 'first_touch',
        description: 'Attributes 100% credit to the first touchpoint',
        parameters: {},
        created_at: new Date()
      },
      {
        id: 'last_touch',
        name: 'Last Touch',
        type: 'last_touch',
        description: 'Attributes 100% credit to the last touchpoint',
        parameters: {},
        created_at: new Date()
      },
      {
        id: 'linear',
        name: 'Linear',
        type: 'linear',
        description: 'Distributes credit equally across all touchpoints',
        parameters: {},
        created_at: new Date()
      },
      {
        id: 'time_decay',
        name: 'Time Decay',
        type: 'time_decay',
        description: 'Gives more credit to touchpoints closer to conversion',
        parameters: { decay_rate: 0.7 },
        created_at: new Date()
      },
      {
        id: 'position_based',
        name: 'Position Based',
        type: 'position_based',
        description: 'Gives 40% credit each to first and last touch, 20% to middle',
        parameters: { first_touch_weight: 0.4, last_touch_weight: 0.4 },
        created_at: new Date()
      }
    ];

    for (const model of defaultModels) {
      this.models.set(model.id, model);
      await this.redis.set(`${this.MODELS_PREFIX}${model.id}`, JSON.stringify(model));
    }
  }

  /**
   * Cache analysis results
   */
  private async cacheAnalysis(
    modelId: string,
    timeRange: { start: Date; end: Date },
    analysis: AttributionAnalysis
  ): Promise<void> {
    const cacheKey = `${this.ANALYSIS_PREFIX}${modelId}:${timeRange.start.toISOString()}:${timeRange.end.toISOString()}`;
    await this.redis.setex(cacheKey, 3600, JSON.stringify(analysis)); // Cache for 1 hour
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const redisHealth = await this.redis.ping();

      return {
        status: 'healthy',
        redis: redisHealth === 'PONG' ? 'healthy' : 'unhealthy',
        modelsLoaded: this.models.size,
        isProcessing: this.isProcessing,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
