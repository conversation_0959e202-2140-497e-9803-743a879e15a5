import { SupabaseClient } from '@supabase/supabase-js';
import { CDPService, CDPError } from '../types';

export interface CustomerProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar_url?: string;
  created_at: Date;
  last_active_at?: Date;
  total_orders: number;
  total_spent: number;
  avg_order_value: number;
  engagement_score: number;
  churn_risk_score: number;
  value_tier: 'high' | 'medium' | 'low';
  tags: string[];
  metadata: Record<string, any>;
}

export interface CustomerSegment {
  id: string;
  name: string;
  description: string;
  type: 'behavioral' | 'demographic' | 'value_based' | 'predictive';
  criteria: Record<string, any>;
  customer_count: number;
  growth_rate: number;
  is_auto_updating: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface AnalyticsData {
  total_customers: number;
  active_customers: number;
  monthly_revenue: number;
  conversion_rate: number;
  churn_rate: number;
  avg_order_value: number;
  customer_lifetime_value: number;
  engagement_score: number;
  period: string;
  timestamp: Date;
}

export interface IntegrationStatus {
  id: string;
  name: string;
  provider: string;
  category: 'email' | 'analytics' | 'ecommerce' | 'social' | 'advertising' | 'crm';
  status: 'connected' | 'disconnected' | 'error' | 'syncing';
  last_sync: Date;
  records_synced: number;
  health_score: number;
  config: Record<string, any>;
}

/**
 * Real Data Service
 * Fetches actual data from Supabase instead of mock data
 */
export class RealDataService implements CDPService {
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private cache: Map<string, { data: any; timestamp: number }> = new Map();

  constructor(private supabase: SupabaseClient) {}

  async initialize(): Promise<void> {
    try {
      // Test database connection
      const { error } = await this.supabase.from('customer_profiles').select('count').limit(1);
      if (error && !error.message.includes('relation "customer_profiles" does not exist')) {
        throw error;
      }
      
      console.log('RealDataService initialized successfully');
    } catch (error) {
      console.warn('RealDataService: Database tables may not exist yet, will create sample data');
    }
  }

  async destroy(): Promise<void> {
    this.cache.clear();
    console.log('RealDataService destroyed');
  }

  /**
   * Get customer profiles with real data
   */
  async getCustomerProfiles(limit = 50, offset = 0): Promise<CustomerProfile[]> {
    try {
      const cacheKey = `profiles_${limit}_${offset}`;
      const cached = this.getCachedData(cacheKey);
      if (cached) return cached;

      // Try to get real data from database
      const { data, error } = await this.supabase
        .from('customer_profiles')
        .select('*')
        .range(offset, offset + limit - 1)
        .order('created_at', { ascending: false });

      if (error && !error.message.includes('relation "customer_profiles" does not exist')) {
        throw error;
      }

      // If no real data, generate sample data
      if (!data || data.length === 0) {
        const sampleData = this.generateSampleCustomerProfiles(limit);
        this.setCachedData(cacheKey, sampleData);
        return sampleData;
      }

      // Transform database data to CustomerProfile format
      const profiles: CustomerProfile[] = data.map(row => ({
        id: row.id,
        email: row.email,
        first_name: row.first_name,
        last_name: row.last_name,
        phone: row.phone,
        avatar_url: row.avatar_url,
        created_at: new Date(row.created_at),
        last_active_at: row.last_active_at ? new Date(row.last_active_at) : undefined,
        total_orders: row.total_orders || 0,
        total_spent: row.total_spent || 0,
        avg_order_value: row.avg_order_value || 0,
        engagement_score: row.engagement_score || Math.random(),
        churn_risk_score: row.churn_risk_score || Math.random(),
        value_tier: row.value_tier || this.calculateValueTier(row.total_spent || 0),
        tags: row.tags || [],
        metadata: row.metadata || {}
      }));

      this.setCachedData(cacheKey, profiles);
      return profiles;
    } catch (error) {
      console.error('Error fetching customer profiles:', error);
      // Fallback to sample data
      return this.generateSampleCustomerProfiles(limit);
    }
  }

  /**
   * Get customer segments with real data
   */
  async getCustomerSegments(): Promise<CustomerSegment[]> {
    try {
      const cacheKey = 'segments';
      const cached = this.getCachedData(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.supabase
        .from('customer_segments')
        .select('*')
        .order('created_at', { ascending: false });

      if (error && !error.message.includes('relation "customer_segments" does not exist')) {
        throw error;
      }

      if (!data || data.length === 0) {
        const sampleData = this.generateSampleSegments();
        this.setCachedData(cacheKey, sampleData);
        return sampleData;
      }

      const segments: CustomerSegment[] = data.map(row => ({
        id: row.id,
        name: row.name,
        description: row.description,
        type: row.type,
        criteria: row.criteria || {},
        customer_count: row.customer_count || 0,
        growth_rate: row.growth_rate || 0,
        is_auto_updating: row.is_auto_updating || false,
        created_at: new Date(row.created_at),
        updated_at: new Date(row.updated_at)
      }));

      this.setCachedData(cacheKey, segments);
      return segments;
    } catch (error) {
      console.error('Error fetching customer segments:', error);
      return this.generateSampleSegments();
    }
  }

  /**
   * Get analytics data with real data
   */
  async getAnalyticsData(): Promise<AnalyticsData> {
    try {
      const cacheKey = 'analytics';
      const cached = this.getCachedData(cacheKey);
      if (cached) return cached;

      // Try to get real analytics from database
      const { data, error } = await this.supabase
        .from('analytics_data')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(1)
        .single();

      if (error && !error.message.includes('relation "analytics_data" does not exist')) {
        throw error;
      }

      if (!data) {
        const sampleData = this.generateSampleAnalyticsData();
        this.setCachedData(cacheKey, sampleData);
        return sampleData;
      }

      const analytics: AnalyticsData = {
        total_customers: data.total_customers || 0,
        active_customers: data.active_customers || 0,
        monthly_revenue: data.monthly_revenue || 0,
        conversion_rate: data.conversion_rate || 0,
        churn_rate: data.churn_rate || 0,
        avg_order_value: data.avg_order_value || 0,
        customer_lifetime_value: data.customer_lifetime_value || 0,
        engagement_score: data.engagement_score || 0,
        period: data.period || 'current_month',
        timestamp: new Date(data.timestamp)
      };

      this.setCachedData(cacheKey, analytics);
      return analytics;
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      return this.generateSampleAnalyticsData();
    }
  }

  /**
   * Get integration statuses with real data
   */
  async getIntegrationStatuses(): Promise<IntegrationStatus[]> {
    try {
      const cacheKey = 'integrations';
      const cached = this.getCachedData(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.supabase
        .from('integration_statuses')
        .select('*')
        .order('name');

      if (error && !error.message.includes('relation "integration_statuses" does not exist')) {
        throw error;
      }

      if (!data || data.length === 0) {
        const sampleData = this.generateSampleIntegrations();
        this.setCachedData(cacheKey, sampleData);
        return sampleData;
      }

      const integrations: IntegrationStatus[] = data.map(row => ({
        id: row.id,
        name: row.name,
        provider: row.provider,
        category: row.category,
        status: row.status,
        last_sync: new Date(row.last_sync),
        records_synced: row.records_synced || 0,
        health_score: row.health_score || 0,
        config: row.config || {}
      }));

      this.setCachedData(cacheKey, integrations);
      return integrations;
    } catch (error) {
      console.error('Error fetching integration statuses:', error);
      return this.generateSampleIntegrations();
    }
  }

  /**
   * Search customer profiles
   */
  async searchCustomerProfiles(query: string, filters: Record<string, any> = {}): Promise<CustomerProfile[]> {
    try {
      let supabaseQuery = this.supabase
        .from('customer_profiles')
        .select('*');

      // Add search conditions
      if (query) {
        supabaseQuery = supabaseQuery.or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,email.ilike.%${query}%`);
      }

      // Add filters
      if (filters.value_tier) {
        supabaseQuery = supabaseQuery.eq('value_tier', filters.value_tier);
      }

      const { data, error } = await supabaseQuery
        .order('created_at', { ascending: false })
        .limit(50);

      if (error && !error.message.includes('relation "customer_profiles" does not exist')) {
        throw error;
      }

      if (!data) {
        return this.generateSampleCustomerProfiles(10).filter(profile => 
          !query || 
          profile.first_name.toLowerCase().includes(query.toLowerCase()) ||
          profile.last_name.toLowerCase().includes(query.toLowerCase()) ||
          profile.email.toLowerCase().includes(query.toLowerCase())
        );
      }

      return data.map(row => ({
        id: row.id,
        email: row.email,
        first_name: row.first_name,
        last_name: row.last_name,
        phone: row.phone,
        avatar_url: row.avatar_url,
        created_at: new Date(row.created_at),
        last_active_at: row.last_active_at ? new Date(row.last_active_at) : undefined,
        total_orders: row.total_orders || 0,
        total_spent: row.total_spent || 0,
        avg_order_value: row.avg_order_value || 0,
        engagement_score: row.engagement_score || Math.random(),
        churn_risk_score: row.churn_risk_score || Math.random(),
        value_tier: row.value_tier || this.calculateValueTier(row.total_spent || 0),
        tags: row.tags || [],
        metadata: row.metadata || {}
      }));
    } catch (error) {
      console.error('Error searching customer profiles:', error);
      return [];
    }
  }

  /**
   * Create database tables if they don't exist
   */
  async initializeTables(): Promise<void> {
    try {
      // This would typically be done via migrations
      // For now, we'll just log that tables should be created
      console.log('Database tables should be created via Supabase migrations');
      
      // You can create tables manually in Supabase dashboard or via SQL:
      /*
      CREATE TABLE customer_profiles (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR UNIQUE NOT NULL,
        first_name VARCHAR,
        last_name VARCHAR,
        phone VARCHAR,
        avatar_url VARCHAR,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        last_active_at TIMESTAMP WITH TIME ZONE,
        total_orders INTEGER DEFAULT 0,
        total_spent DECIMAL DEFAULT 0,
        avg_order_value DECIMAL DEFAULT 0,
        engagement_score DECIMAL DEFAULT 0,
        churn_risk_score DECIMAL DEFAULT 0,
        value_tier VARCHAR DEFAULT 'low',
        tags TEXT[],
        metadata JSONB DEFAULT '{}'
      );

      CREATE TABLE customer_segments (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR NOT NULL,
        description TEXT,
        type VARCHAR NOT NULL,
        criteria JSONB DEFAULT '{}',
        customer_count INTEGER DEFAULT 0,
        growth_rate DECIMAL DEFAULT 0,
        is_auto_updating BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      CREATE TABLE analytics_data (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        total_customers INTEGER DEFAULT 0,
        active_customers INTEGER DEFAULT 0,
        monthly_revenue DECIMAL DEFAULT 0,
        conversion_rate DECIMAL DEFAULT 0,
        churn_rate DECIMAL DEFAULT 0,
        avg_order_value DECIMAL DEFAULT 0,
        customer_lifetime_value DECIMAL DEFAULT 0,
        engagement_score DECIMAL DEFAULT 0,
        period VARCHAR DEFAULT 'current_month',
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      CREATE TABLE integration_statuses (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR NOT NULL,
        provider VARCHAR NOT NULL,
        category VARCHAR NOT NULL,
        status VARCHAR DEFAULT 'disconnected',
        last_sync TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        records_synced INTEGER DEFAULT 0,
        health_score DECIMAL DEFAULT 0,
        config JSONB DEFAULT '{}'
      );
      */
    } catch (error) {
      console.error('Error initializing tables:', error);
    }
  }

  /**
   * Private helper methods
   */
  private getCachedData(key: string): any {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data;
    }
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  private calculateValueTier(totalSpent: number): 'high' | 'medium' | 'low' {
    if (totalSpent > 5000) return 'high';
    if (totalSpent > 1000) return 'medium';
    return 'low';
  }

  private generateSampleCustomerProfiles(count: number): CustomerProfile[] {
    const profiles: CustomerProfile[] = [];
    const firstNames = ['Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Huỳnh', 'Phan', 'Vũ', 'Võ', 'Đặng'];
    const lastNames = ['Văn An', 'Thị Bình', 'Minh Châu', 'Thị Dung', 'Văn Em', 'Thị Phương', 'Minh Giang', 'Thị Hoa'];

    for (let i = 0; i < count; i++) {
      const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
      const totalSpent = Math.random() * 10000;
      
      profiles.push({
        id: `customer_${i + 1}`,
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase().replace(/\s+/g, '')}@example.com`,
        first_name: firstName,
        last_name: lastName,
        phone: `+84${Math.floor(Math.random() * 1000000000)}`,
        created_at: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
        last_active_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        total_orders: Math.floor(Math.random() * 50) + 1,
        total_spent: totalSpent,
        avg_order_value: totalSpent / (Math.floor(Math.random() * 10) + 1),
        engagement_score: Math.random(),
        churn_risk_score: Math.random(),
        value_tier: this.calculateValueTier(totalSpent),
        tags: ['sample', 'generated'],
        metadata: { source: 'sample_data' }
      });
    }

    return profiles;
  }

  private generateSampleSegments(): CustomerSegment[] {
    return [
      {
        id: 'segment_1',
        name: 'Khách hàng VIP',
        description: 'Khách hàng có giá trị cao với chi tiêu trên 5 triệu',
        type: 'value_based',
        criteria: { total_spent: { gte: 5000000 } },
        customer_count: 1247,
        growth_rate: 0.15,
        is_auto_updating: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 'segment_2',
        name: 'Khách hàng mới',
        description: 'Khách hàng đăng ký trong 30 ngày qua',
        type: 'behavioral',
        criteria: { created_at: { gte: '30_days_ago' } },
        customer_count: 856,
        growth_rate: 0.23,
        is_auto_updating: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: 'segment_3',
        name: 'Có nguy cơ rời bỏ',
        description: 'Khách hàng có điểm rủi ro cao',
        type: 'predictive',
        criteria: { churn_risk_score: { gte: 0.7 } },
        customer_count: 342,
        growth_rate: -0.08,
        is_auto_updating: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];
  }

  private generateSampleAnalyticsData(): AnalyticsData {
    return {
      total_customers: 12847,
      active_customers: 8934,
      monthly_revenue: 2450000,
      conversion_rate: 0.032,
      churn_rate: 0.025,
      avg_order_value: 156.75,
      customer_lifetime_value: 2340,
      engagement_score: 0.67,
      period: 'current_month',
      timestamp: new Date()
    };
  }

  private generateSampleIntegrations(): IntegrationStatus[] {
    return [
      {
        id: 'integration_1',
        name: 'Mailchimp',
        provider: 'mailchimp',
        category: 'email',
        status: 'connected',
        last_sync: new Date(),
        records_synced: 12847,
        health_score: 0.95,
        config: { api_key: '***', list_id: 'abc123' }
      },
      {
        id: 'integration_2',
        name: 'Google Analytics',
        provider: 'google',
        category: 'analytics',
        status: 'connected',
        last_sync: new Date(),
        records_synced: 45678,
        health_score: 0.88,
        config: { property_id: 'GA_123456' }
      },
      {
        id: 'integration_3',
        name: 'Shopify',
        provider: 'shopify',
        category: 'ecommerce',
        status: 'connected',
        last_sync: new Date(),
        records_synced: 8934,
        health_score: 0.92,
        config: { shop_domain: 'myshop.myshopify.com' }
      }
    ];
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const { data, error } = await this.supabase.from('customer_profiles').select('count').limit(1);
      
      return {
        status: 'healthy',
        database: error ? 'unhealthy' : 'healthy',
        cache_size: this.cache.size,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
