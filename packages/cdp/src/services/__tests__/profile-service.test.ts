import { ProfileService } from '../profile-service';
import { mockSupabaseClient, mockRedisClient, createTestCustomerProfile, createTestCustomerIdentity } from '../../../tests/setup';
import { ProfileNotFoundError, IdentityResolutionError } from '../../types';

describe('ProfileService', () => {
  let profileService: ProfileService;

  beforeEach(() => {
    profileService = new ProfileService(mockSupabaseClient as any, mockRedisClient as any);
  });

  describe('initialize', () => {
    it('should initialize successfully with valid database connection', async () => {
      mockSupabaseClient.from().select().limit().mockResolvedValue({
        data: [],
        error: null,
      });

      await expect(profileService.initialize()).resolves.not.toThrow();
    });

    it('should throw error if database connection fails', async () => {
      mockSupabaseClient.from().select().limit().mockResolvedValue({
        data: null,
        error: { message: 'Connection failed', code: 'CONNECTION_ERROR' },
      });

      await expect(profileService.initialize()).rejects.toThrow('Failed to initialize ProfileService');
    });
  });

  describe('getProfile', () => {
    const testProfile = createTestCustomerProfile();

    it('should return profile from cache if available', async () => {
      const cachedProfile = JSON.stringify(testProfile);
      mockRedisClient.get.mockResolvedValue(cachedProfile);

      const result = await profileService.getProfile('test-profile-id');

      expect(mockRedisClient.get).toHaveBeenCalledWith('cdp:profile:test-profile-id');
      expect(result).toEqual(testProfile);
      expect(mockSupabaseClient.from).not.toHaveBeenCalled();
    });

    it('should fetch profile from database if not in cache', async () => {
      mockRedisClient.get.mockResolvedValue(null);
      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: {
          id: 'test-profile-id',
          account_id: 'test-account-id',
          primary_email: '<EMAIL>',
          first_name: 'John',
          last_name: 'Doe',
          total_sessions: 10,
          total_revenue: '1000000',
          email_engagement_score: '0.7',
          churn_risk_score: '0.2',
          lifecycle_stage: 'customer',
          customer_value_tier: 'medium',
          demographics: {},
          preferences: {},
          custom_attributes: {},
          tags: [],
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          computed_at: '2024-01-01T00:00:00Z',
          customer_identities: [],
        },
        error: null,
      });

      const result = await profileService.getProfile('test-profile-id');

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('customer_profiles');
      expect(mockRedisClient.setex).toHaveBeenCalled();
      expect(result.id).toBe('test-profile-id');
      expect(result.primary_email).toBe('<EMAIL>');
    });

    it('should throw ProfileNotFoundError if profile does not exist', async () => {
      mockRedisClient.get.mockResolvedValue(null);
      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: null,
        error: { message: 'No rows returned', code: 'PGRST116' },
      });

      await expect(profileService.getProfile('non-existent-id')).rejects.toThrow(ProfileNotFoundError);
    });
  });

  describe('createOrUpdateProfile', () => {
    const testIdentities = [
      createTestCustomerIdentity({ type: 'email', value: '<EMAIL>' }),
      createTestCustomerIdentity({ type: 'user_id', value: 'user-123' }),
    ];

    it('should create new profile if no existing profile found', async () => {
      // Mock identity resolution to return null (no existing profile)
      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: null,
        error: { message: 'No rows returned', code: 'PGRST116' },
      });

      // Mock profile creation
      mockSupabaseClient.rpc.mockResolvedValue({
        data: { id: 'new-profile-id' },
        error: null,
      });

      // Mock getProfile call after creation
      const newProfile = createTestCustomerProfile({ id: 'new-profile-id' });
      mockRedisClient.get.mockResolvedValue(null);
      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: {
          ...newProfile,
          created_at: newProfile.created_at.toISOString(),
          updated_at: newProfile.updated_at.toISOString(),
          computed_at: newProfile.computed_at.toISOString(),
          customer_identities: testIdentities,
        },
        error: null,
      });

      const result = await profileService.createOrUpdateProfile(
        'test-account-id',
        testIdentities,
        { first_name: 'John', last_name: 'Doe' }
      );

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
        'create_customer_profile_with_identities',
        expect.any(Object)
      );
      expect(result.id).toBe('new-profile-id');
    });

    it('should update existing profile if found', async () => {
      const existingProfile = createTestCustomerProfile();
      
      // Mock identity resolution to return existing profile
      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: {
          customer_profile_id: existingProfile.id,
          customer_profiles: {
            ...existingProfile,
            customer_identities: testIdentities,
          },
        },
        error: null,
      });

      // Mock profile update
      mockSupabaseClient.rpc.mockResolvedValue({
        data: { id: existingProfile.id },
        error: null,
      });

      // Mock getProfile call after update
      mockRedisClient.get.mockResolvedValue(null);
      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: {
          ...existingProfile,
          first_name: 'Updated John',
          customer_identities: testIdentities,
        },
        error: null,
      });

      const result = await profileService.createOrUpdateProfile(
        'test-account-id',
        testIdentities,
        { first_name: 'Updated John' }
      );

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith(
        'update_customer_profile_with_identities',
        expect.any(Object)
      );
      expect(result.first_name).toBe('Updated John');
    });
  });

  describe('resolveIdentity', () => {
    const testIdentities = [
      createTestCustomerIdentity({ type: 'email', value: '<EMAIL>' }),
      createTestCustomerIdentity({ type: 'user_id', value: 'user-123' }),
    ];

    it('should return existing profile if identity matches', async () => {
      const existingProfile = createTestCustomerProfile();
      
      mockSupabaseClient.from().select().eq().single
        .mockResolvedValueOnce({
          data: {
            customer_profile_id: existingProfile.id,
            customer_profiles: existingProfile,
          },
          error: null,
        });

      const result = await profileService.resolveIdentity('test-account-id', testIdentities);

      expect(result).toBeTruthy();
      expect(result?.id).toBe(existingProfile.id);
    });

    it('should return null if no matching identity found', async () => {
      mockSupabaseClient.from().select().eq().single.mockResolvedValue({
        data: null,
        error: { message: 'No rows returned', code: 'PGRST116' },
      });

      const result = await profileService.resolveIdentity('test-account-id', testIdentities);

      expect(result).toBeNull();
    });

    it('should try multiple identities until one matches', async () => {
      const existingProfile = createTestCustomerProfile();
      
      // First identity doesn't match
      mockSupabaseClient.from().select().eq().single
        .mockResolvedValueOnce({
          data: null,
          error: { message: 'No rows returned', code: 'PGRST116' },
        })
        // Second identity matches
        .mockResolvedValueOnce({
          data: {
            customer_profile_id: existingProfile.id,
            customer_profiles: existingProfile,
          },
          error: null,
        });

      const result = await profileService.resolveIdentity('test-account-id', testIdentities);

      expect(mockSupabaseClient.from().select().eq().single).toHaveBeenCalledTimes(2);
      expect(result?.id).toBe(existingProfile.id);
    });
  });

  describe('searchProfiles', () => {
    it('should search profiles with criteria and return results', async () => {
      const testProfiles = [
        createTestCustomerProfile({ id: 'profile-1' }),
        createTestCustomerProfile({ id: 'profile-2' }),
      ];

      mockSupabaseClient.from().select().eq().range().order.mockResolvedValue({
        data: testProfiles.map(profile => ({
          ...profile,
          customer_identities: [],
        })),
        error: null,
        count: 2,
      });

      const result = await profileService.searchProfiles(
        'test-account-id',
        { lifecycle_stage: 'customer' },
        10,
        0
      );

      expect(result.profiles).toHaveLength(2);
      expect(result.total).toBe(2);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('customer_profiles');
    });

    it('should handle empty search results', async () => {
      mockSupabaseClient.from().select().eq().range().order.mockResolvedValue({
        data: [],
        error: null,
        count: 0,
      });

      const result = await profileService.searchProfiles(
        'test-account-id',
        { lifecycle_stage: 'prospect' },
        10,
        0
      );

      expect(result.profiles).toHaveLength(0);
      expect(result.total).toBe(0);
    });
  });

  describe('deleteProfile', () => {
    it('should delete profile and clear cache', async () => {
      mockSupabaseClient.from().delete().eq.mockResolvedValue({
        data: null,
        error: null,
      });

      await profileService.deleteProfile('test-profile-id');

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('customer_profiles');
      expect(mockRedisClient.del).toHaveBeenCalledWith('cdp:profile:test-profile-id');
    });

    it('should throw error if deletion fails', async () => {
      mockSupabaseClient.from().delete().eq.mockResolvedValue({
        data: null,
        error: { message: 'Deletion failed', code: 'DELETE_ERROR' },
      });

      await expect(profileService.deleteProfile('test-profile-id')).rejects.toThrow('Failed to delete profile');
    });
  });

  describe('getHealthStatus', () => {
    it('should return healthy status when all systems are working', async () => {
      mockSupabaseClient.from().select().limit.mockResolvedValue({
        data: [],
        error: null,
      });
      mockRedisClient.ping.mockResolvedValue('PONG');

      const status = await profileService.getHealthStatus();

      expect(status.status).toBe('healthy');
      expect(status.database).toBe('healthy');
      expect(status.cache).toBe('healthy');
    });

    it('should return unhealthy status when database fails', async () => {
      mockSupabaseClient.from().select().limit.mockResolvedValue({
        data: null,
        error: { message: 'Database error', code: 'DB_ERROR' },
      });

      const status = await profileService.getHealthStatus();

      expect(status.status).toBe('unhealthy');
      expect(status.database).toBe('unhealthy');
      expect(status.error).toBe('Database error');
    });

    it('should return cache disabled status when Redis is not available', async () => {
      const profileServiceWithoutRedis = new ProfileService(mockSupabaseClient as any);
      
      mockSupabaseClient.from().select().limit.mockResolvedValue({
        data: [],
        error: null,
      });

      const status = await profileServiceWithoutRedis.getHealthStatus();

      expect(status.cache).toBe('disabled');
    });
  });
});
