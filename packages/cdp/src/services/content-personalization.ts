import { SupabaseClient } from '@supabase/supabase-js';
import Redis from 'ioredis';
import { CDPService, CDPError } from '../types';

export interface PersonalizedContent {
  id: string;
  customer_id: string;
  content_type: 'email' | 'web' | 'mobile' | 'sms' | 'push';
  template_id: string;
  personalized_data: Record<string, any>;
  confidence: number;
  ab_test_variant?: string;
  created_at: Date;
  expires_at?: Date;
}

export interface ContentTemplate {
  id: string;
  name: string;
  type: 'email' | 'web' | 'mobile' | 'sms' | 'push';
  content: string;
  variables: string[];
  rules: PersonalizationRule[];
  created_at: Date;
  updated_at: Date;
}

export interface PersonalizationRule {
  id: string;
  condition: string;
  action: 'replace' | 'show' | 'hide' | 'modify';
  target: string;
  value: any;
  priority: number;
}

export interface PersonalizationRequest {
  customer_id: string;
  content_type: 'email' | 'web' | 'mobile' | 'sms' | 'push';
  template_id?: string;
  context: Record<string, any>;
  ab_test?: {
    test_id: string;
    variant: string;
  };
}

/**
 * Content Personalization Service
 * Provides AI-powered content personalization and dynamic content generation
 */
export class ContentPersonalization implements CDPService {
  private readonly CONTENT_PREFIX = 'cdp:personalized_content:';
  private readonly TEMPLATES_PREFIX = 'cdp:content_templates:';
  private readonly RULES_PREFIX = 'cdp:personalization_rules:';
  private readonly PERFORMANCE_PREFIX = 'cdp:content_performance:';

  private templates: Map<string, ContentTemplate> = new Map();
  private isProcessing = false;

  constructor(
    private supabase: SupabaseClient,
    private redis: Redis
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.redis.ping();

      // Load content templates
      await this.loadContentTemplates();

      // Initialize default templates
      await this.initializeDefaultTemplates();

      console.log('ContentPersonalization initialized successfully');
    } catch (error) {
      throw new CDPError(
        'Failed to initialize ContentPersonalization',
        'CONTENT_PERSONALIZATION_INIT_FAILED',
        { error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  async destroy(): Promise<void> {
    this.isProcessing = false;
    console.log('ContentPersonalization destroyed');
  }

  /**
   * Personalize content for a customer
   */
  async personalizeContent(request: PersonalizationRequest): Promise<PersonalizedContent> {
    try {
      const { customer_id, content_type, template_id, context, ab_test } = request;

      // Get customer data
      const customerData = await this.getCustomerData(customer_id);

      // Get or select template
      const template = template_id
        ? await this.getTemplate(template_id)
        : await this.selectBestTemplate(customerData, content_type, context);

      if (!template) {
        throw new Error('No suitable template found');
      }

      // Apply personalization rules
      const personalizedData = await this.applyPersonalizationRules(
        template,
        customerData,
        context
      );

      // Generate dynamic content
      const dynamicContent = await this.generateDynamicContent(
        template,
        personalizedData,
        customerData
      );

      // Create personalized content
      const personalizedContent: PersonalizedContent = {
        id: `content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        customer_id,
        content_type,
        template_id: template.id,
        personalized_data: {
          ...personalizedData,
          content: dynamicContent,
          personalization_score: this.calculatePersonalizationScore(personalizedData)
        },
        confidence: this.calculateConfidence(customerData, template),
        ab_test_variant: ab_test?.variant,
        created_at: new Date(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      };

      // Cache personalized content
      await this.cachePersonalizedContent(personalizedContent);

      // Track personalization event
      await this.trackPersonalizationEvent(personalizedContent);

      return personalizedContent;
    } catch (error) {
      throw new CDPError(
        'Failed to personalize content',
        'CONTENT_PERSONALIZATION_FAILED',
        { request, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get personalized email content
   */
  async personalizeEmail(
    customerId: string,
    emailType: 'welcome' | 'promotional' | 'transactional' | 'newsletter',
    context: Record<string, any> = {}
  ): Promise<PersonalizedContent> {
    return this.personalizeContent({
      customer_id: customerId,
      content_type: 'email',
      context: { ...context, email_type: emailType }
    });
  }

  /**
   * Get personalized web content
   */
  async personalizeWebContent(
    customerId: string,
    pageType: 'homepage' | 'product' | 'category' | 'checkout',
    context: Record<string, any> = {}
  ): Promise<PersonalizedContent> {
    return this.personalizeContent({
      customer_id: customerId,
      content_type: 'web',
      context: { ...context, page_type: pageType }
    });
  }

  /**
   * Get personalized mobile content
   */
  async personalizeMobileContent(
    customerId: string,
    screenType: 'home' | 'product' | 'profile' | 'notifications',
    context: Record<string, any> = {}
  ): Promise<PersonalizedContent> {
    return this.personalizeContent({
      customer_id: customerId,
      content_type: 'mobile',
      context: { ...context, screen_type: screenType }
    });
  }

  /**
   * Create or update content template
   */
  async createTemplate(template: Omit<ContentTemplate, 'id' | 'created_at' | 'updated_at'>): Promise<ContentTemplate> {
    try {
      const newTemplate: ContentTemplate = {
        ...template,
        id: `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        created_at: new Date(),
        updated_at: new Date()
      };

      // Store template
      await this.redis.set(
        `${this.TEMPLATES_PREFIX}${newTemplate.id}`,
        JSON.stringify(newTemplate)
      );

      this.templates.set(newTemplate.id, newTemplate);

      return newTemplate;
    } catch (error) {
      throw new CDPError(
        'Failed to create template',
        'TEMPLATE_CREATE_FAILED',
        { template, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get content performance analytics
   */
  async getContentPerformance(timeRange: { start: Date; end: Date }): Promise<any> {
    try {
      const performanceKeys = await this.redis.keys(`${this.PERFORMANCE_PREFIX}*`);
      const performance: Record<string, any> = {};

      for (const key of performanceKeys) {
        const data = await this.redis.hgetall(key);
        const templateId = key.replace(this.PERFORMANCE_PREFIX, '');

        performance[templateId] = {
          impressions: parseInt(data.impressions || '0'),
          clicks: parseInt(data.clicks || '0'),
          conversions: parseInt(data.conversions || '0'),
          revenue: parseFloat(data.revenue || '0'),
          ctr: data.impressions ? (parseInt(data.clicks || '0') / parseInt(data.impressions)) * 100 : 0,
          conversionRate: data.clicks ? (parseInt(data.conversions || '0') / parseInt(data.clicks)) * 100 : 0
        };
      }

      return {
        templates: performance,
        summary: this.calculatePerformanceSummary(performance),
        timeRange
      };
    } catch (error) {
      throw new CDPError(
        'Failed to get content performance',
        'CONTENT_PERFORMANCE_FAILED',
        { timeRange, error: error instanceof Error ? error.message : String(error) }
      );
    }
  }

  /**
   * Get customer data for personalization
   */
  private async getCustomerData(customerId: string): Promise<any> {
    try {
      // Get customer profile
      const { data: profile } = await this.supabase
        .from('customer_profiles')
        .select('*')
        .eq('id', customerId)
        .single();

      // Get recent events
      const { data: events } = await this.supabase
        .from('analytics_events')
        .select('*')
        .eq('customer_profile_id', customerId)
        .order('timestamp', { ascending: false })
        .limit(100);

      // Get preferences from cache
      const preferences = await this.getCustomerPreferences(customerId);

      return {
        id: customerId,
        profile: profile || {},
        events: events || [],
        preferences,
        behavioral_data: this.analyzeBehavioralData(events || [])
      };
    } catch (error) {
      console.error('Failed to get customer data:', error);
      return {
        id: customerId,
        profile: {},
        events: [],
        preferences: {},
        behavioral_data: {}
      };
    }
  }

  /**
   * Select best template for customer
   */
  private async selectBestTemplate(
    customerData: any,
    contentType: string,
    context: Record<string, any>
  ): Promise<ContentTemplate | null> {
    const availableTemplates = Array.from(this.templates.values())
      .filter(template => template.type === contentType);

    if (availableTemplates.length === 0) {
      return null;
    }

    // Score templates based on customer data
    const scoredTemplates = availableTemplates.map(template => ({
      template,
      score: this.scoreTemplate(template, customerData, context)
    }));

    // Return highest scoring template
    scoredTemplates.sort((a, b) => b.score - a.score);
    return scoredTemplates[0]?.template || null;
  }

  /**
   * Apply personalization rules
   */
  private async applyPersonalizationRules(
    template: ContentTemplate,
    customerData: any,
    context: Record<string, any>
  ): Promise<Record<string, any>> {
    const personalizedData: Record<string, any> = {
      customer_name: customerData.profile.first_name || 'Valued Customer',
      customer_email: customerData.profile.email || '',
      customer_tier: this.determineCustomerTier(customerData),
      personalization_timestamp: new Date().toISOString()
    };

    // Apply template rules
    for (const rule of template.rules) {
      if (this.evaluateCondition(rule.condition, customerData, context)) {
        personalizedData[rule.target] = this.applyRuleAction(rule, customerData, context);
      }
    }

    // Add behavioral personalization
    const behavioralData = this.generateBehavioralPersonalization(customerData);
    Object.assign(personalizedData, behavioralData);

    // Add contextual personalization
    const contextualData = this.generateContextualPersonalization(context, customerData);
    Object.assign(personalizedData, contextualData);

    return personalizedData;
  }

  /**
   * Generate dynamic content
   */
  private async generateDynamicContent(
    template: ContentTemplate,
    personalizedData: Record<string, any>,
    customerData: any
  ): Promise<string> {
    let content = template.content;

    // Replace variables with personalized data
    for (const variable of template.variables) {
      const value = personalizedData[variable] || this.getDefaultValue(variable);
      const regex = new RegExp(`{{${variable}}}`, 'g');
      content = content.replace(regex, String(value));
    }

    // Apply AI-powered content optimization
    content = await this.optimizeContent(content, customerData);

    return content;
  }

  /**
   * Score template for customer
   */
  private scoreTemplate(
    template: ContentTemplate,
    customerData: any,
    context: Record<string, any>
  ): number {
    let score = 0.5; // Base score

    // Score based on customer tier
    const customerTier = this.determineCustomerTier(customerData);
    if (template.name.toLowerCase().includes(customerTier)) {
      score += 0.3;
    }

    // Score based on behavioral data
    const behavioralScore = this.calculateBehavioralScore(template, customerData);
    score += behavioralScore * 0.2;

    // Score based on context
    const contextualScore = this.calculateContextualScore(template, context);
    score += contextualScore * 0.2;

    return Math.min(1, score);
  }

  /**
   * Evaluate personalization rule condition
   */
  private evaluateCondition(
    condition: string,
    customerData: any,
    context: Record<string, any>
  ): boolean {
    try {
      // Simple condition evaluation (in production, use a proper expression evaluator)
      if (condition.includes('customer_tier')) {
        const tier = this.determineCustomerTier(customerData);
        return condition.includes(tier);
      }

      if (condition.includes('total_revenue')) {
        const revenue = customerData.profile.total_revenue || 0;
        if (condition.includes('>')) {
          const parts = condition.split('>');
          const threshold = parts[1] ? parseInt(parts[1].trim()) : 0;
          return revenue > threshold;
        }
      }

      return true; // Default to true for demo
    } catch (error) {
      return false;
    }
  }

  /**
   * Apply rule action
   */
  private applyRuleAction(
    rule: PersonalizationRule,
    customerData: any,
    context: Record<string, any>
  ): any {
    switch (rule.action) {
      case 'replace':
        return rule.value;
      case 'show':
        return true;
      case 'hide':
        return false;
      case 'modify':
        return this.modifyValue(rule.value, customerData);
      default:
        return rule.value;
    }
  }

  /**
   * Generate behavioral personalization
   */
  private generateBehavioralPersonalization(customerData: any): Record<string, any> {
    const behavioral: Record<string, any> = {};

    // Analyze recent events
    const recentEvents = customerData.events.slice(0, 20);
    const eventTypes = recentEvents.map((e: any) => e.event_type);

    // Product recommendations based on behavior
    if (eventTypes.includes('product_view')) {
      behavioral.show_product_recommendations = true;
      behavioral.recommendation_type = 'viewed_products';
    }

    // Cart abandonment
    if (eventTypes.includes('add_to_cart') && !eventTypes.includes('purchase')) {
      behavioral.show_cart_reminder = true;
      behavioral.cart_urgency = 'high';
    }

    // Engagement level
    const engagementLevel = this.calculateEngagementLevel(customerData);
    behavioral.engagement_level = engagementLevel;
    behavioral.content_complexity = engagementLevel > 0.7 ? 'detailed' : 'simple';

    return behavioral;
  }

  /**
   * Generate contextual personalization
   */
  private generateContextualPersonalization(
    context: Record<string, any>,
    customerData: any
  ): Record<string, any> {
    const contextual: Record<string, any> = {};

    // Time-based personalization
    const hour = new Date().getHours();
    if (hour < 12) {
      contextual.greeting = 'Good morning';
    } else if (hour < 18) {
      contextual.greeting = 'Good afternoon';
    } else {
      contextual.greeting = 'Good evening';
    }

    // Location-based (if available)
    if (context.location) {
      contextual.location_specific_content = true;
      contextual.local_offers = true;
    }

    // Device-based
    if (context.device_type === 'mobile') {
      contextual.mobile_optimized = true;
      contextual.content_length = 'short';
    }

    return contextual;
  }

  /**
   * Optimize content using AI
   */
  private async optimizeContent(content: string, customerData: any): Promise<string> {
    // AI-powered content optimization would go here
    // For now, apply simple optimizations

    // Adjust tone based on customer tier
    const tier = this.determineCustomerTier(customerData);
    if (tier === 'vip') {
      content = content.replace(/Hi/g, 'Dear');
      content = content.replace(/Thanks/g, 'Thank you');
    }

    // Add urgency for high-value customers
    if ((customerData.profile.total_revenue || 0) > 5000000) {
      content = content.replace(/Limited time/g, 'Exclusive limited time');
    }

    return content;
  }

  /**
   * Helper methods
   */
  private determineCustomerTier(customerData: any): string {
    const revenue = customerData.profile.total_revenue || 0;
    if (revenue >= 10000000) return 'vip';
    if (revenue >= 5000000) return 'premium';
    if (revenue >= 1000000) return 'standard';
    return 'basic';
  }

  private calculateEngagementLevel(customerData: any): number {
    const recentEvents = customerData.events.slice(0, 30);
    return Math.min(1, recentEvents.length / 30);
  }

  private calculateBehavioralScore(template: ContentTemplate, customerData: any): number {
    return Math.random() * 0.5 + 0.3; // Simplified
  }

  private calculateContextualScore(template: ContentTemplate, context: Record<string, any>): number {
    return Math.random() * 0.3 + 0.2; // Simplified
  }

  private calculatePersonalizationScore(personalizedData: Record<string, any>): number {
    const personalizedFields = Object.keys(personalizedData).length;
    return Math.min(1, personalizedFields / 10);
  }

  private calculateConfidence(customerData: any, template: ContentTemplate): number {
    const dataQuality = Object.keys(customerData.profile).length / 10;
    const templateQuality = template.rules.length / 5;
    return Math.min(1, (dataQuality + templateQuality) / 2);
  }

  private modifyValue(value: any, customerData: any): any {
    return value; // Simplified
  }

  private getDefaultValue(variable: string): string {
    const defaults: Record<string, string> = {
      customer_name: 'Valued Customer',
      customer_email: '',
      greeting: 'Hello',
      company_name: 'Our Company'
    };
    return defaults[variable] || '';
  }

  private analyzeBehavioralData(events: any[]): any {
    return {
      most_viewed_categories: [],
      purchase_frequency: 'low',
      preferred_channels: ['email']
    };
  }

  private async getCustomerPreferences(customerId: string): Promise<any> {
    const preferencesData = await this.redis.get(`customer_preferences:${customerId}`);
    return preferencesData ? JSON.parse(preferencesData) : {};
  }

  private async getTemplate(templateId: string): Promise<ContentTemplate | null> {
    return this.templates.get(templateId) || null;
  }

  private async cachePersonalizedContent(content: PersonalizedContent): Promise<void> {
    const key = `${this.CONTENT_PREFIX}${content.customer_id}:${content.content_type}`;
    await this.redis.setex(key, 3600, JSON.stringify(content)); // Cache for 1 hour
  }

  private async trackPersonalizationEvent(content: PersonalizedContent): Promise<void> {
    // Track personalization performance
    const performanceKey = `${this.PERFORMANCE_PREFIX}${content.template_id}`;
    await this.redis.hincrby(performanceKey, 'impressions', 1);
  }

  private calculatePerformanceSummary(performance: Record<string, any>): any {
    const templates = Object.values(performance);
    const totalImpressions = templates.reduce((sum: number, t: any) => sum + t.impressions, 0);
    const totalClicks = templates.reduce((sum: number, t: any) => sum + t.clicks, 0);
    const totalConversions = templates.reduce((sum: number, t: any) => sum + t.conversions, 0);

    return {
      totalImpressions,
      totalClicks,
      totalConversions,
      overallCTR: totalImpressions ? (totalClicks / totalImpressions) * 100 : 0,
      overallConversionRate: totalClicks ? (totalConversions / totalClicks) * 100 : 0
    };
  }

  private async loadContentTemplates(): Promise<void> {
    const templateKeys = await this.redis.keys(`${this.TEMPLATES_PREFIX}*`);

    for (const key of templateKeys) {
      const templateData = await this.redis.get(key);
      if (templateData) {
        const template = JSON.parse(templateData);
        this.templates.set(template.id, template);
      }
    }
  }

  private async initializeDefaultTemplates(): Promise<void> {
    const defaultTemplates: Omit<ContentTemplate, 'id' | 'created_at' | 'updated_at'>[] = [
      {
        name: 'Welcome Email - VIP',
        type: 'email',
        content: `
          <h1>{{greeting}}, {{customer_name}}!</h1>
          <p>Welcome to our exclusive VIP program. As a valued customer, you have access to premium features and personalized service.</p>
          <p>Your customer tier: {{customer_tier}}</p>
          {{#if show_product_recommendations}}
          <div>
            <h3>Recommended for you:</h3>
            <!-- Product recommendations -->
          </div>
          {{/if}}
        `,
        variables: ['greeting', 'customer_name', 'customer_tier'],
        rules: [
          {
            id: 'vip_rule',
            condition: 'customer_tier == "vip"',
            action: 'show',
            target: 'vip_content',
            value: true,
            priority: 1
          }
        ]
      },
      {
        name: 'Homepage Banner - Personalized',
        type: 'web',
        content: `
          <div class="hero-banner">
            <h2>{{greeting}}, {{customer_name}}!</h2>
            <p>{{#if engagement_level > 0.7}}Discover new arrivals tailored just for you{{else}}Welcome back! Check out what's new{{/if}}</p>
            {{#if show_cart_reminder}}
            <div class="cart-reminder">
              <p>You have items waiting in your cart!</p>
              <button>Complete Purchase</button>
            </div>
            {{/if}}
          </div>
        `,
        variables: ['greeting', 'customer_name', 'engagement_level'],
        rules: [
          {
            id: 'cart_reminder_rule',
            condition: 'show_cart_reminder == true',
            action: 'show',
            target: 'cart_reminder',
            value: true,
            priority: 1
          }
        ]
      }
    ];

    for (const template of defaultTemplates) {
      await this.createTemplate(template);
    }
  }

  /**
   * Get health status
   */
  async getHealthStatus(): Promise<Record<string, any>> {
    try {
      const redisHealth = await this.redis.ping();

      return {
        status: 'healthy',
        redis: redisHealth === 'PONG' ? 'healthy' : 'unhealthy',
        templatesLoaded: this.templates.size,
        isProcessing: this.isProcessing,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }
}
