# @kit/cdp - Customer Data Platform

A comprehensive Customer Data Platform (CDP) module for unified customer profiles, segmentation, and marketing automation.

## 🎯 Overview

The CDP module provides:

- **Unified Customer Profiles** - 360° view of customers across all touchpoints
- **Identity Resolution** - Merge customer data from multiple sources
- **Real-time Segmentation** - Dynamic customer segments based on behavior
- **Journey Orchestration** - Automated marketing workflows
- **Predictive Analytics** - Churn prediction, LTV calculation, and more
- **Marketing Activation** - Multi-channel campaign execution

## 🚀 Quick Start

### Installation

```bash
npm install @kit/cdp
```

### Basic Usage

```typescript
import { createCDPManager, defaultCDPConfig } from '@kit/cdp';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_ANON_KEY!
);

// Create CDP Manager
const cdp = createCDPManager(defaultCDPConfig, supabase);

// Initialize CDP
await cdp.initialize();

// Get Profile Service
const profileService = cdp.getService('profile');

// Create or update customer profile
const profile = await profileService.createOrUpdateProfile(
  'account-id',
  [
    {
      type: 'email',
      value: '<EMAIL>',
      verified: true,
      source: 'website',
      first_seen_at: new Date(),
      created_at: new Date()
    }
  ],
  {
    first_name: 'John',
    last_name: 'Doe',
    behavior: {
      total_sessions: 5,
      total_pageviews: 25,
      total_revenue: 1000000,
      first_seen_at: new Date(),
    }
  }
);

console.log('Customer Profile:', profile);
```

## 📊 Core Features

### Customer Profiles

```typescript
// Get profile by ID
const profile = await profileService.getProfile('profile-id');

// Search profiles
const { profiles, total } = await profileService.searchProfiles(
  'account-id',
  { lifecycle_stage: 'customer' },
  50, // limit
  0   // offset
);

// Update profile
const updatedProfile = await profileService.updateProfile(
  'profile-id',
  {
    lifecycle_stage: 'advocate',
    custom_attributes: {
      vip_status: true
    }
  }
);
```

### Identity Resolution

```typescript
// Resolve customer identity across multiple touchpoints
const identities = [
  { type: 'email', value: '<EMAIL>', source: 'website' },
  { type: 'user_id', value: 'user-123', source: 'app' },
  { type: 'visitor_id', value: 'visitor-456', source: 'analytics' }
];

const profile = await profileService.resolveIdentity('account-id', identities);
```

### Behavioral Tracking

```typescript
// Update customer behavior from analytics events
await profileService.updateProfile('profile-id', {
  behavior: {
    total_sessions: profile.behavior.total_sessions + 1,
    total_pageviews: profile.behavior.total_pageviews + 5,
    last_activity_at: new Date()
  }
});
```

## 🏗️ Architecture

### Services

- **ProfileService** - Customer profile management and identity resolution
- **SegmentationService** - Dynamic customer segmentation (coming soon)
- **JourneyService** - Customer journey orchestration (coming soon)
- **PredictiveService** - ML-powered analytics (coming soon)

### Data Flow

```
Analytics Events → Profile Updates → Segment Evaluation → Journey Triggers → Marketing Activation
```

### Database Schema

The CDP module uses the following core tables:

- `customer_profiles` - Unified customer profiles
- `customer_identities` - Identity resolution mapping
- `customer_segments` - Segment definitions (coming soon)
- `customer_journeys` - Journey definitions (coming soon)

## ⚙️ Configuration

```typescript
import { CDPConfig } from '@kit/cdp';

const config: CDPConfig = {
  // Real-time processing
  realTimeProcessing: true,
  batchProcessingInterval: 3600, // 1 hour

  // Data retention
  profileRetentionDays: 2555, // 7 years
  eventRetentionDays: 365,    // 1 year

  // Performance
  cacheEnabled: true,
  cacheTTL: 3600,             // 1 hour
  maxConcurrentProcessing: 10,

  // ML features
  enablePredictiveAnalytics: false,
  modelUpdateFrequency: 86400, // 24 hours

  // Integrations
  enabledIntegrations: ['analytics'],
  integrationConfigs: {}
};
```

## 🔌 Integrations

### Analytics Integration

```typescript
// Automatically process analytics events to update customer profiles
cdp.on('analytics.event', async (event) => {
  const identities = extractIdentities(event);
  await profileService.createOrUpdateProfile(
    event.account_id,
    identities,
    {
      behavior: {
        total_sessions: 1, // increment
        last_activity_at: new Date(event.created_at)
      }
    }
  );
});
```

### Custom Integrations

```typescript
// Register custom service
await cdp.registerService('custom', new CustomService());

// Use custom service
const customService = cdp.getService('custom');
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Test Example

```typescript
import { ProfileService } from '@kit/cdp';
import { mockSupabaseClient, createTestCustomerProfile } from '@kit/cdp/tests';

describe('ProfileService', () => {
  let profileService: ProfileService;

  beforeEach(() => {
    profileService = new ProfileService(mockSupabaseClient);
  });

  it('should create customer profile', async () => {
    const profile = await profileService.createOrUpdateProfile(
      'account-id',
      [{ type: 'email', value: '<EMAIL>' }],
      { first_name: 'Test' }
    );

    expect(profile.first_name).toBe('Test');
  });
});
```

## 📈 Performance

### Caching

The CDP module uses Redis for caching to improve performance:

- Customer profiles cached for 1 hour
- Segment memberships cached for 30 minutes
- Predictive scores cached for 2 hours

### Database Optimization

- Optimized indexes for common queries
- Materialized views for analytics
- Partitioning for large datasets

## 🔒 Security

### Row Level Security (RLS)

All tables use RLS policies to ensure data isolation:

```sql
-- Example RLS policy
CREATE POLICY "customer_profiles_read" ON customer_profiles FOR SELECT
  TO authenticated USING (
    account_id = (select auth.uid()) OR
    public.has_role_on_account(account_id)
  );
```

### Data Privacy

- PII data is encrypted at rest
- Sensitive fields are excluded from logs
- GDPR compliance features built-in

## 🚧 Roadmap

### Phase 1 (Current)
- ✅ Customer Profile Management
- ✅ Identity Resolution
- ✅ Basic Analytics Integration

### Phase 2 (Next)
- 🔄 Dynamic Segmentation
- 🔄 Segment Builder UI
- 🔄 Predictive Analytics

### Phase 3 (Future)
- ⏳ Journey Orchestration
- ⏳ Marketing Activation
- ⏳ Advanced ML Models

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

- 📖 [Documentation](./docs/)
- 🐛 [Issue Tracker](https://github.com/your-repo/issues)
- 💬 [Discussions](https://github.com/your-repo/discussions)

---

Built with ❤️ by the Makerkit Team
