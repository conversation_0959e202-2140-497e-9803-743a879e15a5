import { jest } from '@jest/globals';

// Mock Supabase client
const createMockQueryBuilder = () => ({
  select: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  update: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  neq: jest.fn().mockReturnThis(),
  gt: jest.fn().mockReturnThis(),
  gte: jest.fn().mockReturnThis(),
  lt: jest.fn().mockReturnThis(),
  lte: jest.fn().mockReturnThis(),
  like: jest.fn().mockReturnThis(),
  ilike: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis(),
  contains: jest.fn().mockReturnThis(),
  range: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  single: jest.fn(),
  maybeSingle: jest.fn(),
});

export const mockSupabaseClient = {
  from: jest.fn(() => createMockQueryBuilder()),
  rpc: jest.fn(),
  auth: {
    getUser: jest.fn(),
    getSession: jest.fn(),
  },
};

// Mock Redis client
export const mockRedisClient = {
  get: jest.fn(),
  set: jest.fn(),
  setex: jest.fn(),
  del: jest.fn(),
  exists: jest.fn(),
  keys: jest.fn(),
  ping: jest.fn(),
  flushall: jest.fn(),
};

// Test data factories
export const createTestCustomerProfile = (overrides = {}) => ({
  id: 'test-profile-id',
  account_id: 'test-account-id',
  identities: [],
  primary_email: '<EMAIL>',
  primary_phone: '+***********',
  primary_user_id: 'test-user-id',
  first_name: 'John',
  last_name: 'Doe',
  full_name: 'John Doe',
  demographics: {},
  behavior: {
    total_sessions: 10,
    total_pageviews: 50,
    total_events: 100,
    total_purchases: 2,
    total_revenue: 1000000,
    average_order_value: 500000,
    avg_session_duration: 300,
    last_activity_at: new Date(),
    last_order_at: new Date(),
    first_seen_at: new Date(),
    last_seen_at: new Date(),
  },
  preferences: {},
  engagement_scores: {
    email_engagement_score: 0.7,
    website_engagement_score: 0.8,
    social_engagement_score: 0.5,
    overall_engagement_score: 0.67,
  },
  predictive_scores: {
    churn_risk_score: 0.2,
    lifetime_value_score: 1000000,
    purchase_propensity_score: 0.8,
    engagement_propensity_score: 0.7,
    upsell_propensity_score: 0.6,
  },
  lifecycle_stage: 'customer' as const,
  customer_value_tier: 'medium' as const,
  segments: [],
  custom_attributes: {},
  tags: [],
  created_at: new Date(),
  updated_at: new Date(),
  computed_at: new Date(),
  ...overrides,
});

export const createTestCustomerIdentity = (overrides = {}) => ({
  type: 'email' as const,
  value: '<EMAIL>',
  verified: true,
  source: 'test',
  first_seen_at: new Date(),
  created_at: new Date(),
  ...overrides,
});

export const createTestAnalyticsEvent = (overrides = {}) => ({
  id: 'test-event-id',
  account_id: 'test-account-id',
  theme_id: 'test-theme-id',
  event_type: 'pageview',
  event_data: {
    page_path: '/test',
    page_title: 'Test Page',
  },
  created_at: new Date().toISOString(),
  user_id: 'test-user-id',
  visitor_id: 'test-visitor-id',
  device_type: 'desktop',
  source: 'web',
  ...overrides,
});

// Setup global test environment
beforeEach(() => {
  // Reset all mocks
  jest.clearAllMocks();

  // Reset Supabase client mocks
  mockSupabaseClient.from.mockImplementation(() => createMockQueryBuilder());
  mockSupabaseClient.rpc.mockResolvedValue({
    data: null,
    error: null,
  });

  // Reset Redis client mocks
  mockRedisClient.get.mockResolvedValue(null);
  mockRedisClient.set.mockResolvedValue('OK');
  mockRedisClient.setex.mockResolvedValue('OK');
  mockRedisClient.del.mockResolvedValue(1);
  mockRedisClient.exists.mockResolvedValue(0);
  mockRedisClient.keys.mockResolvedValue([]);
  mockRedisClient.ping.mockResolvedValue('PONG');
});

// Global test utilities
global.testUtils = {
  mockSupabaseClient,
  mockRedisClient,
  createTestCustomerProfile,
  createTestCustomerIdentity,
  createTestAnalyticsEvent,
};
