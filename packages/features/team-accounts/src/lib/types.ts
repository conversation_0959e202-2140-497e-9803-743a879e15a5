export interface TeamAccountAuthData {
  id: string;
  name: string;
  settings: Record<string, any>;
}

export interface CreateZaloUserParams {
  zaloId: string;
  name: string;
  picture?: string | null;
  teamId: string;
}

export type ZaloAuthResult = {
  userId: string;
  name: string;
  picture: string | null;
  phone: string | null;
  accountRole: string;
  email: string;
  zaloId: string; // Add zalo_id for reference
};

export interface ZaloUserData {
  id: string;
  name: string;
  picture?: string | null;
}

export type AccountRole = 'customer' | 'admin';
