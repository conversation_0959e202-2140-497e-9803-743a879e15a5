import { SupabaseClient } from '@supabase/supabase-js';

import { Database } from '@kit/supabase/database';

import { ZaloAuthResult } from '../lib/types';

/**
 * Class representing an API for interacting with team accounts.
 * @constructor
 * @param {SupabaseClient<Database>} client - The Supabase client instance.
 */
export class TeamAccountsApi {
  constructor(private readonly client: SupabaseClient<Database>) {}

  /**
   * @name getTeamAccount
   * @description Get the account data for the given slug.
   * @param slug
   */
  async getTeamAccount(slug: string) {
    const { data, error } = await this.client
      .from('accounts')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  /**
   * @name getTeamAccountById
   * @description Check if the user is already in the account.
   * @param accountId
   */
  async getTeamAccountById(accountId: string) {
    const { data, error } = await this.client
      .from('accounts')
      .select('*')
      .eq('id', accountId)
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  /**
   * @name getSubscription
   * @description Get the subscription data for the account.
   * @param accountId
   */
  async getSubscription(accountId: string) {
    const { data, error } = await this.client
      .from('subscriptions')
      .select('*, items: subscription_items !inner (*)')
      .eq('account_id', accountId)
      .maybeSingle();

    if (error) {
      throw error;
    }

    return data;
  }

  /**
   * Get the orders data for the given account.
   * @param accountId
   */
  async getOrder(accountId: string) {
    const response = await this.client
      .from('orders')
      .select('*, items: order_items !inner (*)')
      .eq('account_id', accountId)
      .maybeSingle();

    if (response.error) {
      throw response.error;
    }

    return response.data;
  }

  /**
   * @name getAccountWorkspace
   * @description Get the account workspace data.
   * @param slug
   */
  async getAccountWorkspace(slug: string) {
    const accountPromise = this.client.rpc('team_account_workspace', {
      account_slug: slug,
    });

    const accountsPromise = this.client.from('user_accounts').select('*');

    const [accountResult, accountsResult] = await Promise.all([
      accountPromise,
      accountsPromise,
    ]);

    if (accountResult.error) {
      return {
        error: accountResult.error,
        data: null,
      };
    }

    if (accountsResult.error) {
      return {
        error: accountsResult.error,
        data: null,
      };
    }

    const accountData = accountResult.data[0];

    if (!accountData) {
      return {
        error: new Error('Account data not found'),
        data: null,
      };
    }

    return {
      data: {
        account: accountData,
        accounts: accountsResult.data,
      },
      error: null,
    };
  }

  /**
   * @name hasPermission
   * @description Check if the user has permission to manage billing for the account.
   */
  async hasPermission(params: {
    accountId: string;
    userId: string;
    permission: Database['public']['Enums']['app_permissions'];
  }) {
    const { data, error } = await this.client.rpc('has_permission', {
      account_id: params.accountId,
      user_id: params.userId,
      permission_name: params.permission,
    });

    if (error) {
      throw error;
    }

    return data;
  }

  /**
   * @name getMembersCount
   * @description Get the number of members in the account.
   * @param accountId
   */
  async getMembersCount(accountId: string) {
    const { count, error } = await this.client
      .from('accounts_memberships')
      .select('*', {
        head: true,
        count: 'exact',
      })
      .eq('account_id', accountId);

    if (error) {
      throw error;
    }

    return count;
  }

  /**
   * @name getCustomerId
   * @description Get the billing customer ID for the given account.
   * @param accountId
   */
  async getCustomerId(accountId: string) {
    const { data, error } = await this.client
      .from('billing_customers')
      .select('customer_id')
      .eq('account_id', accountId)
      .maybeSingle();

    if (error) {
      throw error;
    }

    return data?.customer_id;
  }

  /**
   * @name getInvitation
   * @description Get the invitation data from the invite token.
   * @param adminClient - The admin client instance. Since the user is not yet part of the account, we need to use an admin client to read the pending membership
   * @param token - The invitation token.
   */
  async getInvitation(adminClient: SupabaseClient<Database>, token: string) {
    const { data: invitation, error } = await adminClient
      .from('invitations')
      .select<
        string,
        {
          id: string;
          account: {
            id: string;
            name: string;
            slug: string;
            picture_url: string;
          };
        }
      >(
        'id, expires_at, account: account_id !inner (id, name, slug, picture_url)',
      )
      .eq('invite_token', token)
      .gte('expires_at', new Date().toISOString())
      .single();

    if (error ?? !invitation) {
      return null;
    }

    return invitation;
  }
}

export function createTeamAccountsApi(client: SupabaseClient<Database>) {
  return new TeamAccountsApi(client);
}

export class TeamAccountsAuthApi {
  constructor(private readonly adminClient: SupabaseClient<Database>) {}

  async validateZaloToken(accessToken: string): Promise<any> {
    try {
      const response = await fetch(
        'https://graph.zalo.me/v2.0/me?fields=id,name,picture',
        { headers: { access_token: accessToken } },
      );
      return response.ok ? await response.json() : null;
    } catch {
      return null;
    }
  }

  /**
   * Enhanced getOrCreateCustomer with optimal database architecture
   *
   * Key improvements:
   * 1. teamId is now optional - supports guest/public authentication
   * 2. Uses accounts table lookup instead of auth.listUsers() - much more efficient
   * 3. Leverages setup_new_user() trigger - automatic accounts creation
   * 4. Stores zalo_id in accounts.public_data - stable user identification
   * 5. Prevents mapping issues when email changes
   *
   * Architecture:
   * - setup_new_user() trigger creates accounts record when user is created
   * - accounts.id = user.id (1:1 relationship)
   * - zalo_id stored in accounts.public_data jsonb field
   * - Efficient single query lookup instead of listing all users
   *
   * @param teamId - Optional team/account ID. If null, user can authenticate without team context
   * @param accessToken - Zalo access token for user validation
   * @returns ZaloAuthResult with user data and role information
   */
  async getOrCreateCustomer(
    teamId: string | null, // Now optional
    accessToken: string,
  ): Promise<ZaloAuthResult> {
    const zaloUser = await this.validateZaloToken(accessToken);
    if (!zaloUser || zaloUser.error) {
      throw new Error(
        zaloUser?.error === 452
          ? 'Zalo session expired. Please try logging in again.'
          : zaloUser?.message || 'Invalid Zalo token',
      );
    }
    const zaloId = zaloUser.id;
    const name = zaloUser.name || `Zalo User ${zaloId}`;
    const picture = zaloUser.picture?.data?.url || null;
    const uniqueEmail = `${zaloId}@zalo.user`;

    // Kiểm tra user theo zalo_id trong bảng accounts (tối ưu hơn listUsers)
    // Trigger setup_new_user sẽ tự động tạo account khi user được tạo
    // Sử dụng public_data.zalo_id để lookup thay vì auth.users
    const { data: existingAccount, error: lookupError } = await this.adminClient
      .from('accounts')
      .select(
        'id, primary_owner_user_id, name, picture_url, email, phone, public_data',
      )
      .eq('public_data->>zalo_id', zaloId)
      .eq('is_personal_account', true)
      .single();

    if (lookupError && lookupError.code !== 'PGRST116') {
      throw new Error(`Failed to lookup user account: ${lookupError.message}`);
    }

    let userId: string;
    let phone: string | null = null;
    if (existingAccount) {
      // User đã tồn tại, sử dụng account hiện có
      userId = existingAccount.primary_owner_user_id;
      phone = existingAccount.phone || null;

      // Cập nhật account info nếu cần (name, picture có thể thay đổi)
      const updatedPublicData = {
        ...existingAccount.public_data,
        zalo_id: zaloId,
        last_login: new Date().toISOString(),
      };

      // Chỉ cập nhật các field có giá trị mới và khác với giá trị hiện có
      const updateData: any = {
        public_data: updatedPublicData,
      };

      // Chỉ cập nhật name nếu zaloUser.name có giá trị và khác với giá trị hiện có
      if (zaloUser.name && zaloUser.name !== existingAccount.name) {
        updateData.name = zaloUser.name;
      }

      // Chỉ cập nhật picture_url nếu có picture mới và khác với giá trị hiện có
      if (picture && picture !== existingAccount.picture_url) {
        updateData.picture_url = picture;
      }

      // Chỉ thực hiện update nếu có thay đổi (ngoài public_data luôn được update)
      const hasChanges = Object.keys(updateData).length > 1; // > 1 vì luôn có public_data

      if (hasChanges) {
        const { error: updateAccountError } = await this.adminClient
          .from('accounts')
          .update(updateData)
          .eq('id', existingAccount.id);

        if (updateAccountError) {
          console.warn(
            'Failed to update account info:',
            updateAccountError.message,
          );
        } else {
          // Chỉ cập nhật user metadata khi đã cập nhật accounts thành công
          const userMetadataUpdate: any = {
            zalo_id: zaloId,
            // Chỉ update account_id nếu teamId được cung cấp
            ...(teamId ? { account_id: teamId } : {}),
          };

          // Chỉ cập nhật name nếu có trong updateData (đã được validate)
          if (updateData.name) {
            userMetadataUpdate.name = updateData.name;
          }

          // Chỉ cập nhật avatar_url nếu có trong updateData (đã được validate)
          if (updateData.picture_url) {
            userMetadataUpdate.avatar_url = updateData.picture_url;
          }

          const { error: updateUserError } =
            await this.adminClient.auth.admin.updateUserById(userId, {
              user_metadata: userMetadataUpdate,
            });

          if (updateUserError) {
            console.warn(
              'Failed to update user metadata:',
              updateUserError.message,
            );
          }
        }
      }
    } else {
      // Tạo user mới trong auth.users
      const userMetadata: any = {
        zalo_id: zaloId,
        name,
        avatar_url: picture,
      };

      // Chỉ thêm account_id nếu teamId được cung cấp
      if (teamId) {
        userMetadata.account_id = teamId;
      }

      const { data: newUser, error: createError } =
        await this.adminClient.auth.admin.createUser({
          email: uniqueEmail,
          email_confirm: true,
          user_metadata: userMetadata,
          app_metadata: {
            provider: 'zalo',
            providers: ['zalo'],
            role: 'customer',
          },
        });

      if (createError || !newUser) {
        throw createError || new Error('Failed to create user');
      }
      userId = newUser.user.id;

      // Trigger setup_new_user() sẽ tự động tạo bản ghi trong accounts
      // Cập nhật accounts record với zalo_id trong public_data
      const { error: updateAccountError } = await this.adminClient
        .from('accounts')
        .update({
          public_data: {
            zalo_id: zaloId,
            provider: 'zalo',
            created_at: new Date().toISOString(),
          },
        })
        .eq('id', userId); // accounts.id = user.id từ trigger

      if (updateAccountError) {
        console.warn(
          'Failed to update account public_data:',
          updateAccountError.message,
        );
      }
    }

    // Kiểm tra và thêm membership vào teamId (chỉ khi teamId được cung cấp)
    let accountRole = 'customer'; // Default role

    if (teamId) {
      const { data: existingMembership, error: membershipError } =
        await this.adminClient
          .from('accounts_memberships')
          .select('user_id, account_id, account_role')
          .eq('user_id', userId)
          .eq('account_id', teamId)
          .single();

      if (membershipError && membershipError.code !== 'PGRST116') {
        throw new Error(
          `Failed to check membership: ${membershipError.message}`,
        );
      }

      if (existingMembership) {
        // User đã có membership, sử dụng role hiện có
        accountRole = existingMembership.account_role;
      } else {
        // Tạo membership mới
        const { error: insertError } = await this.adminClient
          .from('accounts_memberships')
          .insert({
            user_id: userId,
            account_id: teamId,
            account_role: 'customer', // Vai trò mặc định cho Zalo user
          });

        if (insertError) {
          throw new Error(`Failed to add membership: ${insertError.message}`);
        }
      }
    }

    return {
      userId,
      name,
      picture,
      phone,
      accountRole,
      email: uniqueEmail,
      zaloId, // Include zalo_id for reference
    };
  }
}

export function createTeamAccountsAuthApi(client: SupabaseClient<Database>) {
  return new TeamAccountsAuthApi(client);
}
