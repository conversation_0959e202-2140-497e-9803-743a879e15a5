'use server';

import { revalidatePath } from 'next/cache';

import { z } from 'zod';

import { enhanceAction } from '@kit/next/actions';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

// Cập nhật schema validation
const ProductSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  price: z.number().min(0, 'Price must be positive'),
  category_id: z.string().optional(),
  image_url: z.string().url().optional(),
  image_urls: z.array(z.string().url()).optional().default([]),
  external_id: z.string().optional(),
  stock: z.number().min(0, 'Stock must be positive').optional(),
  description: z.string().optional(),
  status: z.enum(['active', 'draft', 'archived']).default('active'),
});

export async function createProduct(params: {
  name: string;
  price: number;
  category_id?: string;
  image_url?: string;
  image_urls?: string[];
  external_id?: string;
  stock?: number;
  accountId: string;
  accountSlug: string;
}) {
  const supabase = getSupabaseServerClient();

  const { data, error } = await supabase
    .from('products')
    .insert({
      name: params.name,
      price: params.price,
      category_id: params.category_id,
      image_url: params.image_url,
      image_urls: params.image_urls || [], // Thêm trường mới
      external_id: params.external_id,
      stock: params.stock,
      account_id: params.accountId,
    })
    .select()
    .single();

  if (error) {
    throw error;
  }

  revalidatePath(`/home/<USER>/products`);
  return data;
}

export async function updateProduct(params: {
  id: string;
  name: string;
  price: number;
  category_id?: string;
  image_url?: string;
  image_urls?: string[];
  external_id?: string;
  stock?: number;
  accountId: string;
  accountSlug: string;
}) {
  const supabase = getSupabaseServerClient();

  const { data, error } = await supabase
    .from('products')
    .update({
      name: params.name,
      price: params.price,
      category_id: params.category_id,
      image_url: params.image_url,
      image_urls: params.image_urls || [], // Thêm trường mới
      external_id: params.external_id,
      stock: params.stock,
    })
    .eq('id', params.id)
    .eq('account_id', params.accountId)
    .select()
    .single();

  if (error) {
    throw error;
  }

  revalidatePath(`/home/<USER>/products`);
  return data;
}

// Delete product action
export const deleteProductAction = enhanceAction(
  async ({
    accountId,
    productId,
  }: {
    accountId: string;
    productId: string;
  }) => {
    const client = getSupabaseServerClient();

    const { error } = await client
      .from('products')
      .delete()
      .eq('id', productId)
      .eq('account_id', accountId);

    if (error) {
      throw error;
    }

    revalidatePath(`/home/<USER>/products`);
  },
  {
    auth: true,
    schema: z.object({
      accountId: z.string(),
      productId: z.string(),
    }),
  },
);

// Bulk update products action
export const bulkUpdateProductsAction = enhanceAction(
  async ({
    accountId,
    productIds,
    updateData,
  }: {
    accountId: string;
    productIds: string[];
    updateData: Partial<ProductData>;
  }) => {
    const client = getSupabaseServerClient();

    const { data, error } = await client
      .from('products')
      .update(updateData)
      .in('id', productIds)
      .eq('account_id', accountId)
      .select();

    if (error) {
      throw error;
    }

    revalidatePath(`/home/<USER>/products`);
    return data;
  },
  {
    auth: true,
    schema: z.object({
      accountId: z.string(),
      productIds: z.array(z.string()),
      updateData: ProductSchema.partial(),
    }),
  },
);

// Update product status action
export const updateProductStatusAction = enhanceAction(
  async ({
    accountId,
    productId,
    status,
  }: {
    accountId: string;
    productId: string;
    status: 'active' | 'draft' | 'archived';
  }) => {
    const client = getSupabaseServerClient();

    const { data, error } = await client
      .from('products')
      .update({ status })
      .eq('id', productId)
      .eq('account_id', accountId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    revalidatePath(`/home/<USER>/products`);
    return data;
  },
  {
    auth: true,
    schema: z.object({
      accountId: z.string(),
      productId: z.string(),
      status: z.enum(['active', 'draft', 'archived']),
    }),
  },
);

// Update product stock action
export const updateProductStockAction = enhanceAction(
  async ({
    accountId,
    productId,
    stock,
  }: {
    accountId: string;
    productId: string;
    stock: number;
  }) => {
    const client = getSupabaseServerClient();

    const { data, error } = await client
      .from('products')
      .update({ stock })
      .eq('id', productId)
      .eq('account_id', accountId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    revalidatePath(`/home/<USER>/products`);
    return data;
  },
  {
    auth: true,
    schema: z.object({
      accountId: z.string(),
      productId: z.string(),
      stock: z.number().min(0),
    }),
  },
);
