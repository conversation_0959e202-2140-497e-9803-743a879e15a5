import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@kit/supabase/database';
import { getZaloUserDetail } from '@kit/zns';

export interface EducationUserContext {
  userId: string;
  name: string;
  phone: string;
  picture: string;
  email: string;
  accountRole: string;
  organizationId?: string;
  relationshipData?: any;
}

interface RoleDetectionResult {
  role: string;
  isInvited: boolean;
  relationshipData?: any;
  invitationId?: string;
}

export class EducationAuthApi {
  constructor(private client: SupabaseClient<Database>) {}

  async getOrCreateEducationUser(
    accountId: string,
    accessToken: string,
  ): Promise<EducationUserContext> {
    // 1. Lấy thông tin user từ Zalo
    const zaloUser = await getZaloUserDetail(accessToken);
    const phone = this.normalizePhone(zaloUser.phone);
    
    // 2. Tìm organization thuộc account này
    const { data: organization } = await this.client
      .from('organizations')
      .select('id')
      .eq('account_id', accountId)
      .single();

    if (!organization) {
      throw new Error('Organization not found for this account');
    }

    // 3. Detect role dựa trên invitation và existing data
    const roleDetection = await this.detectUserRole(
      accountId,
      organization.id,
      phone,
      zaloUser.email
    );

    // 4. Tạo hoặc update user
    const userContext = await this.createOrUpdateUser(
      accountId,
      organization.id,
      zaloUser,
      roleDetection
    );

    return userContext;
  }

  private async detectUserRole(
    accountId: string,
    organizationId: string,
    phone: string,
    email?: string
  ): Promise<RoleDetectionResult> {
    // Priority 1: Check existing invitation
    const invitation = await this.checkInvitation(accountId, phone, email);
    if (invitation) {
      return {
        role: invitation.role,
        isInvited: true,
        invitationId: invitation.id,
      };
    }

    // Priority 2: Check existing guardian relationship
    const guardianRelation = await this.checkExistingGuardian(organizationId, phone);
    if (guardianRelation) {
      return {
        role: 'guardian',
        isInvited: false,
        relationshipData: guardianRelation,
      };
    }

    // Priority 3: Check if phone belongs to instructor
    const instructorRelation = await this.checkExistingInstructor(organizationId, phone);
    if (instructorRelation) {
      return {
        role: 'instructor',
        isInvited: false,
        relationshipData: instructorRelation,
      };
    }

    // Default: Guest role
    return {
      role: 'guest',
      isInvited: false,
    };
  }

  private async checkInvitation(
    accountId: string,
    phone: string,
    email?: string
  ) {
    let query = this.client
      .from('invitations')
      .select('*')
      .eq('account_id', accountId)
      .eq('status', 'pending');

    if (email) {
      query = query.or(`email.eq.${email},metadata->>phone.eq.${phone}`);
    } else {
      query = query.eq('metadata->>phone', phone);
    }

    const { data: invitation } = await query.single();
    return invitation;
  }

  private async checkExistingGuardian(organizationId: string, phone: string) {
    const { data: guardian } = await this.client
      .from('guardians')
      .select(`
        *,
        learner_guardians(
          learner:learners(id, full_name, learner_code)
        )
      `)
      .eq('organization_id', organizationId)
      .eq('phone', phone)
      .single();

    return guardian;
  }

  private async checkExistingInstructor(organizationId: string, phone: string) {
    // Check if phone exists in programs as instructor
    const { data: programs } = await this.client
      .from('programs')
      .select(`
        id,
        name,
        instructor:auth.users!instructor_id(phone, full_name)
      `)
      .eq('organization_id', organizationId);

    const instructorProgram = programs?.find(p => 
      p.instructor?.phone === phone
    );

    return instructorProgram;
  }

  private async createOrUpdateUser(
    accountId: string,
    organizationId: string,
    zaloUser: any,
    roleDetection: RoleDetectionResult
  ): Promise<EducationUserContext> {
    const phone = this.normalizePhone(zaloUser.phone);
    const email = zaloUser.email || `${phone}@zalo.temp`;

    // Tạo hoặc lấy auth user
    const { data: existingUser } = await this.client.auth.admin.listUsers({
      filter: `phone.eq.${phone}`,
    });

    let authUser;
    if (existingUser.users.length > 0) {
      authUser = existingUser.users[0];
      
      // Update user metadata
      await this.client.auth.admin.updateUserById(authUser.id, {
        user_metadata: {
          ...authUser.user_metadata,
          full_name: zaloUser.name,
          picture: zaloUser.picture,
          zalo_id: zaloUser.id,
          account_id: accountId,
          role: roleDetection.role,
          organization_id: organizationId,
        },
      });
    } else {
      const { data: newUser } = await this.client.auth.admin.createUser({
        email,
        phone,
        user_metadata: {
          full_name: zaloUser.name,
          picture: zaloUser.picture,
          zalo_id: zaloUser.id,
          account_id: accountId,
          role: roleDetection.role,
          organization_id: organizationId,
        },
        email_confirm: true,
        phone_confirm: true,
      });
      authUser = newUser.user;
    }

    if (!authUser) throw new Error('Failed to create user');

    // Handle role-specific logic
    await this.handleRoleSpecificSetup(
      authUser.id,
      accountId,
      organizationId,
      roleDetection,
      zaloUser
    );

    return {
      userId: authUser.id,
      name: zaloUser.name,
      phone,
      picture: zaloUser.picture,
      email,
      accountRole: roleDetection.role,
      organizationId,
      relationshipData: roleDetection.relationshipData,
    };
  }

  private async handleRoleSpecificSetup(
    userId: string,
    accountId: string,
    organizationId: string,
    roleDetection: RoleDetectionResult,
    zaloUser: any
  ) {
    // Create membership if invited
    if (roleDetection.isInvited && roleDetection.invitationId) {
      await this.createMembershipFromInvitation(
        userId,
        accountId,
        roleDetection.invitationId,
        roleDetection.role
      );
    } else {
      // Create guest membership for non-invited users
      await this.createGuestMembership(userId, accountId);
    }

    // Role-specific setup
    switch (roleDetection.role) {
      case 'guardian':
        await this.setupGuardianProfile(
          userId,
          organizationId,
          roleDetection.relationshipData,
          zaloUser
        );
        break;
      
      case 'instructor':
        await this.setupInstructorProfile(
          userId,
          organizationId,
          roleDetection.relationshipData,
          zaloUser
        );
        break;
      
      case 'guest':
        // Log guest access for potential follow-up
        await this.logGuestAccess(userId, organizationId, zaloUser);
        break;
    }
  }

  private async createMembershipFromInvitation(
    userId: string,
    accountId: string,
    invitationId: string,
    role: string
  ) {
    // Create membership
    await this.client
      .from('accounts_memberships')
      .upsert({
        user_id: userId,
        account_id: accountId,
        account_role: role,
      });

    // Mark invitation as accepted
    await this.client
      .from('invitations')
      .update({ 
        status: 'accepted',
        accepted_at: new Date().toISOString(),
      })
      .eq('id', invitationId);
  }

  private async createGuestMembership(userId: string, accountId: string) {
    // Create guest membership (limited access)
    await this.client
      .from('accounts_memberships')
      .upsert({
        user_id: userId,
        account_id: accountId,
        account_role: 'guest',
      });
  }

  private async setupGuardianProfile(
    userId: string,
    organizationId: string,
    existingGuardian: any,
    zaloUser: any
  ) {
    if (existingGuardian) {
      // Update existing guardian with user_id
      await this.client
        .from('guardians')
        .update({
          user_id: userId,
          zalo_id: zaloUser.id,
          full_name: zaloUser.name,
        })
        .eq('id', existingGuardian.id);
    }
    // If no existing guardian, they remain as guest until admin adds them
  }

  private async setupInstructorProfile(
    userId: string,
    organizationId: string,
    instructorData: any,
    zaloUser: any
  ) {
    // Update instructor user profile
    await this.client.auth.admin.updateUserById(userId, {
      user_metadata: {
        ...zaloUser,
        instructor_programs: instructorData ? [instructorData.id] : [],
        organization_id: organizationId,
      },
    });
  }

  private async logGuestAccess(
    userId: string,
    organizationId: string,
    zaloUser: any
  ) {
    // Log for admin review
    await this.client
      .from('guest_access_logs')
      .insert({
        user_id: userId,
        organization_id: organizationId,
        zalo_data: zaloUser,
        access_time: new Date().toISOString(),
      });
  }

  private normalizePhone(phone: string): string {
    // Remove all non-digits and replace leading 0 with 84
    return phone.replace(/\D/g, '').replace(/^0/, '84');
  }
}
