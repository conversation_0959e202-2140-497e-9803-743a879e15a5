import { SupabaseClient } from '@supabase/supabase-js';

import { Database } from '@kit/supabase/database';

/**
 *
 * Service này cung cấp các methods cơ bản để:
 * Lấy theme đang active của một account
 * Cập nhật cấu hình theme
 * Tạo theme mới
 * Cấu trúc theme được thiết kế để dễ dàng mở rộng và tùy chỉnh, đồng thời cung cấp một baseline tốt cho việc styling trong ứng dụng của bạn.
 * */
export class AccountThemeService {
  constructor(private readonly client: SupabaseClient<Database>) {}

  async getActiveTheme(accountId: string) {
    const { data, error } = await this.client
      .from('account_themes')
      .select('*')
      .eq('account_id', accountId)
      .eq('is_active', true)
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  async updateThemeConfig(
    accountId: string,
    themeId: string,
    config: Record<string, any>,
  ) {
    const { data, error } = await this.client
      .from('account_themes')
      .update({ config })
      .eq('account_id', accountId)
      .eq('id', themeId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  async createTheme(
    accountId: string,
    params: {
      name: string;
      config: Record<string, any>;
      isMarketplace?: boolean;
      price?: number;
      description?: string;
    },
  ) {
    const { data, error } = await this.client
      .from('account_themes')
      .insert({
        account_id: accountId,
        name: params.name,
        config: params.config,
        is_marketplace: params.isMarketplace,
        price: params.price,
        description: params.description,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }
}

export function createAccountThemeService(client: SupabaseClient<Database>) {
  return new AccountThemeService(client);
}
