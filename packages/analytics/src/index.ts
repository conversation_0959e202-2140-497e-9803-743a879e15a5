import { createAnalyticsManager } from './analytics-manager';
import { NullAnalyticsService } from './null-analytics-service';
import { ZaloMiniAppAnalyticsService } from './providers/zalo-miniapp-provider';
import type { AnalyticsManager } from './types';

export const analytics: AnalyticsManager = createAnalyticsManager({
  providers: {
    null: () => NullAnalyticsService,
    zaloMiniApp: (config) => new ZaloMiniAppAnalyticsService(config),
  },
});
