import { getUniversalSupabaseClient } from '@kit/supabase/universal-client';

import { AnalyticsService } from '../types';

export interface ZaloMiniAppConfig {
  accountId?: string;
  themeId?: string;
}

export class ZaloMiniAppAnalyticsService implements AnalyticsService {
  private supabase;
  private accountId?: string;
  private themeId?: string;

  constructor(config?: ZaloMiniAppConfig) {
    this.supabase = getUniversalSupabaseClient();

    this.accountId = config?.accountId;
    this.themeId = config?.themeId;
  }

  async initialize() {
    // Không cần khởi tạo gì đặc biệt
    return Promise.resolve();
  }

  async identify(userId: string, traits?: Record<string, any>) {
    if (!userId || !this.accountId) return;

    try {
      // Lưu thông tin người dùng vào analytics_events thay vì tạo bảng mới
      await this.supabase.from('analytics_events').insert({
        account_id: this.accountId,
        theme_id: this.themeId,
        event_type: 'identify',
        event_data: traits || {},
        user_id: userId,
        source: 'zalo_miniapp',
      });
    } catch (error) {
      console.error(
        'Error identifying user in ZaloMiniAppAnalyticsService:',
        error,
      );
    }
  }

  async trackPageView(url: string, properties?: Record<string, any>) {
    if (!this.accountId || !this.themeId) {
      console.warn('ZaloMiniAppAnalyticsService: accountId or themeId not set');
      return;
    }

    try {
      // Lưu lượt xem trang
      await this.supabase.from('analytics_events').insert({
        account_id: this.accountId,
        theme_id: this.themeId,
        event_type: 'pageview',
        event_data: {
          pagePath: url,
          ...properties,
        },
        visitor_id: properties?.visitorId,
        user_id: properties?.userId,
        device_type: properties?.deviceType || 'mobile',
        source: 'zalo_miniapp',
      });
    } catch (error) {
      console.error(
        'Error tracking page view in ZaloMiniAppAnalyticsService:',
        error,
      );
    }
  }

  async trackEvent(eventName: string, eventProperties?: Record<string, any>) {
    if (!this.accountId || !this.themeId) {
      console.warn('ZaloMiniAppAnalyticsService: accountId or themeId not set');
      return;
    }

    try {
      // Lưu sự kiện
      await this.supabase.from('analytics_events').insert({
        account_id: this.accountId,
        theme_id: this.themeId,
        event_type: eventName,
        event_data: eventProperties || {},
        visitor_id: eventProperties?.visitorId,
        user_id: eventProperties?.userId,
        device_type: eventProperties?.deviceType || 'mobile',
        source: 'zalo_miniapp',
      });
    } catch (error) {
      console.error(
        'Error tracking event in ZaloMiniAppAnalyticsService:',
        error,
      );
    }
  }
}

export default ZaloMiniAppAnalyticsService;
