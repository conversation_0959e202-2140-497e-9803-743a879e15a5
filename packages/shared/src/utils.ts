/**
 * Check if the code is running in a browser environment.
 */
export function isBrowser() {
  return typeof window !== 'undefined';
}

/**
 * @name formatCurrency
 * @description Format the currency based on the current i18n language
 */
export function formatCurrencyVND(value: number | string | null | undefined) {
  if (value === null || value === undefined) {
    return '0 VNĐ';
  }
  try {
    return (
      new Intl.NumberFormat('vi-VN', {
        style: 'decimal',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(Number(value)) + ' VNĐ'
    );
  } catch (error) {
    console.error('Error formatting currency:', error, { value });
    return '0 VNĐ';
  }
}

/**
 * @name formatCurrency
 * @description Format the currency based on the currency code
 */
export function formatCurrency(params: {
  currencyCode: string;
  locale: string;
  value: string | number;
}) {
  const [lang, region] = params.locale.split('-');

  return new Intl.NumberFormat(region ?? lang, {
    style: 'currency',
    currency: params.currencyCode,
  }).format(Number(params.value));
}

export const parseCurrencyInput = (value: string): number => {
  return Number(value.replace(/[^0-9.-]+/g, ''));
};
