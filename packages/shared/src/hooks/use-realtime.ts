import { useCallback, useEffect, useRef, useState } from 'react';

interface RealtimeOptions<T> {
  table: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  filter?: string;
  initialData?: T[];
  onError?: (error: Error) => void;
  apiUrl?: string;
}

interface RealtimeState<T> {
  data: T[];
  error: Error | null;
  isLoading: boolean;
  isConnected: boolean;
}

export function useRealtime<T extends { id: string | number }>(
  options: RealtimeOptions<T>,
) {
  const {
    table,
    event = '*',
    filter,
    initialData = [],
    onError,
    apiUrl = '/api/realtime/subscribe',
  } = options;

  const [state, setState] = useState<RealtimeState<T>>({
    data: initialData,
    error: null,
    isLoading: true,
    isConnected: false,
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const MAX_RECONNECT_ATTEMPTS = 5;

  const connect = useCallback(async () => {
    try {
      // First, get subscription details from API
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ table, event, filter }),
      });

      if (!response.ok) {
        throw new Error('Failed to initialize realtime connection');
      }

      const { channel } = await response.json();

      // Then establish WebSocket connection
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/api/realtime/ws?channel=${channel}`;

      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        setState((prev) => ({ ...prev, isConnected: true }));
        reconnectAttemptsRef.current = 0;
      };

      ws.onmessage = (event) => {
        const payload = JSON.parse(event.data);

        setState((prev) => {
          const currentData = [...prev.data];

          switch (payload.eventType) {
            case 'INSERT':
              return {
                ...prev,
                data: [...currentData, payload.new as T],
                isLoading: false,
              };
            case 'UPDATE':
              return {
                ...prev,
                data: currentData.map((item) =>
                  item.id === payload.new.id ? payload.new : item,
                ),
                isLoading: false,
              };
            case 'DELETE':
              return {
                ...prev,
                data: currentData.filter((item) => item.id !== payload.old.id),
                isLoading: false,
              };
            default:
              return { ...prev, isLoading: false };
          }
        });
      };

      ws.onerror = (error) => {
        const err = new Error('WebSocket error');
        setState((prev) => ({ ...prev, error: err }));
        onError?.(err);
      };

      ws.onclose = () => {
        setState((prev) => ({ ...prev, isConnected: false }));
        handleReconnect();
      };

      wsRef.current = ws;
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Unknown error');
      setState((prev) => ({ ...prev, error: err }));
      onError?.(err);
    }
  }, [table, event, filter, apiUrl, onError]);

  const handleReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current >= MAX_RECONNECT_ATTEMPTS) {
      setState((prev) => ({
        ...prev,
        error: new Error('Max reconnection attempts reached'),
      }));
      return;
    }

    reconnectTimeoutRef.current = setTimeout(
      () => {
        reconnectAttemptsRef.current += 1;
        connect();
      },
      1000 * Math.pow(2, reconnectAttemptsRef.current),
    ); // Exponential backoff
  }, [connect]);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
  }, []);

  useEffect(() => {
    connect();
    return () => disconnect();
  }, [connect, disconnect]);

  // Expose methods to manually control the connection
  const methods = {
    reconnect: connect,
    disconnect,
  };

  return { ...state, ...methods };
}
