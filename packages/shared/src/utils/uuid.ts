/**
 * Kiểm tra xem một chuỗi có phải là UUID hợp lệ hay không
 * @param value Chuỗi cần kiểm tra
 * @returns true nếu chuỗi là UUID hợp lệ, false nếu không phải
 */
export function isUUID(value: string): boolean {
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value);
}

/**
 * Lấy ID từ slug hoặc UUID
 * @param value Slug hoặc UUID
 * @param getIdFromSlug Hàm lấy ID từ slug
 * @returns ID nếu value là UUID, hoặc kết quả từ hàm getIdFromSlug nếu value là slug
 */
export async function getIdFromSlugOrUUID<T>(
  value: string,
  getIdFromSlug: (slug: string) => Promise<T>
): Promise<T> {
  // Kiểm tra xem value có phải là UUID không
  if (isUUID(value)) {
    return value as unknown as T;
  }
  
  // Nếu không phải UUID, sử dụng hàm getIdFromSlug để lấy ID từ slug
  return await getIdFromSlug(value);
}
