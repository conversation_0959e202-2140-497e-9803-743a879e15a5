# Hướng dẫn sử dụng API Checkout

Tài liệu này mô tả cách sử dụng API Checkout để tính toán giá trị đơn hàng và áp dụng voucher trong Zalo Mini App.

## Tổng quan

API Checkout cho phép bạn tính toán giá trị đơn hàng dựa trên các sản phẩm được chọn, áp dụng flash sale và voucher. API này giúp đảm bảo tính nhất quán giữa giá trị hiển thị cho người dùng và giá trị cuối cùng khi tạo đơn hàng.

## Endpoint

```
POST /api/checkout/calculate
```

## Xác thực

API yêu cầu xác thực bằng JWT token. Token này cần được gửi trong header `Authorization`.

```
Authorization: Bearer <your_jwt_token>
```

## Tham số

| Tham số | Ki<PERSON>u dữ liệu | Bắt buộc | <PERSON><PERSON> tả |
|---------|--------------|----------|-------|
| items | array | Có | Danh sách các sản phẩm trong đơn hàng |
| voucher_code | string | Không | Mã voucher (nếu có) |

### Cấu trúc của mỗi item trong mảng items

| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| product_id | string | Có | ID của sản phẩm |
| quantity | number | Có | Số lượng sản phẩm |
| attribute_id | string | Không | ID của thuộc tính sản phẩm (nếu có) |

## Phản hồi

### Thành công

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "product_id": "product-123",
        "quantity": 2,
        "price": 100000,
        "original_price": 120000,
        "flash_sale_id": "flash-sale-456",
        "discount_percentage": 16.67,
        "total_price": 200000,
        "attribute_id": null,
        "product": {
          "name": "Tên sản phẩm",
          "image_url": "/images/products/product-123.jpg"
        }
      }
    ],
    "subtotal": 200000,
    "discount": 0,
    "voucher_discount": 20000,
    "voucher_code": "SUMMER10",
    "voucher_id": "voucher-789",
    "final_total": 180000
  }
}
```

### Lỗi

```json
{
  "success": false,
  "error": "Mô tả lỗi",
  "details": "Chi tiết lỗi (nếu có)"
}
```

## Mã lỗi

| Mã lỗi | Mô tả |
|--------|-------|
| 400 | Thiếu tham số bắt buộc hoặc định dạng không hợp lệ |
| 404 | Không tìm thấy sản phẩm hoặc voucher |
| 422 | Voucher không hợp lệ hoặc đã hết hạn |
| 500 | Lỗi server khi tính toán |

## Ví dụ

### Tính toán đơn hàng không có voucher

```bash
curl -X POST https://your-domain.com/api/checkout/calculate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "items": [
      {
        "product_id": "product-123",
        "quantity": 2
      },
      {
        "product_id": "product-456",
        "quantity": 1,
        "attribute_id": "attr-789"
      }
    ]
  }'
```

### Tính toán đơn hàng có voucher

```bash
curl -X POST https://your-domain.com/api/checkout/calculate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "items": [
      {
        "product_id": "product-123",
        "quantity": 2
      },
      {
        "product_id": "product-456",
        "quantity": 1,
        "attribute_id": "attr-789"
      }
    ],
    "voucher_code": "SUMMER10"
  }'
```

## Lưu ý

1. API này tự động áp dụng flash sale nếu sản phẩm đang trong thời gian khuyến mãi.
2. Nếu cung cấp voucher_code, API sẽ kiểm tra tính hợp lệ của voucher và áp dụng giảm giá nếu hợp lệ.
3. Giá trị subtotal là tổng giá trị của các sản phẩm sau khi đã áp dụng flash sale.
4. Giá trị final_total là giá trị cuối cùng sau khi đã áp dụng tất cả các giảm giá.

## Tích hợp với Zalo Mini App

Để tích hợp API Checkout vào Zalo Mini App, bạn cần:

1. Lấy JWT token từ quá trình xác thực
2. Gửi danh sách sản phẩm và mã voucher (nếu có) đến API
3. Hiển thị kết quả tính toán cho người dùng

### Ví dụ tích hợp (JavaScript)

```javascript
// Hàm tính toán giá trị đơn hàng
async function calculateCheckout(items, voucherCode = null) {
  try {
    const requestData = {
      items: items.map(item => ({
        product_id: item.productId,
        quantity: item.quantity,
        attribute_id: item.attributeId || null
      }))
    };
    
    if (voucherCode) {
      requestData.voucher_code = voucherCode;
    }
    
    const response = await fetch('https://your-domain.com/api/checkout/calculate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
      },
      body: JSON.stringify(requestData)
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Lỗi khi tính toán đơn hàng');
    }
    
    return data.data;
  } catch (error) {
    console.error('Error calculating checkout:', error);
    throw error;
  }
}

// Ví dụ sử dụng
const cartItems = [
  { productId: 'product-123', quantity: 2 },
  { productId: 'product-456', quantity: 1, attributeId: 'attr-789' }
];

// Tính toán không có voucher
calculateCheckout(cartItems)
  .then(result => {
    console.log('Subtotal:', result.subtotal);
    console.log('Final total:', result.final_total);
  })
  .catch(error => {
    console.error('Failed to calculate checkout:', error);
  });

// Tính toán có voucher
calculateCheckout(cartItems, 'SUMMER10')
  .then(result => {
    console.log('Subtotal:', result.subtotal);
    console.log('Voucher discount:', result.voucher_discount);
    console.log('Final total:', result.final_total);
  })
  .catch(error => {
    console.error('Failed to calculate checkout:', error);
  });
```
