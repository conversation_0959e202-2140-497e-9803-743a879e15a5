# Analytics Best Practices & Troubleshooting

## 🎯 Best Practices

### 1. **Event Naming Convention**

#### **Consistent Naming**
```typescript
// ✅ Good - Consistent snake_case
analytics.trackEvent('product_view', { productId: '123' });
analytics.trackEvent('add_to_cart', { productId: '123' });
analytics.trackEvent('checkout_started', { cartValue: 100 });

// ❌ Bad - Inconsistent naming
analytics.trackEvent('productView', { productId: '123' });
analytics.trackEvent('add-to-cart', { productId: '123' });
analytics.trackEvent('CheckoutStarted', { cartValue: 100 });
```

#### **Hierarchical Event Names**
```typescript
// ✅ Good - Clear hierarchy
analytics.trackEvent('ecommerce.product.view', data);
analytics.trackEvent('ecommerce.cart.add', data);
analytics.trackEvent('ecommerce.checkout.start', data);
analytics.trackEvent('user.auth.login', data);
analytics.trackEvent('user.profile.update', data);
```

### 2. **Data Structure Standards**

#### **Consistent Property Names**
```typescript
// ✅ Good - Standardized properties
const standardEventData = {
  // IDs
  user_id: 'user-123',
  session_id: 'session-456',
  product_id: 'prod-789',
  
  // Values
  value: 100.50,
  currency: 'VND',
  quantity: 2,
  
  // Context
  page_path: '/products',
  referrer: 'google.com',
  device_type: 'mobile',
  
  // Timestamps
  timestamp: new Date().toISOString(),
  
  // Custom properties với prefix
  custom_category: 'electronics',
  custom_brand: 'apple'
};
```

#### **Type Safety**
```typescript
// Define event schemas
interface ProductViewEvent {
  product_id: string;
  product_name: string;
  category: string;
  price: number;
  currency: string;
  user_id?: string;
  session_id: string;
}

interface AddToCartEvent {
  product_id: string;
  quantity: number;
  price: number;
  cart_total: number;
  currency: string;
}

// Type-safe tracking functions
export const trackProductView = (data: ProductViewEvent) => {
  analytics.trackEvent('product_view', data);
};

export const trackAddToCart = (data: AddToCartEvent) => {
  analytics.trackEvent('add_to_cart', data);
};
```

### 3. **Performance Optimization**

#### **Batch Events**
```typescript
// ✅ Good - Batch multiple events
class EventBatcher {
  private events: Array<{ name: string; data: any }> = [];
  private batchSize = 10;
  private flushInterval = 5000; // 5 seconds

  constructor() {
    setInterval(() => this.flush(), this.flushInterval);
  }

  track(eventName: string, eventData: any) {
    this.events.push({ name: eventName, data: eventData });
    
    if (this.events.length >= this.batchSize) {
      this.flush();
    }
  }

  private async flush() {
    if (this.events.length === 0) return;
    
    const eventsToSend = [...this.events];
    this.events = [];
    
    try {
      await analytics.trackEvent('batch_events', { events: eventsToSend });
    } catch (error) {
      // Re-queue events on failure
      this.events.unshift(...eventsToSend);
    }
  }
}
```

#### **Async Tracking**
```typescript
// ✅ Good - Non-blocking analytics
export const trackEventAsync = (eventName: string, data: any) => {
  // Don't await - fire and forget
  analytics.trackEvent(eventName, data).catch(error => {
    console.warn('Analytics tracking failed:', error);
  });
};

// ✅ Good - Queue for offline scenarios
class OfflineEventQueue {
  private queue: Array<{ name: string; data: any; timestamp: number }> = [];

  track(eventName: string, data: any) {
    if (navigator.onLine) {
      analytics.trackEvent(eventName, data);
    } else {
      this.queue.push({
        name: eventName,
        data,
        timestamp: Date.now()
      });
    }
  }

  flushQueue() {
    if (this.queue.length === 0) return;
    
    const events = [...this.queue];
    this.queue = [];
    
    events.forEach(event => {
      analytics.trackEvent(event.name, {
        ...event.data,
        queued_timestamp: event.timestamp
      });
    });
  }
}
```

### 4. **Privacy & Compliance**

#### **Data Sanitization**
```typescript
// Remove PII before tracking
const sanitizeEventData = (data: any) => {
  const sanitized = { ...data };
  
  // Remove sensitive fields
  delete sanitized.email;
  delete sanitized.phone;
  delete sanitized.address;
  delete sanitized.credit_card;
  
  // Hash user identifiers
  if (sanitized.user_id) {
    sanitized.user_id = hashUserId(sanitized.user_id);
  }
  
  return sanitized;
};

export const trackEventSafely = (eventName: string, data: any) => {
  const sanitizedData = sanitizeEventData(data);
  analytics.trackEvent(eventName, sanitizedData);
};
```

#### **Consent Management**
```typescript
class ConsentManager {
  private hasConsent = false;
  private queuedEvents: Array<{ name: string; data: any }> = [];

  setConsent(consent: boolean) {
    this.hasConsent = consent;
    
    if (consent) {
      // Flush queued events
      this.queuedEvents.forEach(event => {
        analytics.trackEvent(event.name, event.data);
      });
      this.queuedEvents = [];
    }
  }

  track(eventName: string, data: any) {
    if (this.hasConsent) {
      analytics.trackEvent(eventName, data);
    } else {
      // Queue events until consent is given
      this.queuedEvents.push({ name: eventName, data });
    }
  }
}
```

### 5. **Error Handling**

#### **Graceful Degradation**
```typescript
// ✅ Good - Never break user experience
export const safeTrack = async (eventName: string, data: any) => {
  try {
    await analytics.trackEvent(eventName, data);
  } catch (error) {
    // Log error but don't throw
    console.warn(`Analytics tracking failed for ${eventName}:`, error);
    
    // Optional: Send to error tracking service
    errorTracker.captureException(error, {
      context: 'analytics',
      eventName,
      eventData: data
    });
  }
};

// ✅ Good - Retry mechanism
export const trackWithRetry = async (
  eventName: string, 
  data: any, 
  maxRetries = 3
) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await analytics.trackEvent(eventName, data);
      return; // Success
    } catch (error) {
      if (attempt === maxRetries) {
        console.error(`Analytics tracking failed after ${maxRetries} attempts:`, error);
        return;
      }
      
      // Exponential backoff
      await new Promise(resolve => 
        setTimeout(resolve, Math.pow(2, attempt) * 1000)
      );
    }
  }
};
```

## 🔧 Troubleshooting

### 1. **Common Issues**

#### **Events không xuất hiện trong database**
```typescript
// Debug checklist
const debugAnalytics = async () => {
  console.log('1. Check if analytics is initialized:', analytics);
  
  console.log('2. Check active providers:', 
    analytics.getActiveProviders?.() || 'Method not available'
  );
  
  console.log('3. Check network connectivity:', navigator.onLine);
  
  console.log('4. Test event tracking:');
  try {
    await analytics.trackEvent('debug_test', { timestamp: Date.now() });
    console.log('✅ Event tracking successful');
  } catch (error) {
    console.error('❌ Event tracking failed:', error);
  }
  
  console.log('5. Check authentication:');
  // Check if user is authenticated and has proper permissions
};
```

#### **RLS Policy Issues**
```sql
-- Debug RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'analytics_events';

-- Test policy manually
SELECT * FROM analytics_events 
WHERE account_id = 'your-account-id' 
LIMIT 1;
```

#### **Performance Issues**
```typescript
// Monitor analytics performance
const performanceMonitor = {
  trackingTimes: [] as number[],
  
  async trackWithTiming(eventName: string, data: any) {
    const start = performance.now();
    
    try {
      await analytics.trackEvent(eventName, data);
      const duration = performance.now() - start;
      this.trackingTimes.push(duration);
      
      if (duration > 1000) { // Slow tracking
        console.warn(`Slow analytics tracking: ${duration}ms for ${eventName}`);
      }
    } catch (error) {
      console.error('Analytics tracking error:', error);
    }
  },
  
  getAverageTime() {
    if (this.trackingTimes.length === 0) return 0;
    return this.trackingTimes.reduce((a, b) => a + b) / this.trackingTimes.length;
  }
};
```

### 2. **Debugging Tools**

#### **Analytics Debugger Component**
```typescript
// Development-only debugging component
export function AnalyticsDebugger() {
  const [events, setEvents] = useState<any[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;

    // Intercept analytics calls
    const originalTrackEvent = analytics.trackEvent;
    analytics.trackEvent = async (eventName: string, data: any) => {
      setEvents(prev => [...prev, { 
        eventName, 
        data, 
        timestamp: new Date().toISOString() 
      }]);
      return originalTrackEvent.call(analytics, eventName, data);
    };

    return () => {
      analytics.trackEvent = originalTrackEvent;
    };
  }, []);

  if (process.env.NODE_ENV !== 'development') return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-blue-500 text-white px-3 py-2 rounded"
      >
        Analytics Debug ({events.length})
      </button>
      
      {isVisible && (
        <div className="mt-2 bg-white border rounded shadow-lg p-4 max-h-96 overflow-auto">
          <h3>Recent Analytics Events</h3>
          {events.slice(-10).map((event, index) => (
            <div key={index} className="border-b py-2">
              <strong>{event.eventName}</strong>
              <pre className="text-xs">{JSON.stringify(event.data, null, 2)}</pre>
              <small>{event.timestamp}</small>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
```

#### **Analytics Health Check**
```typescript
export const analyticsHealthCheck = async () => {
  const results = {
    providers: [],
    connectivity: navigator.onLine,
    authentication: null,
    database: null,
    performance: null
  };

  // Check providers
  try {
    results.providers = analytics.getActiveProviders?.() || [];
  } catch (error) {
    console.error('Provider check failed:', error);
  }

  // Check authentication
  try {
    const response = await fetch('/api/auth/user');
    results.authentication = response.ok;
  } catch (error) {
    results.authentication = false;
  }

  // Check database connectivity
  try {
    const testEvent = await analytics.trackEvent('health_check', { 
      timestamp: Date.now() 
    });
    results.database = true;
  } catch (error) {
    results.database = false;
  }

  // Performance test
  const start = performance.now();
  try {
    await analytics.trackEvent('performance_test', {});
    results.performance = performance.now() - start;
  } catch (error) {
    results.performance = null;
  }

  return results;
};
```

### 3. **Monitoring & Alerts**

#### **Error Rate Monitoring**
```typescript
class AnalyticsMonitor {
  private errorCount = 0;
  private totalCalls = 0;
  private errorThreshold = 0.1; // 10% error rate

  async trackWithMonitoring(eventName: string, data: any) {
    this.totalCalls++;
    
    try {
      await analytics.trackEvent(eventName, data);
    } catch (error) {
      this.errorCount++;
      
      const errorRate = this.errorCount / this.totalCalls;
      if (errorRate > this.errorThreshold) {
        // Alert: High error rate
        this.sendAlert(`Analytics error rate: ${(errorRate * 100).toFixed(1)}%`);
      }
      
      throw error;
    }
  }

  private sendAlert(message: string) {
    // Send to monitoring service
    console.error('ANALYTICS ALERT:', message);
  }
}
```
